/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AIRepairVM.kt
 ** Description:AI修复页面的ViewModel
 ** Version: 1.0
 ** Date : 2024/4/25
 ** Author: xiewujie@Apps.Gallery3D
 ** TAG: AIRepairVM
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  xie<PERSON><PERSON><PERSON>@Apps.Gallery3D      2024/04/25    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.airepair

import android.app.Activity
import android.app.Application
import android.graphics.Bitmap
import androidx.collection.arrayMapOf
import com.oplus.aiunit.core.protocol.common.ErrorCode
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.foundation.archv2.bus.PublisherChannel
import com.oplus.gallery.foundation.tracing.constant.AIEditGalleryError
import com.oplus.gallery.foundation.ui.notification.NotificationAction
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.runOnUIThreadImmediate
import com.oplus.gallery.framework.abilities.config.ISettingsAbility
import com.oplus.gallery.framework.abilities.config.SensitiveFuncBanItem
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_REPAIR_BANNED_TIME
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_REPAIR_DE_BLUR_DETECT_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_REPAIR_DE_REFLECTION_DETECT_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_AGREE_AI_UNIT_PRIVACY
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REGION_CN
import com.oplus.gallery.framework.abilities.editing.effect.AvEffect
import com.oplus.gallery.framework.abilities.editing.sink.DefaultBitmapSink
import com.oplus.gallery.framework.abilities.editing.sink.IAvSink
import com.oplus.gallery.framework.abilities.editing.sink.emptySink
import com.oplus.gallery.framework.abilities.editing.track.usage.IAvEffectUsage
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.common.Config.Loader.PREVIEW_PHOTO_MAX_LENGTH
import com.oplus.gallery.photoeditor.editingvvm.EditingSubVM
import com.oplus.gallery.photoeditor.editingvvm.EditingVM
import com.oplus.gallery.photoeditor.editingvvm.EmptyUnitReplier
import com.oplus.gallery.photoeditor.editingvvm.TObserver
import com.oplus.gallery.photoeditor.editingvvm.TopicID.AIRepair.REPLY_TOPIC_AI_REPAIR_CANCEL
import com.oplus.gallery.photoeditor.editingvvm.TopicID.AIRepair.REPLY_TOPIC_AI_REPAIR_CLICK_DETAIL_BUTTON
import com.oplus.gallery.photoeditor.editingvvm.TopicID.AIRepair.REPLY_TOPIC_AI_REPAIR_EFFECT_END
import com.oplus.gallery.photoeditor.editingvvm.TopicID.AIRepair.REPLY_TOPIC_AI_REPAIR_INTRODUCTION_CLICK
import com.oplus.gallery.photoeditor.editingvvm.TopicID.AIRepair.REPLY_TOPIC_AI_REPAIR_NOTIFY_WINDOWS_FOCUS_CHANGED
import com.oplus.gallery.photoeditor.editingvvm.TopicID.AIRepair.TOPIC_AI_REPAIR_INTRODUCTION_VIEW_DATE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.AIRepair.TOPIC_AI_REPAIR_REPAIR_STATUS
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_INPUTS_MEDIA_ITEM
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_INPUTS_THUMBNAIL
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Notification.TOPIC_NOTIFICATION_ACTION
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.REPLY_TOPIC_OPERATING_ACTION
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_OPERATING_EFFECT_USAGE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OperatingAction.TOPIC_OPERATING_ACTION_CANCEL
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OperatingAction.TOPIC_OPERATING_ACTION_CONFIRM
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_IS_PREVIEW_LOADED
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_PREVIEW_IS_SCROLLABLE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_PREVIEW_IS_TOUCHABLE
import com.oplus.gallery.photoeditor.editingvvm.UnitReplier
import com.oplus.gallery.photoeditor.editingvvm.airepair.defogmongo.AIDeFogMongoTemplate
import com.oplus.gallery.photoeditor.editingvvm.airepair.deglare.AIDeGlareTemplate
import com.oplus.gallery.photoeditor.editingvvm.airepair.AIRepairStatus.Companion.KEY_DE_REFLECTION_MASK
import com.oplus.gallery.photoeditor.editingvvm.airepair.AIRepairStatus.Companion.KEY_REQUEST_IMAGE
import com.oplus.gallery.photoeditor.editingvvm.airepair.deblur.AIDeBlurTemplate
import com.oplus.gallery.photoeditor.editingvvm.airepair.dereflection.AIDeReflectionTemplate
import com.oplus.gallery.photoeditor.editingvvm.airepair.track.AIRepairTrackHelper
import com.oplus.gallery.photoeditor.editingvvm.airepair.track.AIRepairTrackHelper.recordRepairEventData
import com.oplus.gallery.photoeditor.editingvvm.airepair.track.AIRepairTrackHelper.trackAIUnitErrorEvent
import com.oplus.gallery.photoeditor.editingvvm.airepair.track.AIRepairTrackHelper.trackGalleryError
import com.oplus.gallery.photoeditor.editingvvm.airepair.track.AIRepairTrackInfo
import com.oplus.gallery.photoeditor.editingvvm.get
import com.oplus.gallery.photoeditor.editingvvm.getForSubscriber
import com.oplus.gallery.photoeditor.editingvvm.notifyOnce
import com.oplus.gallery.photoeditor.editingvvm.operating.ExitPageListener
import com.oplus.gallery.photoeditor.editingvvm.operating.NonParametricRecord
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingAction
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecord
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingType
import com.oplus.gallery.photoeditor.editingvvm.registerForSubscriber
import com.oplus.gallery.photoeditor.editingvvm.subscribeOnce
import com.oplus.gallery.photoeditor.editingvvm.subscribeT
import com.oplus.gallery.photoeditor.editingvvm.unregisterForSubscriber
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * AI修复类功能的ViewModel，包含AI去模糊和AI去反光
 */
internal class AIRepairVM(editingVM: EditingVM) : EditingSubVM(editingVM) {

    private var repairStatusPub: PublisherChannel<AIRepairStatus>? = null

    private var introductionViewDataPub: PublisherChannel<List<AIRepairIntroductionViewData>>? = null

    private var permissionObserver: IAIFuncPermissionObserver? = null

    private val isDomestic by lazy {
        ConfigAbilityWrapper.getBoolean(IS_REGION_CN)
    }

    private val template: AIFuncTemplate?
        get() {
            return when (getCurrentStrategy()?.strategyID) {
                R.id.strategy_ai_deblur, R.id.strategy_specific_ai_deblur -> AIDeBlurTemplate()
                R.id.strategy_ai_dereflection, R.id.strategy_specific_ai_dereflection -> AIDeReflectionTemplate()
                R.id.strategy_ai_deglare, R.id.strategy_specific_ai_deglare -> AIDeGlareTemplate()
                R.id.strategy_ai_defog, R.id.strategy_specific_ai_defog -> AIDeFogMongoTemplate()
                else -> {
                    val strategyName = getCurrentStrategy()?.strategyNameRes?.let { name -> app.getString(name) }
                    GLog.e(TAG, LogFlag.DL, "[template] error strategy:$strategyName")
                    null
                }
            }
        }

    private var currentRecord: OperatingRecord? = null

    private var updateOperatingViewStateJob: Job? = null

    private var mediaItem: MediaItem? = null

    private val requestObserver = AIRepairRequestObserver().apply {
        onResult = this@AIRepairVM::onRepairEnd
    }

    private val onEffectUsageChanged: TObserver<IAvEffectUsage> = {
        GLog.d(TAG, "[onEffectUsageChanged] effectUsage is $it")
        requestObserver.notifyUsageChanged(it)
    }

    private val cancelRepairChannel = object : EmptyUnitReplier() {

        override fun onEmptyReply() {
            cancelRepair()
            exit()
        }
    }

    private val windowsFocusChangeListener = object : UnitReplier<Boolean>() {
        override fun onSingleReply(hasFocus: Boolean) {
            if (hasFocus) {
                updateAIUnitConfig {
                    permissionObserver?.checkPermission()
                }
            }
        }
    }

    private val effectEndChannel = object : UnitReplier<Unit>() {
        override fun onSingleReply(arg: Unit) {
            updateRepairState(AIRepairState.EFFECT_END)
        }
    }

    /**
     * 操作栏的完成按钮点击回调
     */
    private val confirmRep = object : UnitReplier<ExitPageListener>() {
        override fun onSingleReply(arg: ExitPageListener) {
            trackInfo?.let {
                AIRepairTrackHelper.trackRepairExitEvent(true, it)
            }
            arg.onExitPage(isConfirm = true)
        }
    }

    /**
     * 操作栏的取消按钮点击回调
     */
    private val cancelRep = object : UnitReplier<ExitPageListener>() {
        override fun onSingleReply(arg: ExitPageListener) {
            cancelRepair()
            arg.onExitPage(isConfirm = false)
        }
    }

    private val introductionConfirmChannel = object : UnitReplier<Unit>() {
        override fun onSingleReply(arg: Unit) {
            if (template?.introductionConfigId?.let { ConfigAbilityWrapper.getBoolean(it, true) } == true) {
                startRepair()
                markIntroductionUsed()
            }
        }
    }

    private val introductionClickChannel = object : UnitReplier<Unit>() {
        override fun onSingleReply(arg: Unit) {
            showIntroduction()
        }
    }

    @Volatile
    var isDestroy = false

    /**
     * AI修复（去反光、模糊）操作记录对象：用于埋点
     */
    private var trackInfo: AIRepairTrackInfo? = null

    override fun onCreate() {
        super.onCreate()
        isDestroy = false
        vmBus?.apply {
            repairStatusPub = registerForSubscriber(TOPIC_AI_REPAIR_REPAIR_STATUS, AIRepairSection.TAG)
            introductionViewDataPub = registerForSubscriber(TOPIC_AI_REPAIR_INTRODUCTION_VIEW_DATE, AIRepairSection.TAG)
            register(REPLY_TOPIC_AI_REPAIR_NOTIFY_WINDOWS_FOCUS_CHANGED, windowsFocusChangeListener)
            register(REPLY_TOPIC_AI_REPAIR_CANCEL, cancelRepairChannel)
            register(REPLY_TOPIC_AI_REPAIR_EFFECT_END, effectEndChannel)
            register(REPLY_TOPIC_AI_REPAIR_CLICK_DETAIL_BUTTON, introductionClickChannel)
            register(REPLY_TOPIC_AI_REPAIR_INTRODUCTION_CLICK, introductionConfirmChannel)
            subscribeT(TOPIC_OPERATING_EFFECT_USAGE, onEffectUsageChanged)
        }
        if (showIntroductionIfNeed().not()) {
            launch(Dispatchers.UI) {
                // 触发修复前，不能进行放大和缩小
                vmBus?.notifyOnce(TOPIC_PREVIEW_IS_TOUCHABLE, false)
                vmBus?.notifyOnce(TOPIC_PREVIEW_IS_SCROLLABLE, false)
                delay(START_REPAIR_DELAY)
                startRepair()
            }
        }
        startObserverPermission()
    }

    override fun onResume() {
        super.onResume()
        vmBus?.apply {
            register(TOPIC_OPERATING_ACTION_CONFIRM, confirmRep)
            register(TOPIC_OPERATING_ACTION_CANCEL, cancelRep)
        }
    }

    override fun onPause() {
        super.onPause()
        vmBus?.apply {
            unregister(TOPIC_OPERATING_ACTION_CONFIRM, confirmRep)
            unregister(TOPIC_OPERATING_ACTION_CANCEL, cancelRep)
        }
    }

    private fun startObserverPermission() {
        updateAIUnitConfig()
        permissionObserver = template?.createPermissionObserver(editingVM.getApplication())
        permissionObserver?.startObserve(
            postNotificationAction = {
                vmBus?.notifyOnce(TOPIC_NOTIFICATION_ACTION, it)
            },
            refuseAction = {
                exit()
            }
        )
    }

    private fun showIntroductionIfNeed(): Boolean {
        val template = template ?: let {
            GLog.e(TAG, "[showIntroductionIfNeed] can't show because can't get template")
            return false
        }
        val isShowIntroduction = ConfigAbilityWrapper.getBoolean(template.introductionConfigId, true)
        if (isShowIntroduction) {
            showIntroduction()
        }
        return isShowIntroduction
    }

    private fun showIntroduction() {
        template?.introductionViewDataList?.let {
            introductionViewDataPub?.publish(it)
        }
    }

    private fun updateAIUnitConfig(delay: Long = 0, block: () -> Unit = {}) {
        // 进入AI修复页面后，刷新一下下载配置
        launch(Dispatchers.IO) {
            delay(delay)
            app.getAppAbility<ISettingsAbility>()?.use {
                it.updateBlockingConfig(
                    app,
                    IS_AGREE_AI_UNIT_PRIVACY,
                    AI_REPAIR_DE_BLUR_DETECT_STATE,
                    AI_REPAIR_DE_REFLECTION_DETECT_STATE,
                )
            }
            withContext(Dispatchers.UI) {
                block.invoke()
            }
        }
    }

    private fun checkRepairLegality(
        block: () -> Unit
    ) {
        // 检查是否是触发过太多次拒识而导致修复不能用
        val bannedTime = ConfigAbilityWrapper.getLong(AI_REPAIR_BANNED_TIME, 0)
        if (bannedTime > System.currentTimeMillis()) {
            GLog.d(TAG) { "[checkRepairLegality] repair is banned" }
            mediaItem?.let {
                trackGalleryError(it, trackInfo, AIEditGalleryError.ERROR_FUNCTION_BANNED)
            }
            updateRepairState(AIRepairState.ERROR_REPAIR_BAN)
            return
        }
        block()
    }

    private fun startRepair() {
        mediaItem = vmBus?.get<MediaItem>(TOPIC_INPUTS_MEDIA_ITEM)
        checkRepairLegality {
            if (isDestroy) {
                GLog.d(TAG, LogFlag.DL, "[startRepair] unnecessary download because vm is destroy")
                return@checkRepairLegality
            }
            getEffectBitmap { bitmap ->
                if (isDestroy) {
                    GLog.d(TAG, LogFlag.DL, "[startRepair] unnecessary start because viewModel is destroyed")
                    return@getEffectBitmap
                }
                if (bitmap == null) {
                    updateRepairState(AIRepairState.ERROR_UNKNOWN)
                    GLog.e(TAG, "[startRepair] error, can't not bitmap")
                    mediaItem?.let {
                        trackGalleryError(it, trackInfo, AIEditGalleryError.ERROR_DOWNLOAD_PREVIEW_BITMAP)
                    }
                    return@getEffectBitmap
                }
                launch(Dispatchers.UI.immediate) {
                    initAndRecordTrackInfo(bitmap)
                    onRequestStart(bitmap)
                    // 在预览图没有准备好之前，不能向管线提交任务，不然会使得预览图loaded太晚，依赖TOPIC_IS_PREVIEW_LOADED的地方会出异常
                    waitPreviewLoaded {
                        trackInfo?.apply {
                            recordRepairEventData(this, bitmap, template?.type, getCurrentStrategy()?.strategyID)
                        }
                        emitRecord(bitmap)
                    }
                }
            }
        }
    }

    private fun waitPreviewLoaded(block: () -> Unit) {
        vmBus?.subscribeOnce<Boolean>(TOPIC_IS_PREVIEW_LOADED) {
            block.invoke()
        }
    }

    /**
     * 初始化并记录埋点信息
     */
    private fun initAndRecordTrackInfo(previewBitmap: Bitmap) {
        trackInfo = AIRepairTrackInfo()
        trackInfo?.apply {
            recordRepairEventData(this, previewBitmap, template?.type, getCurrentStrategy()?.strategyID)
        }
    }

    private fun markIntroductionUsed() {
        editingVM.getApplication<Application>().getAppAbility<ISettingsAbility>()?.use {
            template?.let { template -> it.markAIFuncIntroductionUsed(template.introductionConfigId) }
        }
    }

    private fun downloadPreviewBitmap(block: (Bitmap?) -> Unit) {
        val start = System.currentTimeMillis()
        vmBus?.subscribeOnce<Boolean>(TOPIC_IS_PREVIEW_LOADED) {
            GLog.d(TAG, LogFlag.DF, "[downloadPreviewBitmap] wait preview cost time:${GLog.getTime(start)} ms")
            if (isDestroy) {
                GLog.d(TAG, LogFlag.DL, "[downloadPreviewBitmap] unnecessary download because vm is destroy")
                return@subscribeOnce
            }
            var requestCode = -1
            var bitmapSink = emptySink()
            bitmapSink = DefaultBitmapSink(
                PREVIEW_PHOTO_MAX_LENGTH, PREVIEW_PHOTO_MAX_LENGTH
            ) { bitmap, _, _, responseCode ->
                if (requestCode == responseCode) {
                    GLog.d(TAG, "[downloadPreviewBitmap] cost time ${GLog.getTime(start)} ms, bitmap:$bitmap")
                    block.invoke(bitmap)
                    // 一定removeSink，否则内存泄露
                    editingVM.sessionProxy.removeSink(IAvSink.AvSinkType.BITMAP, bitmapSink)
                }
            }
            requestCode = editingVM.sessionProxy.addAsOnceFrameSink(IAvSink.AvSinkType.BITMAP, bitmapSink) ?: 0
        }
    }

    private fun getEffectBitmap(block: (Bitmap?) -> Unit) {
        val isPreviewLoaded = vmBus?.get<Boolean>(TOPIC_IS_PREVIEW_LOADED) == true
        val inputThumbnail = vmBus?.get<Bitmap>(TOPIC_INPUTS_THUMBNAIL)
        // 如果从推荐进入页面，管线完全准备就绪需要1.3s，不等待，直接取缩图开始动画，否则视觉上会有卡顿
        if (isPreviewLoaded.not() && isSpecificStrategy() && (inputThumbnail != null)) {
            block(inputThumbnail)
        } else {
            downloadPreviewBitmap(block)
        }
    }

    private fun isSpecificStrategy(): Boolean {
        return when (getCurrentStrategy()?.strategyID) {
            R.id.strategy_specific_ai_deblur,
            R.id.strategy_specific_ai_dereflection,
            R.id.strategy_specific_ai_deglare,
            R.id.strategy_specific_ai_defog -> true

            else -> false
        }
    }

    private fun onRequestStart(inputBitmap: Bitmap) {
        GLog.d(TAG, "[onRequestStart] inputBitmap:$inputBitmap")
        updateRepairState(AIRepairState.START, mapOf(KEY_REQUEST_IMAGE to inputBitmap))
    }

    private fun onRepairEnd(result: AIRepairResult?) {
        GLog.d(TAG, "[onRepairEnd] result:$result")
        runOnUIThreadImmediate {
            handleRepairResult(result)
            trackInfo?.apply {
                this.cnt += 1
            }
        }
    }

    private fun handleRepairResult(result: AIRepairResult?) {
        trackInfo?.apply {
            this.costTime = System.currentTimeMillis() - this.startTime
        }
        if (result == null) {
            mediaItem?.let {
                trackAIUnitErrorEvent(it, trackInfo, null)
            }
            updateRepairState(AIRepairState.ERROR_UNKNOWN)
            return
        }
        result.apply {
            val isSuccess = (code == 0) && (specificCode == 0)
            if (isSuccess) {
                val extra = mask?.let {
                    mapOf(KEY_DE_REFLECTION_MASK to it)
                } ?: emptyMap()
                updateRepairState(AIRepairState.RESULT_OK, extra)
                return
            }
            val errorState = when {
                code == ErrorCode.kErrorTimeOut.value() -> AIRepairState.ERROR_TIMEOUT
                code == ErrorCode.KErrorNetworkUnavailable.value() ||
                        code == ErrorCode.kErrorNoInternet.value() -> AIRepairState.ERROR_NETWORK

                specificCode == ERROR_SERVICE_BUSY -> AIRepairState.ERROR_SERVICE_BUSY
                specificCode == ERROR_REACH_DAY_LIMIT -> AIRepairState.REACH_DAY_LIMIT
                specificCode == ERROR_SENSITIVE_CONTENT -> AIRepairState.ERROR_CONTENT_NOT_SUPPORT
                else -> AIRepairState.ERROR_UNKNOWN
            }
            updateRepairState(errorState)
        }

        mediaItem?.let {
            trackAIUnitErrorEvent(it, trackInfo, result)
        }
    }

    private fun emitRecord(bitmap: Bitmap) {
        val record = template?.let {
            if (it.type == AIRepairType.DEGLARE || it.type == AIRepairType.DEFOG) {
                AIRepairRealmeRecord(it.command, bitmap)
            } else {
                AIRepairRecord(it.command)
            }
        } ?: return
        trackInfo?.startTime = System.currentTimeMillis()
        emitRecord(record, OperatingType.ONLY_EFFECT) { effectUsage ->
            if (effectUsage == null) {
                GLog.e(TAG, LogFlag.DL, "[emitRecord] error, effectUsage is null")
                updateRepairState(AIRepairState.ERROR_UNKNOWN)
            } else {
                currentRecord = record
            }
        }
    }

    private fun pushStack() {
        currentRecord?.let {
            emitRecord(it, OperatingType.ONLY_RECORD)
            currentRecord = null
        }
    }

    private fun updateRepairState(state: AIRepairState, data: Map<String, Any> = emptyMap()) {
        if (isStateValid(state).not()) {
            GLog.d(TAG, "[updateRepairState] state is invalid")
            return
        }
        // 如果请求失败，则需要移除请求的记录
        removeEffectIfNeed(state)
        // 更新UI状态
        updateUIOperatingState(state)
        // 处理具体的结果
        handleResult(state)
        // 发布状态给上层处理
        template?.let {
            repairStatusPub?.publish(AIRepairStatus(state, it.type, data))
        }
    }

    private fun removeEffectIfNeed(state: AIRepairState) {
        when {
            state.isError -> {
                currentRecord?.let {
                    emitRecord(it, OperatingType.ONLY_UNDO_EFFECT)
                }
            }

            else -> Unit
        }
    }

    private fun updateUIOperatingState(state: AIRepairState) {
        when {
            state == AIRepairState.START -> {
                updateOperatingViewStateJob?.cancel()
                updateOperatingViewStateJob = null
                updateUIOperable(false)
            }

            state.isFinished -> updateUIOperable(true)
        }
    }

    /**
     * 处理具体的结果
     * @param state 修复状态
     */
    private fun handleResult(state: AIRepairState) {
        when (state) {
            AIRepairState.EFFECT_END -> pushStack()
            AIRepairState.ERROR_NETWORK -> template?.let { showRetryError(it.networkErrorMessage) }
            AIRepairState.ERROR_UNKNOWN -> template?.let { showRetryError(it.unknownErrorMessage) }
            AIRepairState.REACH_DAY_LIMIT -> showConfirmExitDialog(R.string.picture3d_editor_text_function_use_frequently_tips)
            AIRepairState.ERROR_SERVICE_BUSY -> showRetryError(R.string.picture3d_editor_text_function_is_too_many_people_using)
            AIRepairState.ERROR_CONTENT_NOT_SUPPORT -> {
                val message = if (isDomestic) {
                    template?.contentSensitiveMessage
                } else {
                    template?.contentSensitiveMessageOversea
                }
                message?.let { showConfirmExitDialog(it) }
                launch(Dispatchers.IO) { countRefuse() }
            }

            AIRepairState.ERROR_TIMEOUT -> showRetryError(R.string.picture3d_editor_text_error_because_network_is_bad)
            AIRepairState.ERROR_REPAIR_BAN -> showBanDialog()
            else -> Unit
        }
    }

    private fun showBanDialog() {
        val resource = editingVM.getApplication<Application>().resources
        val accurateTim: String = TimeUtils.getAccurateTime(ConfigAbilityWrapper.getLong(AI_REPAIR_BANNED_TIME, 0))
        val messageString = if (ConfigAbilityWrapper.getBoolean(IS_REGION_CN)) {
            String.format(resource.getString(R.string.picture3d_editor_text_banned_use_dialog_content), accurateTim)
        } else {
            resource.getString(R.string.picture3d_editor_text_unified_show_refuse_copywriting_oversea)
        }
        val action = NotificationAction.ConfirmDialogAction(
            titleString = resource.getString(R.string.picture3d_editor_text_banned_use_dialog_title),
            messageString = messageString,
            positiveButtonTextResId = com.oplus.gallery.basebiz.R.string.common_ok,
            confirmCallback = object : NotificationAction.IConfirmCallback {
                override fun onPositiveButtonClicked(activity: Activity) {
                    exit()
                }
            },
        )
        vmBus?.notifyOnce(TOPIC_NOTIFICATION_ACTION, action)
    }

    private fun exit() {
        vmBus?.notifyOnce(REPLY_TOPIC_OPERATING_ACTION, OperatingAction(R.id.action_finish_strategy))
    }

    private fun updateUIOperable(operable: Boolean) {
        vmBus?.notifyOnce(TOPIC_PREVIEW_IS_TOUCHABLE, operable)
        vmBus?.notifyOnce(TOPIC_PREVIEW_IS_SCROLLABLE, operable)
        if (operable) {
            restoreOperatingState()
        } else {
            updateOperatingViewState(titleBarOperable = false, compareButtonVisible = false, cancelable = false)
        }
    }

    private fun restoreOperatingState() {
        updateOperatingViewStateJob = launch(Dispatchers.UI) {
            updateOperatingViewState(titleBarOperable = true, compareButtonVisible = false)
            // 避免消除胶囊和对比按钮重叠，对比按钮需要延迟显示
            delay(SHOW_COMPARE_BUTTON_DELAY)
            updateOperatingViewState(titleBarOperable = true, compareButtonVisible = true)
            updateOperatingViewStateJob = null
        }
    }

    private fun isStateValid(state: AIRepairState): Boolean {
        val current = vmBus?.getForSubscriber<AIRepairStatus>(TOPIC_AI_REPAIR_REPAIR_STATUS, AIRepairSection.TAG)?.state ?: run {
            GLog.d(TAG, "[isStateValid] state is null")
            return true
        }
        // 如果请求已经被取消了，则状态需要重新开始，即认为非Start状态都是非法的
        if ((current == AIRepairState.CANCEL) && (state != AIRepairState.START)) {
            GLog.d(TAG, "[isStateValid] state is invalid, state is $state")
            return false
        }
        return true
    }

    private fun countRefuse() {
        app.getAppAbility<ISettingsAbility>()?.use {
            it.markAIFuncBanTime(
                SensitiveFuncBanItem.AI_REPAIR
            )
        }
    }

    private fun showRetryError(errorMessageID: Int) {
        val action = NotificationAction.ConfirmDialogAction(
            titleString = editingVM.getApplication<Application>().getString(errorMessageID),
            negativeButtonTextResId = com.oplus.gallery.basebiz.R.string.common_quit,
            positiveButtonTextResId = com.oplus.gallery.basebiz.R.string.common_retry,
            confirmCallback = object : NotificationAction.IConfirmCallback {
                override fun onPositiveButtonClicked(activity: Activity) {
                    startRepair()
                }

                override fun onNegativeButtonClicked() {
                    exit()
                }
            },
        )
        vmBus?.notifyOnce(TOPIC_NOTIFICATION_ACTION, action)
    }

    private fun showConfirmExitDialog(title: Int) {
        val resource = editingVM.getApplication<Application>().resources
        val action = NotificationAction.ConfirmDialogAction(
            titleString = resource.getString(title),
            positiveButtonTextResId = com.oplus.gallery.basebiz.R.string.common_ok,
            confirmCallback = object : NotificationAction.IConfirmCallback {
                override fun onPositiveButtonClicked(activity: Activity) {
                    exit()
                }
            },
        )
        vmBus?.notifyOnce(TOPIC_NOTIFICATION_ACTION, action)
    }

    private fun cancelRepair() {
        GLog.d(TAG, "[cancelRepair] type:$template")
        trackInfo?.let {
            AIRepairTrackHelper.trackRepairExitEvent(false, it)
        }
        updateRepairState(AIRepairState.CANCEL)
        currentRecord?.let {
            cancelEffect(it, true)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        vmBus?.apply {
            unregisterForSubscriber(TOPIC_AI_REPAIR_REPAIR_STATUS, AIRepairSection.TAG, repairStatusPub)
            unregisterForSubscriber(TOPIC_AI_REPAIR_INTRODUCTION_VIEW_DATE, AIRepairSection.TAG, introductionViewDataPub)
            unregister(REPLY_TOPIC_AI_REPAIR_NOTIFY_WINDOWS_FOCUS_CHANGED, windowsFocusChangeListener)
            unregister(REPLY_TOPIC_AI_REPAIR_CANCEL, cancelRepairChannel)
            unregister(REPLY_TOPIC_AI_REPAIR_EFFECT_END, effectEndChannel)
            unregister(REPLY_TOPIC_AI_REPAIR_CLICK_DETAIL_BUTTON, introductionConfirmChannel)
            unregister(REPLY_TOPIC_AI_REPAIR_INTRODUCTION_CLICK, introductionClickChannel)
            unsubscribe(TOPIC_OPERATING_EFFECT_USAGE, onEffectUsageChanged)
        }
        requestObserver.destroy()
        permissionObserver?.endObserve()
        updateUIOperable(true)
        isDestroy = true
    }

    internal companion object {

        /**
         * 服务器限流的错误码
         */
        private const val ERROR_SERVICE_BUSY = 429

        /**
         * 请求次数达到阈值，云端返回异常状态码
         */
        private const val ERROR_REACH_DAY_LIMIT = 3000429

        private const val SHOW_COMPARE_BUTTON_DELAY = 300L

        /**
         * Delay 300 ms后开始修复，此时是入场动画结束的时间
         */
        private const val START_REPAIR_DELAY = 300L

        /**
         * 敏感照片的异常码，返回此异常码时，表示算法拒绝处理此类型的照片，防止不可控的内容生成
         */
        const val ERROR_SENSITIVE_CONTENT = 3000603

        const val TAG = "AIRepairVM"
    }
}

/**
 * AI修复的记录
 * @param command 操作的命令
 */
internal class AIRepairRecord(command: String) : NonParametricRecord(
    cmd = AvEffect.AiRepairEffect.name,
    arguments = arrayMapOf(
        GLOBAL_KEY_EXECUTE_COMMAND to command,
        AI_REPAIR_RECORD_ID to generateId(),
    )
) {
    /**
     * AI修复不可修改
     */
    override val isModifiable: Boolean = false

    /**
     * 是否去过模糊，操作栈合并时，任意即录有去过模糊操作，即认为是去过模糊的
     */
    var isDeblured = (command == CMD_DEBLUR)
        private set

    /**
     * 是否去过反光，操作栈合并时，任意即录有去过反光操作，即认为是去过反光的
     */
    var isDeReflectioned = (command == CMD_DEREFLECTION)
        private set

    var isSharpen = (command == CMD_SHARPEN)
        private set

    override fun plusAssign(operatingRecord: OperatingRecord) {
        if (operatingRecord is AIRepairRecord) {
            isDeblured = operatingRecord.isDeblured || isDeblured
            isDeReflectioned = operatingRecord.isDeReflectioned || isDeReflectioned
            isSharpen = operatingRecord.isSharpen || isSharpen
        }

        operatingRecordList.addAll(0, operatingRecord.split())
    }


    companion object {
        /**
         * AI修复记录的ID的KEY，每次请求都应该是不一样的，用于区分缓存
         */
        private const val AI_REPAIR_RECORD_ID = "ai_repair_record_id"
        internal const val CMD_DEBLUR = "cmd_deblur"
        internal const val CMD_DEREFLECTION = "cmd_dereflection"
        internal const val CMD_SHARPEN = "cmd_sharpen"

        private fun generateId(): String {
            return System.currentTimeMillis().toString()
        }
    }
}

/**
 * AI照片编辑的记录, 端侧作去雾操
 * @param command 操作的命令
 */
internal class AIRepairRealmeRecord(command: String, bitmap: Bitmap) : NonParametricRecord(
    cmd = AvEffect.AiRepairRealmeEffect.name,
    arguments = arrayMapOf(
        GLOBAL_KEY_EXECUTE_COMMAND to command,
        AI_REPAIR_REALME_RECORD_ID to generateId(),
        KEY_INPUT_BITMAP to bitmap
    )
) {
    /**
     * AI照片编辑不可修改
     */
    override val isModifiable: Boolean = false

    /**
     * 是否去过眩光，操作栈合并时，任意即录有去过眩光操作，即认为是去过眩光的
     */
    var isDeglare = (command == CMD_DEGLARE)
        private set


    override fun plusAssign(operatingRecord: OperatingRecord) {
        if (operatingRecord is AIRepairRealmeRecord) {
            isDeglare = operatingRecord.isDeglare || isDeglare
        }

        operatingRecordList.addAll(0, operatingRecord.split())
    }


    companion object {
        /**
         * realme AI修复记录的ID的KEY，每次请求都应该是不一样的，用于区分缓存
         */
        private const val AI_REPAIR_REALME_RECORD_ID = "ai_repair_realme_record_id"
        const val KEY_INPUT_BITMAP = "key_input_bitmap"
        internal const val CMD_DEGLARE = "cmd_deglare"
        internal const val CMD_DEFOG = "cmd_defog"

        private fun generateId(): String {
            return System.currentTimeMillis().toString()
        }
    }
}