/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - EditImpl.kt
 ** Description: IEdit 实现类
 ** Version: 1.0
 ** Date: 2025/06/19
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>      <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>        2025/06/19  1.0          OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.olive.edit.engine2.impl

import android.util.Rational
import android.util.Size
import com.meicam.sdk.NvsStreamingContext
import com.meicam.sdk.NvsTimeline
import com.oplus.gallery.business_lib.menuoperation.item.exportvideo.IExportVideoCallback
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.adjust.IVideoAdjustEditor
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.adjust.VideoAdjustEditorImp
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.edge.IVideoEdgeEditor
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.edge.VideoEdgeEditorImp
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.engine2.IEdit
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.engine2.ISlowMotionEditor
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.engine2.SlowMotionEditorImpl
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.filter.IVideoFilterEditor
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.filter.VideoFilterEditorImp
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.transform.IVideoTransformEditor
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.transform.VideoTransformEditorImp
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.watermark.IVideoWatermarkEditor
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.watermark.VideoWatermarkEditorImp
import java.util.Hashtable

/**
 * IEdit 实现类
 * @param timeline 要进行编辑的 timeline 对象
 */
class EditImpl(private val nvsContext: NvsStreamingContext, private val timeline: NvsTimeline) : IEdit {

    override fun changeVideoFps(fpsRational: Rational): Boolean {
        return false
    }

    override fun getSlowMotionEditor(): ISlowMotionEditor {
        return SlowMotionEditorImpl()
    }

    override fun getFilterEditor(): IVideoFilterEditor {
        val nvsTrack = timeline.getVideoTrackByIndex(0)
        return VideoFilterEditorImp(timeline, nvsTrack, nvsTrack.getClipByIndex(0))
    }

    override fun getAdjustEditor(): IVideoAdjustEditor {
        val nvsTrack = timeline.getVideoTrackByIndex(0)
        return VideoAdjustEditorImp(timeline, nvsTrack, nvsTrack.getClipByIndex(0))
    }

    override fun getTransformEditor(): IVideoTransformEditor? {
        val nvsTrack = timeline.getVideoTrackByIndex(0)
        return VideoTransformEditorImp(nvsContext, timeline, nvsTrack, nvsTrack.getClipByIndex(0))
    }

    override fun getVideoEdgeEditor(): IVideoEdgeEditor? {
        val nvsTrack = timeline.getVideoTrackByIndex(0)
        return VideoEdgeEditorImp(nvsContext, timeline, nvsTrack, nvsTrack.getClipByIndex(0))
    }

    override fun getWatermarkEditor(): IVideoWatermarkEditor? {
        val nvsTrack = timeline.getVideoTrackByIndex(0)
        return VideoWatermarkEditorImp(nvsContext, timeline, nvsTrack, nvsTrack.getClipByIndex(0), Size(0, 0))
    }

    override fun exportVideo(videoType: Int, filePath: String?, configuration: Hashtable<String, Any>?) {
    }

    override fun registerExportVideoCallback(exportVideoCallback: IExportVideoCallback) {
    }

    override fun unRegisterExportVideoCallback() {
    }
}