/***********************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd.
 ** All rights reserved.
 **
 ** File: - AiDeGlareImageSizeInterceptor.kt
 ** Description: xxx
 ** Version: 1.0
 ** Date : 2025/4/23
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>      <data>      <version >     <desc>
 ** ------------------------------------------------------------
 ** gongziyao      2025/4/23       1.0          created
 ***************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.component.aideglare

import android.os.Bundle
import android.util.Size
import com.oplus.gallery.foundation.ui.notification.NotificationAction
import com.oplus.gallery.foundation.uiflowinterceptor.ToastInterceptor
import com.oplus.gallery.foundation.util.ext.maxLen
import com.oplus.gallery.foundation.util.ext.minLen
import com.oplus.gallery.photoeditor.R

/**
 * AI去眩光功能标签拦截器，拦截不符合标签的图片
 */
class AiDeGlareImageSizeInterceptor(
    private val size: Size? = null,
    updateUI: (NotificationAction) -> Unit
) : ToastInterceptor(updateUI) {

    override fun createToastAction(): NotificationAction.ToastAction {
        val title = if ((size?.minLen() ?: 0) < MIN_IMAGE_SIZE) {
            R.string.picture3d_editor_aihd_disable_too_small
        } else if ((size?.maxLen() ?: 0) > MAX_IMAGE_SIZE) {
            R.string.picture3d_editor_aihd_disable_too_large
        } else {
            R.string.picture3d_editor_aihd_disable_ratio_too_large
        }
        return NotificationAction.ToastAction(title)
    }

    override fun onCheckCondition(param: Bundle): Boolean {
        if (size == null) return true
        return (size.minLen() > MIN_IMAGE_SIZE) && (size.maxLen() < MAX_IMAGE_SIZE) && isRatioValid(size)
    }

    /**
     * 宽高比绝对值需要 <= 8
     */
    private fun isRatioValid(size: Size): Boolean {
        val ratio = size.maxLen() / size.minLen()
        return ratio <= RATIO
    }

    companion object {
        private const val MIN_IMAGE_SIZE = 256
        private const val MAX_IMAGE_SIZE = 6000
        private const val RATIO = 8
    }
}