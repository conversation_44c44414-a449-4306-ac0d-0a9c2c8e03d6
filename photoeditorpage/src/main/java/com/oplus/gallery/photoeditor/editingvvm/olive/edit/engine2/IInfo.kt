/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IInfo.kt
 ** Description: 视频的信息获取接口
 ** Version: 1.0
 ** Date: 2025/06/19
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>      <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>       2025/06/19  1.0          OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.olive.edit.engine2

import android.graphics.Bitmap
import android.graphics.ColorSpace
import android.util.Rational
import android.util.Size
import kotlinx.coroutines.flow.Flow

/**
 * 视频的信息获取接口
 */
interface IInfo {
    /**
     * 获取 timeline 当前的视频大小
     */
    fun getSize(): Size

    /**
     * 获取 timeline 当前的视频帧率
     */
    fun getFps(): Rational

    /**
     * 获取原始的视频信息
     */
    fun getInfo(): VideoInfo

    /**
     * 获取带特效的时间轴缩略图
     * 需要在主线程调用
     * @param timeUs 时间点，单位是微秒
     * @param maxLength 缩略图最大长度
     * @param config: 指定像素格式
     * @param colorSpace: 指定色彩空间
     * @return 返回时间点与缩略图映射
     */
    suspend fun getEffectThumbnail(
        timeUs: Long,
        maxLength: Int,
        config: Bitmap.Config? = null,
        colorSpace: ColorSpace? = null
    ): Pair<Long, Bitmap?>

    /**
     * 从原视频中抽帧，不带效果
     * 需要在IO线程中调用
     * @param timeUs 时间点，单位是微秒
     * @param isHdrVideoOlive olive视频是否是hdr视频
     */
    fun getNoEffectFrame(timeUs: Long, isHdrVideoOlive: Boolean): Bitmap?
}