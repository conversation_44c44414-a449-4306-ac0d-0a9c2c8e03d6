/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PlayImpl.kt
 ** Description: IPlay 实现类
 ** Version: 1.0
 ** Date: 2025/06/19
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>      <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>        2025/06/19  1.0          OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.olive.edit.engine2.impl

import android.graphics.Bitmap
import android.graphics.SurfaceTexture
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.engine2.IPlay

/**
 * IPlay 实现类
 * @param timeline 要进行编辑的 timeline 对象
 */
class PlayImpl : IPlay {

    override fun play(onFirstFrameReady: (() -> Unit)?): Boolean {
        return true
    }

    override fun stop() {
    }

    override fun seekTo(time: Long): Boolean {
        return true
    }

    override fun setSeekingListener(onSeekingListener: ((positionUs: Long) -> Unit)?) {
    }

    override fun setSeekCapturedFrameListener(capturedFrameListener: ((positionUs: Long, Bitmap?) -> Unit)?) {
    }

    override fun setPlayRange(start: Long, end: Long) {
    }

    override fun setMute(isMute: Boolean) {
    }

    override fun setPreviewSurfaceTexture(surfaceTexture: SurfaceTexture?, sizeLevel: Int): Boolean {
        return true
    }
}