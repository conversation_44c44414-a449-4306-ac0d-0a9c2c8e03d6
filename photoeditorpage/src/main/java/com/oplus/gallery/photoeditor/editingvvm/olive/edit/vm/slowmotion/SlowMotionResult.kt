/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 * File: - SlowMotionResult.kt
 * Description:
 * Version: 1.0
 * Date: 2025/07/18
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>    <desc>
 * ------------------------------------------------------------------------------
 * huca<PERSON><PERSON>@Apps.Gallery        2025/07/18      1.0        OPLUS_ARCH_EXTENDS
 ********************************************************************************/

package com.oplus.gallery.photoeditor.editingvvm.olive.edit.vm.slowmotion

import com.oplus.gallery.photoeditor.editingvvm.olive.record.OliveVideoRecord
import com.oplus.gallery.photoeditor.editingvvm.olive.record.SlowMotionRecord

/**
 * 慢动作的结果
 *
 * @param code 结果码
 * @param msg 结果描述
 * @param type 结果类型
 * @param progress 进度
 * @param multiple 慢动作插帧倍数
 * @param videoLow 低清视频路径
 * @param videoHigh 高清视频路径
 */
data class SlowMotionResult(
    var code: Int = 0,
    var msg: String? = null,
    var type: Int = 0,
    var progress: Float = 0f,
    var multiple: Float = OliveVideoRecord.DEFAULT_MULTIPLE,
    var videoLow: String? = null,
    var videoHigh: String? = null,
) {
    fun clear() {
        code = 0
        msg = null
        type = 0
        progress = 0f
        videoLow = null
        videoHigh = null
    }

    /**
     * 从map中获取结果
     *
     * @param map 结果map
     *
     * @return 结果
     */
    fun fill(map: Map<String, *>) {
        msg = map[SlowMotionRecord.RESULT_KEY_RSP_ERROR_MSG] as? String ?: msg
        type = map[SlowMotionRecord.RESULT_KEY_RSP_STATE] as? Int ?: type
        progress = map[SlowMotionRecord.RESULT_KEY_RSP_PROGRESS] as? Float ?: progress
        multiple = map[SlowMotionRecord.RESULT_KEY_RSP_RESULT_MULTIPLE] as? Float ?: multiple
        videoLow = map[SlowMotionRecord.RESULT_KEY_RSP_RESULT_VIDEO_LOW] as? String ?: videoLow
        videoHigh = map[SlowMotionRecord.RESULT_KEY_RSP_RESULT_VIDEO_HIGH] as? String ?: videoHigh
        // 若结果里有videoHigh或videoLow，则认为是成功的，code传0，否则传tbl插件传过来的code
        code = if (!videoLow.isNullOrEmpty() || !videoHigh.isNullOrEmpty()) 0 else map[SlowMotionRecord.RESULT_KEY_RSP_CODE] as? Int ?: code
    }

    companion object {
        /**
         * 默认的失败的结果
         */
        val FAIL_RESULT = mapOf(
            SlowMotionRecord.RESULT_KEY_RSP_CODE to SlowMotionRecord.ERROR_CODE_DEFAULT,
            SlowMotionRecord.RESULT_KEY_RSP_ERROR_MSG to null,
        )

        /**
         * 超时
         */
        val TIMEOUT_RESULT: Map<String, *> = mapOf(
            SlowMotionRecord.RESULT_KEY_RSP_CODE to SlowMotionRecord.CODE_EXEC_TIME_OUT,
            SlowMotionRecord.RESULT_KEY_RSP_ERROR_MSG to null,
        )

        /**
         * 用户取消
         */
        val USER_CANCEL_RESULT: Map<String, *> = mapOf(
            SlowMotionRecord.RESULT_KEY_RSP_CODE to SlowMotionRecord.CODE_EXEC_USER_CANCEL,
            SlowMotionRecord.RESULT_KEY_RSP_ERROR_MSG to null,
        )
    }
}