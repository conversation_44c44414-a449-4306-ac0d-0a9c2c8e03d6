/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 * File: - SlowMotionImpl.kt
 * Description:
 * Version: 1.0
 * Date: 2025/07/18
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>    <desc>
 * ------------------------------------------------------------------------------
 * huca<PERSON><PERSON>@Apps.Gallery        2025/07/18      1.0        OPLUS_ARCH_EXTENDS
 ********************************************************************************/

package com.oplus.gallery.photoeditor.editingvvm.olive

import android.util.Size
import com.meicam.sdk.NvsVideoStreamInfo.VIDEO_ROTATION_270
import com.meicam.sdk.NvsVideoStreamInfo.VIDEO_ROTATION_90
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.swap
import com.oplus.gallery.framework.abilities.editing.effect.AvEffect
import com.oplus.gallery.photoeditor.editingvvm.TObserver
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OLiveVideoEdit.TOPIC_OLIVE_SLOW_MOTION
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Olive.TOPIC_OLIVE_UI_STATE
import com.oplus.gallery.photoeditor.editingvvm.get
import com.oplus.gallery.photoeditor.editingvvm.notifyOnce
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.vm.slowmotion.SlowMotionResult
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.vm.slowmotion.statemachine.Event
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.vm.slowmotion.statemachine.SlowMotionStateMachine
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.vm.slowmotion.statemachine.State
import com.oplus.gallery.photoeditor.editingvvm.olive.record.OliveVideoRecord
import com.oplus.gallery.photoeditor.editingvvm.olive.record.SlowMotionRecord.Companion.CODE_SUCCESS
import com.oplus.gallery.photoeditor.editingvvm.olive.record.SlowMotionRecord.Companion.TYPE_RESPONSE_STATE_PROGRESS
import com.oplus.gallery.photoeditor.editingvvm.subscribeT
import com.oplus.gallery.photoeditor.statemachine.StateProcess
import com.oplus.gallery.standard_lib.app.UI
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.concurrent.TimeUnit
import kotlin.math.roundToLong

internal class SlowMotionImpl(
    private val oliveVM: OliveVM,
) : CoroutineScope by oliveVM {
    private val editingVM = oliveVM.editingVM

    private val vmBus = editingVM.viewModelBus

    private val slowMotionResult: SlowMotionResult?
        get() = vmBus?.get<SlowMotionResult>(TOPIC_OLIVE_SLOW_MOTION)

    private val machine = SlowMotionStateMachine(State.IDLE).apply {
        addProcess(StateProcess(State.IDLE, State.PROCESS, Event.BEGIN_PROCESS) {
            sendOpenging()
            vmBus?.subscribeT(TOPIC_OLIVE_SLOW_MOTION, oliveSlowMotionObserver)
            vmBus?.notifyOnce(TOPIC_OLIVE_SLOW_MOTION, true)
        })
        addProcess(StateProcess(State.PROCESS, State.IDLE, Event.MARKED_ERROR) {
            GLog.d(TAG, LogFlag.DL) { "MARKED_ERROR $slowMotionResult" }
            sendClose()
            vmBus?.unsubscribe(TOPIC_OLIVE_SLOW_MOTION, oliveSlowMotionObserver)
            vmBus?.notifyOnce(TOPIC_OLIVE_SLOW_MOTION, false)
        })
        addProcess(StateProcess(State.PROCESS, State.IDLE, Event.MARKED_SUCCESS) {
            GLog.d(TAG, LogFlag.DL) { "MARKED_SUCCESS $slowMotionResult" }
            sendSuccessRecord()
            sendOpen()
            vmBus?.unsubscribe(TOPIC_OLIVE_SLOW_MOTION, oliveSlowMotionObserver)
        })
        addProcess(StateProcess(State.PROCESS, State.IDLE, Event.USER_CANCEL) {
            GLog.d(TAG, LogFlag.DL) { "USER_CANCEL $slowMotionResult" }
            sendClose()
            vmBus?.unsubscribe(TOPIC_OLIVE_SLOW_MOTION, oliveSlowMotionObserver)
            vmBus?.notifyOnce(TOPIC_OLIVE_SLOW_MOTION, false)
        })
    }

    private val oliveSlowMotionObserver: TObserver<SlowMotionResult> = {
        if (it.type == TYPE_RESPONSE_STATE_PROGRESS) {
            sendOpenging()
        } else if (it.code == CODE_SUCCESS) {
            migrateState(Event.MARKED_SUCCESS)
        } else {
            migrateState(Event.MARKED_ERROR)
        }
    }

    fun deal(state: OLiveSlowMotionState) {
        GLog.d(TAG, LogFlag.DL) { "start: $state" }
        val lastRecord = oliveVM.findLastRecord<OliveVideoRecord>(AvEffect.OliveVideoEffect.name)
        if (lastRecord?.slowMotionPath != null) {
            val record = lastRecord.asRecord(
                slowMotionEnable = (state == OLiveSlowMotionState.OPEN)
            )
            oliveVM.emitRecord(record)
            when (state) {
                OLiveSlowMotionState.OPEN -> sendOpen()
                OLiveSlowMotionState.CLOSED -> sendClose()
                else -> Unit
            }
        } else {
            when (state) {
                OLiveSlowMotionState.OPEN -> migrateState(Event.BEGIN_PROCESS)
                OLiveSlowMotionState.CLOSED -> migrateState(Event.USER_CANCEL)
                OLiveSlowMotionState.OPENING -> migrateState(Event.USER_CANCEL)
            }
        }
    }

    /**
     * 发送当前可以显示慢动作的开启中
     */
    private fun sendOpenging() = launch(Dispatchers.UI.immediate) {
        vmBus?.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE)?.also { bean ->
            bean.id = OliveBeanId.OLIVE_SLOW_MOTION
            bean.slowMotionState = OLiveSlowMotionState.OPENING
            fillOliveBean(bean)
            oliveVM.oliveUiPub?.publish(bean)
        }
    }

    /**
     * 发送当前可以显示慢动作的开启
     */
    private fun sendOpen() = launch(Dispatchers.UI.immediate) {
        vmBus?.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE)?.also { bean ->
            bean.id = OliveBeanId.OLIVE_SLOW_MOTION
            bean.slowMotionState = OLiveSlowMotionState.OPEN
            fillOliveBean(bean)
            oliveVM.oliveUiPub?.publish(bean)
        }
    }

    /**
     * 发送当前可以显示慢动作的关闭
     */
    private fun sendClose() = launch(Dispatchers.UI.immediate) {
        vmBus?.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE)?.also { bean ->
            bean.id = OliveBeanId.OLIVE_SLOW_MOTION
            bean.slowMotionState = OLiveSlowMotionState.CLOSED
            fillOliveBean(bean)
            oliveVM.oliveUiPub?.publish(bean)
        }
    }

    /**
     * 修改状态机状态
     *
     * @param event 影响状态的事件
     */
    private fun migrateState(event: Event) {
        launch(Dispatchers.UI.immediate) {
            machine.doEvent(event)
        }
    }

    /**
     * 发送成功结果
     */
    private fun sendSuccessRecord() {
        val slowMotionResult = slowMotionResult ?: return
        val bean = vmBus?.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE) ?: OliveUiBean()
        bean.slowVideoPath = slowMotionResult.videoHigh ?: slowMotionResult.videoLow
        val videoInfo = bean.slowVideoPath?.let { oliveVM.oliveVideoEngine?.getInfo()?.getSpecificInfo(it)?.videoInfo }
        bean.slowDurationUs = videoInfo?.durationUs ?: bean.slowDurationUs
        bean.slowMotionStartUs = ((bean.coverTimeMs - DEFAULT_SLOW_RANGE).toFloat() / bean.totalDuration * bean.slowDurationUs).roundToLong()
            .coerceIn(0, bean.slowDurationUs)
        bean.slowMotionEndUs = ((bean.coverTimeMs + DEFAULT_SLOW_RANGE).toFloat() / bean.totalDuration * bean.slowDurationUs).roundToLong()
            .coerceIn(0, bean.slowDurationUs)
        bean.slowMotionMultiple = bean.slowDurationUs.toFloat() / TimeUnit.MILLISECONDS.toMicros(bean.totalDuration)
        oliveVM.oliveUiPub?.publish(bean)
        val slowMotionSize = when {
            videoInfo == null -> Size(0, 0)
            (videoInfo.rotation == VIDEO_ROTATION_90) || (videoInfo.rotation == VIDEO_ROTATION_270) -> videoInfo.size.swap()
            else -> videoInfo.size
        }
        val record = (oliveVM.findLastRecord(AvEffect.OliveVideoEffect.name) ?: OliveVideoRecord.createDefault()).asRecord(
            slowMotionEnable = true,
            slowMotionPath = bean.slowVideoPath,
            slowMotionMultiple = bean.slowMotionMultiple,
            slowMotionTrimInUs = bean.slowMotionStartUs,
            slowMotionTrimOutUs = bean.slowMotionEndUs,
            slowMotionVideoWidth = slowMotionSize.width,
            slowMotionVideoHeight = slowMotionSize.height,
            slowMotionDurationUs = bean.slowDurationUs
        )
        oliveVM.emitRecord(record)
    }

    /**
     * 填充 olive bean
     * @param bean olive bean
     */
    private fun fillOliveBean(bean: OliveUiBean) {
        val slowMotionResult = slowMotionResult ?: return
        bean.slowMotionOpeningProgress = slowMotionResult.progress.toInt()
        bean.slowMotionCode = slowMotionResult.code
        bean.slowMotionMsg = slowMotionResult.msg
    }

    companion object {
        private const val TAG = "SlowMotionPipelineImpl"

        /**
         * 默认慢动作区间范围
         */
        private const val DEFAULT_SLOW_RANGE = 500L
    }
}