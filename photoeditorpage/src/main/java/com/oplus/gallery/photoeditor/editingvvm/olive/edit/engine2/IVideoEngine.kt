/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IVideoEngine.kt
 ** Description: 视频引擎接口
 ** Version: 1.0
 ** Date: 2025/02/21
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>      <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>         2025/03/10  1.0          OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.olive.edit.engine2

import com.oplus.gallery.foundation.codec.extend.HdrTransformData

/**
 * 视频引擎接口
 * 实现播放、编辑以及信息获取接口
 */
internal interface IVideoEngine {

    /**
     * 创建 timeline
     * @param clip 构造 timeline 的切片
     * @param videoTransformConfig 下变换的配置
     */
    fun createTimeline(clip: Clip, videoTransformConfig: VideoTransformConfig)

    /**
     * 判断是否初始化完成
     * 用于外部有复杂逻辑需要确保对 engine 调用的接口能正确执行的情况，一般情况下，引擎内部自己判断没初始化过不执行即可
     */
    fun hasInit(): Boolean

    /**
     * 获取编辑对象，该对象用于修改timeline（包含其中的结构，如clip，特效等）
     */
    fun getIEdit(): IEdit?

    /**
     * 获取信息对象，该对象用于获取文件信息
     */
    fun getIInfo(): IInfo?

    /**
     * 获取播放对象，该对象用于播放
     */
    fun getIPlay(): IPlay?

    /**
     * 释放资源
     */
    fun release()
}

/**
 * 构造timeline的切片，用于描述如何构造 timeline
 * 比如：
 * 1.普通的 timeline，由单段视频组成，只需要一层 clip，包含 path 和 range
 * 2.复杂的 timeline，由多段视频组成，比如慢动作
 * Clip(listOf(
 *      clip1(path1) // 预览分辨率的原视频
 *      clip2(path2) // 插帧视频
 *      clip3(path1) // 预览分辨率的原视频
 *      clip4(path4) // 原视频
 * ))
 * @param path 文件路径
 * @param range 播放区间 ，比如一个 timeline 上是多段拼接的，每段都只播放一部分，需要控制 clip 的播放区间
 * @param childClips 子clip集合
 */
class Clip private constructor(
    val path: String? = null,
    val range: LongRange? = null,
    val childClips: List<Clip>? = null
) {
    constructor(path: String, range: LongRange) : this(path, range, childClips = null)
    constructor(childClips: List<Clip>?) : this(path = null, range = null, childClips)
}

/**
 * 视频下变换的配置类。提供视频在播放、抽帧等场景时的下变换配置
 *
 * @param hdrTransformData 下变换参数
 * @param needFXTransformToSdr 是否需要美摄CustomFX转换成sdr
 * @param forceSdr 强制设置为SDR的timeline，默认为false，只有GIF场景设置为true
 * @param hdrSdrRatioFunc 获取当前HDR/SDR比例的回调方法，默认实现为回调1.0f，即显示sdr效果
 */
data class VideoTransformConfig(
    val hdrTransformData: HdrTransformData?,
    val needFXTransformToSdr: Boolean = false,
    val forceSdr: Boolean = false,
    val hdrSdrRatioFunc: () -> Float = { 1.0f }
)