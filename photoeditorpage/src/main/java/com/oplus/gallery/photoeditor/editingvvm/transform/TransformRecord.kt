/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - TransformRecord
 ** Description: 裁剪旋转的操作记录
 ** Version: 1.0
 ** Date : 2024/7/22
 ** Author: youpeng@Apps.Gallery3D
 ** TAG: TransformRecord
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  youpeng@Apps.Gallery3D      2024/07/22    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.transform

import android.graphics.RectF
import androidx.collection.ArrayMap
import androidx.collection.arrayMapOf
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.foundation.opengl.transform.AxisAngleTransform
import com.oplus.gallery.foundation.opengl.transform.Matrix
import com.oplus.gallery.foundation.opengl.transform.Pose
import com.oplus.gallery.foundation.opengl.transform.Vector
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.toList
import com.oplus.gallery.foundation.util.ext.toRectF
import com.oplus.gallery.foundation.util.math.MathUtil.areFloatsEqual
import com.oplus.gallery.foundation.util.math.MathUtils
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.editing.effect.AvEffect
import com.oplus.gallery.photoeditor.common.Config
import com.oplus.gallery.photoeditor.editingvvm.imagequalityenhance.ImageQualityEnhanceRecord
import com.oplus.gallery.photoeditor.editingvvm.operating.DoubleArrayListToFloatArrayConverter
import com.oplus.gallery.photoeditor.editingvvm.operating.DynamicParametricRecord
import com.oplus.gallery.photoeditor.editingvvm.operating.FloatToIntConverter
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecord
import com.oplus.gallery.photoeditor.editingvvm.transform.TransformRecord.Companion.ARGUMENT_KEY_CLIP_RECT
import com.oplus.gallery.photoeditor.editingvvm.transform.TransformRecord.Companion.ARGUMENT_KEY_DISPLAY_BOUNDS
import com.oplus.gallery.photoeditor.editingvvm.transform.TransformRecord.Companion.ARGUMENT_KEY_HORIZONTAL_TILT
import com.oplus.gallery.photoeditor.editingvvm.transform.TransformRecord.Companion.ARGUMENT_KEY_BASE_MATRIX
import com.oplus.gallery.photoeditor.editingvvm.transform.TransformRecord.Companion.ARGUMENT_KEY_MATRIX
import com.oplus.gallery.photoeditor.editingvvm.transform.TransformRecord.Companion.ARGUMENT_KEY_VERTICAL_TILT
import com.oplus.gallery.photoeditor.editingvvm.transform.TransformRecord.Companion.ARGUMENT_KEY_VIEWPORT_HEIGHT
import com.oplus.gallery.photoeditor.editingvvm.transform.TransformRecord.Companion.ARGUMENT_KEY_VIEWPORT_WIDTH
import com.oplus.gallery.photoeditor.editingvvm.transform.TransformRecord.Companion.HORIZONTAL_TILT_SWITCH
import com.oplus.gallery.photoeditor.editingvvm.transform.TransformRecord.Companion.ROTATE_ORIENTATION
import com.oplus.gallery.photoeditor.editingvvm.transform.TransformRecord.Companion.RULE_ANGLE_SWITCH
import com.oplus.gallery.photoeditor.editingvvm.transform.TransformRecord.Companion.VERTICAL_TILT_SWITCH

/**
 * 裁剪旋转的操作记录
 * @param rotate 旋转按钮状态
 * @param ruleAngle 校正的旋转值
 * @param ruleAngleSwitch 校正按钮的开关状态
 * @param ratio 比例信息
 * @param matrix 图片的矩阵数据
 * @param clipRect 裁剪区域，类型为List，顺序为 left，top，right，bottom，不用 RectF 的原因是序列化无法识别，需要用基础类型，下同
 * @param imageRect 显示区域，类型为List，顺序为 left，top，right，bottom
 * @param horizontalTilt 水平校正的数值
 * @param horizontalTiltSwitch 水平校正的开关
 * @param verticalTilt 垂直校正的数值
 * @param verticalTiltSwitch 垂直校正的开关
 * @param axisAngleTransform 轴角变换
 * @param matrixWithoutTilt 排除梯形等特殊变换的矩阵，用于裁剪旋转页预览还原，默认与 [matrix] 相同
 */
@Suppress("LongParameterList")
internal class TransformRecord(
    // 算法必要的5个参数 (见TBL算法插件仓库 TransformAlgoImpl.execute->checkAndGetTransformIO->getTransformArguments; + drawTexture;)
    val matrix: Matrix = Matrix(),
    val clipRect: List<Float> = emptyList(),
    val imageRect: List<Float> = emptyList(),
    val viewportWidth: Int = 0,
    val viewportHeight: Int = 0,
    // 裁剪编辑页UI还原必要的参数 (见 TransformUIBean)
    val rotate: RotateOrientation = RotateOrientation.ROTATE_TOP,
    val ruleAngle: Int = MathUtils.ZERO,
    val ruleAngleSwitch: Boolean = true,
    val isHorizontalMirror: Boolean = false,
    val ratio: Ratio = Ratio.RATIO_FREE,
    val horizontalTilt: Int = MathUtils.ZERO,
    val horizontalTiltSwitch: Boolean = true,
    val verticalTilt: Int = MathUtils.ZERO,
    val verticalTiltSwitch: Boolean = true,
    // 高阶UI还原可能会用到的参数
    val axisAngleTransform: AxisAngleTransform? = null,
    val matrixWithoutTilt: Matrix = matrix
) : DynamicParametricRecord(
    version = TRANSFORM_RECORD_VERSION,
    effectName = AvEffect.TransformEffect.name,
    arguments = arrayMapOf<String, Any>().also {
        // 裁剪旋转固定命令
        it[GLOBAL_KEY_EXECUTE_COMMAND] = ARGUMENT_VAL_TRANSFORM_COMMAND
        // 裁剪算法参数组
        it[ARGUMENT_KEY_MATRIX] = matrix.matrix
        it[ARGUMENT_KEY_CLIP_RECT] = clipRect
        it[ARGUMENT_KEY_DISPLAY_BOUNDS] = imageRect
        it[ARGUMENT_KEY_VIEWPORT_WIDTH] = viewportWidth
        it[ARGUMENT_KEY_VIEWPORT_HEIGHT] = viewportHeight
        it[HORIZONTAL_TILT_SWITCH] = horizontalTiltSwitch
        it[VERTICAL_TILT_SWITCH] = verticalTiltSwitch
        it[ARGUMENT_KEY_HORIZONTAL_TILT] = horizontalTilt
        it[ARGUMENT_KEY_VERTICAL_TILT] = verticalTilt
        // 裁剪编辑页UI还原组
        it[ROTATE_ORIENTATION] = rotate.angle
        it[RULE_ANGLE_SWITCH] = ruleAngleSwitch
        it[RULE_ANGLE] = ruleAngle
        it[IS_HORIZONTAL_MIRROR] = isHorizontalMirror
        it[RATIO] = when (ratio) {
            Ratio.RATIO_FREE -> Ratio.RATIO_FREE.value
            Ratio.RATIO_ORIGINAL -> Ratio.RATIO_ORIGINAL.value
            else -> Config.RATIO.RATIO_CUSTOMIZE
        }
        // 高阶UI还原可能会用到的参数
        it[ROTATE_AXIS] = axisAngleTransform?.axis?.value
        it[ROTATE_ANGLE] = axisAngleTransform?.angle
        it[ARGUMENT_KEY_BASE_MATRIX] = matrixWithoutTilt.matrix
    }) {

    /**
     * 是否已添加过画质增强效果
     */
    var hasQualityEnhanceEffect: Boolean = false
        set(value) {
            field = value
            if (value) {
                GLog.d(TransformVM.TAG, LogFlag.DL) { "[hasQualityEnhanceEffect] is added quality enhance effect" }
                // 如果添加了画质增强效果，是非单特效的，需要设置为false
                _isModifiable = false
            }
        }

    private var _isParametric = ConfigAbilityWrapper.getBoolean(ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_PROJECT_SAVE, false)

    override val isParametric: Boolean
        get() = _isParametric

    /**
     * 只有支持参数化，并且未添加过画质增强时，才是单特效的。
     * 1. 支持参数化时：
     *   1.1. 如果没有添加过画质增强效果。为单特效，设置为true
     *   1.2. 如果添加了画质增强效果。非单特效，需要设置为false
     * 2. 不支持参数化时：不论是否有画质增强效果，都非单特效，设置为false
     */
    private var _isModifiable = _isParametric

    override val isModifiable: Boolean
        get() = _isModifiable

    /**
     * 收集记录,多条记录合并后，保留部分都存入此处
     */
    val recordList: MutableList<OperatingRecord> = mutableListOf<OperatingRecord>().apply { add(this@TransformRecord) }

    /**
     * 用于应用变换的垂直校正的数值
     */
    val verticalTiltForApplyTransform: Int
        get() = if (verticalTiltSwitch) verticalTilt else 0

    /**
     * 用于应用变换的水平校正的数值
     */
    val horizontalTiltForApplyTransform: Int
        get() = if (horizontalTiltSwitch) {
            if (isHorizontalMirror) -horizontalTilt else horizontalTilt
        } else 0

    override fun plusAssign(operatingRecord: OperatingRecord) {
        //从裁剪旋转进行画质增强保存的时候，会将_isParametric改为false
        _isParametric = _isParametric and operatingRecord.isParametric
        _isModifiable = _isModifiable  and operatingRecord.isModifiable
        val list = when (operatingRecord) {
            is TransformRecord -> {
                recordList.clear()
                operatingRecord.split()
            }

            is ImageQualityEnhanceRecord -> {
                _isModifiable = false
                operatingRecord.split()
            }

            else -> operatingRecord.split()
        }
        recordList.addAll(0, list)
    }

    override fun split(): List<OperatingRecord> {
        return recordList
    }

    override fun standardize() {
        super.standardize()
        standardizeConverters.forEach {
            it.second.forEach { key ->
                runCatching {
                    arguments[key] = it.first.convert(arguments[key])
                }.onFailure {
                    GLog.e(TAG, LogFlag.DL) {
                        "[standardize] error in convert argument: $key, error: $it"
                    }
                }
            }
        }
    }

    override fun argumentsForAlgo(algoArguments: List<AvEffect.Argument>?): Map<String, Any> {
        return arguments.filterKeys {
            it == ARGUMENT_KEY_MATRIX || it == ARGUMENT_KEY_CLIP_RECT || it == ARGUMENT_KEY_DISPLAY_BOUNDS
                    || it == ARGUMENT_KEY_VIEWPORT_WIDTH || it == ARGUMENT_KEY_VIEWPORT_HEIGHT || it == GLOBAL_KEY_EXECUTE_COMMAND
        }.toMutableMap().also { filterArguments ->
            // 传给算法的 RectF 需要转为 RectF 类型
            convertArgumentsValuesFromListToRect(filterArguments, ARGUMENT_KEY_CLIP_RECT)
            convertArgumentsValuesFromListToRect(filterArguments, ARGUMENT_KEY_DISPLAY_BOUNDS)
        }
    }

    internal fun parseRotate(degree: Int) = when (degree) {
        RotateOrientation.ROTATE_TOP.angle -> RotateOrientation.ROTATE_TOP
        RotateOrientation.ROTATE_RIGHT.angle -> RotateOrientation.ROTATE_RIGHT
        RotateOrientation.ROTATE_BOTTOM.angle -> RotateOrientation.ROTATE_BOTTOM
        RotateOrientation.ROTATE_LEFT.angle -> RotateOrientation.ROTATE_LEFT
        else -> RotateOrientation.ROTATE_TOP
    }

    internal fun parseRuleAngle(map: ArrayMap<String, Any>) = if (map[RULE_ANGLE] is Int) {
        map[RULE_ANGLE] as Int
    } else {
        (map[RULE_ANGLE] as? Float)?.toInt() ?: MathUtils.ZERO
    }

    internal fun parseRatio(ratio: Any?, clipRatio: Float) = when (ratio) {
        Ratio.RATIO_FREE.value -> Ratio.RATIO_FREE
        Ratio.RATIO_ORIGINAL.value -> Ratio.RATIO_ORIGINAL
        Config.RATIO.RATIO_CUSTOMIZE -> {
            if (clipRatio.areFloatsEqual(Config.RATIO.RATIO_MOVIE, MathUtils.ZERO_F)) {
                Ratio.RATIO_FREE
            } else if (clipRatio.areFloatsEqual(Config.RATIO.RATIO_MOVIE, TransformVM.FLOAT_FACTOR)) {
                Ratio.RATIO_MOVIE
            } else if (clipRatio.areFloatsEqual(Config.RATIO.RATIO_SQUARE, TransformVM.FLOAT_FACTOR)) {
                Ratio.RATIO_1X1
            } else if (clipRatio.areFloatsEqual(Config.RATIO.RATIO_4_X_3, TransformVM.FLOAT_FACTOR)) {
                Ratio.RATIO_4X3
            } else if (clipRatio.areFloatsEqual(Config.RATIO.RATIO_3_X_4, TransformVM.FLOAT_FACTOR)) {
                Ratio.RATIO_3X4
            } else if (clipRatio.areFloatsEqual(Config.RATIO.RATIO_16_X_9, TransformVM.FLOAT_FACTOR)) {
                Ratio.RATIO_16X9
            } else if (clipRatio.areFloatsEqual(Config.RATIO.RATIO_9_X_16, TransformVM.FLOAT_FACTOR)) {
                Ratio.RATIO_9X16
            } else if (clipRatio.areFloatsEqual(Config.RATIO.RATIO_3_X_2, TransformVM.FLOAT_FACTOR)) {
                Ratio.RATIO_3X2
            } else if (clipRatio.areFloatsEqual(Config.RATIO.RATIO_2_X_3, TransformVM.FLOAT_FACTOR)) {
                Ratio.RATIO_2X3
            } else {
                Ratio.RATIO_FREE
            }
        }

        else -> Ratio.RATIO_FREE
    }

    internal fun parseAxisAngleTransform(map: ArrayMap<String, Any>): AxisAngleTransform {
        val rotateAxis = if (map[ROTATE_AXIS] is ArrayList<*>) {
            val rotateArray = map[ROTATE_AXIS] as ArrayList<Double>
            if (rotateArray.size == Vector.ELEM_COUNT) {
                Vector(rotateArray[0].toFloat(), rotateArray[1].toFloat(), rotateArray[2].toFloat(), Vector.POINT)
            } else {
                Vector()
            }
        } else {
            Vector()
        }
        val rotateAngle = (map[ROTATE_ANGLE] as? Float) ?: 0f
        return AxisAngleTransform(rotateAxis, rotateAngle)
    }

    companion object {
        /**
         * 当前transformRecord版本号.
         * 注意: 如果TransformRecord有增加或减少序列化的字段, 则应考虑考升高这里的版本号,
         *      以免在 "新版本->旧版本(升级),或者旧版本->新版本(降级)"时, 出现crash
         */
        const val TRANSFORM_RECORD_VERSION = 0.2f

        const val ROTATE_AXIS = "rotate_axis"
        const val ROTATE_ANGLE = "rotateAngle"
        const val RULE_ANGLE = "ruleAngle"
        const val RULE_ANGLE_SWITCH = "ruleAngleSwitch"
        const val RATIO = "ratio"
        const val ROTATE_ORIENTATION = "rotateOrientation"
        const val IS_HORIZONTAL_MIRROR = "isHorizontalMirror"
        const val HORIZONTAL_TILT_SWITCH = "horizontalTiltSwitch"
        const val VERTICAL_TILT_SWITCH = "verticalTiltSwitch"

        /**
         * 水平校正的值
         */
        const val ARGUMENT_KEY_HORIZONTAL_TILT = "horizontalTilt"

        /**
         * 垂直校正的值
         */
        const val ARGUMENT_KEY_VERTICAL_TILT = "verticalTilt"

        /**
         * Key值 - 图片显示区域，通常对应图片大小
         */
        const val ARGUMENT_KEY_DISPLAY_BOUNDS = "key_transform_display_bounds"

        /**
         * Key值 - 裁剪旋转的最终变换矩阵，长度16，即4x4矩阵
         */
        const val ARGUMENT_KEY_MATRIX = "key_transform_matrix"

        /**
         * Key值 - 裁剪旋转的基本变换（排除梯形等特殊变换）矩阵，长度16，即4x4矩阵
         */
        const val ARGUMENT_KEY_BASE_MATRIX = "key_transform_base_matrix"

        /**
         * Key值 - 图片裁剪区域
         */
        const val ARGUMENT_KEY_CLIP_RECT = "key_transform_clip_rect"

        /**
         * Key值 - 设置视口宽度，即Surface宽度
         */
        const val ARGUMENT_KEY_VIEWPORT_WIDTH = "key_transform_viewport_width"

        /**
         * Key值 - 设置视口高度，即Surface高度
         */
        const val ARGUMENT_KEY_VIEWPORT_HEIGHT = "key_transform_viewport_height"

        /**
         * Value值 - 裁剪旋转算法的指令
         */
        const val ARGUMENT_VAL_TRANSFORM_COMMAND = "cmd_transform_draw"

        /**
         * 标准化转换时，哪些Key对应的值需要做什么转换
         */
        private val standardizeConverters = arrayOf(
            /**
             * ArrayList<Double>转FloatArray
             */
            DoubleArrayListToFloatArrayConverter() to arrayOf(
                ARGUMENT_KEY_MATRIX,
                ARGUMENT_KEY_BASE_MATRIX
            ),
            /**
             * Float转Int
             */
            FloatToIntConverter() to arrayOf(
                ARGUMENT_KEY_VIEWPORT_WIDTH,
                ARGUMENT_KEY_VIEWPORT_HEIGHT,
                RULE_ANGLE,
                ARGUMENT_KEY_HORIZONTAL_TILT,
                ARGUMENT_KEY_VERTICAL_TILT
            )
        )
    }

    override fun toString(): String {
        return "Parametric(ruleAngle: $ruleAngle,\n" +
                "ruleAngleSwitch: $ruleAngleSwitch, \n" +
                "horizontalTilt:$horizontalTilt,\n" +
                "horizontalSwitch:$horizontalTiltSwitch,\n" +
                "verticalTilt:$verticalTilt,\n" +
                "verticalTiltSwitch:$verticalTiltSwitch,\n" +
                "matrix=$matrix, \n" +
                "imageRect = $imageRect clipRect=$clipRect, \n" +
                "viewportWidth=$viewportWidth, viewportHeight=$viewportHeight, \n" +
                "rotate=$rotate, ruleAngle=$ruleAngle, isHorizontalMirror=$isHorizontalMirror, ratio=$ratio), isModifiable=$isModifiable\n"
    }
}

/**
 * OperatingRecord转成TransformRecord,需要兼容处理数据从json反序列化成OperatingRecord之后的数据处理
 * @return [TransformRecord]
 */
internal fun OperatingRecord.asTransformRecord(): TransformRecord {
    val record = TransformRecord()
    val map = this.arguments
    val matrix = Matrix(map[ARGUMENT_KEY_MATRIX] as FloatArray)
    // 旧数据无此字段，兼容处理
    val matrixWithoutTilt = Matrix((map[ARGUMENT_KEY_BASE_MATRIX] as? FloatArray) ?: matrix.matrix)
    val pose = Pose()
    pose.apply(matrix)
    val ruleAngle = record.parseRuleAngle(map)
    val ruleAngleSwitch = map[RULE_ANGLE_SWITCH] as? Boolean ?: true
    val rotateOrientation = map[ROTATE_ORIENTATION] as? Number ?: let {
        GLog.w(TransformVM.TAG, LogFlag.DL) { "[asTransformRecord] rotateOrientation is null" }
        0
    }
    val rotate = record.parseRotate(rotateOrientation.toInt())
    val clipRect = if (map[ARGUMENT_KEY_CLIP_RECT] is List<*>) {
        (map[ARGUMENT_KEY_CLIP_RECT] as List<Float>).toRectF()
    } else {
        RectF()
    }

    val imageRect = if (map[ARGUMENT_KEY_DISPLAY_BOUNDS] is List<*>) {
        (map[ARGUMENT_KEY_DISPLAY_BOUNDS] as List<Float>).toRectF()
    } else {
        RectF()
    }

    val clipRatio = clipRect.width() / clipRect.height()
    val isHorizontalMirror = map[TransformRecord.IS_HORIZONTAL_MIRROR] as? Boolean ?: let {
        GLog.w(TransformVM.TAG, LogFlag.DL) { "[asTransformRecord] isHorizontalMirror is null" }
        false
    }
    var ratio = map[TransformRecord.RATIO]
    ratio = record.parseRatio(ratio, clipRatio)
    val axisAngleTransform = record.parseAxisAngleTransform(map)
    val horizontalTiltSwitch = map[HORIZONTAL_TILT_SWITCH] as? Boolean ?: true
    val verticalTiltSwitch = map[VERTICAL_TILT_SWITCH] as? Boolean ?: true
    val horizontalTiltValue = map[ARGUMENT_KEY_HORIZONTAL_TILT] as? Int ?: 0
    val verticalTiltValue = map[ARGUMENT_KEY_VERTICAL_TILT] as? Int ?: 0
    GLog.d(TransformVM.TAG, LogFlag.DL) {
        "[asTransformRecord] rotateOrientation:$rotateOrientation, rotate:$rotate, isHorizontalMirror:$isHorizontalMirror, " +
                "ruleAngle:$ruleAngle, horizontalTiltValue:$horizontalTiltValue, verticalTiltValue:$verticalTiltValue"
    }
    return TransformRecord(
        // 算法必须
        matrix = matrix,
        clipRect = clipRect.toList(),
        imageRect = imageRect.toList(),
        viewportWidth = map[ARGUMENT_KEY_VIEWPORT_WIDTH] as? Int ?: 0,
        viewportHeight = map[ARGUMENT_KEY_VIEWPORT_HEIGHT] as? Int ?: 0,
        // UI还原必须
        rotate = rotate,
        ruleAngle = ruleAngle,
        ruleAngleSwitch = ruleAngleSwitch,
        horizontalTilt = horizontalTiltValue,
        horizontalTiltSwitch = horizontalTiltSwitch,
        verticalTilt = verticalTiltValue,
        verticalTiltSwitch = verticalTiltSwitch,
        isHorizontalMirror = isHorizontalMirror,
        ratio = ratio,
        // 高阶UI还原可能会用到的参数
        axisAngleTransform = axisAngleTransform,
        matrixWithoutTilt = matrixWithoutTilt
    )
}