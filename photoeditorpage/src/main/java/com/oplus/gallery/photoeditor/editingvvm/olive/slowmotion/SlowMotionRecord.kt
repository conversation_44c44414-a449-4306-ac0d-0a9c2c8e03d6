/*******************************************************************************
 *  Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 *  All rights reserved.
 *   File: - SlowMotionRecord.kt
 *  Description:
 *   Version: 1.0
 *  Date: 2025/06/12
 *  Author: <EMAIL>
 *  TAG: OPLUS_ARCH_EXTENDS
 *  ------------------------------- Revision History: ----------------------------
 *  <author>                        <date>       <version>    <desc>
 *  ------------------------------------------------------------------------------
 *  huca<PERSON><PERSON>@Apps.Gallery        2025/06/12      1.0        OPLUS_ARCH_EXTENDS
 ********************************************************************************/

package com.oplus.gallery.photoeditor.editingvvm.olive.slowmotion

import androidx.collection.arrayMapOf
import com.oplus.gallery.foundation.fileaccess.GalleryFileProvider
import com.oplus.gallery.framework.abilities.editing.effect.AvEffect
import com.oplus.gallery.photoeditor.editingvvm.operating.NonParametricRecord

class SlowMotionRecord(fileDirPath: String) : NonParametricRecord(
    cmd = AvEffect.AiLightingEffect.name,
    arguments = arrayMapOf(
        GLOBAL_KEY_EXECUTE_COMMAND to CMD_SLOW_MOTION_START,
        KEY_FILE_PROVIDER_AUTHORITY to GalleryFileProvider.GALLERY_AUTHORITY,
        KEY_FILE_DIR_PATH to fileDirPath,
    )
) {
    override val isModifiable: Boolean = false

    companion object {
        const val CMD_SLOW_MOTION_START = "slowmotion_plugin_cmd_start"
        const val KEY_FILE_PROVIDER_AUTHORITY = "file_provider_authority"
        const val KEY_FILE_DIR_PATH = "file_dir_path"
        const val KEY_SLOW_MOTION_RESULT = "slow_motion_result"

        const val RESULT_KEY_RESPONSE_CODE = "response_code"
        const val RESULT_KEY_RESPONSE_ERROR_MSG = "response_error_msg"
        const val RESULT_KEY_RESPONSE_RESULT_VIDEOS = "response_result_videos"
    }
}