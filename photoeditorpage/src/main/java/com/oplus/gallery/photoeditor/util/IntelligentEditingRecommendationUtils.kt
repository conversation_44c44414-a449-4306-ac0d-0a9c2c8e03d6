/*********************************************************************************
 ** Copyright (C), 2024-2034, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : IntelligentEditingRecommendationUtils.kt
 ** Description : 智能编辑推荐工具类
 ** Version     : 1.0
 ** Date        : 2024/11/29
 ** Author      : lixu
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>   <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** lixu     2024/11/29     1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.photoeditor.util

import com.oplus.gallery.foundation.archv2.bus.IViewModelBus
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.editing.effect.AvEffect
import com.oplus.gallery.photoeditor.editingvvm.TopicID
import com.oplus.gallery.photoeditor.editingvvm.aibesttake.AIBestTakeRecord
import com.oplus.gallery.photoeditor.editingvvm.ailighting.bean.AiLightingBlendRecord
import com.oplus.gallery.photoeditor.editingvvm.airepair.AIRepairRealmeRecord
import com.oplus.gallery.photoeditor.editingvvm.airepair.AIRepairRecord
import com.oplus.gallery.photoeditor.editingvvm.eliminate.EliminateVM
import com.oplus.gallery.photoeditor.editingvvm.imagequalityenhance.ImageQualityEnhanceRecord
import com.oplus.gallery.photoeditor.editingvvm.notifyOnce
import com.oplus.gallery.photoeditor.editingvvm.operating.FindRecordParams
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecord
import com.oplus.gallery.photoeditor.editingvvm.operating.RecordFindType
import com.oplus.gallery.photoeditor.editingvvm.operating.STACK_INDEX_ALL
import com.oplus.gallery.photoeditor.editingvvm.transform.TransformRecord

/**
 * 智能编辑推荐工具类
 */
object IntelligentEditingRecommendationUtils {
    private const val TAG = "IntelligentEditingRecommendationUtils"

    /**
     * 判断图片是否做过画质增强
     *
     * @param vmBus  ViewModelBus
     * @return true: 画质增强过，false: 未画质增强过
     */
    @JvmStatic
    fun isImageQualityEnhanced(vmBus: IViewModelBus?): Boolean {
        var isEnhanced = false
        getMultiEffectNameList(AvEffect.ImageQualityEnhanceEffect.name)?.apply {
            for (effectName in this) {
                if (isEnhanced) {
                    break
                }
                GLog.d(TAG, LogFlag.DF) { "[isImageQualityEnhanced] $effectName" }
                getRecordListFromEffectName(effectName, vmBus)?.forEach {
                    if (isEnhanced) {
                        return@forEach
                    }
                    isEnhanced = if (it is ImageQualityEnhanceRecord) {
                        GLog.d(TAG, LogFlag.DF) { "[isImageQualityEnhanced] isEnhanced:${it.isEnhanced}" }
                        it.isEnhanced
                    } else {
                        val optRecordList = getOperatingRecordList(effectName, it)
                        // 其他入口，经过画质增强后再调节其他特效，此时特效经过combine后需要通过recordList再判断是否画质增强过
                        isImageQualityEnhancedFromRecordList(optRecordList)
                    }
                }
            }
        }
        return isEnhanced
    }

    /**
     * 判断图片是否做过AI修复：AI去反光和AI去阴影
     *
     * @param vmBus  ViewModelBus
     * @return Pair.first: true: 去阴影过,false: 未去阴影过 ; Pair.second:  true:去反光过,false: 未去反光过
     */
    @JvmStatic
    fun isAiRepairPerformed(vmBus: IViewModelBus?): Pair<Boolean, Boolean> {
        var isDeblured = false
        var isDeReflectioned = false
        getMultiEffectNameList(AvEffect.AiRepairEffect.name)?.apply {
            for (effectName in this) {
                if (isDeblured && isDeReflectioned) {
                    break
                }
                GLog.d(TAG, LogFlag.DF) { "[isAiRepairPerformed] $effectName" }
                getRecordListFromEffectName(effectName, vmBus)?.forEach {
                    if (isDeblured && isDeReflectioned) {
                        return@forEach
                    }
                    if (it is AIRepairRecord) {
                        GLog.d(TAG, LogFlag.DF) { "[isAiRepairPerformed] isDeblured:${it.isDeblured}, isDeReflectioned:${it.isDeReflectioned}" }
                        isDeblured = it.isDeblured || isDeblured
                        isDeReflectioned = it.isDeReflectioned || isDeReflectioned
                    }
                    if (isDeblured.not() || isDeReflectioned.not()) {
                        val operatingRecordList = getOperatingRecordList(effectName, it)

                        /**
                         * 1、如果执行过AI去反光再执行非AI修复特效，再执行AI去阴影，这时候需要再使用recordList判断是否做过AI去反光
                         * 2、其他入口，经过画质增强后再调节其他特效，此时特效经过combine后需要通过recordList再判断是否画质增强过
                         */
                        val isAiRepairPerformed = isAiRepairPerformedFromRecordList(isDeblured, isDeReflectioned, operatingRecordList)
                        isDeblured = isAiRepairPerformed.first
                        isDeReflectioned = isAiRepairPerformed.second
                    }
                }
            }
        }
        return Pair(isDeblured, isDeReflectioned)
    }

    /**
     * 从记录列表中获取是否做过AI修复的状态
     *
     * @param isDeblured 是否已经去模糊
     * @param isDeReflectioned 是否已经去反光
     * @param operatingRecordList 操作记录列表
     * @return 是否已经去反光和去模糊
     */
    @JvmStatic
    private fun isAiRepairPerformedFromRecordList(
        isDeblured: Boolean,
        isDeReflectioned: Boolean,
        operatingRecordList: List<OperatingRecord>?
    ): Pair<Boolean, Boolean> {
        var isDebluredReturn = isDeblured
        var isDeReflectionedReturn = isDeReflectioned
        operatingRecordList?.forEach { splitRecord ->
            if (isDebluredReturn && isDeReflectionedReturn) {
                return@forEach
            }
            if (splitRecord is AIRepairRecord) {
                GLog.d(TAG, LogFlag.DF) { "[isAiRepairPerformedFromRecordList] isDeblured:${splitRecord.isDeblured}," +
                            "isDeReflectioned:${splitRecord.isDeReflectioned}" }
                isDebluredReturn = splitRecord.isDeblured || isDebluredReturn
                isDeReflectionedReturn = splitRecord.isDeReflectioned || isDeReflectionedReturn
            }
        }
        return Pair(isDebluredReturn, isDeReflectionedReturn)
    }

    /**
     * 判断图片是否做过AI表情优化
     * @param vmBus  ViewModelBus
     * @return true: 表情优化过，false: 未表情优化过
     */
    @JvmStatic
    fun isPerformBestTake(vmBus: IViewModelBus?): Boolean {
        var isEnhanced = false
        getMultiEffectNameList(AvEffect.AiBestTakeEffect.name)?.apply {
            for (effectName in this) {
                if (isEnhanced) {
                    break
                }
                GLog.d(TAG, LogFlag.DF) { "[isBestTakeEnhanced] $effectName" }
                getRecordListFromEffectName(effectName, vmBus)?.forEach {
                    if (isEnhanced) {
                        return@forEach
                    }
                    isEnhanced = if (it is AIBestTakeRecord) {
                        GLog.d(TAG, LogFlag.DF) { "[isBestTakeEnhanced] isEnhanced:${it.isAiBestTakeEnhanced}" }
                        it.isAiBestTakeEnhanced
                    } else {
                        val optRecordList = getOperatingRecordList(effectName, it)
                        isBestTakeEnhancedFromRecordList(optRecordList)
                    }
                }
            }
        }
        return isEnhanced
    }

    /**
     * 从记录列表中获取是否做过AI表情优化的状态
     * @param operatingRecordList 操作记录列表
     * @return 是否已经表情优化过
     */
    @JvmStatic
    private fun isBestTakeEnhancedFromRecordList(operatingRecordList: List<OperatingRecord>?): Boolean {
        var isBestTakeEnhancedReturn = false
        operatingRecordList?.forEach { splitRecord ->
            if (isBestTakeEnhancedReturn) {
                return@forEach
            }
            if (splitRecord is AIBestTakeRecord) {
                GLog.d(TAG, LogFlag.DF) { "[isBestTakeEnhancedFromRecordList] isEnhanced:${splitRecord.isAiBestTakeEnhanced}" }
                isBestTakeEnhancedReturn = splitRecord.isAiBestTakeEnhanced
            }
        }
        return isBestTakeEnhancedReturn
    }

    /**
     * 从记录列表中获取是否做过画质增强的状态
     *
     * @param operatingRecordList 操作记录列表
     * @return 是否已经画质增强过
     */
    @JvmStatic
    private fun isImageQualityEnhancedFromRecordList(operatingRecordList: List<OperatingRecord>?): Boolean {
        var isEnhancedReturn = false
        operatingRecordList?.forEach { splitRecord ->
            if (isEnhancedReturn) {
                return@forEach
            }
            if (splitRecord is ImageQualityEnhanceRecord) {
                GLog.d(TAG, LogFlag.DF) { "[isImageQualityEnhancedFromRecordList] isEnhanced:${splitRecord.isEnhanced}" }
                isEnhancedReturn = splitRecord.isEnhanced
            }
        }
        return isEnhancedReturn
    }

    /**
     * 判断图片是否做过AI补光
     * @param vmBus  ViewModelBus
     * @return true: 进行过AI补光，false: 未进行过AI补光
     */
    @JvmStatic
    fun isPerformAiLighting(vmBus: IViewModelBus?): Boolean {
        return vmBus?.notifyOnce<List<OperatingRecord>>(
            TopicID.Operating.REPLY_TOPIC_OPERATING_FIND_SPECIFIC_RECORDS,
            FindRecordParams(stackIndex = STACK_INDEX_ALL, findType = RecordFindType.FIND_ALL)
        )?.any { it is AiLightingBlendRecord } ?: false
    }

    /**
     * 根据特效名称获取操作记录列表，若没有符合的特效名称则返回空
     *
     * @param effectName 特效名称
     * @param operatingRecord 操作记录
     * @return 操作记录列表
     */
    @JvmStatic
    private fun getOperatingRecordList(effectName: String, operatingRecord: OperatingRecord): List<OperatingRecord>? {
        return when (effectName) {
            AvEffect.TransformEffect.name -> (operatingRecord as? TransformRecord)?.split()
            AvEffect.ImageQualityEnhanceEffect.name -> (operatingRecord as? ImageQualityEnhanceRecord)?.operatingRecordList
            AvEffect.AiRepairEffect.name -> (operatingRecord as? AIRepairRecord)?.operatingRecordList
            AvEffect.AiEliminateEffect.name -> (operatingRecord as? EliminateVM.EliminateRecord)?.operatingRecordList
            AvEffect.AiBestTakeEffect.name -> (operatingRecord as? AIBestTakeRecord)?.operatingRecordList
            AvEffect.AiRepairRealmeEffect.name -> (operatingRecord as? AIRepairRealmeRecord)?.operatingRecordList
            else -> null
        }
    }

    /**
     * 获取多特效列表：
     * 这里与是否做过相关特效相关，由于做了多特效后，再保存时会将操作记录combine合并为一个，这里需要根据当前多特效的场景循环遍历所有的特效记录，
     * 直到查询到做过相关特效
     * 场景：AI图像助手里面的多特效、裁剪选装中关于AI超清相关的多特效，若最后一个特效不是目标特效，就需在合并的record中查找目标特效
     * 注意：
     * 1、AI图像助手里面的多特效需要注意下，若新增一项特效，这里也需要对应新增；
     * 2、若大图新增其他AI入口，也需要注意获取是否做过对应特效时的场景，是否包含这种多特效场景；
     *
     * @param containsEffectName 需要包含的特效名称
     * @return 多特效列表
     */
    @JvmStatic
    private fun getMultiEffectNameList(containsEffectName: String): List<String>? {
        return when (containsEffectName) {
            AvEffect.ImageQualityEnhanceEffect.name -> {
                /**
                 * 与是否做过画质增强相关的多特效名称列表
                 * 当前与画质增强相关的特效有：画质增强；裁剪旋转；AI修复：去反光、去模糊；AI消除；最佳表情
                 */
                listOf(
                    AvEffect.ImageQualityEnhanceEffect.name,
                    AvEffect.TransformEffect.name,
                    AvEffect.AiRepairEffect.name,
                    AvEffect.AiEliminateEffect.name,
                    AvEffect.AiBestTakeEffect.name
                )
            }

            AvEffect.AiRepairEffect.name -> {
                /**
                 * 与是否做过AI修复相关的多特效名称列表
                 * 当前AI修复多特效相关的特效有：AI修复：去反光、去模糊；画质增强；AI消除；最佳表情
                 */
                listOf(
                    AvEffect.AiRepairEffect.name,
                    AvEffect.ImageQualityEnhanceEffect.name,
                    AvEffect.AiEliminateEffect.name,
                    AvEffect.AiBestTakeEffect.name
                )
            }

            AvEffect.AiBestTakeEffect.name -> {
                /**
                 * 与是否做过最佳表情相关的多特效名称列表
                 * 当前与最佳表情相关的特效有：最佳表情；裁剪旋转；AI修复：去反光、去模糊；AI消除；画质增强
                 */
                listOf(
                    AvEffect.AiBestTakeEffect.name,
                    AvEffect.TransformEffect.name,
                    AvEffect.AiRepairEffect.name,
                    AvEffect.AiEliminateEffect.name,
                    AvEffect.ImageQualityEnhanceEffect.name
                )
            }

            else -> null
        }
    }

    /**
     * 根据特效名称查找对应的操作栈记录，使用map过滤出对应的特效记录，没有符合条件的特效名称则返回空
     *
     * @param effectName 特效名称
     * @param vmBus ViewModelBus
     * @return 操作记录列表
     */
    @JvmStatic
    private fun getRecordListFromEffectName(effectName: String, vmBus: IViewModelBus?): List<OperatingRecord>? {
        val operatingRecordList =
            vmBus?.notifyOnce<List<OperatingRecord>>(TopicID.Operating.REPLY_TOPIC_OPERATING_FIND_ALL_RECORD, effectName)
        return when (effectName) {
            AvEffect.ImageQualityEnhanceEffect.name -> operatingRecordList?.mapNotNull { it as? ImageQualityEnhanceRecord }
            AvEffect.TransformEffect.name -> operatingRecordList?.mapNotNull { it as? TransformRecord }
            AvEffect.AiRepairEffect.name -> operatingRecordList?.mapNotNull { it as? AIRepairRecord }
            AvEffect.AiEliminateEffect.name -> operatingRecordList?.mapNotNull { it as? EliminateVM.EliminateRecord }
            AvEffect.AiBestTakeEffect.name -> operatingRecordList?.mapNotNull { it as? AIBestTakeRecord }
            else -> null
        }
    }
}