/***********************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd.
 ** All rights reserved.
 **
 ** File: - AIDeGlareTemplate.kt
 ** Description: xxx
 ** Version: 1.0
 ** Date : 2025/3/26
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>      <data>      <version >     <desc>
 ** ------------------------------------------------------------
 ** gongziyao      2025/3/26       1.0          created
 ***************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.airepair.deglare

import android.content.Context
import com.oplus.aiunit.core.data.DetectName
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.FIRST_USING_AI_DEGLARE_INTRODUCTION
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.airepair.AIFuncPermissionObserver
import com.oplus.gallery.photoeditor.editingvvm.airepair.AIFuncPermissionResId
import com.oplus.gallery.photoeditor.editingvvm.airepair.AIFuncTemplate
import com.oplus.gallery.photoeditor.editingvvm.airepair.AIRepairIntroductionViewData
import com.oplus.gallery.photoeditor.editingvvm.airepair.AIRepairRealmeRecord.Companion.CMD_DEGLARE
import com.oplus.gallery.photoeditor.editingvvm.airepair.AIRepairType
import com.oplus.gallery.photoeditor.editingvvm.airepair.IAIFuncPermissionObserver

/**
 * AI 去眩光功能模板
 */
internal class AIDeGlareTemplate : AIFuncTemplate {
    override val introductionConfigId: String = FIRST_USING_AI_DEGLARE_INTRODUCTION

    override val introductionViewDataList = listOf(
        AIRepairIntroductionViewData(
            R.drawable.picture3d_editor_ic_introduction_de_glare,
            R.string.picture3d_editor_text_ai_deglare_introduction_fix_blur_title,
            R.string.base_aideglare_dialog_title_functional_experience_description,
            R.raw.picture_editor_ai_edit_picture_guide_deglare_anim,
            needPlayAnim = true
        )
    )

    override val command: String = CMD_DEGLARE

    override val networkErrorMessage: Int = R.string.picture3d_editor_text_deglare_fail_because_network

    override val unknownErrorMessage: Int = R.string.picture3d_editor_text_deglare_unknown_error

    override val contentSensitiveMessage: Int = R.string.picture3d_editor_text_deglare_refuse_error

    override val contentSensitiveMessageOversea: Int = R.string.picture3d_editor_text_deglare_refuse_error_oversea

    override val type: AIRepairType = AIRepairType.DEGLARE

    override fun createPermissionObserver(context: Context): IAIFuncPermissionObserver {
        return AIFuncPermissionObserver(
            context,
            AIFuncPermissionResId(
                com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_ai_deglare_statement,
                com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_request_network_ai_deglare
            ),
            DetectName.VISION_IMAGE_DEBLUR
        )
    }
}