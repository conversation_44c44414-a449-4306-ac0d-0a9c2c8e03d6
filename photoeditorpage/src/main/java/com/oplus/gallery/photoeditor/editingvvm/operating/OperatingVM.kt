/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OperatingVM
 ** Description:
 ** Version: 1.0
 ** Date : 2024/1/26
 ** Author: wudanyang
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  wudanyang                      2024/1/26    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.operating

import android.content.res.Resources
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.view.View
import com.oplus.gallery.addon.graphics.UltraHdrInfo
import com.oplus.gallery.addon.graphics.toUltraHdrInfo
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.isCShotPhoto
import com.oplus.gallery.business_lib.model.data.base.item.isOlivePhoto
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.CameraMode.FLAG_PORTRAIT_BLUR
import com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils.EDITOR_SAVE_STATUS_ASK
import com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils.EDITOR_SAVE_STATUS_DELETE
import com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils.EDITOR_SAVE_STATUS_NEW
import com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils.EDITOR_SAVE_STATUS_OVERWRITE
import com.oplus.gallery.foundation.archv2.bus.PublisherChannel
import com.oplus.gallery.foundation.archv2.bus.ReplierChannel
import com.oplus.gallery.foundation.archv2.compositepage.INVALID
import com.oplus.gallery.foundation.codec.extend.EXTEND_KEY_LOCAL_HDR_META_DATA
import com.oplus.gallery.foundation.codec.extend.EXTEND_KEY_ORIGIN_HDR_LINEAR_MASK
import com.oplus.gallery.foundation.codec.extend.EXTEND_KEY_FRONT_DEPTH_CONFIG
import com.oplus.gallery.foundation.codec.extend.EXTEND_KEY_ULTRA_HDR_INFO
import com.oplus.gallery.foundation.codec.extend.LocalHdrInfoStruct
import com.oplus.gallery.foundation.codec.extend.portraitblur.FrontDepthStruct
import com.oplus.gallery.foundation.exif.oplus.OplusExifTag
import com.oplus.gallery.foundation.opengl2.texture.ITexture
import com.oplus.gallery.foundation.ui.notification.NotificationAction
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.getOrLog
import com.oplus.gallery.foundation.util.ext.getOrRun
import com.oplus.gallery.foundation.util.ext.isNullOrEmpty
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.foundation.util.math.MathUtils
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.foundation.util.version.ApiLevelUtil
import com.oplus.gallery.framework.abilities.config.ISettingsAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IMAGE_EDITOR_SAVE_STATUS
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SELL_MODE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_OLIVE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_SAVE_OLIVE
import com.oplus.gallery.framework.abilities.editing.asset.MetadataType
import com.oplus.gallery.framework.abilities.editing.effect.AvEffect
import com.oplus.gallery.framework.abilities.editing.track.usage.IAvEffectUsage
import com.oplus.gallery.framework.abilities.watermark.file.PhoneInfo
import com.oplus.gallery.framework.abilities.watermark.file.WatermarkInfo
import com.oplus.gallery.framework.abilities.watermark.file.WatermarkPattern
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.EditingSubVM
import com.oplus.gallery.photoeditor.editingvvm.EditingVM
import com.oplus.gallery.photoeditor.editingvvm.EffectPattern
import com.oplus.gallery.photoeditor.editingvvm.EmptyReplier
import com.oplus.gallery.photoeditor.editingvvm.EmptyUnitReplier
import com.oplus.gallery.photoeditor.editingvvm.IntObserver
import com.oplus.gallery.photoeditor.editingvvm.SingleArgReplierChannel
import com.oplus.gallery.photoeditor.editingvvm.StringReplier
import com.oplus.gallery.photoeditor.editingvvm.TObserver
import com.oplus.gallery.photoeditor.editingvvm.TopicID
import com.oplus.gallery.photoeditor.editingvvm.TopicID.EditingWatermark.REPLY_TOPIC_RESTORE_WATERMARK_IF_NEED
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_EXIT
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_INPUTS_DATA_SOURCE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_INPUTS_MEDIA_ITEM
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_INPUTS_PARAMETRIC_DATA_SOURCE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_MENU_AUTO_SELECT
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_PHONE_INFO
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Management.TOPIC_MANAGEMENT_STRATEGY_ID
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Notification.TOPIC_NOTIFICATION_ACTION
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OLiveVideoEdit.TOPIC_OLIVE_IS_EDITING
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.REPLAY_TOPIC_OPERATING_ON_SAVE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.REPLY_TOPIC_COLLECT_CONTENT_EDIT
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.REPLY_TOPIC_OPERATING_ACTION
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.REPLY_TOPIC_OPERATING_CANCEL_EFFECT
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.REPLY_TOPIC_OPERATING_CANCEL_EFFECT_AND_POP_RECORD
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.REPLY_TOPIC_OPERATING_ENTER_SUB
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.REPLY_TOPIC_OPERATING_FIND_ALL_OPERATING_RECORDS
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.REPLY_TOPIC_OPERATING_FIND_ALL_RECORD
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.REPLY_TOPIC_OPERATING_FIND_IMPORT_RECORDS
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.REPLY_TOPIC_OPERATING_FIND_RECORD
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.REPLY_TOPIC_OPERATING_FIND_RECORDS_AFTER_NONPARAMETRIC
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.REPLY_TOPIC_OPERATING_FIND_RECORD_UNTIL_NON_PARAMETRIC
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.REPLY_TOPIC_OPERATING_FIND_SPECIFIC_RECORDS
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.REPLY_TOPIC_OPERATING_FIND_TOP_RECORD
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.REPLY_TOPIC_OPERATING_RECORD_STEP
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.REPLY_TOPIC_OPERATING_UNDO_STACK_AND_EFFECT
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.REPLY_TOPIC_OPERATING_UPDATE_RECOVER_BTN
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_IMAGE_PACK
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_OPERATING_ANIMATOR_UPDATE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_OPERATING_BACK_TYPE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_OPERATING_COMPARE_TARGET
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_OPERATING_EFFECT_USAGE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_OPERATING_MODIFY_STATE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_OPERATING_OPERATING_MEMO
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_OPERATING_RECORD
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_OPERATING_RECOVER_WATERMARK_EFFECT
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_OPERATING_UPDATE_WATERMARK_EFFECT
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_OPERATING_VIEW_STATE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_WATERMARK_RECORD_RECOVER_EFFECT
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OperatingAction.TOPIC_OPERATING_ACTION_BACK_KEY
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OperatingAction.TOPIC_OPERATING_ACTION_CANCEL
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OperatingAction.TOPIC_OPERATING_ACTION_CONFIRM
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OperatingAction.TOPIC_OPERATING_ACTION_REDO
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OperatingAction.TOPIC_OPERATING_ACTION_RESTORE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OperatingAction.TOPIC_OPERATING_ACTION_UNDO
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OperatingAction.TOPIC_OPERATING_SAVE_STATUS
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OperatingAction.TOPIC_OPERATING_ACTION_SAVE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Output.REPLY_TOPIC_SAVE_FILE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Output.TOPIC_OUTPUT_SAVE_RESULT
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Pipeline.TOPIC_PIPELINE_EFFECT_ONLY_UNDO
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Pipeline.TOPIC_PIPELINE_SESSION_CREATED
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_PREVIEW_CURRENT_TEXTURE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_PREVIEW_IS_SCROLLABLE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_PREVIEW_IS_TOUCHABLE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_PREVIEW_LONG_PRESS_STATE
import com.oplus.gallery.photoeditor.editingvvm.UnitReplier
import com.oplus.gallery.photoeditor.editingvvm.UnitReplierChannel
import com.oplus.gallery.photoeditor.editingvvm.get
import com.oplus.gallery.photoeditor.editingvvm.inputarguments.CancelOperation
import com.oplus.gallery.photoeditor.editingvvm.inputarguments.DataSource
import com.oplus.gallery.photoeditor.editingvvm.inputarguments.FinishOperation
import com.oplus.gallery.photoeditor.editingvvm.inputarguments.InputArgumentsVM
import com.oplus.gallery.photoeditor.editingvvm.inputarguments.ParametricDataSource
import com.oplus.gallery.photoeditor.editingvvm.notification.DiscardChangeConfirmDialogAction
import com.oplus.gallery.photoeditor.editingvvm.notification.RecoverConfirmDialogAction
import com.oplus.gallery.photoeditor.editingvvm.notification.SaveDialogAction
import com.oplus.gallery.photoeditor.editingvvm.notifyOnce
import com.oplus.gallery.photoeditor.editingvvm.olive.OliveRecord
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecordWithInvoker.Companion.INVOKE_TYPE_CANCEL_EXIT_SUB_PAGE
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecordWithInvoker.Companion.INVOKE_TYPE_COMPLETE_EXIT_SUB_PAGE
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecordWithInvoker.Companion.INVOKE_TYPE_NOTIFY_RECORD_STEP
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecordWithInvoker.Companion.INVOKE_TYPE_PERFORM_REDO
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecordWithInvoker.Companion.INVOKE_TYPE_PERFORM_UNDO
import com.oplus.gallery.photoeditor.editingvvm.operating.stack.CommandFormat
import com.oplus.gallery.photoeditor.editingvvm.operating.stack.OperatingStack
import com.oplus.gallery.photoeditor.editingvvm.output.ContentEditFileProcessType
import com.oplus.gallery.photoeditor.editingvvm.output.ResultCode.Companion.EXPORT_SUCCESS
import com.oplus.gallery.photoeditor.editingvvm.output.ResultCode.Companion.SAVE_FAILED_DIMENSION_TOO_LARGE
import com.oplus.gallery.photoeditor.editingvvm.output.ResultCode.Companion.SAVE_FAILED_ERROR
import com.oplus.gallery.photoeditor.editingvvm.output.ResultCode.Companion.SAVE_FAILED_NO_SPACE
import com.oplus.gallery.photoeditor.editingvvm.output.ResultCode.Companion.SAVE_SUCCESS
import com.oplus.gallery.photoeditor.editingvvm.output.SaveResult
import com.oplus.gallery.photoeditor.editingvvm.pagecontainer.isSpecificStrategy
import com.oplus.gallery.photoeditor.editingvvm.portraitblur.PortraitBlurRecord
import com.oplus.gallery.photoeditor.editingvvm.portraitblur.track.PortraitBlurTrackHelper
import com.oplus.gallery.photoeditor.editingvvm.preview.LongPressEvent
import com.oplus.gallery.photoeditor.editingvvm.subscribeOnce
import com.oplus.gallery.photoeditor.editingvvm.subscribeR
import com.oplus.gallery.photoeditor.editingvvm.subscribeT
import com.oplus.gallery.photoeditor.editingvvm.subscribeUntilConsumed
import com.oplus.gallery.photoeditor.editingvvm.transform.track.TransformTrackHelper
import com.oplus.gallery.photoeditor.editingvvm.unregisterDuplexT
import com.oplus.gallery.photoeditor.editingvvm.unregisterT
import com.oplus.gallery.photoeditor.editingvvm.watermark.WatermarkRecord
import com.oplus.gallery.photoeditor.track.CommonEditorTrackConstant
import com.oplus.gallery.photoeditor.track.CommonEditorTracker
import com.oplus.gallery.photoeditor.track.FilterTracker
import com.oplus.gallery.photoeditor.util.isVisible
import com.oplus.gallery.photoeditor.util.setBackgroundEnable
import com.oplus.gallery.photoeditor.util.setEnable
import com.oplus.gallery.photoeditor.util.setVisible
import com.oplus.gallery.photoeditor.widget.EditorBottomActionBar.ACTION_TYPE_IMAGE_BUTTON
import com.oplus.gallery.photoeditor.widget.EditorBottomActionBar.ACTION_TYPE_TEXT_BUTTON
import com.oplus.gallery.standard_lib.app.TrackScope
import com.oplus.gallery.standard_lib.codec.drawable.HdrImageDrawable
import com.oplus.gallery.standard_lib.codec.drawable.IHdrImageContent
import com.oplus.gallery.standard_lib.codec.drawable.LHdrMetadataPack
import com.oplus.gallery.standard_lib.codec.drawable.UHdrMetadataPack
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlin.math.max
import com.oplus.gallery.basebiz.R as BaseR
import kotlin.collections.reversed as ktReversed

/**
 * 通用操作的管理（对比、取消、保存、撤销、恢复、勾、叉）
 * @param editingVM 主ViewModel
 */
@Suppress("LargeClass")
internal class OperatingVM(editingVM: EditingVM) : EditingSubVM(editingVM) {

    /**
     * 操作栈，有操作记录的时候从操作记录恢复，无操作记录的时候新建一个空的，私有，统一由OperatingVM暴露操作接口
     */
    private var operatingStack: OperatingStack = OperatingStack()

    /**
     * 对比按钮按压状态
     */
    private var compareTargetPub: PublisherChannel<CompareTarget>? = null

    /**
     * 操作栏ViewState
     */
    private var operatingViewStatePub: PublisherChannel<OperatingViewState>? = null

    /**
     * 返回类型
     */
    private var backTypePub: PublisherChannel<BackType>? = null

    /**
     * 进入一级编辑页恢复水印效果record
     */
    private var watermarkRecordRecoverEffectPub: PublisherChannel<WatermarkRecord>? = null

    private var effectUsagePub: PublisherChannel<IAvEffectUsage>? = null

    /**
     * 当前操作栈是否有修改的状态
     */
    private var modifyStatePub: PublisherChannel<Boolean>? = null

    private var recordPub: PublisherChannel<OperatingRecordWithInvoker>? = null

    private var editingImagePackPub: PublisherChannel<EditingImagePack>? = null

    private var saveStatusPub: PublisherChannel<SaveStatus>? = null

    /**
     * operating操作栏动画参数更新发布通道
     */
    private var operatingAnimatorUpdatePub: PublisherChannel<OperatingAnimatorUpdater>? = null

    private var operatingMemo: OperatingMemo = OperatingMemo()
    private var operatingMemoPub: PublisherChannel<OperatingMemo>? = null

    /**
     * 参数化数据通知
     */
    private var parametricDataSourcePub: PublisherChannel<ParametricDataSource>? = null

    /**
     * 是否处于退出状态
     * */
    private var isExiting = false
    private var isHandlingConfirmAction = false

    /**
     * 编辑退出类型
     */
    private var exitType = -1

    /**
     * 编辑启动时间
     */
    private var startTime = 0L

    /**
     * 编辑二级页类型
     */
    private var operateType = CommonEditorTrackConstant.Value.TYPE_DEFAULT

    /**
     * 记录二级页的项是否完成或保留编辑效果 0取消 1完成
     */
    private var secondEditCompletionMap = mutableMapOf<Int, Int>()

    private var recoverPreviewManager: RecoverPreviewManager = RecoverPreviewManager(
        operatingStack, editingVM, app,
        object : RecoverPreviewManager.ITaskFinishCallback {
            override fun onParametricReady(parametricDataSource: ParametricDataSource) {
                parametricDataSourcePub?.publish(parametricDataSource)
                // 当前未产生新的编辑内容，且存在原图时，显示复原按钮
                if (operatingStack.isInitialState() && parametricDataSource.isParametric) {
                    updateRecoverBtn(View.VISIBLE)
                }
            }

            override fun onFinish(result: Boolean, checkResult: CheckResult?) {
                setAndPublishOperatingMemo(result, checkResult)
                if (result && (checkResult != null)) {
                    val watermarkInfo = vmBus?.get<WatermarkInfo>(TOPIC_OPERATING_UPDATE_WATERMARK_EFFECT) ?: vmBus?.get<WatermarkInfo>(
                        TOPIC_OPERATING_RECOVER_WATERMARK_EFFECT
                    )
                    val phoneInfo = vmBus?.get<PhoneInfo>(TOPIC_PHONE_INFO) ?: PhoneInfo()
                    val watermarkRecord = WatermarkRecord.createAddWatermarkRecord(
                        watermarkInfo,
                        phoneInfo
                    )
                    watermarkRecordRecoverEffectPub?.publish(watermarkRecord)
                }
            }
        })

    private val editingImagePackRep = object : UnitReplier<EditingImagePack>() {
        override fun onSingleReply(arg: EditingImagePack) {
            editingImagePackPub?.publish(arg)
        }
    }

    /**
     * Operating操作栏动效参数更新监听者
     */
    private val operatingAnimatorUpdateRep = object : UnitReplier<OperatingAnimatorUpdater>() {
        override fun onSingleReply(arg: OperatingAnimatorUpdater) {
            operatingAnimatorUpdatePub?.publish(arg)
        }
    }

    private val compareTargetRep = object : UnitReplier<CompareTarget>() {
        override fun onSingleReply(arg: CompareTarget) {
            notifyChangeCompareTarget(arg)
        }
    }

    private val viewStateRep = object : UnitReplier<OperatingViewState>() {
        override fun onSingleReply(arg: OperatingViewState) {
            operatingViewStatePub?.publish(arg)
        }
    }

    private val actionRep = object : UnitReplier<OperatingAction>() {
        override fun onSingleReply(arg: OperatingAction) {
            notifyAction(arg)
        }
    }

    private val enterSubRep = object : EmptyUnitReplier() {
        override fun onEmptyReply() {
            notifyEnterSubPage()
        }
    }

    private val updateRecoverBtnRep = object : UnitReplier<Int>() {
        override fun onSingleReply(arg: Int) {
            updateRecoverBtn(arg)
        }
    }

    private val recordStepRep = object : SingleArgReplierChannel<OperatingRecordAction, IAvEffectUsage?>() {
        override fun onSingleReply(arg: OperatingRecordAction): IAvEffectUsage? {
            return notifyRecordStep(arg)
        }
    }

    private val cancelEffectRep = object : UnitReplierChannel {
        override fun onReply(vararg args: Any) {
            if (args.size < 2) return
            val operatingRecord = args[0] as? OperatingRecord ?: return
            val render = args[1] as? Boolean ?: return
            notifyCancelEffect(operatingRecord, render)
        }
    }

    private val cancelEffectAndPopRecordRep = object : UnitReplierChannel {
        override fun onReply(vararg args: Any) {
            if (args.size < 2) return
            val operatingRecord = args[0] as? OperatingRecord ?: return
            val render = args[1] as? Boolean ?: return
            notifyCancelEffectAndPopRecord(operatingRecord, render)
        }
    }

    private val undoStackAndEffectRep = object : ReplierChannel<OperatingRecord?> {

        override fun onReply(vararg args: Any): OperatingRecord? {
            if (args.size < 2) return null
            val nextAction = args[0] as? (() -> Unit)
            val needRender = args[1] as? Boolean ?: true
            return undoStackAndEffect(nextAction, needRender)
        }
    }

    private val findRecordRep = object : StringReplier<OperatingRecord?>() {
        override fun onSingleReply(arg: String): OperatingRecord? {
            return operatingStack.findLast(isFindUntilNonParametric = false) { arg == it.effectName }
        }
    }

    private val findTopRecordRep = object : EmptyReplier<OperatingRecord?>() {
        override fun onEmptyReply(): OperatingRecord? {
            return operatingStack.peek()
        }
    }

    private val findSpecificRecordsRep = object : SingleArgReplierChannel<FindRecordParams, List<OperatingRecord>>() {
        override fun onSingleReply(arg: FindRecordParams): List<OperatingRecord> {
            return findSpecificRecords(arg)
        }
    }

    private val findRecordUntilNonParametricRep = object : StringReplier<OperatingRecord?>() {
        override fun onSingleReply(arg: String): OperatingRecord? {
            return operatingStack.findLast(isFindUntilNonParametric = true) { arg == it.effectName }
        }
    }

    private val findAllRecord = object : StringReplier<List<OperatingRecord>>() {
        override fun onSingleReply(arg: String): List<OperatingRecord> {
            return operatingStack.findAllRecord {
                it.effectName == arg
            }
        }
    }

    private val processParameterRecordsRep = object : UnitReplier<Boolean>() {
        override fun onSingleReply(arg: Boolean) {
            processParameterRecords(arg)
        }
    }

    private val findAllOperatingRecords = object : StringReplier<List<OperatingRecord>>() {
        override fun onSingleReply(arg: String): List<OperatingRecord> {
            return operatingStack.findAllRecord {
                arg == ALL_OPERATING_RECORDS
            }
        }
    }

    private val findRecordsAfterNonparametric = object : StringReplier<List<OperatingRecord>>() {
        override fun onSingleReply(arg: String): List<OperatingRecord> {
            return operatingStack.findRecordsAfterNonparametric(arg) ?: emptyList()
        }
    }

    private val findImportRecord = object : StringReplier<List<OperatingRecord>>() {
        override fun onSingleReply(arg: String): List<OperatingRecord> {
            return operatingStack.findImportRecord()
        }
    }

    private fun onBackPressed() {
        val operatingActionNtf = vmBus?.subscribeR<Unit>(TOPIC_OPERATING_ACTION_BACK_KEY)
        // 看看是否有注册的topic
        if (operatingActionNtf == null) {
            cancel()
        } else {
            operatingActionNtf.notify(object : BackKeyListener {
                override fun onBack() {
                    vmBus?.unsubscribe(TOPIC_OPERATING_ACTION_BACK_KEY, operatingActionNtf)
                    cancel()
                }
            })
        }
    }

    private val strategyIdObserver: IntObserver = {
        onStrategyIdChanged(it)
    }

    private val previewLongPressStateObserver: TObserver<LongPressEvent> = {
        onReceiveLongPress(it)
    }

    private val currentTextureObserver: TObserver<ITexture> = {
        onTextureChanged(it)
    }

    private val inputSourceObserver: TObserver<DataSource> = {
        launch(Dispatchers.IO) {
            recoverPreviewManager.parseExtendData(it)
        }
    }

    private val saveResultObserver: TObserver<SaveResult> = {
        GLog.d(TAG, LogFlag.DL, "[saveResultObserver] resultDesc:${it.resultDesc}")
        saveStatusPub?.publish(SaveStatus.SAVE_COMPLETE)
        when (it.resultCode) {
            SAVE_SUCCESS -> exitCurrentPage(true)

            EXPORT_SUCCESS -> {
                backTypePub?.publish(BackType.LAST_PAGE)
                backTypePub?.publish(BackType.FINISH_PAGE)
            }

            SAVE_FAILED_NO_SPACE -> notifyPostFailAction(BaseR.string.common_collage_low_memory_to_save)

            SAVE_FAILED_DIMENSION_TOO_LARGE -> notifyPostFailAction(BaseR.string.common_collage_too_large_to_save)

            SAVE_FAILED_ERROR -> notifyPostFailAction(BaseR.string.common_saving_failure)
        }
    }

    /**
     * 对图片拓展数据中水印数据解析完成时的通知
     */
    private val recoverWatermarkEffectObserver: TObserver<WatermarkInfo?> = { watermarkInfo ->
        recoverPreviewManager.prepareWatermarkInfo(watermarkInfo)
    }

    private val updateWatermarkEffectObserver: TObserver<WatermarkInfo?> = { watermarkInfo ->
        recoverPreviewManager.updateWatermarkInfo(watermarkInfo)
        modifyWatermarkEffectsIfNeed(watermarkInfo)
    }

    /**
     *  操作失败后移除effect请求记录
     */
    private val undoEffectRep = object : UnitReplierChannel {
        override fun onReply(vararg args: Any) {
            val record = args[0] as? OperatingRecord ?: return
            val render = args[1] as? Boolean ?: false
            val popIfOnTop = args[2] as? Boolean ?: false
            GLog.d(TAG, LogFlag.DL) { "[undoEffectObserver] record:$record, render:$render" }
            if (popIfOnTop && (operatingStack.peek() == record)) {
                operatingStack.pop()
            }
            undoEffect(record, render)
        }
    }

    /**
     * 监听session创建完成的时机，session创建完成再恢复操作栈
     */
    private val sessionCreateObserver: TObserver<Boolean> = { _ ->
        launch {
            recoverPreviewManager.notifySessionCreated()
        }
    }

    init {
        vmBus?.apply {
            compareTargetPub = registerDuplex(TOPIC_OPERATING_COMPARE_TARGET, compareTargetRep)
            operatingViewStatePub = registerDuplex(TOPIC_OPERATING_VIEW_STATE, viewStateRep)
            backTypePub = register(TOPIC_OPERATING_BACK_TYPE)
            watermarkRecordRecoverEffectPub = register(TOPIC_WATERMARK_RECORD_RECOVER_EFFECT)
            effectUsagePub = register(TOPIC_OPERATING_EFFECT_USAGE)
            modifyStatePub = register(TOPIC_OPERATING_MODIFY_STATE)
            recordPub = register(TOPIC_OPERATING_RECORD)
            operatingMemoPub = register(TOPIC_OPERATING_OPERATING_MEMO)
            saveStatusPub = register(TOPIC_OPERATING_SAVE_STATUS)
            editingImagePackPub = registerDuplex(TOPIC_IMAGE_PACK, editingImagePackRep)
            operatingAnimatorUpdatePub = registerDuplex(TOPIC_OPERATING_ANIMATOR_UPDATE, operatingAnimatorUpdateRep)
            register(REPLY_TOPIC_OPERATING_ACTION, actionRep)
            register(REPLY_TOPIC_OPERATING_ENTER_SUB, enterSubRep)
            register(REPLY_TOPIC_OPERATING_RECORD_STEP, recordStepRep)
            register(REPLY_TOPIC_OPERATING_CANCEL_EFFECT, cancelEffectRep)
            register(REPLY_TOPIC_OPERATING_CANCEL_EFFECT_AND_POP_RECORD, cancelEffectAndPopRecordRep)
            register(REPLY_TOPIC_OPERATING_UNDO_STACK_AND_EFFECT, undoStackAndEffectRep)
            register(REPLY_TOPIC_OPERATING_UPDATE_RECOVER_BTN, updateRecoverBtnRep)
            register(REPLY_TOPIC_OPERATING_FIND_RECORD, findRecordRep)
            register(REPLY_TOPIC_OPERATING_FIND_TOP_RECORD, findTopRecordRep)
            register(REPLY_TOPIC_OPERATING_FIND_SPECIFIC_RECORDS, findSpecificRecordsRep)
            register(REPLY_TOPIC_OPERATING_FIND_RECORD_UNTIL_NON_PARAMETRIC, findRecordUntilNonParametricRep)
            register(REPLY_TOPIC_OPERATING_FIND_ALL_RECORD, findAllRecord)
            register(REPLY_TOPIC_COLLECT_CONTENT_EDIT, processParameterRecordsRep)
            register(REPLY_TOPIC_OPERATING_FIND_ALL_OPERATING_RECORDS, findAllOperatingRecords)
            register(REPLY_TOPIC_OPERATING_FIND_RECORDS_AFTER_NONPARAMETRIC, findRecordsAfterNonparametric)
            register(REPLY_TOPIC_OPERATING_FIND_IMPORT_RECORDS, findImportRecord)
            register(TOPIC_PIPELINE_EFFECT_ONLY_UNDO, undoEffectRep)

            startTime = System.currentTimeMillis()
        }
    }

    override fun onCreate() {
        super.onCreate()
        vmBus?.apply {
            parametricDataSourcePub = register(TOPIC_INPUTS_PARAMETRIC_DATA_SOURCE)
            subscribeT(TOPIC_PREVIEW_CURRENT_TEXTURE, currentTextureObserver)
            subscribeT(TOPIC_INPUTS_DATA_SOURCE, inputSourceObserver)
            subscribeT(TOPIC_MANAGEMENT_STRATEGY_ID, strategyIdObserver)
            subscribeT(TOPIC_OUTPUT_SAVE_RESULT, saveResultObserver)
            subscribeOnce(TOPIC_PIPELINE_SESSION_CREATED, sessionCreateObserver)

            subscribeT(TOPIC_PREVIEW_LONG_PRESS_STATE, previewLongPressStateObserver)
            subscribeT(TOPIC_OPERATING_RECOVER_WATERMARK_EFFECT, recoverWatermarkEffectObserver)
            subscribeT(TOPIC_OPERATING_UPDATE_WATERMARK_EFFECT, updateWatermarkEffectObserver)
        }
    }

    private fun onReceiveLongPress(pressEvent: LongPressEvent) {
        val state = vmBus?.get<OperatingViewState>(TOPIC_OPERATING_VIEW_STATE) ?: OperatingViewState()
        val canCompare = state.compareBtnFlags.isVisible()
        GLog.d(TAG, LogFlag.DL) { "previewLongPressStateObserver $pressEvent canCompare: $canCompare" }
        if (canCompare.not()) return

        when (pressEvent) {
            is LongPressEvent.Finish -> notifyChangeCompareTarget(CompareTarget.RecoverEffect)

            is LongPressEvent.Begin -> notifyChangeCompareTarget(CompareTarget.PreEffect)
        }
    }

    private fun getCombineStrategy(): ICombineStrategy {
        return when (getCurrentStrategy()?.effectPattern) {
            EffectPattern.SINGLE_EFFECT -> {
                // 无上下文单特效的，直接replace即可
                PickLast()
            }

            EffectPattern.SINGLE_EFFECT_WITH_CONTEXT -> {
                // 有上下文单特效的，保留初始record，新record加到旧record上
                PlusAssignToFirst()
            }

            EffectPattern.MULTI_EFFECT, EffectPattern.MULTI_EFFECT_WITH_CONTEXT -> {
                // 多特效的，保留最终record，旧record加到新record上
                PlusAssignToLast()
            }

            else -> PlusAssignToFirst()
        }
    }

    //========================================开始点击按钮时的通知处理========================================
    /**
     * 处理操作栏的点击事件
     */
    private fun notifyAction(action: OperatingAction): Boolean {
        GLog.d(TAG, LogFlag.DL) {
            val actionName = kotlin.runCatching {
                app.resources.getResourceEntryName(action.id)
            }.getOrNull()
            "notifyAction: actionName=$actionName, invoker = ${action.invoker}"
        }
        when (action.id) {
            R.id.editor_id_text_action_cancel -> cancel()
            R.id.editor_id_text_action_done -> confirm()
            R.id.editor_id_text_action_save -> save()
            R.id.editor_id_text_action_recover -> recover()
            R.id.editor_id_action_redo -> redo()
            R.id.editor_id_action_undo -> undo()
            R.id.editor_id_action_restore -> restore()
            R.id.action_finish_strategy -> exitCurrentPage(false)
            R.id.operation_back_key -> onBackPressed()
            else -> GLog.w(TAG, "notifyAction: id is invalid.")
        }
        return true
    }

    private fun getSaveDialogActionCallback(): SaveDialogAction.ISaveConfirmCallback {
        return object : SaveDialogAction.ISaveConfirmCallback {
            override fun onNeutralButtonClicked(isChecked: Boolean) {
                GLog.d(TAG, "[getSaveDialogActionCallback] overwrite original clicked. isChecked=$isChecked")
                if (isChecked) {
                    setEditorSaveStatus(EDITOR_SAVE_STATUS_OVERWRITE)
                }
                notifyToSave(EDITOR_SAVE_STATUS_OVERWRITE)
                exitType = CommonEditorTrackConstant.Value.EXIT_TYPE_ORIGIN
            }

            override fun onPositiveButtonClicked(isChecked: Boolean) {
                GLog.d(TAG, "[getSaveDialogActionCallback] as new clicked. isChecked=$isChecked")
                if (isChecked) {
                    setEditorSaveStatus(EDITOR_SAVE_STATUS_NEW)
                }
                notifyToSave(EDITOR_SAVE_STATUS_NEW)
                exitType = CommonEditorTrackConstant.Value.EXIT_TYPE_NEW_PHOTO
            }
        }
    }

    private fun notifyToSave(editorSaveStatus: Int = EDITOR_SAVE_STATUS_ASK) {
        // 通知保存开始，通知出去之后，不要切换线程，协程， 管线和栈操作都在当前线程做完
        vmBus?.notifyOnce(REPLAY_TOPIC_OPERATING_ON_SAVE)
        if (shouldRestoreWatermark()) {
            vmBus?.notifyOnce(REPLY_TOPIC_RESTORE_WATERMARK_IF_NEED)
        }
        // 开始保存后禁用手势滑动
        vmBus?.apply {
            notifyOnce(TOPIC_PREVIEW_IS_TOUCHABLE, false)
            notifyOnce(TOPIC_PREVIEW_IS_SCROLLABLE, false)
        }
        saveStatusPub?.publish(SaveStatus.SAVE_BEGIN)
        val dataSource = vmBus?.get<DataSource>(TopicID.InputArguments.TOPIC_INPUTS_DATA_SOURCE)
        val hasContentEdit = dataSource?.hasContentEdit ?: false
        // 通知output保存，保存完成需要根据结果做出对应的提示或跳转通知
        val isSupportParametric = vmBus?.get<ParametricDataSource>(TOPIC_INPUTS_PARAMETRIC_DATA_SOURCE)?.isSupportParametric ?: false
        val parametricData = operatingStack.export(CommandFormat.JSON, isSupportParametric, isOliveFile())?.apply {
            this.hasContentEdit = (this.hasContentEdit || hasContentEdit)
        } ?: ""
        val contentEditProcess = getContentEditFileProcessType()
        waitOliveEdited {
            vmBus?.apply {
                notifyOnce(REPLY_TOPIC_SAVE_FILE, parametricData, contentEditProcess, editorSaveStatus)
            }
        }
    }

    /**
     * 等待olive水印编辑完成：
     * 1、TOPIC_OLIVE_IS_EDITING为null时，直接执行，无需等待
     * 2、TOPIC_OLIVE_IS_EDITING为true时，水印正在加载中，需要等待加载完成后即值为false时，才能执行保存操作，
     * 否则异步保存Olive水印会导致水印添加异常。
     */
    private fun waitOliveEdited(block: () -> Unit) {
        if (vmBus?.get<Boolean>(TOPIC_OLIVE_IS_EDITING) == null) {
            block.invoke()
            return
        }
        vmBus.subscribeUntilConsumed<Boolean>(TOPIC_OLIVE_IS_EDITING, true) {
            GLog.d(TAG, LogFlag.DL, "[waitOliveEdited] TOPIC_OLIVE_IS_EDITING is $it")
            if (it) {
                false
            } else {
                block.invoke()
                true
            }
        }
    }

    /**
     * 内容图的处理逻辑
     * 编辑操作栈中有效果，就要UPDATE内容图，记录一张无水印的内容图供下次编辑
     */
    private fun getContentEditFileProcessType(): ContentEditFileProcessType {
        return if (operatingStack.peek() != null) {
            ContentEditFileProcessType.UPDATE
        } else {
            ContentEditFileProcessType.CLEAR
        }
    }

    private fun notifyPostFailAction(textResId: Int) {
        vmBus?.notifyOnce(TOPIC_NOTIFICATION_ACTION, NotificationAction.ToastAction(textResId))
    }

    private fun setEditorSaveStatus(status: Int) {
        app.getAppAbility<ISettingsAbility>()?.use {
            it.setImageEditorSaveStatus(status)
        }
    }

    private fun notifyPostCancelAction() {
        val action = DiscardChangeConfirmDialogAction(
            confirmCallback = object : NotificationAction.IConfirmCallback {
                override fun onNeutralButtonClicked() {
                    onCancel()
                }
            })
        vmBus?.notifyOnce(TOPIC_NOTIFICATION_ACTION, action)
    }

    private fun isFirstPage(): Boolean {
        return getCurrentStrategy()?.pageLevel == STACK_INDEX_ONE
    }

    private fun needShowCancelDialog(): Boolean {
        return getCurrentStrategy()?.showDialogWhenCancel == true
    }

    /**
     * 对比按钮按压状态改变 通知 显示对应的预览图
     * @param compareTarget 对比状态
     */
    private fun notifyChangeCompareTarget(compareTarget: CompareTarget) {
        compareTargetPub?.publish(compareTarget)
    }

    private fun onStrategyIdChanged(strategyID: Int) {
        updateViewState()
    }

    private fun onTextureChanged(texture: ITexture) {
        updateViewState()
    }

    private fun notifyCancelEffect(record: OperatingRecord, render: Boolean = false) {
        editingVM.sessionProxy.cancelEffect(record, render)
    }

    private fun notifyCancelEffectAndPopRecord(record: OperatingRecord, render: Boolean = false) {
        editingVM.sessionProxy.cancelEffect(record, render)
        val peekRecord = operatingStack.peek()
        GLog.d(TAG, LogFlag.DF) { "notifyCancelEffectAndPopRecord, record=$record, peekRecord=$peekRecord" }
        if (peekRecord == record) {
            operatingStack.pop()
        } else if (null != peekRecord && peekRecord != record) {
            GLog.w(TAG, LogFlag.DF) { "notifyCancelEffectAndPopRecord, wtf! " }
        }
    }

    /**
     * 进入次级编辑页
     */
    private fun notifyEnterSubPage() {
        operatingStack.stack()
    }
    //========================================结束点击按钮时的通知处理========================================

    //========================================开始保存时弹框相关的业务处理========================================

    /**
     * SOTA:OLive关声音
     *  false->不弹弹窗的情况下保存为新文件
     * @return
     */
    private fun isNeedSaveNewOlive(): Boolean {
        val opRecord = vmBus?.notifyOnce<OperatingRecord>(REPLY_TOPIC_OPERATING_FIND_RECORD, AvEffect.OliveEffect.name).getOrRun {
            GLog.e(TAG, LogFlag.DL) { "[isNeedSaveNewOlive] OperatingRecord is not invalid" }
        } ?: return false

        val isOliveSoundEnable = opRecord.arguments[OliveRecord.OLIVE_SOUND_ENABLE] as? Boolean
        // 关声音,修改视频时长 都视为破坏性编辑 需要强制另存为新图
        if (isOliveSoundEnable == false) {
            return true
        }
        return false
    }

    private fun getNextSaveStep(): SaveDialogActionInfo {
        val isSellMode = ConfigAbilityWrapper.getBoolean(FEATURE_IS_SELL_MODE)
        if (isSellMode) {
            //卖场模式下，保存逻辑默认为保存新图
            return SaveDialogActionInfo(false, EDITOR_SAVE_STATUS_NEW)
        }
        // SOTA:OLive关声音
        if (isNeedSaveNewOlive()) {
            return SaveDialogActionInfo(false, EDITOR_SAVE_STATUS_NEW)
        }

        /**
         * 规避方案
         *
         * 经产品同意，heif格式的连拍图，在编辑保存时默认保存为新图，在编辑适配保存格式为heif后，再去除这个规避方案
         */
        vmBus?.get<DataSource>(TOPIC_INPUTS_DATA_SOURCE)?.filePath?.let {
            val mimeType = MimeTypeUtils.getMimeType(it, null)
            val isCShotPhoto = vmBus.get<MediaItem>(TOPIC_INPUTS_MEDIA_ITEM)?.isCShotPhoto() == true
            if (MimeTypeUtils.isHeifOrHeic(mimeType) && isCShotPhoto) {
                return SaveDialogActionInfo(false, EDITOR_SAVE_STATUS_NEW)
            }
        }
        val isSupportParametric = vmBus?.get<ParametricDataSource>(TOPIC_INPUTS_PARAMETRIC_DATA_SOURCE)?.isSupportParametric ?: false
        val defaultMsgResId = if (isSupportParametric) {
            R.string.picture3d_editor_support_project_save_dialog_msg
        } else {
            R.string.picture3d_editor_save_dialog_msg
        }
        return when (getCurrentStrategy()?.strategyID) {
            R.id.strategy_rm_ai_scenery -> {
                SaveDialogActionInfo(
                    true,
                    EDITOR_SAVE_STATUS_NEW,
                    titleResId = R.string.picture3d_editor_ai_scenery_export_title,
                    msgResId = null,
                    positiveButtonTextResId = R.string.picture3d_editor_ai_scenery_done,
                    neutralButtonTextResId = null,
                    isShowCheckBox = false
                )
            }

            R.id.strategy_privacy_watermark,
            R.id.strategy_privacy_watermark_master -> {
                // 这个分支只有隐私水印单独编辑
                when (checkOliveSaveTypeIfNeed()) {
                    // 隐私水印保存时，不需要弹框，直接存为新图
                    FileSaveType.NORMAL -> SaveDialogActionInfo(false, EDITOR_SAVE_STATUS_NEW)
                    // olive 保存为静态图
                    FileSaveType.SPECIALIZED_TO_NORMAL -> {
                        if (ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_SAVE_OLIVE, false)) {
                            SaveDialogActionInfo(false, EDITOR_SAVE_STATUS_NEW)
                        } else {
                            SaveDialogActionInfo(
                                true,
                                EDITOR_SAVE_STATUS_ASK,
                                titleResId = R.string.picture3d_editor_save_olive_dialog_title_backup,
                                msgResId = R.string.picture3d_editor_save_olive_dialog_message_backup,
                                isShowCheckBox = false
                            )
                        }
                    }
                }
            }

            R.id.strategy_specific_portrait_blur -> {
                // 人像景深无法拍摄Live图，都是普通图片，保存时，不需要弹框，直接覆盖原图
                SaveDialogActionInfo(false, EDITOR_SAVE_STATUS_OVERWRITE)
            }

            else -> {
                var uiSaveMode = ConfigAbilityWrapper.getInt(IMAGE_EDITOR_SAVE_STATUS, EDITOR_SAVE_STATUS_ASK)
                vmBus?.get<InputArgumentsVM.Exit>(TOPIC_EXIT)?.also { exit ->
                    // 如果是外部设定删除原图，不需要弹框
                    if (exit.isDeleteOriginPictureAfterEdited) {
                        uiSaveMode = EDITOR_SAVE_STATUS_DELETE
                        return SaveDialogActionInfo(false, uiSaveMode, msgResId = defaultMsgResId)
                    }
                }
                val isNeedShowDialog = (uiSaveMode == EDITOR_SAVE_STATUS_ASK)
                if (ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_SAVE_OLIVE, false)) {
                    SaveDialogActionInfo(isNeedShowDialog, uiSaveMode, msgResId = defaultMsgResId)
                } else {
                    when (checkOliveSaveTypeIfNeed()) {
                        FileSaveType.NORMAL -> SaveDialogActionInfo(isNeedShowDialog, uiSaveMode, msgResId = defaultMsgResId)
                        FileSaveType.SPECIALIZED_TO_NORMAL -> {
                            SaveDialogActionInfo(
                                true,
                                uiSaveMode,
                                msgResId = defaultMsgResId
                            )
                        }
                    }
                }
            }
        }
    }

    private fun checkOliveSaveTypeIfNeed(): FileSaveType {
        val isOlive = isOliveFile()
        val hasNotOliveEdit = operatingStack.anyInFirstStack { it.effectName != AvEffect.OliveEffect.name } ?: false
        return if (isOlive && hasNotOliveEdit) {
            FileSaveType.SPECIALIZED_TO_NORMAL
        } else {
            FileSaveType.NORMAL
        }.also {
            GLog.d(TAG, LogFlag.DF) { "[checkOliveSaveTypeIfNeed] fileSaveType = $it, isOlive = $isOlive, hasNotOliveEdit = $hasNotOliveEdit" }
        }
    }

    private fun isOliveFile(): Boolean {
        val mediaItem: MediaItem? = vmBus?.get<MediaItem>(TOPIC_INPUTS_MEDIA_ITEM)
        return ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_OLIVE)
                // tagFlags 用于判断先复原为 olive 文件再进行普通编辑的场景
                && ((mediaItem?.isOlivePhoto() == true) || editingVM.sessionProxy.matchesTagFlag(OplusExifTag.EXIF_TAG_OLIVE))
    }

    /**
     * 保存弹框所需信息
     * @param needShowDialog 是否需要弹框
     * @param uiSaveMode 用户选择的保存模式（存为新图、覆盖原图）
     * @param titleResId 标题文案
     * @param msgResId 描述文案
     * @param positiveButtonTextResId 确认按钮文案
     * @param negativeButtonTextResId 取消按钮文案
     * @param neutralButtonTextResId neutral按钮文案
     */
    data class SaveDialogActionInfo(
        val needShowDialog: Boolean,
        val uiSaveMode: Int,
        val titleResId: Int = R.string.picture3d_editor_save_dialog_title,
        val msgResId: Int? = R.string.picture3d_editor_save_dialog_msg,
        val positiveButtonTextResId: Int? = R.string.picture3d_editor_save_dialog_as_new,
        val negativeButtonTextResId: Int? = BaseR.string.common_cancel,
        val neutralButtonTextResId: Int? = R.string.picture3d_editor_save_dialog_overwrite_original,
        /**
         * 是否显示“记住此次选择，可在相册设置中修改保存方式”的复选框
         */
        val isShowCheckBox: Boolean? = true
    )
    //========================================结束保存时弹框相关的业务处理========================================

    //========================================开始record相关的业务处理========================================

    /**
     * 发布当前最新的record
     * @param curRecord 已知的最新记录，若为null，从栈里面找最新的
     */
    private fun publishCurRecord(curRecord: OperatingRecord? = null, invokeType: String) {
        val result = curRecord ?: (operatingStack.findLast(isFindUntilNonParametric = false) { true } ?: NullRecord)
        val operatingRecordWithInvoker = OperatingRecordWithInvoker(result, invokeType)
        recordPub?.publish(operatingRecordWithInvoker)
    }

    /**
     * 批量回退一些record
     *
     * @param abandonedRecords: 从旧到新的records
     * @param isRenderOnce: 控制管线是否走'批量处理编辑特效': 即一次操作多个effect,但是只渲染一次,出帧一次
     */
    private fun removeRecordList(abandonedRecords: List<OperatingRecord>, isRenderOnce: Boolean = true) {
        if (abandonedRecords.isNullOrEmpty()) {
            GLog.d(TAG, LogFlag.DL) { "removeRecordList: list is null or empty, got nothing to remove." }
            return
        }
        /**
         * 移除abandonedEffect
         */
        val removeAbandonedEffect = fun(abandoned: OperatingRecord, renderIfRemove: Boolean) {
            editingVM.sessionProxy.removeEffect(record = abandoned, removeCache = true, render = renderIfRemove, isRenderingOnce = true)
        }

        /**
         * 1. 移除操作需要 reversed
         * 2. isJustPackingBox 需要split
         * 3.多record对应单effect的情况，多级栈最后一个record移除时才是removeEffect，否则都是modifyEffect
         * 4.非参数化但是可以修改的情况
         * 5.如果找到的上一个特效的arguments为空，arguments为空的特效走的是removeEffect，removeEffect在effectUsageMap
         *  中是没有对应的EffectUsage，所以要走removeEffect。不然会出现走到modifyEffect，使用了其它EffectUsage导致效果异常
         */
        val abandonEffect = fun(renderIfRemove: Boolean) {
            abandonedRecords.flatMap { it.split() }.ktReversed().forEach { abandoned ->
                if (abandoned.isModifiable.not()) {
                    removeAbandonedEffect(abandoned, renderIfRemove)
                    return@forEach
                }
                findLastRecord(abandoned.isParametric) { it.isSameEffect(abandoned) }?.let {
                    if (it.arguments.isEmpty) {
                        removeAbandonedEffect(abandoned, renderIfRemove)
                        return@forEach
                    }
                    editingVM.sessionProxy.modifyEffect(oldRecord = abandoned, newRecord = it)
                } ?: removeAbandonedEffect(abandoned, renderIfRemove)
            }
        }
        if (isRenderOnce) {
            editingVM.sessionProxy.renderOnce { abandonEffect(true) }
        } else {
            abandonEffect(false)
        }
    }

    /**
     * 记录操作
     * @param recordAction 操作记录
     */
    private fun notifyRecordStep(recordAction: OperatingRecordAction): IAvEffectUsage? {
        if (isExiting) {
            GLog.w(TAG, LogFlag.DL, "notifyRecordStep: current is exiting, no need apply this record, return null.")
            return null
        }

        val record = recordAction.record
        // 参数化记录就要找栈里的最后一个参数化记录，否则只找当前栈里是否有相同的记录
        val lastRecord = findLastRecord(record.isParametric) { it.isSameEffect(record) }
        // 根据预览时是否走管线，决定是否调用applyEffect
        if (recordAction.type.applyEffect) {
            applyEffect(record, lastRecord, recordAction.render)
        } else if (recordAction.type.undoEffect) {
            // 撤销效果
            undoEffect(record, recordAction.render)
        }
        /**
         * AdjustRecord 的 '==' 的行为似乎不符合预期:
         *   对于对象不是同一个, 但是内筒相同的 AdjustRecord 我们希望 '==' 返货false. 但是实际上他会返回true.
         *
         *  作为对比:
         *    FilterRecord没有该问题(它的'==' 行为符合预期: 只判定object是否是相同的对象, 而不是判定实际内容)
         *
         *  为了对齐 AdjustRecord 和FilterRecord 在编辑栈框架的行为, 这里需要用 '==='
         */
        if ((recordAction.type.applyRecord) && (record === lastRecord).not()) {
            operatingStack.push(record)
            publishCurRecord(record, invokeType = INVOKE_TYPE_NOTIFY_RECORD_STEP)
            updateViewState()
        }
        return editingVM.sessionProxy.getEffectUsage(record)
    }

    /**
     * 查找栈里最后一个记录
     *  1.参数化编辑查找所有级别栈
     *  2.非参数化编辑查找当前级别栈
     */
    private fun findLastRecord(isParametric: Boolean, predicate: (OperatingRecord) -> Boolean): OperatingRecord? {
        return if (isParametric) {
            operatingStack.findLast(isFindUntilNonParametric = true, predicate)
        } else {
            getCurrentStrategy()?.pageLevel?.let { operatingStack.findLast(it, isFindUntilNonParametric = false, predicate) }
        }
    }

    /**
     * 移除参数化的record，让管线重新渲染一帧内容编辑图
     * 需要特殊处理的条件：当次编辑都是参数化编辑，那么内容的数据不变
     * @param remove true 移除效果， false 还原效果
     */
    private fun processParameterRecords(remove: Boolean) {
        val recordList = operatingStack.peekInStack { it.isParametric }
        // 防止isJustPackingBox为true时，且其中有非参数化记录时，非参数化记录被移除，这里需要提前split. 修复9229016：两次AI构图，由于第二次AI构图没有调节效果导致没有还原效果图的调节效果
        val splitRecords = recordList?.flatMap { it.split() }?.filter { it.isParametric }?.toMutableList()
        // 取最新的record去做还原。
        val splitRecordList = splitRecords?.groupBy { it.effectName }?.values?.map { it.first() }?.toMutableList()
        // 因为隐私水印保存后不能去除，保存隐私水印时，不应该移除隐私水印的record，保留隐私水印在内容图上
        if (vmBus?.get<EditingImagePack>(TOPIC_IMAGE_PACK)?.watermarkInfo?.params?.pattern == WatermarkPattern.PATTERN_PRIVACY) {
            splitRecordList?.removeIf { (it is WatermarkRecord) && it.isAddWatermarkRecord() }
        }
        // olive编辑参数不能去除，去除后无法还原
        splitRecordList?.removeIf { it is OliveRecord }
        splitRecordList?.filter { record -> record.isJustPackingBox.not() }?.let {
            if (remove) {
                editingVM.sessionProxy.batchRemoveEffect(it)
            } else {
                editingVM.sessionProxy.batchAddEffect(it.ktReversed())
            }
        }
    }

    /**
     * 查找指定条件下的记录
     * @param arg 条件类
     * @return 返回查找到的记录
     */
    private fun findSpecificRecords(arg: FindRecordParams): List<OperatingRecord> {
        val opStack = operatingStack
        return when (arg.findType) {
            RecordFindType.FIND_LAST,
            RecordFindType.FIND_LAST_UNTIL_NON_PARAMETRIC -> {
                val isFindUntilNonParametric = arg.findType == RecordFindType.FIND_LAST_UNTIL_NON_PARAMETRIC
                val result = if (arg.stackIndex == STACK_INDEX_ALL) {
                    opStack.findLast(isFindUntilNonParametric = isFindUntilNonParametric, predicate = arg.predicate)
                } else {
                    opStack.findLast(index = arg.stackIndex, isFindUntilNonParametric = isFindUntilNonParametric, predicate = arg.predicate)
                }
                result?.let { listOf(result) } ?: emptyList()
            }

            RecordFindType.FIND_FIRST -> emptyList()

            RecordFindType.FIND_ALL -> opStack.findAllRecord(index = arg.stackIndex, predicate = arg.predicate)
        }
    }

    //========================================结束record相关的业务处理========================================

    //========================================开始按钮相关的处理逻辑（撤销、重做、复原、一级二级页的进入和退出）========================================

    /**
     * 当前编辑页是否有修改
     * @return true/false
     */
    private fun hasModified(): Boolean {
        // 二级页判断操作栈是否可回退且最后一步有实际效果；一级页还需要判断是否可回退或是否做了复原动作
        return if (isFirstPage().not()) {
            operatingStack.canUndo() && (operatingStack.peek()?.hasEffect == true)
        } else {
            operatingStack.canUndo() || operatingMemo.hasRecovery
        }
    }

    /**
     * 是否有对比效果：
     * 一级页需要判断是否可回退，二级页判断操作栈是否可回退且最后一步有实际效果。
     * 执行过复原 hasPerformedRecovery，算作有修改 hasModified()，并不算作对比效果 hasComparable()
     * @return true/false
     */
    private fun hasComparable(): Boolean {
        return if (isFirstPage()) {
            operatingStack.canUndo()
        } else {
            val curPageAllRecords = operatingStack.findAllRecord(
                includeImportRecord = false,
                index = operatingStack.getStackCount() - 1,
                predicate = { true })
            val curPageEffectTypeSet = curPageAllRecords
                .map { it.effectName }.toSet()

            if (curPageEffectTypeSet.size <= 1) {
                // 对于单一effect类型的编辑页 按照如下判定(第二个条件是参数化编辑页特有逻辑)
                operatingStack.canUndo() && (curPageAllRecords.firstOrNull()?.hasEffect == true)
            } else {
                // 对于复合多effect的编辑页, 要逐一判断每个record类型 的头部record
                curPageEffectTypeSet.any { effectName ->
                    curPageAllRecords.firstOrNull { it.effectName == effectName }?.hasEffect == true
                }
            }
        }
    }

    /**
     * 点击二级页面的完成
     */
    private fun confirm() {
        if (isHandlingConfirmAction) return
        isHandlingConfirmAction = true
        saveOperateType()
        confirmed {
            isHandlingConfirmAction = false
            exitCurrentPage(it && hasModified())
            // markby dingyong 调试阶段打印log，后续会考虑移除
            GLog.d(TAG, LogFlag.DF, "confirm")
        }
    }

    /**
     * 点击一级页面的复原按钮
     */
    private fun recover() {
        val callback = object : NotificationAction.IConfirmCallback {
            override fun onNeutralButtonClicked() {
                clearStack()
                updateViewState()
                // markby dingyong 调试阶段打印log，后续会考虑移除
                GLog.d(TAG, LogFlag.DF, "recover")
                notifyToSave(EDITOR_SAVE_STATUS_OVERWRITE)
            }
        }
        vmBus?.notifyOnce(TOPIC_NOTIFICATION_ACTION, RecoverConfirmDialogAction(callback))
        exitType = CommonEditorTrackConstant.Value.EXIT_TYPE_RECOVER
    }

    private fun clearStack() {
        operatingStack.clear()
    }

    private fun setAndPublishOperatingMemo(hasRecoverEffects: Boolean, checkResult: CheckResult? = null) {
        checkResult?.let {
            operatingMemo = operatingMemo.copy(hasRecoverEffects = hasRecoverEffects, recoverDataSource = it)
        } ?: run {
            operatingMemo = operatingMemo.copy(hasRecoverEffects = hasRecoverEffects)
        }
        operatingMemoPub?.publish(operatingMemo)
        GLog.d(TAG, "[setAndPublishOperatingMemo] operatingMemo = $operatingMemo")
    }

    /**
     * 恢复按钮被点击
     * 先看下有么有对重置进行拦截的，有则先执行拦截的部分逻辑，再在回调中执行正常重置业务
     * 没有则直接执行恢复逻辑
     */
    private fun restore() {
        // 显然只能restore当前多级栈的最顶层栈.
        val stackIndex = max(operatingStack.getStackCount() - 1, 0)
        val subPageRecords = findSpecificRecords(FindRecordParams(stackIndex = stackIndex))

        val operatingActionNtf = vmBus?.subscribeR<Unit>(TOPIC_OPERATING_ACTION_RESTORE)
        GLog.d(TAG, LogFlag.DF, "[restore] subPageRecords size: ${subPageRecords.size}, $operatingActionNtf")
        // 如果有注册的topic，发通知回调
        operatingActionNtf?.notify(subPageRecords.size, object : OperatingResultListener {
            override fun onResult(isConsumed: Boolean) {
                GLog.d(TAG, LogFlag.DF, "[restore] onResult: isSuccess = $isConsumed")
                if (isConsumed) {
                    doRestoreUnchecked()
                }
                vmBus?.unsubscribe(TOPIC_OPERATING_ACTION_RESTORE, operatingActionNtf)
            }
        }) ?: run {
            GLog.d(TAG, LogFlag.DF, "[restore] No registered action, will do restore directly")
            doRestoreUnchecked()
        }
    }

    /**
     * 不做任何检查,执行restore
     */
    private fun doRestoreUnchecked() {
        GLog.d(TAG, LogFlag.DF, "[doRestoreUnchecked]")
        operatingStack.clearTopStack { abandonedRecords ->
            removeRecordList(abandonedRecords)
        }
    }

    private fun cancel() {
        // markby dingyong 调试阶段打印log，后续会考虑移除
        GLog.d(TAG, LogFlag.DF, "cancel")
        if (!isFirstPage()) {
            saveOperateType()
        }
        if (hasModified() && needShowCancelDialog()) {
            notifyPostCancelAction()
        } else {
            onCancel()
        }
    }

    /**
     * 点击取消后，开始执行退出的流程
     */
    private fun onCancel() {
        val exit = vmBus?.get<InputArgumentsVM.Exit>(TOPIC_EXIT)
        val currentStrategyID = getCurrentStrategy()?.strategyID
        val autoSelectStrategyID = vmBus?.get<Int>(TOPIC_MENU_AUTO_SELECT)
        /*
         * 取消后续操作
         */
        val cancelOperation = exit?.rule?.getCancelOperation {
            /*
             * 从演示apk进入编辑，会自动跳转到指定页面：
             * 1，从 AI去反光 跟 AI去拖影 进来是AI图像助手页
             * 2，从AI超清像素进来是裁剪旋转
             * 3，从AI消除进来是AI消除
             * 若当前页面就是自动跳转的指定页，取消就要退回到演示apk
             */
            currentStrategyID == autoSelectStrategyID
        }
        if (cancelOperation == CancelOperation.FINISH) {
            backTypePub?.publish(BackType.FINISH_PAGE)
            return
        }
        val operatingActionNtf = vmBus?.subscribeR<Unit>(TOPIC_OPERATING_ACTION_CANCEL)
        if (operatingActionNtf == null) {
            exitCurrentPage(false)
        } else {
            operatingActionNtf.notify(object : ExitPageListener {
                override fun onExitPage(isConfirm: Boolean, exitPage: Boolean) {
                    GLog.d(TAG, "[cancel] onExitPage: isConfirm = $isConfirm")
                    vmBus?.unsubscribe(TOPIC_OPERATING_ACTION_CANCEL, operatingActionNtf)
                    exitCurrentPage(isConfirm)
                }
            })
        }
    }

    /**
     * 退出当前页面
     * @param isConfirm true：确认退出；false：取消退出
     */
    private fun exitCurrentPage(isConfirm: Boolean) {
        // markby dingyong 调试阶段打印log，后续会考虑移除
        GLog.w(TAG, LogFlag.DF, "exitCurrentPage: isConfirm = $isConfirm")
        if (isExiting) {
            GLog.w(TAG, LogFlag.DL, "<onCancel> current is exiting return!")
            return
        }
        // 一级页的退出状态不需要恢复，因为还有后续的退出动画等，前面也可以用这个状态防止再次点击保存
        isExiting = true
        val mediaItem = vmBus?.get<MediaItem>(TOPIC_INPUTS_MEDIA_ITEM)
        val list = vmBus?.notifyOnce<List<OperatingRecord>>(REPLY_TOPIC_OPERATING_FIND_ALL_OPERATING_RECORDS, ALL_OPERATING_RECORDS)
        if (isFirstPage()) {
            // 退出编辑页
            val exit = vmBus?.get<InputArgumentsVM.Exit>(TOPIC_EXIT)
            // 支持转场动画，发送转场动画通知，监听动画结束livedata，结束完通知页面去finish
            if (exit?.rule?.getFinishOperation() == FinishOperation.TO_PHOTO_PAGE) {
                // 不支持转场动画，跳转到大图页面
                backTypePub?.publish(BackType.TO_PHOTO_PAGE)
            } else {
                backTypePub?.publish(BackType.LAST_PAGE)
            }
            if (!isConfirm) {
                exitType = CommonEditorTrackConstant.Value.EXIT_TYPE_CANCEL
            } else if (exitType != CommonEditorTrackConstant.Value.EXIT_TYPE_RECOVER) {
                when (ConfigAbilityWrapper.getInt(IMAGE_EDITOR_SAVE_STATUS, EDITOR_SAVE_STATUS_ASK)) {
                    EDITOR_SAVE_STATUS_OVERWRITE -> exitType = CommonEditorTrackConstant.Value.EXIT_TYPE_ORIGIN
                    EDITOR_SAVE_STATUS_NEW -> exitType = CommonEditorTrackConstant.Value.EXIT_TYPE_NEW_PHOTO
                }
            }

            TrackScope.launch(Dispatchers.IO) {
                CommonEditorTracker.trackExitFirstPage(mediaItem, list, exitType, startTime, secondEditCompletionMap)
            }
            if (isConfirm) {
                FilterTracker.trackFilterEvent(list)
                trackPortraitBlurEvent()
            }
            TransformTrackHelper.trackExitEditTransformEvent()
        } else {
            // 退出次级页
            exitSubPage(isConfirm)
            backTypePub?.publish(BackType.LAST_PAGE)
            isExiting = false
            if (operateType > CommonEditorTrackConstant.Value.TYPE_FIRST_PAGE) {
                CommonEditorTracker.trackExitSecondPage(isConfirm, mediaItem, operateType)
                secondEditCompletionMap[operateType] = if (isConfirm) {
                    CommonEditorTrackConstant.Value.EXIT_SECOND_TYPE_DONE
                } else {
                    CommonEditorTrackConstant.Value.EXIT_SECOND_TYPE_CANCEL
                }
            }
            operateType = CommonEditorTrackConstant.Value.TYPE_DEFAULT
        }
    }

    private fun trackPortraitBlurEvent() {
        // 如果当前记录二级页操作的Map为空，说明是从大图直接跳转人像景深编辑页，此时点击保存需要触发埋点上报
        if (secondEditCompletionMap.isNotEmpty()) return
        val isFront = editingVM.sessionProxy.getMetadataStruct<FrontDepthStruct>(
            MetadataType.EXTENDED,
            EXTEND_KEY_FRONT_DEPTH_CONFIG
        ) != null
        vmBus?.notifyOnce<OperatingRecord>(REPLY_TOPIC_OPERATING_FIND_RECORD, AvEffect.PortraitBlurEffect.name)?.also {
            val fNumber = it.arguments[PortraitBlurRecord.KEY_PORTRAIT_BLUR_APERTURE] as? Float
            PortraitBlurTrackHelper.trackPortraitBlurData(true, isFront, fNumber, true)
        }
    }

    private fun saveOperateType() {
        val pageId = CommonEditorTracker.getPageType(getCurrentStrategy()?.strategyID ?: INVALID)
        if (pageId > CommonEditorTrackConstant.Value.TYPE_FIRST_PAGE) {
            operateType = pageId
        }
    }

    private fun confirmed(block: (isConsume: Boolean) -> Unit) {
        val operatingActionNtf = vmBus?.subscribeR<Unit>(TOPIC_OPERATING_ACTION_CONFIRM)
        // 看看是否有注册的topic
        if (operatingActionNtf == null) {
            /**
             * 解bug8494519
             * 快速点击完成场景，当前正在退出或者已经退到编辑一级页面，还有点完成的事件过来，不需要退出
             */
            getCurrentStrategy()?.pageLevel ?: run {
                isHandlingConfirmAction = false
                return
            }
            if (isFirstPage()) {
                isHandlingConfirmAction = false
                GLog.w(TAG, LogFlag.DF) { "[confirm] confirmed at firstPage，ignore" }
            } else {
                block(true)
            }
        } else {
            operatingActionNtf.notify(object : ExitPageListener {
                override fun onExitPage(isConfirm: Boolean, exitPage: Boolean) {
                    GLog.d(TAG, "[confirm] onExitPage: isConfirm = $isConfirm")
                    vmBus?.unsubscribe(TOPIC_OPERATING_ACTION_CONFIRM, operatingActionNtf)
                    if (!exitPage) {
                        isHandlingConfirmAction = false
                        return
                    }
                    block(isConfirm)
                }
            }) ?: block(true)
        }
    }

    private fun notifySaveAction(info: SaveDialogActionInfo) {
        val action = SaveDialogAction(
            titleResId = info.titleResId,
            msgResId = info.msgResId,
            positiveButtonTextResId = info.positiveButtonTextResId,
            negativeButtonTextResId = info.negativeButtonTextResId,
            neutralButtonTextResId = info.neutralButtonTextResId,
            isShowCheckBox = info.isShowCheckBox,
            confirmCallback = getSaveDialogActionCallback()
        )
        vmBus?.notifyOnce(TOPIC_NOTIFICATION_ACTION, action)
    }

    /**
     * 点击一级页面的保存
     */
    private fun save() {
        if (isExiting || (vmBus?.get<SaveStatus>(TOPIC_OPERATING_SAVE_STATUS) == SaveStatus.SAVE_BEGIN)) {
            GLog.w(TAG, LogFlag.DF) { "[save] isExiting = $isExiting or has not save complete，return" }
            return
        }

        if (hasModified().not()) {
            // 一级页面没有修改，目前是不能触发保存的
            GLog.e(TAG, "has not modify，cannot save. return")
            return
        }
        val saveDialogActionInfo = getNextSaveStep()
        GLog.d(TAG, LogFlag.DF) { "[save] saveDialogActionInfo = $saveDialogActionInfo" }

        val operatingActionNtf = vmBus?.subscribeR<Unit>(TOPIC_OPERATING_ACTION_SAVE)
        operatingActionNtf?.let {
            operatingActionNtf.notify(object : ExitPageListener {
                override fun onExitPage(isConfirm: Boolean, exitPage: Boolean) {
                    vmBus?.unsubscribe(TOPIC_OPERATING_ACTION_SAVE, operatingActionNtf)
                }
            })
        }

        if (saveDialogActionInfo.needShowDialog) {
            notifySaveAction(saveDialogActionInfo)
        } else {
            notifyToSave(saveDialogActionInfo.uiSaveMode)
        }
        // markby dingyong 调试阶段打印log，后续会考虑移除
        GLog.d(TAG, LogFlag.DF, "save")
    }

    /**
     * 退出次级编辑页
     * @param isConfirm true：确认退出；false：取消退出
     */
    private fun exitSubPage(isConfirm: Boolean) {
        val pageLevel = getCurrentStrategy()?.pageLevel ?: return
        GLog.d(TAG, LogFlag.DL) { "exitSubPage: level = $pageLevel" }
        val combineStrategy = getCombineStrategy()
        operatingStack.stackPop(isConfirm, combineStrategy) { combinedRecord, abandonedRecords ->
            // markby dingyong 调试阶段打印log，后续会考虑移除
            GLog.d(TAG, "exitSubPage isConfirm = $isConfirm, combinedRecord = $combinedRecord, abandonedRecords = $abandonedRecords")
            if (isConfirm) {
                // Mark by wudanyang: @jiepengpeng 这里只需要remove水印废弃的record，不能remove其它，得再看看有没有啥好点的方案
                editingVM.sessionProxy.removeAbandonedRecords(
                    abandonedRecords.filter { (it != combinedRecord) && (it.effectName == AvEffect.WatermarkEffect.name) }
                )
            } else {
                val hasWatermark = vmBus?.get<EditingImagePack>(TOPIC_IMAGE_PACK)?.watermarkInfo?.hasWatermark() ?: false
                // 若是有水印且二级返回一级则不用渲染，因为后面添加水印会渲染
                removeRecordList(abandonedRecords, (pageLevel > STACK_INDEX_TWO) || hasWatermark.not())
            }
            // 每次二级页退出的时候，统一做下兜底清理，按理这时候不会出现deadUsage（出现的话根据log定位为什么会出现）
            editingVM.sessionProxy.trimDeadUsages()
            if (isConfirm) {
                publishCurRecord(invokeType = INVOKE_TYPE_COMPLETE_EXIT_SUB_PAGE)
                // 这个时机触发一次检测操作，确保检测结果正确，当前时机能确保record为二级编辑后的合并record（内部会判断是否需要检测)
                vmBus?.notifyOnce(TopicID.Detecting.TOPIC_DETECTING_ALL)
            } else {
                publishCurRecord(invokeType = INVOKE_TYPE_CANCEL_EXIT_SUB_PAGE)
            }
        }

        if (pageLevel > STACK_INDEX_TWO) { // pageLevel>1，说明是2级以上的子页面了，不做水印处理
            GLog.d(TAG, LogFlag.DL) { "[exitSubPage] pageLevel>1, no need deal watermark." }
            return
        } else if ((getCurrentStrategy()?.strategyID == R.id.strategy_watermark)
            || (getCurrentStrategy()?.strategyID == R.id.strategy_watermark_master)
        ) { // 水印页面自行处理水印
            GLog.d(TAG, LogFlag.DL) { "[exitSubPage] click from watermark page, no need deal watermark." }
        } else {
            vmBus?.notifyOnce(REPLY_TOPIC_RESTORE_WATERMARK_IF_NEED)
        }
    }

    /**
     * 回退按钮被点击，执行回退逻辑
     */
    private fun undo() {
        val operatingActionNtf = vmBus?.subscribeR<Unit>(TOPIC_OPERATING_ACTION_UNDO)
        // 看看是否有业务注册的topic，意图消费回退事件
        if (operatingActionNtf == null) {
            performUndo()
            return
        }
        operatingActionNtf.notify(object : OperatingResultListener {
            override fun onResult(isConsumed: Boolean) {
                GLog.d(TAG, "[undo] onResult: isConsumed: $isConsumed")
                // 回退事件是否已被消费，如未消费，继续执行performUndo
                if (isConsumed.not()) {
                    performUndo()
                }
                vmBus?.unsubscribe(TOPIC_OPERATING_ACTION_UNDO, operatingActionNtf)
            }
        })
    }

    private fun performUndo() {
        undoStackAndEffect({
            // 判断当前undo、redo状态
            updateViewState()
        })
    }

    /**
     * 重做按钮被点击，执行重做逻辑
     */
    private fun redo() {
        val operatingActionNtf = vmBus?.subscribeR<Unit>(TOPIC_OPERATING_ACTION_REDO)
        // 看看是否有业务注册的topic，意图消费重做事件
        if (operatingActionNtf == null) {
            performRedo()
            return
        }
        operatingActionNtf.notify(object : OperatingResultListener {
            override fun onResult(isConsumed: Boolean) {
                GLog.d(TAG, "[redo] onResult: isConsumed: $isConsumed")
                // 重做事件是否已被消费，如未消费，继续执行performRedo
                if (isConsumed.not()) {
                    performRedo()
                }
                vmBus?.unsubscribe(TOPIC_OPERATING_ACTION_REDO, operatingActionNtf)
            }
        })
    }

    private fun performRedo() {

        fun redoSingleEffect(redoRecord: OperatingRecord, parentRecord: OperatingRecord? = null) {
            val lastRecord = operatingStack.findLast(isFindUntilNonParametric = redoRecord.isModifiable) {
                // redo的case不能找自己
                it.isSameEffect(redoRecord) && (redoRecord === it).not()
            }
            GLog.d(TAG, LogFlag.DL) { "performRedo.redoSingle: lastRecord = $lastRecord , redoRecord = $redoRecord, parentRecord = $parentRecord" }
            applyEffect(redoRecord, lastRecord, render = false)
        }

        /**
        redo的时候, 要在 "从redoStack栈pop出来后, undoStack栈还没进入前"这个时机, 就开始如下的管线操作.
        否则后续乱:
        管线操作前, 可能存在若干次栈查找动作, 以决定是modifyEffect还是addEffect.
        在彻底的redo(redoStack+undoStack更新完成后), 再去查栈, 本次可能查到record自己.
        这在record没有子record的时候, 无非是查询条件增加一个 "(redoRecord === it).not()" 简单应付下.
        但是在多中类型record复合的record中(尤其是参数化+非参数化 最后合并成一个非参数化record)时, 行为就乱了.
         */
        operatingStack.redo redoCallBack@{ redoRecord ->
            if (null == redoRecord) {
                return@redoCallBack
            }

            if (redoRecord.split().size > 1) {
                GLog.d(TAG, LogFlag.DL) { "performRedo: lastRecord = $redoRecord. will process multi-sub-record..." }
                redoRecord.split().forEach { redoSingleEffect(it, redoRecord) }
            } else {
                redoSingleEffect(redoRecord)
            }
            // redo()可能存在'修改个effect', 为了减轻管线负载,和避免'闪'的UI问题. 这里要统一到最后才请求一次渲染.
            editingVM.sessionProxy.invalidate()

            updateImagePackIfNeed(redoRecord)
            publishCurRecord(redoRecord, invokeType = INVOKE_TYPE_PERFORM_REDO)
        }
        // 判断当前undo、redo状态
        updateViewState()
    }


    //========================================结束按钮相关的处理逻辑（撤销、重做、复原、一级二级页的进入和退出）========================================

    //========================================开始effect相关处理========================================

    /**
     * 回退栈跟特效
     * @param nextAction 下一步动作
     * @param needRender 是否要绘制
     * @return 返回刚刚回退的记录
     */
    private fun undoStackAndEffect(nextAction: (() -> Unit)? = null, needRender: Boolean = true): OperatingRecord? {
        return operatingStack.undo()?.let { record ->
            undoEffect(record, needRender)
            publishCurRecord(invokeType = INVOKE_TYPE_PERFORM_UNDO)
            nextAction?.invoke()
            record
        }
    }

    /**
     * 应用效果，可能是add/insert/modify三种操作，根据栈中参数化的情况
     * @param record 需要应用的效果
     * @param lastRecord 锚，用来查找需要修改的usage，为空时说明当前缓存中没有对应usage
     * @param render 管线是否进行对该效果进行渲染，默认为渲染
     */
    private fun applyEffect(record: OperatingRecord, lastRecord: OperatingRecord?, render: Boolean = true) {
        // 如果是空效果，移除特效
        if (record.arguments.isEmpty) {
            lastRecord?.also {
                editingVM.sessionProxy.removeEffect(it, render = render)
            } ?: run {
                deleteWatermarkEffectIfNeed(record, render = render)
            }
            return
        }

        when {
            (lastRecord == record) -> {
                // 如果是同个record，直接modify，而且不入栈
                if (editingVM.sessionProxy.modifyEffect(lastRecord, record, render)) {
                    return
                }
            }

            record.isParametric -> {
                if (applyParametricEffect(record, lastRecord, render)) {
                    return
                }
            }

            record.isModifiable -> {
                if (applyModifiableEffect(record, lastRecord, render)) {
                    return
                }
            }
        }

        // 在这个地方查询session中是否有添加水印的usage,其他操作应该在添加水印后面执行效果
        val (index, resultRecord) = editingVM.sessionProxy.queryEffect {
            (it.key is WatermarkRecord) && ((it.key as WatermarkRecord).isAddWatermarkRecord())
        }

        GLog.d(TAG, "applyEffect index:$index")
        val isSupportParametric = vmBus?.get<ParametricDataSource>(TOPIC_INPUTS_PARAMETRIC_DATA_SOURCE)?.isSupportParametric ?: false
        if (resultRecord != null) {
            // 如果当前是水印record需要修改effect
            if (record.effectName == AvEffect.WatermarkEffect.name) {
                // 执行modifyEffect，newRecord为参数化时，查找effectUsage 遇到非参数化需要停止查找，水印为特殊情况不需要提前停止 stopOnNonParametric设置为false
                val isSuccess = editingVM.sessionProxy.modifyEffect(oldRecord = resultRecord, newRecord = record, stopOnNonParametric = false)
                if (isSuccess) {
                    editingVM.sessionProxy.removeAbandonedRecords(arrayListOf(resultRecord))
                    return
                }
            } else {
                // 找优先级低于当前，优先级diff最小，且在最近一次非参数化记录之后的一条记录
                val nextRecord: OperatingRecord? = operatingStack.findNextPriorityRecord(record, isSupportParametric = isSupportParametric)
                nextRecord?.let {
                    val isSuccess = editingVM.sessionProxy.insertSingleEffect(record, it, render)
                    if (isSuccess) {
                        return
                    }
                }
                editingVM.sessionProxy.insertSingleEffect(record, index, render)
            }
        } else {
            // 找优先级低于当前，优先级diff最小，且在最近一次非参数化记录之后的一条记录
            val nextRecord: OperatingRecord? = operatingStack.findNextPriorityRecord(record, isSupportParametric = isSupportParametric)
            GLog.d(TAG, LogFlag.DL) { "applyEffect: record = $record , nextRecord = $nextRecord" }
            nextRecord?.let {
                val isSuccess = editingVM.sessionProxy.insertSingleEffect(record, it, render)
                if (isSuccess) {
                    return
                }
            }
            // 找不到直接新增effect
            editingVM.sessionProxy.addSingleEffect(record, render = render)
        }
    }

    private fun applyModifiableEffect(record: OperatingRecord, lastRecord: OperatingRecord?, render: Boolean): Boolean {
        lastRecord?.let {
            // 若两个记录对应同一个effectUsage，则修改这个effectUsage；若两个记录对应不同effectUsage，则添加effectUsage
            val oldEffect = editingVM.sessionProxy.getEffectUsage(it)
            val newEffect = editingVM.sessionProxy.getEffectUsage(record)
            val isSuccess = when {
                oldEffect == null -> false
                (newEffect != null) && (newEffect != oldEffect) -> {
                    editingVM.sessionProxy.addSingleEffect(record, render = render)
                    true
                }

                else -> editingVM.sessionProxy.modifyEffect(it, record, render)
            }
            if (isSuccess) {
                return true
            }
        }
        return false
    }

    private fun applyParametricEffect(record: OperatingRecord, lastRecord: OperatingRecord?, render: Boolean): Boolean {
        lastRecord?.let {
            // 找到同效果的参数化记录，修改effect
            val isSuccess = editingVM.sessionProxy.modifyEffect(it, record, render)
            if (isSuccess) {
                return true
            }
        }
        val isSupportParametric = vmBus?.get<ParametricDataSource>(TOPIC_INPUTS_PARAMETRIC_DATA_SOURCE)?.isSupportParametric ?: false
        // 找优先级低于当前，优先级diff最小，且在最近一次非参数化记录之后的一条记录
        val nextRecord: OperatingRecord? = operatingStack.findNextPriorityRecord(record, isSupportParametric = isSupportParametric)
        nextRecord?.let {
            val isSuccess = editingVM.sessionProxy.insertSingleEffect(record, it, render)
            if (isSuccess) {
                return true
            }
        }
        return false
    }

    /**
     * 撤销当前record对应的效果
     * @param record OperatingRecord
     * @param render 是否需要渲染
     */
    private fun undoEffect(record: OperatingRecord, render: Boolean = true) {
        record.splitForUndo().let { childRecordList ->
            childRecordList.forEach { subRecord ->
                // 只有最后一个subRecord才有可能有必要渲染
                undoSingle(subRecord, render = false)
            }
            // undoEffect()可能存在'修改个effect', 为了减轻管线负载,和避免'闪'的UI问题. 这里要统一到最后才请求一次渲染.
            if (render) {
                editingVM.sessionProxy.invalidate()
            }
        }
    }

    /**
     * 撤销当前record对应的效果
     * @param record OperatingRecord
     * @param render 是否需要渲染
     *
     * 注意: 不支持record.split() !!
     */
    private fun undoSingle(
        record: OperatingRecord,
        render: Boolean
    ) {
        if (record.isModifiable.not()) {
            // 如果是不可修改的记录，直接移除
            editingVM.sessionProxy.removeEffectDirectly(record, render = render)
            return
        }

        val lastRecord = operatingStack.findLast(
            // 编辑过程中只有一个水印效果，应该一直查找
            isFindUntilNonParametric = if (record.effectName == AvEffect.WatermarkEffect.name) false else record.isParametric
        ) { (it.effectName == record.effectName) && (it === record).not() }

        if (lastRecord != null) {
            if (lastRecord.arguments.isEmpty) {
                // 上一步是显示原图（arguments为空时显示原图），只需要把record所属的effect移除掉
                editingVM.sessionProxy.removeEffectDirectly(record, render = render)
                updateImagePackIfNeed(lastRecord)
                return
            } else {
                // 若两个记录对应同一个effectUsage，则修改这个effectUsage；若两个记录对应不同effectUsage，则移除回退的effectUsage（一级栈有两个isModifiable的record的场景）
                val undoEffect = editingVM.sessionProxy.getEffectUsage(record)
                val lastEffect = editingVM.sessionProxy.getEffectUsage(lastRecord)
                val isSuccess = when {
                    undoEffect == null -> false
                    (lastEffect != null) && (lastEffect != undoEffect) && editingVM.sessionProxy.isEffectInClip(lastEffect) -> {
                        editingVM.sessionProxy.removeEffectDirectly(record, render = render)
                    }

                    else -> editingVM.sessionProxy.modifyEffect(record, lastRecord, render = render)
                }
                if (isSuccess) {
                    // 判断当前undo、redo状态
                    updateImagePackIfNeed(lastRecord)
                    return
                }
            }
        }
        // 如果这个记录是原图效果（arguments为空时显示原图），需要重新恢复旧记录的效果
        if (record.arguments.isEmpty) {
            // 如果无旧记录，就不执行
            lastRecord?.takeIf { it.arguments.isEmpty.not() }?.also {
                editingVM.sessionProxy.addEffect(it, render = render)
                updateImagePackIfNeed(it)
            } ?: run {
                addWatermarkIfNeedWhenUndo(record)
            }
        } else {
            // 其它场景正常移除effect
            var hasRemoveEffect = false
            addWatermarkIfNeedWhenUndo(record) {
                editingVM.sessionProxy.removeEffect(record, render = false)
                hasRemoveEffect = true
            }
            if (hasRemoveEffect.not()) {
                editingVM.sessionProxy.removeEffect(record, render = render)
            }
        }
    }

    //========================================结束effect相关处理========================================

    //========================================开始按钮状态相关处理========================================

    /**
     * 更新当前操作状态
     */
    private fun updateViewState() {
        val state = vmBus?.get<OperatingViewState>(TOPIC_OPERATING_VIEW_STATE) ?: let {
            GLog.d(TAG, "[updateViewState] can't find operatingViewState")
            OperatingViewState()
        }
        val currentStrategy = getCurrentStrategy() ?: let {
            GLog.e(TAG, "[updateViewState] error, can't get currentStrategy")
            return
        }
        // 撤销、恢复
        val canUndo = currentStrategy.isUndoable && operatingStack.canUndo()
        val canRedo = currentStrategy.isUndoable && operatingStack.canRedo()
        val redoUndoVisible = (currentStrategy.isUndoable) && (canRedo || canUndo)
        val undoBtnFlags = state.undoBtnFlags.setEnable(canUndo).setVisible(redoUndoVisible)
        val redoBtnFlags = state.redoBtnFlags.setEnable(canRedo).setVisible(redoUndoVisible)
        /*
        * 保存、标题
        * 编辑首页并且菜单分区时才设置按钮背景可用
        */
        val backgroundEnable = getCurrentStrategy()?.strategyID == R.id.strategy_editing_page
        val saveBtnFlags = state.saveBtnFlags.setEnable(hasModified() || currentStrategy.forceSaveBtnEnable).setBackgroundEnable(backgroundEnable)
        val cancelBtnFlags = state.cancelFlags.setBackgroundEnable(backgroundEnable)
        val isFirstPage = isFirstPage()
        val operatingBarType = if (isFirstPage) ACTION_TYPE_TEXT_BUTTON else ACTION_TYPE_IMAGE_BUTTON
        val titleText = getCurrentStrategy()?.strategyNameRes ?: Resources.ID_NULL
        val saveBtnText = getCurrentStrategy()?.saveBtnNameRes ?: Resources.ID_NULL
        val parametricDataSource = vmBus?.get<ParametricDataSource>(TOPIC_INPUTS_PARAMETRIC_DATA_SOURCE)
        //显示复原按钮的条件：1.在编辑一级页。2.当前未产生编辑操作（跟大图的显示状态一样）。3.未点击过复原按钮。4.该图片存在原图数据
        val isSupportParametric = vmBus?.get<ParametricDataSource>(TOPIC_INPUTS_PARAMETRIC_DATA_SOURCE)?.isSupportParametric ?: false
        val isRecoverable = (isSupportParametric && isFirstPage && (vmBus?.isSpecificStrategy()?.not() == true))
                && operatingStack.isInitialState()
                && operatingMemo.hasRecovery.not()
                && (parametricDataSource?.isParametric == true)
        //复原按钮
        val recoverBtnFlags = state.recoverFlags.setVisible(isRecoverable).setBackgroundEnable(backgroundEnable)
        // 对比按钮 markby youpeng:还原按钮和复原按钮的显示逻辑需要添加项目化保存的feature开关控制
        val compareBtnVisible = currentStrategy.forceShowCompareBtn || (currentStrategy.isComparable && hasComparable()) || isRecoverable
        val compareBtnFlags = state.compareBtnFlags.setVisible(compareBtnVisible)
        //对比按钮显示时是否需要动画
        val compareBtnVisibleWithAnimator = currentStrategy.compareBtnVisibleWithAnimator

        //重置按钮与标题切换显示时是否需要动画
        val restoreChangeWithAnimator = currentStrategy.restoreChangeWithAnimator
        //是否应该显示标题
        val titleTextFlags = state.titleTextFlags.setVisible(currentStrategy.titleVisible)

        saveBtnFlags.setVisible(isRecoverable.not())

        val newState = state.copy(
            operatingBarType = operatingBarType,
            titleText = titleText,
            undoBtnFlags = undoBtnFlags,
            redoBtnFlags = redoBtnFlags,
            saveBtnFlags = saveBtnFlags,
            compareBtnFlags = compareBtnFlags,
            compareBtnVisibleWithAnimator = compareBtnVisibleWithAnimator,
            recoverFlags = recoverBtnFlags,
            cancelFlags = cancelBtnFlags,
            restoreChangeWithAnimator = restoreChangeWithAnimator,
            titleTextFlags = titleTextFlags,
            saveBtnText = saveBtnText
        )
        if ((state != newState) || hasModified()) {
            operatingViewStatePub?.publish(newState)
            updateModifyState()
        }
    }

    /**
     * 更新当前是否发生了修改
     */
    private fun updateModifyState() {
        val hasModified = hasModified()
        modifyStatePub?.publish(hasModified)
    }

    /**
     * 复原按钮状态变化
     * @param viewState ViewState值
     */
    private fun updateRecoverBtn(viewState: Int) {
        val state = vmBus?.get<OperatingViewState>(TOPIC_OPERATING_VIEW_STATE) ?: OperatingViewState()
        val isSupportParametric = vmBus?.get<ParametricDataSource>(TOPIC_INPUTS_PARAMETRIC_DATA_SOURCE)?.isSupportParametric ?: false

        // 编辑首页并且菜单分区时才设置按钮背景可用
        val backgroundEnable = getCurrentStrategy()?.strategyID == R.id.strategy_editing_page
        val isRecoverable = if (isSupportParametric && (vmBus?.isSpecificStrategy()?.not() == true)) viewState == View.VISIBLE else false
        //复原按钮
        val recoverBtnFlags = state.recoverFlags.setVisible(isRecoverable).setBackgroundEnable(backgroundEnable)

        val newState = state.copy(
            recoverFlags = recoverBtnFlags
        )
        operatingViewStatePub?.publish(newState)
    }

    //========================================结束按钮状态相关处理========================================

    //========================================开始水印相关的处理========================================

    /**
     * 添加一个水印
     * 该水印样式和之前去除水印的样式一样
     */
    private fun addWatermarkIfNeedWhenUndo(record: OperatingRecord, preAddEffectCallback: () -> Unit = {}) {
        var shouldAddWatermarkEffect = false
        var originRecord: WatermarkRecord? = null
        if (record is WatermarkRecord && record.isAddWatermarkRecord()) {
            val watermarkInfo = vmBus?.get<WatermarkInfo>(TOPIC_OPERATING_UPDATE_WATERMARK_EFFECT) ?: vmBus?.get<WatermarkInfo>(
                TOPIC_OPERATING_RECOVER_WATERMARK_EFFECT
            )
            GLog.d(TAG, LogFlag.DL) { "addWatermarkIfNeedWhenUndo: hasWatermark=${watermarkInfo?.hasWatermark()}" }
            // 恢复到最初的水印效果
            originRecord = WatermarkRecord.createAddWatermarkRecord(watermarkInfo, record.phoneInfo)
            if (watermarkInfo?.hasWatermark() == true) {
                shouldAddWatermarkEffect = true
            }
            updateImagePackIfNeed(originRecord)
        }
        if (shouldAddWatermarkEffect && (originRecord != null)) {
            preAddEffectCallback.invoke()
            editingVM.sessionProxy.addEffect(originRecord)
        }
    }

    private fun deleteWatermarkEffectIfNeed(record: OperatingRecord, render: Boolean = true) {
        // 显示原图需要将默认添加水印的effectUsage删掉
        if (record is WatermarkRecord) {
            val (_, resultRecord) = editingVM.sessionProxy.queryEffect {
                (it.key is WatermarkRecord) && ((it.key as WatermarkRecord).isAddWatermarkRecord())
            }
            if (resultRecord is WatermarkRecord) {
                editingVM.sessionProxy.removeEffect(resultRecord, render)
            }
        }
    }

    /**
     * 更新水印特效，如果还没有加过水印特效则不处理，用于更新水印地理位置解析之后更新水印
     */
    private fun modifyWatermarkEffectsIfNeed(watermarkInfo: WatermarkInfo?) {
        watermarkInfo?.let { info ->
            if (info.hasWatermark().not()) return
            val phoneInfo = vmBus?.get<PhoneInfo>(TOPIC_PHONE_INFO) ?: PhoneInfo()
            val newRecord = WatermarkRecord.createAddWatermarkRecord(info.copy(), phoneInfo)
            val (_, oldRecord) = editingVM.sessionProxy.queryEffect {
                (it.key is WatermarkRecord) && ((it.key as WatermarkRecord).isAddWatermarkRecord())
            }
            oldRecord?.takeIf { editingVM.sessionProxy.modifyEffect(it, newRecord) }?.let {
                editingVM.sessionProxy.removeAbandonedRecords(arrayListOf(it))
            }
        }
    }

    private fun shouldRestoreWatermark(): Boolean {
        return getCurrentStrategy()?.let {
            (it.pageLevel == STACK_INDEX_ONE) && (it.needHideWatermark)
        } ?: let {
            GLog.e(TAG, "[shouldRemoveWatermark] can't not find strategy")
            false
        }
    }

    private fun updateImagePackIfNeed(record: OperatingRecord) {
        if (record is WatermarkRecord) {
            vmBus?.get<EditingImagePack>(TOPIC_IMAGE_PACK)?.also {
                it.watermarkInfo = record.watermarkInfo

                GLog.d(TAG, "[updateImagePackIfNeed] ${record.watermarkInfo?.params}")
            }
        }
    }
    //========================================结束水印相关的处理========================================

    override fun onDestroy() {
        super.onDestroy()
        recoverPreviewManager.release()
        vmBus?.apply {
            unregisterDuplexT(TOPIC_OPERATING_COMPARE_TARGET, compareTargetRep, compareTargetPub)
            unregisterDuplexT(TOPIC_OPERATING_VIEW_STATE, viewStateRep, operatingViewStatePub)
            unregisterDuplexT(TOPIC_IMAGE_PACK, editingImagePackRep, editingImagePackPub)

            unregisterT(TOPIC_OPERATING_BACK_TYPE, backTypePub)
            unregisterT(TOPIC_WATERMARK_RECORD_RECOVER_EFFECT, watermarkRecordRecoverEffectPub)
            unregisterT(TOPIC_OPERATING_EFFECT_USAGE, effectUsagePub)
            unregisterT(TOPIC_OPERATING_MODIFY_STATE, modifyStatePub)
            unregisterT(TOPIC_OPERATING_RECORD, recordPub)
            unregisterT(TOPIC_OPERATING_OPERATING_MEMO, operatingMemoPub)
            unregisterT(TOPIC_OPERATING_SAVE_STATUS, saveStatusPub)
            unregisterT(TOPIC_INPUTS_PARAMETRIC_DATA_SOURCE, parametricDataSourcePub)
            unregister(REPLY_TOPIC_OPERATING_ACTION, actionRep)
            unregister(REPLY_TOPIC_OPERATING_ENTER_SUB, enterSubRep)
            unregister(REPLY_TOPIC_OPERATING_RECORD_STEP, recordStepRep)
            unregister(REPLY_TOPIC_OPERATING_CANCEL_EFFECT, cancelEffectRep)
            unregister(REPLY_TOPIC_OPERATING_CANCEL_EFFECT_AND_POP_RECORD, cancelEffectAndPopRecordRep)
            unregister(REPLY_TOPIC_OPERATING_UNDO_STACK_AND_EFFECT, undoStackAndEffectRep)
            unregister(REPLY_TOPIC_OPERATING_UPDATE_RECOVER_BTN, updateRecoverBtnRep)
            unregister(REPLY_TOPIC_OPERATING_FIND_RECORD, findRecordRep)
            unregister(REPLY_TOPIC_OPERATING_FIND_TOP_RECORD, findTopRecordRep)
            unregister(REPLY_TOPIC_OPERATING_FIND_SPECIFIC_RECORDS, findSpecificRecordsRep)
            unregister(REPLY_TOPIC_OPERATING_FIND_RECORD_UNTIL_NON_PARAMETRIC, findRecordUntilNonParametricRep)
            unregister(REPLY_TOPIC_OPERATING_FIND_ALL_RECORD, findAllRecord)
            unregister(REPLY_TOPIC_COLLECT_CONTENT_EDIT, processParameterRecordsRep)
            unregister(REPLY_TOPIC_OPERATING_FIND_ALL_OPERATING_RECORDS, findAllOperatingRecords)
            unregister(REPLY_TOPIC_OPERATING_FIND_RECORDS_AFTER_NONPARAMETRIC, findRecordsAfterNonparametric)
            unregister(REPLY_TOPIC_OPERATING_FIND_IMPORT_RECORDS, findImportRecord)
            unregister(TOPIC_PIPELINE_EFFECT_ONLY_UNDO, undoEffectRep)
            unsubscribe(TOPIC_PIPELINE_SESSION_CREATED, sessionCreateObserver)
            unsubscribe(TOPIC_OUTPUT_SAVE_RESULT, saveResultObserver)
            unsubscribe(TOPIC_MANAGEMENT_STRATEGY_ID, strategyIdObserver)
            unsubscribe(TOPIC_PREVIEW_CURRENT_TEXTURE, currentTextureObserver)
            unsubscribe(TOPIC_INPUTS_DATA_SOURCE, inputSourceObserver)
            unsubscribe(TOPIC_PREVIEW_LONG_PRESS_STATE, previewLongPressStateObserver)
            unsubscribe(TOPIC_OPERATING_RECOVER_WATERMARK_EFFECT, recoverWatermarkEffectObserver)
            unsubscribe(TOPIC_OPERATING_UPDATE_WATERMARK_EFFECT, updateWatermarkEffectObserver)
        }
    }

    /**
     * 源图信息（这里的源图指的是 "需要应用效果的底图"，是一个抽象的概念。
     * 如果是ipu滤镜的场景，那么它内部的信息对应的就是扩展数据中的src图及相关信息
     * 如果是参数化的场景，那么它内部的信息对应的是内容图（无水印）及相关信息）
     * @param bitmap 主图的图像信息
     * @param hdrImageContent 增益图的图像信息
     * @param responseCode 结果码
     */
    data class SrcFileBitmapInfo(
        var bitmap: Bitmap,
        var hdrImageContent: IHdrImageContent?,
        val responseCode: Int
    ) {

        /**
         * 用bitmap的gainmap来更新hdr信息
         * 目前用到的场景：滤镜在解析到拓展数据中的src图之后，需要用src图的gainmap和hdr信息。业务场景是依赖这个封装对象的属性信息，所以这里需要更新一下数据
         */
        fun updateFrameWithGainmap(editingVM: EditingVM) {
            var updateHdrImageContent = hdrImageContent
            if (ApiLevelUtil.isAtLeastAndroidU()) {
                val gainMap = bitmap.gainmap.getOrLog()
                gainMap?.let {
                    val uhdrMetadataPack = UHdrMetadataPack(gainMap.toUltraHdrInfo())
                    val hdrImageContent = HdrImageDrawable(gainMap.gainmapContents, uhdrMetadataPack)
                    updateHdrImageContent = hdrImageContent
                } ?: let {
                    // 人像模式：如果src图没有gainmap 通过扩展信息获取 src图gainmap
                    if ((editingVM.sessionProxy.getTagFlag() and FLAG_PORTRAIT_BLUR) == FLAG_PORTRAIT_BLUR) {
                        val srcGainmapByteArray = editingVM.sessionProxy.getMetadata(
                            MetadataType.EXTENDED,
                            EXTEND_KEY_ORIGIN_HDR_LINEAR_MASK
                        ) as? ByteArray
                        srcGainmapByteArray?.let { maskData ->
                            val options = BitmapFactory.Options().apply {
                                // Local HDR图片中目前存放的灰图编码为单通道8bit，解码用该config。
                                inPreferredConfig = Bitmap.Config.ALPHA_8
                            }
                            val srcGainmap = BitmapFactory.decodeByteArray(maskData, 0, maskData.size, options)
                            BitmapUtils.strideAligned(bitmap)
                            if (srcGainmap == null) return

                            // localHdr 版本大于4.0 srcGainmap 为uhdr 的灰图
                            val localHdrVersion = getLocalHdrDataVersion(editingVM)
                            if (localHdrVersion > MathUtils.FOUR_F) {
                                val ultraHdrInfo = getUltraHdrInfo(editingVM)
                                ultraHdrInfo?.let { updateHdrImageContent = HdrImageDrawable(srcGainmap, UHdrMetadataPack(it)) }
                            } else {
                                val metaByteArray = getLocalHdrMetaData(editingVM)
                                metaByteArray?.let { updateHdrImageContent = HdrImageDrawable(srcGainmap, LHdrMetadataPack(metaByteArray)) }
                            }
                        }
                    }
                }
            }
            hdrImageContent = updateHdrImageContent
        }

        /**
         * 获取LocalHdrMetadata的版本信息
         */
        private fun getLocalHdrDataVersion(editingVM: EditingVM): Float {
           return editingVM.sessionProxy.getMetadataStruct<LocalHdrInfoStruct>(
               MetadataType.EXTENDED,
               EXTEND_KEY_LOCAL_HDR_META_DATA
           )?.toData()?.version ?: MathUtils.ZERO_F
        }

        /**
         * 获取LocalHdrMetadata
         */
        private fun getLocalHdrMetaData(editingVM: EditingVM): ByteArray? {
            return editingVM.sessionProxy.getMetadata(MetadataType.EXTENDED, EXTEND_KEY_LOCAL_HDR_META_DATA) as? ByteArray
        }

        /**
         * 获取UltraHdrInfo
         */
        private fun getUltraHdrInfo(editingVM: EditingVM): UltraHdrInfo? {
            return editingVM.sessionProxy.getMetadata(MetadataType.EXTENDED, EXTEND_KEY_ULTRA_HDR_INFO)?.let { data ->
                if (data is Pair<*, *> && data.second is UltraHdrInfo) {
                    data.second as UltraHdrInfo
                } else {
                    GLog.w(TAG, LogFlag.DL) { "[getUHdrInfo] Invalid UHDR data format: ${data::class}" }
                    null
                }
            }
        }
    }

    internal companion object {
        const val TAG = "OperatingVM"

        /**
         * 获取操作栈所有操作记录
         */
        const val ALL_OPERATING_RECORDS = "all_operating_records"
    }
}

/**
 * 上级编辑页, 监听下次级页进出动作.
 *
 * 用途案例:
 *   比如从裁剪页 进超清像素场景. 裁剪页需要知道超清像素页的进出状态.
 */
enum class SubPageState {
    /**
     * 进入次级页
     */
    ENTER,

    /**
     * 次级页取消退出
     */
    CANCEL_EXIT,

    /**
     * 次级页完成退出
     */
    CONFIRM_EXIT
}