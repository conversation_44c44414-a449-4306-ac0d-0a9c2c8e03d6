/********************************************************************************
 ** Copyright (C), 2024-2034, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OliveSection
 ** Description: 实况编辑切片
 ** Version: 1.0
 ** Date : 2024/5/20
 ** Author: lizhi
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lizhi                           2024/5/20    1.0          first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.olive

import android.content.res.Resources
import android.graphics.Bitmap
import android.graphics.RectF
import android.view.View
import android.widget.RelativeLayout
import androidx.lifecycle.lifecycleScope
import com.oplus.gallery.addon.os.VibratorUtils
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.business_lib.template.editor.EditorAnimationUtils
import com.oplus.gallery.business_lib.template.editor.EditorAppUiConfig
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig
import com.oplus.gallery.foundation.archv2.ISectionBus
import com.oplus.gallery.foundation.archv2.bus.NotifierChannel
import com.oplus.gallery.foundation.archv2.section.SectionSpec
import com.oplus.gallery.foundation.ui.notification.NotificationAction
import com.oplus.gallery.foundation.ui.widget.BlockingSnackBar
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.foundation.util.ext.minLen
import com.oplus.gallery.foundation.util.ext.scaleBitmap
import com.oplus.gallery.foundation.util.math.MathUtils
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.photoeditor.EditingFragment
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.BaseEditingSection
import com.oplus.gallery.photoeditor.editingvvm.EditingVM
import com.oplus.gallery.photoeditor.editingvvm.TObserver
import com.oplus.gallery.photoeditor.editingvvm.TopicID
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_INPUTS_IS_SUPPORT_HDR_EDIT
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Notification.TOPIC_NOTIFICATION_ACTION
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OLiveVideoEdit.REPLY_TOPIC_CHECK_PLAY_VIDEO_FPS_PROXY
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OLiveVideoEdit.TOPIC_PREVIEW_OLIVE_VIDEO_ENGINE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Olive.REPLY_TOPIC_CUSTOMIZE_HDR_DRAWABLE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Olive.REPLY_TOPIC_OLIVE_LOAD_THUMBNAIL_LIST
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Olive.REPLY_TOPIC_OLIVE_SUBMIT_COVER_TO_PIPELINE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Olive.REPLY_TOPIC_OLIVE_UPDATE_FIXED_SLIDING_TIME_STAMP
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Olive.REPLY_TOPIC_OLIVE_UPDATE_LEFT_HANDLE_TIMEMS_WITH_PERCENT
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Olive.REPLY_TOPIC_OLIVE_UPDATE_RIGHT_HANDLE_TIMEMS_WITH_PERCENT
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Olive.REPLY_TOPIC_OLIVE_UPDATE_SLIDING_TIMEMS_WITH_PERCENT
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Olive.REPLY_TOPIC_OLIVE_UPDATE_UI_CONFIG
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Olive.TOPIC_OLIVE_DOWNLOAD_XDR_COMPONENT_EXECUTED
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Olive.TOPIC_OLIVE_EXPORT_PHOTO_DATA
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Olive.TOPIC_OLIVE_IS_HDR_VIDEO
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Olive.TOPIC_OLIVE_UI_CONFIG
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Olive.TOPIC_OLIVE_UI_STATE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Output.REPLY_TOPIC_LOOK_EXPORT_FILE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.REPLY_TOPIC_PREVIEW_SHOW_OLIVE_VIDEO
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_PREVIEW_ANIMATION_PROPERTIES
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_PREVIEW_LONG_PRESS_STATE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_PREVIEW_SINGLE_TAP_STATE
import com.oplus.gallery.photoeditor.editingvvm.get
import com.oplus.gallery.photoeditor.editingvvm.notifyOnce
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.engine.IOliveVideoEngine
import com.oplus.gallery.photoeditor.editingvvm.olive.engine.processor.proxdr.plugins.checker.OliveCoverProXDRComponentCheckStrategy
import com.oplus.gallery.photoeditor.editingvvm.olive.engine.processor.proxdr.plugins.dwonload.OliveCoverProXDRModelDownloader
import com.oplus.gallery.photoeditor.editingvvm.olive.widget.OLiveEditorUnSupportTips
import com.oplus.gallery.photoeditor.editingvvm.olive.widget.OLiveMaskView
import com.oplus.gallery.photoeditor.editingvvm.olive.widget.OLiveRecommendLayout
import com.oplus.gallery.photoeditor.editingvvm.olive.widget.OLiveStatusLayout
import com.oplus.gallery.photoeditor.editingvvm.olive.widget.OLiveStatusListener
import com.oplus.gallery.photoeditor.editingvvm.olive.widget.OLiveThumbnailLayoutAdapter
import com.oplus.gallery.photoeditor.editingvvm.olive.widget.OLiveThumbnailSeekBar
import com.oplus.gallery.photoeditor.editingvvm.olive.widget.OLiveTrimPositionChangeListener
import com.oplus.gallery.photoeditor.editingvvm.operating.EditingImagePack
import com.oplus.gallery.photoeditor.editingvvm.output.SaveData
import com.oplus.gallery.photoeditor.editingvvm.output.SaveData.Companion.KEY_HDR_IMAGE_CONTENT
import com.oplus.gallery.photoeditor.editingvvm.preview.EditingSurfaceView
import com.oplus.gallery.photoeditor.editingvvm.preview.LongPressEvent
import com.oplus.gallery.photoeditor.editingvvm.preview.PreviewAnimationProperties
import com.oplus.gallery.photoeditor.editingvvm.preview.SingleTapEvent
import com.oplus.gallery.photoeditor.editingvvm.subscribeDuplexT
import com.oplus.gallery.photoeditor.editingvvm.subscribeR
import com.oplus.gallery.photoeditor.editingvvm.subscribeT
import com.oplus.gallery.photoeditor.editingvvm.unsubscribeDuplexT
import com.oplus.gallery.photoeditor.editingvvm.unsubscribeR
import com.oplus.gallery.photoeditor.track.OLiveEditorTracker
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.standard_lib.codec.drawable.HdrImageType
import com.oplus.gallery.standard_lib.codec.drawable.IHdrImageContent
import com.oplus.gallery.standard_lib.codec.drawable.UHdrMetadataPack
import com.oplus.gallery.standard_lib.codec.drawable.isHdrContentValid
import com.oplus.gallery.standard_lib.ui.util.DoubleClickUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.math.min

/**
 * 实况编辑页切片
 * @param sectionBus 总线
 */
internal class OliveSection(
    sectionBus: ISectionBus<EditingFragment, EditingVM>,
) : BaseEditingSection(sectionBus) {

    /**
     * 预览的SurfaceView，用于截图绘制SeekBar上的图片
     */
    private val glRootView: EditingSurfaceView by lazy { sectionBus.rootView.findViewById(R.id.picture_gl_root_view) }

    /**
     * glRootView 上绘制内容的区域
     */
    private val previewContentRect: RectF
        get() = vBus.get<PreviewAnimationProperties>(TOPIC_PREVIEW_ANIMATION_PROPERTIES)?.contentDrawingOutBound?.current ?: RectF(
            0F, 0F, glRootView.width.toFloat(), glRootView.height.toFloat()
        )

    /**
     * OliveView预览视图的控制器
     */
    private val olivePreviewController by lazy {
        OlivePreviewController(
            vBus.get<IOliveVideoEngine>(TOPIC_PREVIEW_OLIVE_VIDEO_ENGINE),
            showOLiveVideoNtf,
        )
    }

    /**
     * 设置封面的root布局
     */
    private val toolbarLayout by lazy {
        val baseUIView = sectionBus.rootView.findViewById<RelativeLayout>(R.id.photo_editor_ui_framework)
        layoutInflater.inflate(R.layout.picture3d_editor_olive_toolbar, baseUIView, false)
    }

    /**
     * 缩图轴区域根布局
     */
    private val toolbarContainerView by lazy {
        toolbarLayout.findViewById<RelativeLayout>(R.id.olive_edit_toolbar)
    }

    /**
     * 时间戳缩图布局
     */
    private val oliveThumbnailSeekBar by lazy {
        toolbarLayout.findViewById<OLiveThumbnailSeekBar>(R.id.olive_edit_thumbnail_container).apply {
            setOLiveThumbnailAdapter(thumbnailLayoutAdapter)
        }
    }

    /**
     * 圆点标记布局
     */
    private val oliveRecommendLayout by lazy {
        toolbarLayout.findViewById<OLiveRecommendLayout>(R.id.olive_edit_olive_recommend_container)
    }

    /**
     * 时间轴蒙层组件
     */
    private val oliveMaskView by lazy {
        toolbarLayout.findViewById<OLiveMaskView>(R.id.olive_edit_thumbnail_mask)
    }

    /**
     * 开关声音和实况的view
     */
    private var oliveStatusView: OLiveStatusLayout? = null

    /**
     * olive状态编辑布局资源id
     */
    private var oliveStatusLayoutId: Int = Resources.ID_NULL

    // HDR弹框条件不满足时，无需弹框
    private val unSupportTips by lazy { OLiveEditorUnSupportTips(activity) }

    private val oliveCoverProXDRComponentCheckStrategy: OliveCoverProXDRComponentCheckStrategy by lazy {
        OliveCoverProXDRComponentCheckStrategy(context = activity.applicationContext) { action ->
            GLog.d(OliveVM.TAG, LogFlag.DL) { "[checkSupportability] action = $action" }
            activity.lifecycleScope.launch(Dispatchers.UI) {
                vBus.notifyOnce(TOPIC_NOTIFICATION_ACTION, action)
            }
        }
    }

    /**
     * 视频 seekTo 到指定位置后，判断是否需要同步刷新选帧框图片
     */
    private var needUpdateSelectBitmap = false

    /**
     * 视频 seekTo 到指定位置后，判断预览图是否需要切换为视频模式
     */
    private var pendingSwitchToVideoMode = false

    /**
     * 选择框是否为自动滚动到指定位置场景，非手动点击
     * 场景：当设置封面气泡显示时，长按大图后，需要滚动到封面位置
     */
    private var isSelectAutoSeeking = false

    /**
     * 时间戳缩图布局的适配器
     */
    private val thumbnailLayoutAdapter = OLiveThumbnailLayoutAdapter(activity)

    /**
     * 封面优化弹框是否显示
     */
    private var isEnhanceBitmapLoadingShow = false

    /**
     * 是否正在切换封面帧，用于非封面帧位置时长按不播放实况
     * 默认为false
     */
    private var isTimelineSeekNewImage = false

    /**
     * tips提示弹窗
     */
    private var immediateLookTipsSnackBar: BlockingSnackBar? = null

    /**
     * 点击查看按钮监听
     */
    private val lookSnackBarClickListener = View.OnClickListener {
        if (DoubleClickUtils.isFastDoubleClick()) {
            return@OnClickListener
        }
        immediateLookTipsSnackBar?.dismiss()
        /*
            只有当获取到正确的SaveData信息时才通知执行退出跳转大图，
            如果SaveData为空对象会导致跳转异常
         */
        vBus.get<SaveData>(TOPIC_OLIVE_EXPORT_PHOTO_DATA)?.let {
            /*
              获取导出照片的hdr增益图，并通知更新自定义增益图
              未获取到增益图时(未下载ProXDR算法)传空字符串，用于通知清除原来进入编辑时保存的大图增益图
            */
            val hdrImageDrawable = it.find<IHdrImageContent>(KEY_HDR_IMAGE_CONTENT, TAG) ?: ""
            vBus.notifyOnce(REPLY_TOPIC_CUSTOMIZE_HDR_DRAWABLE, hdrImageDrawable)
            vBus.notifyOnce(REPLY_TOPIC_LOOK_EXPORT_FILE, it)
        }
    }

    /**
     * 实况和声音开关监听器
     */
    private val onOliveStatusListener = object : OLiveStatusListener {
        override fun onSwitchChange(onEnableChange: Boolean) {
            changeOliveSwitchStatus(onEnableChange)
        }

        override fun onSoundChange(onEnableChange: Boolean) {
            changeOliveSlowMotion(onEnableChange)
//            changeOliveSoundStatus(onEnableChange)
        }

        override fun onCoverChange() {
            checkHdrAlgorithm(operateType = OperateType.COVER)
        }

        override fun onExportPhoto() {
            checkHdrAlgorithm(operateType = OperateType.EXPORT)
        }
    }

    private var onVideoSeekingListener: ((positionUs: Long) -> Unit)? = null

    /**
     * 视频seek完成通知
     */
    private fun onSeekComplete(positionUs: Long) {
        GLog.d(TAG, LogFlag.DL) { "[onSeekComplete] positionUs: $positionUs, thread=${Thread.currentThread().name}" }
        if (pendingSwitchToVideoMode) {
            olivePreviewController.changedOliveViewShow(false)
            pendingSwitchToVideoMode = false
        }
    }

    private fun onSeekCapturedFrame(positionUs: Long, bitmap: Bitmap?) {
        val seekBoxFrameBitmap = bitmap ?: run {
            GLog.e(TAG, LogFlag.DL) { "[onSeekCapturedFrame] bitmap = null" }
            return
        }

        val scale = oliveThumbnailSeekBar.getTrimViewSize().minLen() / min(previewContentRect.width(), previewContentRect.height())
        val seekBoxBitmap = seekBoxFrameBitmap.scaleBitmap(scale, true) ?: run {
            GLog.d(TAG, LogFlag.DL) { "[onSeekCapturedFrame] scaleBitmap is null, return seekBoxFrameBitmap" }
            seekBoxFrameBitmap
        }

        lifecycleScope.launch(Dispatchers.UI) {
            oliveThumbnailSeekBar.setCurrentSelectBitmap(seekBoxBitmap)
        }
    }

    /**
     * 时间轴的事件监听器
     */
    private val trimPositionChangeListener = object : OLiveTrimPositionChangeListener {
        override fun onSelectSeekStart() {
            GLog.d(TAG, LogFlag.DL) { "[onSelectSeekStart]" }
            olivePreviewController.pauseVideo()
            isTimelineSeekNewImage = false
        }

        override fun onSelectSeeking(selectPosPercent: Float) {
            vBus.notifyOnce(REPLY_TOPIC_OLIVE_UPDATE_SLIDING_TIMEMS_WITH_PERCENT, selectPosPercent, false)
        }

        override fun onSelectSeekComplete(selectPosPercent: Float) {
            GLog.d(TAG, LogFlag.DL) {
                "[onSelectSeekComplete] selectPosPercent:$selectPosPercent, isSelectAutoSeeking:$isSelectAutoSeeking"
            }
            if (selectPosPercent.isNaN()) {
                // 规避下低概率bug:7815909
                GLog.e(TAG, LogFlag.DL) { "[onSelectSeekComplete] selectPosPercent is NaN. return" }
                return
            }
            vBus.notifyOnce(REPLY_TOPIC_OLIVE_UPDATE_SLIDING_TIMEMS_WITH_PERCENT, selectPosPercent, false)
            if (!isSelectAutoSeeking) {
                // HDR弹框如果还是显示状态，则封面帧还未更新，无需刷新
                if (unSupportTips.isShowing().not()) {
                    // 更新设为封面View位置
                    updateRecommendCoverTipsPosition(needUpdateSetCoverTips = true)
                    // 如果非当前封面帧位置，则将设为封面位置View设置为可见，并且需要动画
                    vBus.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE)?.also {
                        GLog.d(TAG, LogFlag.DL) {
                            "[onSelectSeekComplete] bean: slidingTimeMs: ${it.slidingTimeMs}, coverTimeMs: ${it.coverTimeMs}"
                        }
                        // 当前选帧框位置如果不是封面帧位置，说明正在准备切换封面
                        isTimelineSeekNewImage = (it.slidingTimeMs != it.coverTimeMs)
                        //快速滑动造成选择框和把手的触摸差，在隐藏圆点后在封面位置不变的情况下没有显示原封面圆点
                        if (it.slidingTimeMs == it.coverTimeMs) oliveRecommendLayout.setRecommendViewVisible()
                    }
                }
            } else {
                isSelectAutoSeeking = false
            }
        }

        override fun onTrimSeekStart() {
            olivePreviewController.pauseVideo()
            isTimelineSeekNewImage = false
            oliveRecommendLayout.visibility = View.INVISIBLE
            GLog.d(TAG, LogFlag.DL) { "[onTrimSeekStart]: hide olive recommend layout" }
        }

        override fun onTrimSeeking(trimPosPercent: Float, isLeftTrim: Boolean) {
            val bean = vBus.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE) ?: OliveUiBean()
            val trimTimeMs = bean.totalDuration * trimPosPercent
            val isLeftBeyond = isLeftTrim && (trimTimeMs > bean.coverTimeMs)
            val isRightBeyond = isLeftTrim.not() && (trimTimeMs < bean.coverTimeMs)
            if (isLeftBeyond || isRightBeyond) {
                vBus.notifyOnce(REPLY_TOPIC_OLIVE_UPDATE_SLIDING_TIMEMS_WITH_PERCENT, trimPosPercent, false)
            }
        }

        override fun onTrimLeftComplete(leftTrimPosPercent: Float, selectPosPercent: Float, isCurrentSelectPosChange: Boolean) {
            activity.lifecycleScope.launch(Dispatchers.Main) {
                updateTrimComplete(
                    leftTrimPosPercent,
                    true,
                    selectPosPercent,
                    isCurrentSelectPosChange
                )
            }
        }

        override fun onTrimRightComplete(rightTrimPosPercent: Float, selectPosPercent: Float, isCurrentSelectPosChange: Boolean) {
            activity.lifecycleScope.launch(Dispatchers.Main) {
                updateTrimComplete(
                    rightTrimPosPercent,
                    false,
                    selectPosPercent,
                    isCurrentSelectPosChange
                )
            }
        }
    }


    /**
     * 用于把手左右滑动停止之后的逻辑更新
     */
    private fun updateTrimComplete(
        trimPosPercent: Float,
        isTrimLeft: Boolean,
        selectPosPercent: Float,
        isCurrentSelectPosChange: Boolean,
    ) {
        if (selectPosPercent.isNaN() || trimPosPercent.isNaN()) {
            GLog.e(TAG, LogFlag.DL) { "[updateTrimComplete] selectPosPercent is NaN. return" }
            return
        }
        val bean = vBus.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE) ?: OliveUiBean()
        val trimPosTimeMs = bean.totalDuration * trimPosPercent
        //左侧越过原封面
        val isLeftBeyond = isTrimLeft && (trimPosTimeMs > bean.coverTimeMs)
        //右侧越过原封面
        val isRightBeyond = isTrimLeft.not() && (trimPosTimeMs < bean.coverTimeMs)
        if (isLeftBeyond || isRightBeyond || isCurrentSelectPosChange) {
            /**
             * 更新封面圆点时，原封面圆点会在把手抬起的瞬间属性可见，
             * 更新新封面圆点后原封面园点不可见，造成视觉封面圆点的跳转显示
             * 只有在把手的位置越过封面的位置抬手时才需要隐藏封面圆点
             */
            oliveRecommendLayout.hideCoverView()
            // 通知seekbar position变更 --等同于seekbar seek动作
            vBus.notifyOnce(REPLY_TOPIC_OLIVE_UPDATE_SLIDING_TIMEMS_WITH_PERCENT, selectPosPercent, true)
            vBus.notifyOnce(TopicID.Preview.TOPIC_PREVIEW_IS_TOUCHABLE, false)
            checkHdrAlgorithm(operateType = OperateType.COVER, blockOperationsFromUser = false)
        }
        val notifyInfo: String = if (isTrimLeft) {
            REPLY_TOPIC_OLIVE_UPDATE_LEFT_HANDLE_TIMEMS_WITH_PERCENT
        } else {
            REPLY_TOPIC_OLIVE_UPDATE_RIGHT_HANDLE_TIMEMS_WITH_PERCENT
        }
        vBus.notifyOnce(notifyInfo, trimPosPercent)
        // seekbar位置需要与slidingTimeMs保持一致和封面位置coverTimeMs没有直接关系
        oliveThumbnailSeekBar.startSelectMove(bean.slidingTimeMs)
        oliveThumbnailSeekBar.invalidate()
        oliveRecommendLayout.visibility = View.VISIBLE
        GLog.d(TAG, LogFlag.DL) { "[updateTrimComplete]: show olive recommend layout" }
    }

    /**
     * 用于通知 VM ，做UI数据更新
     */
    private var oliveNtf: NotifierChannel<Unit>? = null

    /**
     * 通知PreviewVM，切换Olive视频展示
     */
    private var showOLiveVideoNtf: NotifierChannel<Unit>? = null

    /**
     * 用于通知 VM ，UI配置更新
     */
    private var uiConfigChangeNtf: NotifierChannel<Unit>? = null

    /**
     * 用于通知 VM ，封面图到管线
     */
    private var submitCoverToPipelineNtf: NotifierChannel<Unit>? = null

    /**
     * 用于接收 VM 的消息，做UI刷新
     */
    private val oliveObserver: TObserver<OliveUiBean> = {
        when (it.id) {
            OliveBeanId.RECOVER -> onInitDataFinished(it)

            OliveBeanId.COVER -> {
                showCoverBitmap(it.coverTimeMs, it.coverBitmap, it.coverGainMap)
            }

            OliveBeanId.SLIDING -> {
                showSlidingBitmap(it)
            }

            OliveBeanId.THUMBNAIL_LIST -> onThumbnailListChange(it.thumbnailList)

            OliveBeanId.ADSORBENT_LIST -> onAdsorbentListChange(it.adsorbentList)

            OliveBeanId.TOTAL_DURATION -> loadThumbnailList()

            OliveBeanId.OLIVE_EXPORT_IMAGE -> showImmediateLookTips()

            OliveBeanId.OLIVE_SLOW_MOTION -> onSlowMotionChange()

            else -> GLog.w(TAG, LogFlag.DL) { "[oliveObserver] do nothing. id: ${it.id}" }
        }
    }

    private val oliveUiConfigObserver: TObserver<IOLiveEditViewUIConfig> = {
        onOliveUiConfigChanged(it)
    }

    private val previewLongPressStateObserver: TObserver<LongPressEvent> = {
        GLog.i(TAG, LogFlag.DL) { "previewLongPressStateObserver $it" }
        when (it) {
            is LongPressEvent.Begin -> {
                playVideoIfNeed(it.point.x.toInt(), it.point.y.toInt())
                OLiveEditorTracker.trackOLivePlayEvent(OLiveEditorTracker.OLivePlayType.LONG_PRESS_PHOTO_EDIT_SECOND_LEVEL)
            }

            is LongPressEvent.Finish -> {
                startSelectMoveAnimator()
                // 通知校验预览是否更新或还原帧率和规格
                vBus.notifyOnce(REPLY_TOPIC_CHECK_PLAY_VIDEO_FPS_PROXY, false)
            }
        }
    }

    private val notificationActionChangeObserver: TObserver<NotificationAction> = {
        if (it is NotificationAction.LoadingDialogAction) {
            isEnhanceBitmapLoadingShow = it.isShowing
        }
    }

    private val previewSingleTapStateObserver: TObserver<SingleTapEvent> = {
        GLog.i(TAG, LogFlag.DL) { "previewSingleTapStateObserver $it" }
        when (it) {
            is SingleTapEvent.SingleTapUp -> startSelectMoveAnimator()

            else -> {}
        }
    }

    override fun onCreate() {
        super.onCreate()
        GTrace.traceBegin("$TAG.onCreate")
        subscribeVM()
        initView()
        //初始化实况图片视频资源
        lifecycleScope.launch {
            withContext(Dispatchers.IO) { olivePreviewController.initSourceMetaData() }
            // 这里是加载缩略图的入口，ID名字起的有点奇怪
            oliveNtf?.notify(OliveUiBean(id = OliveBeanId.TOTAL_DURATION, totalDuration = olivePreviewController.getDuration()))
        }
        oliveNtf?.notify(OliveUiBean(id = OliveBeanId.RECOVER))
        GTrace.traceEnd()
    }

    override fun onDestroy() {
        super.onDestroy()
        unSupportTips.release()
        unsubscribeVM()

        /**
         * stopVideo会触发VideoSeekingListener回调，导致低概率bug: 8589771
         * 所以要在stopVideo之前置空
         */
        olivePreviewController.setVideoSeekingListener(null)
        olivePreviewController.setVideoSeekCapturedFrameListener(null)
        // 释放player
        olivePreviewController.stopVideo()

        // 释放缩图
        thumbnailLayoutAdapter.release()

        oliveStatusView?.setOLiveStatusChangeListener(null)
        // 移除工具栏
        removeExtraViewFromBaseUIView(toolbarLayout)
        removeContentViewIfNeed()
        oliveThumbnailSeekBar.setTrimPositionChangeListener(null)
        immediateLookTipsSnackBar?.let {
            if (it.isShown) it.dismiss()
        }
        onVideoSeekingListener = null
    }

    private fun subscribeVM() {
        oliveNtf = vBus.subscribeDuplexT(TOPIC_OLIVE_UI_STATE, oliveObserver)
        showOLiveVideoNtf = vBus.subscribeR(REPLY_TOPIC_PREVIEW_SHOW_OLIVE_VIDEO)
        uiConfigChangeNtf = vBus.subscribeR(REPLY_TOPIC_OLIVE_UPDATE_UI_CONFIG)
        submitCoverToPipelineNtf = vBus.subscribeR(REPLY_TOPIC_OLIVE_SUBMIT_COVER_TO_PIPELINE)
        vBus.subscribeT(TOPIC_OLIVE_UI_CONFIG, oliveUiConfigObserver)
        vBus.subscribeT(TOPIC_PREVIEW_LONG_PRESS_STATE, previewLongPressStateObserver)
        vBus.subscribeT(TOPIC_PREVIEW_SINGLE_TAP_STATE, previewSingleTapStateObserver)
        vBus.subscribeT(TOPIC_NOTIFICATION_ACTION, notificationActionChangeObserver)
    }

    private fun unsubscribeVM() {
        vBus.unsubscribeDuplexT(TOPIC_OLIVE_UI_STATE, oliveNtf, oliveObserver)
        vBus.unsubscribeR(REPLY_TOPIC_PREVIEW_SHOW_OLIVE_VIDEO, showOLiveVideoNtf)
        vBus.unsubscribeR(REPLY_TOPIC_OLIVE_UPDATE_UI_CONFIG, uiConfigChangeNtf)
        vBus.unsubscribeR(REPLY_TOPIC_OLIVE_SUBMIT_COVER_TO_PIPELINE, submitCoverToPipelineNtf)
        vBus.unsubscribe(TOPIC_OLIVE_UI_CONFIG, oliveUiConfigObserver)
        vBus.unsubscribe(TOPIC_PREVIEW_LONG_PRESS_STATE, previewLongPressStateObserver)
        vBus.unsubscribe(TOPIC_PREVIEW_SINGLE_TAP_STATE, previewSingleTapStateObserver)
        vBus.unsubscribe(TOPIC_NOTIFICATION_ACTION, notificationActionChangeObserver)
    }

    private fun initView() {
        onVideoSeekingListener = { positionUs ->
            onSeekComplete(positionUs)
        }
        onVideoSeekingListener?.also {
            olivePreviewController.setVideoSeekingListener(it)
        }

        olivePreviewController.setVideoSeekCapturedFrameListener(::onSeekCapturedFrame)

        addToolLayout()
        oliveThumbnailSeekBar.setTrimPositionChangeListener(trimPositionChangeListener)
    }

    /**
     * VM 中 REPLY_TOPIC_OLIVE_INIT_DATA执行完回调
     */
    private fun onInitDataFinished(uiBean: OliveUiBean) {
        GLog.i(TAG, LogFlag.DL) { "[onInitDataFinished] uiBean: $uiBean" }
        oliveThumbnailSeekBar.setAdsorbentLengthList(uiBean.adsorbentList)

        val bean = vBus.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE) ?: kotlin.run {
            GLog.w(TAG, LogFlag.DL) { "onInitDataFinished, ui bean is null, return" }
            return
        }
        changeToImageMode(bean.coverTimeMs)
    }

    /**
     * 转换到图片显示模式
     */
    private fun changeToImageMode(coverTimeMs: Long) {
        olivePreviewController.changedOliveViewShow(true)
        pendingSwitchToVideoMode = false

        // 显示封面高清图时，隐藏的视频组件也需要更新到对应位置
        needUpdateSelectBitmap = true
        olivePreviewController.seekTo(coverTimeMs)
    }

    /**
     * 添加自定义的工具栏组件
     */
    private fun addToolLayout() {
        toolbarLayout.apply {
            (layoutParams as? RelativeLayout.LayoutParams)?.addRule(RelativeLayout.ABOVE, R.id.title_bar_container)
            addExtraViewToBaseUIView(this)
            EditorAnimationUtils.startAlphaAnimation(
                this, true, 0, ROTATE_CLIP_FRAME_VIEW_ADD_ANIMATION_DURATION_TIME, null
            )
        }
    }

    /**
     * 播放olive视频
     * 触发时机：
     * 1，长按触发
     *
     * @param eventX 触摸位置X坐标
     * @param eventY 触摸位置Y坐标
     */
    private fun playVideoIfNeed(eventX: Int, eventY: Int) {
        /*
            长按不执行播放的三个场景：
            1.选择切换封面帧时
            2.在封面优化过程中时不播放
            3.实况开关关闭时
         */
        if (isTimelineSeekNewImage) {
            return GLog.d(TAG, LogFlag.DL) { "[playVideoIfNeed] isCoverPosition is false, return" }
        }

        vBus.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE)?.also { bean ->
            //在实况开关关闭时不响应长按播放视频
            if (bean.oliveEnable.not()) {
                return
            }
            //在封面优化过程中不响应长按播放视频
            if (bean.isCoverProcessing.not()) {
                // 通知校验预览是否更新或还原帧率和规格
                vBus.notifyOnce(REPLY_TOPIC_CHECK_PLAY_VIDEO_FPS_PROXY, true)
                //设置播放器音效状态
                olivePreviewController.setMutePlayer(bean.oliveSoundEnable.not())
                VibratorUtils.vibrate(activity, VibratorUtils.EffectType.VIBRATE_TYPE_WEAK)
                olivePreviewController.playVideo(bean.startTime, bean.endTime)
            }
        }
    }

    /**
     * 选择框移动到封面位置，显示封面
     * 当显示设置封面弹框时，长按大图不会播放，而是执行此处，显示封面
     */
    private fun startSelectMoveAnimator() {
        olivePreviewController.pauseVideo()
        isTimelineSeekNewImage = false
        vBus.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE)?.also { bean ->
            if ((bean.slidingTimeMs == bean.coverTimeMs) && (bean.isCoverProcessing.not())) {
                GLog.d(TAG, LogFlag.DL) { "[startSelectMoveAnimator] is already in cover, no need animator" }
                changeToImageMode(bean.coverTimeMs)
                return
            }
            isSelectAutoSeeking = true
            oliveThumbnailSeekBar.startSelectMoveAnimator(bean.coverTimeMs)
        }
    }

    override fun onUiConfigChanged(baseActivity: BaseActivity, layoutId: Int, config: EditorAppUiConfig) {
        super.onUiConfigChanged(baseActivity, layoutId, config)
        oliveStatusLayoutId = layoutId
        uiConfigChangeNtf?.notify(config.appUiConfig)
    }

    /**
     * olive定制UI配置发生变化时回调
     */
    private fun onOliveUiConfigChanged(uiConfig: IOLiveEditViewUIConfig) {
        val thumbnailScale = uiConfig.getThumbnailContainerScale()
        val viewWidth = uiConfig.getThumbnailContainerViewWidth(activity.resources)
        GLog.d(TAG, LogFlag.DL) {
            "[onOliveUIConfigChange] uiConfig: $uiConfig, thumbnailScale: $thumbnailScale, viewWidth: $viewWidth"
        }
        updateToolBarLayoutWidth(viewWidth)
        oliveThumbnailSeekBar.updateLayoutSize(thumbnailScale)
        oliveThumbnailSeekBar.updateLayoutPosition(thumbnailScale)
        oliveMaskView.updateLayoutSize(thumbnailScale)
        oliveMaskView.updateLayoutPosition(thumbnailScale)
        // 重新加载缩图数据
        loadThumbnailList()
        reInitView()
    }

    /**
     * 更新缩图轴区域布局的宽度
     * @param viewWidth
     */
    private fun updateToolBarLayoutWidth(viewWidth: Int) {
        val toolBarContainer = toolbarContainerView as RelativeLayout
        val layoutParams = toolBarContainer.layoutParams
        layoutParams.width = viewWidth
        toolBarContainer.requestLayout()
    }

    /**
     * 重新加载资源状态编辑布局
     */
    private fun reInitView() {
        removeContentViewIfNeed()
        sectionContentView = layoutInflater.inflate(oliveStatusLayoutId, sectionContainerView, false)
        sectionContentView?.let {
            sectionContainerView?.addView(it)
            initOliveStatusView()
        }
    }

    /**
     * 实例化olive状态编辑view
     */
    private fun initOliveStatusView() {
        val oliveUiConfig = vBus.get<IOLiveEditViewUIConfig>(TOPIC_OLIVE_UI_CONFIG)
            ?: IOLiveEditViewUIConfig.getOliveUIConfig(activity.getCurrentAppUiConfig())
        val spaceValue = oliveUiConfig.getStatusControlViewSpaceValue(activity.resources)
        oliveStatusView = sectionBus.rootView.findViewById(R.id.olive_status)
        oliveStatusView?.updateChildViewSpace(spaceValue)
        oliveStatusView?.setOLiveStatusChangeListener(onOliveStatusListener)
        initOliveSoundSwitchView()
    }

    /**
     * 初始化实况静音开关view的显示状态
     */
    private fun initOliveSoundSwitchView() {
        val bean = vBus.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE) ?: OliveUiBean()
        // 是否支持导出超清图片
        val isSupportExportImage = ConfigAbilityWrapper.getBoolean(ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_OLIVE_EXTRACT_UHDR_FRAME)
        oliveStatusView?.initStatusView(
            isOliveEnable = bean.oliveEnable,
            isSoundEnable = bean.oliveSoundEnable,
            isSupportExportImage = isSupportExportImage
        )
        updateThumbnailLayoutAlpha(bean.oliveEnable)
    }

    /**
     * 从toolbar_container中移除历史view
     */
    private fun removeContentViewIfNeed() {
        oliveStatusView?.setOnClickListener(null)
        sectionContentView?.also { sectionContainerView?.removeView(it) }
    }

    /**
     * 加载缩图列表数据
     */
    private fun loadThumbnailList() {
        GLog.d(TAG, LogFlag.DL) { "[loadThumbnailList] start" }
        oliveThumbnailSeekBar.calculationThumbnailCount { count ->
            vBus.notifyOnce(REPLY_TOPIC_OLIVE_LOAD_THUMBNAIL_LIST, count.toLong())
        }
    }

    /**
     * 缩图数据集变化时回调
     * markby zubintang: 这个函数需要优化下，做了很多不该在这里做的事情
     */
    private fun onThumbnailListChange(thumbnailList: List<OLiveThumbnailItem>) {
        GLog.d(TAG, LogFlag.DL) { "[onThumbnailListChange] size: ${thumbnailList.size}" }
        // 更新缩图列表
        thumbnailLayoutAdapter.updateThumbnailList(thumbnailList)
        // 更新裁剪位置
        updateTrimPosition()
        // 更新封面小圆点、原始封面小圆点、设为封面tips位置
        updateRecommendCoverTipsPosition(needUpdateCover = true, needUpdateOriginal = true, needUpdateSetCoverTips = true)
    }

    /**
     * 吸附位置集合变化时
     */
    private fun onAdsorbentListChange(adsorbentList: List<Long>) {
        oliveThumbnailSeekBar.setAdsorbentLengthList(adsorbentList)
    }

    /**
     * 确保正确设置裁剪控件初始位置
     * 更新裁剪区域的位置，内部会判断是否重新设置
     */
    private fun updateTrimPosition() {
        vBus.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE)?.also { bean ->
            bean.totalDuration.takeIf { it > 0 }?.also { duration ->
                oliveThumbnailSeekBar.setTrimInitPos(
                    duration,
                    bean.slidingTimeMs.takeIf { it >= 0 } ?: OLiveEditConstant.DEFAULT_TIME_MS,
                    bean.startTime.takeIf { it >= 0 } ?: OLiveEditConstant.DEFAULT_TIME_MS,
                    bean.endTime.takeIf { it >= 0 } ?: duration)
            }
        }
    }

    /**
     * 更新时间轴上圆点标记组件、封面气泡组件位置
     *
     * @param needUpdateCover 是否需要更新封面圆点组件
     * @param needUpdateOriginal 是否需要更新原始封面圆点组件
     * @param needUpdateSetCoverTips 是否需要更新设为封面气泡组件
     */
    private fun updateRecommendCoverTipsPosition(
        needUpdateCover: Boolean = false,
        needUpdateOriginal: Boolean = false,
        needUpdateSetCoverTips: Boolean = false,
    ) {
        vBus.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE)?.also { bean ->
            val oliveUiConfig = vBus.get<IOLiveEditViewUIConfig>(TOPIC_OLIVE_UI_CONFIG)
                ?: IOLiveEditViewUIConfig.getOliveUIConfig(activity.getCurrentAppUiConfig())
            val duration = bean.totalDuration.toFloat().takeIf { it > MathUtils.ZERO_F } ?: let {
                GLog.w(TAG, LogFlag.DL) { "[updateRecommendCoverTipsPosition] duration is invalid" }
                return
            }
            // 更新封面圆点位置
            val coverPercent = bean.coverTimeMs / duration
            coverPercent.takeIf {
                needUpdateCover && (it >= 0) && (it <= 1)
            }?.also { percent ->
                oliveThumbnailSeekBar.calculationContentPercentWidth(oliveUiConfig, percent) { position ->
                    oliveRecommendLayout.updateCoverRecommendView(true, oliveUiConfig, position)
                }
            } ?: GLog.w(TAG, LogFlag.DL) { "[updateRecommendCoverTipsPosition] needUpdateCover: $needUpdateCover" }

            // 更新原始封面圆点位置
            val originalPercent = bean.originalTimeMs / duration
            originalPercent.takeIf {
                needUpdateOriginal && (it >= 0) && (it <= 1)
            }?.also { percent ->
                oliveThumbnailSeekBar.calculationContentPercentWidth(oliveUiConfig, percent) { position ->
                    oliveRecommendLayout.updateOriginalRecommendView(true, oliveUiConfig, position)
                }
            } ?: GLog.w(TAG, LogFlag.DL) { "[updateRecommendCoverTipsPosition] needUpdateOriginal: $needUpdateOriginal" }
        }
    }

    /**
     * 显示当前选中位置的图片（包括预览区和选帧框）
     */
    private fun showSlidingBitmap(uiBean: OliveUiBean? = null) {
        val bean = uiBean ?: vBus.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE) ?: return

        setVideoBitmap(bean.slidingTimeMs)

        /**
         * 如果再次滑动到了当前封面，则需要直接切换过去。
         */
        if ((bean.slidingTimeMs == bean.coverTimeMs) && bean.isCoverProcessing.not()) {
            showCoverBitmap(bean.coverTimeMs, bean.coverBitmap, bean.coverGainMap)
            return
        }

        /**
         * 如果当前滑动到了原始封面图的位置，需要临时切换到显示原始封面帧的图片，而不是显示该位置的视频内容，避免在设为封面帧的时候，出现跳动。
         */
        val originalCoverItem = vBus.get<EditingImagePack>(TopicID.Operating.TOPIC_IMAGE_PACK)?.oliveOriginalCoverItem
        if (bean.slidingTimeMs == originalCoverItem?.timeMs) {
            showOriginalCoverWithoutHdr()
            return
        }
    }

    private fun showOriginalCoverWithoutHdr() {
        val originalCoverItem = vBus.get<EditingImagePack>(TopicID.Operating.TOPIC_IMAGE_PACK)?.oliveOriginalCoverItem
        if (originalCoverItem == null) {
            GLog.e(TAG, LogFlag.DL, "[showOriginalCoverWithoutHdr] failed for originalCoverItem is null.")
            return
        }

        showCoverBitmap(originalCoverItem.timeMs, originalCoverItem.bitmap, null)
    }

    /**
     * 预览区和选帧框，显示封面高清图
     * 封面位置，使用 ImageView 显示
     *
     * @param coverTimeMs 封面对应时间戳
     * @param bitmap 封面对应高清图
     */
    private fun showCoverBitmap(coverTimeMs: Long, bitmap: Bitmap?, gainMap: Bitmap?) {
        activity.lifecycleScope.launch(Dispatchers.UI.immediate) {
            GLog.d(TAG, LogFlag.DL) { "[showCoverBitmap] coverTimeMs: $coverTimeMs, isRecycled: ${bitmap?.isRecycled}" }
            if ((coverTimeMs < 0) || (bitmap == null) || bitmap.isRecycled) {
                return@launch
            }
            val bean = vBus.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE)
            /**
             * 当seekbar的slidingTimeMs位置与传入需要显示的coverTimeMs 不一致时，证明视频预期显示的位置和封面位置不是同一个位置
             * 1.此时不能显示封面view否者会闪
             * 2.不可以更新视频位置带带来预览视频界面跳变问题
             */
            if ((bean?.slidingTimeMs != coverTimeMs) && isEnhanceBitmapLoadingShow.not()) {
                GLog.w(TAG, LogFlag.DL) { "[showCoverBitmap] slidingTimeMs: ${bean?.slidingTimeMs}, coverTimeMs = $coverTimeMs. return" }
                return@launch
            }
            val coverInfo = OliveCoverInfo(bitmap, gainMap)
            submitCoverToPipelineNtf?.notify(coverInfo, {
                // 回调回来之后，可能这时候滑动的位置已经不是当前设置封面的位置了，需要做一下校验。
                val currentBean = vBus.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE)
                GLog.d(TAG) { "[showCoverBitmap], slidingTime:${currentBean?.slidingTimeMs}, coverTime:$coverTimeMs" }
                if ((currentBean == null) || (currentBean.slidingTimeMs == coverTimeMs)) {
                    changeToImageMode(coverTimeMs)
                }
            })
        }
    }

    /**
     * 预览区和选帧框，设置视频缩图
     * 非封面位置，使用 VideoView 显示
     *
     * @param timeMs 对应时间戳
     */
    private fun setVideoBitmap(timeMs: Long) {
        activity.lifecycleScope.launch(Dispatchers.UI) {
            timeMs.takeIf { timeMs >= 0 }?.also {
                pendingSwitchToVideoMode = true
                needUpdateSelectBitmap = true

                // seekTo完成后回调 INFO_SEEK_COMPLETE，切换视频显示模式
                val finalSeekTime = olivePreviewController.seekTo(it)
                // 如果seekTo的回调时间戳和传入的时间戳不一致，说明做了时间修正，需要同步给外部，保证设为封面帧的时候，选中的时间戳也是修正后的时间戳
                if (finalSeekTime != timeMs) {
                    vBus.notifyOnce(REPLY_TOPIC_OLIVE_UPDATE_FIXED_SLIDING_TIME_STAMP, finalSeekTime)
                }
            }
        }
    }

    private fun getOLiveOriginalHdrMetadataPack(): UHdrMetadataPack? {
        /**
         * 当图片是UHDR土拍时，需要携带相关参数
         */
        val hdrImageContent = vBus.get<IHdrImageContent>(TopicID.InputArguments.TOPIC_INPUTS_HDR_DRAWABLE)?.takeIf {
            it.isHdrContentValid() && (it.metadata.getHdrType() == HdrImageType.UHDR)
        }
        return (hdrImageContent?.metadata as? UHdrMetadataPack)
    }

    /**
     * 校验Hdr算法
     * @param operateType 操作类型

     * @param blockOperationsFromUser 是否阻塞用户的操作；当前只用在xdr插件下载场景
     */
    private fun checkHdrAlgorithm(operateType: OperateType, blockOperationsFromUser: Boolean = true) {
        tryShowUnsupportTips {
            // 是否为uhdr图
            val isUHdrImage = getOLiveOriginalHdrMetadataPack() != null
            // 是否执行过插件下载流程
            val isExecuted = vBus.get<Boolean>(TOPIC_OLIVE_DOWNLOAD_XDR_COMPONENT_EXECUTED) == true
            // proXDR插件是否在下载中
            val isDownloading = OliveCoverProXDRModelDownloader.isDownloadingFromUserTriggered()
            // 是否为hdr视频，hdr视频的帧不需要做ProXDR处理
            val isHdrVideo = vBus.get<Boolean>(TOPIC_OLIVE_IS_HDR_VIDEO) == true
            // 是否需要走xdr插件下载流程
            val needCheckProXDRComponent =
                isUHdrImage && isHdrVideo.not() && isExecuted.not() && (blockOperationsFromUser.not() && isDownloading).not()
            if (needCheckProXDRComponent) {
                oliveCoverProXDRComponentCheckStrategy.checkSupportability { isSuccess ->
                    if (isSuccess.not() && blockOperationsFromUser) {
                        GLog.d(TAG, LogFlag.DL) { "[updateCurrentCover.checkSupportability] isSuccess = $isSuccess. return" }
                        return@checkSupportability
                    }
                    sectionBus.mainVM.downloadXdrComponentExecutedPub?.publish(true)
                    if (operateType == OperateType.COVER) notifyUpdateCover() else notifyExportPhoto()
                }
            } else {
                if (operateType == OperateType.COVER) notifyUpdateCover() else notifyExportPhoto()
            }
        }
    }

    private fun notifyUpdateCover() {
        activity.lifecycleScope.launch(Dispatchers.UI) {
            isTimelineSeekNewImage = false
            oliveNtf?.notify(OliveUiBean(id = OliveBeanId.COVER))
            updateRecommendCoverTipsPosition(needUpdateCover = true)
        }
    }

    private fun notifyExportPhoto() {
        //执行导出照片相关逻辑
        activity.lifecycleScope.launch(Dispatchers.UI) {
            oliveNtf?.notify(OliveUiBean(id = OliveBeanId.OLIVE_EXPORT_IMAGE))
        }
    }

    /**
     * 更改实况开关状态
     * @param onEnableChange 开关状态
     */
    private fun changeOliveSwitchStatus(onEnableChange: Boolean) {
        if (onEnableChange.not()) {
            startSelectMoveAnimator()
        }
        updateThumbnailLayoutAlpha(onEnableChange)
        oliveNtf?.notify(OliveUiBean(id = OliveBeanId.OLIVE_ENABLE_STATUS, oliveEnable = onEnableChange))
    }

    /**
     * 更新缩图轴布局子view的透明度
     */
    private fun updateThumbnailLayoutAlpha(oliveEnable: Boolean) {
        oliveMaskView.setMaskViewVisible(oliveEnable.not())
        if (oliveEnable) {
            oliveRecommendLayout.alpha = VIEW_ENABLE_ALPHA
            oliveThumbnailSeekBar.alpha = VIEW_ENABLE_ALPHA
        } else {
            oliveRecommendLayout.alpha = VIEW_DISENABLE_ALPHA
            oliveThumbnailSeekBar.alpha = VIEW_DISENABLE_ALPHA
        }
    }

    /**
     * 调用VM去执行慢动作的逻辑
     *
     * @param enable 开关慢动作
     */
    private fun changeOliveSlowMotion(enable: Boolean) {
        oliveNtf?.notify(OliveUiBean(id = OliveBeanId.OLIVE_SLOW_MOTION, oliveSlowMotion = enable))
    }

    /**
     * 更改声音开关状态
     * @param onEnableChange 开关状态
     */
    private fun changeOliveSoundStatus(onEnableChange: Boolean) {
        olivePreviewController.setMutePlayer(onEnableChange.not())
        oliveNtf?.notify(OliveUiBean(id = OliveBeanId.OLIVE_SOUND_STATUS, oliveSoundEnable = onEnableChange))
    }


    /**
     * 尝试判断是否显示HDR提示弹框
     *
     * @param nextAction 回调接口
     */
    private fun tryShowUnsupportTips(nextAction: () -> Unit) {
        // 不支持hdr编辑，不需要弹框提示
        val isSupportHdrEdit = vBus.get<Boolean>(TOPIC_INPUTS_IS_SUPPORT_HDR_EDIT) ?: false
        if (isSupportHdrEdit.not()) {
            GLog.d(TAG, LogFlag.DL) { "[tryShowUnSupportTips] not supportHdrImageEdit, no need show" }
            nextAction.invoke()
            return
        }

        vBus.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE)?.also { bean ->
            // 本次编辑已经弹过提示了，不再继续弹框提示
            if (bean.hasShowUnSupportTips) {
                GLog.d(TAG, LogFlag.DL) { "[tryShowUnSupportTips] has show UnSupportTips, no need show again" }
                nextAction.invoke()
                return
            }

            // 当前滑动位置是回到原始封面位置了，则支持HDR，不弹框提示
            if (bean.slidingTimeMs != OLiveEditConstant.INVALID_TIME_MS && bean.slidingTimeMs == bean.originalTimeMs) {
                GLog.d(TAG, LogFlag.DL) { "[tryShowUnSupportTips] is restored original cover, no need to show" }
                nextAction.invoke()
                return
            }
        }

        /**
         * 如果当前已经提亮起来了, 需要显示弹框
         */
        if (vBus.get<IHdrImageContent>(TopicID.InputArguments.TOPIC_INPUTS_HDR_DRAWABLE)?.isHdrContentValid() == false) {
            GLog.d(TAG, LogFlag.DL) { "[tryShowUnSupportTips] not hdr scene, no need to show" }
            nextAction.invoke()
            return
        }

        if (!unSupportTips.shouldShowTips()) {
            GLog.d(TAG, LogFlag.DL) { "[tryShowUnSupportTips] hdr scene should not show tips" }
            nextAction.invoke()
            return
        }

        unSupportTips.showTips {
            vBus.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE)?.also { bean ->
                bean.hasShowUnSupportTips = true
            }
            nextAction.invoke()
        }
    }

    /**
     * 显示立即查看提示
     */
    private fun showImmediateLookTips() {
        //不为空，代表tips为显示状态
        if (immediateLookTipsSnackBar != null) {
            return
        }
        val bottomMargin = resources?.getDimensionPixelSize(com.support.snackbar.R.dimen.coui_snack_bar_margin_bottom)
            ?: ScreenUtils.dpToPixel(R.dimen.photo_editor_margin_88_dp)
        val tipsInfo = resources?.getString(R.string.picture3d_editor_olive_export_success) ?: ""
        immediateLookTipsSnackBar =
            BlockingSnackBar.make(sectionBus.rootView, tipsInfo, DEFAULT_TIPS_SNACKBAR_DISPLAY_DURATION, bottomMargin).apply {
                setOnAction(R.string.picture3d_editor_olive_export_immediate_look, lookSnackBarClickListener)
                setOnStatusChangeListener(object : BlockingSnackBar.OnStatusChangeListener {
                    override fun onShown(blockingSnackBar: BlockingSnackBar) {
                    }

                    override fun onDismissed(blockingSnackBar: BlockingSnackBar) {
                        immediateLookTipsSnackBar = null
                    }
                })
                show()
            }
    }

    /**
     * 慢动作状态变更，此方法用户UI状态变换
     */
    private fun onSlowMotionChange() {
        TODO("Not yet implemented")
    }

    /**
     * 这里设置的是空的toolbar容器，用于把 default_preview_area 挤上去。
     * 真实的工具栏是自定义的 toolbarLayout，会占满高度
     */
    override fun getContainerViewID(): Int {
        return R.id.toolbar_container
    }

    override fun getContainerViewWidthResourceId(): Int {
        return R.dimen.picture3d_olive_container_bar_width
    }

    override fun getContainerViewHeightResourceId(): Int {
        return R.dimen.picture3d_olive_container_bar_height
    }

    override fun getContentLayoutId(config: AppUiResponder.AppUiConfig): Int {
        return if (EditorUIConfig.isEditorLandscape(config)) {
            R.layout.olive_edit_status_layout_landscape
        } else {
            R.layout.olive_edit_status_layout
        }
    }

    internal companion object {
        const val TAG = "OliveSection"
        val SPEC = SectionSpec(OliveSection::class.java)

        private const val ROTATE_CLIP_FRAME_VIEW_ADD_ANIMATION_DURATION_TIME = 300

        //完全不透明
        private const val VIEW_ENABLE_ALPHA = 1.0f

        // 30%不透明度
        private const val VIEW_DISENABLE_ALPHA = 0.3f

        /**
         * 默认的提示snack bar显示时长，交互确认用4s
         */
        private const val DEFAULT_TIPS_SNACKBAR_DISPLAY_DURATION = 4000
    }

    /**
     * 操作类型枚举值
     */
    internal enum class OperateType {
        COVER, EXPORT
    }
}