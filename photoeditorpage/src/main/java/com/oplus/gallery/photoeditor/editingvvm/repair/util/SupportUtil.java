/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - ${FILE_NAME}
 * * Description: build this module.
 * * Version: 1.0
 * * Date : 2020/02/27
 * * Author: GuangJin.Ye@Apps.Gallery3D
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  GuangJin.Ye@Apps.Gallery3D       2020/02/27    1.0     build this module
 ****************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.repair.util;

import static com.oplus.gallery.business_lib.function.FunctionSwitcherConfig.PREFERENCE_AI_REPAIR_ENABLE;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.text.TextUtils;

import com.oplus.gallery.business_lib.api.ApiDmManager;
import com.oplus.gallery.basebiz.helper.function.FunctionSwitchManager;
import com.oplus.gallery.business_lib.function.FunctionSwitcherConfig;
import com.oplus.gallery.business_lib.model.http.ISecurityUrl;
import com.oplus.gallery.foundation.fileaccess.utils.FileOperationUtils;
import com.oplus.gallery.foundation.util.stdid.StdIdManager;
import com.oplus.gallery.foundation.util.text.TextUtil;
import com.oplus.gallery.standard_lib.file.File;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.standard_lib.util.io.IOUtils;
import com.oplus.gallery.foundation.util.media.MimeTypeUtils;

import java.io.ByteArrayOutputStream;

public final class SupportUtil {

    private static final String TAG = "SupportUtil";
    public static final int DEFAULT_QUALITY = 100;
    public static final long KB = 1024;
    private static final int TEN_THOUSAND = 10 * 1000;
    private static final long MB = 1024 * KB;
    private static final int STEP = 10;

    private SupportUtil() {

    }

    public static final class Size {
        public static final int MAX = 20; //MB
        public static final int MIN = 100; //KB
        public static final int RESOLUTION_LIMIT_MILLION = 48;
        public static final int RESOLUTION_LIMIT = 4800;
        public static final int RESOLUTION_LIMIT_VALUE = RESOLUTION_LIMIT * TEN_THOUSAND;
        public static final long MAX_BYTE = MAX * MB;
        public static final long MIN_BYTE = MIN * KB;
        public static final long NETWORK_LIMIT = 4 * MB;

        private Size() {

        }
    }

    public static final class Format {
        public static final String JPG = ".jpg";
        public static final String JPEG = ".jpeg";
        public static final String PNG = ".png";

        private Format() {

        }
    }

    public static boolean isSupportAiRepairMimeType(String type) {
        return MimeTypeUtils.isJpeg(type) || MimeTypeUtils.isPng(type);
    }

    public static boolean isSupportMinSize(long length) {
        return length >= Size.MIN_BYTE;
    }



    public static boolean isSupportMaxSize(long length) {
        return length <= Size.MAX_BYTE;
    }



    public static byte[] compressBitmap(Bitmap bitmap) {
        return compressBitmap(bitmap, DEFAULT_QUALITY);
    }

    private static byte[] compressBitmap(Bitmap bitmap, int quality) {
        if (bitmap == null) {
            GLog.e(TAG, "compressBitmap. bitmap is null.");
            return null;
        }
        if (quality < 0) {
            GLog.e(TAG, "compressBitmap. quality = " + quality);
            return null;
        }
        ByteArrayOutputStream baos = null;
        try {
            baos = new ByteArrayOutputStream();
            bitmap.compress(Bitmap.CompressFormat.JPEG, quality, baos);
            int currentSize = baos.toByteArray().length;
            int previousSize = -1;
            while ((currentSize > Size.NETWORK_LIMIT) && (currentSize != previousSize)
                    && (quality > 0)) {
                baos.reset();
                quality -= STEP;
                bitmap.compress(Bitmap.CompressFormat.JPEG, quality, baos);
                previousSize = currentSize;
                currentSize = baos.toByteArray().length;
            }
            return baos.toByteArray();
        } catch (Exception e) {
            GLog.e(TAG, "compressBitmap Exception = " + e);
        } finally {
            bitmap.recycle();
            IOUtils.closeQuietly(baos);
        }
        return null;
    }

    public static byte[] compressBitmap(String filePath) {
        if (!isFileExist(filePath)) {
            GLog.e(TAG, "compressBitmap. filePath is null.");
            return null;
        }
        long len = new File(filePath).length();
        if (len <= Size.NETWORK_LIMIT) {
            return FileOperationUtils.fileToByte(filePath);
        }
        int quality = DEFAULT_QUALITY - STEP - (int) (len / Size.NETWORK_LIMIT * STEP);

        return compressBitmap(BitmapFactory.decodeFile(filePath), quality);
    }

    public static String getStdId() {
        return StdIdManager.Companion.getINSTANCE().getAuId();
    }

    /**
     * 是否支持修复功能（老的架构里面修复有关的都是带有AI, 现在为了和新的AI修复进行区分, 将方法的Ai去掉）
     */
    public static boolean isSupportRepair() {
        return isAddressValid()
                && FunctionSwitcherConfig.INSTANCE.getFunctionEnableResult(PREFERENCE_AI_REPAIR_ENABLE, false);
    }

    public static boolean isAddressValid() {
        String url = TextUtil.EMPTY_STRING;
        ISecurityUrl securityUrl = ApiDmManager.getAppDM().getSecurityUrl();
        if (securityUrl != null) {
            url = securityUrl.getOCloudAiRepairHostName();
        }
        return !TextUtils.isEmpty(url);
    }

    public static boolean isSupportAiFilter() {
        return FunctionSwitchManager.INSTANCE.isSwitchEnable(FunctionSwitcherConfig.PREFERENCE_AI_FILTER_ENABLE);
    }

    public static boolean isFileExist(String path) {
        try {
            if (TextUtils.isEmpty(path)) {
                return false;
            }
            File file = new File(path);
            if (file.exists() && file.isFile()) {
                return true;
            }
        } catch (Exception e) {
            GLog.w(TAG, "isFileEixist, " + e);
        }
        return false;
    }

}
