/***********************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd.
 ** All rights reserved.
 **
 ** File: - AIGraffitiGuidePagerAdapter.kt
 ** Description: xxx
 ** Version: 1.0
 ** Date : 2024/10/11
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>      <data>      <version >     <desc>
 ** ------------------------------------------------------------
 ** gongziyao      2024/10/11       1.0          created
 ***************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.aigraffiti.ui

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.scrollview.COUINestedScrollView
import com.coui.appcompat.textview.COUITextView
import com.oplus.anim.EffectiveAnimationView
import com.oplus.gallery.photoeditor.R

internal class AIGraffitiGuidePagerAdapter(
    private var photoUsed: String
) : RecyclerView.Adapter<AIGraffitiGuidePagerAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val rootView = LayoutInflater.from(parent.context).inflate(
            R.layout.picture3d_editor_aigraffiti_guide_content_vertical, parent, false
        )
        return ViewHolder(rootView)
    }

    override fun getItemCount(): Int = 1

    override fun getItemViewType(position: Int): Int {
        return VIEW_TYPE_VERTICAL
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bindData()
    }

    companion object {
        const val VIEW_TYPE_HORIZONTAL = 0
        const val VIEW_TYPE_VERTICAL = 1
        const val ANIM_PLAY_DELAY = 200L
        const val SCROLLING_DOWN = 2
        const val SCROLL_UP = -2
        const val DIVIDER_REFRESH_DELAY = 500L
        const val PHOTO_USE_VALUE_SIZE = 2
        const val INDEX_TOTAL = 1
        const val DEFAULT_TOTAL = 10
    }

    inner class ViewHolder(val itemView: View) : RecyclerView.ViewHolder(itemView) {
        fun bindData() {
            itemView.apply {
                findViewById<EffectiveAnimationView>(R.id.gp_effective_anim_view).apply {
                    postDelayed({
                        playAnimation()
                    }, ANIM_PLAY_DELAY)
                }
                val lineView = findViewById<View>(R.id.gp_line)
                postDelayed({
                    findViewById<COUINestedScrollView>(R.id.gp_scroll_container).let {
                        val canScroll = it.canScrollVertically(SCROLLING_DOWN) or it.canScrollVertically(SCROLL_UP)
                        lineView.visibility = if (canScroll) View.VISIBLE else View.GONE
                    }
                }, DIVIDER_REFRESH_DELAY)
                val usedCount = photoUsed.split(",")
                val total = if (usedCount.size == PHOTO_USE_VALUE_SIZE) usedCount[INDEX_TOTAL].toInt() else DEFAULT_TOTAL
                findViewById<COUITextView>(R.id.gp_guide_desc)?.apply {
                    val summary = resources.getQuantityString(
                        com.oplus.gallery.basebiz.R.plurals.basebiz_ai_graffiti_guide_panel_description,
                        total,
                        total
                    )
                    setText(summary, TextView.BufferType.NORMAL)
                    setTooltipText(summary)
                }
            }
        }
    }
}
