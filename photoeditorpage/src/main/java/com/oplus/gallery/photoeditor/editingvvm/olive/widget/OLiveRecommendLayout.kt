/*********************************************************************************
 ** Copyright (C), 2024-2034, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OLiveRecommendLayout.kt
 ** Description: OLive（动态照片）时间轴封面圆点容器组件
 ** Version: 1.0
 ** Date: 2024/1/31
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2024/1/31      1.0        created
 *********************************************************************************/

package com.oplus.gallery.photoeditor.editingvvm.olive.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.RelativeLayout
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.olive.IOLiveEditViewUIConfig
import com.oplus.gallery.standard_lib.app.AppConstants

/**
 * OLive（动态照片）时间轴封面圆点容器组件
 */
class OLiveRecommendLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    defStyleRes: Int = 0
) : RelativeLayout(context, attrs, defStyleAttr, defStyleRes) {

    /**
     * 封面帧的tipsView（小圆点）
     */
    private val oLiveCoverRecommendView: OliveCoverRecommendView

    /**
     * 原始封面帧的tipsView（灰色小圆点）
     */
    private val oLiveOriginalRecommendView: OliveOrignalRecommendView

    /**
     * 封面图位置
     */
    private var coverPosition: Int = INVALID_POSITION

    /**
     * 默认封面图位置
     */
    private var orignalPosition: Int = INVALID_POSITION

    /**
     * 封面帧的tipsView（小圆点）显示标记
     */
    private var isCoverViewShow: Boolean = true

    init {
        val view = LayoutInflater.from(context).inflate(R.layout.olive_edit_recommend_layout, this, true)
        oLiveCoverRecommendView = view.findViewById(R.id.olive_edit_olive_cover_recommend_view)
        oLiveOriginalRecommendView = view.findViewById(R.id.olive_edit_olive_original_recommend_view)
        oLiveCoverRecommendView.visibility = View.INVISIBLE
        oLiveOriginalRecommendView.visibility = View.INVISIBLE
    }
    /**
     * 更新封面View（小白点）
     *
     * @param isShow 是否需要显示
     * @param oliveEditUIConfig UI配置
     * @param position 需要更新到的位置
     */
    fun updateCoverRecommendView(isShow: Boolean, oliveEditUIConfig: IOLiveEditViewUIConfig, position: Int) {
        //防止重复加载 对于隐藏了封面圆点如果走到这里就需要重新更新一下圆点显示
        if ((position == coverPosition) && isCoverViewShow) {
            return
        }
        coverPosition = position
        updateRecommendView(isShow, RecommendViewType.COVER, oliveEditUIConfig, position)
    }

    /**
     * 更新原始封面View（小灰点）
     *
     * @param isShow 是否需要显示
     * @param oliveEditUIConfig UI配置
     * @param position 需要更新到的位置
     */
    fun updateOriginalRecommendView(isShow: Boolean, oliveEditUIConfig: IOLiveEditViewUIConfig, position: Int) {
        //防止重复加载
        if (position == orignalPosition) {
            return
        }
        orignalPosition = position
        updateRecommendView(isShow, RecommendViewType.ORIGINAL, oliveEditUIConfig, position)
    }

    /**
     * 根据Type，获取对应的圆点组件
     *
     * @param recommendViewType 圆点类型
     *
     * @return 圆点组件
     */
    private fun getRecommendView(recommendViewType: RecommendViewType): OLiveRecommendView {
        return when (recommendViewType) {
            RecommendViewType.COVER -> oLiveCoverRecommendView
            RecommendViewType.ORIGINAL -> oLiveOriginalRecommendView
        }
    }

    /**
     * 更新圆点组件
     * @param isShow 是否显示
     * @param recommendViewType 圆点类型
     * @param oliveEditUIConfig UI配置
     * @param position 需要更新到的位置
     */
    private fun updateRecommendView(
        isShow: Boolean,
        recommendViewType: RecommendViewType,
        oliveEditUIConfig: IOLiveEditViewUIConfig,
        position: Int
    ) {
        val recommendView = getRecommendView(recommendViewType)
        //需要在post里面用到组件宽高计算，如果不加post，那个时候还拿不到宽高
        recommendView.post {
            val marginStart = position - recommendView.width / AppConstants.Number.NUMBER_2
            recommendView.updateRecommendViewSizeAndPotion(oliveEditUIConfig, marginStart)
            if (isShow) {
                recommendView.show(true)
            } else {
                recommendView.hide(true)
            }
            GLog.d(TAG, LogFlag.DL) { "[updateRecommendView]: show=$isShow,  recommendViewType = $recommendViewType, location=$marginStart" }
        }
    }

    /**
     *封面白点属性设置为不可见
     */
    fun hideCoverView() {
        if (oLiveCoverRecommendView.visibility == VISIBLE) {
            oLiveCoverRecommendView.visibility = INVISIBLE
            isCoverViewShow = false
        } else {
            isCoverViewShow = true
        }
    }

    /**
     *封面白点属性设置为可见
     */
    fun setRecommendViewVisible() {
        oLiveCoverRecommendView.visibility = View.VISIBLE
    }

    companion object {
        private const val TAG = "OLiveRecommendLayout"
        private const val INVALID_POSITION = -1
    }

    private enum class RecommendViewType {
        /**
         * 覆盖的封面帧
         */
        COVER,

        /**
         * 原始的封面帧
         */
        ORIGINAL
    }
}
