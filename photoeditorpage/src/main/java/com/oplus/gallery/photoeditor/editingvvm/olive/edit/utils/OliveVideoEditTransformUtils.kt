/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: -OliveVideoEditFilterUtils
 ** Description: olive编辑旋转裁剪相关
 ** Version: 1.0
 ** Date : 2024/8/7
 ** Author: dingyong@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  dingyong@Apps.Gallery3D      2024/8/7    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.olive.edit.utils

import android.util.Size
import com.oplus.gallery.foundation.opengl.transform.Matrix
import com.oplus.gallery.photoeditor.editingvvm.transform.TransformRecord
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.data.OliveVideoEditTransEffects

internal object OliveVideoEditTransformUtils {

    @JvmStatic
    internal fun createTransformRecord(currentRecord: TransformRecord?, originVideoSize: Size?): OliveVideoEditTransEffects {
        currentRecord?.let {
            val transEffects = OliveVideoEditTransEffects()
            transEffects.matrix = currentRecord.matrix
            transEffects.clipRect = currentRecord.clipRect
            transEffects.imageRect = currentRecord.imageRect
            originVideoSize?.let {
                transEffects.originVideoWidth = originVideoSize.width
                transEffects.originVideoHeight = originVideoSize.height
            }
            return transEffects
        } ?: return createDefaultTransformRecord(originVideoSize)
    }

    @JvmStatic
    private fun createDefaultTransformRecord(originVideoSize: Size?): OliveVideoEditTransEffects {
        val transEffects = OliveVideoEditTransEffects()
        transEffects.matrix = Matrix()
        transEffects.clipRect = emptyList()
        transEffects.imageRect = emptyList()
        originVideoSize?.let {
            transEffects.originVideoWidth = originVideoSize.width
            transEffects.originVideoHeight = originVideoSize.height
        }
        return transEffects
    }

    /**
     * 是否为失效的旋转裁剪记录
     */
    @JvmStatic
    internal fun isInvalidTransformRecord(currentRecord: TransformRecord): Boolean {
        if (currentRecord.axisAngleTransform == null) {
            return true
        }
        if (currentRecord.axisAngleTransform.axis == null) {
            return true
        }
        if (currentRecord.axisAngleTransform.axis.value == null) {
            return true
        }
        if (currentRecord.clipRect.isEmpty()) {
            return true
        }
        if (currentRecord.imageRect.isEmpty()) {
            return true
        }
        if (currentRecord.viewportWidth == 0 || currentRecord.viewportHeight == 0) {
            return true
        }
        return false
    }
}