/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - ControlledRelativeLayout.java
 * * Description: XXXXXXXXXXXXXXXXXXXXX.
 * * Version: 1.0
 * * Date : 2017/11/15
 * * Author: xinyang.Hu@Apps.Gallery3D
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  xinyang.Hu@Apps.Gallery3D       2017/11/15    1.0     build this module
 * *  BBB       2016/11/25     1.1     add some components
 ****************************************************************/

package com.oplus.gallery.photoeditor.widget.layout;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;

public class ControlledRelativeLayout extends RelativeLayout {

    private boolean mIsMenuClickable = true;

    public ControlledRelativeLayout(Context context) {
        this(context, null);
    }

    public ControlledRelativeLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public void setMenuClickable(boolean clickable) {
        mIsMenuClickable = clickable;
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent motionEvent) {
        if (!mIsMenuClickable) {
            return true;
        } else {
            return super.onInterceptTouchEvent(motionEvent);
        }
    }
}
