/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SpecificEditViewAction
 ** Description:
 ** Version: 1.0
 ** Date : 2024/1/26
 ** Author: wudanyang
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  wudanyang                      2024/1/26    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.viewaction

import android.content.Intent
import android.os.Bundle
import androidx.core.net.toUri
import com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_EDIT_FROM_PHOTO
import com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_EDIT_IMAGE_URI
import com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_EDIT_MEDIA_ITEM_PATH
import com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_EDIT_SKILL
import com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_IS_FROM_EXTERNAL
import com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.PHOTO_EDITOR_FRAGMENT
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.business_lib.menuoperation.EditPhotoAction.Companion.KEY_MEDIA_MODEL_TYPE
import com.oplus.gallery.business_lib.menuoperation.EditPhotoAction.Companion.KEY_SUPPORT_LOSS_LESS_CACHE
import com.oplus.gallery.business_lib.model.config.PhotoEditorType
import com.oplus.gallery.foundation.uikit.intentrouter.EnsuredIntent
import com.oplus.gallery.foundation.uikit.intentrouter.IntentFilter
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.systemcore.IntentUtils
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.util.Picture3dUtil
import com.oplus.gallery.photoeditor.editingvvm.inputarguments.InputArgumentsVM.Companion.EDIT_SKILL_AI_ID_PHOTO_VALUE
import com.oplus.gallery.photoeditor.editingvvm.inputarguments.InputArgumentsVM.Companion.KEY_AI_ID_PHOTO_URI

/**
 * 跳转到指定编辑项页面，使用老的编辑框架
 * @param activity
 * @param intentFilter
 */
class SpecificEditViewAction(
    activity: BaseActivity,
    intentFilter: IntentFilter
) : BaseEditViewAction<Bundle?>(activity, intentFilter) {

    override val tag: String
        get() = TAG

    override fun onSetupIntentFilter(intentFilter: IntentFilter) {
        intentFilter.filterAll()
    }

    override fun onHandleIntent(ensuredIntent: EnsuredIntent, intent: Intent): Boolean {
        val skill = ensuredIntent.intent.getStringExtra(KEY_EDIT_SKILL)

        // 跳转到特定的某项编辑页
        val shouldIntercept = skill.isNullOrEmpty().not() and PhotoEditorType.NORMAL.tag.equals(skill).not()
        if (shouldIntercept) {
            dealIntent(intent)
        }
        return shouldIntercept
    }

    override fun processArgs(intent: Intent): Bundle? {
        val isFromPhotoPage = IntentUtils.getBooleanExtra(intent, KEY_EDIT_FROM_PHOTO, false)
        val photoUri = IntentUtils.getStringExtra(intent, KEY_EDIT_IMAGE_URI)
        val isFromExternal = isFromPhotoPage.not()
        val lossLessCache = IntentUtils.getBooleanExtra(intent, KEY_SUPPORT_LOSS_LESS_CACHE, false)
        var mediaItemPath: String? = null
        val editPhotoUri = photoUri ?: if (isFromExternal) {
            if (IntentUtils.getStringExtra(intent, KEY_EDIT_SKILL) == EDIT_SKILL_AI_ID_PHOTO_VALUE) {
                IntentUtils.getParcelableExtra(intent, KEY_AI_ID_PHOTO_URI)
            } else {
                intent.data
            }
        } else {
            mediaItemPath = IntentUtils.getStringExtra(intent, KEY_EDIT_MEDIA_ITEM_PATH)
            IntentUtils.getStringExtra(intent, KEY_EDIT_IMAGE_URI)?.toUri()
        } ?: run {
            GLog.w(TAG, "[startPhotoEditorPage] No image file detected for edit, intent is $intent")
            exitPhotoEditorWithToast(R.string.picture3d_exit_photoeditor_with_empty_photo_list)
            return null
        }
        val modelType = IntentUtils.getStringExtra(intent, KEY_MEDIA_MODEL_TYPE)
        return Bundle().apply {
            putString(KEY_EDIT_IMAGE_URI, editPhotoUri.toString())
            putString(KEY_EDIT_MEDIA_ITEM_PATH, mediaItemPath)
            putBoolean(KEY_IS_FROM_EXTERNAL, isFromExternal)
            putString(KEY_MEDIA_MODEL_TYPE, modelType)
            putBoolean(KEY_SUPPORT_LOSS_LESS_CACHE, lossLessCache)
            IntentUtils.getStringExtra(intent, Picture3dUtil.PHOTO_EDITOR_INVOKER)?.let {
                putString(Picture3dUtil.PHOTO_EDITOR_INVOKER, it)
            }
            intent.extras?.let {
                putAll(it)
            }
        }
    }

    override fun startPhotoEditorPage(data: Bundle?) {
        if (data == null) {
            GLog.e(TAG, "[startPhotoEditorPage] data is null,return ")
            return
        }
        startFragment(
            routerName = PHOTO_EDITOR_FRAGMENT,
            data = data
        )
    }

    companion object {
        private const val TAG = "SpecificEditViewAction"
    }
}