/********************************************************************************
 ** Copyright (C), 2024-2034, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - FilterUIBean
 ** Description: 滤镜切片的UI属性
 ** Version: 1.0
 ** Date : 2024/06/08
 ** Author: liyunting
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** liyunting                           2024/06/08   1.0          first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.filter

import android.graphics.Bitmap
import com.oplus.gallery.foundation.util.math.MathUtils

/**
 * 滤镜切片的UI属性集
 */
internal data class FilterUIBean(
    var id: FilterUIBeanId = FilterUIBeanId.NONE,
    var menuIndex: Int = MathUtils.ZERO,
    var ruleScrollerIndex: Int = MathUtils.ZERO
)

/**
 * 滤镜缩图UI属性集
 */
internal data class ThumbnailBean(
    /**
     * 缩图位置
     */
    var index: Int = 0,
    /**
     * 缩图bitmap
     */
    var bitmap: Bitmap
)

/**
 * 接收/发送 UiBean 事件的ID
 */
enum class FilterUIBeanId {
    NONE,

    /**
     * 从操作栈恢复UI
     */
    RECOVER_RECORD,
}