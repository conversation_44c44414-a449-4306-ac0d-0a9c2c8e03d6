/***********************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd.
 ** All rights reserved.
 **
 ** File: - AIDeFogMongoTemplate.kt
 ** Description: xxx
 ** Version: 1.0
 ** Date : 2025/3/26
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>      <data>      <version >     <desc>
 ** ------------------------------------------------------------
 ** gongziyao      2025/3/26       1.0          created
 ***************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.airepair.defogmongo

import android.content.Context
import android.content.res.Resources
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.FIRST_USING_AI_DEFOG_INTRODUCTION
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.airepair.AIFuncPermissionObserver
import com.oplus.gallery.photoeditor.editingvvm.airepair.AIFuncPermissionResId
import com.oplus.gallery.photoeditor.editingvvm.airepair.AIFuncTemplate
import com.oplus.gallery.photoeditor.editingvvm.airepair.AIRepairIntroductionViewData
import com.oplus.gallery.photoeditor.editingvvm.airepair.AIRepairRealmeRecord.Companion.CMD_DEFOG
import com.oplus.gallery.photoeditor.editingvvm.airepair.AIRepairType
import com.oplus.gallery.photoeditor.editingvvm.airepair.IAIFuncPermissionObserver

/**
 * AI 去雾功能模板
 */
internal class AIDeFogMongoTemplate : AIFuncTemplate {

    override val introductionConfigId: String = FIRST_USING_AI_DEFOG_INTRODUCTION

    override val introductionViewDataList = listOf(
        AIRepairIntroductionViewData(
            R.drawable.picture3d_editor_ic_introduction_de_fog_mongo,
            R.string.picture3d_editor_text_ai_deFogMongo_introduction_eliminate_reflections_title,
            R.string.base_aideFogMongo_dialog_title_functional_experience_description,
            R.raw.picture_editor_ai_edit_picture_guide_defog_anim,
            needPlayAnim = true
        )
    )
    override val command: String = CMD_DEFOG

    override val networkErrorMessage: Int = Resources.ID_NULL

    override val unknownErrorMessage: Int = R.string.picture3d_editor_text_deReflection_unknown_error

    override val contentSensitiveMessage: Int = R.string.picture3d_editor_text_dereflection_refuse_error

    override val contentSensitiveMessageOversea: Int = R.string.picture3d_editor_text_unified_show_refuse_copywriting_oversea

    override val type: AIRepairType = AIRepairType.DEFOG

    override fun createPermissionObserver(context: Context): IAIFuncPermissionObserver {
        return AIFuncPermissionObserver(context, AIFuncPermissionResId(Resources.ID_NULL, Resources.ID_NULL), TextUtil.EMPTY_STRING)
    }
}