/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AIRepairRequestObserver.kt
 ** Description: AI修复请求的观察者，观察数据变化并整合数据提供给外部
 ** Version: 1.0
 ** Date : 2024/5/30
 ** Author: xiewujie@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  xiewujie@Apps.Gallery3D      2024/05/30    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.airepair

import android.graphics.Bitmap
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_AIUNIT
import com.oplus.gallery.foundation.util.systemcore.GallerySystemProperties
import com.oplus.gallery.framework.abilities.editing.effect.AvEffect
import com.oplus.gallery.framework.abilities.editing.track.usage.IAvEffectUsage
import com.oplus.gallery.olivesdk.util.GLog

/**
 * AI修复请求的观察者，观察数据变化并整合数据提供给外部AI修复请求的观察者，观察数据变化并整合数据提供给外部
 */
class AIRepairRequestObserver {

    private val resultObserver = object : IAvEffectUsage.ValueObserver {
        override fun onValueChanged(key: String, value: Any?) {
            val resultMap = value as? Map<String, Any?> ?: run {
                GLog.e(TAG, "[resultObserver] value is invalid:$value")
                return
            }
            assembleResult(resultMap).let {
                onResult?.invoke(it)
            }
        }
    }

    private var currentEffectUsage: IAvEffectUsage? = null

    /**
     * 当结果返回时触发
     */
    var onResult: ((AIRepairResult?) -> Unit)? = null

    /**
     * 通知当前AvEffectUsage变化
     * @param effectUsage 最新的特效，会通过此对象观察AI修复数据的变化
     */
    fun notifyUsageChanged(effectUsage: IAvEffectUsage?) {
        if (currentEffectUsage == effectUsage) {
            return
        }
        if (effectUsage?.effect == AvEffect.AiRepairEffect || effectUsage?.effect == AvEffect.AiRepairRealmeEffect) {
            removeObserver()
            currentEffectUsage = effectUsage
            effectUsage.addValueObserver(AI_REPAIR_KEY_RESPONSE_RESULT, resultObserver)
        } else {
            removeObserver()
        }
    }

    private fun assembleResult(resultMap: Map<String, Any?>): AIRepairResult? {
        if (DEBUG_AIUNIT) {
            // 自定义调试码
            return AIRepairResult(DEBUG_CODE, DEBUG_SPECIFIC_CODE, "doing debug", null)
        }
        val code = resultMap[AI_REPAIR_KEY_RESPONSE_CODE] as? Int
        val specifyCode = resultMap[AI_REPAIR_KEY_RESPONSE_SPECIFY_CODE] as? Int
        val message = resultMap[AI_REPAIR_KEY_RESPONSE_MSG] as? String
        val mask = resultMap[AI_REPAIR_KEY_RESPONSE_MASK] as? Bitmap
        if ((code == null) || (specifyCode == null)) {
            GLog.e(TAG, "[assembleResult] error, code:$code, specifyCode:$specifyCode, bitmap:$mask")
            return null
        }
        return AIRepairResult(code, specifyCode, message, mask)
    }

    private fun removeObserver() {
        currentEffectUsage?.removeValueObserver(AI_REPAIR_KEY_RESPONSE_RESULT, resultObserver)
        currentEffectUsage = null
    }

    fun destroy() {
        onResult = null
        removeObserver()
    }

    companion object {

        private const val TAG = "AIRepairRequestObserver"

        private const val AI_REPAIR_KEY_RESPONSE_RESULT = "key_response_result"

        private const val AI_REPAIR_KEY_RESPONSE_CODE = "key_response_code"

        private const val AI_REPAIR_KEY_RESPONSE_SPECIFY_CODE = "key_response_specify_code"

        private const val AI_REPAIR_KEY_RESPONSE_MSG = "key_response_msg"

        private const val AI_REPAIR_KEY_RESPONSE_MASK = "key_response_mask"

        private val DEBUG_CODE get() = GallerySystemProperties.getInt("debug.gallery.repair.code", 0)

        private val DEBUG_SPECIFIC_CODE get() =  GallerySystemProperties.getInt("debug.gallery.repair.specific_code", 0)
    }
}

data class AIRepairResult(val code: Int, val specificCode: Int, val msg: String?, val mask: Bitmap?)