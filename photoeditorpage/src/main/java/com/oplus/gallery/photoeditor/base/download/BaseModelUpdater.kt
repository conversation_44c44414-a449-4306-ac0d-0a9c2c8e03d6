/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : BaseModelUpdater.kt
 ** Description : 相册通用的AIUnit模型的下载器（从AIUnit下载）
 ** Version     : 1.0
 ** Date        : 2024/04/10
 ** Author      : <EMAIL>
 ** getTag()    : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                         <date>    <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2024/04/10     1.0      OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.photoeditor.base.download

import android.content.Context
import com.oplus.aiunit.download.api.AIDownload
import com.oplus.aiunit.download.api.CustomTerms
import com.oplus.aiunit.download.api.DownloadRequest
import com.oplus.aiunit.download.core.DownloadListener
import com.oplus.aiunit.download.core.ErrorCode
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.photoeditor.editingvvm.component.eliminate.ai.ModelUpdateListener
import com.oplus.gallery.standard_lib.app.AppScope
import kotlinx.coroutines.launch

/**
 * 从AIUnit下载通用的模型下载器
 */
abstract class BaseModelUpdater : DownloadListener {

    private var applicationContext: Context? = null

    /** 本次操作是下载还是更新，用于区分词条 */
    private var isUpdate: Boolean = false
    private var cancelCallback: ((CancelStage) -> Unit)? = null
    private var installFinishCallback: ((code: Int) -> Unit)? = null

    /** 下载安装过程的监听，可在外部创建，回调进度更新UI操作 */
    var listener: ModelUpdateListener? = null

    /** 是否正在更新中 */
    var isUpdating = false
        private set

    /**
     * 设置打印log的tag
     */
    abstract val tag: String

    /**
     * 构建下载所需的自定义文案包
     */
    abstract fun buildCustomTerms(context: Context, isUpdate: Boolean): CustomTerms

    /**
     * 构建下载的场景名称
     */
    abstract fun buildSceneName(): String

    /**
     * 构建下载的能力名称
     */
    abstract fun buildDetectName(): String

    /**
     * 更新是否需要下载更新的配置
     */
    abstract fun updateDownloadConfig(context: Context)

    /**
     * 开始下载
     * @param context context
     * @param isUpdate 是否为更新算法
     * @param cancelCallback 取消的回调
     * @param installFinishCallback 安装完成时的回调
     */
    fun start(
        context: Context,
        isUpdate: Boolean = false,
        cancelCallback: ((CancelStage) -> Unit)? = null,
        installFinishCallback: ((code: Int) -> Unit)? = null
    ) {
        GLog.d(tag, "[start] start download,isUpdate:$isUpdate")
        this.applicationContext = context.applicationContext
        this.isUpdate = isUpdate
        this.cancelCallback = cancelCallback
        this.installFinishCallback = installFinishCallback

        val request = DownloadRequest().apply {
            sceneName = buildSceneName()
            detectName = buildDetectName()
            terms = buildCustomTerms(context, isUpdate)
            enableProgressUI = true
            enableProgressCallback = true
            downloadListener = this@BaseModelUpdater
        }
        AppScope.launch {
            AIDownload.start(context.applicationContext, request)
        }
    }

    /**
     * 下载就绪
     * @param fullSize, 完整大小
     * @param offsetSize, 已下载大小
     */
    override fun onPrepare(fullSize: Long, offsetSize: Long) {
        GLog.d(tag, "[onPrepare],download is calling, fullSize$fullSize")
        isUpdating = true
        // AIUnit准备阶段，由于真正进度还没回来，保证UI显示0%
        listener?.onDownloading(0, 1)
    }

    /**
     * 下载开始
     * @param fullSize, 完整大小
     * @param offsetSize, 已下载大小
     */
    override fun onStart(fullSize: Long, offsetSize: Long) {
        GLog.d(tag, "[onStart],download is calling start")
        isUpdating = true
    }

    /**
     * 下载中
     * @param fullSize, 完整大小
     * @param offsetSize, 已下载大小
     * @param speed, B/s
     */
    override fun onProgress(fullSize: Long, offsetSize: Long, speed: Long) {
        listener?.onDownloading(offsetSize, fullSize)
    }

    /**
     * 安装中，下载成功时回调
     */
    override fun onInstall() {
        GLog.d(tag, "[onInstall]")
        listener?.onDownloadSuccess()
    }

    /**
     * 安装成功时回调
     * @param fullSize, 完整大小
     * @param downloadSize, 已下载大小
     * @param fromBreakpoint 从断点开始
     */
    override fun onSuccess(fullSize: Long, downloadSize: Long, fromBreakpoint: Boolean) {
        GLog.d(
            tag, "[onSuccess] fullSize:$fullSize, downloadSize:$downloadSize, fromBreakpoint:$fromBreakpoint"
        )
        isUpdating = false
        // 下载成功后，需要写下载的config配置，防止重新下载
        applicationContext?.let {
            updateDownloadConfig(it)
        }
        listener?.onInstallSuccess()
        installFinishCallback?.invoke(ERROR_CODE_NONE)
    }

    /**
     * 失败时回调
     * @param err 错误码
     */
    override fun onFail(err: Int) {
        GLog.d(tag, "[onFail],code is $err")
        applicationContext?.let {
            updateDownloadConfig(it)
        }
        isUpdating = false
        when (err) {
            ErrorCode.CODE_NO_NEED_DOWNLOAD -> installFinishCallback?.invoke(ERROR_CODE_NO_NEED_DOWNLOAD)
            ErrorCode.CODE_DOWNLOADING_FAIL -> listener?.onDownloadFail()
            ErrorCode.CODE_INSTALL_FAIL -> listener?.onInstallFail()
            ErrorCode.CODE_DOWNLOAD_CONFIRM_USER_CANCEL, ErrorCode.CODE_DOWNLOAD_CELLULAR_USER_CANCEL -> {
                listener?.onDownloadCancel()
                cancelCallback?.invoke(CancelStage.NOT_DOWNLOAD)
            }

            else -> listener?.onDownloadFail(err)
        }
        /**
         *  更新模型的前提下，用户点击取消，直接使用消除功能
         *  CODE_DOWNLOAD_CONFIRM_USER_CANCEL:安装时的取消
         *  CODE_DOWNLOAD_CELLULAR_USER_CANCEL:网络被用户拒接而取消
         */
        if (isUpdate && ((err == ErrorCode.CODE_DOWNLOAD_CONFIRM_USER_CANCEL) || (err == ErrorCode.CODE_DOWNLOAD_CELLULAR_USER_CANCEL))) {
            cancelCallback?.invoke(CancelStage.UPDATING)
        }
    }

    /**
     * 下载中被取消
     */
    override fun onCancel() {
        GLog.d(tag, "[onCancel],download is calling cancel")
        isUpdating = false
        listener?.onDownloadCancel()
        cancelCallback?.invoke(CancelStage.DOWNLOADING)
    }


    /**
     * 仅仅移除请求，避免内存泄漏，但AIUnit下载任务还在继续
     */
    fun removeDownloadRequestIfNeed() {
        if (isUpdating.not()) {
            return
        }
        applicationContext = null
        cancelCallback = null
        installFinishCallback = null
        isUpdating = false
        AppScope.launch {
            AIDownload.removeOnly(buildSceneName())
        }
    }

    companion object {
        // 下载错误码：无效值
        const val ERROR_CODE_NONE = 0

        // 下载错误码：当前本地是最新，无需下载
        const val ERROR_CODE_NO_NEED_DOWNLOAD = 1
    }
}

/**
 * 取消阶段
 * 描述用户在哪个阶段执行了主动取消
 */
enum class CancelStage {
    /**
     * 还没下载就点了取消
     */
    NOT_DOWNLOAD,

    /**
     * 下载中点了取消
     */
    DOWNLOADING,

    /**
     * 更新中点了取消
     */
    UPDATING
}
