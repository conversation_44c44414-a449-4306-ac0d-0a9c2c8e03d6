/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OperatingSection
 ** Description:
 ** Version: 1.0
 ** Date : 2024/1/26
 ** Author: wudanyang
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  wudanyang                      2024/1/26    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.operating

import android.animation.ObjectAnimator
import android.content.res.Resources
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.business_lib.template.editor.EditorAnimationUtils.createAlphaAnimator
import com.oplus.gallery.business_lib.template.editor.EditorAppUiConfig
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig.isEditorLandscape
import com.oplus.gallery.foundation.archv2.ISectionBus
import com.oplus.gallery.foundation.archv2.section.SectionSpec
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.photoeditor.EditingFragment
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.common.EnterAlpha
import com.oplus.gallery.photoeditor.common.ExitAlpha
import com.oplus.gallery.photoeditor.editingvvm.BaseEditingSection
import com.oplus.gallery.photoeditor.editingvvm.EditingVM
import com.oplus.gallery.photoeditor.editingvvm.TObserver
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.REPLY_TOPIC_OPERATING_ACTION
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_OPERATING_COMPARE_TARGET
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_OPERATING_ANIMATOR_UPDATE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_OPERATING_VIEW_STATE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OperatingAction.TOPIC_OPERATING_SAVE_STATUS
import com.oplus.gallery.photoeditor.editingvvm.notifyOnce
import com.oplus.gallery.photoeditor.editingvvm.subscribeT
import com.oplus.gallery.photoeditor.util.isBackgroundEnable
import com.oplus.gallery.photoeditor.util.isClickable
import com.oplus.gallery.photoeditor.util.isEnable
import com.oplus.gallery.photoeditor.util.isVisible
import com.oplus.gallery.photoeditor.widget.EditorBottomActionBar
import com.oplus.gallery.photoeditor.widget.EditorCompareButton
import com.oplus.gallery.standard_lib.ui.util.DoubleClickUtils

/**
 * 负责通用操作相关的UI与交互
 * 管理编辑页的取消、保存、对比、撤销、恢复、勾、叉等UI
 * @param sectionBus 总线
 */
internal class OperatingSection(
    sectionBus: ISectionBus<EditingFragment, EditingVM>
) : BaseEditingSection(sectionBus) {

    /**
     * 操作栏
     */
    private var operatingBar: EditorBottomActionBar? = null

    /**
     * 对比按钮
     */
    private var compareButton: EditorCompareButton? = null

    /**
     * 进退场动画实例
     */
    private var enterAnimator: ObjectAnimator? = null
    private var exitingAnimator: ObjectAnimator? = null

    /**
     * 操作栏ViewState监听
     */
    private val operatingViewStateObserver: TObserver<OperatingViewState> = {
        updateCompareBtn(it)
        updateOperatingBar(it)
    }

    /**
     * 操作栏进退场动画参数更新监听
     */
    private val operatingAnimatorUpdateObserver: TObserver<OperatingAnimatorUpdater> = {
        when (it.type) {
            AnimatorUpdateType.ENTERING -> {
                clearEnterAnimator()
                addEnterAnimator(createAlphaAnimator(operatingBar, true, it.duration, it.interpolator, it.delay))
            }

            AnimatorUpdateType.EXITING -> {
                clearExitAnimator()
                addExitAnimator(createAlphaAnimator(operatingBar, false, it.duration, it.interpolator, it.delay))
            }

            AnimatorUpdateType.RESET -> {
                clearEnterAnimator()
                clearExitAnimator()
                resetAnimators()
            }
        }
    }


    private val saveStatusObserver: TObserver<SaveStatus> = {
        when (it) {
            SaveStatus.SAVE_BEGIN -> showLoadingDialog(SHOW_SAVE_LOADING_DELAY)
            SaveStatus.SAVE_COMPLETE -> hideLoadingDialog(0)
        }
    }

    /**
     * @return 容器layoutID
     */
    override fun getContainerViewID(): Int {
        return R.id.title_bar_container
    }

    /**
     * @param config
     * @return 内容layoutID
     */
    override fun getContentLayoutId(config: AppUiResponder.AppUiConfig): Int {
        return R.layout.picture3d_editor_preview_bottom_action_bar_layout
    }

    private fun updateCompareBtn(state: OperatingViewState) {
        compareButton?.setVisibleWithAnimator(state.compareBtnFlags.isVisible(), state.compareBtnVisibleWithAnimator)
    }

    private fun updateOperatingBar(state: OperatingViewState) {
        operatingBar?.apply {
            setActionType(state.operatingBarType)

            // 这里即使设置显示title, 后面如果要显示restore按钮等占据了显示位, 也会隐藏title的
            val titleVisible = state.titleTextFlags.isVisible()
            setTitleVisible(titleVisible)

            // 标题栏中间区域显示规则：还原按钮和撤销恢复按钮不会同时出现，它俩分别与标题切换显示
            val showUndoRedo = state.undoBtnFlags.isVisible() || state.redoBtnFlags.isVisible()
            val showRestore = state.restoreBtnFlags.isVisible()
            val restoreEnable = state.restoreBtnFlags.isEnable()
            val restoreChangeWithAnimator = state.restoreChangeWithAnimator
            GLog.d(TAG, "[updateOperatingBar] showUndoRedo: $showUndoRedo, restoreBtn: $showRestore, restoreEnable: $restoreEnable")
            if (showUndoRedo.not()) {
                if (restoreChangeWithAnimator == EditorBottomActionBar.WITH_ANIMATION) {
                    onRestoreChanged(showRestore)
                } else {
                    onRestoreChangedWithoutAnimator(showRestore)
                }
            }

            if (showRestore.not()) {
                GLog.d(
                    TAG,
                    "[updateOperatingBar] undoEnable: ${state.undoBtnFlags.isEnable()}, redoEnable: ${state.redoBtnFlags.isEnable()}"
                )
                setUndoViewEnable(state.undoBtnFlags.isEnable())
                setRedoViewEnable(state.redoBtnFlags.isEnable())
                setUndoRedoBarVisible(showUndoRedo)
            }

            if (showUndoRedo.not() && showRestore.not()) {
                if (state.titleText != Resources.ID_NULL) {
                    setTitle(context.resources.getString(state.titleText))
                } else {
                    setTitle(TextUtil.EMPTY_STRING)
                }
            }

            if (state.doneBtnText != Resources.ID_NULL) {
                setDoneText(context.resources.getString(state.doneBtnText))
            }

            if (state.saveBtnText != Resources.ID_NULL) {
                setSaveText(context.resources.getString(state.saveBtnText))
            }
            setDoneTextEnable(state.doneBtnFlags.isEnable())
            setDoneTextClickable(state.doneBtnFlags.isClickable())
            setCancelTextEnable(state.cancelFlags.isEnable())
            setCancelTextClickable(state.cancelFlags.isClickable())
            setCancelTextBackgroundEnable(state.cancelFlags.isBackgroundEnable())
            setSaveTextEnable(state.saveBtnFlags.isEnable())
            setSaveTextBackgroundEnable(state.saveBtnFlags.isBackgroundEnable())
            setRecoverTextEnable(state.recoverFlags.isEnable())
            setRecoverTextVisible(state.recoverFlags.isVisible())
            setRecoverTextBackgroundEnable(state.recoverFlags.isBackgroundEnable())
            setRestoreViewEnable(restoreEnable)
        }
    }

    override fun onCreate() {
        super.onCreate()
        initViews()
        initAnimators()
        subscribeTopics()
    }

    private fun subscribeTopics() {
        vBus.apply {
            subscribeT(TOPIC_OPERATING_VIEW_STATE, operatingViewStateObserver)
            subscribeT(TOPIC_OPERATING_SAVE_STATUS, saveStatusObserver)
            subscribeT(TOPIC_OPERATING_ANIMATOR_UPDATE, operatingAnimatorUpdateObserver)
        }
    }

    private fun initViews() {
        operatingBar = (sectionContentView as? EditorBottomActionBar)?.also {
            it.setOnActionItemClickListener { v ->
                if (DoubleClickUtils.isFastDoubleClick()) {
                    return@setOnActionItemClickListener
                }
                // 按钮点击事件分发
                vBus.notifyOnce(REPLY_TOPIC_OPERATING_ACTION, OperatingAction(v?.id ?: -1))
            }
        }
        compareButton = sectionBus.rootView.findViewById<EditorCompareButton>(R.id.compare_button)?.also {
            it.setCompareButtonTouchEventListener(object : EditorCompareButton.OnCompareButtonTouchEventListener {
                override fun onCompareTouchDown() {
                    vBus.notifyOnce(TOPIC_OPERATING_COMPARE_TARGET, CompareTarget.PreEffect)
                }

                override fun onCompareTouchUp() {
                    vBus.notifyOnce(TOPIC_OPERATING_COMPARE_TARGET, CompareTarget.RecoverEffect)
                }
            })
        }
    }

    private fun initAnimators() {
        // operatingBar入场动画
        enterAnimator = createAlphaAnimator(operatingBar, true, EnterAlpha.DURATION, EnterAlpha.INTERPOLATOR, EnterAlpha.START_DELAY)
        enterAnimator?.let {
            addEnterAnimator(it)
        }

        // operatingBar退场动画
        exitingAnimator = createAlphaAnimator(operatingBar, false, ExitAlpha.DURATION, ExitAlpha.INTERPOLATOR)
        exitingAnimator?.let {
            addExitAnimator(it)
        }
    }

    /**
     * 重置进退场动画
     */
    private fun resetAnimators() {
        enterAnimator?.let {
            addEnterAnimator(it)
        }
        exitingAnimator?.let {
            addExitAnimator(it)
        }
    }

    override fun onBackPressed(isBackHandled: Boolean): Boolean {
        if (DoubleClickUtils.isFastDoubleClick()) {
            return true
        }
        GLog.d(TAG, "onBackPressed: isBackHandled = $isBackHandled")
        vBus.notifyOnce(
            REPLY_TOPIC_OPERATING_ACTION, OperatingAction(R.id.operation_back_key)
        )
        return true
    }

    override fun onUiConfigChanged(baseActivity: BaseActivity, layoutId: Int, config: EditorAppUiConfig) {
        super.onUiConfigChanged(baseActivity, layoutId, config)
        val isPortrait = !isEditorLandscape(baseActivity.getCurrentAppUiConfig())
        operatingBar?.setTextViewStyle(isPortrait)
    }

    override fun onDestroy() {
        super.onDestroy()
        vBus.apply {
            unsubscribe(TOPIC_OPERATING_VIEW_STATE, operatingViewStateObserver)
            unsubscribe(TOPIC_OPERATING_SAVE_STATUS, saveStatusObserver)
            unsubscribe(TOPIC_OPERATING_ANIMATOR_UPDATE, operatingAnimatorUpdateObserver)
        }
    }

    internal companion object {
        const val TAG = "OperatingSection"
        val SPEC = SectionSpec(OperatingSection::class.java)

        /**
         * 保存：延时300毫秒显示loading框
         */
        private const val SHOW_SAVE_LOADING_DELAY = 300L
    }
}