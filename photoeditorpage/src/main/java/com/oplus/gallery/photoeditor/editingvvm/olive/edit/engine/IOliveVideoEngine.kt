/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:  IOliveVideoEngine
 ** Description: 80377011 created
 ** Version: 1.0
 ** Date : 2024/7/19
 ** Author: 80377011
 **
 ** ---------------------Revision History: ---------------------
 **  <author>       <date>      <version>       <desc>
 **  80377011      2024/7/19      1.0     NEW
 ****************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.olive.edit.engine

import android.graphics.Bitmap
import android.graphics.ColorSpace
import android.graphics.SurfaceTexture
import android.net.Uri
import android.util.Rational
import android.util.Size
import android.view.SurfaceView
import com.oplus.gallery.business_lib.menuoperation.item.exportvideo.IExportVideoCallback
import com.oplus.gallery.foundation.codec.extend.HdrTransformData
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.adjust.IVideoAdjustEditor
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.edge.IVideoEdgeEditor
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.engine2.VideoInfo
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.filter.IVideoFilterEditor
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.transform.IVideoTransformEditor
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.watermark.IVideoWatermarkEditor
import kotlinx.coroutines.flow.Flow
import java.util.Hashtable

/**
 * 视频资源处理器
 * 当前支持情况:
 * 1.混合媒体资源文件，一个文件的一部分是视频资源; 如olive图
 * 2.单个视频资源, 如mp4文件等，
 *
 * 注意：虽然类名叫olive，实际此Engine处理的是单纯的视频操作和olive本质上不挂钩
 */
interface IOliveVideoEngine {
    /**
     * 文件的Uri：映射的文件，必须包含视频信息
     * 即：
     * 1.混合媒体资源文件，一个文件的一部分是视频资源
     * 2.单个视频资源, 如mp4文件等
     *
     * 详见: [VideoSourceDescriptor]
     */
    val sourceUri: Uri?

    /**
     * 针对 [sourceUri]资源的描述
     * 详见：[VideoSourceDescriptor]
     */
    val sourceDescriptor: VideoSourceDescriptor?

    /**
     * 是否调用了play接口，调用stop后会置为false
     */
    val isPlayed: Boolean

    /**
     * 是否初始成功了
     */
    val hasInit: Boolean

    // REGION: ---------------- 初始化和销毁 ----------------
    /**
     * 初始化视频
     * @param uri 视频路径
     * @param microVideo olive的视频相关信息
     * @param oliveTransformConfig 针对HDR视频下变换的配置
     */
    fun initFromVideo(
        sourceUri: Uri,
        sourceDescriptor: VideoSourceDescriptor = VideoSourceDescriptor.NORMAL,
        oliveTransformConfig: OliveTransformConfig
    ): String?

    /**
     * 资源销毁
     */
    fun destroy()

    // ENDREGION


    // REGION: ---------------- 视频预览 ----------------
    /**
     * getVideoSize
     */
    fun getVideoSize(): Size

    /**
     * gatVideoFps
     */
    fun gatVideoFps(): Rational

    /**
     * 更改视频帧率
     * @param fps 要更改的帧率值
     */
    fun changeVideoFps(fpsRational: Rational): Boolean

    /**
     * 设置预览的window
     * @return SurfaceView
     */
    fun setupPreviewWindow(): SurfaceView?

    /**
     * 设置SurfaceTexture
     * @param textureProvider ISurfaceTextureProvider
     */
    fun setPreviewSurfaceTexture(surfaceTexture: SurfaceTexture?): Boolean

    /**
     * 更新SurfaceTexture
     * @param textureProvider ISurfaceTextureProvider todo
     * @param sizeLevel 尺寸规格，表示以1/sizeLevel的分辨率进行输出，SurfaceTexture的尺寸不变
     */
    fun updatePreviewSurfaceTexture(surfaceTexture: SurfaceTexture?, sizeLevel: Int = 1): Boolean

    /**
     * 播放
     * @param onFirstFrameReady 第一帧准备的回调
     */
    fun play(onFirstFrameReady: (() -> Unit)? = null): Boolean

    /**
     * 播放的时间范围
     * @param start 开始时间
     * @param end 结束时间
     */
    fun setPlayRange(start: Long, end: Long)

    /**
     * pause
     */
    fun pause()

    /**
     * resume
     */
    fun resume()

    /**
     * stop
     */
    fun stop()

    /**
     * setMute
     * @param isMute 是否静音
     */
    fun setMute(isMute: Boolean)

    /**
     * seekTo
     * @param time seek的时间
     * @return Boolean
     */
    fun seekTo(time: Long): Boolean

    /**
     * @param onSeekingListener seek的回调 positionUs回调的时间搓，单位为us
     * seek的回退
     */
    fun setSeekingListener(onSeekingListener: ((positionUs: Long) -> Unit)?)

    /**
     * 设置返回seek帧的Listener。
     * @param capturedFrameListener seekComplete后，sdk返回当前帧图的 listener
     */
    fun setSeekCapturedFrameListener(capturedFrameListener: ((positionUs: Long, Bitmap?) -> Unit)?)

    /**
     * 获取视频时长
     * @return 视频时长
     */
    fun getDuration(): Long

    /**
     * 获取带特效的时间轴缩略图
     * 需要在主线程调用
     * @param timeUs 时间点，微妙
     * @param maxLength 缩略图最大长度
     * @param config: 指定像素格式
     * @param colorSpace: 指定色彩空间
     * @return 返回时间点与缩略图映射
     */
    suspend fun grabThumbnailAsync(timeUs: Long, maxLength: Int, config: Bitmap.Config? = null, colorSpace: ColorSpace? = null): Pair<Long, Bitmap?>

    /**
     * 从原视频中抽帧，不带效果
     * 需要在IO线程中调用
     * @param timeUs 时间点，微妙
     * @param isHdrVideoOlive olive视频是否是hdr视频
     */
    fun retrieveFrameFromSource(timeUs: Long, isHdrVideoOlive: Boolean): Bitmap?

    // ENDREGION


    // REGION: ---------------- 视频编辑 ----------------
    /**
     * 滤镜
     * @return IOliveVideoFilterEditor
     */
    fun getFilterEditor(): IVideoFilterEditor

    /**
     * 调节
     * @return IOliveVideoAdjustEditor
     */
    fun getAdjustEditor(): IVideoAdjustEditor

    /**
     * 旋转裁剪
     * @return IOliveVideoTransformEditor
     */
    fun getTransformEditor(): IVideoTransformEditor?

    /**
     * 视频模糊边缘编辑
     * @return IOliveVideoEdgeEditor
     */
    fun getVideoEdgeEditor(): IVideoEdgeEditor?

    /**
     * 水印
     * @return IOliveVideoWatermarkEditor
     */
    fun getWatermarkEditor(): IVideoWatermarkEditor?

    /**
     * 获取无水印视频帧尺寸
     */
    fun getWithoutWatermarkVideoSize(): Size

    /**
     * 移除所有的特效
     */
    fun removeAllVideoFx()

    /**
     * 是否有特效
     */
    fun isHasVideoFx(): Boolean

    // ENDREGION

    /**
     * 获取回调接口
     * @param exportVideoCallback IExportVideoCallback
     */
    fun registerExportVideoCallback(exportVideoCallback: IExportVideoCallback)

    /**
     * 释放回调资源
     */
    fun unRegisterExportVideoCallback()

    // REGION: ---------------- 视频导出 ----------------
    /**
     * 导出视频
     * @param filePath 路径
     */
    fun exportVideo(filePath: String?, configuration: Hashtable<String, Any>? = null)

    // ENDREGION
    fun retrieveInfo(): VideoInfo
}

interface ISurfaceTextureProvider {
    fun getSurfaceTexture(): SurfaceTexture?
}

/**
 * 针对Olive视频下变换的配置类。提供视频在播放、抽帧等场景时的下变换配置
 *
 * @param hdrTransformData 下变换参数
 * @param needFXTransformToSdr 是否需要美摄CustomFX转换成sdr
 * @param forceSdr 强制设置为SDR的timeline，默认为false，只有GIF场景设置为true
 * @param hdrSdrRatioFunc 获取当前HDR/SDR比例的回调方法，默认实现为回调1.0f，即显示sdr效果
 */
data class OliveTransformConfig(
    val hdrTransformData: HdrTransformData?,
    val needFXTransformToSdr: Boolean = false,
    val forceSdr: Boolean = false,
    val hdrSdrRatioFunc: () -> Float = { 1.0f }
)

/**
 * 视频源描述
 * 释义：这里是以文件资源本身的结构区分，线上线下等空间等维度不在范围内
 * 职责：描述可以播放的资源文件内的视频必要信息
 */
sealed class VideoSourceDescriptor {

    /**
     * 常规/常规视频文件描述：MP4,avi等 ，播放器使用标准的解析方式即可解码出对应播放内容
     */
    object NORMAL : VideoSourceDescriptor()

    /**
     * 混合视频信息描述：
     * 例如：
     * 1.源文件里包含可以播放的内容的位置
     */
    class MIX(
        val offset: Long, // 视频区域开始的位置
        val length: Long, // 从offset + length 为视频源区域结束位置
    ) : VideoSourceDescriptor()
}