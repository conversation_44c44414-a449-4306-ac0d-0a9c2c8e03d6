/*
 * Copyright (C) 2010 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.oplus.gallery.photoeditor.gl.texure;

import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Paint.FontMetricsInt;
import android.text.Layout.Alignment;
import android.text.StaticLayout;
import android.text.TextPaint;
import android.text.TextUtils;
import androidx.annotation.ColorInt;
import androidx.annotation.NonNull;
import com.oplus.gallery.foundation.util.typeface.TypefaceUtil;


// StringTexture is a texture shows the content of a specified String.
//
// To create a StringTexture, use the newInstance() method and specify
// the String, the font size, and the color.
public final class StringTexture extends CanvasTexture {
    private final String mText;
    private final TextPaint mPaint;
    private final FontMetricsInt mMetrics;
    private StaticLayout mStaticLayout;

    private StringTexture(String text, TextPaint paint, FontMetricsInt metrics, int width,
                          int height) {
        super(width, height);
        mText = text;
        mPaint = paint;
        mMetrics = metrics;
    }

    private StringTexture(String text, TextPaint paint, FontMetricsInt metrics, int width,
                          int height, StaticLayout sl) {
        super(width, height);
        mText = text;
        mPaint = paint;
        mMetrics = metrics;
        mStaticLayout = sl;
    }

    public static TextPaint getDefaultPaint(float textSize, int color) {
        TextPaint paint = new TextPaint();
        paint.setTextSize(textSize);
        paint.setAntiAlias(true);
        paint.setColor(color);
        return paint;
    }

    public static StringTexture newInstance(String text, float textSize, int color) {
        return newInstance(text, getDefaultPaint(textSize, color));
    }

    public static StringTexture newInstance(String text, float textSize, int color, boolean isBold) {
        TextPaint paint = getDefaultPaint(textSize, color);
        return newInstance(text, paint, isBold);
    }

    public static StringTexture newInstance(String text, float textSize, int color,
                                            float lengthLimit, boolean isBold) {
        TextPaint paint = getDefaultPaint(textSize, color);
        if (lengthLimit > 0) {
            text = TextUtils.ellipsize(
                text, paint, lengthLimit, TextUtils.TruncateAt.END).toString();
        }
        return newInstance(text, paint, isBold);
    }

    public static StringTexture newInstance(String text, @NonNull TextPaint paint) {
        return newInstance(text, paint, false);
    }

    public static StringTexture newInstance(String text, @NonNull TextPaint paint, boolean isBold) {
        if (isBold) {
            paint.setTypeface(TypefaceUtil.INSTANCE.getSansSerifMedium());
        }

        FontMetricsInt metrics = paint.getFontMetricsInt();
        int width = (int) Math.ceil(paint.measureText(text));
        int height = metrics.bottom - metrics.top;
        // The texture size needs to be at least 1x1.
        if (width <= 0) {
            width = 1;
        }
        if (height <= 0) {
            height = 1;
        }
        return new StringTexture(text, paint, metrics, width, height);
    }

    //multiple lines, default align center
    public static StringTexture newInstance(String text, int width, TextPaint paint, Alignment alignment) {
        FontMetricsInt metrics = paint.getFontMetricsInt();
        StaticLayout staticLayout = StaticLayout.Builder
            .obtain(text, 0, text.length(), paint, width)
            .setAlignment(alignment)
            .setLineSpacing(0, 1.2f)
            .setIncludePad(false)
            .build();
        int height = staticLayout.getHeight();
        return new StringTexture(text, paint, metrics, width, height, staticLayout);
    }

    //for recycle
    public int getTextHeight() {
        if (mStaticLayout != null) {
            return mStaticLayout.getHeight();
        }
        return 0;
    }

    @Override
    protected void onDraw(Canvas canvas, Bitmap backing) {
        if (mStaticLayout != null) {
            mStaticLayout.draw(canvas);
        } else {
            canvas.translate(0, -mMetrics.top);
            canvas.drawText(mText, 0, 0, mPaint);
        }
    }

    public void setTextColor(@ColorInt int color) {
        mPaint.setColor(color);
    }

    public int getTextColor() {
        return mPaint.getColor();
    }
}
