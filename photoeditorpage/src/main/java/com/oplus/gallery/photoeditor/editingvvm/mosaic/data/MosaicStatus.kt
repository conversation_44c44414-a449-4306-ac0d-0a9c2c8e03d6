/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - MosaicStatus.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/6/24
 * Author: zhangjisong
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * zhangjisong      2024/6/24        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.mosaic.data

import android.graphics.Bitmap
import android.graphics.RectF
import android.util.Size
import androidx.annotation.FloatRange
import com.oplus.gallery.business_lib.template.editor.data.EditorMenuItemViewData
import com.oplus.gallery.photoeditor.editingvvm.mosaic.data.MosaicStatus.AutoMosaicPaintedStatus.Trigger
import com.oplus.gallery.photoeditor.editingvvm.mosaic.data.MosaicStatus.AutoMosaicPaintedStatus.Trigger.PrivacyMosaic
import com.oplus.gallery.photoeditor.editingvvm.mosaic.data.MosaicStatus.AutoMosaicPaintedStatus.Trigger.TouchRegion
import com.oplus.gallery.photoeditor.editingvvm.mosaic.data.MosaicStatus.BrushStyle.Blur
import com.oplus.gallery.photoeditor.editingvvm.mosaic.data.MosaicStatus.BrushStyle.Burnished
import com.oplus.gallery.photoeditor.editingvvm.mosaic.data.MosaicStatus.BrushStyle.Camouflage
import com.oplus.gallery.photoeditor.editingvvm.mosaic.data.MosaicStatus.BrushStyle.Dot
import com.oplus.gallery.photoeditor.editingvvm.mosaic.data.MosaicStatus.BrushStyle.Pixel
import com.oplus.gallery.photoeditor.editingvvm.mosaic.data.MosaicStatus.BrushStyle.PolkaDot
import com.oplus.gallery.photoeditor.editingvvm.mosaic.data.MosaicStatus.OperationMode.Auto
import com.oplus.gallery.photoeditor.editingvvm.mosaic.data.MosaicStatus.OperationMode.Manual
import com.oplus.gallery.photoeditor.editingvvm.mosaic.data.MosaicStatus.PrivacyMosaic.InUsing
import com.oplus.gallery.photoeditor.editingvvm.mosaic.data.MosaicStatus.PrivacyMosaic.NotSupport
import com.oplus.gallery.photoeditor.editingvvm.mosaic.data.MosaicStatus.PrivacyMosaic.Unused

/**
 * 马赛克状态，一般由 VM 产生，通过 Bus 通知给 UI。有粘滞保留最新的状态。
 */
internal sealed interface MosaicStatus {

    /**
     * 状态名称，用于区分哪种类型的状态
     */
    val statusName: String

    /**
     * 操作模式，主要分类如下：
     *
     * - [Manual] 手动模式
     * - [Auto] 自动模式
     */
    enum class OperationMode : MosaicStatus {
        /**
         * 手动操作模式
         */
        Manual,

        /**
         * 自动操作模式，会自动检测可打码区域和隐私区域
         */
        Auto;

        override val statusName: String = "OperationMode"

        /**
         * 扩展函数：是否为手动模式 [Manual]
         */
        val isManualMode: Boolean get() = this == Manual

        /**
         * 扩展函数：是否为自动模式 [Auto]
         */
        val isAutoMode: Boolean get() = this == Auto
    }

    /**
     * 画笔样式表，用于描述不同 [OperationMode] 下对应的画笔样式列表 [List]<[EditorMenuItemViewData]>
     *
     * @param styles 画笔样式表映射
     */
    data class BrushStyles(
        val styles: Map<OperationMode, List<EditorMenuItemViewData>?>
    ) : MosaicStatus {

        override val statusName: String = "BrushStyles"

        companion object {
            /**
             * 空的画笔样式表，一般当做默认值使用
             */
            val Empty: BrushStyles = BrushStyles(emptyMap())
        }
    }

    /**
     * 隐私马赛克功能状态，主要分类如下：
     *
     * - [NotSupport] 不支持一键隐私打码
     * - [InUsing] 支持一键隐私打码并且正在使用中
     * - [Unused] 支持一键隐私打码但未使用
     */
    enum class PrivacyMosaic : MosaicStatus {
        /**
         * 不支持一键隐私打码功能
         */
        NotSupport,

        /**
         * 支持一键隐私打码功能并且正在使用中
         */
        InUsing,

        /**
         * 支持一键隐私打码功能但未使用
         */
        Unused;

        override val statusName: String = "PrivacyMosaic"

        /**
         * 扩展函数：是否正在使用一键隐私打码
         */
        val isUsing: Boolean get() = this == InUsing

        /**
         * 扩展函数：是否支持一键隐私打码功能
         */
        val isSupported: Boolean get() = this != NotSupport
    }

    /**
     * 马赛克画笔风格，主要分类如下：
     *
     * - [Pixel] 像素风格
     * - [Blur] 模糊风格
     * - [Dot] 圆点风格
     * - [Burnished] 笔刷风格
     * - [PolkaDot] 斑点风格，暂未启用
     * - [Camouflage] 迷彩风格，暂未启用
     */
    enum class BrushStyle(var pattern: String) : MosaicStatus {
        /**
         * 像素风格
         */
        Pixel("mosaic"),

        /**
         * 模糊风格
         */
        Blur("blur"),

        /**
         * 圆点风格
         */
        Dot("dot"),

        /**
         * 笔刷风格
         */
        Burnished("stroke_3"),

        /**
         * 斑点风格，暂未启用
         */
        PolkaDot("polka_dot"),

        /**
         * 迷彩风格，暂未启用
         */
        Camouflage("camouflage");

        override val statusName: String = "BrushStyle"
    }

    /**
     * 自动马赛克绘制状态，用于描述由最近由 [trigger] 触发，使用 [style] 风格，打码了 [regions] 哪些区域
     *
     * @param trigger 最近一次触发绘制的源头 [Trigger]
     * @param style 最近一次触发绘制时使用的画笔风格 [BrushStyle]
     * @param regions 最近一次触发绘制的区域 [Set]<[RectF]>
     */
    data class AutoMosaicPaintedStatus(
        val trigger: Trigger,
        val style: BrushStyle,
        val regions: Set<RegionData>
    ) : MosaicStatus {

        override val statusName: String get() = "AutoMosaicPaintedStatus"

        /**
         * 自动打码的触发源，主要分类如下：
         *
         * - [TouchRegion] 来自手指滑选、点击
         * - [PrivacyMosaic] 来自一键隐私马赛克功能
         */
        enum class Trigger {
            /**
             * 来自手指滑选、点击
             */
            TouchRegion,

            /**
             * 来自一键隐私马赛克功能
             */
            PrivacyMosaic
        }
    }

    /**
     * 隐私区域数据
     *
     * @param originSize 检测图原始宽高
     * @param polygon 隐私区域原始多边形区域，值域一般为 [[0, 1]]，但可能超出该范围。数据布局为：
     * ```Kotlin
     * [
     *     leftTopX, leftTopY,
     *     rightTopX, rightTopY,
     *     rightBottomX, rightBottomY,
     *     leftBottomX, leftBottomY
     * ]
     * ```
     */
    data class RegionData(
        val originSize: Size,
        val polygon: FloatArray
    ) {

        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (javaClass != other?.javaClass) return false

            other as RegionData

            if (originSize != other.originSize) return false
            if (!polygon.contentEquals(other.polygon)) return false

            return true
        }

        override fun hashCode(): Int {
            var result = originSize.hashCode()
            result = HASH_CODE_MAGIC_NUMBER * result + polygon.contentHashCode()
            return result
        }

        override fun toString(): String {
            return "RegionData(originSize=$originSize, polygon=${polygon.contentToString()})"
        }

        private companion object {
            private const val HASH_CODE_MAGIC_NUMBER = 31
        }
    }

    /**
     * 隐私检测状态，用于自动打码、一键隐私打码功能，包含检测功能下载状态。
     */
    sealed interface SensitiveDetection : MosaicStatus {

        override val statusName: String get() = "SensitiveDetection"

        /**
         * 是否支持隐私区域检测功能
         */
        val isSupport: Boolean get() = this !is NotSupport

        /**
         * 是否正在检测中
         */
        val isDetecting: Boolean get() = this == Detecting

        /**
         * 是否已检测出可打码区域
         */
        val isSensitive: Boolean get() = this is Sensitive

        /**
         * 是否没有检测出可打码区域
         */
        val isNotSensitive: Boolean get() = this == NotSensitive

        /**
         * 内容是否无效
         */
        val isInvalidContent: Boolean get() = this == InvalidContent

        /**
         * 是否正在下载
         */
        val isDownloading: Boolean get() = this is Downloading

        /**
         * 是否正在安装
         */
        val isInstalling: Boolean get() = this == Installing

        /**
         * 是否需要下载
         */
        val needDownload: Boolean get() = this == NeedDownload

        /**
         * 是否需要检测
         */
        val needDetect: Boolean get() = this == NeedDetect

        /**
         * 不支持隐私区域检测
         */
        enum class NotSupport : SensitiveDetection {
            /**
             * 设备不支持
             */
            UnsupportedDevice,

            /**
             * 内部检测器异常
             */
            DetectorError
        }

        /**
         * 正在检测中
         */
        data object Detecting : SensitiveDetection

        /**
         * 无效内容，无法获取被检测的图片
         */
        data object InvalidContent : SensitiveDetection

        /**
         * 没有任何可打码区域、隐私区域
         */
        data object NotSensitive : SensitiveDetection

        /**
         * 需要下载才能使用检测功能
         */
        data object NeedDownload : SensitiveDetection

        /**
         * 功能已下载，正在安装中
         */
        data object Installing : SensitiveDetection

        /**
         * 功能下载、安装完毕，需要触发检测。
         *
         * 此处仅为瞬时状态，因为该状态发布后，VM 会立即触发检测，UI 无需关心
         */
        data object NeedDetect : SensitiveDetection

        /**
         * 功能正在下载中
         *
         * @param progress 下载进度百分比，值域为 [[0f, 1f]]
         */
        data class Downloading(@FloatRange(from = 0.0, to = 1.0) val progress: Float) : SensitiveDetection

        /**
         * 检测出可打码区域
         *
         * @param preview 可打码区域的预览图，可能为 null
         * @param default 隐私区域，一键隐私打码功能时，会自动打码该区域
         * @param options 其他可选打码区域
         */
        data class Sensitive(
            val preview: Bitmap? = null,
            val default: Set<RegionData> = emptySet(),
            val options: Set<RegionData> = emptySet()
        ) : SensitiveDetection {
            /**
             * 是否没有可打码区域，统计的区域包含隐私区域 [default] 和可选区域 [options]
             */
            val isEmpty: Boolean get() = default.isEmpty() && options.isEmpty()

            /**
             * 是否有可打码区域，统计的区域包含隐私区域 [default] 和可选区域 [options]
             */
            val isNotEmpty: Boolean get() = default.isNotEmpty() || options.isNotEmpty()

            /**
             * 所有可打码的区域，统计的区域包含隐私区域 [default] 和可选区域 [options]
             */
            val all: Set<RegionData> by lazy { default + options }

            /**
             * 释放内部资源，主要是释放 [preview]
             */
            fun release(): Unit = preview?.recycle() ?: Unit

            companion object {
                /**
                 * 没有任何可打码区域的 [Sensitive]，一般用于默认值
                 */
                val None: Sensitive = Sensitive()
            }
        }
    }
}