/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OliveVideoFilterEditorImp
 ** Description: OliveVideoFilterEditorImp
 ** Version: 1.0
 ** Date : 2024/7/25
 ** Author: dingyong@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  dingyong@Apps.Gallery3D      2024/07/25    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.photoeditor.editingvvm.olive.edit.filter

import android.text.TextUtils
import com.meicam.sdk.NvsTimeline
import com.meicam.sdk.NvsVideoClip
import com.meicam.sdk.NvsVideoFx
import com.meicam.sdk.NvsVideoTrack
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_IS_LIVE_PHOTO_SUPPORT_FILTER
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_OPEN_OLIVE_VIDEO_EDIT_LOG
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.constant.OliveVideoEditEffectsProperty.FilterLutPropertyKey.DATA_FILE_PATH
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.constant.OliveVideoEditEffectsProperty.FilterLutPropertyKey.INTENSITY
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.constant.OliveVideoEditEffectsProperty.FxNameKey.LUT_KYE
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.data.OliveVideoEditFilterEffects

class OliveVideoFilterEditorImp(
    private var nvsTimeline: NvsTimeline?,
    private var nvsVideoTrack: NvsVideoTrack?,
    private var nvsVideoClip: NvsVideoClip?
) : IOliveVideoFilterEditor {

    private var fixLutIndex = INVALID_INDEX
    private var nvsVideoFx: NvsVideoFx? = null

    override fun setFilter(filter: OliveVideoEditFilterEffects?): Int {
        if (DEBUG_IS_LIVE_PHOTO_SUPPORT_FILTER.not()) {
            return fixLutIndex
        }
        val filterPath = filter?.filterFilePath
        val intensity = filter?.intensity ?: 0L
        if (nvsVideoFx == null) {
            nvsVideoFx = nvsVideoClip?.appendBuiltinFx(LUT_KYE)
        }
        nvsVideoFx?.apply {
            fixLutIndex = index
            if (filterPath.isNullOrEmpty()) {
                GLog.d(TAG, LogFlag.DL, "setFilter start removeFx = $fixLutIndex")
                removeFilter(fixLutIndex)
                GLog.d(TAG, LogFlag.DL, "setFilter end removeFx = $fixLutIndex")
                return INVALID_INDEX
            }
            setStringVal(DATA_FILE_PATH, filterPath)
            setFloatVal(INTENSITY, intensity.toDouble())
            if (DEBUG_OPEN_OLIVE_VIDEO_EDIT_LOG) {
                GLog.d(TAG, LogFlag.DF, "setFilter LUT_KYE = $LUT_KYE, ${getStringVal(DATA_FILE_PATH)}, $filter")
            } else {
                GLog.d(TAG, LogFlag.DF, "setFilter filter.name =  ${filter.name}")
            }
        }
        return fixLutIndex
    }

    override fun removeFilter(index: Int) {
        nvsVideoClip?.removeFx(index)
        fixLutIndex = INVALID_INDEX
        nvsVideoFx = null
    }

    override fun isHasFilter(): Boolean {
        val isHasFilter = TextUtils.isEmpty(nvsVideoFx?.getStringVal(DATA_FILE_PATH)).not() && (fixLutIndex != INVALID_INDEX)
        GLog.d(TAG, LogFlag.DF, "isHasFilter = $isHasFilter")
        return isHasFilter
    }

    override fun release() {
        removeAllFx()
        nvsVideoFx = null
    }

    override fun removeAllFx() {
        val track: NvsVideoTrack = nvsTimeline?.getVideoTrackByIndex(0) ?: return
        for (i in 0 until track.clipCount) {
            val clip = track.getClipByIndex(i) ?: continue
            clip.removeAllVideoFx()
        }
        fixLutIndex = INVALID_INDEX
    }

    private companion object {
        private const val TAG = "OliveVideoFilterEditorImp"
        private const val INVALID_INDEX = -1
    }
}