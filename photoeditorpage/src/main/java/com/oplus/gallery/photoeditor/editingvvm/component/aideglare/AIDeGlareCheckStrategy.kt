/***********************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd.
 ** All rights reserved.
 **
 ** File: - AIDeGlareCheckStrategy.kt
 ** Description: xxx
 ** Version: 1.0
 ** Date : 2025/3/31
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>      <data>      <version >     <desc>
 ** ------------------------------------------------------------
 ** gongziyao      2025/3/31       1.0          created
 ***************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.component.aideglare

import android.content.Context
import android.os.Bundle
import android.util.Size
import com.oplus.aiunit.core.data.DetectName
import com.oplus.gallery.basebiz.helper.function.restrict.ISupportCheckStrategy
import com.oplus.gallery.basebiz.uiflowinterceptor.NetworkAuthorizationInterceptor
import com.oplus.gallery.basebiz.uiflowinterceptor.NetworkInterceptor
import com.oplus.gallery.basebiz.uiflowinterceptor.PrivacyInterceptor
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitModelDownloadInterceptor
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitModelDownloadInterceptor.Companion.MODEL_DOWNLOAD_SUCCESS
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitPlugin
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitPluginState
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitPrivacyInterceptor
import com.oplus.gallery.foundation.ui.notification.NotificationAction
import com.oplus.gallery.foundation.uiflowinterceptor.IInterceptor
import com.oplus.gallery.foundation.uiflowinterceptor.RealInterceptorChain
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_DEGLARE_DOWNLOAD_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_EDIT_PICTURE_DE_GLARE_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.UPDATE_AI_DEGLARE_SHOW_TIMESTAMP
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.Permission.IS_AUTHORIZE_ACCESS_AI_DE_GLARE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.Privacy.AUTHORIZE_AI_DEGLARE
import com.oplus.gallery.framework.abilities.download.AIUnitDownloadWord
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.component.aiunit.AIUnitDisabledInterceptor
import com.oplus.gallery.photoeditor.editingvvm.component.aiunit.AIUnitLoginInterceptor
import com.oplus.gallery.photoeditor.editingvvm.component.aiunit.AIUnitOfflineInterceptor
import com.oplus.gallery.photoeditor.editingvvm.notification.AIFuncRealmeExportPrivacyInterceptor

/**
 * realme AI去眩光功能支持性检查策略
 */
class AIDeGlareCheckStrategy(
    private val imageSize: Size
) : ISupportCheckStrategy {

    private var chain: RealInterceptorChain? = null

    private val aiDeglarePluginState by lazy {
        AIUnitPluginState(
            AIUnitPlugin.AI_DEGLARE.downloadPlugin,
            AI_DEGLARE_DOWNLOAD_STATE,
            UPDATE_AI_DEGLARE_SHOW_TIMESTAMP
        )
    }

    override fun checkSupportability(
        context: Context,
        function: (isSupport: Boolean) -> Unit,
        postNotificationAction: (NotificationAction) -> Unit
    ) {
        val interceptors = getInterceptors(context, postNotificationAction)
        chain = RealInterceptorChain(
            interceptors,
            onSuccessCallback = {
                // 如果当次是模型下载成功的场景，返回false,不做后续进二级页的事务
                val isModelDownloadSuccess = it.getBoolean(MODEL_DOWNLOAD_SUCCESS)
                function(isModelDownloadSuccess.not())
            },
            onFailCallback = { function(false) }
        )
        chain?.proceed(Bundle())
    }

    private fun getInterceptors(context: Context, postNotificationAction: (NotificationAction) -> Unit): List<IInterceptor<Bundle, Unit>> =
        listOf(
            // AIUnit停用拦截
            AIUnitDisabledInterceptor(context, postNotificationAction),
            // 功能下线拦截
            AIUnitOfflineInterceptor(
                R.string.picture3d_editor_text_ai_edit_picture_offline_title_deglare,
                AI_EDIT_PICTURE_DE_GLARE_STATE,
                postNotificationAction
            ),
            // 用户须知检查
            AIFuncRealmeExportPrivacyInterceptor(
                context,
                IS_AUTHORIZE_ACCESS_AI_DE_GLARE,
                com.oplus.gallery.framework.abilities.authorizing.R.string.base_aideglare_dialog_message_with_agreement_and_privacy,
                postNotificationAction
            ),
            // 隐私权限检查
            PrivacyInterceptor(
                context,
                listOf(AUTHORIZE_AI_DEGLARE),
                com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_ai_deglare_statement,
                postNotificationAction
            ),
            // 无网络拦截
            NetworkInterceptor(postNotificationAction),
            // 网络权限
            NetworkAuthorizationInterceptor(
                com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_request_network_ai_deglare,
                postNotificationAction
            ),
            // 图片尺寸限制弹窗，对于过小或过大尺寸的照片
            AiDeGlareImageSizeInterceptor(imageSize, postNotificationAction),
            // AIUnit用户须知拦截器
            AIUnitPrivacyInterceptor(context),
            // 登录拦截
            AIUnitLoginInterceptor(context, DetectName.VISION_IMAGE_DEBLUR),
            // 模型是否需要下载拦截
            AIUnitModelDownloadInterceptor(
                context,
                aiDeglarePluginState,
                createWord(),
                postNotificationAction
            )
        )

    private fun createWord(): AIUnitDownloadWord {
        return AIUnitDownloadWord(
            R.string.picture3d_editor_text_deglare_install_title,
            R.string.picture3d_editor_text_deglare_update_desc,
            R.string.picture3d_editor_text_deglare_install_desc,
            R.string.picture3d_editor_text_deglare_toast_download_fail,
            R.string.picture3d_editor_text_deglare_toast_install_fail,
            R.string.picture3d_editor_text_deglare_updating,
            R.string.picture3d_editor_text_deglare_downloading,
            R.string.picture3d_editor_text_deglare_update_finish,
            R.string.picture3d_editor_text_deglare_download_finish
        )
    }

    override fun destroy() {
        chain?.destroy()
        chain = null
    }
}