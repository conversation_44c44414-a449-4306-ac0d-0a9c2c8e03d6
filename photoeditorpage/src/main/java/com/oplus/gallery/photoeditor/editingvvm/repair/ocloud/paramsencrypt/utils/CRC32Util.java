/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - ${FILE_NAME}
 * * Description: build this module.
 * * Version: 1.0
 * * Date : 2020/03/03
 * * Author: GuangJin.Ye@Apps.Gallery3D
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  GuangJin.Ye@Apps.Gallery3D       2020/03/03    1.0     build this module
 ****************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.repair.ocloud.paramsencrypt.utils;

import java.util.zip.CRC32;

public class CRC32Util {

    public static long disgest(byte[] buff) {
        CRC32 c = new CRC32();
        c.update(buff);
        return c.getValue();
    }

}