/*********************************************************************************
 ** Copyright (C), 2024-2034, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OliveSlowMotionInterceptors.kt
 ** Description: OLive（动态照片）慢动作前置检查拦截器
 ** Version: 1.0
 ** Date: 2025/06/13
 ** Author: <EMAIL>
 ** -
 ** ---------------------Revision History: ---------------------
 **  <author>    <date>    <version >    <desc>
 **  <EMAIL>    2025/6/13    1.0    build this module
 ****************************************************************/

package com.oplus.gallery.photoeditor.editingvvm.olive

import android.content.Context
import android.net.Uri
import android.os.Bundle
import android.util.Rational
import android.util.Size
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitModelDownloadInterceptor
import com.oplus.gallery.foundation.archv2.bus.IViewBus
import com.oplus.gallery.foundation.ui.notification.NotificationAction
import com.oplus.gallery.foundation.uiflowinterceptor.ConditionInterceptor
import com.oplus.gallery.foundation.uiflowinterceptor.IInterceptor
import com.oplus.gallery.foundation.uiflowinterceptor.ToastInterceptor
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.maxLen
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_INPUTS_DATA_SOURCE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OLiveVideoEdit.TOPIC_PREVIEW_OLIVE_VIDEO_ENGINE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Olive.TOPIC_OLIVE_UI_STATE
import com.oplus.gallery.photoeditor.editingvvm.get
import com.oplus.gallery.photoeditor.editingvvm.inputarguments.DataSource
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.engine.base.IVideoEngine

/**
 * olive慢动作 时长拦截器
 * 当慢动作时长超过4s时，弹出toast提示
 */
class OliveSlowMotionDurationInterceptor(
    val context: Context,
    val vBus: IViewBus,
    updateUI: (NotificationAction) -> Unit
) : ToastInterceptor(updateUI) {
    override fun onCheckCondition(param: Bundle): Boolean {
        val bean = vBus.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE) ?: OliveUiBean()
        return bean.totalDuration <= OLIVE_DURATION_THRESHOLD
    }

    override fun createToastAction(): NotificationAction.ToastAction {
        val msg = context.resources.getQuantityString(R.plurals.photoeditorpage_olive_duration_over_plural, FOUR, FOUR)
        return NotificationAction.ToastAction(textString = msg)
    }

    companion object {
        const val TAG = "OliveSlowMotionDurationInterceptor"

        const val FOUR = 4

        /**
         * 慢动作时长阈值
         */
        const val OLIVE_DURATION_THRESHOLD = 4000L
    }
}

/**
 * olive慢动作 实况视频分辨率，帧率，时长拦截器
 * 当慢动作视频分辨率超过2k，帧率超过30fps，时长超过3s时，弹出实况素材较大提示
 */
class OliveSlowMotionSpecificationInterceptor(
    val vBus: IViewBus,
    val updateUI: (NotificationAction) -> Unit
) : ConditionInterceptor() {
    override fun onCheckCondition(param: Bundle): Boolean {
        vBus.get<DataSource>(TOPIC_INPUTS_DATA_SOURCE)?.let {
            if (check(it.imageUri)) {
                return true
            }
        }

        // 模型下载成功的场景, 不提示等待信息
        if (param.getBoolean(AIUnitModelDownloadInterceptor.MODEL_DOWNLOAD_SUCCESS)) {
            return true
        }

        // 慢动作素材已加载, 不提示等待信息
        if (vBus.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE)?.slowVideoPath.isNullOrEmpty().not()) {
            return true
        }

        val videoEngine = vBus.get<IVideoEngine>(TOPIC_PREVIEW_OLIVE_VIDEO_ENGINE) ?: return true

        // 判定视频帧率是否超过30fps 超过则提示等待信息
        if ((videoEngine.getInfo()?.getFps()?.compareTo(Rational(OLIVE_FRAME_RATE_THRESHOLD, 1)) ?: 0) > 0) {
            GLog.d(TAG, LogFlag.DL) { "Olive视频帧率超过30fps" }
            return false
        }

        // 判定视频分辨率是否超过2k 超过则提示等待信息
        val videoSize = videoEngine.getInfo()?.getSize() ?: Size(0, 0)
        videoSize.maxLen().let {
            if (it >= OLIVE_VIDEO_SIZE_THRESHOLD_2K) {
                GLog.d(TAG, LogFlag.DL) { "Olive视频分辨率超过2k" }
                return false
            }
        }

        return true
    }

    override fun onConditionFailed(chain: IInterceptor.IChain<Bundle, Unit>) {
        vBus.get<DataSource>(TOPIC_INPUTS_DATA_SOURCE)?.let {
            mark(it.imageUri)
        }
        updateUI(OliveMaterialTooLargeNotificationAction())
        chain.proceed(chain.param)
    }

    companion object {
        const val TAG = "OliveSlowMotionDurationInterceptor"

        /**
         * 慢动作帧率阈值
         */
        const val OLIVE_FRAME_RATE_THRESHOLD = 30

        /**
         * 慢动作分辨率阈值
         */
        const val OLIVE_VIDEO_SIZE_THRESHOLD_2K = 2000

        /**
         * 记录慢动作素材是否已弹出提示的容器
         */
        private val map = mutableMapOf<Uri, Boolean?>()

        /**
         * 判断慢动作素材是否已弹出提示
         */
        private fun check(uri: Uri): Boolean {
            return map[uri] == true
        }

        /**
         * 标记慢动作素材弹出提示
         */
        private fun mark(uri: Uri) {
            map[uri] = true
        }
    }
}