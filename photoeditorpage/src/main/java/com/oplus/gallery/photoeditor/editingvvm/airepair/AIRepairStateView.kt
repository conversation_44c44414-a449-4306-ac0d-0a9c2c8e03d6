/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AIRepairStateView.kt
 ** Description: AI修复的状态控件，用于修复过程中状态的提示
 ** Version: 1.0
 ** Date : 2024/5/21
 ** Author: xiewujie@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  xiewujie@Apps.Gallery3D      2024/05/21    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.airepair

import android.content.Context
import android.util.AttributeSet
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.widget.layout.ProgressCapsuleStateView

/**
 * AI修复的状态控件，用于修复过程中状态的提示
 * @param context Context
 * @param attributeSet AttributeSet
 */
class AIRepairStateView @JvmOverloads constructor(
    context: Context,
    attributeSet: AttributeSet? = null
) : ProgressCapsuleStateView(context, attributeSet) {

    init {
        tvActionNext.visibility = VISIBLE
    }

    private val debluringText by lazy {
        resources.getString(R.string.picture3d_editor_text_debluring)
    }

    private val deRefreshText by lazy {
        resources.getString(R.string.picture3d_editor_text_doing_deReflection)
    }

    private val recognizeText by lazy {
        resources.getString(R.string.picture3d_editor_text_deReflection_recognizing)
    }

    private val deglareingText by lazy {
        resources.getString(R.string.picture3d_editor_text_deglare)
    }

    private val deFogMongoText by lazy {
        resources.getString(R.string.picture3d_editor_text_doing_deFogMongo)
    }

    /**
     * 进入去模糊状态
     */
    fun enterDebluringState() {
        showLoadingText(debluringText)
    }

    /**
     * 进入识别中状态
     */
    fun enterRecognizeState() {
        showLoadingText(recognizeText)
    }

    /**
     * 进入消除反光状态
     */
    fun enterDeReflectionState() {
        showLoadingText(deRefreshText)
    }

    /**
     * 进入去眩光状态
     */
    fun enterDeglareingState() {
        showLoadingText(deglareingText)
    }

    /**
     * 进入去雾状态
     */
    fun enterDeFogMongoState() {
        showLoadingText(deFogMongoText)
    }
}