/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - BestTakeSupportCheckStrategy.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/12/25
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>       2024/12/25        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.component.besttake

import android.content.Context
import android.os.Bundle
import android.util.Size
import com.oplus.gallery.basebiz.helper.function.restrict.ISupportCheckStrategy
import com.oplus.gallery.basebiz.uiflowinterceptor.FaceScanPermissionInterceptor
import com.oplus.gallery.basebiz.uiflowinterceptor.NetworkAuthorizationInterceptor
import com.oplus.gallery.basebiz.uiflowinterceptor.NetworkInterceptor
import com.oplus.gallery.basebiz.uiflowinterceptor.PrivacyInterceptor
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.business_lib.model.data.base.utils.Constants
import com.oplus.gallery.business_lib.model.data.face.data.BestTakeExt.MAX_FACE_COUNT
import com.oplus.gallery.foundation.ui.notification.NotificationAction
import com.oplus.gallery.foundation.uiflowinterceptor.IInterceptor
import com.oplus.gallery.foundation.uiflowinterceptor.RealInterceptorChain
import com.oplus.gallery.foundation.uiflowinterceptor.ToastInterceptor
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_BEST_TAKE_DETECT_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.Permission.IS_AUTHORIZE_ACCESS_AI_BEST_TAKE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.Privacy.AUTHORIZE_CLOUD_EDITOR
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.Privacy.AUTHORIZE_REQUEST_SERVER_UPDATE_AND_DOWNLOAD
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.component.aiunit.AIUnitDisabledInterceptor
import com.oplus.gallery.photoeditor.editingvvm.component.aiunit.AIUnitOfflineInterceptor
import com.oplus.gallery.photoeditor.editingvvm.detect.DetectingResult
import com.oplus.gallery.photoeditor.editingvvm.detect.FaceState
import com.oplus.gallery.photoeditor.editingvvm.notification.AIFuncExportPrivacyInterceptor
import com.oplus.gallery.photoeditor.editingvvm.notification.MediaSizeLimitInterceptor
import com.oplus.gallery.framework.abilities.authorizing.R as AuthorizingR

/**
 * AI 最佳表情是否可用性策略
 */
internal class BestTakeSupportCheckStrategy(
    private val imageSize: Size,
    private val detectingResult: DetectingResult
) : ISupportCheckStrategy {
    private var chain: RealInterceptorChain? = null
    override fun checkSupportability(
        context: Context,
        function: (isSupport: Boolean) -> Unit,
        postNotificationAction: (NotificationAction) -> Unit
    ) {
        val interceptors = getInterceptors(context, postNotificationAction)
        chain = RealInterceptorChain(interceptors,
            onSuccessCallback = { function(true) },
            onFailCallback = { function(false) }
        )
        chain?.proceed(Bundle())
    }

    private fun getInterceptors(context: Context, postNotificationAction: (NotificationAction) -> Unit): List<IInterceptor<Bundle, Unit>> {
        val noFaceToastInterceptor = object : ToastInterceptor(postNotificationAction) {
            override fun createToastAction(): NotificationAction.ToastAction {
                return NotificationAction.ToastAction(R.string.picture3d_photo_editor_repair_toast_no_face)
            }

            override fun onCheckCondition(param: Bundle): Boolean {
                return (detectingResult as? DetectingResult.Face)?.faceState == FaceState.FACE
            }
        }
        val overFaceLimitToastInterceptor = object : ToastInterceptor(postNotificationAction) {
            override fun createToastAction(): NotificationAction.ToastAction {
                return NotificationAction.ToastAction(R.string.photoeditorpage_best_take_portraits_has_reached_the_uppper_limit)
            }

            override fun onCheckCondition(param: Bundle): Boolean {
                val faceRectList = (detectingResult as? DetectingResult.Face)?.faceRectList ?: emptyList()
                return faceRectList.size <= MAX_FACE_COUNT
            }
        }
        val isScanningFaceInterceptor = object : ToastInterceptor(postNotificationAction) {
            override fun createToastAction(): NotificationAction.ToastAction {
                return NotificationAction.ToastAction(R.string.photoeditorpage_best_take_scanning_again_prompt)
            }

            override fun onCheckCondition(param: Bundle): Boolean {
                val isScannerIdle = ApiDmManager.getScanDM().isScannerIdle()
                GLog.d("BestTakeSupportCheckStrategy", LogFlag.DL) { "isScannerIdle: $isScannerIdle" }
                return isScannerIdle
            }
        }
        return listOf(
            // AIUnit停用拦截
            AIUnitDisabledInterceptor(context, postNotificationAction),
            // 功能下线拦截
            AIUnitOfflineInterceptor(
                R.string.photoeditorpage_best_take_offline_title,
               AI_BEST_TAKE_DETECT_STATE,
                postNotificationAction
            ),
            // 用户须知检查
            AIFuncExportPrivacyInterceptor(
                context,
                IS_AUTHORIZE_ACCESS_AI_BEST_TAKE,
                AuthorizingR.string.authorizing_ai_group_photo_statement,
                postNotificationAction
            ),
            // 隐私权限检查
            PrivacyInterceptor(
                context,
                listOf(AUTHORIZE_CLOUD_EDITOR, AUTHORIZE_REQUEST_SERVER_UPDATE_AND_DOWNLOAD),
                AuthorizingR.string.authorizing_ai_group_photo_statement,
                postNotificationAction
            ),
            // 人脸扫描权限检查
            FaceScanPermissionInterceptor(context, postNotificationAction),
            // 无网络拦截
            NetworkInterceptor(postNotificationAction),
            // 网络权限
            NetworkAuthorizationInterceptor(
                R.string.photoeditorpage_best_take_need_connect_network,
                postNotificationAction
            ),
            // 人脸检测
            noFaceToastInterceptor,
            // 人脸上限检测，超过20个人脸
            overFaceLimitToastInterceptor,
            //是否正在后台扫描检测
            isScanningFaceInterceptor,
            // 图片尺寸限制弹窗，对于过小或过大尺寸的照片
            MediaSizeLimitInterceptor(
                context,
                imageSize,
                Constants.AIFunc.MIN_IMAGE_SIZE,
                Constants.AIFunc.MAX_IMAGE_SIZE,
                updateUI = postNotificationAction
            )
        )
    }

    override fun destroy() {
        chain?.destroy()
        chain = null
    }
}