/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AIBestTakeUtils
 ** Description: 最佳表情工具类
 **
 ** Version: 1.0
 ** Date: 2025/01/21
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <EMAIL>  2025/01/21  1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.aibesttake

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.BitmapRegionDecoder
import android.graphics.BlurMaskFilter
import android.graphics.Canvas
import android.graphics.ColorSpace
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.Rect
import android.graphics.RectF
import android.util.Size
import android.util.SizeF
import android.view.View
import android.view.ViewTreeObserver
import androidx.core.content.ContextCompat
import androidx.core.graphics.times
import androidx.core.graphics.toRect
import androidx.core.graphics.toRectF
import com.oplus.gallery.business_lib.model.data.face.data.CvFaceInfo
import com.oplus.gallery.business_lib.model.data.face.data.getOriginRectF
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.display.ScreenUtils.toDp
import com.oplus.gallery.foundation.util.display.ScreenUtils.toPx
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt.DISPLAY_P3
import com.oplus.gallery.foundation.util.ext.fitCenter
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.foundation.util.math.MathUtil.toScale
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.aibesttake.AIBestTakeUtils.RATIO_3
import com.oplus.gallery.photoeditor.editingvvm.aibesttake.AIBestTakeUtils.RATIO_4
import com.oplus.gallery.photoeditor.editingvvm.aibesttake.bean.FaceFocusParam
import kotlin.math.ln

/**
 * 最佳表情工具类
 */
object AIBestTakeUtils {

    const val RATIO_3 = 3
    const val RATIO_4 = 4
    private const val TAG = "AIBestTakeUtils"

    private var faceFrameBorderWidthPx = 0f
    private var faceFrameRadiusPx = 0f
    private var faceFrameArcLengthPx = 0f
    private const val MAX_STROKE_WIDTH_DP = 3f
    private const val MIN_WIDTH_DP = 24f
    private const val STROKE_WIDTH_BASE_DP = 1.2f
    private const val CORNER_RATIO = 0.01f
    private const val VISIBLE_LINE_RATIO = 0.25f
    private const val STROKE_ADJUSTMENT_FACTOR = 0.0116f

    /**保留2位小数*/
    const val NUMBER_DIGITS = 2

    // 透明度 (204 ≈ 80% 不透明)
    private const val OVERLAY_ALPHA = 204

    // 阴影相关常量
    private const val SHADOW_MARGIN = 90
    private const val FIRST_SHADOW_OFFSET_Y = 12f
    private const val SECOND_SHADOW_OFFSET_Y = 24f
    private const val SHADOW_BLUR_RADIUS = 25f

    /**
     * 裁剪和外扩
     * @param face 目标人脸信息
     * @return 裁剪出的人脸图
     */
    @JvmStatic
    fun cropOriginFace(face: CvFaceInfo, colorSpace: ColorSpace = DISPLAY_P3): Bitmap? {
        GLog.i(TAG, LogFlag.DL) {
            "cropAndExpand: face = $face , width = ${face.thumbWidth} , height = ${face.thumbHeight} , rect = ${face.rect}"
        }
        val filePath = face.data ?: let {
            GLog.w(TAG, LogFlag.DL) { "cropAndExpand: filePath is null." }
            return null
        }
        var decoder: BitmapRegionDecoder?
        val originRect = face.getOriginRectF().toRect()
        var rectBmp = kotlin.runCatching {
            decoder = BitmapRegionDecoder.newInstance(filePath)
            // 先转float再乘缩放比，尽可能保证精度
            decoder?.decodeRegion(originRect, BitmapFactory.Options().also {
                it.inPreferredConfig = Bitmap.Config.ARGB_8888
                it.inSampleSize = 1
            })
        }.getOrNull()
        if (rectBmp == null) {
            // 区域解码失败的话，先解码原图，然后再裁剪
            val bitmap = BitmapFactory.decodeFile(filePath, BitmapFactory.Options().also {
                it.inPreferredConfig = Bitmap.Config.ARGB_8888
                it.inSampleSize = 1
            })
            rectBmp = BitmapUtils.cropBitmap(bitmap, originRect)
        }
        if (rectBmp == null) {
            GLog.w(TAG, LogFlag.DL) { "cropAndExpand: crop bitmap failed, cvFaceInfo = $face" }
        }
        BitmapUtils.trySetBitmapColorSpace(rectBmp, colorSpace, "cropOriginFace")
        return rectBmp
    }

    /**
     * 裁剪出指定bitmap的图片
     * @param face 裁剪目标图的faceInfo
     * @param target 裁剪目标图
     * @return 裁剪出来来人脸
     */
    @JvmStatic
    fun cropFace(face: CvFaceInfo, target: Bitmap, colorSpace: ColorSpace = DISPLAY_P3): Bitmap? {
        GLog.d(TAG, LogFlag.DL) {
            "cropFace: face = $face , width = ${face.thumbWidth} , height = ${face.thumbHeight} , rect = ${face.rect}"
        }
        val factor = if ((face.thumbWidth != 0) && (face.thumbHeight != 0)) {
            target.width.coerceAtLeast(target.height).toFloat() / face.thumbWidth.coerceAtLeast(face.thumbHeight)
        } else {
            1f
        }
        val targetRect = face.rect.toRectF().times(factor).toRect()
        val rectBmp = BitmapUtils.cropBitmap(target, targetRect)
        if (rectBmp == null) {
            GLog.w(TAG, LogFlag.DL) { "cropAndExpand: crop bitmap failed, cvFaceInfo = $face" }
        }
        BitmapUtils.trySetBitmapColorSpace(rectBmp, colorSpace, "cropFace")
        return rectBmp
    }

    /**
     * 获取旋转和缩放转换后顶点坐标。
     * @param rect 图片位置
     * @param bitmap 此处传入的bitmap为动效汇出的bitmap，我们会根据动效绘制的bitmap大小做大小修正，确保相册自己绘制的框与动效的框一致
     * @param scale 缩放倍数
     * @param degrees 旋转角度
     * @return 转换后的顶点坐标
     */
    @JvmStatic
    fun getVertexCoordinates(rect: Rect, scale: Float = 1f, degrees: Float = 0f): FloatArray {
        val left = rect.left.toFloat()
        val right = rect.right.toFloat()
        val top = rect.top.toFloat()
        val bottom = rect.bottom.toFloat()
        val centerX = rect.exactCenterX()
        val centerY = rect.exactCenterY()
        val sourceVertex = floatArrayOf(
            left, top,
            right, top,
            left, bottom,
            right, bottom,
            centerX, centerY
        )
        val dstVertex = FloatArray(sourceVertex.size)
        val matrix = Matrix()
        if (scale != 1f) {
            matrix.postScale(scale, scale, centerX, centerY)
        }
        if (degrees != 0f) {
            matrix.postRotate(degrees, centerX, centerY)
        }
        matrix.mapPoints(dstVertex, sourceVertex)
        return dstVertex
    }

    /**
     * 是否包含闭眼、眯眯眼等表情
     * @return true/false
     */
    @JvmStatic
    fun Map<CvFaceInfo, List<CvFaceInfo>>.containsBadFaces(): Boolean {
        val isSupportAutoEdit = this.any {
            // 至少一组支持自动编辑
            it.key.isSupportAutoEdit && it.value.any { face -> face.isSupportAutoEdit }
        }
        GLog.d(AIBestTakeVM.TAG, LogFlag.DL) { "containsBadFaces: $isSupportAutoEdit" }
        return isSupportAutoEdit
    }

    /**
     * 计算矩形面积
     * @return 返回矩形面积
     */
    @JvmStatic
    fun Rect.area(): Int {
        return height() * width()
    }

    /**
     * 获取人脸对焦框的参数
     * @param faceWidth 人脸宽度
     * @return 返回一个人脸的参数集
     */
    @JvmStatic
    fun getFaceFocusParam(
        faceWidth: Float,
        faceHeight: Float,
    ): FaceFocusParam {
        updateStrokeAndCornerRadius(faceWidth)
        return FaceFocusParam(faceWidth, faceHeight, faceFrameBorderWidthPx, faceFrameRadiusPx, faceFrameArcLengthPx)
    }

    @JvmStatic
    private fun updateStrokeAndCornerRadius(width: Float) {
        val widthInDp = width.toScale(NUMBER_DIGITS).toDp
        // 线宽计算
        val strokeWidthDp = when {
            widthInDp <= MIN_WIDTH_DP -> STROKE_WIDTH_BASE_DP
            else -> STROKE_WIDTH_BASE_DP + (STROKE_ADJUSTMENT_FACTOR * (widthInDp - MIN_WIDTH_DP) * ln(widthInDp / MIN_WIDTH_DP))
        }
        // 线宽
        val clampedStrokeWidthDp = if (strokeWidthDp > MAX_STROKE_WIDTH_DP) MAX_STROKE_WIDTH_DP else strokeWidthDp.toScale(NUMBER_DIGITS)
        faceFrameBorderWidthPx = clampedStrokeWidthDp
        // 圆角半径
        faceFrameRadiusPx = (widthInDp * CORNER_RATIO).toPx
        // 虚线段长度
        faceFrameArcLengthPx = (widthInDp * VISIBLE_LINE_RATIO).toPx
    }

    /**
     * 获取缩放后的人脸框
     * @param faceRect 缩放前的人脸框
     * @param scale 缩放比例
     * @return Rect 缩放后的人脸框
     */
    @JvmStatic
    fun getFaceScaleRect(faceRect: Rect, scale: Float): Rect {
        return Rect(
            (faceRect.left * scale).toInt(),
            (faceRect.top * scale).toInt(),
            (faceRect.right * scale).toInt(),
            (faceRect.bottom * scale).toInt()
        )
    }

    /**
     * 计算两个RectF的重叠部分区域
     * 如可用于计算处于可视内容区域内的图像Rect（图像实际区域和可视区域的重叠）
     *
     * @param rectFOne 区域一
     * @param rectFTwo 区域二
     * @return Rect 交叠区域
     */
    @JvmStatic
    fun getOverlapRectF(rectFOne: RectF, rectFTwo: RectF): Rect {
        return Rect(
            maxOf(rectFOne.left.toInt(), rectFTwo.left.toInt()),
            maxOf(rectFOne.top.toInt(), rectFTwo.top.toInt()),
            minOf(rectFOne.right.toInt(), rectFTwo.right.toInt()),
            minOf(rectFOne.bottom.toInt(), rectFTwo.bottom.toInt())
        )
    }

    /**
     * 根据目标区域的信息，获取当前图像需要缩放的比例
     * @param targetRectF 目标区域
     * @param width 需要进行缩放的区域的宽度
     * @param height 需要进行缩放的区域的高度
     * @return Pair<Float, Boolean> 1.缩放比例 2.缩放参考的方向是否是水平方向
     */
    @JvmStatic
    fun getScaleBasedOnTarget(targetRectF: RectF, width: Int, height: Int): Pair<Float, Boolean> {
        val scaleX = targetRectF.width() / width
        val scaleY = targetRectF.height() / height
        val scale = scaleX.coerceAtMost(scaleY)
        return Pair(scale, scale == scaleX)
    }

    /**
     * 获取人脸在预览区的大小
     * @param faceRect 人脸区域的实际大小
     * @param thumbnailSizeF 预览缩图大小（960缩图，动效等场景）
     * @param previewDisplayRectF 预览图区域RectF，当前页面预览图区域
     * @return 人脸在预览图上的相对位置
     */
    @JvmStatic
    fun getFaceSizeOnPreview(faceRect: Rect, targetSize: Size?, thumbnailSizeF: SizeF?, previewDisplayRectF: RectF?): Size {
        val size = targetSize ?: return Size(faceRect.width(), faceRect.height())
        val thumbnailSize = thumbnailSizeF ?: return Size(faceRect.width(), faceRect.height())
        val previewDisplayRect = previewDisplayRectF ?: return Size(faceRect.width(), faceRect.height())
        val fitCenterRect = RectF(0f, 0f, thumbnailSize.width, thumbnailSize.height).fitCenter(previewDisplayRect)
        val scaleX = fitCenterRect.width() / size.width.toFloat()
        val scaleY = fitCenterRect.height() / size.height.toFloat()
        return Size((faceRect.width() * scaleX).toInt(), (faceRect.height() * scaleY).toInt())
    }

    /**
     * 生成一个用于分组的唯一 ID。
     *
     * 该方法通过计算传入的 [cvFaceInfo] 对象的哈希值来生成一个唯一的分组 ID。
     * 返回的 ID 是一个整数类型，适用于需要唯一标识符的场景。
     *
     * @param cvFaceInfo 传入的 [CvFaceInfo] 对象，用于生成唯一 ID。
     * @return 唯一值
     */
    @JvmStatic
    fun generateFaceId(cvFaceInfo: CvFaceInfo): Int {
        return cvFaceInfo.hashCode()
    }

    /**
     * 生成一个基于当前时间戳的唯一 ID。
     *
     * 该函数通过调用 `System.currentTimeMillis()` 获取当前时间的毫秒数，
     * 并将其作为唯一标识符返回。
     *
     * @return 基于当前时间戳生成的唯一 ID，类型为 `Long`。
     */
    @JvmStatic
    fun generateFaceId(): Long {
        return System.currentTimeMillis()
    }


    /**
     * 初始化交互视图，并根据当前屏幕模式更新位置
     * @param faceRect 人脸框
     * @param targetSize 原图尺寸
     * @param thumbnailSize 缩图尺寸
     * @param displayRect 显示区域
     * @return 映射后的人脸框
     */
    @JvmStatic
    fun mapFaceRectToDisplayCoordinates(
        faceRect: Rect,
        targetSize: Size,
        thumbnailSize: Size,
        displayRect: RectF
    ): RectF {
        val thumbnailScaleX = thumbnailSize.width.toFloat() / targetSize.width
        val thumbnailScaleY = thumbnailSize.height.toFloat() / targetSize.height

        val scaledFaceRect = RectF(
            faceRect.left * thumbnailScaleX,
            faceRect.top * thumbnailScaleY,
            faceRect.right * thumbnailScaleX,
            faceRect.bottom * thumbnailScaleY
        )

        val displayScaleX = displayRect.width() / thumbnailSize.width
        val displayScaleY = displayRect.height() / thumbnailSize.height

        return RectF(
            displayRect.left + scaledFaceRect.left * displayScaleX,
            displayRect.top + scaledFaceRect.top * displayScaleY,
            displayRect.left + scaledFaceRect.right * displayScaleX,
            displayRect.top + scaledFaceRect.bottom * displayScaleY
        ).toRatio1To1(targetSize.width, targetSize.height)
    }

    /**
     * 截取指定视图的截图
     * @param bitmap 待截取的图片
     * @param topMargin 顶部边距
     * @param leftMargin 左侧边距
     * @param targetView 截取的视图
     * @param cardViewWidth 卡片视图宽度
     * @param cardViewHeight 卡片视图高度
     * @param shadowMargin 阴影边距
     * @param onScreenshotReady 截图完成回调
     * @return 截取的图片
     */
    @JvmStatic
    @Suppress("LongParameterList")
    fun cropBitmapFromView(
        bitmap: Bitmap,
        topMargin: Int,
        leftMargin: Int,
        targetView: View,
        cardViewWidth: Int,
        cardViewHeight: Int,
        shadowMargin: Int,
        onScreenshotReady: (Bitmap?) -> Unit
    ) {
        // 确保 View 已布局完成
        targetView.viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                // 移除监听器
                targetView.viewTreeObserver.removeOnGlobalLayoutListener(this)
                // 获取 View 在其父视图中的位置
                val viewLeft = leftMargin + shadowMargin
                val viewTop = topMargin + shadowMargin
                val viewWidth = cardViewWidth
                val viewHeight = cardViewHeight

                // 检查坐标和尺寸是否合法
                if ((viewLeft < 0) || (viewTop < 0) || (viewWidth <= 0) || (viewHeight <= 0)) {
                    onScreenshotReady(null)
                    return
                }
                GLog.d(TAG, LogFlag.DL) {
                    "onGlobalLayout: viewLeft = $viewLeft , viewTop = $viewTop , viewWidth = $viewWidth , shadowMargin =$shadowMargin" +
                        ", viewHeight = $viewHeight , bitmap width = ${bitmap.width} , height = ${bitmap.height}"
                }
                // 裁剪 Bitmap
                val result = runCatching {
                    Bitmap.createBitmap(
                        bitmap, viewLeft, viewTop,
                        viewWidth.coerceAtMost(bitmap.width - viewLeft),
                        viewHeight.coerceAtMost(bitmap.height - viewTop)
                    )
                }.getOrElse { error ->
                    GLog.w(TAG, LogFlag.DL) { "cropBitmapFromView failed: $error" }
                    null
                }
                onScreenshotReady(result)
            }
        })
    }

    /**
     * 1.创建一个圆角矩形 Bitmap
     * 2.原有的Bitmap作为背景，在画布上绘制一层透明度的覆盖层
     * @param bitmap 待处理的 Bitmap
     * @param cornerRadius 圆角半径
     */
    @JvmStatic
    fun createRoundedBitmap(bitmap: Bitmap, cornerRadius: Float, context: Context): Bitmap {
        // 创建一个新的透明 Bitmap，大小与原图相同，并且考虑到边框宽度
        val output = Bitmap.createBitmap(
            bitmap.width,
            bitmap.height,
            Bitmap.Config.ARGB_8888
        )
        val canvas = Canvas(output)

        // 初始化 Paint 对象，并设置抗锯齿和滤波器以提高图像质量
        val paint = Paint().apply {
            isAntiAlias = true
            isFilterBitmap = true
            isDither = true
        }

        // 创建一个圆角矩形，作为 Bitmap 的形状
        val rectF = RectF(0f, 0f, bitmap.width.toFloat(), bitmap.height.toFloat())

        // 在画布上绘制圆角矩形
        canvas.drawRoundRect(rectF, cornerRadius, cornerRadius, paint)

        // 设置 Xfermode 为 PorterDuffXfermode，模式为 SRC_IN，这将只保留源图像与目标图像重叠的部分
        paint.xfermode = PorterDuffXfermode(PorterDuff.Mode.SRC_IN)

        // 绘制原始 Bitmap 到画布上，但只有在圆角矩形内的部分才会被保留
        canvas.drawBitmap(bitmap, 0f, 0f, paint)

        // 添加覆盖层
        val overlayPaint = Paint().apply {
            isAntiAlias = true
            color = ContextCompat.getColor(context, R.color.photo_editor_ai_best_take_choose_view_overlay_color)
            alpha = OVERLAY_ALPHA // 设置透明度，0-255之间
        }
        // 填充整个区域
        overlayPaint.style = Paint.Style.FILL
        canvas.drawRoundRect(rectF, cornerRadius, cornerRadius, overlayPaint)
        return output
    }

    /**
     * 添加两层阴影
     * @param sourceBitmap 源 Bitmap
     * @param cornerRadius 圆角半径
     */
    @JvmStatic
    fun addShadowToBitmap(sourceBitmap: Bitmap, cornerRadius: Float, context: Context): Bitmap {
        // 创建一个新的Bitmap来容纳源Bitmap和其阴影
        val resultBitmap = Bitmap.createBitmap(
            sourceBitmap.width + SHADOW_MARGIN, // 增加宽度以容纳阴影偏移
            sourceBitmap.height + SHADOW_MARGIN,
            Bitmap.Config.ARGB_8888
        )
        val canvas = Canvas(resultBitmap)

        val paint = Paint().apply {
            isAntiAlias = true
            isFilterBitmap = true
            isDither = true
        }

        // 第一层阴影偏移量：只向右下12偏移
        val firstShadowOffsetY = FIRST_SHADOW_OFFSET_Y
        // 第二层阴影偏移量，在原有基础上再向下偏移24像素
        val secondShadowOffsetY = SECOND_SHADOW_OFFSET_Y

        // 绘制第一层阴影图层
        val firstShadowPaint = Paint(paint).apply {
            color = ContextCompat.getColor(context, R.color.photo_editor_ai_best_take_choose_view_shadow_color) // 第一层阴影颜色和透明度
            maskFilter = BlurMaskFilter(SHADOW_BLUR_RADIUS, BlurMaskFilter.Blur.NORMAL)
        }

        // 绘制第二层阴影图层，进一步偏移
        val secondShadowPaint = Paint(paint).apply {
            color = ContextCompat.getColor(context, R.color.photo_editor_ai_best_take_choose_view_shadow_color) // 第二层阴影颜色更浅或不同以示区别
            maskFilter = BlurMaskFilter(SHADOW_BLUR_RADIUS, BlurMaskFilter.Blur.NORMAL)
        }

        // 计算源bitmap放置的位置，确保阴影完全显示
        val rectF = RectF(
            (SHADOW_MARGIN / 2).toFloat(), (SHADOW_MARGIN / 2).toFloat(), (sourceBitmap.width + (SHADOW_MARGIN / 2)).toFloat(),
            (sourceBitmap.height + (SHADOW_MARGIN / 2)).toFloat()
        )

        /**
         * 先绘制最底层的阴影，然后是上层的阴影，最后是源图像
         * 注意：这里使用了相同的cornerRadius来保证阴影和原图的圆角一致
         *
         */
        canvas.drawRoundRect(
            RectF(rectF.left, rectF.top + secondShadowOffsetY, rectF.right, rectF.bottom + secondShadowOffsetY),
            cornerRadius, cornerRadius, secondShadowPaint
        )

        canvas.drawRoundRect(
            RectF(rectF.left, rectF.top + firstShadowOffsetY, rectF.right, rectF.bottom + firstShadowOffsetY),
            cornerRadius, cornerRadius, firstShadowPaint
        )

        /**
         * 最后绘制源Bitmap，注意这里我们直接绘制位图，而不是用drawRoundRect
         * 如果需要裁剪成圆角矩形，可以考虑先绘制到一个临时Canvas上并应用PorterDuffXfermode
         */
        canvas.drawBitmap(sourceBitmap, (SHADOW_MARGIN / 2).toFloat(), (SHADOW_MARGIN / 2).toFloat(), null)
        return resultBitmap
    }
}

/**
 * 获取外扩成 1:1 宽高比的区域，将整个人脸区域包裹住。
 * @param width 原图宽
 * @param height 原图高
 */
fun RectF.toRatio1To1(width: Int?, height: Int?): RectF {
    width ?: return this
    height ?: return this
    val faceWidth = width()
    val faceHeight = height()
    val rect = RectF()
    if (faceWidth > faceHeight) {
        val offset = ((faceWidth - faceHeight) / 2)
        val leftOffset = left + offset
        val rightOffset = right - offset
        rect.top = top
        rect.bottom = bottom
        rect.left = leftOffset
        rect.right = rightOffset
    } else {
        val offset = ((faceHeight - faceWidth) / 2)
        val topOffset = top + offset
        val bottomOffset = bottom - offset
        rect.top = topOffset
        rect.bottom = bottomOffset
        rect.left = left
        rect.right = right
    }
    return rect
}

/**
 * 将硬件加速的 Bitmap 转换为可修改的 Bitmap。
 * @param hardwareBitmap 硬件加速的 Bitmap
 * @return 可修改的 Bitmap
 */
fun convertHardwareBitmapToMutable(hardwareBitmap: Bitmap): Bitmap {
    val newBitmap = Bitmap.createBitmap(
        hardwareBitmap.width,
        hardwareBitmap.height,
        Bitmap.Config.ARGB_8888
    )
    val bitmap = hardwareBitmap.copy(Bitmap.Config.ARGB_8888, true)
    val canvas = Canvas(newBitmap)
    val paint = Paint()
    canvas.drawBitmap(bitmap, 0f, 0f, paint)
    return newBitmap
}