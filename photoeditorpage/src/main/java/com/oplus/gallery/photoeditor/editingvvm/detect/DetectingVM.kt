/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DetectingVM
 ** Description:
 ** Version: 1.0
 ** Date : 2024/6/3
 ** Author: wudanyang
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  wudanyang                      2024/6/3    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.detect

import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.graphics.Rect
import androidx.annotation.WorkerThread
import androidx.collection.arrayMapOf
import androidx.core.graphics.times
import androidx.core.graphics.toRect
import androidx.core.graphics.toRectF
import com.oplus.aiunit.eraser.client.ImageDefectClient
import com.oplus.aiunit.eraser.data.DefectData
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.cache.memorycache.BitmapPools
import com.oplus.gallery.foundation.cvimageprocess.detect.FaceDetectVendor
import com.oplus.gallery.foundation.cvimageprocess.detect.FaceDetectorProxy
import com.oplus.gallery.foundation.cvimageprocess.detect.data.IpuBeautyExtendData
import com.oplus.gallery.foundation.opengl2.texture.ITexture
import com.oplus.gallery.foundation.opengl2.texture.toBitmapSafety
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.getOrLog
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.BEAUTY_COMPONENT_VERSION
import com.oplus.gallery.framework.abilities.download.Constants.INVALID_VERSION
import com.oplus.gallery.framework.abilities.editing.asset.MetadataType
import com.oplus.gallery.framework.abilities.editing.effect.AvEffect
import com.oplus.gallery.framework.abilities.editing.sink.DefaultBitmapSink
import com.oplus.gallery.framework.abilities.editing.sink.IAvSink
import com.oplus.gallery.framework.abilities.editing.track.usage.IAvEffectUsage
import com.oplus.gallery.framework.abilities.scan.face.IFaceScanAbility
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.common.Config.Loader.PREVIEW_PHOTO_MAX_LENGTH
import com.oplus.gallery.photoeditor.editingvvm.EditingSubVM
import com.oplus.gallery.photoeditor.editingvvm.EditingVM
import com.oplus.gallery.photoeditor.editingvvm.EmptyUnitReplier
import com.oplus.gallery.photoeditor.editingvvm.TObserver
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Detecting.TOPIC_DETECTING_ALL
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Detecting.TOPIC_DETECTING_BITMAP
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Detecting.TOPIC_DETECTING_BY_STRATEGY
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Detecting.TOPIC_DETECTING_RESULT_BY_ALGO
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Detecting.TOPIC_DETECTING_TARGET
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_OPERATING_EFFECT_USAGE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Pipeline.TOPIC_PIPELINE_MAIN_TEXTURE
import com.oplus.gallery.photoeditor.editingvvm.UnitReplier
import com.oplus.gallery.photoeditor.editingvvm.beauty.data.CombinableBeautyRecord.Companion.ARGUMENT_CMD_FACE_DETECTION
import com.oplus.gallery.photoeditor.editingvvm.beauty.data.CombinableBeautyRecord.Companion.ARGUMENT_KEY_FACE_DETECTION
import com.oplus.gallery.photoeditor.editingvvm.beauty.ipu.BeautyExtendDataFinder
import com.oplus.gallery.photoeditor.editingvvm.component.beauty.BeautyModelConfig
import com.oplus.gallery.photoeditor.editingvvm.component.beauty.BeautyModelConfig.Companion.BEAUTY_SOURCES
import com.oplus.gallery.photoeditor.editingvvm.get
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecord.Companion.GLOBAL_KEY_EXECUTE_COMMAND
import com.oplus.gallery.photoeditor.editingvvm.pagecontainer.isSpecificStrategy
import com.oplus.gallery.photoeditor.editingvvm.subscribeT
import com.oplus.gallery.photoeditor.editingvvm.subscribeUntilConsumed
import com.oplus.gallery.photoeditor.renderer.texture.PipelineTexture
import com.oplus.gallery.photoeditor.track.CommonEditorTrackConstant
import com.oplus.gallery.photoeditor.track.CommonEditorTracker
import com.oplus.gallery.standard_lib.app.CPU
import com.oplus.gallery.standard_lib.app.EditingLongTime
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.standard_lib.file.File
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlin.math.max
import kotlin.system.measureTimeMillis

/**
 * 负责图片检测相关
 * @param editingVM 主ViewModel
 */
internal class DetectingVM(editingVM: EditingVM) : EditingSubVM(editingVM) {

    /**
     * 这里改成一个map，以对应不同的检测类型
     */
    private val detectingResults: MutableMap<DetectAlgo, DetectingResult> = mutableMapOf()

    /**
     * 回调存储到一个map 中，应对同时进行多项检测，回调错乱的问题
     */
    private val detectingArgMap: MutableMap<Int, DetectingArg> = mutableMapOf()

    /**
     * 当前不支持同时进行两项检测，如果出现两项检测，会出现异常
     */
    private val detectingByStrategyRep = object : UnitReplier<DetectingArg>() {
        override fun onSingleReply(arg: DetectingArg) {
            detectingByStrategy(arg)
        }
    }

    private val detectingTargetRep = object : UnitReplier<Bitmap>() {
        override fun onSingleReply(arg: Bitmap) {
            executeScanningDetect(arg) { faceRectList ->
                detectingResults[DetectAlgo.DETECT_SCANNING] = DetectingResult.Face(
                    faceState = faceRectList?.isNotEmpty()?.let { if (it) FaceState.FACE else FaceState.NO_FACE } ?: FaceState.NO_FACE,
                    faceRectList = faceRectList
                )
            }
        }
    }

    private val detectingBitmapRep = object : UnitReplier<DetectingBitmapArg>() {
        override fun onSingleReply(arg: DetectingBitmapArg) {
            executeScanningDetect(arg.bitmap) { faceRectList ->
                arg.result(
                    DetectingResult.Face(
                        faceState = faceRectList?.isNotEmpty()?.let { if (it) FaceState.FACE else FaceState.NO_FACE } ?: FaceState.NO_FACE,
                    faceRectList = faceRectList
                ))
            }
        }
    }

    private val detectingAllRep = object : EmptyUnitReplier() {
        override fun onEmptyReply() {
            // 这里始终只跑一次，被调用时才跑，否则如果实时监听mainTexture，会加重mainTexture更新负载
            launch(Dispatchers.UI) {
                vmBus?.subscribeUntilConsumed<ITexture>(TOPIC_PIPELINE_MAIN_TEXTURE, publishAtOnce = true) { texture ->
                    GLog.d(TAG, LogFlag.DL, "[detectingAllRep] subscribeUntilConsumed , texture:$texture")
                    // 防止多线程并发情况下编辑退出了还发送请求到管线
                    if (isDestroyed.not()) {
                        detectAllIfNecessary(texture)
                    }
                    return@subscribeUntilConsumed true
                }
            }
        }
    }

    private val onEffectUsageChanged: TObserver<IAvEffectUsage> = {
        resetBeautyDetectingResultIfNeed(it)
        resetDetectByScanningResultIfNeed(it)
    }

    private val isSupportIpuBeauty: Boolean by lazy {
        ConfigAbilityWrapper.getBoolean(
            ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_IPU_BEAUTY,
            false
        )
    }

    /**
     * 接受补光界面的检测结果，并保存
     */
    private val detectResultRep = object : UnitReplier<DetectingResult>() {
        override fun onSingleReply(arg: DetectingResult) {
            if (arg is DetectingResult.Person) {
                detectingResults[DetectAlgo.DETECT_PERSON] = arg
            }
        }
    }

    init {
        vmBus?.apply {
            register(TOPIC_DETECTING_ALL, detectingAllRep)
            register(TOPIC_DETECTING_TARGET, detectingTargetRep)
            register(TOPIC_DETECTING_BITMAP, detectingBitmapRep)
            register(TOPIC_DETECTING_BY_STRATEGY, detectingByStrategyRep)
            register(TOPIC_DETECTING_RESULT_BY_ALGO, detectResultRep)
            subscribeT(TOPIC_OPERATING_EFFECT_USAGE, onEffectUsageChanged)
        }
    }

    private fun detectAllIfNecessary(texture: ITexture) {
        // 如果不是普通编辑一级页，不触发检测逻辑
        if (vmBus?.isSpecificStrategy() == true) {
            GLog.d(TAG, LogFlag.DL, "[detectIfNecessary] specific page.")
            return
        }
        if (texture !is PipelineTexture) {
            GLog.d(TAG, LogFlag.DL, "[detectIfNecessary] invalid texture.")
            return
        }
        val usage = editingVM.sessionProxy.getAliveUsage(texture.requestCode)

        GLog.d(TAG, LogFlag.DL, "[detectIfNecessary] detect triggered, effectName = ${usage?.effect?.name}")
        // 这里判断一下是否需要重新触发扫描
        when (usage?.effect) {
            //换图插件需要重跑一次检测
            AvEffect.OliveEffect,
            AvEffect.ImageReplaceEffect -> {
                // 内容图变化，需要更新内容图的检测结果
                executeScanningDetect(texture) { faceRectList ->
                    setScanningDetectResult(faceRectList)
                }
            }

            // 其它case只有第一次需要更新
            else -> {
                if (!detectingResults.containsKey(DetectAlgo.DETECT_SCANNING)) {
                    executeScanningDetect(texture) { faceRectList ->
                        setScanningDetectResult(faceRectList)
                        // 首次检测完，保存人脸信息用作埋点使用，无需频繁更新
                        saveForTracking()
                    }
                }
                if (!detectingResults.containsKey(DetectAlgo.DETECT_BEAUTY)) {
                    launch(Dispatchers.IO) {
                        detectingByBeauty(DetectingArg(R.id.strategy_beauty) {})
                    }
                }
            }
        }
    }

    private fun setScanningDetectResult(faceRectList: List<Rect>?): DetectingResult {
        val result = DetectingResult.Face(
            faceState = faceRectList?.isNotEmpty()?.let { if (it) FaceState.FACE else FaceState.NO_FACE } ?: FaceState.NO_FACE,
            faceRectList = faceRectList
        )
        detectingResults[DetectAlgo.DETECT_SCANNING] = result
        return result
    }

    private fun setBeautyDetectResult(hasFace: Boolean?, faceRectList: List<Rect>?): DetectingResult {
        val result = DetectingResult.Face(
            faceState = hasFace?.let { if (it) FaceState.FACE else FaceState.NO_FACE } ?: FaceState.UNKNOWN,
            faceRectList = faceRectList
        )
        detectingResults[DetectAlgo.DETECT_BEAUTY] = result
        return result
    }

    override fun onDestroy() {
        super.onDestroy()
        vmBus?.apply {
            unregister(TOPIC_DETECTING_ALL, detectingAllRep)
            unregister(TOPIC_DETECTING_TARGET, detectingTargetRep)
            unregister(TOPIC_DETECTING_BITMAP, detectingBitmapRep)
            unregister(TOPIC_DETECTING_BY_STRATEGY, detectingByStrategyRep)
            unregister(TOPIC_DETECTING_RESULT_BY_ALGO, detectResultRep)
            unsubscribe(TOPIC_OPERATING_EFFECT_USAGE, onEffectUsageChanged)
        }
    }

    /**
     * 未下载美颜SO文件时，返回true。否则返回false。
     */
    private fun isBeautyNotDownloaded(): Boolean {
        return ConfigAbilityWrapper.getInt(BEAUTY_COMPONENT_VERSION, INVALID_VERSION) == INVALID_VERSION
    }

    private fun detectingByStrategy(detectingArg: DetectingArg) {
        detectingArgMap[detectingArg.strategyID] = detectingArg
        detectingArgMap[detectingArg.strategyID]?.let { arg ->
            launch {
                when (arg.strategyID) {
                    // 美颜还是用美颜库的检测方式，否则用标签的人脸扫描扫出人脸但是美颜库扫不出，或者反过来，都有问题
                    R.id.strategy_beauty -> detectingByBeauty(arg)
                    // 表情优化需要单独扫描
                    R.id.strategy_ai_best_take -> detectingByAiBestTake(arg)
                    // ai修复和调节使用扫描的检测能力
                    R.id.strategy_repair, R.id.strategy_adjustment -> detectByScanning(arg)
                    // ai补光 进入时进行人物主体检测
                    R.id.strategy_ai_lighting -> detectPersonIfNecessary(arg)
                    // 其它case能拿就拿，不需要触发扫描
                    else -> {
                        arg.invokeCallback(
                            detectingArgMap, detectingResults[DetectAlgo.DETECT_SCANNING] ?: DetectingResult.Face(FaceState.UNKNOWN)
                        )
                    }
                }
            }
        }
    }

    private fun resetBeautyDetectingResultIfNeed(effectUsage: IAvEffectUsage?) {
        val detectingResult = detectingResults[DetectAlgo.DETECT_BEAUTY]
        GLog.d(TAG, LogFlag.DL) { "[resetBeautyDetectingResultIfNeed] effectUsage：${effectUsage?.effect}, detectingResult:$detectingResult" }
        if (effectUsage?.effect.needResetBeautyDetectEffect() && (detectingResult != null)) {
            detectingResults[DetectAlgo.DETECT_BEAUTY] = DetectingResult.Face(
                faceState = FaceState.UNKNOWN,
                faceRectList = null
            )
        }
    }

    private fun resetDetectByScanningResultIfNeed(effectUsage: IAvEffectUsage?) {
        val detectingResult = detectingResults[DetectAlgo.DETECT_SCANNING]
        GLog.d(TAG, LogFlag.DL) { "[resetDetectByScanningResultIfNeed] effectUsage：${effectUsage?.effect}, detectingResult:$detectingResult" }

        if (effectUsage?.effect.needResetScanningDetectEffect() && (detectingResult != null)) {
            detectingResults[DetectAlgo.DETECT_SCANNING] = DetectingResult.Face(
                faceState = FaceState.UNKNOWN,
                faceRectList = null
            )
        }
    }

    private fun AvEffect?.needResetBeautyDetectEffect(): Boolean {
        return when (this) {
            AvEffect.OliveEffect,
            AvEffect.TransformEffect,
            AvEffect.TextEffect,
            AvEffect.MosaicEffect,
            AvEffect.AiEliminateEffect,
            AvEffect.StickerEffect,
            AvEffect.DoodleEffect,
            AvEffect.AiGraffitiEffect,
            AvEffect.PmStickerEffect,
            AvEffect.ImageReplaceEffect -> true

            else -> false
        }
    }

    private fun AvEffect?.needResetScanningDetectEffect(): Boolean {
        return when (this) {
            AvEffect.TransformEffect, AvEffect.PortraitBlurEffect -> true
            else -> false
        }
    }

    private fun detectingByBeauty(arg: DetectingArg) {
        val detectingResult = detectingResults[DetectAlgo.DETECT_BEAUTY]
        if (detectingResult != null && (detectingResult as? DetectingResult.Face)?.faceState?.hasDetected() == true) {
            arg.invokeCallback(detectingArgMap, detectingResult)
        } else {
            detectBeautyFace { hasFace ->
                val texture = vmBus?.get<ITexture>(TOPIC_PIPELINE_MAIN_TEXTURE).getOrLog(TAG, "detectingByBeauty texture") ?: return@detectBeautyFace
                executeScanningDetect(texture) { faceRectList ->
                    setBeautyDetectResult(hasFace, faceRectList).also {
                        arg.invokeCallback(detectingArgMap, it)
                    }
                }
            }
        }
    }

    /**
     * 不需要使用缓存，实时扫描，避免二次编辑的时候，人脸被遮挡了还是能进来
     */
    private fun detectingByAiBestTake(arg: DetectingArg) {
        val texture = vmBus?.get<ITexture>(TOPIC_PIPELINE_MAIN_TEXTURE) ?: let {
            GLog.w(TAG, LogFlag.DL, "[detectingByAiBestTake] texture is null")
            return
        }
        executeScanningDetect(texture) { faceRectList ->
            GLog.d(TAG, LogFlag.DL) { " [detectingByAiBestTake] result faceRectList size = ${faceRectList?.size}" }
            setScanningDetectResult(faceRectList).also {
                arg.invokeCallback(detectingArgMap, it)
            }
        }
    }

    private fun detectByScanning(arg: DetectingArg) {
        val detectingResult = detectingResults[DetectAlgo.DETECT_SCANNING]
        if (detectingResult != null && (detectingResult as? DetectingResult.Face)?.faceState?.hasDetected() == true) {
            arg.invokeCallback(detectingArgMap, detectingResult)
        } else {
            val texture = vmBus?.get<ITexture>(TOPIC_PIPELINE_MAIN_TEXTURE) ?: let {
                GLog.w(TAG, LogFlag.DL, "[detectingByStrategy] texture is null")
                arg.invokeCallback(detectingArgMap, detectingResult ?: DetectingResult.Face(FaceState.UNKNOWN))
                return
            }
            executeScanningDetect(texture) { faceRectList ->
                setScanningDetectResult(faceRectList).also {
                    arg.invokeCallback(detectingArgMap, it)
                }
            }
        }
    }

    /**
     * 保存人脸是否包含人脸信息，埋点需要
     */
    private fun saveForTracking() {
        val hasFace = if (detectingResults.any { (it.value as? DetectingResult.Face)?.faceState == FaceState.FACE }) {
            CommonEditorTrackConstant.Value.HAS_FACE
        } else {
            CommonEditorTrackConstant.Value.NO_FACE
        }
        CommonEditorTracker.saveFace(hasFace)
    }

    private fun detectBeautyFace(callback: (Boolean?) -> Unit) {
        if (isSupportIpuBeauty) detectFaceByIpuBeauty(callback) else detectFaceByLocalBeauty(callback)
    }

    @SuppressLint("UnsafeDynamicallyLoadedCode")
    private fun loadLibs() {
        val config = BeautyModelConfig()
        val path = config.defaultComponentDirPath.absolutePath
        for (beautySource in BEAUTY_SOURCES) {
            runCatching {
                System.load("$path${File.separator}$beautySource")
            }.onFailure {
                GLog.w(TAG, LogFlag.DL) { "loadLibs: failed: $path${File.separator}$beautySource" }
            }
        }
    }

    /**
     * 通过本地美颜检测人脸
     */
    private fun detectFaceByLocalBeauty(callback: (Boolean?) -> Unit) {
        if (isBeautyNotDownloaded()) {
            GLog.w(TAG, LogFlag.DL, "[executeBeautyDetect] Beauty so file not download yet. Can't detect face.")
            callback(null)
            return
        }
        runCatching {
            loadLibs()
            editingVM.sessionProxy.addObservableEffect(
                AvEffect.BeautyEffect,
                arrayMapOf(
                    GLOBAL_KEY_EXECUTE_COMMAND to ARGUMENT_CMD_FACE_DETECTION,
                    ARGUMENT_KEY_FACE_DETECTION to false
                ),
                ARGUMENT_KEY_FACE_DETECTION
            ) {
                callback((it as? Boolean) == true)
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "[executeBeautyDetect] error. ${it.message}")
            callback(null)
        }
    }

    /**
     * 解析美颜的扩展数据
     */
    private fun parseBeautyExtendData(): IpuBeautyExtendData {
        val beautyInfo = editingVM.sessionProxy.getMetadata(
            MetadataType.EXTENDED,
            BeautyExtendDataFinder.EXTENSION_KEY_BEAUTY_INFO
        ) as? ByteArray
        return BeautyExtendDataFinder.getBeautyExtendData(beautyInfo)
    }

    /**
     * 通过ipu美颜检测人脸
     */
    private fun detectFaceByIpuBeauty(callback: (Boolean?) -> Unit) {
        var startTime = System.currentTimeMillis()
        requestStackTopBitmap(onBitmapReceive = { bitmap ->
            GLog.d(TAG, LogFlag.DL) {
                "[detectFaceByIpuBeauty], getDefaultBitmap passedBitmap wh=${bitmap.width},${bitmap.height}, cost time= ${
                    GLog.getTime(
                        startTime
                    )
                }"
            }
            startTime = System.currentTimeMillis()
            val data = parseBeautyExtendData()
            GLog.d(TAG, LogFlag.DL) {
                "[detectFaceByIpuBeauty], parseExtBeautyInfo data=$data, cost time= ${GLog.getTime(startTime)}"
            }
            startTime = System.currentTimeMillis()

            val faceDetectorProxy = FaceDetectorProxy(vendor = FaceDetectVendor.IPU_BEAUTY, context = app.applicationContext)
            val hasFace = faceDetectorProxy.use {
                it.detectFace(bitmap, data).hasFace
            }
            GLog.d(TAG, LogFlag.DL) {
                "[detectFaceByIpuBeauty], detectFace hasFace= $hasFace , cost time= ${GLog.getTime(startTime)}"
            }
            callback.invoke(hasFace)
        })
    }

    private fun executeScanningDetect(texture: ITexture, callback: (List<Rect>?) -> Unit) {
        launch(Dispatchers.CPU) {
            val scale = (ThumbnailSizeUtils.getTargetSizeInType(ThumbnailSizeUtils.getFullThumbnailKey()).toFloat() /
                    max(texture.width, texture.height))
                .coerceAtMost(1f)
            val bitmap = texture.toBitmapSafety(scale, runScope = editingVM::doOnGL)
            bitmap ?: run {
                GLog.d(TAG, LogFlag.DL) { "executeScanningDetect: texture to bitmap is null." }
                return@launch
            }
            executeScanningDetect(bitmap, scale, callback)
        }
    }

    private fun executeScanningDetect(bitmap: Bitmap, scale: Float = 1f, callback: (List<Rect>?) -> Unit) {
        launch(Dispatchers.EditingLongTime) {
            app.getAppAbility<IFaceScanAbility>()?.use { ability ->
                ability.newFaceDetector().use { detector ->
                    val rectResult = detector.detectRect(bitmap)

                    /**
                     * scale后做的扫描，结果需要等比还原
                     * 这里的精度损失目前看现象可以接受，如果后续有高精度要求，这块优化可以回撤（marked by wudanyang）
                     */
                    val faceRectList = rectResult.rectList?.map {
                        if (scale <= 0) {
                            it
                        } else {
                            // 这里先转成RectF，换算，再转回Rect，精度会高点，但还是会偏移几个像素
                            it.toRectF().times(1f / scale).toRect()
                        }
                    }
                    GLog.d(TAG, LogFlag.DL, "[executeScanningDetect] faceRectList:$faceRectList, bitmap:$bitmap")
                    callback(faceRectList)
                }
            } ?: callback(null)
        }
    }

    /**
     * 确认是否需要做人物检测，如果不需要则也需要返回对应的结果
     * @param arg 检测参数
     */
    private fun detectPersonIfNecessary(arg: DetectingArg) {
        if (arg.isClickEnter) {
            //补光点击进入当前不进行主体检测，直接去取检测的结果
            if (detectingResults.containsKey(DetectAlgo.DETECT_PERSON)) {
                arg.invokeCallback(detectingArgMap, detectingResults[DetectAlgo.DETECT_PERSON] ?: DetectingResult.Person(HasPersonState.UNKNOW))
            } else {
                arg.invokeCallback(detectingArgMap, DetectingResult.Person(HasPersonState.UNKNOW))
            }
        } else {
            //每次进入Ai图像助手都清空本地缓存
            detectingResults.remove(DetectAlgo.DETECT_PERSON)
            detectPerson(arg)
        }
    }

    /**
     * 执行人物主体检测
     */
    private fun detectPerson(arg: DetectingArg) {
        requestTextureBtimap(ThumbnailSizeUtils.getTargetSizeInType(ThumbnailSizeUtils.getFullThumbnailKey()).toFloat(), onBitmapReceive = { bitmap ->
            launch(Dispatchers.IO) {
                executePersonDetect(bitmap) { hasPerson, isException ->
                    //检测完成后回收检测图片
                    bitmap.recycle()
                    if (isException) {
                        detectingArgMap[arg.strategyID]?.invokeCallback(detectingArgMap, DetectingResult.Person(HasPersonState.UNKNOW))
                    } else {
                        DetectingResult.Person(if (hasPerson) HasPersonState.PERSON else HasPersonState.NO_PERSON).let {
                            //有检测结果进行缓存
                            detectingResults[DetectAlgo.DETECT_PERSON] = it
                            detectingArgMap[arg.strategyID]?.invokeCallback(detectingArgMap, it)
                        }
                    }
                }
            }
        })
    }

    /**
     * 获取 Texture当前的bitmap
     * @param onBitmapReceive 请求结果, 回调栈顶的 bitmap
     */
    private fun requestTextureBtimap(thumbialSize: Float, onBitmapReceive: (Bitmap) -> Unit) {
        launch(Dispatchers.UI) {
            vmBus?.subscribeUntilConsumed<ITexture>(TOPIC_PIPELINE_MAIN_TEXTURE, publishAtOnce = true) { texture ->
                GLog.d(TAG, LogFlag.DL, "[requestTextureBtimap] subscribeUntilConsumed , texture:$texture")
                // 防止多线程并发情况下编辑退出了还发送请求到管线
                if (isDestroyed.not()) {
                    launch(Dispatchers.CPU) inner@{
                        val scale = (thumbialSize / max(texture.width, texture.height)).coerceAtMost(1f)
                        val bitmap = texture.toBitmapSafety(scale, runScope = editingVM::doOnGL)
                        bitmap ?: run {
                            GLog.d(TAG, LogFlag.DL) { "[requestTextureBtimap]: texture to bitmap is null." }
                            return@inner
                        }
                        onBitmapReceive(bitmap)
                    }
                }
                return@subscribeUntilConsumed true
            }
        }
    }

    /**
     * 请求编辑栈顶 bitmap
     * @param onBitmapReceive 请求结果, 回调栈顶的 bitmap
     */
    private fun requestStackTopBitmap(onBitmapReceive: (Bitmap) -> Unit) {
        val startTime = System.currentTimeMillis()
        var stackTopBitmapSink: DefaultBitmapSink? = null
        stackTopBitmapSink = DefaultBitmapSink(
            PREVIEW_PHOTO_MAX_LENGTH,
            PREVIEW_PHOTO_MAX_LENGTH,
            onBitmapReceive = { bitmap, _, _, responseCode ->
                GLog.d(TAG, LogFlag.DL) {
                    "[requestStackTopBitmap] responseCode = $responseCode  cost time = ${GLog.getTime(startTime)}"
                }
                onBitmapReceive.invoke(bitmap)
                BitmapPools.recycle(bitmap)
                stackTopBitmapSink?.also {
                    editingVM.sessionProxy.removeSink(it.sinkType, it)
                    stackTopBitmapSink = null
                }
            }).also {
            //触发出帧操作
            editingVM.sessionProxy.addAsOnceFrameSink(IAvSink.AvSinkType.BITMAP, sink = it)
        }
    }

    /**
     * 执行补光缺陷检测
     *
     * @param bitmap 原图
     * @param onComplete 补光结果，是否有异常导致false
     * @receiver
     */
    @WorkerThread
    suspend fun executePersonDetect(bitmap: Bitmap, onComplete: (detectResult: Boolean, isException: Boolean) -> Unit) {
        //图像检测 client 初始化
        val defectClient = ImageDefectClient(app.applicationContext)
        val cost = measureTimeMillis {
            defectClient.initSync()
        }
        GLog.d(TAG, LogFlag.DL, "[executePersonDetect] init cost:$cost")
        val start = System.currentTimeMillis()

        val items = mutableListOf<String>()
        //因为这里只需要做人物主体的扫描，所以不传 JUDGE_RELIGHT_BY_STRENGTH
        items.add(DefectData.JUDGE_RELIGHT)

        val defectResult = runCatching {
            defectClient.processStage(bitmap, items)
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "[executePersonDetect] detect fail, ", it)
            return onComplete.invoke(false, true)
        }.getOrNull() ?: let {
            GLog.e(TAG, LogFlag.DL, "[executePersonDetect] detect result is null")
            return onComplete.invoke(false, true)
        }
        defectClient.stopAndClear()
        //如果是 -1 说明没有检测补光项，结果不保存
        if (defectResult.isReLightFlag == NO_IMAGE_RECOGNITION_DETECTED) {
            return onComplete.invoke(false, true)
        }
        GLog.d(TAG, LogFlag.DL) { "[executePersonDetect] cost time:${GLog.getTime(start)}ms, result: $defectResult" }
        //如果这里 defectResult.isReLightFlag == DefectData.DEFECT_RELIGHT 仅代表图片有人物主体
        onComplete.invoke(defectResult.isReLightFlag == DefectData.DEFECT_RELIGHT, false)
    }

    companion object {
        const val TAG = "DetectingVM"
        const val NO_IMAGE_RECOGNITION_DETECTED = -1
    }
}

/**
 * 是否已扫描
 * @return 有人脸、无人脸都返回true；未扫描返回false
 */
internal fun FaceState.hasDetected(): Boolean {
    return (this == FaceState.FACE) || (this == FaceState.NO_FACE)
}

/**
 * 检测参数
 * @param strategyID
 * @param isClickEnter 是否是点击图标发起的检测
 * @param function 检测结果回调
 */
internal data class DetectingArg(
    val strategyID: Int,
    val isClickEnter: Boolean = true,
    private val function: (detectingResult: DetectingResult) -> Unit
) {
    fun invokeCallback(map: MutableMap<Int, DetectingArg>, result: DetectingResult) {
        // 调用原始的结果回调
        function(result)
        // 回调结束后统一执行移除
        map.remove(strategyID)
    }
}

/**
 * 检测图片的参数
 * @param bitmap 待检测的bitmap
 * @param result 拿到bitmap的结果回调
 */
internal data class DetectingBitmapArg(
    val bitmap: Bitmap,
    val result: (detectingResult: DetectingResult) -> Unit
)

/**
 * 检测结果
 *
 * @constructor
 */
sealed class DetectingResult {
    /**
     * 人脸检测的返回结果
     *
     * @property faceState 人脸状态
     * @property faceRectList 人脸框列表
     */
    data class Face(val faceState: FaceState, val faceRectList: List<Rect>? = null) : DetectingResult()

    /**
     * 人物主体检测返回结果
     *
     * @property hasPersonState 人物主体检测状态
     */
    data class Person(val hasPersonState: HasPersonState) : DetectingResult()
}

/**
 * 人物主体状态
 */
enum class HasPersonState {
    /**
     * 没有进行检测
     */
    UNKNOW,

    /**
     * 检测后有人物主体
     */
    PERSON,

    /**
     * 检测后无人物主体
     */
    NO_PERSON
}

/**
 * 人脸状态
 */
enum class FaceState {
    UNKNOWN,
    FACE,
    NO_FACE
}

/**
 * 检测算法
 */
enum class DetectAlgo {
    /**
     * 扫描算法
     */
    DETECT_SCANNING,

    /**
     * 美颜算法
     */
    DETECT_BEAUTY,

    /**
     * 人物主体检测算法
     */
    DETECT_PERSON
}