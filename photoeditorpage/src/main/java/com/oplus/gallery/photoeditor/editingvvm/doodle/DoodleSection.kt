/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DoodleSection
 ** Description:
 ** Version: 1.0
 ** Date : 2024/6/6
 ** Author: lijie
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lijie                      2024/6/6    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.doodle

import android.graphics.ColorSpace
import android.view.Gravity
import android.view.View
import android.widget.RelativeLayout
import androidx.core.view.marginEnd
import androidx.core.view.marginStart
import androidx.core.view.marginTop
import androidx.lifecycle.lifecycleScope
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.business_lib.template.editor.EditorAppUiConfig
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig.getListOrientation
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig.isEditorLandscape
import com.oplus.gallery.business_lib.template.editor.ui.IEditorCustomUI
import com.oplus.gallery.business_lib.template.editor.ui.WindowSizeForm
import com.oplus.gallery.foundation.archv2.ISectionBus
import com.oplus.gallery.foundation.archv2.bus.NotifierChannel
import com.oplus.gallery.foundation.archv2.section.SectionSpec
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.updateMarginRelative
import com.oplus.gallery.foundation.util.math.MathUtils
import com.oplus.gallery.photoeditor.EditingFragment
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.BaseEditingSection
import com.oplus.gallery.photoeditor.editingvvm.EditingVM
import com.oplus.gallery.photoeditor.editingvvm.TObserver
import com.oplus.gallery.photoeditor.editingvvm.TopicID
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_PREVIEW_ANIMATION_STATE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_PREVIEW_RENDER_STATE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_PREVIEW_ANIMATION_COMMAND
import com.oplus.gallery.photoeditor.editingvvm.get
import com.oplus.gallery.photoeditor.editingvvm.notifyOnce
import com.oplus.gallery.photoeditor.editingvvm.preview.PreviewAnimationCommand
import com.oplus.gallery.photoeditor.editingvvm.preview.PreviewAnimationState
import com.oplus.gallery.photoeditor.editingvvm.preview.PreviewRenderState
import com.oplus.gallery.photoeditor.editingvvm.subscribeDuplexT
import com.oplus.gallery.photoeditor.editingvvm.subscribeT
import com.oplus.gallery.photoeditor.editingvvm.unsubscribeDuplexT
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplusos.vfxsdk.doodleengine.Paint
import com.oplusos.vfxsdk.doodleengine.PaintView
import com.oplusos.vfxsdk.doodleengine.toolkit.Toolkit
import com.oplusos.vfxsdk.doodleengine.toolkit.penviews.BallPenView
import com.oplusos.vfxsdk.doodleengine.toolkit.penviews.EraserView
import com.oplusos.vfxsdk.doodleengine.toolkit.penviews.GeometryView
import com.oplusos.vfxsdk.doodleengine.toolkit.penviews.LassoView
import com.oplusos.vfxsdk.doodleengine.toolkit.penviews.MarkView
import com.oplusos.vfxsdk.doodleengine.toolkit.penviews.PenView
import com.oplusos.vfxsdk.doodleengine.toolkit.penviews.PencilView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 标记编辑切片
 * @param sectionBus 总线
 */
internal open class DoodleSection(
    sectionBus: ISectionBus<EditingFragment, EditingVM>
) : BaseEditingSection(sectionBus), IEditorCustomUI {

    /**
     * 绘制视图
     */
    var paintView: PaintView? = null

    /**
     * Toolkit
     */
    private var toolkit: Toolkit? = null

    /**
     * 绘制视图监听器
     */
    private var paintViewAdaptListener: PaintViewAdaptListener? = null

    private var curImgScale: Float = 1.0F

    private var curImgTx: Float = 0F

    private var curImgTy: Float = 0F

    private var isConfigChanged: Boolean = false

    private var doodleNtf: NotifierChannel<Unit>? = null

    private val previewRenderObserver: TObserver<PreviewRenderState> = {
        when (it) {
            is PreviewRenderState.RenderEnd -> {
                if (isConfigChanged) {
                    updateMatrix()
                }
            }

            else -> GLog.d(TAG) { "[previewRenderObserver] $it" }
        }
    }

    private val animationStateObserver: TObserver<PreviewAnimationState> = {
        when (it) {
            PreviewAnimationState.AnimationStart -> onContentAnimationStart()
            PreviewAnimationState.AnimationUpdate -> onContentAnimationUpdate()
            PreviewAnimationState.AnimationEnd -> onContentAnimationEnd()
        }
    }

    private val doodleObserver: TObserver<DoodleUIBean> = {
        when (it.id) {
            DoodleBeanId.INIT -> {
                GLog.d(TAG) { "[doodleObserver] init: ${it.paintViewScale} originBitmap: ${it.originBitmap}" }
                initListener()
                initDoodleView()
                sectionContentView?.isClickable = true
            }

            DoodleBeanId.REFRESH_PAINT_VIEW -> {
                GLog.d(TAG) { "[doodleObserver] refresh paint view: ${it.shouldRefreshPaintView}" }
                paintView?.onPause()
                paintView?.onResume()
            }

            DoodleBeanId.OPERATING_REDO -> {
                GLog.d(TAG) { "[doodleObserver] operating redo" }
                paintView?.redo()
            }

            DoodleBeanId.OPERATING_UNDO -> {
                GLog.d(TAG) { "[doodleObserver] operating undo" }
                paintView?.undo()
            }

            DoodleBeanId.GENERATE_BITMAP -> {
                GLog.d(TAG) { "[doodleObserver] generate bitmap" }
                paintView?.readCanvasData()
            }

            DoodleBeanId.RENDER_END -> {
                GLog.d(TAG) { "[doodleObserver] render end" }
                curImgScale = it.paintViewScale?.curImgScale ?: 1.0F
                curImgTx = it.paintViewScale?.curImgTx ?: 0F
                curImgTy = it.paintViewScale?.curImgTy ?: 0F

                paintView?.updateCanvas(it.paintViewScale?.matrixArray)
                isConfigChanged = false
            }

            DoodleBeanId.START_INIT -> {
                GLog.d(TAG, LogFlag.DL) { "[doodleObserver] start init" }
                if (it.originBitmap != null) {
                    updateMatrix()
                    initPaintView(it)
                }
            }

            else -> Unit
        }
    }

    override fun onCreate() {
        super.onCreate()
        subscribeViewModel()
        val matrix = animationProperties.fullPose.final.affine()
        doodleNtf?.notify(DoodleUIBean(id = DoodleBeanId.INIT, paintViewScale = PaintViewScale(matrix = matrix)))
    }

    private fun updateMatrix() {
        val matrix = animationProperties.fullPose.final.affine()
        doodleNtf?.notify(DoodleUIBean(id = DoodleBeanId.RENDER_END, paintViewScale = PaintViewScale(matrix = matrix)))
    }

    private fun subscribeViewModel() {
        doodleNtf = vBus.subscribeDuplexT(TopicID.Doodle.TOPIC_DOODLE_UI_STATE, doodleObserver)
        vBus.subscribeT(TOPIC_PREVIEW_RENDER_STATE, previewRenderObserver)
        vBus.subscribeT(TOPIC_PREVIEW_ANIMATION_STATE, animationStateObserver)
    }

    private fun unsubscribeViewModel() {
        vBus.unsubscribeDuplexT(TopicID.Doodle.TOPIC_DOODLE_UI_STATE, doodleNtf, doodleObserver)
        vBus.unsubscribe(TOPIC_PREVIEW_RENDER_STATE, previewRenderObserver)
        vBus.unsubscribe(TOPIC_PREVIEW_ANIMATION_STATE, animationStateObserver)
    }

    private fun initListener() {
        if (paintViewAdaptListener != null) {
            GLog.d(TAG, LogFlag.DL) { "paintViewAdaptListener is !=null" }
            return
        }

        paintViewAdaptListener = object : PaintViewAdaptListener() {
            override fun onAddedNode() {
                super.onAddedNode()
                GLog.d(TAG) { "[onAddedNode]" }
                lifecycleScope.launch(Dispatchers.Main) {
                    doodleNtf?.notify(
                        DoodleUIBean(
                            id = DoodleBeanId.UPDATE_DOODLE_CANVAS_BITMAP,
                            doodleCanvasBitmap = paintView?.getCanvasBitmap(),
                        )
                    )
                }
            }

            override fun onGenerateImage() {
                super.onGenerateImage()
                GLog.d(TAG) { "[onGenerateImage]" }
                onPaintViewGenerateImage()
            }

            override fun onZoomStart() {
                super.onZoomStart()
                onPaintViewZoomStart()
            }

            override fun onZooming(x: Float, y: Float, bgScale: Float, canvasScale: Int) {
                super.onZooming(x, y, bgScale, canvasScale)
                onPaintViewZooming(x, y, bgScale)
            }

            override fun onZoomEnd() {
                super.onZoomEnd()
                onPaintViewZoomEnd()
            }
        }
    }

    private fun initDoodleView() {
        toolkit = sectionContainerView?.findViewById(R.id.doodle_toolkit)
        configView()
        sectionBus.hostInstance.context?.resources?.getColor(R.color.picture3d_editor_title_bar_background, null)
            ?.apply {
                toolkit?.setBackground(this)
                setTitleBarContainerBackgroundColor(this)
                setToolbarContainerBackgroundColor(this)
            }
        fillPenViews()
        updateToolbarContainer()
    }

    override fun getContainerViewWidthResourceId(): Int {
        return R.dimen.picture3d_editor_doodle_toolbar_width_landscape
    }

    override fun getContainerViewHeightResourceId(): Int {
        return R.dimen.picture3d_doodle_text_container_bar_height
    }

    private fun initPaintView(doodleUIBean: DoodleUIBean) {
        paintView = sectionBus.hostInstance.context?.let { PaintView(it) }
        paintView?.let { toolkit?.setPaintView(it) }
        val originBitmap = doodleUIBean.originBitmap
        val originBitmapWidth = originBitmap?.width ?: 0
        val originBitmapHeight = originBitmap?.height ?: 0
        if (originBitmapWidth == 0 || originBitmapHeight == 0) {
            GLog.e(TAG, "[initPaintView] originBitmapWidth or originBitmapHeight is 0")
            return
        }
        initListener()
        curImgScale = doodleUIBean.paintViewScale?.curImgScale ?: 1.0F
        curImgTx = doodleUIBean.paintViewScale?.curImgTx ?: 0F
        curImgTy = doodleUIBean.paintViewScale?.curImgTy ?: 0F
        paintView?.apply {
            if (originBitmap?.colorSpace === ColorSpace.get(ColorSpace.Named.DISPLAY_P3)) {
                GLog.d(TAG) { "[initPaintView] this image support p3 color space" }
                this.onCreate(originBitmapWidth, originBitmapHeight, true)
            } else {
                GLog.d(TAG) { "[initPaintView] this image support rgba color space" }
                this.onCreate(originBitmapWidth, originBitmapHeight, false)
            }
            // 震动功能除了升级版本号，相册还需要主动开启,需要在paintview初始化后，主动调用enableVibration接口
            this.enableVibration()
            // initCanvas的逻辑需要放在onStart之前，否则可能会导致sdk拿到默认的数据导致显示错误
            this.initCanvas(doodleUIBean.paintViewScale?.matrixArray)
            this.onStart()
            this.setUnlimitedScale(true)
            this.addListener(paintViewAdaptListener)
            this.setZoomListener(paintViewAdaptListener)
            vBus.notifyOnce(TopicID.Preview.TOPIC_PREVIEW_ENABLE_RENDER_TEXTURE, true)
            this.setBackground(originBitmap)
            addExtraViewToBaseUIView(this, sectionBus.rootView.findViewById(R.id.compare_button))
        }
    }

    private fun setTitleBarContainerBackgroundColor(color: Int) {
        sectionBus.rootView.findViewById<RelativeLayout>(R.id.title_bar_container).setBackgroundColor(color)
    }

    private fun setToolbarContainerBackgroundColor(color: Int) {
        sectionBus.rootView.findViewById<View>(R.id.toolbar_container).setBackgroundColor(color)
    }

    override fun onCallAfterEditorScheme() {
        configView()
    }

    override fun onCallBeforeEditorScheme() {
        configView()
    }

    private fun configView() {
        val view: View = sectionBus.rootView.findViewById(R.id.right_masking) ?: return
        view.visibility = View.VISIBLE
        view.bringToFront()
        post {
            toolkit?.also {
                it.setPadding(
                    it.paddingLeft,
                    MathUtils.ZERO,
                    it.paddingRight,
                    MathUtils.ZERO
                )
            }
        }
    }

    override fun getContentLayoutId(config: AppUiResponder.AppUiConfig): Int {
        return if (EditorUIConfig.isEditorLandscape(config)) {
            R.layout.picture3d_editor_doodle_toolbar_landscape
        } else {
            R.layout.picture3d_editor_doodle_toolbar
        }
    }

    private fun onPaintViewGenerateImage() {
        lifecycleScope.launch(Dispatchers.Main) {
            doodleNtf?.notify(
                DoodleUIBean(
                    id = DoodleBeanId.GENERATE_BITMAP,
                    doodleCanvasBitmap = paintView?.getCanvasBitmap(),
                    hasModified = paintView?.undoStatus ?: false
                )
            )
        }
    }

    override fun getContainerViewID(): Int {
        return R.id.toolbar_container
    }

    private fun fillPenViews() {
        val context = toolkit?.context
        val ballPenView: BallPenView? = context?.let { BallPenView(it, null) }
        if (ballPenView != null) {
            toolkit?.addPenView(ballPenView, false)
        }
        val pencilView: PencilView? = context?.let { PencilView(it, null) }
        if (pencilView != null) {
            toolkit?.addPenView(pencilView, false)
        }
        val markView: MarkView? = context?.let { MarkView(it, null) }
        if (markView != null) {
            toolkit?.addPenView(markView, false)
        }
        val penView: PenView? = context?.let { PenView(it, null) }
        if (penView != null) {
            toolkit?.addPenView(penView, false)
        }
        val eraserView: EraserView? = context?.let { EraserView(it, null) }
        if (eraserView != null) {
            toolkit?.addPenView(eraserView, false)
        }
        val lassoView: LassoView? = context?.let { LassoView(it, null) }
        if (lassoView != null) {
            toolkit?.addPenView(lassoView, false)
        }
        val regularView: GeometryView? = context?.let { GeometryView(it, null) }
        if (regularView != null) {
            toolkit?.addPenView(regularView, true)
        }
    }

    private fun onPaintViewZoomStart() {
        lifecycleScope.launch(Dispatchers.Main) {
            doodleNtf?.notify(
                DoodleUIBean(
                    id = DoodleBeanId.REFRESH_PAINT_VIEW_SCALE_DATA,
                    paintViewScale = PaintViewScale(isOnZooming = true, isOnZoomBack = false)
                )
            )
            paintView?.zoomStart()
        }
    }

    /**
     * 由SDK来绘制，并且接收触摸事件进行缩放。app接收到SDK回调的坐标信息和缩放信息来应用于自身图片的矩阵信息，用于在后续的缩放回弹时使用这些数据。
     * app是通过得到两次回调之间的偏移量来计算自身的矩阵信息
     *
     * @param x       图片在SDK坐标系的坐标x
     * @param y       图片在SDK坐标系的坐标y
     * @param bgScale 图片当前显示的大小相对于原图大小的缩放系数
     */
    private fun onPaintViewZooming(x: Float, y: Float, bgScale: Float) {
        lifecycleScope.launch(Dispatchers.Main) {
            val isOnZooming = vBus.get<DoodleUIBean>(TopicID.Doodle.TOPIC_DOODLE_UI_STATE)?.paintViewScale?.isOnZooming ?: false
            if (!isOnZooming) {
                return@launch
            }
            val offsetX: Float = x - curImgTx
            val offsetY: Float = y - curImgTy
            val offsetScale = if (curImgScale <= 0f) bgScale else bgScale / curImgScale
            vBus.notifyOnce(TOPIC_PREVIEW_ANIMATION_COMMAND, PreviewAnimationCommand.ContentScroll(-offsetX, -offsetY))
            vBus.notifyOnce(TOPIC_PREVIEW_ANIMATION_COMMAND, PreviewAnimationCommand.ContentScale(offsetScale, x, y))
            vBus.notifyOnce(TOPIC_PREVIEW_ANIMATION_COMMAND, PreviewAnimationCommand.ForceFinishAnimation)
            curImgScale = bgScale
            curImgTx = x
            curImgTy = y
            paintView?.zooming()
        }
    }

    private fun onPaintViewZoomEnd() {
        lifecycleScope.launch(Dispatchers.Main) {
            doodleNtf?.notify(
                DoodleUIBean(
                    id = DoodleBeanId.REFRESH_PAINT_VIEW_SCALE_DATA,
                    paintViewScale = PaintViewScale(isOnZooming = false, isOnZoomBack = true)
                )
            )
            paintView?.zoomEnd()
            vBus.notifyOnce(TOPIC_PREVIEW_ANIMATION_COMMAND, PreviewAnimationCommand.SnapBack)
        }
    }

    open fun focusChange(hasFocus: Boolean) {
        doodleNtf?.notify(DoodleUIBean(id = DoodleBeanId.REFRESH_PAINT_VIEW, shouldRefreshPaintView = !hasFocus))
    }

    private fun refreshPaintViewIfNeed() {
        lifecycleScope.launch(Dispatchers.Main) {
            val refreshPaintView = vBus.get<DoodleUIBean>(TopicID.Doodle.TOPIC_DOODLE_UI_STATE)?.shouldRefreshPaintView ?: false
            if (refreshPaintView) {
                doodleNtf?.notify(DoodleUIBean(id = DoodleBeanId.REFRESH_PAINT_VIEW))
            }
        }
    }

    private fun resetViewConfig() {
        val view: View? = sectionBus.rootView.findViewById(R.id.right_masking)
        view?.visibility = View.GONE
        toolkit?.setPadding(
            toolkit?.paddingLeft ?: 0, toolkit?.paddingTop ?: 0,
            toolkit?.paddingRight ?: 0, toolkit?.paddingBottom ?: 0
        )
    }

    override fun onStart() {
        super.onStart()
        paintView?.onStart()
    }

    override fun onResume() {
        super.onResume()
        paintView?.onResume()
    }

    override fun onPause() {
        super.onPause()
        paintView?.onPause()
        doodleNtf?.notify(DoodleUIBean(id = DoodleBeanId.REFRESH_DATA, shouldRefreshPaintView = false))
    }

    override fun onStop() {
        super.onStop()
        paintView?.onStop()
    }

    override fun onDestroy() {
        super.onDestroy()
        paintView?.apply {
            removeExtraViewFromBaseUIView(this)
            removeListener(paintViewAdaptListener)
            removeZoomListener()
            onDestroy()
        }
        setTitleBarContainerBackgroundColor(android.R.color.transparent)
        setToolbarContainerBackgroundColor(android.R.color.transparent)
        resetViewConfig()
        unsubscribeViewModel()
    }

    private fun onContentAnimationStart() {
        GLog.d(TAG) { "[onAnimationStart]" }
        if (paintView != null) {
            lifecycleScope.launch(Dispatchers.Main) {
                updateMatrix()
            }
        }
    }

    private fun onContentAnimationUpdate() {
        lifecycleScope.launch(Dispatchers.Main) {
            doodleNtf?.notify(DoodleUIBean(id = DoodleBeanId.ANIMATE_UPDATED, isAnimateUpdated = true))
        }
    }

    private fun onContentAnimationEnd() {
        lifecycleScope.launch(Dispatchers.Main) {
            doodleNtf?.notify(DoodleUIBean(id = DoodleBeanId.ANIMATE_UPDATED, isAnimateUpdated = false))
        }
    }

    override fun onUiConfigChanged(baseActivity: BaseActivity, layoutId: Int, config: EditorAppUiConfig) {
        super.onUiConfigChanged(baseActivity, layoutId, config)
        val isFirstConfigChanged = vBus.get<DoodleUIBean>(TopicID.Doodle.TOPIC_DOODLE_UI_STATE)?.isFirstConfigChanged ?: false
        if (!isFirstConfigChanged) {
            doodleNtf?.notify(DoodleUIBean(id = DoodleBeanId.CHANGE_UI_CONFIG, isFirstConfigChanged = true))
            return
        }
        if (!config.isSystembarchanged) {
            toolkit?.popupWindowDismiss()
            toolkit?.setToolkitOrientation(getListOrientation(config.appUiConfig))
            isConfigChanged = true
        }
        val refreshPaintView = vBus.get<DoodleUIBean>(TopicID.Doodle.TOPIC_DOODLE_UI_STATE)?.shouldRefreshPaintView ?: false
        if (refreshPaintView) {
            refreshPaintViewIfNeed()
        }
        val isLandscape = isEditorLandscape(config.appUiConfig)
        toolkit?.gravity = if (isLandscape) Gravity.RIGHT else Gravity.BOTTOM
        updateToolbarContainer()
    }

    private fun updateToolbarContainer() {
        val config = sectionBus.hostInstance.getCurrentAppUiConfig()
        var marginStart = 0
        var marginTop = 0
        val resources = sectionBus.rootView.resources
        val sizeForm = WindowSizeForm.getSizeForm(config)
        when (sizeForm) {
            WindowSizeForm.STANDARD_LANDSCAPE -> {
                marginStart = resources.getDimensionPixelOffset(R.dimen.photo_editor_margin_24_dp)
                marginTop = 0
            }

            WindowSizeForm.TABLET_LANDSCAPE -> {
                marginStart = resources.getDimensionPixelOffset(R.dimen.photo_editor_margin_40_dp)
                marginTop = 0
            }

            else -> {
                marginStart = 0
                marginTop = resources.getDimensionPixelOffset(R.dimen.picture3d_editor_preview_margin_top)
            }
        }
        sectionContainerView?.apply {
            setPadding(0, paddingTop, 0, paddingBottom)
            if ((this.marginStart != marginStart)
                || (this.marginTop != marginTop)) {
                updateMarginRelative(marginStart, marginTop, marginEnd, 0)
            }
        }
    }

    internal companion object {
        const val TAG = "DoodleSection"
        private const val ANIMATION_FADE_IN_DURATION = 300
        val SPEC = SectionSpec(DoodleSection::class.java)

        private const val BALL_PEN_DEFAULT_PAINT_COLOR_R = 0.93f
        private const val BALL_PEN_DEFAULT_PAINT_COLOR_G = 0.25f
        private const val BALL_PEN_DEFAULT_PAINT_COLOR_B = 0.25f
        private const val BALL_PEN_DEFAULT_PAINT_COLOR_A = 1.0f

        private const val PEN_DEFAULT_PAINT_COLOR_R = 0.0f
        private const val PEN_DEFAULT_PAINT_COLOR_G = 0.0f
        private const val PEN_DEFAULT_PAINT_COLOR_B = 0.0f
        private const val PEN_DEFAULT_PAINT_COLOR_A = 1.0f

        init {
            val paintList = getDoodleDefaultAttrs()
            Toolkit.Companion.setDefaultPaintAttrs(ContextGetter.context, paintList, Paint.Type.BALLPEN)
        }

        /**
         * 获取DefaultAttrs
         * @param Toolkit 要显示的DefaultAttrs
         */
        private fun getDoodleDefaultAttrs(): ArrayList<Paint> {
            val paintList = ArrayList<Paint>()
            paintList.add(
                Paint(
                    Paint.Type.BALLPEN, Paint.Stroke.TYPE3.ordinal,
                    BALL_PEN_DEFAULT_PAINT_COLOR_R,
                    BALL_PEN_DEFAULT_PAINT_COLOR_G,
                    BALL_PEN_DEFAULT_PAINT_COLOR_B,
                    BALL_PEN_DEFAULT_PAINT_COLOR_A
                )
            )
            paintList.add(
                Paint(
                    Paint.Type.PEN, Paint.Stroke.TYPE3.ordinal,
                    PEN_DEFAULT_PAINT_COLOR_R,
                    PEN_DEFAULT_PAINT_COLOR_G,
                    PEN_DEFAULT_PAINT_COLOR_B,
                    PEN_DEFAULT_PAINT_COLOR_A
                )
            )
            return paintList
        }
    }
}