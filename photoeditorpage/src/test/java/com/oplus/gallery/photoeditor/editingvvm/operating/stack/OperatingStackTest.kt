/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - OperatingStackTest.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/6/28
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2024/6/28        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.operating.stack

import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecord
import io.mockk.every
import io.mockk.mockk
import org.junit.Assert
import org.junit.Test

class OperatingStackTest {
    private fun createRecordList(start: Int, endInclusive: Int): List<OperatingRecord> {
        return IntRange(start, endInclusive).map {
            mockk<OperatingRecord>().apply {
                every { plusAssign(any()) } answers {}
            }
        }
    }

    /**
     * Given: OperatingStack
     * When: push一些记录，undo部分记录，创建次级栈，push一些记录，stackPop(isKeepAsOneStep = false)
     * Then: canUndo和canRedo都为true
     */
    @Test
    fun should_return_true_true_when_exitSubPageStack_false_canUndo_canRedo() {
        val records1 = createRecordList(0, 2)
        val records2 = createRecordList(3, 5)
        val records3 = createRecordList(6, 8)
        val stack = OperatingStack()
        records1.forEach { stack.push(it) }
        records2.forEach { stack.push(it) }
        repeat(records2.size) { stack.undo() }
        stack.addStack()
        records3.forEach { stack.push(it) }
        stack.stackPop(isKeepAsOneStep = false) { _, _ -> }
        Assert.assertTrue(stack.canUndo())
        Assert.assertTrue(stack.canRedo())
    }

    /**
     * Given: OperatingStack
     * When: push一些记录，undo部分记录，创建次级栈，push一些记录，stackPop(isKeepAsOneStep = true)
     * Then: canUndo为true，canRedo为false
     */
    @Test
    fun should_return_true_false_when_exitSubPageStack_true_canUndo_canRedo() {
        val records1 = createRecordList(0, 2)
        val records2 = createRecordList(3, 5)
        val records3 = createRecordList(6, 8)
        val stack = OperatingStack()
        records1.forEach { stack.push(it) }
        records2.forEach { stack.push(it) }
        repeat(records2.size) { stack.undo() }
        stack.addStack()
        records3.forEach { stack.push(it) }
        stack.stackPop(isKeepAsOneStep = true) { _, _ -> }
        Assert.assertTrue(stack.canUndo())
        Assert.assertFalse(stack.canRedo())
    }
}