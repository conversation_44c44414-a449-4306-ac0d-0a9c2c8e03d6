/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - BaseMultiStackTest.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/6/28
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2024/6/28        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.operating.stack

import org.junit.Assert
import org.junit.Test

class MultiStackTest {
    private fun createActionList(start: Int, endInclusive: Int): List<Action> {
        return IntRange(start, endInclusive).map { Action(it) }
    }

    private fun createStack(): MultiStack<Action> {
        return MultiStack<Action>().apply { addStack() }
    }

    /**
     * getActiveElements返回的是push的元素
     */
    @Test
    fun should_return_actions_when_push() {
        val actions = createActionList(0, 2)
        val stack = createStack()
        actions.forEach { stack.push(it) }
        Assert.assertArrayEquals(actions.asReversed().toTypedArray(), stack.getActiveElements().toTypedArray())
    }

    /**
     * getActiveElements返回的是pushAll的元素
     */
    @Test
    fun should_return_actions_when_pushAll() {
        val actions = createActionList(0, 2)
        val stack = createStack()
        stack.pushAll(actions)
        Assert.assertArrayEquals(actions.asReversed().toTypedArray(), stack.getActiveElements().toTypedArray())
    }

    /**
     * getActiveElements返回的是undo的元素且getActiveElements不包含undo的元素
     */
    @Test
    fun should_return_actions_when_undo() {
        val actions = createActionList(0, 2)
        val stack = createStack()
        actions.forEach { stack.push(it) }
        (actions.size - 1).downTo(0).forEach { Assert.assertEquals(actions[it], stack.undo()) }
        Assert.assertArrayEquals(emptyArray(), stack.getActiveElements().toTypedArray())
    }

    /**
     * undo再redo后getActiveElements返回的是原本push的元素
     */
    @Test
    fun should_return_actions_when_redo() {
        val actions = createActionList(0, 2)
        val stack = createStack()
        actions.forEach { stack.push(it) }
        repeat(actions.size) { stack.undo() }
        repeat(actions.size) { stack.redo() }
        Assert.assertArrayEquals(actions.asReversed().toTypedArray(), stack.getActiveElements().toTypedArray())
    }

    /**
     * undo再push后getActiveElements不包含undo的元素
     */
    @Test
    fun should_return_actions_when_undo_push() {
        val actions1 = createActionList(0, 2)
        val actions2 = createActionList(3, 5)
        val actions3 = createActionList(6, 8)
        val stack = createStack()
        actions1.forEach { stack.push(it) }
        actions2.forEach { stack.push(it) }
        repeat(actions2.size) { stack.undo() }
        actions3.forEach { stack.push(it) }
        Assert.assertArrayEquals((actions1 + actions3).asReversed().toTypedArray(), stack.getActiveElements().toTypedArray())
    }

    /**
     * canUndo和canRedo分别返回true和false
     */
    @Test
    fun should_return_true_false_when_canUndo_canRedo() {
        val actions = createActionList(0, 2)
        val stack = createStack()
        actions.forEach { stack.push(it) }
        Assert.assertTrue(stack.canUndo())
        Assert.assertFalse(stack.canRedo())
    }

    /**
     * 部分undo后canUndo和canRedo分别返回true和true
     */
    @Test
    fun should_return_true_true_when_canUndo_canRedo() {
        val actions = createActionList(0, 2)
        val stack = createStack()
        actions.forEach { stack.push(it) }
        stack.undo()
        Assert.assertTrue(stack.canUndo())
        Assert.assertTrue(stack.canRedo())
    }

    /**
     * 全部undo后canUndo和canRedo分别返回false和true
     */
    @Test
    fun should_return_false_true_when_canUndo_canRedo() {
        val actions = createActionList(0, 2)
        val stack = createStack()
        actions.forEach { stack.push(it) }
        repeat(actions.size) { stack.undo() }
        Assert.assertFalse(stack.canUndo())
        Assert.assertTrue(stack.canRedo())
    }

    /**
     * undo再push后canUndo和canRedo分别返回true和false
     */
    @Test
    fun should_return_true_false_when_undo_push_canUndo_canRedo() {
        val actions = createActionList(0, 2)
        val stack = createStack()
        stack.push(actions[0])
        stack.push(actions[1])
        stack.undo()
        stack.push(actions[2])
        Assert.assertTrue(stack.canUndo())
        Assert.assertFalse(stack.canRedo())
    }

    /**
     * destroyStack返回的是push的元素
     */
    @Test
    fun should_return_actions_when_destroyStack() {
        val actions = createActionList(0, 2)
        val stack = createStack()
        actions.forEach { stack.push(it) }
        Assert.assertArrayEquals(actions.asReversed().toTypedArray(), stack.removeStack().toTypedArray())
    }

    /**
     * getActiveElementsAtStack返回的是push的元素
     */
    @Test
    fun should_return_actions_when_getActiveElementsAtStack() {
        val actions = createActionList(0, 2)
        val stack = createStack()
        actions.forEach { stack.push(it) }
        Assert.assertArrayEquals(actions.asReversed().toTypedArray(), stack.getActiveElementsAtStack(0).toTypedArray())
    }

    private data class Action(private val id: Int)
}