import numpy as np
import sys

# Linear RGB to XYZ
def rgb_to_xyz(r, g, b):
    # sRGB to XYZ conversion matrix
    matrix = np.array([[0.4124564, 0.3575761, 0.1804375],
                       [0.2126729, 0.7151522, 0.0721750],
                       [0.0193339, 0.1191920, 0.9503041]])
    return matrix @ np.array([r, g, b])

# XYZ to P3 RGB conversion
def xyz_to_p3(x, y, z):
    # P3 RGB to XYZ conversion matrix
    matrix = np.array([[2.4934969, -0.9313836, -0.4027108],
                       [-0.8294890, 1.7626641, 0.0236247],
                       [0.0358458, -0.0761724, 0.9568845]])
    return matrix @ np.array([x, y, z])

# Ensure values are in range [0, 1]
def clamp(val):
    return np.clip(val, 0, 1)

# Convert linear RGB to P3 RGB
def linear_rgb_to_p3(r, g, b):
    # Step 1: Convert linear RGB to XYZ
    x, y, z = rgb_to_xyz(r, g, b)
    
    # Step 2: Convert XYZ to P3 RGB
    p3_rgb = xyz_to_p3(x, y, z)
    
    # Step 3: Clamp P3 RGB values to [0, 1]
    return np.array([clamp(c) for c in p3_rgb])

# Convert input RGB values (0-255) to linear RGB
def rgb_to_linear(rgb):
    return rgb / 255.0

# do gamma correction for gamma as 2.2
def inv_oetf(x):
    if x < 0.04045:
        return x / 12.92
    else:
        return pow((x + 0.055) / 1.055, 2.4)
		
def srgbOetf(x):
    if x < 0.00304:
	    return 12.92 * x
    else:
        return 1.055 * pow(x, 1/2.4) - 0.055	

# Convert linear RGB values (0-1) to P3 RGB
def rgb_to_p3(rgb):
    # Step 1: Convert RGB to linear RGB
    linear_rgb = rgb_to_linear(rgb)
    gamma_rgb = [inv_oetf(x) for x in linear_rgb]
    
    # Step 2: Convert linear RGB to P3 RGB
    p3_gamma_rgb = linear_rgb_to_p3(*gamma_rgb)
    return np.array([round(clamp(srgbOetf(p3_x)) * 255) for p3_x in p3_gamma_rgb])

def main(args):
    colorCode = args[1]
    sr = int(colorCode[1:3], 16)
    sg = int(colorCode[3:5], 16)
    sb = int(colorCode[5:7], 16)
    input_rgb = np.array([sr, sg, sb])  # Example input (0-255 range)
    p3_rgb = rgb_to_p3(input_rgb)
    print("srgb {},{},{} to p3 {},{},{}".format(sr, sg, sb, p3_rgb[0], p3_rgb[1], p3_rgb[2]))
    print("srgb {} to p3 #{:X}{:X}{:X}".format(colorCode, p3_rgb[0], p3_rgb[1], p3_rgb[2]))


if __name__ == '__main__':
    main(sys.argv)