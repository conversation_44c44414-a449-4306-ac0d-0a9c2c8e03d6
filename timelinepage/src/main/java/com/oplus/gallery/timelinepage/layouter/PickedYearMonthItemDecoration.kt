/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : PickedMonthItemDecoration.kt
 ** Description : 月视图Item装饰的布局
 ** Version     : 1.0
 ** Date        : 2023/2/14 20:15
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2023/2/14  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.timelinepage.layouter

import android.graphics.Rect
import com.oplus.gallery.business_lib.timeline.layout.BaseItemDecoration

/**
 * 月视图Item装饰的布局
 */
class PickedYearMonthItemDecoration(index: Int) : BaseItemDecoration(index) {
    /**
     * 标题区点击热区
     */
    val hotRegionRect: Rect = Rect()

    /**
     * 一级标题位置
     */
    val titleRect: Rect = Rect()

    /**
     * 箭头位置
     */
    val arrowRect: Rect = Rect()

    /**
     * 二级标题位置
     */
    val subtitleRect: Rect = Rect()
}