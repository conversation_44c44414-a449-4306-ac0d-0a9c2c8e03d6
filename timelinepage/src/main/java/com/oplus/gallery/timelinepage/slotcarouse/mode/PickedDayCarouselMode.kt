/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - CarouselModel
 ** Description: 日视图轮播模式
 **
 ** Version: 1.0
 ** Date: 2023/03/22
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D  2023/03/22  1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.timelinepage.slotcarouse.mode

import com.oplus.gallery.business_lib.timeline.view.TimelineView
import com.oplus.gallery.business_lib.timeline.viewmodel.TimelineViewModel
import com.oplus.gallery.timelinepage.slotcarouse.CarouselSource

/**
 * 精选日视图轮播模式
 */
class PickedDayCarouselMode(
    timelineView: TimelineView,
    timelineViewModel: TimelineViewModel
) : CarouselMode(timelineView, timelineViewModel) {

    override fun getVideoDuration(duration: Int) = duration

    override fun convertIndexToSource(list: List<Int>): List<CarouselSource> {
        return if (list.isNotEmpty()) {
            createCarouselSource(list.first())?.let {
                listOf(it)
            } ?: emptyList()
        } else emptyList()
    }
}