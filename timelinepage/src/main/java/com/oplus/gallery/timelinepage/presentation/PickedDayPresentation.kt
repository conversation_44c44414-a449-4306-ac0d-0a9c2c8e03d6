/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - PickedDayPresentation.kt
 * Description:
 * Version: 1.0
 * Date: 2021/11/16
 ** Author: <EMAIL>
 * TAG:
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                   <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * <EMAIL>      2021/11/16     1.0
 *************************************************************************************************/
package com.oplus.gallery.timelinepage.presentation

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.Rect
import com.oplus.gallery.basebiz.permission.helper.PermissionHelper
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.business_lib.model.data.base.timeline.TimeNode
import com.oplus.gallery.business_lib.model.data.memories.userprofile.UserProfileSettings
import com.oplus.gallery.business_lib.timeline.TimelineConfig.DAY
import com.oplus.gallery.business_lib.timeline.TimelineConfig.DAY_LESS_COL
import com.oplus.gallery.business_lib.timeline.TimelineConfig.DAY_MORE_COL
import com.oplus.gallery.business_lib.timeline.TimelineConfig.MONTH
import com.oplus.gallery.business_lib.timeline.TimelineConfig.YEAR
import com.oplus.gallery.business_lib.timeline.animation.BaseSwitchAnimation
import com.oplus.gallery.business_lib.timeline.animation.ISwitchAnimation
import com.oplus.gallery.business_lib.timeline.diff.DecorationAdapter
import com.oplus.gallery.business_lib.timeline.diff.DecorationHolder
import com.oplus.gallery.business_lib.timeline.diff.ElementAdapter
import com.oplus.gallery.business_lib.timeline.diff.ItemAdapter
import com.oplus.gallery.business_lib.timeline.layout.BaseLayouter
import com.oplus.gallery.business_lib.timeline.layout.BaseNodeDecoration
import com.oplus.gallery.business_lib.timeline.layout.LayoutConfig
import com.oplus.gallery.business_lib.timeline.presentation.BaseSlotPresentation
import com.oplus.gallery.business_lib.timeline.presentation.Presentation
import com.oplus.gallery.business_lib.timeline.view.VirtualItem
import com.oplus.gallery.business_lib.timeline.view.VirtualViewIndexSpec
import com.oplus.gallery.business_lib.util.TimelineUtils.INVALID_INDEX
import com.oplus.gallery.business_lib.util.TimelineUtils.TIMELINE_TAG
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.display.ScreenUtils.toPx
import com.oplus.gallery.standard_lib.baselist.view.isSelectMode
import com.oplus.gallery.timelinepage.TimelineTabConfig.PICKED_MONTH
import com.oplus.gallery.timelinepage.TimelineTabConfig.PICKED_YEAR
import com.oplus.gallery.timelinepage.animation.DecorationFadeAnimation
import com.oplus.gallery.timelinepage.animation.PickedAndMyPhotoSwitchAnimation
import com.oplus.gallery.timelinepage.animation.PickedYMAndPickedDaySwitchAnimation
import com.oplus.gallery.timelinepage.animation.SlotFadeAnimation
import com.oplus.gallery.timelinepage.drawer.PickedDayDecorationDrawer
import com.oplus.gallery.timelinepage.drawer.PickedDaySlotDrawer
import com.oplus.gallery.timelinepage.layouter.PickedDayLayouter
import com.oplus.gallery.timelinepage.layouter.PickedDayNodeDecoration
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@SuppressLint("UseCompatLoadingForDrawables")
class PickedDayPresentation(
    layoutConfig: LayoutConfig,
    coroutineScope: CoroutineScope,
) : BaseSlotPresentation<PickedDayLayouter, PickedDaySlotDrawer>(layoutConfig, coroutineScope) {

    companion object {

        /**
         * 时间标题离顶栏小于这个距离时，就会开始 alpha 淡出
         */
        private const val TITLE_ALPHA_FADE_START_DISTANCE_DP = 50
        private const val FLOAT_ONE_PERCENT = 0.01f
    }

    override val tag = "${TIMELINE_TAG}PickedDayPresentation#${layoutConfig.type}"
    private val titleDisplayMode: TitleDisplayMode
        get() = if (isSelectMode(selectModeSpec)) TitleDisplayMode.NORMAL_MODE else TitleDisplayMode.HOVER_MODE

    private val minSpaceFromNextTitle by lazy {
        timelineView.context.resources.getDimensionPixelSize(com.oplus.gallery.basebiz.R.dimen.base_picked_day_min_space_from_next_title)
    }
    private val maxSpaceFromNextTitle by lazy {
        timelineView.context.resources.getDimensionPixelSize(com.oplus.gallery.basebiz.R.dimen.base_picked_day_max_space_from_next_title)
    }

    private val decorationFadeAnimation: DecorationFadeAnimation by lazy { DecorationFadeAnimation(timelineView) }

    private lateinit var decorationDrawer: PickedDayDecorationDrawer

    override var revertLayoutDirection: Boolean = false
        set(value) {
            field = false
            GLog.w(tag, "setRevertLayoutDirection. PickedDayPresentation don't support revertLayoutDirection, skip.")
        }

    override val needInterceptZoomEvent = true

    private val emptyRect: Rect = Rect()

    override fun onInit() {
        super.onInit()
        accessibilityExplorer = PickedDayAccessibilityTouchHelper()
        decorationDrawer = PickedDayDecorationDrawer(timelineView.context, layoutConfig).apply {
            stateChangeObservers.add(this)
            coroutineScope.launch(Dispatchers.IO) {
                curPermanent = getPermanent(timelineView.context)
            }
        }
    }

    override fun initAdapter() {
        itemAdapter = ItemAdapter(timelineView, revertLayoutDirection, layouter) { timelineInfo }
        decorationAdapter = DecorationAdapter(timelineView, revertLayoutDirection, layouter) { timelineInfo }
        itemAdapter.offsetCallback = fun(offset: Int) {
            // 正序时可见区域不包含第一个item，则需要矫正位置, 倒序时可见区域不包含最后一个item，则需要矫正位置
            val needOffsetHolders = (isPositiveOrder() && !layouter.visibleItemRange().contains(layouter.allItemSize() - 1)) ||
                (!isPositiveOrder() && !layouter.visibleItemRange().contains(0))
            if (needOffsetHolders) {
                itemAdapter.ignoreUpdateHolders = true
                decorationAdapter.ignoreUpdateHolders = true
                val consume = layouter.scrollBy(offset)
                itemAdapter.offsetHolders(consume)
                decorationAdapter.offsetHolders(consume)
                itemAdapter.ignoreUpdateHolders = false
                decorationAdapter.ignoreUpdateHolders = false
            }
        }

        itemAdapter.animationListener = object : ElementAdapter.OnAnimationListener {
            override fun onStart() {
                elementAnimationListener?.onStart()
            }

            override fun onFinish() {
                elementAnimationListener?.onFinish()
            }
        }
    }

    override fun onCreateSlotDrawer(): PickedDaySlotDrawer {
        return PickedDaySlotDrawer(timelineView.context).apply { stateChangeObservers.add(this) }
    }

    override fun onCreateLayouter(): PickedDayLayouter {
        return PickedDayLayouter(layoutConfig, null, coroutineScope)
    }

    override fun updateLayoutConfig(config: LayoutConfig) {
        super.updateLayoutConfig(config)
        // 更新LayoutConfig后 重置typesettingConfig，使之重新初始化
        layouter.typesettingConfig = null
    }

    override fun onSwitchAnimationCreate(target: Presentation, fromIndex: Int, toIndex: Int): ISwitchAnimation {
        return if ((fromIndex == INVALID_INDEX) || (toIndex == INVALID_INDEX)) {
            SlotFadeAnimation(layouter, target.layouter)
        } else if (listOf(PICKED_YEAR, PICKED_MONTH).contains(target.type)) {
            PickedYMAndPickedDaySwitchAnimation(this, target, isInTopLayer = true)
        } else if (listOf(YEAR, MONTH, DAY_MORE_COL, DAY, DAY_LESS_COL).contains(target.type)) {
            PickedAndMyPhotoSwitchAnimation(this, target, isInTopLayer = true)
        } else {
            SlotFadeAnimation(layouter, target.layouter)
        }
    }

    override fun onForeground() {
        super.onForeground()
        refreshTimeTitleIfNeed()
    }

    override fun onDraw(canvas: Canvas) {
        onDrawSlot(canvas)

        if (titleDisplayMode == TitleDisplayMode.HOVER_MODE) {
            drawRemain(canvas)
            drawDecoration(canvas)
        } else {
            drawDecoration(canvas)
            drawRemain(canvas)
        }
    }

    override fun onDrawWhenSwitching(canvas: Canvas) {
        val animation = switchAnimation as BaseSwitchAnimation<*, *>

        // 绘制Slot缩图
        drawSlotAnimation(canvas)

        // 绘制节点装饰
        animation.nodeAnimators().forEach { (nodeIndex, animator) ->
            timelineInfo.getTimeNode(nodeIndex)?.let {
                (animator.nodeDecoration as? PickedDayNodeDecoration)?.apply {
                    titleOffsetY = calculateTitleOffset(animator.currentRect.top.toInt(), this)
                    titleAlpha = calculateTitleAlpha(animator.currentRect.top.toInt(), this)
                    backgroundAlpha = if (animation.isFrom) 0f else animator.alpha
                }
                decorationDrawer.draw(canvas, it, animator)
                decorationDrawer.drawRemain(canvas, it, animator)
            }
        }
    }

    private fun drawDecoration(canvas: Canvas) {
        decorationAdapter.holders().forEach { holder ->
            if (decorationAdapter.skipDraw(holder)) {
                return@forEach
            }
            holder.data?.let { (timeNode, nodeDecoration) ->
                if (nodeDecoration !is PickedDayNodeDecoration) return@forEach
                nodeDecoration.nodeRect.let { nodeRect ->
                    nodeRect.set(holder.rect)
                    nodeRect.offset(0, -layouter.scrollPosition())
                }
                val nodeIndex = nodeDecoration.index
                if (decorationFadeAnimation.isAnimating(nodeIndex)) {
                    decorationFadeAnimation.fadeOutAnimators[nodeIndex]?.let { (titleOffsetY, animator) ->
                        nodeDecoration.titleOffsetY = titleOffsetY
                        nodeDecoration.titleAlpha = animator.animatedValue as Float
                        decorationDrawer.draw(canvas, timeNode, nodeDecoration, holder.alpha)
                    }
                    decorationFadeAnimation.fadeInAnimators[nodeIndex]?.let { (titleOffsetY, animator) ->
                        nodeDecoration.titleOffsetY = titleOffsetY
                        nodeDecoration.titleAlpha = animator.animatedValue as Float
                        decorationDrawer.draw(canvas, timeNode, nodeDecoration, holder.alpha)
                    }
                } else {
                    if (titleDisplayMode == TitleDisplayMode.HOVER_MODE) {
                        nodeDecoration.titleOffsetY = calculateTitleOffset(nodeDecoration.nodeRect.top, nodeDecoration)
                        // 需要更新nextNode的titleOffsetY，否则calculateTitleAlpha时使用的nextNode因还未更新titleOffset，导致计算alpha是错误的
                        val nextNodeHolder = findNodeHolder(nodeDecoration.index + 1)
                        updateNodeTitleOffset(nextNodeHolder)
                        /*
                         动画时,holder的node和layouter.decorationOfTimeNode()获取的node不是同一个对象，
                         因为动画前，一次layout后layouter的node更新后了,而且更新后titleContentRect是空的，导致calculateTitleAlpha计算出错
                         所以需要传入holder持有的node.titleContentRect
                         */
                        nodeDecoration.titleAlpha = calculateTitleAlpha(nodeDecoration.nodeRect.top, nodeDecoration)
                    } else {
                        nodeDecoration.titleOffsetY = 0
                    }
                    if (isAnimating()) {
                        nodeDecoration.isDirty = true
                    }
                    decorationDrawer.draw(canvas, timeNode, nodeDecoration, holder.alpha)
                }
            }
        }
    }

    private fun updateNodeTitleOffset(nodeHolder: DecorationHolder?) {
        nodeHolder ?: return
        (nodeHolder.data?.second as? PickedDayNodeDecoration)?.let { nodeDecoration ->
            tmpRect.set(nodeHolder.rect)
            tmpRect.offset(0, -layouter.scrollPosition())
            nodeDecoration.titleOffsetY = calculateTitleOffset(tmpRect.top, nodeDecoration)
        }
    }

    private fun findNodeHolder(nodeIndex: Int): DecorationHolder? {
        return decorationAdapter.holders().firstOrNull { it.startIndex == nodeIndex }
    }

    private fun drawRemain(canvas: Canvas) {
        decorationAdapter.holders().forEach { holder ->
            if (decorationAdapter.skipDraw(holder)) return@forEach
            holder.data?.let { (timeNode, nodeDecoration) ->
                if (nodeDecoration !is PickedDayNodeDecoration) return@forEach
                nodeDecoration.nodeRect.let { nodeRect ->
                    nodeRect.set(holder.rect)
                    nodeRect.offset(0, -layouter.scrollPosition())
                }
                decorationDrawer.measureIfNeed(timeNode, nodeDecoration)
                decorationDrawer.drawRemain(canvas, timeNode, nodeDecoration, holder.alpha)
            }
        }
    }

    override fun onSelectionModeChange() {
        startDecorationFadeAnimation(isSelectMode(selectModeSpec))
    }

    private fun startDecorationFadeAnimation(isSelectionMode: Boolean) {
        val itemIndexOfHoverPosition = layouter.findVisibleItemUnder(layouter.slotWidth() / 2, toolbarRect.centerY()).second
        val nodeIndex = layouter.nodeIndexOfItem(itemIndexOfHoverPosition)
        layouter.decorationOfTimeNode<PickedDayNodeDecoration>(nodeIndex)?.let { decoration ->
            layouter.rectOfTimeNode(nodeIndex, tmpRect, isRelative = true)
            decoration.titleOffsetY = calculateTitleOffset(tmpRect.top, decoration)
            val titleAlpha = calculateTitleAlpha(tmpRect.top, decoration)
            decorationFadeAnimation.reset()
            if (isSelectionMode) {
                // 进入编辑模式，普通日期淡入，悬停日期淡出
                decorationFadeAnimation.prepareDecorationExit(nodeIndex, decoration.titleOffsetY, titleAlpha)
                decorationFadeAnimation.prepareDecorationEnter(nodeIndex, 0, 1f)
            } else {
                // 退出编辑模式，悬停日期淡入，普通日期淡出
                decorationFadeAnimation.prepareDecorationExit(nodeIndex, 0, 1f)
                decorationFadeAnimation.prepareDecorationEnter(nodeIndex, decoration.titleOffsetY, titleAlpha)
            }
            decorationFadeAnimation.start()
        }
    }

    private fun isPositiveOrder() = ApiDmManager.getSettingDM().isPositiveOrder()

    override fun supportDiff(): Boolean = true
    override fun supportSelection(): Boolean = true

    /**
     * 根据日期标题到屏幕顶部距离与toolbar到屏幕顶部的距离差，来计算标题偏移量
     * 即计算toolbar与日期标题直接的距离
     */
    private fun calculateTitleOffset(nodeRectTop: Int, nodeDecoration: PickedDayNodeDecoration): Int {
        val titlePosition = nodeRectTop - timelineView.scrollY + nodeDecoration.titleContentRect.centerY()
        // 侧边栏收起时,侧边栏icon和悬浮标题会重叠，所以需要设置两者之间的间距
        val titleOffsetMarginTop = nodeDecoration.config.extraInfo.getInt(LayoutConfig.EXTRA_FLOATING_TITLE_OFFSET)
        return (toolbarRect.bottom + titleOffsetMarginTop - titlePosition).coerceAtLeast(0)
    }

    /**
     * 根据当前日期标题于顶栏的距离来计算透明度，靠近顶栏时，标题会渐变消失
     */
    private fun calculateTitleAlpha(
        nodeRectTop: Int,
        nodeDecoration: PickedDayNodeDecoration,
    ): Float {
        val titlePosition = nodeRectTop - timelineView.scrollY + nodeDecoration.titleContentRect.centerY()
        val distanceToToolbarBottom = titlePosition - toolbarRect.bottom
        val alpha = distanceToToolbarBottom.coerceAtMost(TITLE_ALPHA_FADE_START_DISTANCE_DP.toPx) * FLOAT_ONE_PERCENT
        return alpha.coerceAtLeast(0f).coerceAtMost(1f)
    }

    /**
     *
     * 根据当前日期标题与下一个日期的标题之间的距离来计算标题透明度
     * @param nodeDecoration 当前时间节点
     * @param nextDecoration 下个时间节点
     * @param currNodeRect 当前时间节点的绝对坐标
     * @param nextNodeRect 下个时间节点的绝对坐标
     */
    private fun calculateTitleAlpha(
        nodeDecoration: PickedDayNodeDecoration,
        nextDecoration: PickedDayNodeDecoration? = null,
        currNodeRect: Rect = Rect(),
        nextNodeRect: Rect = Rect(),
    ): Float {
        val tmpNextDecoration = nextDecoration ?: layouter.decorationOfTimeNode(nodeDecoration.index + 1)
        tmpNextDecoration ?: return 1f
        tmpRect.set(currNodeRect)
        if (tmpRect.isEmpty) {
            layouter.rectOfTimeNode(nodeDecoration.index, tmpRect)
        }
        val currentTitlePosition = tmpRect.top + nodeDecoration.titleContentRect.bottom + nodeDecoration.titleOffsetY

        tmpRect.set(nextNodeRect)
        if (tmpRect.isEmpty) {
            layouter.rectOfTimeNode(nodeDecoration.index + 1, tmpRect)
        }
        val nextTitlePosition = tmpRect.top + tmpNextDecoration.titleContentRect.top + tmpNextDecoration.titleOffsetY

        val currentTitleAlpha = (nextTitlePosition - currentTitlePosition - minSpaceFromNextTitle) /
            (maxSpaceFromNextTitle - minSpaceFromNextTitle).toFloat()
        return currentTitleAlpha.coerceAtMost(1f).coerceAtLeast(0f)
    }

    private fun getPermanent(context: Context): UserProfileSettings.Permanent? {
        if (PermissionHelper.isPermissionUnavailable()) {
            GLog.d(tag, "getPermanent, permission unavailable.")
            return null
        }
        val permanent = UserProfileSettings.getPermanent(context)
        if ((permanent == null) || permanent.isEmpty) {
            GLog.e(tag, "getPermanent, There is no permanent place.")
        }
        return permanent
    }

    enum class TitleDisplayMode {
        // 悬停模式，退出选择模式时处于该模式，该模式下日期滑动到悬停位置后继续上滑会停留在悬停位置
        HOVER_MODE,

        // 普通模式，进入选择模式时处于该模式，该模式下日期会跟随图片移动，不会停留在悬停位置
        NORMAL_MODE
    }

    inner class PickedDayAccessibilityTouchHelper : SlotAccessibilityTouchHelper() {
        override fun generateVirtualViewId(nodeIndex: Int, itemIndex: Int, elementType: String): Int {
            return when (elementType) {
                BaseLayouter.ELEMENT_TYPE_SUB_TITLE -> VirtualViewIndexSpec.makeIndexSpec(nodeIndex, elementType)
                else -> super.generateVirtualViewId(nodeIndex, itemIndex, elementType)
            }
        }

        override fun fillProperties(item: VirtualItem) = with(item) {
            when (elementType) {
                BaseLayouter.ELEMENT_TYPE_TITLE -> {
                    timelineInfo.getTimeNode(nodeIndex)?.extraInfo?.getString(TimeNode.EXTRA_KEY_TITLE)?.let {
                        description = it
                    }
                    layouter.decorationOfTimeNode<BaseNodeDecoration>(nodeIndex)?.let {
                        rect.set(it.titleContentRect)
                        layouter.rectOfTimeNode(nodeIndex, tmpRect)
                        rect.offset(0, tmpRect.top)
                    }
                    offsetAndAddAction(false)
                }
                BaseLayouter.ELEMENT_TYPE_SUB_TITLE -> {
                    timelineInfo.getTimeNode(nodeIndex)?.extraInfo?.getString(TimeNode.EXTRA_KEY_SUB_TITLE)?.let {
                        description = it
                    }
                    layouter.decorationOfTimeNode<BaseNodeDecoration>(nodeIndex)?.let {
                        rect.set(it.subTitleContentRect)
                        layouter.rectOfTimeNode(nodeIndex, tmpRect)
                        rect.offset(0, tmpRect.top)
                    }
                    offsetAndAddAction()
                }
                BaseLayouter.ELEMENT_TYPE_REMAIN_BUTTON -> {
                    timelineInfo.getTimeNode(nodeIndex)?.takeIf { it.isNeedRemainButton }?.let {
                        description = timelineView.context.getString(com.oplus.gallery.basebiz.R.string.base_art_show_remain_talkback)
                    }
                    layouter.decorationOfTimeNode<PickedDayNodeDecoration>(nodeIndex)?.let {
                        layouter.rectOfTimeNode(nodeIndex, tmpRect)
                        rect.set(it.getCurrentRemainRect(tmpRect))
                        rect.offset(0, tmpRect.top)
                    }
                    offsetAndAddAction()
                }
                else -> super.fillProperties(this)
            }
        }

        override fun getVisibleVirtualItems(): List<VirtualItem> {
            // 如果TimelineView倒序排列，需要将visibleNodeRange倒序遍历
            val visibleNodeRange = if (revertLayoutDirection) {
                layouter.visibleNodeRange().reversed()
            } else {
                layouter.visibleNodeRange()
            }
            val virtualItems = mutableListOf<VirtualItem>()
            visibleNodeRange.forEach { nodeIndex ->
                // 按nodeIndex顺序，将时间标题和地点信息添加到子节点中
                generateVirtualItemIfNeed(nodeIndex, INVALID_INDEX, BaseLayouter.ELEMENT_TYPE_TITLE)?.apply {
                    virtualItems.add(this)
                }
                generateVirtualItemIfNeed(nodeIndex, INVALID_INDEX, BaseLayouter.ELEMENT_TYPE_SUB_TITLE)?.apply {
                    virtualItems.add(this)
                }
                // 如果TimelineView倒序排列，需要将itemRangeOfNode倒序遍历
                val itemRangeOfNode = if (revertLayoutDirection) {
                    layouter.itemRangeOfNode(nodeIndex).reversed()
                } else {
                    layouter.itemRangeOfNode(nodeIndex)
                }
                // 将此块中所有缩图添加到子节点中
                itemRangeOfNode.forEach flag@{ itemIndex ->
                    if (itemIndex !in layouter.visibleItemRange()) return@flag
                    generateVirtualItemIfNeed(nodeIndex, itemIndex, BaseLayouter.ELEMENT_TYPE_SLOT_ITEM)?.apply {
                        virtualItems.add(this)
                    }
                }
                // 按需要，将“当天其他照片”按钮添加到子节点中
                val hasRemainButton = timelineInfo.getTimeNode(nodeIndex)?.isNeedRemainButton ?: false
                if (hasRemainButton) {
                    generateVirtualItemIfNeed(nodeIndex, INVALID_INDEX, BaseLayouter.ELEMENT_TYPE_REMAIN_BUTTON)?.apply {
                        virtualItems.add(this)
                    }
                }
            }
            return virtualItems
        }
    }
}