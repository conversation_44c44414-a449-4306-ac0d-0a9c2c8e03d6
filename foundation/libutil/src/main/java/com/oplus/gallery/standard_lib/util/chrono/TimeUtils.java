/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - TimeUtils.java
 ** Description:
 ** Version: 1.0
 ** Date: 2019/06/29
 ** Author: Dingyong@Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 ** Dingyong@Apps.Gallery3D      2019/06/29    1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/

package com.oplus.gallery.standard_lib.util.chrono;

import android.content.Context;
import android.icu.text.DateFormat;
import android.icu.text.SimpleDateFormat;
import android.icu.util.Calendar;
import android.icu.util.TimeZone;
import android.text.TextUtils;
import android.text.format.DateUtils;

import androidx.annotation.NonNull;

import com.oplus.gallery.addon.utils.OSVersionUtils;
import com.oplus.gallery.foundation.util.R;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.foundation.util.systemcore.LocaleUtils;
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils;
import com.oplus.gallery.foundation.util.text.TextUtil;
import com.oplus.gallery.standard_lib.app.AppConstants;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;

import org.jetbrains.annotations.Nullable;

import java.text.ParseException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Date;
import java.util.Locale;
import java.util.Random;

public class TimeUtils {
    public static final String FORMAT_YYYY = "yyyy";
    public static final String FORMAT_MM = "MM";
    public static final String FORMAT_DD = "dd";
    public static final String FORMAT_HH = "HH";
    public static final String FORMAT_YYYY_MM = "yyyy-MM";
    public static final String FORMAT_YYYYMM = "yyyyMM";
    public static final String FORMAT_YYYY_MM_DD = "yyyy-MM-dd";
    public static final String FORMAT_YYYYMMDD = "yyyyMMdd";
    public static final String TIME_FORMAT = "yyyyMMddHHmmss";
    public static final String FORMAT_YYYYMMDDHHMMSSSS = "yyyyMMddHHmmssSSS";
    public static final String DATE_FORMAT = "yyyy/MM/dd";
    public static final String DATE_FORMAT_YYYY_MM_DD_HH_MM = "yyyy/MM/dd HH:mm";
    public static final String DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_SLASH = "yyyy/MM/dd HH:mm:ss";
    public static final String DATE_FORMAT_YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH-mm-ss";
    public static final String FORMAT_YYYY_MM_DD_HH_MM_SS_COLON_SPLIT = "yyyy:MM:dd HH:mm:ss";
    public static final String FORMAT_YYYY_MM_DD_HH_MM_SS_UNDERLINE_SPLIT = "yyyyMMdd_HHmmss";
    public static final String FORMAT_YYYY_MM_DD_COLON_SPLIT_HH_MM_POINT_SPLIT = "yyyy.MM.dd HH:mm";
    public static final String FORMAT_YYYY_MM_DD_COLON_SPLIT = "yyyy:MM:dd";
    public static final String FORMAT_HH_MM_SS_COLON_SPLIT = "HH:mm:ss";
    public static final String FORMAT_LOCALIZATION_HOUR24_MINUTE_SECOND = "Hms";
    public static final String FORMAT_LOCALIZATION_HOUR_MINUTE_SECOND = "hms";
    public static final String FORMAT_LOCALIZATION_HOUR24_MINUTE = "Hm";
    public static final String FORMAT_LOCALIZATION_HOUR_MINUTE = "hm";
    public static final String FORMAT_YEAR_CH = "yyyy 年";
    public static final String FORMAT_MONTH_CH = "M 月";
    public static final int MILLISECOND_IN_SECOND = 1000;
    public static final int TIME_CONVERSION = 1000;
    public static final String STRFTIME_SEARCH_M = "%m";
    public static final String STRFTIME_SEARCH_Y = "%Y";
    public static final String STRFTIME_SEARCH_Y_M = "%Y-%m";
    public static final String STRFTIME_SEARCH_H = "%H";
    public static final int SECOND_IN_MINUTE       = 60;
    public static final int TIME_1_MS_IN_US        = 1000;
    public static final int TIME_100_MS_IN_MS      = 100;
    public static final int TIME_500_MS_IN_MS      = 500;
    public static final int TIME_1_SEC_IN_MS       = 1000;
    public static final int TIME_2_SEC_IN_MS       = 1000 * 2;
    public static final int TIME_3_SEC_IN_MS       = 1000 * 3;
    public static final int TIME_4_SEC_IN_MS       = 1000 * 4;
    public static final int TIME_5_SEC_IN_MS       = 1000 * 5;
    public static final int TIME_6_SEC_IN_MS       = 1000 * 6;
    public static final int TIME_10_SEC_IN_MS      = 1000 * 10;
    public static final int TIME_14_SEC_IN_MS      = 1000 * 14;
    public static final int TIME_15_SEC_IN_MS      = 1000 * 15;
    public static final int TIME_16_SEC_IN_MS      = 1000 * 16;
    public static final int TIME_20_SEC_IN_MS      = 1000 * 20;
    public static final int TIME_30_SEC_IN_MS      = 1000 * 30;
    public static final int TIME_31_SEC_IN_MS      = 1000 * 31;
    public static final int TIME_1_MIN_IN_MS       = 1000 * 60;
    public static final int TIME_2_MIN_IN_MS       = 1000 * 60 * 2;
    public static final int TIME_3_MIN_IN_MS       = 1000 * 60 * 3;
    public static final int TIME_5_MIN_IN_MS       = 1000 * 60 * 5;
    public static final int TIME_10_MIN_IN_MS      = 1000 * 60 * 10;
    public static final int TIME_20_MIN_IN_MS      = 1000 * 60 * 20;
    public static final int TIME_30_MIN_IN_MS      = 1000 * 60 * 30;
    public static final int TIME_1_HOUR_IN_MS      = 1000 * 60 * 60;
    public static final long TIME_2_HOUR_IN_MS     = 1000 * 60 * 60 * 2L;
    public static final long TIME_4_HOUR_IN_MS     = 1000 * 60 * 60 * 4L;
    public static final long TIME_6_HOUR_IN_MS     = 1000 * 60 * 60 * 6L;
    public static final long TIME_8_HOUR_IN_MS     = 1000 * 60 * 60 * 8L;
    public static final long TIME_12_HOUR_IN_MS    = 1000 * 60 * 60 * 12L;
    public static final long TIME_1_DAY_IN_MS      = 1000 * 60 * 60 * 24L;
    public static final long TIME_2_DAY_IN_MS      = 1000 * 60 * 60 * 24 * 2L;
    public static final long TIME_3_DAY_IN_MS      = 1000 * 60 * 60 * 24 * 3L;
    public static final long TIME_1_WEEK_IN_MS     = 1000 * 60 * 60 * 24 * 7L;
    public static final long TIME_2_WEEK_IN_MS     = 1000 * 60 * 60 * 24 * 14L;
    public static final long TIME_1_MONTH_IN_MS    = 1000 * 60 * 60 * 24 * 30L;
    public static final long TIME_2_MONTH_IN_MS    = 1000 * 60 * 60 * 24 * 60L;
    public static final long TIME_HALF_YEAR_IN_MS  = 1000 * 60 * 60 * 24 * 182L;
    public static final long TIME_1_YEAR_IN_MS     = 1000 * 60 * 60 * 24 * 365L;
    public static final int DAY_THIRTY = 30;
    /**
     * 当前正常情况下的时间戳上限(2050年)，毫秒级
     */
    public static final long MAX_TIME_UNIT_MILLISECOND = 80 * TIME_1_YEAR_IN_MS;
    /** 当前正常情况下的时间戳上限(2050年)，秒级 */
    public static final long MAX_TIME_UNIT_SECOND = MAX_TIME_UNIT_MILLISECOND / TIME_1_SEC_IN_MS;
    /** 1天 **/
    public static final int ONE_DAY = 1;
    public static final long INVALID_TIME = -1L;
    private static final String TAG = "TimeUtils";
    private static final ThreadLocal<DateFormat> FORMAT_YYYY_MM_DD_THREAD_LOCAL;
    private static final ThreadLocal<DateFormat> FORMAT_YYYYMMDD_THREAD_LOCAL;
    private static final ThreadLocal<DateFormat> FORMAT_YYYY_MM_THREAD_LOCAL;

    private static final ThreadLocal<DateFormat> FORMAT_YYYYMM_THREAD_LOCAL;
    private static final ThreadLocal<DateFormat> FORMAT_YYYY_THREAD_LOCAL;
    private static final ThreadLocal<DateFormat> FORMAT_MM_THREAD_LOCAL;
    private static final ThreadLocal<DateFormat> FORMAT_DD_THREAD_LOCAL;
    private static final ThreadLocal<DateFormat> FORMAT_HH_THREAD_LOCAL;

    private static final ThreadLocal<DateFormat> FORMAT_YYYY_MM_DD_THREAD_LOCAL_ENGLISH;
    private static final ThreadLocal<DateFormat> FORMAT_YYYYMMDD_THREAD_LOCAL_ENGLISH;
    private static final ThreadLocal<DateFormat> FORMAT_YYYY_MM_THREAD_LOCAL_ENGLISH;

    private static final ThreadLocal<DateFormat> FORMAT_YYYYMM_THREAD_LOCAL_ENGLISH;
    private static final ThreadLocal<DateFormat> FORMAT_YYYY_THREAD_LOCAL_ENGLISH;
    private static final ThreadLocal<DateFormat> FORMAT_MM_THREAD_LOCAL_ENGLISH;
    private static final ThreadLocal<DateFormat> FORMAT_DD_THREAD_LOCAL_ENGLISH;
    private static final ThreadLocal<DateFormat> FORMAT_HH_THREAD_LOCAL_ENGLISH;

    private static final ThreadLocal<DateFormat> FORMAT_YYYY_MM_DD_HH_MM_SS_COLON_THREAD_LOCAL;

    private static final ThreadLocal<DateFormat> FORMAT_YYYY_MM_DD_HH_MM_SS_UNDERLINE_THREAD_LOCAL;
    private static final ThreadLocal<DateFormat> FORMAT_YYYY_MM_DD_HH_MM_COLON_THREAD_LOCAL;
    private static final ThreadLocal<DateFormat> FORMAT_YYYY_MM_DD_COLON_THREAD_LOCAL;
    private static final ThreadLocal<DateFormat> FORMAT_HH_MM_SS_COLON_THREAD_LOCAL;
    private static final ThreadLocal<DateFormat> FORMAT_FOR_OFFSET_TIME;

    private static final ThreadLocal<DateFormat> FORMAT_YYYY_MM_DD_HH_MM_THREAD_LOCAL;
    private static final ThreadLocal<DateFormat> FORMAT_YYYY_MM_DD_HH_MM_SS;
    private static final ThreadLocal<DateFormat> FORMAT_YYYYMMDDHHMMSSSSS_THREAD_LOCAL;

    private static final Random RANDOM = new Random();

    private static final int SECOND_IN_HOUR = 60 * 60;

    private static final int HOUR_OF_DAY_MAX = 23;

    private static final int MINUTE_MAX = 59;

    private static final int SECOND_MAX = 59;

    private static final int MILLISECOND_MAX = 999;

    private static final String WEEK_DAY_MONTH_DAY = "EEEMMMd";
    private static final String UTC = "UTC";
    private static final String CHINESE_LANGUAGE = "zh";

    static {
        FORMAT_YYYY_MM_DD_THREAD_LOCAL = new ThreadLocal<DateFormat>() {
            @Override
            protected DateFormat initialValue() {
                return new SimpleDateFormat(FORMAT_YYYY_MM_DD, Locale.getDefault());
            }
        };
        FORMAT_YYYYMMDD_THREAD_LOCAL = new ThreadLocal<DateFormat>() {
            @Override
            protected DateFormat initialValue() {
                return new SimpleDateFormat(FORMAT_YYYYMMDD, Locale.getDefault());
            }
        };
        FORMAT_YYYY_MM_THREAD_LOCAL = new ThreadLocal<DateFormat>() {
            @Override
            protected DateFormat initialValue() {
                return new SimpleDateFormat(FORMAT_YYYY_MM, Locale.getDefault());
            }
        };
        FORMAT_YYYYMM_THREAD_LOCAL = new ThreadLocal<DateFormat>() {
            @Override
            protected DateFormat initialValue() {
                return new SimpleDateFormat(FORMAT_YYYYMM, Locale.getDefault());
            }
        };
        FORMAT_YYYY_THREAD_LOCAL = new ThreadLocal<DateFormat>() {
            @Override
            protected DateFormat initialValue() {
                return new SimpleDateFormat(FORMAT_YYYY, Locale.getDefault());
            }
        };
        FORMAT_MM_THREAD_LOCAL = new ThreadLocal<DateFormat>() {
            @Override
            protected DateFormat initialValue() {
                return new SimpleDateFormat(FORMAT_MM, Locale.getDefault());
            }
        };
        FORMAT_DD_THREAD_LOCAL = new ThreadLocal<DateFormat>() {
            @Override
            protected DateFormat initialValue() {
                return new SimpleDateFormat(FORMAT_DD, Locale.getDefault());
            }
        };
        FORMAT_HH_THREAD_LOCAL = ThreadLocal.withInitial(() -> new SimpleDateFormat(FORMAT_HH, Locale.getDefault()));

        FORMAT_YYYY_MM_DD_THREAD_LOCAL_ENGLISH = new ThreadLocal<DateFormat>() {
            @Override
            protected DateFormat initialValue() {
                return new SimpleDateFormat(FORMAT_YYYY_MM_DD, Locale.ENGLISH);
            }
        };
        FORMAT_YYYYMMDD_THREAD_LOCAL_ENGLISH = new ThreadLocal<DateFormat>() {
            @Override
            protected DateFormat initialValue() {
                return new SimpleDateFormat(FORMAT_YYYYMMDD, Locale.ENGLISH);
            }
        };
        FORMAT_YYYYMM_THREAD_LOCAL_ENGLISH = new ThreadLocal<DateFormat>() {
            @Override
            protected DateFormat initialValue() {
                return new SimpleDateFormat(FORMAT_YYYYMM, Locale.ENGLISH);
            }
        };
        FORMAT_YYYY_MM_THREAD_LOCAL_ENGLISH = new ThreadLocal<DateFormat>() {
            @Override
            protected DateFormat initialValue() {
                return new SimpleDateFormat(FORMAT_YYYY_MM, Locale.ENGLISH);
            }
        };
        FORMAT_YYYY_THREAD_LOCAL_ENGLISH = new ThreadLocal<DateFormat>() {
            @Override
            protected DateFormat initialValue() {
                return new SimpleDateFormat(FORMAT_YYYY, Locale.ENGLISH);
            }
        };
        FORMAT_MM_THREAD_LOCAL_ENGLISH = new ThreadLocal<DateFormat>() {
            @Override
            protected DateFormat initialValue() {
                return new SimpleDateFormat(FORMAT_MM, Locale.ENGLISH);
            }
        };
        FORMAT_DD_THREAD_LOCAL_ENGLISH = new ThreadLocal<DateFormat>() {
            @Override
            protected DateFormat initialValue() {
                return new SimpleDateFormat(FORMAT_DD, Locale.ENGLISH);
            }
        };
        FORMAT_HH_THREAD_LOCAL_ENGLISH = ThreadLocal.withInitial(() -> new SimpleDateFormat(FORMAT_HH, Locale.ENGLISH));

        FORMAT_YYYY_MM_DD_HH_MM_SS_COLON_THREAD_LOCAL = new ThreadLocal<DateFormat>() {
            @Override
            protected DateFormat initialValue() {
                return new SimpleDateFormat(FORMAT_YYYY_MM_DD_HH_MM_SS_COLON_SPLIT);
            }
        };
        FORMAT_YYYY_MM_DD_HH_MM_SS_UNDERLINE_THREAD_LOCAL = new ThreadLocal<DateFormat>() {
            @Override
            protected DateFormat initialValue() {
                return new SimpleDateFormat(FORMAT_YYYY_MM_DD_HH_MM_SS_UNDERLINE_SPLIT);
            }
        };

        FORMAT_YYYY_MM_DD_HH_MM_COLON_THREAD_LOCAL = new ThreadLocal<DateFormat>() {
            @Override
            protected DateFormat initialValue() {
                return new SimpleDateFormat(FORMAT_YYYY_MM_DD_COLON_SPLIT_HH_MM_POINT_SPLIT);
            }
        };
        FORMAT_YYYY_MM_DD_COLON_THREAD_LOCAL = new ThreadLocal<DateFormat>() {
            @Override
            protected DateFormat initialValue() {
                SimpleDateFormat formatter = new SimpleDateFormat(FORMAT_YYYY_MM_DD_COLON_SPLIT);
                formatter.setTimeZone(TimeZone.getTimeZone(UTC));
                return formatter;
            }
        };
        FORMAT_HH_MM_SS_COLON_THREAD_LOCAL = new ThreadLocal<DateFormat>() {
            @Override
            protected DateFormat initialValue() {
                SimpleDateFormat formatter = new SimpleDateFormat(FORMAT_HH_MM_SS_COLON_SPLIT);
                formatter.setTimeZone(TimeZone.getTimeZone(UTC));
                return formatter;
            }
        };

        FORMAT_YYYY_MM_DD_HH_MM_THREAD_LOCAL = new ThreadLocal<DateFormat>() {
            @Override
            protected DateFormat initialValue() {
                return new SimpleDateFormat(DATE_FORMAT_YYYY_MM_DD_HH_MM);
            }
        };
        FORMAT_FOR_OFFSET_TIME = new ThreadLocal<DateFormat>() {
            @Override
            protected DateFormat initialValue() {
                return new SimpleDateFormat("XXX");
            }
        };
        FORMAT_YYYY_MM_DD_HH_MM_SS = new ThreadLocal<DateFormat>() {
            @Override
            protected DateFormat initialValue() {
                return new SimpleDateFormat(DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
            }
        };
        FORMAT_YYYYMMDDHHMMSSSSS_THREAD_LOCAL = new ThreadLocal<DateFormat>() {
            @Override
            protected DateFormat initialValue() {
                return new SimpleDateFormat(FORMAT_YYYYMMDDHHMMSSSS);
            }
        };
    }

    /**
     * 返回当地时区与UTC的时差"+xx:xx"，如东八区 返回 “+08:00”
     *
     * @return 时差"+xx:xx"
     */
    public static String getLocalTimeZone() {
        return FORMAT_FOR_OFFSET_TIME.get().format(Calendar.getInstance().getTime());
    }

    /**
     * 返回视频编辑中水印时间,yyyy/MM/dd HH:mm
     * @param date
     * @return
     */
    public static String getAccurateTime(Date date) {
        return new java.text.SimpleDateFormat(DATE_FORMAT_YYYY_MM_DD_HH_MM).format(date);
    }

    /**
     * yyyy/MM/dd HH:mm格式的时间字符
     * @param time 封禁的时间戳
     * @return
     */
    public static String getAccurateTime(long time) {
        return getDate(time, DATE_FORMAT_YYYY_MM_DD_HH_MM);
    }

    /**
     * 获取某个时间戳当天最大的时间点：23:59:59
     * @param time 时间戳
     * @return
     */
    public static long getDayEndTime(long time) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(time);
        calendar.set(Calendar.HOUR_OF_DAY, HOUR_OF_DAY_MAX);
        calendar.set(Calendar.MINUTE, MINUTE_MAX);
        calendar.set(Calendar.SECOND, SECOND_MAX);
        calendar.set(Calendar.MILLISECOND, MILLISECOND_MAX);
        return calendar.getTime().getTime();
    }

    public static String getYearDate(long time) {
        return getDate(time, FORMAT_YYYY);
    }

    public static String getYearDate(long time, Locale locale) {
        return getDate(time, FORMAT_YYYY, locale);
    }

    public static String getMonthDate(long time) {
        return getDate(time, FORMAT_MM);
    }

    public static String getMonthDate(long time, Locale locale) {
        return getDate(time, FORMAT_MM, locale);
    }

    public static String getDayDate(long time) {
        return getDate(time, FORMAT_DD);
    }

    public static String getYearMonthDate(long time) {
        return getDate(time, FORMAT_YYYY_MM);
    }

    public static String getYearMonthDate(long time, Locale locale) {
        return getDate(time, FORMAT_YYYY_MM, locale);
    }

    public static String getHourDate(long time, Locale locale) {
        return getDate(time, FORMAT_HH, locale);
    }

    public static String getYearMonthDayDate(long time, String format) {
        return getYearMonthDayDate(time, format, Locale.getDefault());
    }

    public static String getYearMonthDayDate(long time, String format, Locale locale) {
        return getDate(time, format, locale);
    }

    public static String getYearMonthDayDate(Date date, String format, Locale locale) {
        return getDateFormat(format, locale).format(date);
    }

    public static final DateFormat getDateFormat(String format, @Nullable Locale locale) {
        if (locale == Locale.getDefault()) {
            switch (format) {
                case FORMAT_YYYY_MM_DD:
                    return FORMAT_YYYY_MM_DD_THREAD_LOCAL.get();
                case FORMAT_YYYYMMDD:
                    return FORMAT_YYYYMMDD_THREAD_LOCAL.get();
                case FORMAT_YYYY_MM:
                    return FORMAT_YYYY_MM_THREAD_LOCAL.get();
                case FORMAT_YYYYMM:
                    return FORMAT_YYYYMM_THREAD_LOCAL.get();
                case FORMAT_YYYY:
                    return FORMAT_YYYY_THREAD_LOCAL.get();
                case FORMAT_MM:
                    return FORMAT_MM_THREAD_LOCAL.get();
                case FORMAT_DD:
                    return FORMAT_DD_THREAD_LOCAL.get();
                case FORMAT_HH:
                    return FORMAT_HH_THREAD_LOCAL.get();
                case DATE_FORMAT_YYYY_MM_DD_HH_MM:
                    return FORMAT_YYYY_MM_DD_HH_MM_THREAD_LOCAL.get();
                case FORMAT_YYYY_MM_DD_HH_MM_SS_UNDERLINE_SPLIT:
                    return FORMAT_YYYY_MM_DD_HH_MM_SS_UNDERLINE_THREAD_LOCAL.get();
                default:
                    return FORMAT_YYYY_MM_DD_THREAD_LOCAL.get();
            }
        } else {
            switch (format) {
                case FORMAT_YYYY_MM_DD:
                    return FORMAT_YYYY_MM_DD_THREAD_LOCAL_ENGLISH.get();
                case FORMAT_YYYYMMDD:
                    return FORMAT_YYYYMMDD_THREAD_LOCAL_ENGLISH.get();
                case FORMAT_YYYY_MM:
                    return FORMAT_YYYY_MM_THREAD_LOCAL_ENGLISH.get();
                case FORMAT_YYYYMM:
                    return FORMAT_YYYYMM_THREAD_LOCAL_ENGLISH.get();
                case FORMAT_YYYY:
                    return FORMAT_YYYY_THREAD_LOCAL_ENGLISH.get();
                case FORMAT_MM:
                    return FORMAT_MM_THREAD_LOCAL_ENGLISH.get();
                case FORMAT_DD:
                    return FORMAT_HH_THREAD_LOCAL_ENGLISH.get();
                case FORMAT_HH:
                    return FORMAT_HH_THREAD_LOCAL.get();
                case FORMAT_YYYY_MM_DD_HH_MM_SS_COLON_SPLIT:
                    return FORMAT_YYYY_MM_DD_HH_MM_SS_COLON_THREAD_LOCAL.get();
                case FORMAT_YYYY_MM_DD_COLON_SPLIT_HH_MM_POINT_SPLIT:
                    return FORMAT_YYYY_MM_DD_HH_MM_COLON_THREAD_LOCAL.get();
                case FORMAT_YYYY_MM_DD_COLON_SPLIT:
                    return FORMAT_YYYY_MM_DD_COLON_THREAD_LOCAL.get();
                case FORMAT_HH_MM_SS_COLON_SPLIT:
                    return FORMAT_HH_MM_SS_COLON_THREAD_LOCAL.get();
                case DATE_FORMAT_YYYY_MM_DD_HH_MM_SS:
                    return FORMAT_YYYY_MM_DD_HH_MM_SS.get();
                case FORMAT_YYYYMMDDHHMMSSSS:
                    return FORMAT_YYYYMMDDHHMMSSSSS_THREAD_LOCAL.get();
                default:
                    return FORMAT_YYYY_MM_DD_THREAD_LOCAL_ENGLISH.get();
            }
        }
    }

    public static String getDate(long time, String format) {
        return getDate(time, format, Locale.getDefault());
    }

    public static String getDate(long time, String format, Locale locale) {
        Date date = new Date(time);
        return getDateFormat(format, locale).format(date);
    }

    public static String getStrfTimeDate(long time, String format) {
        String text = "";
        if (!TextUtils.isEmpty(format)) {
            switch (format) {
                case STRFTIME_SEARCH_M:
                    text = getMonthDate(time, Locale.ENGLISH);
                    break;
                case STRFTIME_SEARCH_Y:
                    text = getYearDate(time, Locale.ENGLISH);
                    break;
                case STRFTIME_SEARCH_Y_M:
                    text = getYearMonthDate(time, Locale.ENGLISH);
                    break;
                case STRFTIME_SEARCH_H:
                    text = getHourDate(time, Locale.ENGLISH);
                    break;
                default:
                    text = getMonthDate(time);
                    break;
            }
        }
        return text;
    }

    /**
     * Takes a long value in seconds and convert it into an array of time: [hours, minutes, seconds]
     * @param duration long value describes how many seconds
     * @return an array of hour, minutes and seconds
     */
    public static int[] makeLongDurationToString(long duration) {

        int durationSecond = (int) (duration % AppConstants.Number.NUMBER_60);
        int durationMinute = (int) (duration / AppConstants.Number.NUMBER_60 % AppConstants.Number.NUMBER_60);
        int durationHour = (int) (duration / AppConstants.Number.NUMBER_60 / AppConstants.Number.NUMBER_60);

        return new int[]{durationHour, durationMinute, durationSecond};
    }


    public static String getDateFormat() {
        final Locale locale = Locale.getDefault();
        final String skeleton = WEEK_DAY_MONTH_DAY;
        return android.text.format.DateFormat.getBestDateTimePattern(locale, skeleton);
    }

    public static String getWholeChineseDateInfo(@NonNull Calendar calendar) {
        String week = DateFormat.getInstanceForSkeleton(DateFormat.WEEKDAY).format(calendar.getTime());
        String date = DateFormat.getInstanceForSkeleton(DateFormat.ABBR_MONTH_DAY).format(calendar.getTime());
        return date + "  " + week;
    }

    public static String getYMDDate(Date date) {
        DateFormat longDateFormat = DateFormat.getDateInstance(DateFormat.LONG);
        return longDateFormat.format(date);
    }

    public static String getYMDDate(Date date, TimeZone timeZone) {
        DateFormat longDateFormat = DateFormat.getDateInstance(DateFormat.LONG);
        longDateFormat.setTimeZone(timeZone);
        return longDateFormat.format(date);
    }

    public static String getMDDate(Context context, Date date) {
        return DateUtils.formatDateTime(context, date.getTime(), DateUtils.FORMAT_ABBREV_ALL);
    }

    /**
     * 返回水印和大图详细信息中的本地化格式日期
     * SHORT：返回本地化用分隔符格式化的日期，例：ch yyyy/MM/dd ind dd/MM/yyyy
     * @param date
     * @return
     */
    public static String getDashDate(Date date) {
        DateFormat shortDateFormat = DateFormat.getDateInstance(DateFormat.SHORT);
        return shortDateFormat.format(date);
    }

    /**
     * 返回水印和大图详细信息中地化格式的时间
     * 返回本地化用分隔符格式化的时间，到秒，例：ch 11：51：21 ind 11.51.21
     * DateFormat.HOUR_MINUTE_SECOND的值是jms，非英语地区格式化不正确，改用hms
     * @param date
     * @return
     */
    public static String getTimeToSeconds(Context context, Date date) {
        String skeleton = "";
        if (android.text.format.DateFormat.is24HourFormat(context)) {
            skeleton = FORMAT_LOCALIZATION_HOUR24_MINUTE_SECOND;
        } else {
            skeleton = FORMAT_LOCALIZATION_HOUR_MINUTE_SECOND;
        }
        return DateFormat.getInstanceForSkeleton(skeleton).format(date);
    }

    /**
     * 返回本地化时间到分DateFormat
     * DateFormat.HOUR_MINUTE_SECOND的值是jm，非英语地区格式化不正确，改用hm
     * @param context
     * @return DateFormat
     */
    public static DateFormat getTimeToMinute(Context context) {
        String skeleton = "";
        if (android.text.format.DateFormat.is24HourFormat(context)) {
            skeleton = FORMAT_LOCALIZATION_HOUR24_MINUTE;
        } else {
            skeleton = FORMAT_LOCALIZATION_HOUR_MINUTE;
        }
        return DateFormat.getInstanceForSkeleton(skeleton);
    }

    public static String getTime(Date date) {
        return DateFormat.getTimeInstance(DateFormat.SHORT).format(date);
    }

    public static String getDateString(long time) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(time);
        return String.format(Locale.ENGLISH, "%04d-%02d-%02d-%02d",
                calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH) + 1,
                calendar.get(Calendar.DAY_OF_MONTH),
                calendar.get(Calendar.HOUR_OF_DAY));
    }

    public static long getDayStartTime(long time) {
        Calendar date = Calendar.getInstance();
        date.setTimeInMillis(time);
        date.set(Calendar.HOUR_OF_DAY, date.getActualMinimum(Calendar.HOUR_OF_DAY));
        date.set(Calendar.MINUTE, date.getActualMinimum(Calendar.MINUTE));
        date.set(Calendar.SECOND, date.getActualMinimum(Calendar.SECOND));
        date.set(Calendar.MILLISECOND, date.getActualMinimum(Calendar.MILLISECOND));
        return date.getTimeInMillis();
    }

    public static long getMonthStartTime(long time) {
        Calendar monthStart = Calendar.getInstance();
        monthStart.setTimeInMillis(time);
        monthStart.set(Calendar.DAY_OF_MONTH,
                monthStart.getActualMinimum(Calendar.DAY_OF_MONTH));
        monthStart.set(Calendar.HOUR_OF_DAY, monthStart.getActualMinimum(Calendar.HOUR_OF_DAY));
        monthStart.set(Calendar.MINUTE, monthStart.getActualMinimum(Calendar.MINUTE));
        monthStart.set(Calendar.SECOND, monthStart.getActualMinimum(Calendar.SECOND));
        monthStart.set(Calendar.MILLISECOND, monthStart.getActualMinimum(Calendar.MILLISECOND));
        return monthStart.getTimeInMillis();
    }

    public static long getYearStartTime(long time) {
        Calendar yearStart = Calendar.getInstance();
        yearStart.setTimeInMillis(time);
        int currentYear = yearStart.get(Calendar.YEAR);
        yearStart.set(Calendar.YEAR, currentYear);
        yearStart.set(Calendar.MONTH,
                yearStart.getActualMinimum(Calendar.MONTH));
        yearStart.set(Calendar.DAY_OF_MONTH,
                yearStart.getActualMinimum(Calendar.DAY_OF_MONTH));
        yearStart.set(Calendar.HOUR_OF_DAY, yearStart.getActualMinimum(Calendar.HOUR_OF_DAY));
        yearStart.set(Calendar.MINUTE, yearStart.getActualMinimum(Calendar.MINUTE));
        yearStart.set(Calendar.SECOND, yearStart.getActualMinimum(Calendar.SECOND));
        yearStart.set(Calendar.MILLISECOND, yearStart.getActualMinimum(Calendar.MILLISECOND));
        return yearStart.getTimeInMillis();
    }

    public static boolean isToday(long time) {
        return DateUtils.isToday(time);
    }

    public static boolean isYesterday(long time) {
        Date date = new Date(time);
        long currTime = System.currentTimeMillis();
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(currTime - TIME_1_DAY_IN_MS);
        int curYear = calendar.get(Calendar.DAY_OF_YEAR);

        calendar.setTime(date);
        int dateYear = calendar.get(Calendar.DAY_OF_YEAR);

        long beforeTime = Math.abs(currTime - time);
        return (curYear == dateYear) && (beforeTime <= TIME_1_DAY_IN_MS * 2);
    }

    public static String getFileDateTakenFormat(long timeStamp) {
        SimpleDateFormat utcFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        String utcTime = utcFormat.format(new Date(timeStamp));
        return utcTime;
    }

    /**
     * 获取日期格式化，请自己管理locale看是否能直接用default。
     * 建议UI展示使用Locale.getDefault()，其他场景都使用Locale.US
     * 如果某些特殊情况指定Locale，可直接使用指定Locale。比如部分地区语言需特殊处理，请自己指定需要处理的Locale
     * 为何非展示推荐使用Locale.US，因为阿拉伯地区获取到的是阿拉伯字符，
     * 如：“٢٠٢٥٠٦١٦١٧٤٣٢٦”此类，服务器或一些场景并不支持读取，用此类字符串会引发异常
     *
     * @param timeMillis 时间戳
     * @param locale Local
     * @return 日期格式化后的字符串
     */
    public static String getFormatDateTime(long timeMillis, Locale locale) {
        String rel = "";
        SimpleDateFormat formatter = new SimpleDateFormat(TIME_FORMAT, locale);
        Date curDate = new Date(timeMillis);
        rel = formatter.format(curDate);
        return rel;
    }

    /**
     * 获取日期格式化，使用系统locale，仅供UI display使用。
     * 如果需要字符串处理，需判断是否会出现不支持阿拉伯字符串情况，
     * 避免“٢٠٢٥٠٦١٦١٧٤٣٢٦”此类字符串不支持导致异常
     *
     * @param timeMillis 时间戳
     * @return 日期格式化后的字符串
     * @see #getFormatDateTime(long, Locale)
     */
    @Deprecated
    public static String getFormatDateTime(long timeMillis) {
        return getFormatDateTime(timeMillis, Locale.getDefault());
    }

    /**
     * 获取日期格式化，使用当前时间戳和系统locale，仅供UI display使用。
     * 如果需要字符串处理，需判断是否会出现不支持阿拉伯字符串情况，
     * 避免“٢٠٢٥٠٦١٦١٧٤٣٢٦”此类字符串不支持导致异常
     *
     * @return 日期格式化后的字符串
     * @see #getFormatDateTime(long, Locale)
     */
    @Deprecated
    public static String getFormatDateTime() {
        return getFormatDateTime(System.currentTimeMillis());
    }

    /**
     * 获取“年月日时分秒毫秒"格式的日期字符串
     * @return
     */
    public static String getFormatDateTimeMillis() {
        return getDateFormat(FORMAT_YYYYMMDDHHMMSSSS, null)
                .format(new Date(System.currentTimeMillis()));
    }

    /**
     * 返回日期时间本地化年月
     * @param context
     * @param time
     * @return
     */
    public static String getFormatYMDate(Context context, long time) {
        /*FORMAT_SHOW_YEAR：如果该值被设置了，则年份始终会显示，如果没有设置，年份只有在格式化的时间跟当前时间不在同一年的时候显示。
        FORMAT_SHOW_DATE：显示月份和日期；
        FORMAT_NO_MONTH_DAY：显示月份和日期当中的月份，而不是日期，例如会显示“十二月”，而不是“12月27日”；*/
        int flag = DateUtils.FORMAT_SHOW_YEAR | DateUtils.FORMAT_SHOW_DATE | DateUtils.FORMAT_NO_MONTH_DAY;
        return DateUtils.formatDateTime(context, time, flag);
    }

    /**
     * 如果是今天，副标题显示：今天 小时:分钟、如果是昨天，副标题显示：昨天 小时:分钟、其他时间
     * 显示年月日时分（2023年12月27日 15：43）
     * @param context
     * @param time
     * @return
     */
    public static String getFormatMDHMDate(Context context, long time) {
        Date date = new Date(time);
        long timeMillis = System.currentTimeMillis();
        long beforeTime = timeMillis - time;
        int flag = DateUtils.FORMAT_SHOW_TIME;
        if (beforeTime > 0) {
            if ((beforeTime <= TIME_1_DAY_IN_MS) && isDateInCurrentDay(date)) {
                return String.format("%s %s", context.getString(R.string.common_today), DateUtils.formatDateTime(context, time, flag));
            }
            if ((beforeTime <= TIME_1_DAY_IN_MS * 2) && isDateInCurrentYesterday(date)) {
                return String.format("%s %s", context.getString(R.string.common_yesterday), DateUtils.formatDateTime(context, time, flag));
            }
        }
        flag = flag | DateUtils.FORMAT_SHOW_DATE;
        return DateUtils.formatDateTime(context, time, flag);
    }

    /**
     * 根据时间出返回格式化的年月时间
     * @param time 时间戳
     * @return 年月时间，如 2025年5月
     */
    public static String getFormatYMDDate(Context context, long time) {
        int flag = DateUtils.FORMAT_SHOW_YEAR | DateUtils.FORMAT_SHOW_DATE;
        return DateUtils.formatDateTime(context, time, flag);
    }

    public static String getFormatYMDHMDate(Context context, long time) {
        int flag = DateUtils.FORMAT_SHOW_YEAR | DateUtils.FORMAT_SHOW_TIME | DateUtils.FORMAT_SHOW_DATE;
        return DateUtils.formatDateTime(context, time, flag);
    }

    /**
     * 返回本地化日期MMdd，如果格式化日期跟当前时间不在同一年则返回yyyyMMdd
     * 用于大图顶部标题栏月份不缩写
     * @param context
     * @param time
     * @return
     */
    public static String getFormatMDDate(Context context, long time) {
        int flag = DateUtils.FORMAT_SHOW_DATE;
        return DateUtils.formatDateTime(context, time, flag);
    }

    /**
     * 获取时间戳的日，例如“15日”，中文需要带有“日”字
     * @param date 赋值了时间戳的date对象
     * @return 返回“X日”的字符串
     */
    public static String getDDate(Date date) {
        DateFormat dateFormat = DateFormat.getInstanceForSkeleton(DateFormat.DAY);
        String day = dateFormat.format(date);
        return day;
    }

    /**
     * 返回日期时间本地化月，1月、Jan
     * @param date
     * @return
     */
    public static String getMDate(Date date) {
        DateFormat dateFormat = DateFormat.getInstanceForSkeleton(DateFormat.ABBR_MONTH);
        String month = dateFormat.format(date);
        return month;
    }

    public static String getYMDate(Context context, Date date) {
        SimpleDateFormat sd = new SimpleDateFormat(context.getResources().getString(R.string.common_timeline_yyyymm));
        String time = sd.format(date);
        return time;
    }

    /**
     * 返回年
     * @param date
     * @return
     */
    public static String getYearDate(Date date) {
        DateFormat dateFormat = DateFormat.getInstanceForSkeleton(DateFormat.YEAR);
        return dateFormat.format(date);
    }

    /**
     * 返回本地化月日格式日期，月份为简写（Fri）
     * @param date
     * @return
     */
    public static String getMonthDayDate(Date date) {
        DateFormat dateFormat = DateFormat.getInstanceForSkeleton(DateFormat.ABBR_MONTH_DAY);
        return dateFormat.format(date);
    }

    /**
     * 返回本地化年月日格式日期，月份为简写（Fri）
     * @param date
     * @return
     */
    public static String getYearMonthDayDate(Date date) {
        DateFormat dateFormat = DateFormat.getInstanceForSkeleton(DateFormat.YEAR_ABBR_MONTH_DAY);
        return dateFormat.format(date);
    }

    /**
     * 返回本地化年月格式日期，月份为简写（Fri）
     */
    public static String getYearMonthDate(Date date) {
        DateFormat dateFormat = DateFormat.getInstanceForSkeleton(DateFormat.YEAR_ABBR_MONTH);
        return dateFormat.format(date);
    }

    /**
     * time1到time2是否是在同一天
     * @param time1
     * @param time2
     * @return
     */
    public static boolean isSameDay(Long time1, Long time2) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(time1);
        int year1 = calendar.get(Calendar.YEAR);
        int day1 = calendar.get(Calendar.DAY_OF_YEAR);
        calendar.setTimeInMillis(time2);
        int year2 = calendar.get(Calendar.YEAR);
        int day2 = calendar.get(Calendar.DAY_OF_YEAR);
        return (day1 == day2) && (year1 == year2);
    }

    public static boolean isDateInCurrentYear(Date date) {
        Calendar calendar = Calendar.getInstance();
        int curYear = calendar.get(Calendar.YEAR);
        calendar.setTime(date);
        int dateYear = calendar.get(Calendar.YEAR);
        return curYear == dateYear;
    }

    public static boolean isDateInCurrentDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        int curYear = calendar.get(Calendar.DAY_OF_YEAR);
        calendar.setTime(date);
        int dateYear = calendar.get(Calendar.DAY_OF_YEAR);
        return curYear == dateYear;
    }

    public static String getTimeZoneId() {
        TimeZone zone = TimeZone.getDefault();
        return zone.getID();
    }

    public static boolean isDateInCurrentYesterday(Date date) {
        Calendar calendar = Calendar.getInstance();
        long timeMillis = System.currentTimeMillis();
        long before = timeMillis - TIME_1_DAY_IN_MS;
        calendar.setTimeInMillis(before);
        int curYear = calendar.get(Calendar.DAY_OF_YEAR);
        calendar.setTime(date);
        int dateYear = calendar.get(Calendar.DAY_OF_YEAR);
        return curYear == dateYear;
    }

    public static long clearHour(long timeMillis) {
        Calendar date = Calendar.getInstance();
        date.setTimeInMillis(timeMillis);
        date.set(Calendar.HOUR_OF_DAY, 0);
        date.set(Calendar.MINUTE, 0);
        date.set(Calendar.SECOND, 0);
        date.set(Calendar.MILLISECOND, 0);
        return date.getTimeInMillis();
    }

    public static String makeVideoDurationToHMS(Context context, long duration) {
        StringBuilder stringBuilder = new StringBuilder();
        int[] times = makeLongDurationToString(duration);
        int durationHour = times[0];
        int durationMinute = times[1];
        int durationSecond = times[2];
        if (durationHour > 0) {
            stringBuilder.append(context.getResources().getQuantityString(R.plurals.common_hour_count, durationHour, durationHour));
        }
        if ((durationMinute > 0) || (durationHour > 0)) {
            stringBuilder.append(context.getResources().getQuantityString(R.plurals.common_minute_count, durationMinute, durationMinute));
        }
        stringBuilder.append(context.getResources().getQuantityString(R.plurals.common_second_count, durationSecond, durationSecond));
        return stringBuilder.toString();
    }

    /**
     * Returns a (localized) string for the given duration (in seconds).
     */
    public static String formatDuration(final Context context, int duration) {
        int h = duration / SECOND_IN_HOUR;
        int m = (duration - h * SECOND_IN_HOUR) / SECOND_IN_MINUTE;
        int s = duration - (h * SECOND_IN_HOUR + m * SECOND_IN_MINUTE);
        String durationValue = null;
        if (h == 0) {
            durationValue = String.format(context.getString(R.string.common_details_ms), m, s);
        } else {
            durationValue = String.format(context.getString(R.string.common_details_hms), h, m, s);
        }
        return durationValue;
    }

    /**
     * 视频时长转成 进度
     * @param timeMills     当前时间的毫秒数
     * @param durationMills durationMills 总视频的毫秒数
     * @return 格式为  xx:xx/xx:xx  比如 00:02 / 00:30
     */
    public static String formatVideoDuration(long timeMills, long durationMills, String[] timeTextFormat) {
        if ((timeTextFormat == null) || (timeTextFormat.length != 2)) {
            GLog.e(TAG, "formatVideoDuration timeTextFormat is null  or (timeTextFormat.length != 2");
            return null;
        }
        long time = timeMills / TimeUtils.TIME_1_SEC_IN_MS;
        long duration = durationMills / TimeUtils.TIME_1_SEC_IN_MS;
        int[] progressTimes = makeLongDurationToString(time);
        int[] durationTimes = makeLongDurationToString(duration);
        String progressText = "";
        if (ResourceUtils.isRTL(ContextGetter.context)) {
            if (durationTimes[0] > 0) {
                progressText = String.format(
                        LocaleUtils.getLocale(ContextGetter.context), timeTextFormat[1],
                        durationTimes[0], durationTimes[1], durationTimes[2], progressTimes[0], progressTimes[1], progressTimes[2]);
            } else {
                progressText = String.format(
                        LocaleUtils.getLocale(ContextGetter.context), timeTextFormat[0],
                        durationTimes[1], durationTimes[2], progressTimes[1], progressTimes[2]);
            }
        } else {
            if (durationTimes[0] > 0) {
                progressText = String.format(
                        LocaleUtils.getLocale(ContextGetter.context), timeTextFormat[1],
                        progressTimes[0], progressTimes[1], progressTimes[2], durationTimes[0], durationTimes[1], durationTimes[2]);
            } else {
                progressText = String.format(
                        LocaleUtils.getLocale(ContextGetter.context), timeTextFormat[0],
                        progressTimes[1], progressTimes[2], durationTimes[1], durationTimes[2]);
            }
        }

        return progressText;
    }

    /**
     * 验证时间戳是否有效
     * @param time 毫秒级时间戳
     * @return 是否有效
     */
    public static boolean validateMillisTime(long time) {
        return (time > 0) && (time < TimeUtils.MAX_TIME_UNIT_MILLISECOND);
    }

    /**
     * 验证时间戳是否有效
     * @param time 秒级时间戳
     * @return 是否有效
     */
    public static boolean validateSecondTime(long time) {
        return (time > 0) && (time < TimeUtils.MAX_TIME_UNIT_SECOND);
    }

    /**
     * 检查是否今天以后的时间，以0点为分界线。
     * @param time
     * @return
     */
    public static boolean isAfterToday(long time) {
        return System.currentTimeMillis() > time && !isToday(time);
    }

    /**
     * 获取旅行图集时间
     * @return 旅行图集时间区间
     * 如果是同一天，则返回 yyyy年M月d日
     * 如果是不同天，则返回 yyyy年M月d日-M月d日
     * 如果跨年了结束时间也要显示年份：2024年12月25日-2025年1月13日
     */
    public static String getTravelDateRange(long startTime, long endTime) {
        Date startDate = new Date(startTime);
        Date endDate = new Date(endTime);
        String startDateStr = getYMDDate(startDate);
        String endDateStr = getYMDDate(endDate);
        // 如果日期是同一天，则返回yyyy年MM月dd日
        if (startDateStr.equals(endDateStr)) {
            return startDateStr;
        } else if (!getYearDate(startDate).equals(getYearDate(endDate))) { // 跨年，则结束时间也要带上年份
            return startDateStr + "-" + endDateStr;
        } else { // 未跨年，结束时间不带年份
            return startDateStr + "-" + getMonthDayDate(endDate);
        }
    }

    /**
     * 返回图片、视频水印时间
     * 本地化需求相机、相册发版的节奏不一样会导致编辑水印前后显示不一致的情况
     * 因为相册是自发布应用，如果需求回落14.0就会导致14.0机器支不支持本地化要看用户有没有ota或者升级最新版本相册，没办法保证相机和相册完全版本一致
     * 保证同版本水印一致性的方案是：
     * 1. 相机本地化需求不回落14.0
     * 2. 相册这边判定os版本为低于14.1水印就用旧版显示，14.1及以上使用本地化显示
     */
    public static String getWatermarkTime(Context context, long timeMillis) {
        if (OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_14_1_0)) {
            /*
             * 返回os14.1及以上水印本地化时间格式
             */
            return DateUtils.formatDateTime(context, timeMillis,
                    DateUtils.FORMAT_SHOW_DATE | DateUtils.FORMAT_SHOW_YEAR | DateUtils.FORMAT_SHOW_TIME);
        } else {
            /*
             * 返回os14.1以下水印显示的时间
             * yyyy.MM.dd HH:mm 格式的时间
             */
            String timeToMinutes = null;
            try {
                timeToMinutes = getDateFormat(FORMAT_YYYY_MM_DD_COLON_SPLIT_HH_MM_POINT_SPLIT, null)
                        .format(timeMillis);
            } catch (IllegalArgumentException exception) {
                GLog.w(TAG, "getWatermarkTime: ", exception);
            }
            return timeToMinutes;
        }
    }

    /**
     * 判断当前时间是否大于给定时间
     * @param time yyyyMMddHHmmss
     * @return
     */
    public static boolean isAfterTime(String time) {
        LocalDateTime currentTime = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(TIME_FORMAT);
        LocalDateTime inputTime = null;
        try {
            inputTime = LocalDateTime.parse(time, formatter);
        } catch (DateTimeParseException e) {
            GLog.d(TAG, "isAfterTime exception: " + e.getMessage());
            return true;
        }
        return currentTime.isAfter(inputTime);
    }

    /**
     * 判断当前时间是否在给定时间范围内
     * @param startTime yyyyMMddHHmmss
     * @param endTime   yyyyMMddHHmmss
     * @return
     */
    public static boolean isBetweenTime(String startTime, String endTime) {
        LocalDateTime currentTime = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(TIME_FORMAT);
        LocalDateTime inputStartTime = null;
        try {
            inputStartTime = LocalDateTime.parse(startTime, formatter);
        } catch (DateTimeParseException e) {
            GLog.d(TAG, LogFlag.DL, "isBetweenTime exception: " + e.getMessage());
            return false;
        }
        LocalDateTime inputEndTime = null;
        try {
            inputEndTime = LocalDateTime.parse(endTime, formatter);
        } catch (DateTimeParseException e) {
            GLog.d(TAG, LogFlag.DL, "isBetweenTime exception: " + e.getMessage());
            return false;
        }
        return currentTime.isBefore(inputEndTime) && currentTime.isAfter(inputStartTime);
    }

    /**
     * 判断指定时间是否在给定时间范围内
     * @param specifiedTime yyyy-MM-dd
     * @param startTime     yyyy-MM-dd
     * @param endTime       yyyy-MM-dd
     * @return
     */
    public static boolean isSpecifiedTimeBetweenTime(String specifiedTime, String startTime, String endTime) {
        long inputStartTime = parseFormatTime(startTime, FORMAT_YYYY_MM_DD);
        long inputEndTime = parseFormatTime(endTime, FORMAT_YYYY_MM_DD);
        long inputSpecifiedTime = parseFormatTime(specifiedTime, FORMAT_YYYY_MM_DD);
        return inputSpecifiedTime >= inputStartTime && inputSpecifiedTime <= inputEndTime;
    }

    /**
     * 判断开始和结束时间是否在同一个自然月内
     * @param starTime 开始的时间戳
     * @param endTime 结束的时间戳
     * @return 开始和结束时间在同一个自然月内返回 true， 否则返回 false
     */
    public static boolean isInSameMonth(Long starTime, Long endTime) {
        return getMonthStartTime(starTime) == getMonthStartTime(endTime);
    }

    /**
     * 判断开始和结束时间是否在同一个自然年内
     * @param starTime 开始的时间戳
     * @param endTime 结束的时间戳
     * @return 开始和结束时间在同一个自然年内返回 true， 否则返回 false
     */
    public static boolean isInSameYear(Long starTime, Long endTime) {
        return getYearStartTime(starTime) == getYearStartTime(endTime);
    }

    /**
     * 获取几天前的日期，显示yyyyMMdd
     * @param day 天数
     * @return 显示yyyyMMdd
     */
    public static String getTimeWhenBeforeDays(int day) {
        Calendar ca = Calendar.getInstance();
        ca.setTimeInMillis(System.currentTimeMillis());
        ca.add(Calendar.DAY_OF_YEAR, -day);
        return new SimpleDateFormat(FORMAT_YYYYMMDD).format(ca.getTime());
    }

    /**
     * 获取几月前的日期，显示yyyyMMdd
     * @param month           月数
     * @param isContainsToday 是否包含今天，如果不包含则天数减1
     * @return 显示yyyyMMdd
     */
    public static String getTimeWhenBeforeMonths(int month, boolean isContainsToday) {
        Calendar ca = Calendar.getInstance();
        ca.setTimeInMillis(System.currentTimeMillis());
        if (!isContainsToday) {
            ca.add(Calendar.DAY_OF_YEAR, -ONE_DAY);
        }
        ca.add(Calendar.MONTH, -month);
        return new SimpleDateFormat(FORMAT_YYYYMMDD).format(ca.getTime());
    }

    /**
     * 获取几年前的日期，显示yyyyMMdd
     * @param year            年数
     * @param isContainsToday 是否包含今天，如果不包含则天数减1
     * @return 显示yyyyMMdd
     */
    public static String getTimeWhenBeforeYears(int year, boolean isContainsToday) {
        Calendar ca = Calendar.getInstance();
        ca.setTimeInMillis(System.currentTimeMillis());
        if (!isContainsToday) {
            ca.add(Calendar.DAY_OF_YEAR, -ONE_DAY);
        }
        ca.add(Calendar.YEAR, -year);
        return new SimpleDateFormat(FORMAT_YYYYMMDD).format(ca.getTime());
    }

    /**
     * 显示format样式的时间格式
     * @param timeMillis 时间毫秒数
     * @param format     时间样式
     * @param locale     地区
     * @return format样式的时间格式
     */
    public static String getTimeByLocale(long timeMillis, String format, Locale locale) {
        if (TextUtils.isEmpty(format) || (locale == null)) {
            return TextUtil.EMPTY_STRING;
        }
        Calendar ca = Calendar.getInstance();
        ca.setTimeInMillis(timeMillis);
        return new SimpleDateFormat(format, locale).format(ca.getTime());
    }

    /**
     * 在 [t1,t2] 之间生成随机日期
     *
     * @param t1 开始日期
     * @param t2 结束日期
     * @return 生成的随机日期
     */
    public static String randomBetweenTime(String t1, String t2) {
        long startTime = parseFormatTime(t1, FORMAT_YYYY_MM_DD);
        long endTime = parseFormatTime(t2, FORMAT_YYYY_MM_DD);
        return TimeUtils.getDate(startTime + (long) (RANDOM.nextDouble() * (endTime - startTime)), FORMAT_YYYY_MM_DD);
    }

    /**
     * 将格式化的日期字符串，转化成时间戳
     *
     * @param time    格式化的时间字符串
     * @param format 格式化样式
     * @return 时间戳
     */
    public static long parseFormatTime(String time, String format) {
        try {
            return getDateFormat(format, Locale.getDefault()).parse(time).getTime();
        } catch (ParseException e) {
            return INVALID_TIME;
        }
    }

    /**
     * 检查给定的 Locale 是否是中文，用于格式化不同日期
     *
     * @param locale 要检查的 Locale 对象
     * @return 如果 Locale 是中文，返回 true；否则返回 false
     */
    public static boolean isChineseLocale(Locale locale) {
        return locale.getLanguage().equals(CHINESE_LANGUAGE);
    }

    /**
     * 使用指定的模式将 Date 对象格式化为中文时间字符串
     *
     * @param date    要格式化的 Date 对象
     * @param pattern 日期格式化模式
     * @return 格式化后的中文时间字符串
     */
    public static String getChinaTimeString(Date date, String pattern) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(pattern, Locale.CHINA);
        return dateFormat.format(date);
    }
}