/***********************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - BitmapUtils.java
 ** Description:
 ** Version: 1.0
 ** Date : 2017/12/11
 ** Author: kexiuhua@Apps.Gallery3D
 **
 ** ---------------------Revision History: ---------------------
 **  <author>                 <data>     <version>  <desc>
 **  kexiuhua@Apps.Gallery3D  2017/12/11        build this module
 ****************************************************************/
package com.oplus.gallery.foundation.util.graphic;

import static com.oplus.gallery.foundation.util.math.MathUtil.getAlignedStrideUpToMultipleOfNumber;
import static com.oplus.gallery.foundation.util.math.MathUtil.max;
import static com.oplus.gallery.foundation.util.math.MathUtil.min;
import static com.oplus.gallery.standard_lib.app.AppConstants.Number.NUMBER_0xFF;
import static com.oplus.gallery.standard_lib.app.AppConstants.Number.NUMBER_24;

import android.content.ContentResolver;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.ColorSpace;
import android.graphics.Gainmap;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.PaintFlagsDrawFilter;
import android.graphics.PixelFormat;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.util.Size;

import androidx.annotation.NonNull;

import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.GProperty;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.foundation.util.display.ColorModelManager;
import com.oplus.gallery.foundation.util.ext.BitmapExtKt;
import com.oplus.gallery.foundation.util.math.MathUtil;
import com.oplus.gallery.foundation.util.media.MimeTypeUtils;
import com.oplus.gallery.foundation.util.version.ApiLevelUtil;
import com.oplus.gallery.standard_lib.util.io.IOUtils;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.util.Locale;

import kotlin.Suppress;
import okio.BufferedSource;
import okio.Okio;
import okio.Source;

public class BitmapUtils {

    public static final String COLOR_SPACE_UNKNOWN = "Unknown";
    public static final long UNCONSTRAINED = -1;
    public static final int HIGH_JPEG_QUALITY = 100;
    public static final int LOW_JPEG_QUALITY = 90;
    public static final int DEFAULT_JPEG_QUALITY = HIGH_JPEG_QUALITY;
    public static final int PER_PIXEL_SIZE_ALPHA = 1;
    public static final int PER_PIXEL_SIZE_ARGB = 4;
    private static final int MAX_JPEG_QUALITY = 100;
    private static final int MIN_JPEG_QUALITY = 0;
    private static final int ROTATION_360 = 360;
    private static final int ROTATION_180 = 180;
    private static final int NUM_7 = 7;
    private static final int NUM_8 = 8;
    private static final int IO_BUFFER_LENGTH = 65536;
    private static final int PER_PIXEL_SIZE_DEFAULT = PER_PIXEL_SIZE_ARGB;
    private static final String TAG = "BitmapUtils";

    /**
     * 将byteBuffer转换为指定宽高的bitmap
     *
     * @param byteBuffer 输入的byteBuffer；
     * @param height     指定bitmap的高度
     * @param width      指定bitmap的宽
     * @return 返回需要的bitmap
     */
    public static Bitmap byteBufferToBitmap(ByteBuffer byteBuffer, int width, int height, int format) {
        if ((byteBuffer == null) || (width <= 0) || (height <= 0)) {
            GLog.d(TAG, "[byteBufferToBitmap], input parameter error!");
            return null;
        }
        //default channel is 4.
        int channel = PER_PIXEL_SIZE_DEFAULT;
        if (format == PixelFormat.RGBA_8888) {
            //32 bits per pixel (8 bits per channel) RGB with alpha channel .
            channel = PER_PIXEL_SIZE_ARGB;
        }

        byte[] byteArrayDest = new byte[width * height * channel];
        byte[] byteArraySource = byteBuffer.array();
        if (byteArraySource.length != byteArrayDest.length) {
            GLog.w(TAG, "[byteBufferToBitmap], byteArray length not equal!");
            return null;
        }
        GLog.d(TAG, "[byteBufferToBitmap], Channel number is: " + channel);
        for (int i = 0; i < height; i++) {
            System.arraycopy(byteArraySource, i * width * channel, byteArrayDest, (height - i - 1) * width * channel, width * channel);
        }
        Bitmap bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        if (bitmap == null) {
            return null;
        }
        bitmap.copyPixelsFromBuffer(ByteBuffer.wrap(byteArrayDest));
        return bitmap;
    }

    /**
     * 将一个图片进行复制
     *
     * @param src 原Bitmap
     * @return Bitmap
     */
    public static Bitmap copyBitmap(Bitmap src) {
        return copyBitmap(src, null);
    }

    /**
     * 将一个图片进行复制
     *
     * @param src    原Bitmap
     * @param config Bitmap.Config
     * @return Bitmap
     */
    public static Bitmap copyBitmap(Bitmap src, Bitmap.Config config) {
        return copyBitmap(src, config, false);
    }

    /**
     * 将一个图片进行复制
     *
     * @param src     原Bitmap
     * @param config  Bitmap.Config
     * @param recycle 是否回收原图
     * @return Bitmap
     */
    public static Bitmap copyBitmap(Bitmap src, Bitmap.Config config, boolean recycle) {
        if (src == null) {
            GLog.e(TAG, "[copyBitmap] copy bitmap error, cause src is null!");
            return null;
        }
        if (src.isRecycled()) {
            GLog.e(TAG, "[copyBitmap] copy bitmap error, cause src has bean recycled!");
            return null;
        }
        Bitmap copy = null;
        if (config == null) {
            if (src.getConfig() == null) {
                copy = src.copy(Bitmap.Config.ARGB_8888, true);
            } else {
                copy = src.copy(src.getConfig(), src.isMutable());
            }
        } else {
            copy = src.copy(config, src.isMutable());
        }
        if (recycle) {
            recycleSilently(src);
        }
        return copy;
    }

    /**
     * 将 srcBitmap 内容复制到 dstBitmap 中。
     * 系统中暂未提供既能复用又能复制 Bitmap 的方式，此处使用 Canvas 绘制方式实现。
     * 本地测试，性能上和 Bitmap.copy 差距不大，但是复制一次可以复用已有的 Bitmap，减少申请内存，减少内存抖动
     *
     * @return true：复制成功；false：复制失败，
     * 失败时条件：!dstBitmap.isMutable() || srcBitmap.getByteCount() > dstBitmap.getAllocationByteCount()
     */
    public static boolean copyBitmapTo(Bitmap srcBitmap, Bitmap dstBitmap) {
        if ((srcBitmap == null) || (dstBitmap == null)) {
            return false;
        }
        if (srcBitmap.isRecycled() || dstBitmap.isRecycled()) {
            return false;
        }
        if (!dstBitmap.isMutable()) {
            return false;
        }
        if (srcBitmap.getByteCount() > dstBitmap.getAllocationByteCount()) {
            return false;
        }
        int srcWidth = srcBitmap.getWidth();
        int srcHeight = srcBitmap.getHeight();
        if ((srcWidth <= 0) || (srcHeight <= 0)) {
            return false;
        }
        int dstWidth = dstBitmap.getWidth();
        int dstHeight = dstBitmap.getHeight();
        if ((srcWidth != dstWidth) || (srcHeight != dstHeight)) {
            dstBitmap.reconfigure(srcWidth, srcHeight, srcBitmap.getConfig());
        }

        Rect rect = new Rect(0, 0, srcWidth, srcHeight);
        return copyBitmapTo(srcBitmap, rect, dstBitmap, rect);
    }

    /**
     * 将 srcBitmap中的srcRect内容复制到 dstBitmap的destRect区域。
     * @param srcBitmap
     * @param srcRect
     * @param dstBitmap
     * @param dstRect
     * @return
     */
    public static boolean copyBitmapTo(Bitmap srcBitmap, Rect srcRect, Bitmap dstBitmap, Rect dstRect) {
        if ((srcBitmap == null) || (dstBitmap == null)) {
            return false;
        }
        if ((srcRect == null) || (dstRect == null)) {
            return false;
        }
        if (srcBitmap.isRecycled() || dstBitmap.isRecycled()) {
            return false;
        }
        if (!dstBitmap.isMutable()) {
            return false;
        }

        int srcWidth = srcBitmap.getWidth();
        int srcHeight = srcBitmap.getHeight();
        if ((srcWidth <= 0) || (srcHeight <= 0)) {
            return false;
        }
        if ((srcRect.left < 0) || (srcRect.top < 0) || (srcRect.right > srcWidth) || (srcRect.bottom > srcHeight)) {
            return false;
        }

        int dstWidth = dstBitmap.getWidth();
        int dstHeight = dstBitmap.getHeight();
        if ((dstWidth <= 0) || (dstHeight <= 0)) {
            return false;
        }
        if ((dstRect.left < 0) || (dstRect.top < 0) || (dstRect.right > dstWidth) || (dstRect.bottom > dstHeight)) {
            return false;
        }

        ColorSpace colorSpace = srcBitmap.getColorSpace();
        if ((colorSpace != null) && !(colorSpace.getName().equals(COLOR_SPACE_UNKNOWN))) {
            trySetBitmapColorSpace(dstBitmap, colorSpace, "copyBitmapTo");
        } else {
            trySetBitmapColorSpace(dstBitmap, ColorSpace.get(ColorSpace.Named.SRGB), "copyBitmapTo");
        }

        dstBitmap.setDensity(srcBitmap.getDensity());
        dstBitmap.setHasAlpha(srcBitmap.hasAlpha());

        // 系统中暂未提供既能复用又能复制 Bitmap 的方式，此处使用 Canvas 绘制方式实现
        Paint paint = new Paint(Paint.FILTER_BITMAP_FLAG | Paint.DITHER_FLAG);
        Canvas canvas = new Canvas(dstBitmap);
        canvas.drawBitmap(srcBitmap, srcRect, dstRect, paint);
        canvas.setBitmap(null);

        // 如果图片存在增益图，也要对增益图进行复制
        if (ApiLevelUtil.isAtLeastAndroidU() && srcBitmap.getGainmap() != null) {
            Bitmap copyGainmapContents = copyBitmap(srcBitmap.getGainmap().getGainmapContents());
            if (copyGainmapContents != null) {
                Gainmap gainmap = BitmapExtKt.copyGainMap(srcBitmap.getGainmap(), copyGainmapContents);
                dstBitmap.setGainmap(gainmap);
            }
        }

        if (GProperty.DEBUG) {
            GLog.d(TAG, "[copyBitmapTo] success, " + "srcRect(" + srcRect + "), " + "dstRect(" + dstRect + ")");
        }

        return true;
    }

    // Bitmap.getPixel 不能取最大值，clamp最大值为max-1
    public static float clamp(float src, float min, float max) {
        return (src < min) ? min : ((src < max) ? src : max - 1);
    }

    public static byte[] getFileContent(Context context, String fileName) {
        byte[] data = null;
        InputStream is = null;
        Source source = null;

        try {
            is = context.getAssets().open(fileName);
            source = Okio.source(is);
            BufferedSource bSource = Okio.buffer(source);
            data = bSource.readByteArray();
        } catch (Exception e) {
            GLog.e(TAG, "getFileContent, e:", e);
        } finally {
            IOUtils.closeQuietly(source);
            IOUtils.closeQuietly(is);
        }
        return data;
    }

    /**
     * 缩放图像
     *
     * @param bitmap  被缩放的图像内容
     * @param scale   缩放值
     * @param recycle 是否在完成缩放后回收传入的bitmap
     * @param needAlign4 是否限制图片的宽高为4字节对齐，这里的对齐原则是取最接近bitmap宽高的4字节倍数对齐
     * @return 缩放结果
     */
    public static Bitmap resizeBitmapByScale(Bitmap bitmap, float scale, boolean recycle, boolean needAlign4) {
        return resizeBitmapByScale(bitmap, null, scale, recycle, needAlign4);
    }

    /**
     * 缩放图像
     *
     * @param bitmap        被缩放的图像内容
     * @param bitmapCreator bitmap生成器，用于缩放内容后作为结果返回
     * @param scale         缩放值
     * @param recycle       是否在完成缩放后回收传入的bitmap
     * @param needAlign4    是否限制图片的宽高为4字节对齐，这里的对齐原则是取最接近bitmap宽高的4字节倍数对齐
     * @return 缩放结果
     */
    public static Bitmap resizeBitmapByScale(Bitmap bitmap, BitmapCreator bitmapCreator, float scale, boolean recycle, boolean needAlign4) {
        if (bitmap == null || bitmap.isRecycled()) {
            GLog.e(TAG, "resizeBitmapByScale, bitmap is null or recycled!");
            return null;
        }
        int width = Math.round(bitmap.getWidth() * scale);
        int height = Math.round(bitmap.getHeight() * scale);
        if ((width == bitmap.getWidth())
            && (height == bitmap.getHeight())) {
            return bitmap;
        }
        if (needAlign4) {
            if (!MathUtil.isAlignedStrideMultipleOf4(width)) {
                width = MathUtil.alignment4(width);
            }
            if (!MathUtil.isAlignedStrideMultipleOf4(height)) {
                height = MathUtil.alignment4(height);
            }
            GLog.i(TAG, "resizeBitmapByScale,start width:" + Math.round(bitmap.getWidth() * scale)
                    + ",height:" + Math.round(bitmap.getHeight() * scale)
                    + ",end width:" + width + ",height:" + height);
        }
        if ((width <= 0) || (height <= 0)) {
            GLog.e(TAG, LogFlag.DL, "resizeBitmapByScale, width:" + width + ",height:" + height);
            return bitmap;
        }
        Bitmap target = createBitmap(bitmap, bitmapCreator, width, height);
        Canvas canvas = new Canvas(target);
        Paint paint = new Paint(Paint.FILTER_BITMAP_FLAG | Paint.DITHER_FLAG);
        canvas.drawBitmap(
                bitmap,
                new Rect(0, 0, bitmap.getWidth(), bitmap.getHeight()),
                new Rect(0, 0, target.getWidth(), target.getHeight()),
                paint);
        // 如果图片存在增益图，也要对增益图做等比例缩放并写回到图片中
        if (ApiLevelUtil.isAtLeastAndroidU() && bitmap.getGainmap() != null) {
            Bitmap resizedGainMap = resizeBitmapByScale(bitmap.getGainmap().getGainmapContents(), bitmapCreator, scale, recycle, needAlign4);
            if (resizedGainMap != null) {
                Gainmap gainmap = BitmapExtKt.copyGainMap(bitmap.getGainmap(), resizedGainMap);
                target.setGainmap(gainmap);
            }
        }
        if (recycle) {
            recycleSilently(bitmap);
        }
        return target;
    }

    private static Bitmap.Config getConfig(Bitmap bitmap) {
        Bitmap.Config config = bitmap.getConfig();
        if (config == null) {
            config = Bitmap.Config.ARGB_8888;
        }
        return config;
    }

    /**
     * 将传图的图像缩放至长边不超过指定大小
     *
     * @param bitmap    图像
     * @param maxLength 图像长边最大大小
     * @param recycle   是否在完成缩放后回收传入的bitmap
     * @return 缩放结果
     */
    public static Bitmap resizeByLongSide(Bitmap bitmap, int maxLength, boolean recycle) {
        return resizeByLongSide(bitmap, null, maxLength, false, recycle);
    }

    /**
     * 将传图的图像缩放至长边不超过指定大小
     *
     * @param bitmap        原图像
     * @param bitmapCreator bitmap生成器，用于缩放内容后作为结果返回
     * @param maxLength     图像长边最大大小
     * @param forceEnlarge  当原图分辨率小于maxLength时，是否放大，一般不建议为true，
     *                      单纯提升分辨率并不能提升清晰度，会占用更大的内存
     * @param recycle       是否在完成缩放后回收传入的bitmap
     * @return 缩放结果
     */
    public static Bitmap resizeByLongSide(
        Bitmap bitmap,
        BitmapCreator bitmapCreator,
        int maxLength,
        boolean forceEnlarge,
        boolean recycle
    ) {
        if (bitmap == null) {
            GLog.w(TAG, "resizeByLongSide, bitmap is null!");
            return null;
        }
        float scale = (float) maxLength / Math.max(bitmap.getWidth(), bitmap.getHeight());
        if (!forceEnlarge && (scale >= 1f)) {
            return bitmap;
        }
        return resizeBitmapByScale(bitmap, bitmapCreator, scale, recycle, false);
    }

    /**
     * 将传图的图像缩放至短边不超过指定大小
     *
     * @param bitmap        原图像
     * @param bitmapCreator bitmap生成器，用于缩放内容后作为结果返回
     * @param maxLength     图像短边最大大小
     * @param forceEnlarge  当原图分辨率小于maxLength时，是否放大，一般不建议为true，
     *                      单纯提升分辨率并不能提升清晰度，会占用更大的内存
     * @param recycle       是否在完成缩放后回收传入的bitmap
     * @return 缩放结果
     */
    public static Bitmap resizeByShortSide(
        Bitmap bitmap,
        BitmapCreator bitmapCreator,
        int maxLength,
        boolean forceEnlarge,
        boolean recycle
    ) {
        if (bitmap == null) {
            GLog.w(TAG, "resizeByShortSide, bitmap is null!");
            return null;
        }
        float scale = (float) maxLength / Math.min(bitmap.getWidth(), bitmap.getHeight());
        if (!forceEnlarge && (scale >= 1f)) {
            return bitmap;
        }
        return resizeBitmapByScale(bitmap, bitmapCreator, scale, recycle, false);
    }

    /**
     * 根据传入的原始尺寸计算裁剪为目标尺寸所需的偏移量
     * 同时要参考推荐区域，即最大化地包含推荐区域
     * 1，当推荐区域的中心点小于目标尺寸的中心点时，说明推荐区域偏前面，直接从前面开始取够目标尺寸
     * 2，相反的，推荐区域偏后面时，从后面取够目标尺寸
     * 3，其他情况，以推荐区域中心点为中心取够目标尺寸
     *
     * @param originalSize    原始尺寸
     * @param recommendCenter 推荐区域的中间点坐标
     * @param targetSize      目标尺寸
     * @return
     */
    public static float calculateTranslation(int originalSize, float recommendCenter, int targetSize) {
        /**
         * 没有推荐区域，默认使用居中
         */
        if (recommendCenter <= 0) {
            return (targetSize - originalSize) / 2f;
        }
        float translateValue = 0;
        if (recommendCenter <= targetSize / 2f) {
            translateValue = 0;
        } else if (recommendCenter >= originalSize - targetSize / 2f) {
            translateValue = targetSize - originalSize;
        } else {
            translateValue = targetSize / 2f - recommendCenter;
        }
        return translateValue;
    }

    /**
     * 缩放并裁剪bitmap
     *
     * @param size    短边最大的分辨率
     * @param recycle 是否在处理后回收srcBitmap
     * @return 结果
     */
    public static Bitmap resizeAndCropCenter(Bitmap bitmap, int size, boolean recycle) {
        return resizeAndCrop(bitmap, size, true, recycle, null, null);
    }

    /**
     * 缩放并裁剪bitmap
     *
     * @param srcBitmap        原图
     * @param maxShortSideSize 短边最大的分辨率
     * @param forceEnlarge     true表示即使原图分辨率不足，也放大短边至maxShortSideSize
     * @param recycle          是否在处理后回收srcBitmap
     * @param recommendRect    推荐的裁剪区域，取其宽高比作为最终结果bitmap的宽高比
     * @param bitmapCreator    获取bitmap用于填充裁剪内容，作为结果返回
     * @return 结果
     */
    public static Bitmap resizeAndCrop(Bitmap srcBitmap,
                                       int maxShortSideSize,
                                       boolean forceEnlarge,
                                       boolean recycle,
                                       Rect recommendRect,
                                       BitmapCreator bitmapCreator) {
        if (srcBitmap == null || srcBitmap.isRecycled()) {
            GLog.e(TAG, "resizeAndCropCenter, bitmap is null or recycled!");
            return null;
        }

        final int srcWidth = srcBitmap.getWidth();
        final int srcHeight = srcBitmap.getHeight();
        if ((srcWidth == maxShortSideSize) && (srcHeight == maxShortSideSize) && (recommendRect == null)) {
            return srcBitmap;
        }
        final Rect cropRect = enlargeCropRect(srcWidth, srcHeight, recommendRect);
        final int rectWidth = cropRect.width();
        final int rectHeight = cropRect.height();
        final int shortSideSize = forceEnlarge ? maxShortSideSize : Math.min(maxShortSideSize, Math.min(rectWidth, rectHeight));
        int width = shortSideSize;
        int height = shortSideSize;
        if (rectWidth > rectHeight) {
            width = shortSideSize * rectWidth / rectHeight;
        } else if (rectWidth < rectHeight) {
            height = shortSideSize * rectHeight / rectWidth;
        }

        Bitmap result = createBitmap(srcBitmap, bitmapCreator, width, height);
        Canvas canvas = new Canvas(result);
        Paint paint = new Paint(Paint.FILTER_BITMAP_FLAG | Paint.DITHER_FLAG);
        canvas.drawBitmap(srcBitmap, cropRect, new RectF(0, 0, width, height), paint);
        if (recycle) {
            recycleSilently(srcBitmap);
        }
        return result;
    }

    /**
     * 放大裁剪区域，使其一边挨到原图上
     *
     * @param srcWidth      原图宽
     * @param srcHeight     原图高
     * @param recommendRect 原图尺寸下的推荐裁剪区域，最终图片裁剪比例与推荐比例一致
     * @return 最终裁剪区域
     */
    @NonNull
    public static Rect enlargeCropRect(int srcWidth, int srcHeight, Rect recommendRect) {
        // 存在除法操作，需要判空
        if ((recommendRect == null) || recommendRect.isEmpty()) {
            GLog.w(TAG, LogFlag.DL, () -> "[enlargeCropRect], recommendRect is null or empty, return calculateSquareCropRect!");
            return calculateSquareCropRect(srcWidth, srcHeight);
        }

        int recommendWidth = recommendRect.width();
        int recommendHeight = recommendRect.height();
        // 以推荐区域尽可能放大
        int resultWidth = srcWidth;
        int resultHeight = srcWidth * recommendHeight / recommendWidth;
        if (resultHeight > srcHeight) {
            resultHeight = srcHeight;
            resultWidth = srcHeight * recommendWidth / recommendHeight;
        }

        // 结果裁剪框不能超过原图边界
        final int left = Math.min(Math.max(0, recommendRect.centerX() - resultWidth / 2), srcWidth - resultWidth);
        final int top = Math.min(Math.max(0, recommendRect.centerY() - resultHeight / 2), srcHeight - resultHeight);
        return new Rect(left, top, left + resultWidth, top + resultHeight);
    }

    /**
     * 计算1x1的居中裁剪区域
     *
     * @param srcWidth  原图宽
     * @param srcHeight 原图高
     * @return 裁剪区域
     */
    @NonNull
    private static Rect calculateSquareCropRect(int srcWidth, int srcHeight) {
        if (srcWidth > srcHeight) {
            int left = (srcWidth - srcHeight) / 2;
            return new Rect(left, 0, left + srcHeight, srcHeight);
        } else if (srcWidth < srcHeight) {
            int top = (srcHeight - srcWidth) / 2;
            return new Rect(0, top, srcWidth, top + srcWidth);
        } else {
            return new Rect(0, 0, srcWidth, srcHeight);
        }
    }

    /**
     * 根据目标宽高计算居中裁剪区域
     *
     * @param srcWidth     原图宽
     * @param srcHeight    原图高
     * @param targetWidth  目标宽
     * @param targetHeight 目标高
     * @return 裁剪区域
     */
    @NonNull
    public static Rect calculateCropRect(int srcWidth, int srcHeight, int targetWidth, int targetHeight) {
        float srcAspect = (float) srcWidth / srcHeight;
        float targetAspect = (float) targetWidth / targetHeight;
        if (srcAspect > targetAspect) {
            int newWidth = (int) (srcHeight * targetAspect);
            int widthOffset = (srcWidth - newWidth) / 2;
            return new Rect(widthOffset, 0, srcWidth - widthOffset, srcHeight);
        } else {
            int newHeight = (int) (srcWidth / targetAspect);
            int heightOffset = (srcHeight - newHeight) / 2;
            return new Rect(0, heightOffset, srcWidth, srcHeight - heightOffset);
        }
    }

    /**
     * 水平镜像翻转bitmap
     *
     * @param srcBitmap 待翻转的bitmap
     * @param needRecycle 翻转后原图是否需要回收
     * @return 返回翻转后的bitmap
     * */
    public static Bitmap flipBitmapHorizontally(Bitmap srcBitmap, Boolean needRecycle) {
        long start = System.currentTimeMillis();
        int srcWidth = srcBitmap.getWidth();
        int srcHeight = srcBitmap.getHeight();
        if (srcWidth <= 0 || srcHeight <= 0) {
            GLog.w(TAG, "<flipBitmapHorizontally> srcBitmap size is wrong! return origin!");
            return srcBitmap;
        }
        Matrix matrix = new Matrix();
        matrix.setScale(-1f, 1f, srcWidth / 2f, srcHeight / 2f);
        Bitmap resultBmp = Bitmap.createBitmap(srcBitmap, 0, 0, srcWidth, srcHeight, matrix, true);
        if (needRecycle) {
            srcBitmap.recycle();
        }
        GLog.d(TAG, "<flipBitmapHorizontally> costTime=" + GLog.getTime(start));
        return resultBmp;
    }

    /**
     * 检查和设置原bitmap，并且复制其配置到新创建的bitmap上
     *
     * @param srcBitmap     参考图
     * @param bitmapCreator bitmap生成器
     * @param resultWidth   图像宽
     * @param resultHeight  图像高
     * @return 结果
     */
    private static Bitmap createBitmap(Bitmap srcBitmap, BitmapCreator bitmapCreator, int resultWidth, int resultHeight) {
        Bitmap result = null;
        if (bitmapCreator != null) {
            result = bitmapCreator.create(resultWidth, resultHeight, getConfig(srcBitmap));
        }
        if (result == null) {
            result = Bitmap.createBitmap(resultWidth, resultHeight, getConfig(srcBitmap));
        }
        if (srcBitmap.getConfig() == Bitmap.Config.ALPHA_8) {
            // ALPHA_8不支持设置ColorSpace
            return result;
        }
        if ((srcBitmap.getColorSpace() == null) || srcBitmap.getColorSpace().getName().equals(COLOR_SPACE_UNKNOWN)) {
            srcBitmap.setColorSpace(ColorSpace.get(ColorSpace.Named.SRGB));
        }
        ColorSpace colorSpace = srcBitmap.getColorSpace();
        if ((colorSpace != null)
            && !(colorSpace.getName().equals(COLOR_SPACE_UNKNOWN))
            && ColorModelManager.INSTANCE.isWideColorGamutOpen()) {
            trySetBitmapColorSpace(result, colorSpace, "createBitmap");
        } else {
            trySetBitmapColorSpace(result, ColorSpace.get(ColorSpace.Named.SRGB), "createBitmap");
        }
        return result;
    }

    /**
     * 根据传入的裁剪区域,获取裁剪区域内的Bitmap
     *
     * @param srcBitmap 需要裁剪的图片
     * @param rect      裁剪的区域
     * @return 返回裁剪区域的图片
     */
    public static Bitmap cropBitmap(Bitmap srcBitmap, Rect rect) {
        if (rect == null) {
            GLog.e(TAG, "[cropBitmap] can't crop bitmap because rectF can't be null");
            return null;
        }
        if (srcBitmap == null) {
            GLog.e(TAG, "[cropBitmap] can't crop bitmap because srcBitmap is null");
            return null;
        }
        if (rect.left < 0) {
            rect.left = 0;
        }
        if (rect.top < 0) {
            rect.top = 0;
        }
        if (rect.right > srcBitmap.getWidth()) {
            rect.right = srcBitmap.getWidth();
        }
        if (rect.bottom > srcBitmap.getHeight()) {
            rect.bottom = srcBitmap.getHeight();
        }
        try {
            return Bitmap.createBitmap(srcBitmap, rect.left, rect.top, rect.width(), rect.height());
        } catch (IllegalArgumentException e) {
            GLog.e(TAG, "[cropBitmap] crop bitmap fail, bitmapWidth:"
                + srcBitmap.getWidth() + ", bitmapHeight:" + srcBitmap.getHeight()
                + ", rect:" + rect.toShortString(), e);
        }
        return null;
    }

    public static void recycleSilently(Bitmap bitmap) {
        if ((bitmap != null) && (!bitmap.isRecycled())) {
            bitmap.recycle();
        }
    }

    public static Bitmap rotateBitmap(Bitmap source, int rotation, boolean recycle) {
        return rotateBitmap(source, rotation, Bitmap.Config.ARGB_8888, recycle, false);
    }

    public static Bitmap rotateBitmap(Bitmap source, int rotation, boolean recycle, boolean mirror) {
        return rotateBitmap(source, rotation, Bitmap.Config.ARGB_8888, recycle, mirror);
    }

    public static Bitmap rotateBitmap(Bitmap source, int rotation, Bitmap.Config config, boolean recycle) {
        return rotateBitmap(source, rotation, config, recycle, false);
    }

    public static Bitmap rotateBitmap(Bitmap source, int rotation, Bitmap.Config config, boolean recycle, boolean mirror) {
        if ((source == null) || (rotation == 0) || (rotation == ROTATION_360)) {
            return source;
        }
        if (source.isRecycled()) {
            GLog.e(TAG, LogFlag.DL, "[rotateBitmap] rotate bitmap error, cause src is recycled!");
            return null;
        }
        int destRotation = (rotation + ROTATION_360) % ROTATION_360;
        int w = source.getWidth();
        int h = source.getHeight();
        Matrix m = new Matrix();
        m.setRotate(destRotation, (float) w / 2, (float) h / 2);
        if (mirror) {
            m.postScale(-1f, 1f, (float) w / 2, (float) h / 2);
        }

        Bitmap bitmap = null;
        try {
            if (destRotation == ROTATION_180) {
                bitmap = Bitmap.createBitmap(w, h, config, true);
            } else {
                m.postTranslate((float) h / 2 - (float) w / 2, (float) w / 2 - (float) h / 2);
                bitmap = Bitmap.createBitmap(h, w, config, true);
            }
            // alpha_8不支持设置colorSpace
            if (config != Bitmap.Config.ALPHA_8) {
                ColorSpace colorSpace = source.getColorSpace();
                if ((colorSpace != null) && !(colorSpace.getName().equals(COLOR_SPACE_UNKNOWN))) {
                    bitmap.setColorSpace(colorSpace);
                } else {
                    bitmap.setColorSpace(ColorSpace.get(ColorSpace.Named.SRGB));
                }
            } else {
                GLog.d(TAG, "[rotateBitmap] unnecessary to set colorspace because config is alpha8");
            }
            Paint paint = new Paint();
            Canvas canvas = new Canvas(bitmap);
            canvas.drawBitmap(source, m, paint);
            if (ApiLevelUtil.isAtLeastAndroidU()) {
                // UHdr图则传递gainmap即可，非UHdr图gainmap本身为null, 传递给屏显时，角度是单独带过去的
                bitmap.setGainmap(source.getGainmap());
            }
            if (recycle) {
                source.recycle();
            }
        } catch (IllegalArgumentException e) {
            GLog.e(
                TAG,
                "[rotateBitmap] rotate bitmap error, bitmap width:" + w + ", height:" + h + ", rotation:" + rotation,
                e
            );
            BitmapUtils.recycleSilently(bitmap);
        }
        return bitmap;
    }

    /**
     * 对图片进行缩放，旋转，镜像
     * @param source 原图
     * @param rotation 旋转的方向
     * @param mirror 是否镜像
     * @param size bitmap处理后的宽高
     * @param recycle 是否回收原图
     * @return 返回缩放，旋转，镜像后的bitmap
     */
    @Suppress(names = {"LongMethod", "LongParameterList"})
    public static Bitmap transformBitmap(Bitmap source, int rotation, boolean mirror,
                                         Size size, boolean recycle) {
        int width = size.getWidth();
        int height = size.getHeight();
        if ((source == null) || (source.getWidth() <= 0) || (source.getHeight() <= 0) || (width <= 0) || (height <= 0)) {
            GLog.w(TAG, LogFlag.DL, "[transformBitmap] invalid data." + (source == null));
            return source;
        }
        if (source.isRecycled()) {
            GLog.e(TAG, LogFlag.DL, "[transformBitmap] rotate bitmap error, cause src is recycled!");
            return null;
        }
        int destRotation = (rotation + ROTATION_360) % ROTATION_360;
        int w = source.getWidth();
        int h = source.getHeight();
        float scaleX = 1f;
        float scaleY = 1f;
        Matrix matrix = new Matrix();
        Bitmap bitmap = null;
        matrix.setRotate(destRotation, (float) w / 2, (float) h / 2);
        try {
            if (destRotation != ROTATION_180) {
                matrix.postTranslate((float) h / 2 - (float) w / 2, (float) w / 2 - (float) h / 2);
            }
            int denominatorX = width;
            int denominatorY = height;
            if (width < height) {
                denominatorX = min(w, h);
                denominatorY = max(w, h);
            } else {
                denominatorX = max(w, h);
                denominatorY = min(w, h);
            }
            scaleX = (float) width / denominatorX;
            scaleY = (float) height / denominatorY;
            if (mirror) {
                matrix.postScale(-1f, 1f, (float) width / 2, (float) height / 2);
            }
            Matrix scaleMatrix = new Matrix();
            scaleMatrix.preScale(scaleX, scaleY);
            scaleMatrix.postConcat(matrix);
            bitmap = Bitmap.createBitmap(source, 0, 0, w, h, scaleMatrix, true);
            // alpha_8不支持设置colorSpace
            if (source.getConfig() != Bitmap.Config.ALPHA_8) {
                ColorSpace colorSpace = source.getColorSpace();
                if ((colorSpace != null) && !(colorSpace.getName().equals(COLOR_SPACE_UNKNOWN))) {
                    bitmap.setColorSpace(colorSpace);
                } else {
                    bitmap.setColorSpace(ColorSpace.get(ColorSpace.Named.SRGB));
                }
            } else {
                GLog.d(TAG, "[transformBitmap] unnecessary to set colorspace because config is alpha8");
            }
            /**
             * Bitmap.createBitmap 可能返回原始bitmap，所以要判断一下
             */
            if ((source != bitmap) && recycle) {
                recycleSilently(source);
            }
        } catch (IllegalArgumentException e) {
            GLog.e(
                    TAG,
                    "[transformBitmap] rotate bitmap error, bitmap width:" + w + ", height:" + h
                            + "width:" + width + ",height:" + height + ",mirror:" + mirror + ", rotation:" + rotation, e
            );
            BitmapUtils.recycleSilently(bitmap);
        }
        return bitmap;
    }

    /**
     * 创建指定比例的图片
     *
     * @param src    原始图片
     * @param scale  比例大小
     * @param filter 位图采样是否使用平滑的过渡方式
     * @return 按照指定比例大小创建后的新图
     */
    public static Bitmap scaleBitmap(Bitmap src, float scale, boolean filter) {
        if ((src == null) || (scale <= 0)) {
            return src;
        }
        int srcWidth = src.getWidth();
        int srcHeight = src.getHeight();

        return Bitmap.createScaledBitmap(src, (int) (scale * srcWidth), (int) (scale * srcHeight), filter);
    }

    /**
     * bitmap的宽或高若是超过指定的最大宽高，则按比例缩放到最大宽高
     * @param src
     * @param maxWidth
     * @param maxHeight
     * @return
     */
    public static Bitmap scaleBitmap(Bitmap src, int maxWidth, int maxHeight) {
        if (src == null) {
            return null;
        }
        Bitmap scaledBitmap = src;
        int srcWidth = src.getWidth();
        int srcHeight = src.getHeight();
        if ((srcWidth > maxWidth) || (srcHeight > maxHeight)) {
            float scale = Math.min(maxHeight * 1f / srcHeight, maxWidth * 1f / srcWidth);
            scaledBitmap = Bitmap.createScaledBitmap(src, (int) (scale * srcWidth), (int) (scale * srcHeight), true);
        }

        return scaledBitmap;
    }

    public static Bitmap scaleBitmap(Bitmap src, int maxWidth, int maxHeight, float suggestScale) {
        Bitmap scaledBitmap = src;
        int srcWidth = src.getWidth();
        int srcHeight = src.getHeight();

        if ((maxWidth > 0) && (maxHeight > 0)) {
            if ((srcWidth > maxWidth) || (srcHeight > maxHeight)) {
                int suggestWidth = (int) (srcWidth * suggestScale);
                int suggestHeight = (int) (srcHeight * suggestScale);
                GLog.v(TAG, String.format(Locale.ENGLISH,
                    "[scaleBitmap] bitmap is too large for canvas to upload, resize from (%d, %d) to (%d, %d)",
                    srcWidth, srcHeight, suggestWidth, suggestHeight));
                scaledBitmap = scaleBitmap(src, suggestScale, true);
            }
        }
        GLog.v(TAG, "scaleBitmap maxWidth = " + maxWidth + "maxHeight = " + maxHeight + ",scaledBitmap = " + scaledBitmap);
        return scaledBitmap;
    }

    /**
     * 通过缩放的方式压缩图片
     *
     * @param bitmap       图片的位图
     * @param desireWidth  期望的宽
     * @param desireHeight 期望的高
     * @param scaleMode    压缩的模式，参考{@link ImageUtils#scaleImage(float, float, float, float, int)}
     * @return 返回压缩后的图片
     */
    public static Bitmap scaleToSuggestSize(Bitmap bitmap, int desireWidth, int desireHeight, int scaleMode) {
        if (bitmap != null) {
            float suggestScale = ImageUtils.scaleImage(
                bitmap.getWidth(), bitmap.getHeight(),
                desireWidth, desireHeight,
                scaleMode);
            return scaleBitmap(bitmap, desireWidth, desireHeight, suggestScale);
        }
        return null;
    }

    /**
     * 已过时，请使用EncodeUtils中提供的Bitmap.compressToBytes(CompressQuality.LOW_QUALITY)替换
     *
     * @param bitmap
     * @return
     */
    @Deprecated
    public static byte[] compressLowQualityToBytes(Bitmap bitmap) {
        return compressToBytes(bitmap, Bitmap.CompressFormat.JPEG, LOW_JPEG_QUALITY);
    }

    /**
     * 已过时，请使用EncodeUtils中提供的Bitmap.compressToBytes(CompressQuality.HIGH_QUALITY)替换
     *
     * @param bitmap
     * @return
     */
    @Deprecated
    public static byte[] compressToBytes(Bitmap bitmap) {
        return compressToBytes(bitmap, Bitmap.CompressFormat.JPEG, DEFAULT_JPEG_QUALITY);
    }

    /**
     * 已过时，请使用EncodeUtils中提供的Bitmap.compressToBytes(quality: CompressQuality)替换
     *
     * @param bitmap
     * @param cf
     * @return
     */
    @Deprecated
    public static byte[] compressToBytes(Bitmap bitmap, Bitmap.CompressFormat cf) {
        return compressToBytes(bitmap, cf, DEFAULT_JPEG_QUALITY);
    }

    /**
     * 已过时，请使用EncodeUtils中提供的Bitmap.compressToBytes(quality: CompressQuality)替换
     *
     * @param bitmap
     * @param cf
     * @return
     */
    @Deprecated
    public static byte[] compressToBytes(Bitmap bitmap, Bitmap.CompressFormat cf, int quality) {
        if ((bitmap == null) || (cf == null) || (quality < MIN_JPEG_QUALITY) || (quality > MAX_JPEG_QUALITY)) {
            return new byte[0];
        }
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream(IO_BUFFER_LENGTH)) {
            bitmap.compress(cf, quality, baos);
            return baos.toByteArray();
        } catch (Exception e) {
            GLog.w(TAG, "compressToBytes error", e);
            return new byte[0];
        }
    }

    /**
     * 采用质量压缩，将图片压缩到指定大小
     *
     * @param bitmap     图片
     * @param cf         图片的格式
     * @param targetSize 需要压缩的大小
     * @param interval   允许压缩的质量误差范围
     * @return 返回压缩后的图片字节数组，其大小近似targetSize
     */
    public static byte[] compressToBytes(Bitmap bitmap, Bitmap.CompressFormat cf, long targetSize, int interval) {
        int quality = computeBitmapQualityWithinSize(bitmap, cf, targetSize, interval);
        return compressToBytes(bitmap, cf, quality);
    }

    /**
     * 采用质量压缩，将图片压缩到指定大小,然后返回此质量值
     *
     * @param bitmap     图片
     * @param cf         图片的格式
     * @param targetSize 需要压缩的大小
     * @param interval   允许压缩的质量误差范围
     * @return 返回误差在interval范围内的图片质量值
     */
    public static int computeBitmapQualityWithinSize(Bitmap bitmap, Bitmap.CompressFormat cf, long targetSize, int interval) {
        if ((bitmap == null) || (cf == null) || (targetSize < 0) || (interval <= 0)) {
            return DEFAULT_JPEG_QUALITY;
        }
        ByteArrayOutputStream baos = new ByteArrayOutputStream(IO_BUFFER_LENGTH);
        try {
            int minQuality = 5;
            int maxQuality = 100;
            bitmap.compress(cf, maxQuality, baos);
            int currentSize = baos.toByteArray().length;
            if (currentSize < targetSize) {
                return maxQuality;
            }
            while ((maxQuality - minQuality) >= interval) {
                int quality = (maxQuality + minQuality) / 2;
                baos.reset();
                bitmap.compress(Bitmap.CompressFormat.JPEG, quality, baos);
                currentSize = baos.toByteArray().length;
                if (currentSize > targetSize) {
                    maxQuality = quality;
                } else {
                    minQuality = quality;
                }
            }
            return minQuality;
        } catch (Exception e) {
            GLog.w(TAG, "computeBitmapQualityWithinSize error", e);
        } finally {
            IOUtils.closeQuietly(baos);
        }
        return DEFAULT_JPEG_QUALITY;
    }

    public static boolean isRotationSupported(String mimeType) {
        return MimeTypeUtils.isJpeg(mimeType);
    }

    public static Bitmap createBitmapWithErrorCatchRetry(int width, int height, Bitmap.Config config) {
        Bitmap bitmap = createBitmap(width, height, config);
        if (null == bitmap) {
            bitmap = createBitmap(width, height, config);
        }
        return bitmap;
    }

    private static Bitmap createBitmap(int width, int height, Bitmap.Config config) {
        Bitmap bitmap = null;
        try {
            bitmap = Bitmap.createBitmap(width, height, config);
        } catch (OutOfMemoryError e) {
            System.gc();
            System.runFinalization();
        }
        return bitmap;
    }

    public static Bitmap bakeBitmap(Bitmap src, Bitmap dest, boolean keepRatio) {
        if ((src == null) || (dest == null) || !dest.isMutable() || dest.isRecycled()) {
            return null;
        }

        Canvas canvas = new Canvas(dest);
        Matrix matrix = new Matrix();
        float srcWidth = src.getWidth();
        float srcHeight = src.getHeight();
        float destWidth = dest.getWidth();
        float destHeight = dest.getHeight();
        if (keepRatio) {
            float scaleWidth = destWidth / srcWidth;
            float scaleHeight = destHeight / srcHeight;
            matrix.setScale(scaleWidth, scaleHeight);
        } else {
            float srcRatio = srcWidth / srcHeight;
            float destRatio = destWidth / destHeight;
            float scale = (srcRatio > destRatio) ? (destHeight / srcHeight) : (destWidth / srcWidth);
            float translateX = (destWidth - scale * srcWidth) * 0.5F;
            float translateY = (destHeight - scale * srcHeight) * 0.5F;
            matrix.postScale(scale, scale);
            matrix.postTranslate(translateX, translateY);
        }
        //scale bitmap must anti alias
        if ((srcWidth != destWidth) || (srcHeight != destHeight)) {
            canvas.setDrawFilter(new PaintFlagsDrawFilter(0, Paint.ANTI_ALIAS_FLAG | Paint.FILTER_BITMAP_FLAG));
        }
        canvas.drawBitmap(src, matrix, new Paint(Paint.ANTI_ALIAS_FLAG | Paint.FILTER_BITMAP_FLAG));
        return dest;
    }

    public static Bitmap clampBitmap(Bitmap bitmap,
                                     int contentLeft,
                                     int contentTop,
                                     int contentRight,
                                     int contentBottom) {
        if ((bitmap == null) || bitmap.isRecycled()) {
            return bitmap;
        }

        int bitmapWidth = bitmap.getWidth();
        int bitmapHeight = bitmap.getHeight();
        int contentWidth = contentRight - contentLeft;
        int contentHeight = contentBottom - contentTop;
        if ((contentLeft >= contentRight) || (contentTop >= contentBottom)) {
            return bitmap;
        }
        if ((bitmapWidth == contentWidth) && (bitmapHeight == contentHeight)) {
            return bitmap;
        }

        Canvas canvas = new Canvas(bitmap);
        Rect src = new Rect();
        RectF dest = new RectF();
        if (contentLeft > 0) {
            src.set(contentLeft, contentTop, contentLeft + 1, contentBottom);
            dest.set(0, contentTop, contentLeft, contentBottom);
            canvas.drawBitmap(bitmap, src, dest, null);
        }
        if (contentRight < bitmapHeight) {
            src.set(contentRight - 1, contentTop, contentRight, contentBottom);
            dest.set(contentRight, contentTop, bitmapWidth, contentBottom);
            canvas.drawBitmap(bitmap, src, dest, null);
        }
        if (contentTop > 0) {
            src.set(contentLeft, contentTop, contentRight, contentTop + 1);
            dest.set(contentLeft, 0, contentRight, contentTop);
            canvas.drawBitmap(bitmap, src, dest, null);
        }
        if (contentBottom < bitmapHeight) {
            src.set(contentLeft, contentTop - 1, contentRight, contentTop);
            dest.set(contentLeft, contentTop, contentRight, bitmapHeight);
            canvas.drawBitmap(bitmap, src, dest, null);
        }
        return bitmap;
    }

    /**
     * 字节对齐，修正为4的倍数
     *
     * @param bitmap 传入需要做字节对齐的图片
     * @return 返回字节对齐后的图片
     */
    public static Bitmap strideAligned(Bitmap bitmap) {
        if (bitmap == null) {
            GLog.w(TAG, "strideAligned bitmap is null.");
            return null;
        }
        Bitmap outputBitmap = bitmap;
        int dstWidth = bitmap.getWidth();
        int dstHeight = bitmap.getHeight();
        boolean isSizeMultipleOf4 = MathUtil.isAlignedStrideMultipleOf4(dstWidth)
            && MathUtil.isAlignedStrideMultipleOf4(dstHeight);
        if (!isSizeMultipleOf4) {
            dstWidth = MathUtil.getAlignedStrideUpToMultipleOf4(bitmap.getWidth());
            dstHeight = MathUtil.getAlignedStrideUpToMultipleOf4(bitmap.getHeight());
        }
        if ((dstWidth != bitmap.getWidth()) || (dstHeight != bitmap.getHeight())) {
            outputBitmap = Bitmap.createScaledBitmap(bitmap, dstWidth, dstHeight, true);
        }
        return outputBitmap;
    }

    /**
     * 采用指定字节对齐方式，添加透明padding,非裁剪
     * 添加padding的方式：左上角对齐，右下角加padding, 向alignmentMultiple这个倍数对齐
     *
     * @param sourceBitmap 需要添加
     * @param specifiedByteAlignmentNumber 指定的字节对齐数
     * @return 添加透明padding之后的bitmap
     */
    public static Bitmap addTransparentPadding(Bitmap sourceBitmap, int specifiedByteAlignmentNumber) {
        int paddingWidth = getAlignedStrideUpToMultipleOfNumber(sourceBitmap.getWidth(), specifiedByteAlignmentNumber);
        int paddingHeight = getAlignedStrideUpToMultipleOfNumber(sourceBitmap.getHeight(), specifiedByteAlignmentNumber);
        GLog.d(TAG, "[addTransparentPadding] paddingWidth = " + paddingWidth + ", paddingHeight = " + paddingHeight);

        // 创建一个新的Bitmap，比原始Bitmap的边框大
        ColorSpace colorSpace = sourceBitmap.getColorSpace();
        if (colorSpace == null) {
            colorSpace = ColorSpace.get(ColorSpace.Named.DCI_P3);
        }
        Bitmap borderedBitmap = Bitmap.createBitmap(
                paddingWidth,
                paddingHeight,
                sourceBitmap.getConfig(),
                sourceBitmap.hasAlpha(),
                colorSpace);

        // 使用白色背景初始化新Bitmap
        Canvas canvas = new Canvas(borderedBitmap);
        canvas.drawColor(Color.TRANSPARENT);

        /**
         * 在新Bitmap上绘制原始Bitmap
         * 补padding的方式：是在原图的右边和底部添加透明padding，而不是在四周添加padding，
         * 所以绘制时需要从左上角开始绘制，以保证padding是补在右边和底部的
         */
        canvas.drawBitmap(sourceBitmap, 0 , 0, null);
        return borderedBitmap;
    }

    /**
     * 1:1尺寸图片对齐
     * @param bitmap-传入需要做纠正的图片
     * @param tolerance-宽高差值
     * @return 返回对齐后的图片
     */
    public static Bitmap reviseSquare(Bitmap bitmap, int tolerance) {
        if (bitmap == null) {
            GLog.w(TAG, "reviseSquare bitmap is null.");
            return null;
        }
        Bitmap outputBitmap = bitmap;
        int dstWidth = bitmap.getWidth();
        int dstHeight = bitmap.getHeight();
        if (dstHeight == dstWidth) {
            return outputBitmap;
        }
        if (!MathUtil.equalWithinTolerance(dstHeight, dstWidth, tolerance)) {
            return outputBitmap;
        } else {
            dstWidth = Math.max(bitmap.getWidth(), bitmap.getHeight());
            dstHeight = Math.max(bitmap.getWidth(), bitmap.getHeight());
            outputBitmap = Bitmap.createScaledBitmap(bitmap, dstWidth, dstHeight, true);
        }
        return outputBitmap;
    }

    public static boolean isOpaque(int color) {
        return color >>> NUMBER_24 == NUMBER_0xFF;
    }

    /**
     * 给 Bitmap 设置色彩空间可能会抛异常(IllegalArgumentException, 继承自 RuntimeException),
     * 但现行阶段不允许捕获 RuntimeException, 是编程红线, 设置色彩空间时, 为保险起见, 此处只能提前判断相关的异常点
     */
    public static void trySetBitmapColorSpace(Bitmap bitmap, ColorSpace newColorSpace, String changeReason) {
        if (!isBitmapColorSpaceCompatible(bitmap, newColorSpace)) {
            GLog.w(TAG, changeReason + ".trySetBitmapColorSpace: the specified newColorSpace can't be set to bitmap");
            return;
        }

        bitmap.setColorSpace(newColorSpace);
    }

    /**
     * 检查 newColorSpace 是否能设置给 Bitmap, 给 Bitmap 设置色彩空间前, 使用该函数进行判断
     *
     * @see Bitmap#setColorSpace
     */
    private static boolean isBitmapColorSpaceCompatible(Bitmap bitmap, ColorSpace newColorSpace) {
        if ((bitmap == null) || (newColorSpace == null)) {
            return false;
        }

        if (bitmap.isRecycled()) {
            return false;
        }

        if (bitmap.getConfig() == Bitmap.Config.ALPHA_8) {
            return false;
        }

        ColorSpace oldColorSpace = bitmap.getColorSpace();
        if (oldColorSpace == null) {
            return false;
        }

        if (oldColorSpace.getComponentCount() != newColorSpace.getComponentCount()) {
            GLog.w(TAG, LogFlag.DL, "<isBitmapColorSpaceCompatible> not same comp, return false!");
            return false;
        }

        for (int i = 0; i < oldColorSpace.getComponentCount(); i++) {
            if (oldColorSpace.getMinValue(i) < newColorSpace.getMinValue(i)) {
                return false;
            }
            if (oldColorSpace.getMaxValue(i) > newColorSpace.getMaxValue(i)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 将图片缩放到适当的比例 适配到最大的宽和高
     *
     * @param bitmap    bitmap
     * @param maxWidth  maxWidth 图片最大的宽
     * @param maxHeight maxHeight 图片最大的高
     * @param recycle   recycle
     * @return Bitmap
     */
    public static Bitmap resizeByLongSide(Bitmap bitmap, int maxWidth, int maxHeight, boolean recycle) {
        if (bitmap == null) {
            GLog.w(TAG, "resizeDownBySideLength, bitmap is null!");
            return null;
        }
        int srcWidth = bitmap.getWidth();
        int srcHeight = bitmap.getHeight();
        float scale = Math.min(
            (float) maxWidth / srcWidth, (float) maxHeight / srcHeight);
        if (scale >= 1f) {
            return bitmap;
        }
        return resizeBitmapByScale(bitmap, scale, recycle, false);
    }

    /**
     * 转换drawable为bitmap
     *
     * @param drawable
     * @param colorSpace 将drawable转换为bitmap时设置的色域信息，为空表示不需要存储drawable的色域信息或drawable的色域信息为空
     * @return
     */
    public static Bitmap drawableToBitmap(Drawable drawable, ColorSpace colorSpace) {
        if (drawable == null) {
            return null;
        }
        int intrinsicWidth = drawable.getIntrinsicWidth();
        int intrinsicHeight = drawable.getIntrinsicHeight();
        if ((intrinsicWidth <= 0) || (intrinsicHeight <= 0)) {
            return null;
        }
        Bitmap.Config bitmapConfig = (drawable.getOpacity() != PixelFormat.OPAQUE) ? Bitmap.Config.ARGB_8888 : Bitmap.Config.RGB_565;
        Bitmap bitmap = Bitmap.createBitmap(intrinsicWidth, intrinsicHeight, bitmapConfig);
        if (colorSpace != null) {
            bitmap.setColorSpace(colorSpace);
        }
        Canvas canvas = new Canvas(bitmap);
        drawable.setBounds(0, 0, intrinsicWidth, intrinsicHeight);
        drawable.draw(canvas);
        return bitmap;
    }

    /**
     * 转换drawable为指定大小的bitmap
     *
     * @param drawable drawable
     * @param colorSpace 将drawable转换为bitmap时设置的色域信息，为空表示不需要存储drawable的色域信息或drawable的色域信息为空
     * @param width bitmap宽度
     * @param height bitmap高度
     * @return bitmap
     */
    public static Bitmap drawableToBitmap(Drawable drawable, ColorSpace colorSpace, int width, int height) {
        if (drawable == null) {
            return null;
        }
        if ((drawable.getIntrinsicWidth() <= 0) || (drawable.getIntrinsicHeight() <= 0)) {
            return null;
        }
        Bitmap.Config bitmapConfig = (drawable.getOpacity() != PixelFormat.OPAQUE) ? Bitmap.Config.ARGB_8888 : Bitmap.Config.RGB_565;
        Bitmap bitmap = Bitmap.createBitmap(width, height, bitmapConfig);
        if (colorSpace != null) {
            bitmap.setColorSpace(colorSpace);
        }
        Canvas canvas = new Canvas(bitmap);
        drawable.setBounds(0, 0, width, height);
        drawable.draw(canvas);
        return bitmap;
    }

    /**
     * 通过Uri得到图片宽高
     *
     * @param context
     * @param imageUri 图片Uri
     * @return
     */
    public static Size getImageSize(Context context, Uri imageUri) {
        ContentResolver resolver = context.getContentResolver();
        InputStream inputStream = null;
        try {
            inputStream = resolver.openInputStream(imageUri);
            BitmapFactory.Options options = new BitmapFactory.Options();
            //只读取图片的尺寸信息,不加载图片到内存中
            options.inJustDecodeBounds = true;
            BitmapFactory.decodeStream(inputStream, null, options);
            return new Size(options.outWidth, options.outHeight);
        } catch (FileNotFoundException e) {
            GLog.e(TAG, "[getImageDimensions] decodeStream exception", e);
            return null;
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    GLog.e(TAG, "[getImageDimensions] close error", e);
                }
            }
        }
    }

    /**
     * 创建并返回一个缩放后的Bitmap。
     *
     * @param bitmap 原始Bitmap  新位图的所需宽度
     * @param width  新位图的所需宽度
     * @param height 新位图的所需高度
     * @param filter 缩放位图时是否应使用双线性过滤
     * @return 新的缩放位图或源位图（如果不需要缩放）
     */
    public static Bitmap createScaledBitmap(Bitmap bitmap, int width, int height, boolean filter) {
        if (bitmap == null || bitmap.isRecycled()) {
            GLog.e(TAG, LogFlag.DF, "[createScaledBitmap] bitmap is null or recycled");
            return null;
        }
        return Bitmap.createScaledBitmap(bitmap, width, height, filter);
    }

    /**
     * 将 Bitmap 转为可变的 Bitmap。(isMutable()为true)
     * */
    public static Bitmap obtainMutableBitmapFrom(Bitmap bitmap) {
        if (bitmap == null) {
            return null;
        }
        if (bitmap.isMutable()) {
            return bitmap;
        }
        return bitmap.copy(BitmapExtKt.getConfigSafely(bitmap), true);
    }
}
