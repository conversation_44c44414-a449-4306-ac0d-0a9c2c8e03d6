/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp, Ltd.
 ** VENDOR_EDIT
 ** File        : ViewExt.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/4/28 17:35
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2022/4/28  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.foundation.util.ext

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.RectF
import android.util.Size
import android.view.AttachedSurfaceControl
import android.view.MotionEvent
import android.view.TextureView
import android.view.TouchDelegate
import android.view.View
import android.view.ViewConfiguration
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.annotation.Px
import androidx.core.view.forEach
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.oplus.gallery.foundation.util.R
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.foundation.util.version.ApiLevelUtil
import com.oplus.gallery.standard_lib.app.AppConstants

private const val TAG = "ViewExt"

/**
 * 无效的像素值
 */
private const val INVALID_PX = -1

/**负数表示垂直向上滚或水平向左滚*/
const val SCROLL_UP_OR_LEFT_DELTA = -1f

/**正数表示垂直向下滚或水平向右滚*/
const val SCROLL_DOWN_OR_RIGHT_DELTA = 1f

/**
 * 当前是否是Rtl布局
 */
val View.isRtl: Boolean
    get() = (layoutDirection == View.LAYOUT_DIRECTION_RTL)

/**
 * 更新视图的外间距，只有View的 [layoutParams] 是 [MarginLayoutParams]才生效
 */
fun View.updateMargin(
    @Px left: Int = INVALID_PX,
    @Px top: Int = INVALID_PX,
    @Px right: Int = INVALID_PX,
    @Px bottom: Int = INVALID_PX
) {
    var changed = false
    val lp = layoutParams as? ViewGroup.MarginLayoutParams ?: return
    if ((left != INVALID_PX) && (left != lp.leftMargin)) {
        lp.leftMargin = left
        changed = true
    }
    if ((top != INVALID_PX) && (top != lp.topMargin)) {
        lp.topMargin = top
        changed = true
    }
    if ((right != INVALID_PX) && (right != lp.rightMargin)) {
        lp.rightMargin = right
        changed = true
    }
    if ((bottom != INVALID_PX) && (bottom != lp.bottomMargin)) {
        lp.bottomMargin = bottom
        changed = true
    }
    if (changed) {
        layoutParams = lp
    }
}

/**
 * 更新视图的外间距，只有View的 [layoutParams] 是 [MarginLayoutParams]才生效
 * @return true:传参有变化有更新 false:传参和之前的一样，不需要更新
 */
fun View.updateMarginRelative(
    @Px start: Int = INVALID_PX,
    @Px top: Int = INVALID_PX,
    @Px end: Int = INVALID_PX,
    @Px bottom: Int = INVALID_PX
): Boolean {
    var changed = false
    val lp = layoutParams as? ViewGroup.MarginLayoutParams ?: return false
    if ((start != INVALID_PX) && (start != lp.marginStart)) {
        lp.marginStart = start
        changed = true
    }
    if ((top != INVALID_PX) && (top != lp.topMargin)) {
        lp.topMargin = top
        changed = true
    }
    if ((end != INVALID_PX) && (end != lp.marginEnd)) {
        lp.marginEnd = end
        changed = true
    }
    if ((bottom != INVALID_PX) && (bottom != lp.bottomMargin)) {
        lp.bottomMargin = bottom
        changed = true
    }
    if (changed) {
        layoutParams = lp
    }
    return changed
}

fun View.setRecursionEnable(enable: Boolean) {
    isEnabled = enable
    (this as? ViewGroup)?.forEach {
        it.setRecursionEnable(enable)
    }
}

/**
 * View 执行[slideIn]、[slideOut]时的滑动方向。
 */
enum class ViewSlideDirection {
    /**
     * 视图的 `start`方向，取决于是否是`rtl`布局。
     * - 不是RTL ： left
     * - 是RTL ： right
     */
    START,

    /**
     * 视图的顶部方向
     */
    TOP,

    /**
     * 视图的 `end`方向，取决于是否是`rtl`布局。
     * - 不是RTL ： right
     * - 是RTL ： left
     */
    END,

    /**
     * 视图的底部方向
     */
    BOTTOM
}

fun View.setWeight(weight: Float) {
    layoutParams?.let { lp ->
        (lp as? LinearLayout.LayoutParams)?.takeIf { it.weight != weight }?.let {
            it.weight = weight
            layoutParams = it
        } ?: GLog.d(TAG, "setWeight,update Weight fail,${(lp as? LinearLayout.LayoutParams)},$weight")
    }
}

/**
 * 获取view的全局坐标,包括可见或不可见部分
 * 当view部分滑出屏幕时,
 * @param ignoreInvisible 忽略不可见部分
 *        true:返回的rect不包含不可见的区域
 *        false:返回的rect包含不可见区域部分,rect的left top 可能为负数
 * @return 返回view在屏幕内或外的全局坐标
 */
fun View.getGlobalRect(ignoreInvisible: Boolean = false): Rect {
    val coverViewRect = Rect()
    this.getGlobalVisibleRect(coverViewRect)
    if (ignoreInvisible) {
        return coverViewRect
    }
    // 处理view部分滑出屏幕的不可见区域坐标，还原出view的全局坐标
    val dHeight = coverViewRect.height() - this.height
    val dWidth = coverViewRect.width() - this.width
    GLog.d(TAG, "getCoverGlobalVisibleRect:d=$dWidth:$dHeight,$coverViewRect")
    if (dHeight < 0) {
        // view的部分在垂直方向上滑出屏幕
        if (coverViewRect.top == 0) {
            // 顶部滑出屏幕
            coverViewRect.top += dHeight
        } else {
            // 底部滑出屏幕
            coverViewRect.bottom -= dHeight
        }
    }
    if (dWidth < 0) {
        // view的部分在水平方向上滑出屏幕
        if (coverViewRect.left == 0) {
            // 顶部滑出屏幕
            coverViewRect.left += dWidth
        } else {
            // 底部滑出屏幕
            coverViewRect.right -= dWidth
        }
    }
    return coverViewRect
}

/**
 * 作用：获取view在屏幕上全局的矩形区域的坐标(滑出屏幕或者被遮挡的部分会被补全)
 * @return 返回view屏在幕内或外的全局坐标
 * 注意：1.支持 LTR布局、 RTL布局、TTB布局、BTT布局等布局方向的兼容，Android底层的坐标体系仍然是从左上角原点开始的
 *      2.返回的 rect 不包括 margin
 *      3.方法不支持对 view 内部的 padding、scroll(位移)的处理
 */
fun View.getGlobalRectOnScreen(): Rect {
    /**
     * 获取view在屏幕上的坐标
     * 由于点击的列表中的item后，item有一个点击按压缩小的动画，如果直接使用itemView获取坐标得到的坐标数据itemView静止状态的位置会有几个像素的偏移
     * 已知itemView的父View是没有动画的，所以通过获取到父View在屏幕中的位置，再来反算itemView在屏幕中的位置，以此规避坐标偏移的问题
     */
    val parentLocation = IntArray(2)
    (this.parent as? View)?.getLocationOnScreen(parentLocation)
    Rect().let {
        it.left = parentLocation[0] + this.left
        it.top = parentLocation[1] + this.top
        it.right = it.left + this.width
        it.bottom = it.top + this.height
        return it
    }
}

/**
 * 以View范围的左上角为原点
 * x = 触摸事件的X坐标，y = 触摸事件的Y坐标
 * slop = View的触摸误差阈值
 * 计算逻辑：以下情况认为超出View范围
 * x坐标 < 负的触摸误差阈值
 * y坐标 < 负的触摸误差阈值
 * x坐标 > View宽度+触摸误差阈值
 * y坐标 > View高度+触摸误差阈值
 */
fun View.isOutOfBounds(event: MotionEvent): Boolean {
    val x = event.x.toInt()
    val y = event.y.toInt()
    val slop = ViewConfiguration.get(this.context).scaledWindowTouchSlop
    return (x < -slop) || (y < -slop)
            || (x > (this.width + slop))
            || (y > (this.height + slop))
}

/**
 * 从TextureView截帧 得到Bitmap
 * [TextureView.getBitmap]取到的是未旋转时的界面的Bitmap，所以当我们的TextureView如果有旋转时（如相册大图开始10bit旋转图片）得到的方向和宽高异常
 *
 * 所以一旦TextureView有旋转角度，就需要：
 * 1.求得未旋转时的宽高
 * 2.按照未旋转时的场景去获取Bitmap
 * 3.按照旋转的角度去旋转Bitmap
 *
 * @param width 期望的Bitmap的宽
 * @param height 期望的Bitmap的高
 *
 * @return 当前展示的TextureView的截帧
 */
fun TextureView.extractBitmap(width: Int, height: Int): Bitmap {
    val viewRotation = rotation.toInt()
    // 1.求得未旋转时的宽高
    var noRotateWidth = width
    var noRotateHeight = height
    // 如果TextureView旋转了角度 90 或者 270 ， TextureView中的长宽颠倒了
    if (AppConstants.Degree.isVertical(viewRotation)) {
        noRotateWidth = height
        noRotateHeight = width
    }
    // 2.按照未旋转时的场景去获取Bitmap
    val bmp = getBitmap(noRotateWidth, noRotateHeight)
    // 3.按照旋转的角度去旋转Bitmap
    return BitmapUtils.rotateBitmap(bmp, viewRotation, true)
}

/**
 * 将指定 View 显示在屏幕的内容输出成 bitmap
 */
fun View.getViewBitmap(backgroundColor: Int = 0, radius: Float = 0f): Bitmap {
    val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
    val canvas = Canvas(bitmap)
    if (backgroundColor != 0) {
        val rect = Rect(0, 0, width, height)
        val paint = Paint(Paint.ANTI_ALIAS_FLAG or Paint.DITHER_FLAG).apply {
            color = backgroundColor
        }
        canvas.drawRoundRect(RectF(rect), radius, radius, paint)
    }
    draw(canvas)
    return bitmap
}

/**
 * 将指定 ViewGroup 显示在屏幕的内容输出成 bitmap
 * 注意：因为ViewGroup在被点击的时候，背景有颜色变化的动画，获取到的bitmap的颜色也是变化后的，
 * 不是初始状态的背景颜色，需要拿到初始背景的颜色再获取到所有的子View重新绘制一个
 * @param isNeedDrawParent 是否需要将容器的背景一起绘制
 */
fun View.getViewGroupBitmap(firstBackgroundColor: Int = 0, secondBackgroundColor: Int = 0, isNeedDrawParent: Boolean = false): Bitmap {
    val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
    val canvas = Canvas(bitmap)
    if (secondBackgroundColor != 0) {
        canvas.drawColor(secondBackgroundColor)
    }
    if (firstBackgroundColor != 0) {
        canvas.drawColor(firstBackgroundColor)
    }
    //遍历所有子 View 并按原位置绘制
    (this as? ViewGroup)?.let {
        if (isNeedDrawParent) {
            val backgroundDrawable = this.background
            backgroundDrawable?.let {
                canvas.save()
                canvas.drawDrawable(it, width.toFloat(), height.toFloat())
                canvas.restore()
            }
        }

        for (i in 0 until childCount) {
            val child: View = getChildAt(i)
            // 保存 canvas 状态
            canvas.save()
            // 移动 canvas 到子 View 的位置
            canvas.translate(child.left.toFloat(), child.top.toFloat())
            // 绘制子 View
            child.draw(canvas)
            // 恢复 canvas 状态
            canvas.restore()
        }
    }
    return bitmap
}

/**
 * 获取[rootSurfaceControl]，如果不满足条件则返回null
 */
fun View.getRootSurfaceControlIfPossible(): AttachedSurfaceControl? {
    return if (ApiLevelUtil.isAtLeastAndroidS()) rootSurfaceControl else null
}

/**
 * 更新子视图的外间距
 * @param range 子视图设置间距范围
 */
fun ViewGroup.setChildMargins(
    range: IntRange,
    @Px left: Int = INVALID_PX,
    @Px top: Int = INVALID_PX,
    @Px right: Int = INVALID_PX,
    @Px bottom: Int = INVALID_PX
) {
    for (i in range) {
        getChildAt(i).updateMargin(left, top, right, bottom)
    }
}

/**
 * 对于用户来说可见，比如没有被其他View遮挡，被dialog窗口遮挡
 */
fun View.isViewVisibleToUser(): Boolean {
    if (isShown.not() || hasWindowFocus().not()) {
        return false
    }
    val rect = Rect()
    getGlobalVisibleRect(rect)
    return (rect.width() > 0) && (rect.height() > 0)
}

/**
 * View扩展热区范围
 * @param expandValue 需要扩展的值
 */
fun View.expandHitRect(expandValue: Int) {
    val originalBounds = Rect()
    this.getHitRect(originalBounds)
    val parentView = this.parent as View
    val expandedBounds = Rect(originalBounds)
    //扩展这个矩形以覆盖更多区域
    expandedBounds.top -= expandValue
    expandedBounds.left -= expandValue
    expandedBounds.bottom += expandValue
    expandedBounds.right += expandValue
    GLog.d(TAG, LogFlag.DL) { "[expandHitRect] originalBounds: $originalBounds, expandedBounds: $expandedBounds" }
    //创建 TouchDelegate，将其应用到父视图
    val touchOliveDelegate = TouchDelegate(expandedBounds, this)
    parentView.post {
        parentView.touchDelegate = touchOliveDelegate
    }
}

/**
 * 测量view的大小
 * @return size(width,height)
 */
fun View.measureSize(): Size {
    this.measure(
        View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
        View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
    )
    return Size(this.measuredWidth, this.measuredHeight)
}

fun View.isHighlight(): Boolean {
    return getTag(R.id.tag_is_highlight) == true
}

/**
 * 扩展View方法：判断坐标是否在View区域内
 * @param x X轴坐标
 * @param y Y轴坐标
 */
fun View.containsPoint(x: Float, y: Float): Boolean {
    return (x >= left) && (x <= right) && (y >= top) && (y <= bottom)
}

/**
 * 更新视图的宽高
 */
fun View.updateSize(
    @Px width: Int = INVALID_PX,
    @Px height: Int = INVALID_PX
) {
    var changed = false
    val lp = layoutParams as? ViewGroup.MarginLayoutParams ?: return
    if ((width != INVALID_PX) && (width != lp.width)) {
        lp.width = width
        changed = true
    }
    if ((height != INVALID_PX) && (height != lp.height)) {
        lp.height = height
        changed = true
    }
    if (changed) {
        layoutParams = lp
    }
}

/**
 * view是否可以滚动
 * @param orientation 方向:判断该方向上是否可滚动 @see RecyclerView.HORIZONTAL RecyclerView.VERTICAL
 * @param delta 负数:表示垂直向上滚或水平向左滚 正数:表示垂直向下滚或水平向右滚
 */
fun View.canViewScroll(orientation: Int, delta: Float): Boolean {
    val direction = -delta.toInt()
    return when (orientation) {
        RecyclerView.HORIZONTAL -> canScrollHorizontally(direction)
        RecyclerView.VERTICAL -> canScrollVertically(direction)
        else -> {
            GLog.e(TAG, LogFlag.DL) { "canViewScroll: unknown orientation=$orientation" }
            canScrollHorizontally(direction) || canScrollVertically(direction)
        }
    }
}

/**
 * View是否可见（visible和alpha判断]）
 */
fun View.isVisibleAndOpaque(): Boolean {
    return isVisible && alpha == 1.0f
}