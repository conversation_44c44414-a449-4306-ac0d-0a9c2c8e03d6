/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DeferredExt.kt
 ** Description:
 ** Deferred的方法扩展类
 ** Version: 1.0
 ** Date: 2022/06/10
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/06/10    1.0                create
 ******************************************************************************/

package com.oplus.gallery.foundation.util.ext

import kotlinx.coroutines.Deferred
import kotlinx.coroutines.runBlocking

/**
 * Marked by zhangwenming. 该类最终是放在libtaskscheduling中。但因taskscheduling还未迁移，目前还不能直接放到该lib中
 * 否则会出现与standard_lib循环依赖。包名暂时设定为最终值，待libtaskscheduling一起迁移。
 */

fun <T> Deferred<T>.getOnBlocking(): T = runBlocking {
    await()
}