/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - LocaleUtils.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2020/8/12
 ** Author: <PERSON><PERSON><PERSON>@Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 ** Dajian@Apps.Gallery3D      2020/8/12     1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/

package com.oplus.gallery.foundation.util.systemcore

import android.content.Context
import com.oplus.gallery.foundation.util.debug.GLog
import java.text.NumberFormat
import java.util.Locale
import java.util.regex.Pattern

object LocaleUtils {

    private const val CONNECTOR = "-"

    /**
     * 中文 大陆
     */
    const val LANGUAGE_ZH = "zh"
    /**
     * 简体中文 大陆
     */
    const val LANGUAGE_ZH_CN = "zh-CN"

    /**
     * 简体中文 大陆，这里的命名使用文字变体限制，意义同 LANGUAGE_ZH_CN
     */
    const val LANGUAGE_ZH_HANS = "zh-Hans"

    /**
     * 简体中文 大陆，这里的命名使用文字变体限制，意义同 LANGUAGE_ZH_CN
     */
    const val LANGUAGE_ZH_HANS_CN = "zh-Hans-CN"

    /**
     * 繁体中文 大陆，这里的命名使用文字变体限制
     */
    const val SCRIPT_HANT = "Hant"

    /**
     * 简体中文 马来西亚
     */
    const val LANGUAGE_ZH_MY = "zh-MY"

    /**
     * 简体中文 新加坡
     */
    const val LANGUAGE_ZH_SG = "zh-SG"

    /**
     * 繁体中文 大陆
     */
    const val LANGUAGE_ZH_HANT = "zh-Hant"

    /**
     * 繁体中文 台湾
     */
    const val LANGUAGE_ZH_TW = "zh-TW"

    /**
     * 繁体中文 台湾
     */
    const val COUNTRY_TW = "TW"

    /**
     * 繁体中文 香港
     */
    const val COUNTRY_HK = "HK"

    /**
     * 繁体中文 香港
     */
    const val LANGUAGE_ZH_HK = "zh-HK"

    /**
     * 繁体中文 澳门
     */
    const val LANGUAGE_ZH_MO = "zh-MO"

    /**
     * 英语
     */
    const val LANGUAGE_EN = "en"

    /**
     * 英语 en-us
     */
    const val LANGUAGE_EN_US = "en-US"

    /**
     * ini文件里面的默认语言（兜底）
     */
    const val LANGUAGE_DEFAULT = "default"

    /**
     * 菲律宾语
     */
    private const val LANGUAGE_FIL = "fil"

    /**
     * 尼泊尔
     */
    private const val LANGUAGE_NE = "ne"

    /**
     * 印度语
     */
    private const val LANGUAGE_IN = "in"

    /**
     * 越南语
     */
    private const val LANGUAGE_VI = "vi"

    /**
     * 俄语
     */
    private const val LANGUAGE_RU = "ru"

    /**
     * 日语
     */
    private const val LANGUAGE_JA = "ja"

    /**
     * "[\u4e00-\u9fa5]"是中文的编码集合，包含简体繁体
     */
    private val pattenDecodingCN = Pattern.compile("[\u4e00-\u9fa5]")

    /**
     * 国家码映射表
     *
     * ISO标准：https://github.com/qwd/LocationList/blob/master/iso3166.csv
     */
    @Suppress("LongMethod", "MagicNumber")
    private val countrys = arrayListOf(
        Country(156, "中国", "CN")
    )

    /**
     * 城市码映射表（一线城市和省会城市）
     *
     * ISO标准：https://github.com/qwd/LocationList/blob/master/China-City-List-latest.csv
     */
    @Suppress("LongMethod", "MagicNumber")
    private val citys = arrayListOf(
        Locality("CN", Pair(101010100, 101011700), "北京市", "北京市"),
        Locality("CN", Pair(101020100, 101021700), "上海市", "上海市"),
        Locality("CN", Pair(101030100, 101031700), "天津市", "天津市"),
        Locality("CN", Pair(101040100, 101044100), "重庆市", "重庆市"),
        Locality("CN", Pair(101050101, 101050119), "黑龙江省", "哈尔滨市"),
        Locality("CN", Pair(101060101, 101060111), "吉林省", "长春市"),
        Locality("CN", Pair(101070101, 101070114), "辽宁省", "沈阳市"),
        Locality("CN", Pair(101080101, 101080110), "内蒙古自治区", "呼和浩特市"),
        Locality("CN", Pair(101090101, 101090123), "河北省", "石家庄市"),
        Locality("CN", Pair(101100101, 101100111), "山西省", "太原市"),
        Locality("CN", Pair(101110101, 101110114), "陕西省", "西安市"),
        Locality("CN", Pair(101120101, 101120111), "山东省", "济南市"),
        Locality("CN", Pair(101130101, 101130113), "新疆维吾尔自治区", "乌鲁木齐市"),
        Locality("CN", Pair(101140101, 101140109), "西藏自治区", "拉萨市"),
        Locality("CN", Pair(101150101, 101150108), "青海省", "西宁市"),
        Locality("CN", Pair(101160101, 101160109), "甘肃省", "兰州市"),
        Locality("CN", Pair(101170101, 101170107), "宁夏回族自治区", "银川市"),
        Locality("CN", Pair(101180101, 101180113), "河南省", "郑州市"),
        Locality("CN", Pair(101190101, 101190113), "江苏省", "南京市"),
        Locality("CN", Pair(101200101, 101200114), "湖北省", "武汉市"),
        Locality("CN", Pair(101210101, 101210115), "浙江省", "杭州市"),
        Locality("CN", Pair(101220101, 101220110), "安徽省", "合肥市"),
        Locality("CN", Pair(101230101, 101230114), "福建省", "福州市"),
        Locality("CN", Pair(101240101, 101240110), "江西省", "南昌市"),
        Locality("CN", Pair(101250101, 101250111), "湖南省", "长沙市"),
        Locality("CN", Pair(101260101, 101260112), "贵州省", "贵阳市"),
        Locality("CN", Pair(101270101, 101270121), "四川省", "成都市"),
        Locality("CN", Pair(101280101, 101280112), "广东省", "广州市"),
        // 深圳市属于一线城市，也添加上
        Locality("CN", Pair(101280601, 101280610), "广东省", "深圳市"),
        Locality("CN", Pair(101290101, 101290116), "云南省", "昆明市"),
        Locality("CN", Pair(101300101, 101300113), "广西壮族自治区", "南宁市"),
        Locality("CN", Pair(101310101, 101310105), "海南省", "海口市"),
        // 香港、澳门、台湾对应的城市码非数字，此处暂按原递增规则，算出对应城市码
        Locality("CN", Pair(101320101, 101320120), "香港特别行政区", "香港特别行政区"),
        Locality("CN", Pair(101330101, 101330111), "澳门特别行政区", "***"),
        Locality("CN", Pair(101340101, 101340139), "台湾省", "高雄市")
    )

    /**
     * 获取国家Id
     *
     * @param countryCode 国家码
     */
    @JvmStatic
    fun getCountryIdByCode(countryCode: String): Int? {
        return countrys.find {
            it.countryCode == countryCode
        }?.id
    }

    /**
     * 获取国家信息
     *
     * @param id 国家ID
     */
    @JvmStatic
    fun getCountryById(id: Int): Country? {
        return countrys.find {
            it.id == id
        }
    }

    /**
     * 获取城市Id
     *
     * @param countryCode 所属国家码
     * @param localityName 城市名称
     */
    @JvmStatic
    fun getLocalityIdByName(countryCode: String, localityName: String): Int? {
        // 城市对应多个分区ID，取首个分区ID视为城市ID
        return citys.find {
            (it.countryCode == countryCode) && (it.locality == localityName)
        }?.idPart?.first
    }

    /**
     * 获取分区所属城市信息
     *
     * @param id 地区对应ID
     */
    @JvmStatic
    fun getLocalityById(id: Int): Locality? {
        return citys.find {
            // 地区ID在区间内时
            (id >= it.idPart.first) && (id <= it.idPart.second)
        }
    }

    /**
     * 是否为简体中文
     */
    @JvmStatic
    fun isSimplifiedChinese(context: Context): Boolean {
        val languageTag = getLocale(context).toLanguageTag()
        return ((languageTag.contains(LANGUAGE_ZH_HANS))
                || (languageTag.equals(LANGUAGE_ZH_CN, ignoreCase = true))
                || (languageTag.equals(LANGUAGE_ZH_SG, ignoreCase = true))
                || (languageTag.equals(LANGUAGE_ZH_MY, ignoreCase = true)))
    }

    /**
     * 是否为繁体中文
     */
    @JvmStatic
    fun isTraditionalChinese(context: Context): Boolean {
        val languageTag = getLocale(context).toLanguageTag()
        return ((languageTag.contains(LANGUAGE_ZH_HANT))
                || (languageTag.equals(LANGUAGE_ZH_HK, ignoreCase = true))
                || (languageTag.equals(LANGUAGE_ZH_TW, ignoreCase = true))
                || (languageTag.equals(LANGUAGE_ZH_MO, ignoreCase = true)))
    }

    /**
     * 是否繁体中文-台湾
     */
    @JvmStatic
    fun isChinaTwTraditionalChinese(context: Context): Boolean {
        val locale = getLocale(context)
        val language = locale.language
        val region = locale.country
        return LANGUAGE_ZH_TW.equals("$language$CONNECTOR$region", ignoreCase = true)
    }

    /**
     * 是否英语-en-US
     */
    @JvmStatic
    fun isEnUs(context: Context): Boolean {
        val locale = getLocale(context)
        val language = locale.language
        val region = locale.country
        return LANGUAGE_EN_US.equals("$language$CONNECTOR$region", ignoreCase = true)
    }

    /**
     * 是否为中文(简体中文、繁体中文)
     */
    @JvmStatic
    fun isChinese(context: Context): Boolean {
        return (isSimplifiedChinese(context) || isTraditionalChinese(context))
    }

    /**
     * 是否为简体中文-大陆
     */
    @JvmStatic
    fun isChinaMainland(context: Context): Boolean {
        val languageTag = getLocale(context).toLanguageTag()
        return ((languageTag.contains(LANGUAGE_ZH_HANS))
                || (languageTag.equals(LANGUAGE_ZH_CN, ignoreCase = true)))
    }

    /**
     * 是否为英语
     */
    @JvmStatic
    fun isEnglish(localeLanguage: String): Boolean {
        return localeLanguage.equals(LANGUAGE_EN, ignoreCase = true)
    }

    /**
     * 是否为英语
     */
    @JvmStatic
    fun isEnglish(context: Context): Boolean {
        return (getLocale(context).language).equals(LANGUAGE_EN, ignoreCase = true)
    }

    /**
     * 是否为菲律宾语
     */
    @JvmStatic
    fun isPhilippines(context: Context): Boolean {
        return (getLocale(context).toString()).startsWith(LANGUAGE_FIL, ignoreCase = true)
    }

    /**
     * 是否为尼泊尔语
     */
    @JvmStatic
    fun isNepal(context: Context): Boolean {
        return (getLocale(context).toString()).startsWith(LANGUAGE_NE, ignoreCase = true)
    }

    /**
     * 是否为日语
     */
    @JvmStatic
    fun isJapan(context: Context): Boolean {
        return (getLocale(context).toString()).startsWith(LANGUAGE_JA, ignoreCase = true)
    }

    /**
     * 是否为印度语
     */
    @JvmStatic
    fun isIndonesia(context: Context): Boolean {
        return (getLocale(context).toString()).startsWith(LANGUAGE_IN, ignoreCase = true)
    }

    /**
     * 是否为越南语
     */
    @JvmStatic
    fun isVietnam(context: Context): Boolean {
        return (getLocale(context).toString()).startsWith(LANGUAGE_VI, ignoreCase = true)
    }

    /**
     * 是否为俄语
     */
    @JvmStatic
    fun isRussia(context: Context): Boolean {
        return (getLocale(context).toString()).startsWith(LANGUAGE_RU, ignoreCase = true)
    }

    /**
     * 当前区域
     */
    @JvmStatic
    fun getLocale(context: Context): Locale = context.resources.configuration.locales[0]

    /**
     * 格式化成 当前默认区域设置的通用数字格式
     */
    @JvmStatic
    fun getLocaleFormattedNumber(number: Number): String = NumberFormat.getNumberInstance().format(number)

    /**
     * 将 当前默认区域设置的通用数字格式转换成普通的Number
     */
    @JvmStatic
    fun parseLocaleFormattedNumber(number: String): Number {
        return runCatching {
            if (number.isNotBlank()) {
                NumberFormat.getNumberInstance().parse(number) ?: 0
            } else {
                0
            }
        }.onFailure {
            GLog.e("parseLocaleFormattedNumber error", it)
        }.getOrDefault(0)
    }

    /**
     * pattenDecodingCN = Pattern.compile("[\u4e00-\u9fa5]")是中文的编码集合，包含简体繁体
     */
    @JvmStatic
    fun checkAllChinese(str: String): Boolean {
        return pattenDecodingCN.matcher(str).find()
    }

    /**
     * 格式化成当前默认区域设置的百分比格式
     * @param number 百分比数字，如0.1
     */
    @JvmStatic
    fun getLocaleFormattedPercent(number: Number): String = NumberFormat.getPercentInstance().format(number)
}

/**
 * 国家信息
 *
 * @param id 国家ID
 * @param countryName 国家名称
 * @param countryCode 国家码
 */
data class Country(
    val id: Int,
    val countryName: String,
    val countryCode: String
)

/**
 * 城市信息
 *
 * @param countryCode 国家码
 * @param idPart 城市所包含的地区ID段（分别为地区中的最小ID值和最大ID值）
 * @param adminArea 所属管理区域（例：湖北省）
 * @param locality 城市名称（例：武汉市）
 */
data class Locality(
    val countryCode: String,
    val idPart: Pair<Int, Int>,
    val adminArea: String,
    val locality: String
)