/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File        :  - DisplayUtils.java
 ** Description : The utils for display density.
 ** Version     : 1.0
 ** Date        : 2018/12/4
 ** Author      : Junmin.Luo@Apps.Gallery3D
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                      <data>    <version>  <desc>
 **  Junmin.Luo@Apps.Gallery3D     2018/12/4    1.0      build this module
 ***********************************************************************/
package com.oplus.gallery.foundation.util.display;

import android.app.Activity;
import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Point;
import android.graphics.Rect;
import android.hardware.display.DisplayManager;
import android.view.Display;

import com.coui.appcompat.darkmode.COUIDarkModeHelper;
import com.coui.appcompat.darkmode.COUIDarkModeUtil;
import androidx.annotation.ColorInt;

public final class DisplayUtils {
    private static final int MAX_COLOR = 255;
    private static final int ALPHA_POSITION = 24;
    private static final int BASE_COLOR = 16777215;
    private DisplayUtils() {
        //Do nothing
    }


    public static boolean isPortrait(Activity activity) {
        if (activity == null) {
            return true;
        }
        return activity.getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT;
    }

    public static int getLightColorWithNightMode(Context context, @ColorInt int color) {
        if (context == null) {
            return color;
        }
        return COUIDarkModeUtil.isNightMode(context) ? COUIDarkModeHelper.getInstance().makeLight(color) : color;
    }

    public static int getDarkColorWithNightMode(Context context, @ColorInt int color) {
        if (context == null) {
            return color;
        }
        return COUIDarkModeUtil.isNightMode(context) ? COUIDarkModeHelper.getInstance().makeDark(color) : color;
    }

    public static int getColorWithAlpha(float alpha, int color) {
        int a = Math.min(MAX_COLOR, Math.max(0, (int) (alpha * MAX_COLOR))) << ALPHA_POSITION;
        int rgb = BASE_COLOR & color;
        return a + rgb;
    }

    public static Point getWindowSize(Activity activity) {
        Point pointSize = new Point();
        Rect bounds = activity.getWindowManager().getCurrentWindowMetrics().getBounds();
        pointSize.x = bounds.width();
        pointSize.y = bounds.height();
        return pointSize;
    }

    /**
     * 获取屏幕是否支持HDR显示
     *
     * @param context 上下文
     * @return true：屏幕支持HDR，false：屏幕不支持或判断流程异常
     */
    public static boolean isDisplaySupportHDR(Context context) {
        if (context == null) {
            return false;
        }
        DisplayManager displayManager = (DisplayManager) context.getSystemService(Context.DISPLAY_SERVICE);
        if (displayManager == null) {
            return false;
        }
        Display[] displays = displayManager.getDisplays();
        if (displays == null) {
            return false;
        }
        for (Display display : displays) {
            Display.HdrCapabilities hdrCapabilities = display.getHdrCapabilities();
            if (hdrCapabilities != null && hdrCapabilities.getSupportedHdrTypes().length > 0) {
                return true;
            }
        }
        return false;
    }
}