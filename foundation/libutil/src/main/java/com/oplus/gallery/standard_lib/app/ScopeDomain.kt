/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ScopeDomain.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/09/22
 ** Author: hucanh<PERSON>@Apps.Gallery
 ** TAG: OPLUS_ARCH_COROUTINE
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** h<PERSON><PERSON><PERSON>@Apps.Gallery		2020/09/22		1.0		init
 *********************************************************************************/

package com.oplus.gallery.standard_lib.app

import android.os.Handler
import android.os.Looper
import android.os.Process
import android.util.Log
import com.oplus.gallery.foundation.util.BuildConfig
import com.oplus.gallery.foundation.util.thread.PriorityThreadFactory
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineName
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.asCoroutineDispatcher
import java.util.concurrent.Executors
import kotlin.coroutines.CoroutineContext

private const val TAG = "ScopeDomain"
private const val DISPATCHER_NAME_TRACK = "track"
private const val DISPATCHER_NAME_RESCAN = "rescan"
private const val DISPATCHER_NAME_SINGLE_UN_BUSY = "singleUnBusy"
private const val DISPATCHER_NAME_SINGLE_LONG_TIME = "singleLongTime"
private const val DISPATCHER_NAME_CSHOT = "cShot"
private const val DISPATCHER_EDITING_SESSION = "editingSession"
private const val DISPATCHER_PIPELINE_SESSION = "pipelineSession"
private const val DISPATCHER_EDITING_LONG_TIME = "editingLongTime"
private const val DISPATCHER_NAME_SYNC_ALBUMSET = "syncAlbumSetTask"
private const val DISPATCHER_NAME_SINGLE_FOREGROUND = "singleForegroundTask"
private const val DISPATCHER_NAME_SHARE_PAGE_PRIORITY_FOREGROUND = "sharePagePriorityTask"
private const val SHARE_LOADER_THREAD_SIZE = 4

/**
 * 用于美摄编辑的单线程执行器
 */
private const val DISPATCHER_MEICAM_EDIT = "meicamEdit"

/**
 * 取代GlobalScope作为全局的作用域，默认使用的Dispatcher为CPU
 */
object AppScope : CoroutineScope {
    private const val TAG = "AppScope"

    override val coroutineContext: CoroutineContext
        get() = CoroutineName(TAG) + Dispatchers.CPU + LoggingExceptionHandler + SupervisorJob()
}

/**
 * Model层中的作用域，默认的Dispatcher为IO，生命周期随Model存在而存在，每个Model有一个对应的ModelScope
 */
class ModelScope : CoroutineScope {
    override val coroutineContext: CoroutineContext
        get() = CoroutineName(TAG) + Dispatchers.IO + LoggingExceptionHandler + SupervisorJob()

    companion object {
        private const val TAG = "ModelScope"
    }
}

/**
 * ViewModel层中的作用域，默认的Dispatcher为CPU，生命周期随ViewModel的存在而存在，每个ViewModel有一个对应的ViewModelScope
 */
class ViewModelScope : CoroutineScope {
    override val coroutineContext: CoroutineContext
        get() = if (BuildConfig.DEBUG) {
            // debug模式抛出异常，暴露问题
            CoroutineName(TAG) + Dispatchers.CPU + SupervisorJob()
        } else {
            CoroutineName(TAG) + Dispatchers.CPU + SupervisorJob() + LoggingExceptionHandler
        }

    companion object {
        private const val TAG = "ViewModelScope"
    }
}

/**
 * 为埋点建立的一个作用域，默认Dispatcher为TRACK
 */
object TrackScope : CoroutineScope {
    private const val TAG = "TrackScope"

    override val coroutineContext: CoroutineContext
        get() = CoroutineName(TAG) + Dispatchers.TRACK + LoggingExceptionHandler + SupervisorJob()
}

/**
 * 为媒体库建立的一个作用域，默认Dispatcher为RESCAN
 */
object RescanScope : CoroutineScope {
    private const val TAG = "RescanScope"

    override val coroutineContext: CoroutineContext
        get() = CoroutineName(TAG) + Dispatchers.RESCAN + LoggingExceptionHandler + SupervisorJob()
}

/**
 * 异常处理的Handler句柄，当不适用此异常处理句柄，而协程中崩溃时，并不会捕获异常，可能导致崩溃，故必须使用
 */
val LoggingExceptionHandler = CoroutineExceptionHandler { _, t ->
    // 协程中出现异常需要打印堆栈，否则出现问题难以分析，特别是低概率问题
    Log.e(TAG, "ScopeDomain: ", t)
}

/**
 * UI线程的执行器，取代[Dispatchers.Main]，使用时，当使用[Dispatchers.UI.immediate]时，
 * 会判断是否在主线程，当在主线程时，则立即执行，不会发送到一个Handler
 */
val Dispatchers.UI by lazy { Dispatchers.Main }

/**
 * CPU密集型任务的执行器，取代[Dispatchers.Default]，默认线程池数量为cpu核数
 */
val Dispatchers.CPU by lazy { Dispatchers.Default }

/**
 * 埋点的执行器，其启动时阻塞，当调用了Tracker.config后，作用域放开
 */
val Dispatchers.TRACK by lazy {
    Executors.newSingleThreadExecutor(
        TrackThreadFactory(
            DISPATCHER_NAME_TRACK,
            Process.THREAD_PRIORITY_LOWEST
        )
    ).asCoroutineDispatcher()
}

/**
 * 不繁忙的单线程执行器，使用此单线程尽量不要处理大量任务、耗时任务
 */
val Dispatchers.SINGLE_UN_BUSY by lazy {
    Executors.newSingleThreadExecutor(PriorityThreadFactory(DISPATCHER_NAME_SINGLE_UN_BUSY, Process.THREAD_PRIORITY_BACKGROUND))
        .asCoroutineDispatcher()
}

/**
 * 仅前台页面可以使用的单线程执行器，一般处理和显示相关任务，页面退后台时需要清空任务
 */
val Dispatchers.SINGLE_FOREGROUND by lazy {
    Executors.newSingleThreadExecutor(SingleLongTimeThreadFactory(DISPATCHER_NAME_SINGLE_FOREGROUND, Process.THREAD_PRIORITY_FOREGROUND))
        .asCoroutineDispatcher()
}

/**
 * 分享业务独享线程池，分享数据执行优化
 */
val Dispatchers.SHARE_PAGE_PRIORITY_FOREGROUND by lazy {
    Executors.newFixedThreadPool(
        SHARE_LOADER_THREAD_SIZE,
        PriorityThreadFactory(
            DISPATCHER_NAME_SHARE_PAGE_PRIORITY_FOREGROUND,
            Process.THREAD_PRIORITY_BACKGROUND
        )
    ).asCoroutineDispatcher()
}


/**
 * 不繁忙的单线程执行器，适合长时间执行的逻辑，不要求及时的计算出结果
 */
val Dispatchers.SINGLE_LONG_TIME by lazy {
    Executors.newSingleThreadExecutor(
        SingleLongTimeThreadFactory(
            DISPATCHER_NAME_SINGLE_LONG_TIME,
            Process.THREAD_PRIORITY_BACKGROUND
        )
    ).asCoroutineDispatcher()
}

/**
 * 媒体库重扫的单线程执行器，使用此单线程可能会持续占用分钟甚至小时级时间
 */
val Dispatchers.RESCAN by lazy {
    Executors.newSingleThreadExecutor(
        RescanThreadFactory(
            DISPATCHER_NAME_RESCAN,
            Process.THREAD_PRIORITY_BACKGROUND
        )
    ).asCoroutineDispatcher()
}

/**
 * 仅用于同步图集列表数据到图集表
 */
val Dispatchers.SyncAlbumSetTask by lazy {
    Executors.newSingleThreadExecutor(
        PriorityThreadFactory(
            DISPATCHER_NAME_SYNC_ALBUMSET,
            Process.THREAD_PRIORITY_BACKGROUND
        )
    ).asCoroutineDispatcher()
}

/**
 * 图片编辑专用
 */
val Dispatchers.EditingSession by lazy {
    Executors.newSingleThreadExecutor(
        PriorityThreadFactory(
            DISPATCHER_EDITING_SESSION,
            Process.THREAD_PRIORITY_BACKGROUND
        )
    ).asCoroutineDispatcher()
}

/**
 * 编辑业务专用
 * 业务有耗时、不急的任务统一用这个，不要切IO了
 */
val Dispatchers.EditingLongTime by lazy {
    Executors.newSingleThreadExecutor(
        PriorityThreadFactory(
            DISPATCHER_EDITING_LONG_TIME,
            Process.THREAD_PRIORITY_BACKGROUND
        )
    ).asCoroutineDispatcher()
}

/**
 * 图片编辑管线专用
 */
val Dispatchers.PipelineSession by lazy {
    Executors.newSingleThreadExecutor(
        PriorityThreadFactory(
            DISPATCHER_PIPELINE_SESSION,
            Process.THREAD_PRIORITY_BACKGROUND
        )
    ).asCoroutineDispatcher()
}

/**
 * 仅用于连拍使用的单线程执行器
 */
val Dispatchers.CShot by lazy {
    Executors.newSingleThreadExecutor(
        SingleLongTimeThreadFactory(
            DISPATCHER_NAME_CSHOT,
            Process.THREAD_PRIORITY_BACKGROUND
        )
    ).asCoroutineDispatcher()
}

/**
 * 仅用于美摄编辑使用的单线程执行器
 */
val Dispatchers.MeicamEdit by lazy {
    Executors.newSingleThreadExecutor(
        SingleLongTimeThreadFactory(
            DISPATCHER_MEICAM_EDIT,
            Process.THREAD_PRIORITY_BACKGROUND
        )
    ).asCoroutineDispatcher()
}

/**
 * Dispatchers.Main.immediate只能在主线程中不切换线程调用，在子线程中调用会往主线程队列末端抛消息
 *
 * 当前Dispatchers可以在子线程中往主线程队列最前面抛消息，可以在下一个主线程消息中马上被执行
 */
val Dispatchers.AtFrontOfMainQueueDispatcher by lazy {
    object : CoroutineDispatcher() {
        private val handler = Handler(Looper.getMainLooper())
        override fun dispatch(context: CoroutineContext, block: Runnable) {
            handler.postAtFrontOfQueue(block)
        }
    }
}