/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : ConcurrentExtraMap.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/5/12
 ** Author      : houdong<PERSON>@Apps.Gallery
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** houdong<PERSON>@Apps.Gallery           2023/5/12      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.foundation.util.collections

import java.util.concurrent.ConcurrentHashMap
import kotlin.IntArray

/**
 * 线程安全的hashmap，封装了一些功能，方便开发
 */
class ConcurrentExtraMap : ConcurrentHashMap<String, Any> {
    constructor(initialCapacity: Int, loadFactor: Float) : super(initialCapacity, loadFactor)
    constructor(initialCapacity: Int) : super(initialCapacity)
    constructor() : super()
    constructor(m: MutableMap<out String, out Any>) : super(m)

    fun putBoolean(key: String, value: Boolean) {
        put(key, value)
    }

    fun putBooleanArray(key: String, value: BooleanArray) {
        put(key, value)
    }

    fun putShort(key: String, value: Short) {
        put(key, value)
    }

    fun putShortArray(key: String, value: ShortArray) {
        put(key, value)
    }

    fun putInt(key: String, value: Int) {
        put(key, value)
    }

    fun putIntArray(key: String, value: IntArray) {
        put(key, value)
    }

    fun putLong(key: String, value: Long) {
        put(key, value)
    }

    fun putLongArray(key: String, value: LongArray) {
        put(key, value)
    }

    fun putFloat(key: String, value: Float) {
        put(key, value)
    }

    fun putFloatArray(key: String, value: FloatArray) {
        put(key, value)
    }

    fun putDouble(key: String, value: Double) {
        put(key, value)
    }

    fun putDoubleArray(key: String, value: DoubleArray) {
        put(key, value)
    }

    fun putString(key: String, value: String) {
        put(key, value)
    }

    fun putCharSequence(key: String, value: CharSequence) {
        put(key, value)
    }

    fun getBoolean(key: String?, defaultValue: Boolean = false): Boolean = (get(key) as? Boolean) ?: defaultValue
    fun getBooleanArray(key: String?, defaultValue: BooleanArray): BooleanArray = (get(key) as? BooleanArray) ?: defaultValue

    fun getShort(key: String?, defaultValue: Short = 0): Short = (get(key) as? Short) ?: defaultValue
    fun getShortArray(key: String?, defaultValue: ShortArray): ShortArray = (get(key) as? ShortArray) ?: defaultValue

    fun getInt(key: String?, defaultValue: Int = 0): Int = (get(key) as? Int) ?: defaultValue
    fun getIntArray(key: String?, defaultValue: IntArray): IntArray = (get(key) as? IntArray) ?: defaultValue

    fun getLong(key: String?, defaultValue: Long = 0): Long = (get(key) as? Long) ?: defaultValue
    fun getLongArray(key: String?, defaultValue: LongArray): LongArray = (get(key) as? LongArray) ?: defaultValue

    fun getFloat(key: String?, defaultValue: Float = 0.0F): Float = (get(key) as? Float) ?: defaultValue
    fun getFloatArray(key: String?, defaultValue: FloatArray): FloatArray = (get(key) as? FloatArray) ?: defaultValue

    fun getDouble(key: String?, defaultValue: Double = 0.0): Double = (get(key) as? Double) ?: defaultValue
    fun getDoubleArray(key: String?, defaultValue: DoubleArray): DoubleArray = (get(key) as? DoubleArray) ?: defaultValue

    fun getString(key: String?, defaultValue: String? = null): String? = (get(key) as? String) ?: defaultValue
    fun getCharSequence(key: String?, defaultValue: CharSequence? = null): CharSequence? = (get(key) as? CharSequence) ?: defaultValue
}