/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : MathUtil.kt
 ** Description : Common Math utility
 ** Version     : 1.0
 ** Date        : 2020/03/23
 ** Author      : <PERSON><PERSON>.Shu@Apps.Gallery3D
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON>.Shu@Apps.Gallery3D             2020/03/23  1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.foundation.util.math

import android.graphics.Rect
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.standard_lib.app.AppConstants.Number.NUMBER_1
import com.oplus.gallery.standard_lib.app.AppConstants.Number.NUMBER_2
import com.oplus.gallery.standard_lib.app.AppConstants.Number.NUMBER_31
import com.oplus.gallery.standard_lib.app.AppConstants.Number.NUMBER_4
import java.math.RoundingMode
import kotlin.math.abs
import kotlin.math.roundToLong

/**
 * 基础数学工具类
 */
object MathUtil {

    /**
     * **自然对数的底数**
     * 其值为2.7182818284590452354
     */
    const val E = 2.7182818284590452354

    /**
     * **圆周率π**
     * 其值为3.14159265358979323846
     */
    const val PI = 3.14159265358979323846

    /**
     * **2 * 圆周率π**
     */
    const val PI_2 = 2.0 * PI

    /**
     * **1/2 * 圆周率π**
     */
    const val PI_1_2 = 0.5 * PI

    /**
     * **1/4 * 圆周率π**
     */
    const val PI_1_4 = 0.25 * PI

    /**
     * **180°**
     */
    const val ANGLE_180 = 180

    /**
     * float转int时的精度因子
     */
    private const val FLOAT_TO_INT_FACTOR = 1E-3f

    /**
     * equalWithinTolerance方法的默认Tolerance值，当判断两小数是否相等时，若两者差值低于该值，则认为两者相等
     */
    private const val DEFAULT_EQUAL_TOLERANCE = 0.0000001f

    private const val TAG = "MathUtil"

    private const val ALIGNED_STRIDE_AND_THREE = 3

    private const val ALIGNED_STRIDE_MULTIPLE_FOUR = 4

    private const val ALIGNED_STRIDE_MULTIPLE_ZERO = 0

    /**
     * **单边钳位，如果src小于min，则返回min**
     *
     * @param src 需要被钳位的数值
     * @param min 钳位范围的最小值
     * @return 如果src小于等于min，则返回min，否则返回src原值
     */
    @JvmStatic
    fun <T : Comparable<*>> clampMin(src: T, min: T): T = when {
        ((src is Float) && src.isNaN()) -> src
        ((src is Double) && src.isNaN()) -> src
        (compareValues(src, min) < 0) -> min
        else -> src
    }

    /**
     * **单边钳位，如果src大于max，则返回max**
     *
     * @param src 需要被钳位的数值
     * @param max 钳位范围的最大值
     * @return 如果src大于等于max，则返回max，否则返回src原值
     */
    @JvmStatic
    fun <T : Comparable<*>> clampMax(src: T, max: T): T = when {
        ((src is Float) && src.isNaN()) -> src
        ((src is Double) && src.isNaN()) -> src
        (compareValues(src, max) > 0) -> max
        else -> src
    }

    /**
     * **钳位方法，把src钳位在`[`min, max`]`的范围中。**
     *
     * 适用于[Byte]、[Int]、[Long]、[Float]、[Double]等类型的钳位，同时也适用于能够被默认[Comparator]比较的
     * 引用类型。如果需要比较需要使用特殊方法才能对比的对象，使用
     * *`fun <T : Comparable<*>> clamp(src: T, min: T, max: T, comparator: Comparator<T>): T`*
     *
     * @param src 需要被钳位的数值
     * @param min 钳位范围的最小值
     * @param max 钳位范围的最大值
     * @return 如果src小于等于min，则返回min，如果src大于等于max，则返回max，否则返回src原值
     */
    @JvmStatic
    fun <T : Comparable<*>> clamp(src: T, min: T, max: T): T = when {
        ((src is Float) && src.isNaN()) -> src
        ((src is Double) && src.isNaN()) -> src
        (compareValues(src, min) < 0) -> min
        (compareValues(src, max) > 0) -> max
        else -> src
    }

    /**
     * **钳位方法，把src钳位在`[`min, max`]`的范围中。**
     *
     * 用于比较需要使用特殊方法才能对比的对象。
     *
     * @param src 需要被钳位的数值
     * @param min 钳位范围的最小值
     * @param max 钳位范围的最大值
     * @param comparator 比较器
     * @return 如果src小于等于min，则返回min，如果src大于等于max，则返回max，否则返回src原值
     */
    @JvmStatic
    fun <T : Comparable<*>> clamp(src: T, min: T, max: T, comparator: Comparator<T>): T = when {
        ((src is Float) && src.isNaN()) -> src
        ((src is Double) && src.isNaN()) -> src
        (comparator.compare(src, min) < 0) -> min
        (comparator.compare(src, max) > 0) -> max
        else -> src
    }

    /**
     * 获取 list 的钳位区间。
     * 根据 focusIndex 为范围去保留 limit 数量，减去头尾多余的部分，
     * 1.id list 在 focusIndex 前的元素数量如果不足 limit/2，总数保持为 limit，多余的数量放在 focus 的后面。
     * 2.id list 在 focusIndex 后的元素数量如果不足 limit/2，总数保持为 limit，多余的数量放在 focus 的前面。
     * 3.如果 focus 前后的元素数量都大于 limit/2，那前面取 limit/2，后面取 limit/2 - 1
     * 4.如果 id list 不足 limit，直接返回头尾即可。
     * @param focusIndex id list 被选中的的 item
     * @param size id list 数量
     * @param limit 裁剪后的大小
     * @return Range<Int> 裁剪后对于 id list 的 startIndex 、endIndex，都是闭区间。
     */
    @JvmStatic
    fun getClampRange(focusIndex: Int, size: Int, limit: Int): IntRange? {
        if ((focusIndex < 0) || (focusIndex >= size) || (size <= 0) || (limit <= 0)) return null
        if (size <= limit) return IntRange(0, size - 1)

        // 向前取1000个，不足就从最左侧开始
        val start = (focusIndex - limit / 2).coerceAtLeast(0)
        // 向后取1000个，不足就从最右侧开始
        val end = (focusIndex + (limit / 2 - 1)).coerceAtMost(size - 1)
        return if (start == 0) { // 前边不足
            IntRange(0, limit - 1)
        } else if (end == size - 1) { // 后边不足
            IntRange(size - limit, size - 1)
        } else { // [(focusIndex - limit / 2) , focusIndex + (limit / 2 - 1)]
            IntRange(start, end)
        }
    }

    /**
     * **获取最小值，可输入任意个数值并返回其中最小的一个数值。**
     *
     * 适用于[Byte]、[Int]、[Long]、[Float]、[Double]等类型的钳位，同时也适用于能够被默认[Comparator]比较的
     * 引用类型。如果需要比较需要使用特殊方法才能对比的对象，使用
     * *`fun <T : Comparable<*>> min(vararg values: T, comparator: Comparator<T>): T`*
     *
     * @param values 需要查找最小元素的数组
     * @return 返回数组中的最小值
     */
    @JvmStatic
    fun <T : Comparable<*>> min(vararg values: T): T {
        var min: T = values.first()
        values.forEach { item ->
            if ((item is Float) && item.isNaN()) {
                return@forEach
            } else if ((item is Double) && item.isNaN()) {
                return@forEach
            } else {
                min = if (compareValues(item, min) < 0) item else min
            }
        }
        return min
    }

    /**
     * **获取最小值，可输入任意个数值并返回其中最小的一个数值。**
     *
     * 用于比较需要使用特殊方法才能对比的对象组。
     *
     * @param values 需要查找最小元素的数组
     * @param comparator 比较器
     * @return 返回数组中的最小值
     */
    @JvmStatic
    fun <T : Comparable<*>> min(vararg values: T, comparator: Comparator<T>): T {
        var min: T = values.first()
        values.forEach { item ->
            min = if (comparator.compare(item, min) < 0) item else min
        }
        return min
    }

    /**
     * **获取最大值，可输入任意个数值并返回其中最大的一个数值。**
     *
     * 适用于[Byte]、[Int]、[Long]、[Float]、[Double]等类型的钳位，同时也适用于能够被默认[Comparator]比较的
     * 引用类型。如果需要比较需要使用特殊方法才能对比的对象，使用
     * *`fun <T : Comparable<*>> max(vararg values: T, comparator: Comparator<T>): T`*
     *
     * @param values 需要查找最大元素的数组
     * @return 返回数组中的最大值
     */
    @JvmStatic
    fun <T : Comparable<*>> max(vararg values: T): T {
        var max: T = values.first()
        values.forEach { item ->
            if ((item is Float) && item.isNaN()) {
                return@forEach
            } else if ((item is Double) && item.isNaN()) {
                return@forEach
            } else {
                max = if (compareValues(item, max) > 0) item else max
            }
        }
        return max
    }

    /**
     * **获取最大值，可输入任意个数值并返回其中最大的一个数值。**
     *
     * 用于比较需要使用特殊方法才能对比的对象组。
     *
     * @param values 需要查找最大元素的数组
     * @param comparator 比较器
     * @return 返回数组中的最大值
     */
    @JvmStatic
    fun <T : Comparable<*>> max(vararg values: T, comparator: Comparator<T>): T {
        var max: T = values.first()
        values.forEach { item ->
            max = if (comparator.compare(item, max) > 0) item else max
        }
        return max
    }

    /**
     * **获取大小为输入数组中处于中间位置的数值。此方法会以空间换时间做快速查找。**
     * 此方法会便利输入的所有数值，并挑选出最接近*`min + (max - min) / 2`*的值。
     *
     * @param values 需要查找最接近中间值元素的数组
     * @return 返回最接近*`min + (max - min) / 2`*的元素
     */
    /*
    @Suppress("UNCHECKED_CAST")
    fun <R: Number, T: Comparable<R>> fastMid(vararg values: T): T {
        val sortedValues = values.copyOf()
        sortedValues.sort()

        val min: R = sortedValues.first() as R
        val max: R = sortedValues.last() as R
        val mid = min + (max - min) * 0.5
        sortedValues.forEach {item ->
            if (abs(item - mid).compareTo(0.0) > 0) {
                return item
            }
        }
        return sortedValues.last()
    }
    */

    /**
     * **获取输入数值中的平均值。**
     *
     * 此方法会寻找输入数值中的最大值和最小值，并返回其均值。
     *
     * @param values 需要计算均值的数组
     * @return 返回*`min + (max - min) / 2`*
     */
    @JvmStatic
    fun avg(vararg values: Byte): Byte {
        var min = values.first()
        var max = values.last()
        values.forEach { item ->
            min = if (compareValues(item, min) < 0) item else min
            max = if (compareValues(item, max) > 0) item else max
        }
        return (min + (max - min) * 0.5).toInt().toByte()
    }

    /**
     * **获取输入数值中的平均值。**
     *
     * 此方法会寻找输入数值中的最大值和最小值，并返回其均值。
     *
     * @param values 需要计算均值的数组
     * @return 返回*`min + (max - min) / 2`*
     */
    @JvmStatic
    fun avg(vararg values: Int): Int {
        var min = values.first()
        var max = values.last()
        values.forEach { item ->
            min = if (compareValues(item, min) < 0) item else min
            max = if (compareValues(item, max) > 0) item else max
        }
        return (min + (max - min) * 0.5).toInt()
    }

    /**
     * **获取输入数值中的平均值。**
     *
     * 此方法会寻找输入数值中的最大值和最小值，并返回其均值。
     *
     * @param values 需要计算均值的数组
     * @return 返回*`min + (max - min) / 2`*
     */
    @JvmStatic
    fun avg(vararg values: Long): Long {
        var min = values.first()
        var max = values.last()
        values.forEach { item ->
            min = if (compareValues(item, min) < 0) item else min
            max = if (compareValues(item, max) > 0) item else max
        }
        return (min + (max - min) * 0.5).toLong()
    }

    /**
     * **获取输入数值中的平均值。**
     *
     * 此方法会寻找输入数值中的最大值和最小值，并返回其均值。
     *
     * @param values 需要计算均值的数组
     * @return 返回*`min + (max - min) / 2`*
     */
    @JvmStatic
    fun avg(vararg values: Float): Float {
        var min = values.first()
        var max = values.last()
        values.forEach { item ->
            if (item.isNaN()) {
                return@forEach
            }
            min = if (compareValues(item, min) < 0) item else min
            max = if (compareValues(item, max) > 0) item else max
        }
        return (min + (max - min) * 0.5).toFloat()
    }

    /**
     * **获取输入数值中的平均值。**
     *
     * 此方法会寻找输入数值中的最大值和最小值，并返回其均值。
     *
     * @param values 需要计算均值的数组
     * @return 返回*`min + (max - min) / 2`*
     */
    @JvmStatic
    fun avg(vararg values: Double): Double {
        var min = values.first()
        var max = values.last()
        values.forEach { item ->
            if (item.isNaN()) {
                return@forEach
            }
            min = if (compareValues(item, min) < 0) item else min
            max = if (compareValues(item, max) > 0) item else max
        }
        return (min + (max - min) * 0.5).toDouble()
    }

    /**
     * **判断src是否处于lower与upper之间。**
     *
     * 默认是`src∈[lower, upper]`时返回true，当[exclude]为true时，在`src∈(lower, upper)`时返回true。
     * 此方法适用于[Byte]、[Int]、[Long]、[Float]、[Double]等类型的钳位，同时也适用于能够被默认[Comparator]比较的
     * 引用类型。
     *
     * @param src 需要判断的值
     * @param lower 待判断范围的下限
     * @param upper 待判断范围的上限
     * @param exclude 是否排除上下限，如果false则以`src∈[lower, upper]`为判断条件，
     *                否则以`src∈(lower, upper)`为判断条件
     * @param comparator
     * @return
     */
    @JvmStatic
    fun <T : Comparable<*>> inRange(
        src: T,
        lower: T,
        upper: T,
        exclude: Boolean = false,
        comparator: Comparator<T>? = null
    ): Boolean {
        val comparing = fun(a: T, b: T): Int = comparator?.compare(a, b) ?: compareValues(a, b)
        return if (exclude) {
            ((comparing(src, lower) > 0) && (comparing(src, upper) < 0))
        } else {
            ((comparing(src, lower) >= 0) && (comparing(src, upper) <= 0))
        }
    }

    /**
     * **判断两个Rect的是否相似。**
     *
     * @param srcA 需要参与相似度判断的两个Rect之一
     * @param srcB 需要参与相似度判断的两个Rect之一
     * @param precision 差异值
     * @return 如果两个Rect的各个边的差异都小于[precision]，则返回true。如果传入0f,
     *         则会在两个Rect的left、top、right、bottom完全相等时才会返回true
     */
    @JvmStatic
    fun isRectSimilarly(srcA: Rect, srcB: Rect, precision: Float): Boolean =
        (abs(srcA.left - srcB.left).compareTo(precision) < 0) &&
            (abs(srcA.top - srcB.top).compareTo(precision) < 0) &&
            (abs(srcA.right - srcB.right).compareTo(precision) < 0) &&
            (abs(srcA.bottom - srcB.bottom).compareTo(precision) < 0)

    /**
     * **判断当前Rect与另外一个Rect是否相似。**
     *
     * @param other 需要判断相似度的另外一个Rect
     * @param precision 相似度，为精度值。此方法以它来判断两个Rect的各个边的差异度，如果传入0f,
     *                  则会在两个Rect的left、top、right、bottom完全相等时才会返回true
     */
    @JvmStatic
    fun Rect.equal(other: Rect, precision: Float): Boolean =
        isRectSimilarly(this, other, precision)

    /**
     * **把百分比percent映射到`[`lower, upper`]`范围上**
     *
     * @param percent 需要映射的百分比
     * @param lower 映射范围的下限
     * @param upper 映射范围的上限
     * @return 当percent为0时返回lower，当percent为1时返回upper
     */
    @JvmStatic
    fun map(percent: Double, lower: Int, upper: Int): Int =
        map(percent, lower.toDouble(), upper.toDouble()).toInt()

    /**
     * **把百分比percent映射到`[`lower, upper`]`范围上**
     *
     * @param percent 需要映射的百分比
     * @param lower 映射范围的下限
     * @param upper 映射范围的上限
     * @return 当percent为0时返回lower，当percent为1时返回upper
     */
    @JvmStatic
    fun map(percent: Double, lower: Float, upper: Float): Float =
        map(percent, lower.toDouble(), upper.toDouble()).toFloat()

    /**
     * **把百分比percent映射到`[`lower, upper`]`范围上**
     *
     * @param percent 需要映射的百分比
     * @param lower 映射范围的下限
     * @param upper 映射范围的上限
     * @return 当percent为0时返回lower，当percent为1时返回upper
     */
    @JvmStatic
    fun map(percent: Double, lower: Double, upper: Double): Double =
        lower + (upper - lower) * percent

    /**
     * **把百分比percent映射到`[`lower, upper`]`范围上**
     *
     * @param src 需要映射的值
     * @param lower 映射范围的下限
     * @param upper 映射范围的上限
     * @return 当percent为0时返回lower，当percent为1时返回upper
     */
    @JvmStatic
    fun map(
        src: Double,
        srcLower: Int,
        srcUpper: Int,
        destLower: Int,
        destUpper: Int
    ): Int =
        map(src, srcLower.toDouble(), srcUpper.toDouble(), destLower.toDouble(), destUpper.toDouble()).toInt()

    /**
     * **把百分比percent映射到`[`lower, upper`]`范围上**
     *
     * @param src 需要映射的值
     * @param lower 映射范围的下限src
     * @param upper 映射范围的上限
     * @return 当percent为0时返回lower，当percent为1时返回upper
     */
    @JvmStatic
    fun map(
        src: Double,
        srcLower: Float,
        srcUpper: Float,
        destLower: Float,
        destUpper: Float
    ): Float =
        map(src, srcLower.toDouble(), srcUpper.toDouble(), destLower.toDouble(), destUpper.toDouble()).toFloat()

    /**
     * **把百分比percent映射到`[`lower, upper`]`范围上**
     *
     * @param src 需要映射的值
     * @param lower 映射范围的下限
     * @param upper 映射范围的上限
     * @return 当percent为0时返回lower，当percent为1时返回upper
     */
    @JvmStatic
    fun map(
        src: Double,
        srcLower: Double,
        srcUpper: Double,
        destLower: Double,
        destUpper: Double
    ): Double {
        val percent = (src - srcLower) / (srcUpper - srcLower)
        return destLower + (destUpper - destLower) * percent
    }

    /**
     * **小于等于[num]的最大2的幂**
     */
    @JvmStatic
    fun prevPowerOf2(num: Int): Int = if (num <= 0) {
        throw IllegalArgumentException()
    } else {
        Integer.highestOneBit(num)
    }

    /**
     * **大于等于[num]的最小2的幂**
     */
    @JvmStatic
    fun nextPowerOf2(num: Int): Int {
        var result = num
        require(!(result <= 0 || result > 1 shl 30))
        result -= 1
        result = result or (result shr 16)
        result = result or (result shr 8)
        result = result or (result shr 4)
        result = result or (result shr 2)
        result = result or (result shr 1)
        return result + 1
    }

    /**
     * 相当于OpenGL中的mix函数
     */
    @JvmStatic
    fun mix(start: Float, end: Float, factor: Float): Float {
        val f = clamp(factor, 0f, 1f)
        return start * (1f - f) + end * f
    }

    /**
     * 相当于OpenGL中的mix函数
     */
    @JvmStatic
    fun mix(start: Int, end: Int, factor: Float): Float {
        val f = clamp(factor, 0f, 1f)
        return start * (1f - f) + end * f
    }

    /**
     * 相当于OpenGL中的mix函数
     */
    @JvmStatic
    fun mix(start: Long, end: Long, factor: Float): Float {
        val f = clamp(factor, 0f, 1f)
        return start * (1f - f) + end * f
    }

    /**
     * 相当于OpenGL中的mix函数
     */
    @JvmStatic
    fun mix(start: Double, end: Double, factor: Double): Double {
        val f = clamp(factor, 0.0, 1.0)
        return start * (1.0 - f) + end * f
    }

    /**
     * 对Rect进行混合
     */
    @JvmStatic
    fun mix(start: Rect, end: Rect, factor: Float, out: Rect) {
        out.apply {
            left = mix(start.left, end.left, factor).roundToInt()
            top = mix(start.top, end.top, factor).roundToInt()
            right = mix(start.right, end.right, factor).roundToInt()
            bottom = mix(start.bottom, end.bottom, factor).roundToInt()
        }
    }

    @JvmStatic
    fun ceilLog2(value: Float): Int {
        var i = 0
        while (i < NUMBER_31) {
            if (1 shl i >= value) {
                break
            }
            i++
        }
        return i
    }

    @JvmStatic
    fun floorLog2(value: Float): Int {
        var i = 0
        while (i < NUMBER_31) {
            if (1 shl i > value) {
                break
            }
            i++
        }
        return i - 1
    }

    @JvmStatic
    fun Float.roundToInt(): Int {
        return if (isNaN()) {
            GLog.w(TAG, LogFlag.DL, "roundToInt is isNaN so return 0")
            0
        } else {
            return Math.round(this)
        }
    }

    /**
     * 保留[num]位小数
     * @param roundingMode RoundingMode 默认四舍五人
     */
    @JvmStatic
    fun Double.toScale(num: Int, roundingMode: RoundingMode = RoundingMode.HALF_EVEN): Double {
        kotlin.runCatching {
            return toBigDecimal().setScale(num, roundingMode).toDouble()
        }.onFailure {
            GLog.w(TAG, LogFlag.DL) { "Double.toScale, fail: $it" }
        }
        return this
    }

    /**
     * 保留[num]位小数
     * @param roundingMode RoundingMode 默认四舍五人
     */
    @JvmStatic
    fun Float.toScale(num: Int, roundingMode: RoundingMode = RoundingMode.HALF_EVEN): Float {
        kotlin.runCatching {
            return toBigDecimal().setScale(num, roundingMode).toFloat()
        }.onFailure {
            GLog.w(TAG, LogFlag.DL) { "Float.toScale, fail: $it" }
        }
        return this
    }

    /**
     * 将某个数字进行等分
     * @param number 需要等分的数字
     * @param count 需要等分的分数
     * @return 等分后的数据集合
     */
    @JvmStatic
    fun equalDivisionNumber(number: Long, count: Long): List<Long> {
        val list = ArrayList<Long>()
        if (count <= 1) {
            return list
        }

        /**
         * 视频被分割的个数
         */
        val splitCount = count - 1
        for (index in 0 until count) {
            val videoFrameMills = (number * (index.toDouble() / splitCount)).roundToLong()
            list.add(videoFrameMills)
        }
        return list
    }

    /**
     * 判断传入的两个Double类型参数是否相等，若两者的差值小于给定的Tolerance，即认为两者相等
     */
    @JvmStatic
    fun equalWithinTolerance(left: Double, right: Double, tolerance: Double = DEFAULT_EQUAL_TOLERANCE.toDouble()): Boolean {
        return abs(left - right) <= abs(tolerance)
    }

    /**
     * 判断传入的两个Float类型参数是否相等，若两者的差值小于给定的Tolerance，即认为两者相等
     */
    @JvmStatic
    fun equalWithinTolerance(left: Float, right: Float, tolerance: Float = DEFAULT_EQUAL_TOLERANCE): Boolean {
        return equalWithinTolerance(left.toDouble(), right.toDouble(), tolerance.toDouble())
    }

    /**
     * 计算两组数据的协方差。
     * 注意
     * - 两组数据的数量需要相等，数据量不相等时，自动舍去多余部分
     * - 任一个列表为空时，计算结果返回 NaN
     *
     * 协方差表示了两组数据的相关性，用于衡量两个变量之间的误差。
     * - 协方差为正： 两个变量的趋势一致。
     * - 协方差为负： 两个变量的趋势相反。
     * - 协方差为0： 两个变量没有相关性。
     * ```
     * // 公式如下
     * covariance = 1/(n-1)Σ(xi - xavg)(yi - yavg)
     * // 说明
     * // xi,yi 分别指的是第 i 个 x ，和第 i 个 y。
     * // xavg, yavg 分别指的是 x 的平均值和 y的平均值。
     * ```
     */
    @JvmStatic
    fun covariance(left: LongArray, right: LongArray): Float {
        if (left.isEmpty() || right.isEmpty()) {
            return Float.NaN
        }
        val leftAverage = left.average().toFloat()
        val rightAverage = right.average().toFloat()
        var result = 0F
        repeat(min(left.size, right.size)) { index ->
            result += (left[index] - leftAverage) * (right[index] - rightAverage)
        }
        return result / min(left.size, right.size)
    }

    /**
     * 计算两组数据的协方差。
     * 注意
     * - 两组数据的数量需要相等，数据量不相等时，自动舍去多余部分
     * - 任一个列表为空时，计算结果返回 NaN
     *
     * 协方差表示了两组数据的相关性，用于衡量两个变量之间的误差。
     * - 协方差为正： 两个变量的趋势一致。
     * - 协方差为负： 两个变量的趋势相反。
     * - 协方差为0： 两个变量没有相关性。
     * ```
     * // 公式如下
     * covariance = 1/(n-1)Σ(xi - xavg)(yi - yavg)
     * // 说明
     * // xi,yi 分别指的是第 i 个 x ，和第 i 个 y。
     * // xavg, yavg 分别指的是 x 的平均值和 y的平均值。
     * ```
     */
    @JvmStatic
    fun covariance(left: IntArray, right: IntArray): Float {
        if (left.isEmpty() || right.isEmpty()) {
            return Float.NaN
        }
        val leftAverage = left.average().toFloat()
        val rightAverage = right.average().toFloat()
        var result = 0F
        repeat(min(left.size, right.size)) { index ->
            result += (left[index] - leftAverage) * (right[index] - rightAverage)
        }
        return result / min(left.size, right.size)
    }

    /**
     * 计算两组数据的协方差。
     * 注意
     * - 两组数据的数量需要相等，数据量不相等时，自动舍去多余部分
     * - 任一个列表为空时，计算结果返回 NaN
     *
     * 协方差表示了两组数据的相关性，用于衡量两个变量之间的误差。
     * - 协方差为正： 两个变量的趋势一致。
     * - 协方差为负： 两个变量的趋势相反。
     * - 协方差为0： 两个变量没有相关性。
     * ```
     * // 公式如下
     * covariance = 1/(n-1)Σ(xi - xavg)(yi - yavg)
     * // 说明
     * // xi,yi 分别指的是第 i 个 x ，和第 i 个 y。
     * // xavg, yavg 分别指的是 x 的平均值和 y的平均值。
     * ```
     */
    @JvmStatic
    fun covariance(left: DoubleArray, right: DoubleArray): Float {
        if (left.isEmpty() || right.isEmpty()) {
            return Float.NaN
        }
        val leftAverage = left.average().toFloat()
        val rightAverage = right.average().toFloat()
        var result = 0F
        repeat(min(left.size, right.size)) { index ->
            result += ((left[index] - leftAverage) * (right[index] - rightAverage)).toFloat()
        }
        return result / min(left.size, right.size)
    }

    /**
     * 计算两组数据的协方差。
     * 注意
     * - 两组数据的数量需要相等，数据量不相等时，自动舍去多余部分
     * - 任一个列表为空时，计算结果返回 NaN
     *
     * 协方差表示了两组数据的相关性，用于衡量两个变量之间的误差。
     * - 协方差为正： 两个变量的趋势一致。
     * - 协方差为负： 两个变量的趋势相反。
     * - 协方差为0： 两个变量没有相关性。
     * ```
     * // 公式如下
     * covariance = 1/(n-1)Σ(xi - xavg)(yi - yavg)
     * // 说明
     * // xi,yi 分别指的是第 i 个 x ，和第 i 个 y。
     * // xavg, yavg 分别指的是 x 的平均值和 y的平均值。
     * ```
     */
    @JvmStatic
    fun covariance(left: FloatArray, right: FloatArray): Float {
        if (left.isEmpty() || right.isEmpty()) {
            return Float.NaN
        }
        val leftAverage = left.average().toFloat()
        val rightAverage = right.average().toFloat()
        var result = 0F
        repeat(min(left.size, right.size)) { index ->
            result += (left[index] - leftAverage) * (right[index] - rightAverage)
        }
        return result / min(left.size, right.size)
    }

    @JvmStatic
    fun angleToRadian(angle: Double): Double {
        return angle * PI / ANGLE_180
    }

    /**
     * 图片需要4字节对齐，以保证调用IPU模块时的图片无异常，向上对齐
     *
     * @param size 图片宽或高
     * @return 4的倍数
     */
    @JvmStatic
    fun getAlignedStrideUpToMultipleOf4(size: Int): Int {
        return size.ceilAlignment(NUMBER_4)
    }

    /**
     * 图片需要4字节对齐，以保证调用IPU模块时的图片无异常, 向下对齐
     *
     * @param size 图片宽或高
     * @return 4的倍数
     */
    @JvmStatic
    fun getAlignedStrideDownToMultipleOf4(size: Int): Int {
        return size.floorAlignment(NUMBER_4)
    }

    /**
     * 图片需要2字节对齐，以保证调用IPU模块时的图片无异常，向上对齐
     *
     * @param size 图片宽或高
     * @return 2的倍数
     */
    @JvmStatic
    fun getAlignedStrideUpToMultipleOf2(size: Int): Int {
        return size.ceilAlignment(NUMBER_2)
    }

    /**
     * 图片需要2字节对齐，以保证调用IPU模块时的图片无异常，向下对齐
     *
     * @param size 图片宽或高
     * @return 2的倍数
     */
    @JvmStatic
    fun getAlignedStrideDoneToMultipleOf2(size: Int): Int {
        return size.floorAlignment(NUMBER_2)
    }

    /**
     * 将图片按照指定字节进行对齐
     *
     * @param size 图片宽或高
     * @param specifiedByteAlignmentNumber 指定的字节对齐数
     * @return 对齐后的size
     */
    @JvmStatic
    fun getAlignedStrideUpToMultipleOfNumber(size: Int, specifiedByteAlignmentNumber: Int): Int {
        return if (specifiedByteAlignmentNumber > NUMBER_1) {
            val power = Math.getExponent(specifiedByteAlignmentNumber.toDouble())
            (size + (specifiedByteAlignmentNumber - NUMBER_1)) shr power shl power
        } else {
            size
        }
    }

    /**
     * @param size 图片宽或高
     * @return size是否4的倍数
     */
    @JvmStatic
    fun isAlignedStrideMultipleOf4(size: Int): Boolean {
        return size and ALIGNED_STRIDE_AND_THREE == ALIGNED_STRIDE_MULTIPLE_ZERO
    }

    /**
     * 判断两个float值是否相等
     */
    @JvmStatic
    fun Float.areFloatsEqual(b: Float, epsilon: Float = 1f): Boolean {
        return abs(this - b) < epsilon
    }

    /**
     * 判断两个FloatArray值是否相等
     */
    @JvmStatic
    fun FloatArray.areFloatsEqual(b: FloatArray, epsilon: Float = 1f): Boolean {
        if (this === b) return true

        if (this.size != b.size) return false

        forEachIndexed { index, value ->
            if (value.areFloatsEqual(b[index], epsilon).not()) {
                return false
            }
        }
        return true
    }

    /**
     * Float精度无损的转换成Int
     */
    @JvmStatic
    fun Float.losslessToInt(): Int {
        return (this + FLOAT_TO_INT_FACTOR).toInt()
    }

    /**
     * 返回最接近的4的倍数的数据
     */
    @JvmStatic
    fun Number.alignment4(): Int {
        return closestAlignment(NUMBER_4)
    }

    /**
     * 返回最接近的2的倍数的数据
     */
    @JvmStatic
    fun Number.alignment2(): Int {
        return closestAlignment(NUMBER_2)
    }

    /**
     * 执行字节对齐，最接近原则取整
     * @param multiple 2的倍数
     */
    @JvmStatic
    fun Number.closestAlignment(align: Int): Int {
        return ((toDouble() + (align.toDouble() / 2)) / align).toInt() * align
    }

    /**
     * 执行字节对齐，向下取整原则
     * @param multiple 2的倍数
     */
    @JvmStatic
    fun Number.floorAlignment(align: Int): Int {
        return (toDouble() / align).toInt() * align
    }

    /**
     * 执行字节对齐，向上取整原则
     * @param multiple 2的倍数
     */
    @JvmStatic
    fun Number.ceilAlignment(align: Int): Int {
        return ((toDouble() + (align.toDouble() - 1)) / align).toInt() * align
    }

    /**
     * 返回向下取整的4的倍数的数据
     */
    @JvmStatic
    fun Number.floorToMultipleOf4(): Int {
        return ceilAlignment(NUMBER_4)
    }
}
