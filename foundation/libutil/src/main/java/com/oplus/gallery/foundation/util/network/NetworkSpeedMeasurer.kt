/**************************************************************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - InternetSpeedMeasurer.kt
 ** Description:网速测量类
 ** Version: 1.0
 ** Date: 2024/04/23
 ** Author: wuhengze @Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 *  wuhengze @Apps.Gallery3D    2024/04/23    1.0              build this module
 *************************************************************************************************/
package com.oplus.gallery.foundation.util.network

import android.net.TrafficStats
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.standard_lib.app.AppConstants
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter

/**
 * 网速测量
 *
 * @param measurementIntervalMillis 测量网速的时间间隔，如30min测量一次
 * @param measureTimeWindow 测量网速的窗口时间，即取这个时间内从平均网速
 * 默认60s，即网速是60s内平均网速
 */
class NetworkSpeedMeasurer(
    private val measurementIntervalMillis: Int = 0,
    private val measureTimeWindow: Long = DEFAULT_MEASURE_TIME_WINDOW
) {
    private var startTotalRxInKB: Long = -1L
    private var startMeasureTimeMillis: Long = 0
    private var totalCostTimeMillis = 0L
    private var totalRxKB = 0L
    private var currentSpeed = 0L
    private var lastCalculateTime = 0L
    private val uid by lazy {
        ContextGetter.context.applicationInfo.uid
    }

    /**
     * 测量网速，即测量执行[downloadFunction]期间的网速
     *
     * @param downloadFunction 下载函数
     */
    fun measureInternetSpeed(downloadFunction: () -> Unit): Long {
        networkSpeedMeasureStart()
        downloadFunction.invoke()
        networkSpeedMeasureEnd()
        return currentSpeed
    }

    private fun networkSpeedMeasureStart() {
        if (shouldCalculateSpeed()) {
            // 接受到的流量系统会一直累加，只需要记录开始时的流量值
            if (startTotalRxInKB == INVALID_TOTAL_RX) {
                startTotalRxInKB = getTotalRxKB()
            }
            startMeasureTimeMillis = System.currentTimeMillis()
        }
    }

    private fun networkSpeedMeasureEnd() {
        if (shouldCalculateSpeed().not() || (startMeasureTimeMillis <= 0)) {
            return
        }
        totalCostTimeMillis += System.currentTimeMillis() - startMeasureTimeMillis
        /**
         * 要求计算大于[measureTimeWindow]的平均值网速
         */
        if (totalCostTimeMillis < measureTimeWindow) {
            return
        }
        totalRxKB += getTotalRxKB() - startTotalRxInKB
        currentSpeed = totalRxKB / (totalCostTimeMillis / TimeUtils.TIME_1_SEC_IN_MS)
        GLog.d(
            TAG, "networkSpeedMeasureEnd: totalRx =  $totalRxKB, timeCostInMillis = $totalCostTimeMillis"
                    + " speed = $currentSpeed" + " startTotalRxInKB = $startTotalRxInKB"
        )
        lastCalculateTime = System.currentTimeMillis()
        resetSpeedParam()
    }

    /**
     * 重置速度测量参数
     */
    private fun resetSpeedParam() {
        startTotalRxInKB = 0
        startMeasureTimeMillis = 0
        startTotalRxInKB = INVALID_TOTAL_RX
        totalCostTimeMillis = 0
    }

    private fun shouldCalculateSpeed(): Boolean {
        return System.currentTimeMillis() - lastCalculateTime >= measurementIntervalMillis
    }

    /**
     * 获取
     */
    private fun getTotalRxKB(): Long {
        return if (TrafficStats.getUidRxBytes(uid).toInt() == TrafficStats.UNSUPPORTED) {
            0
        } else {
            TrafficStats.getUidRxBytes(uid) / AppConstants.Capacity.UNIT_KB2B
        }
    }

    companion object {
        private const val TAG = "NetworkSpeedMeasurer"
        private const val DEFAULT_MEASURE_TIME_WINDOW = 60000L
        private const val INVALID_TOTAL_RX = -1L
    }
}