/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - GLog.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2021/04/30
 ** Author: hucanh<PERSON>@Apps.Gallery
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>						<date>			<version>		<desc>
 ** ------------------------------------------------------------------------------
 ** h<PERSON><PERSON><PERSON>@Apps.Gallery		2021/04/30		1.0			OPLUS_ARCH_EXTENDS
 *********************************************************************************/

package com.oplus.gallery.foundation.util.debug

import android.util.Log
import com.oplus.gallery.addon.utils.OSVersionUtils
import com.oplus.gallery.foundation.util.BuildConfig
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_FORCE_PRINT_TRACK
import com.oplus.gallery.foundation.util.debug.GProperty.SECURE
import com.oplus.gallery.foundation.util.debug.logger.DefaultLogger
import com.oplus.gallery.foundation.util.debug.logger.ILogger
import com.oplus.gallery.foundation.util.debug.logger.OldLogger
import com.oplus.gallery.foundation.util.text.TextUtil
import com.usertrace.cdo.usertrace.domain.dto.UserTraceConfigDto
import java.io.PrintWriter

/**
 * msg 消息生成块，调用后能生成日志信息
 */
typealias LogMsgBlock = () -> String?

val emptyMsgBlock: LogMsgBlock = { "" }

object GLog {

    private const val TAG = "GLog"

    private const val LINK_STACK: Int = 3

    /**
     * Logkit 是否支持打包相册自定义日志文件 (OS15.0及以后支持)
     */
    private val isSupportFile: Boolean = OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_15_0_0)

    /**
     * 当前 LogKit 是否会对日志进行限流 (非 Root 版本限流)
     */
    private var isLogLimited: Boolean = SECURE != 0

    /**
     * 日志输出
     */
    private var logger: ILogger = if (isSupportFile && isLogLimited) {
        Log.w(TAG, "use default logger")
        DefaultLogger()
    } else {
        Log.w(TAG, "use old logger , isSupportFile : $isSupportFile , isLogLimited : $isLogLimited")
        OldLogger()
    }

    /**
     * 输入 v 级别日志
     *
     * @param tag tag Used to identify the source of a log message.  It usually identifies
     *        the class or activity where the log call occurs.
     * @param msg The message you would like logged.
     * @param throwable An exception to log
     */
    @JvmStatic
    @JvmOverloads
    @Deprecated(
        message = "过时了，后续不对业务开放",
        replaceWith = ReplaceWith("使用新接口替代")
    )
    fun v(tag: String?, msg: String?, throwable: Throwable? = null) {
        logger.v(TextUtil.EMPTY_STRING, tag, LogFlag.DL, msg, throwable)
    }

    /**
     * 输入 v 级别日志
     *
     * @param moduleName module name
     * @param tag tag Used to identify the source of a log message.  It usually identifies
     *        the class or activity where the log call occurs.
     * @param flag Determine write type
     * @param msg The message you would like logged.
     * @param throwable An exception to log
     */
    @JvmStatic
    @JvmOverloads
    fun v(moduleName: String?, tag: String?, @LogFlag flag: Int, msg: String?, throwable: Throwable? = null) {
        logger.v(moduleName, tag, flag, msg, throwable)
    }

    /**
     * 输入 v 级别日志
     *
     * @param tag tag Used to identify the source of a log message.  It usually identifies
     *        the class or activity where the log call occurs.
     * @param flag Determine write type
     * @param msg The message you would like logged.
     */
    @JvmStatic
    @JvmOverloads
    fun v(tag: String?, @LogFlag flag: Int, msg: String?) {
        logger.v(TextUtil.EMPTY_STRING, tag, flag, msg, null)
    }

    /**
     * 输入 d 级别日志
     *
     * @param tag tag Used to identify the source of a log message.  It usually identifies
     *        the class or activity where the log call occurs.
     * @param msg The message you would like logged.
     * @param throwable An exception to log
     */
    @JvmStatic
    @JvmOverloads
    @Deprecated(
        message = "过时了，后续不对业务开放",
        replaceWith = ReplaceWith("使用新接口替代")
    )
    fun d(tag: String?, msg: String?, throwable: Throwable? = null) {
        logger.d(TextUtil.EMPTY_STRING, tag, LogFlag.DL, msg, throwable)
    }

    /**
     * 输入 d 级别日志
     *
     * @param moduleName module name
     * @param tag tag Used to identify the source of a log message.  It usually identifies
     *        the class or activity where the log call occurs.
     * @param flag Determine write type
     * @param msg The message you would like logged.
     * @param throwable An exception to log
     */
    @JvmStatic
    @JvmOverloads
    fun d(moduleName: String?, tag: String?, @LogFlag flag: Int, msg: String?, throwable: Throwable? = null) {
        logger.d(moduleName, tag, flag, msg, throwable)
    }

    /**
     * 输入 d 级别日志
     *
     * @param tag tag Used to identify the source of a log message.  It usually identifies
     *        the class or activity where the log call occurs.
     * @param flag Determine write type
     * @param msg The message you would like logged.
     */
    @JvmStatic
    @JvmOverloads
    fun d(tag: String?, @LogFlag flag: Int, msg: String?) {
        logger.d(TextUtil.EMPTY_STRING, tag, flag, msg, null)
    }

    /**
     * 输入 i 级别日志
     *
     * @param tag tag Used to identify the source of a log message.  It usually identifies
     *        the class or activity where the log call occurs.
     * @param msg The message you would like logged.
     * @param throwable An exception to log
     */
    @JvmStatic
    @JvmOverloads
    @Deprecated(
        message = "过时了，后续不对业务开放",
        replaceWith = ReplaceWith("使用新接口替代")
    )
    fun i(tag: String?, msg: String?, throwable: Throwable? = null) {
        logger.i(TextUtil.EMPTY_STRING, tag, LogFlag.DL, msg, throwable)
    }

    /**
     * 输入 i 级别日志
     *
     * @param moduleName module name
     * @param tag tag Used to identify the source of a log message.  It usually identifies
     *        the class or activity where the log call occurs.
     * @param flag Determine write type
     * @param msg The message you would like logged.
     * @param throwable An exception to log
     */
    @JvmStatic
    @JvmOverloads
    fun i(moduleName: String?, tag: String?, @LogFlag flag: Int, msg: String?, throwable: Throwable? = null) {
        logger.i(moduleName, tag, flag, msg, throwable)
    }

    /**
     * 输入 i 级别日志
     *
     * @param tag tag Used to identify the source of a log message.  It usually identifies
     *        the class or activity where the log call occurs.
     * @param flag Determine write type
     * @param msg The message you would like logged.
     */
    @JvmStatic
    @JvmOverloads
    fun i(tag: String?, @LogFlag flag: Int, msg: String?) {
        logger.i(TextUtil.EMPTY_STRING, tag, flag, msg, null)
    }

    /**
     * 输入 w 级别日志
     *
     * @param tag tag Used to identify the source of a log message.  It usually identifies
     *        the class or activity where the log call occurs.
     * @param msg The message you would like logged.
     * @param throwable An exception to log
     */
    @JvmStatic
    @JvmOverloads
    @Deprecated(
        message = "过时了，后续不对业务开放",
        replaceWith = ReplaceWith("使用新接口替代")
    )
    fun w(tag: String?, msg: String?, throwable: Throwable? = null) {
        logger.w(TextUtil.EMPTY_STRING, tag, LogFlag.DL, msg, throwable)
    }

    /**
     * 输入 w 级别日志
     *
     * @param moduleName module name
     * @param tag tag Used to identify the source of a log message.  It usually identifies
     *        the class or activity where the log call occurs.
     * @param flag Determine write type
     * @param msg The message you would like logged.
     * @param throwable An exception to log
     */
    @JvmStatic
    @JvmOverloads
    fun w(moduleName: String?, tag: String?, @LogFlag flag: Int, msg: String?, throwable: Throwable? = null) {
        logger.w(moduleName, tag, flag, msg, throwable)
    }

    /**
     * 输入 w 级别日志
     *
     * @param tag tag Used to identify the source of a log message.  It usually identifies
     *        the class or activity where the log call occurs.
     * @param flag Determine write type
     * @param msg The message you would like logged.
     */
    @JvmStatic
    @JvmOverloads
    fun w(tag: String?, @LogFlag flag: Int, msg: String?) {
        logger.w(TextUtil.EMPTY_STRING, tag, flag, msg, null)
    }

    /**
     * 输入 e 级别日志
     *
     * @param tag tag Used to identify the source of a log message.  It usually identifies
     *        the class or activity where the log call occurs.
     * @param msg The message you would like logged.
     * @param throwable An exception to log
     */
    @JvmStatic
    @JvmOverloads
    @Deprecated(
        message = "过时了，后续不对业务开放",
        replaceWith = ReplaceWith("使用新接口替代")
    )
    fun e(tag: String?, msg: String?, throwable: Throwable? = null) {
        logger.e(TextUtil.EMPTY_STRING, tag, LogFlag.DL, msg, throwable)
    }

    /**
     * 输入 e 级别日志
     *
     * @param moduleName module name
     * @param tag tag Used to identify the source of a log message.  It usually identifies
     *        the class or activity where the log call occurs.
     * @param flag Determine write type
     * @param msg The message you would like logged.
     * @param throwable An exception to log
     */
    @JvmStatic
    @JvmOverloads
    fun e(moduleName: String?, tag: String?, @LogFlag flag: Int, msg: String?, throwable: Throwable? = null) {
        logger.e(moduleName, tag, flag, msg, throwable)
    }

    /**
     * 输入 e 级别日志
     *
     * @param tag tag Used to identify the source of a log message.  It usually identifies
     *        the class or activity where the log call occurs.
     * @param flag Determine write type
     * @param msg The message you would like logged.
     */
    @JvmStatic
    @JvmOverloads
    fun e(tag: String?, @LogFlag flag: Int, msg: String?, throwable: Throwable? = null) {
        logger.e(TextUtil.EMPTY_STRING, tag, flag, msg, throwable)
    }

    /**
     * 输入 v 级别日志
     *
     * @param tag tag Used to identify the source of a log message.  It usually identifies
     *        the class or activity where the log call occurs.
     * @param throwable An exception to log
     * @param msgBlock The block that produce message you would like logged.
     */
    @JvmStatic
    @JvmOverloads
    @Deprecated(
        message = "过时了，后续不对业务开放",
        replaceWith = ReplaceWith("使用新接口替代")
    )
    fun v(tag: String?, throwable: Throwable? = null, msgBlock: LogMsgBlock = emptyMsgBlock) {
        logger.v(TextUtil.EMPTY_STRING, tag, LogFlag.DL, throwable, msgBlock)
    }

    /**
     * 输入 v 级别日志
     *
     * @param moduleName module name
     * @param tag tag Used to identify the source of a log message.  It usually identifies
     *        the class or activity where the log call occurs.
     * @param throwable An exception to log
     * @param flag Determine write type
     * @param msgBlock The block that produce message you would like logged.
     */
    @JvmStatic
    @JvmOverloads
    fun v(moduleName: String?, tag: String?, throwable: Throwable? = null, @LogFlag flag: Int, msgBlock: LogMsgBlock = emptyMsgBlock) {
        logger.v(moduleName, tag, flag, throwable, msgBlock)
    }

    /**
     * 输入 v 级别日志
     *
     * @param tag tag Used to identify the source of a log message.  It usually identifies
     *        the class or activity where the log call occurs.
     * @param flag Determine write type
     * @param msgBlock The block that produce message you would like logged.
     */
    @JvmStatic
    @JvmOverloads
    fun v(tag: String?, @LogFlag flag: Int, msgBlock: LogMsgBlock = emptyMsgBlock) {
        logger.v(TextUtil.EMPTY_STRING, tag, flag, null, msgBlock)
    }

    /**
     * 输入 d 级别日志
     *
     * @param tag tag Used to identify the source of a log message.  It usually identifies
     *        the class or activity where the log call occurs.
     * @param throwable An exception to log
     * @param msgBlock The block that produce message you would like logged.
     */
    @JvmStatic
    @JvmOverloads
    @Deprecated(
        message = "过时了，后续不对业务开放",
        replaceWith = ReplaceWith("使用新接口替代")
    )
    fun d(tag: String?, throwable: Throwable? = null, msgBlock: LogMsgBlock = emptyMsgBlock) {
        logger.d(TextUtil.EMPTY_STRING, tag, LogFlag.DL, throwable, msgBlock)
    }

    /**
     * 输入 d 级别日志
     *
     * @param moduleName module name
     * @param tag tag Used to identify the source of a log message.  It usually identifies
     *        the class or activity where the log call occurs.
     * @param throwable An exception to log
     * @param flag Determine write type
     * @param msgBlock The block that produce message you would like logged.
     */
    @JvmStatic
    @JvmOverloads
    fun d(moduleName: String?, tag: String?, throwable: Throwable? = null, @LogFlag flag: Int, msgBlock: LogMsgBlock = emptyMsgBlock) {
        logger.d(moduleName, tag, flag, throwable, msgBlock)
    }

    /**
     * 输入 d 级别日志
     *
     * @param tag tag Used to identify the source of a log message.  It usually identifies
     *        the class or activity where the log call occurs.
     * @param flag Determine write type
     * @param msgBlock The block that produce message you would like logged.
     */
    @JvmStatic
    @JvmOverloads
    fun d(tag: String?, @LogFlag flag: Int, msgBlock: LogMsgBlock = emptyMsgBlock) {
        logger.d(TextUtil.EMPTY_STRING, tag, flag, null, msgBlock)
    }

    /**
     * 输入 i 级别日志
     *
     * @param tag Used to identify the source of a log message.  It usually identifies
     *        the class or activity where the log call occurs.
     * @param throwable An exception to log
     * @param msgBlock The block that produce message you would like logged.
     */
    @JvmStatic
    @JvmOverloads
    @Deprecated(
        message = "过时了，后续不对业务开放",
        replaceWith = ReplaceWith("使用新接口替代")
    )
    fun i(tag: String?, throwable: Throwable? = null, msgBlock: LogMsgBlock = emptyMsgBlock) {
        logger.i(TextUtil.EMPTY_STRING, tag, LogFlag.DL, throwable, msgBlock)
    }

    /**
     * 输入 i 级别日志
     *
     * @param moduleName module name
     * @param tag tag Used to identify the source of a log message.  It usually identifies
     *        the class or activity where the log call occurs.
     * @param throwable An exception to log
     * @param flag Determine write type
     * @param msgBlock The block that produce message you would like logged.
     */
    @JvmStatic
    @JvmOverloads
    fun i(moduleName: String?, tag: String?, throwable: Throwable? = null, @LogFlag flag: Int, msgBlock: LogMsgBlock = emptyMsgBlock) {
        logger.i(moduleName, tag, flag, throwable, msgBlock)
    }

    /**
     * 输入 i 级别日志
     *
     * @param tag tag Used to identify the source of a log message.  It usually identifies
     *        the class or activity where the log call occurs.
     * @param flag Determine write type
     * @param msgBlock The block that produce message you would like logged.
     */
    @JvmStatic
    @JvmOverloads
    fun i(tag: String?, @LogFlag flag: Int, msgBlock: LogMsgBlock = emptyMsgBlock) {
        logger.i(TextUtil.EMPTY_STRING, tag, flag, null, msgBlock)
    }

    /**
     * 输入 w 级别日志
     *
     * @param tag Used to identify the source of a log message.  It usually identifies
     *        the class or activity where the log call occurs.
     * @param throwable An exception to log
     * @param msgBlock The block that produce message you would like logged.
     */
    @JvmStatic
    @JvmOverloads
    @Deprecated(
        message = "过时了，后续不对业务开放",
        replaceWith = ReplaceWith("使用新接口替代")
    )
    fun w(tag: String?, throwable: Throwable? = null, msgBlock: LogMsgBlock = emptyMsgBlock) {
        logger.w(TextUtil.EMPTY_STRING, tag, LogFlag.DL, throwable, msgBlock)
    }

    /**
     * 输入 w 级别日志
     *
     * @param moduleName module name
     * @param tag tag Used to identify the source of a log message.  It usually identifies
     *        the class or activity where the log call occurs.
     * @param throwable An exception to log
     * @param flag Determine write type
     * @param msgBlock The block that produce message you would like logged.
     */
    @JvmStatic
    @JvmOverloads
    fun w(moduleName: String?, tag: String?, throwable: Throwable? = null, @LogFlag flag: Int, msgBlock: LogMsgBlock = emptyMsgBlock) {
        logger.w(moduleName, tag, flag, throwable, msgBlock)
    }

    /**
     * 输入 w 级别日志
     *
     * @param tag tag Used to identify the source of a log message.  It usually identifies
     *        the class or activity where the log call occurs.
     * @param flag Determine write type
     * @param msgBlock The block that produce message you would like logged.
     */
    @JvmStatic
    @JvmOverloads
    fun w(tag: String?, @LogFlag flag: Int, msgBlock: LogMsgBlock = emptyMsgBlock) {
        logger.w(TextUtil.EMPTY_STRING, tag, flag, null, msgBlock)
    }

    /**
     * 输入 e 级别日志
     *
     * @param tag Used to identify the source of a log message.  It usually identifies
     *        the class or activity where the log call occurs.
     * @param throwable An exception to log
     * @param msgBlock The block that produce message you would like logged.
     */
    @JvmStatic
    @JvmOverloads
    @Deprecated(
        message = "过时了，后续不对业务开放",
        replaceWith = ReplaceWith("使用新接口替代")
    )
    fun e(tag: String?, throwable: Throwable? = null, msgBlock: LogMsgBlock = emptyMsgBlock) {
        logger.e(TextUtil.EMPTY_STRING, tag, LogFlag.DL, throwable, msgBlock)
    }

    /**
     * 输入 e 级别日志
     *
     * @param moduleName module name
     * @param tag tag Used to identify the source of a log message.  It usually identifies
     *        the class or activity where the log call occurs.
     * @param throwable An exception to log
     * @param flag Determine write type
     * @param msgBlock The block that produce message you would like logged.
     */
    @JvmStatic
    @JvmOverloads
    fun e(moduleName: String?, tag: String?, throwable: Throwable? = null, @LogFlag flag: Int, msgBlock: LogMsgBlock = emptyMsgBlock) {
        logger.e(moduleName, tag, flag, throwable, msgBlock)
    }

    /**
     * 输入 e 级别日志
     *
     * @param tag tag Used to identify the source of a log message.  It usually identifies
     *        the class or activity where the log call occurs.
     * @param flag Determine write type
     * @param msgBlock The block that produce message you would like logged.
     */
    @JvmStatic
    @JvmOverloads
    fun e(tag: String?, @LogFlag flag: Int, throwable: Throwable? = null, msgBlock: LogMsgBlock = emptyMsgBlock) {
        logger.e(TextUtil.EMPTY_STRING, tag, flag, throwable, msgBlock)
    }

    /**
     * 初始化调试Dev开关[isDevOn]的值
     */
    @JvmStatic
    fun switchDev(devOn: Boolean) {
        logger.switchDev(devOn)
    }

    /**
     * 初始化日志工具箱开关[isLogKitOn]的值
     *
     * @param logkitOn logkit是否支持
     */
    @JvmStatic
    fun switchLogkit(logkitOn: Boolean) {
        logger.switchLogkit(logkitOn)
    }

    /**
     * 初始化日志常开开关[isAlwaysOn]的值
     */
    @JvmStatic
    fun switchAlways(alwaysOn: Boolean) {
        logger.switchAlways(alwaysOn)
    }

    /**
     * 释放计时器
     */
    @JvmStatic
    fun release() {
        logger.release()
    }

    /**
     * 日志回捞
     */
    @JvmStatic
    fun reportLogger(userTraceConfigDto: UserTraceConfigDto?) {
        logger.reportLogger(userTraceConfigDto)
    }

    @JvmStatic
    fun getTime(lastTime: Long): Long = System.currentTimeMillis() - lastTime

    /**
     * 打印代码块的执行时间。
     *
     * log.d(tag) { "[stage] message, cost time 10" }
     */
    @JvmStatic
    @JvmOverloads
    fun <T> costTime(tag: String, stage: String, message: String, block: () -> T): T {
        val startTime = System.currentTimeMillis()
        return block.invoke().apply {
            d(tag) { "[$stage] $message, cost time ${getTime(startTime)}" }
        }
    }

    /**
     * 异步Log监视Reply回复信息。
     * ```
     * 举例：
     * private fun readExif(stage: String, msg: String, fd: ParcelFileDescriptor): ExifEntry? = GLog.watchReplyMethod(TAG, stage, msg) {
     *     val entry = ExifUtils.readExif(fd.fileDescriptor)
     *     GLog.d(TAG) { "$stage, $msg, entry: $entry" }
     *     return@watchReplyMethod entry
     * }
     * ```
     */
    @JvmStatic
    fun <T> watchReplyMethod(tag: String, stage: String, message: String, block: () -> T): T {
        val startTime = System.currentTimeMillis()
        d(tag) { "[$stage] ┏━━ start:  $message ━━┓" }
        val result = block.invoke()
        d(tag) { "[$stage] ┗━━ finish: $message ━━┛ cost time ${getTime(startTime)}" }
        return result
    }

    internal interface ILog {

        fun v(tag: String?, msg: String?, throwable: Throwable?) = v(tag, throwable) { msg }

        fun d(tag: String?, msg: String?, throwable: Throwable?) = d(tag, throwable) { msg }

        fun i(tag: String?, msg: String?, throwable: Throwable?) = i(tag, throwable) { msg }

        fun w(tag: String?, msg: String?, throwable: Throwable?) = w(tag, throwable) { msg }

        fun e(tag: String?, msg: String?, throwable: Throwable?) = e(tag, throwable) { msg }

        /**
         * 输入 v 级别日志
         *
         * @param tag Used to identify the source of a log message.  It usually identifies
         *        the class or activity where the log call occurs.
         * @param throwable An exception to log
         * @param msgBlock The block that produce message you would like logged.
         */
        fun v(tag: String?, throwable: Throwable? = null, msgBlock: LogMsgBlock = emptyMsgBlock) = Unit


        /**
         * 输入 d 级别日志
         *
         * @param tag Used to identify the source of a log message.  It usually identifies
         *        the class or activity where the log call occurs.
         * @param throwable An exception to log
         * @param msgBlock The block that produce message you would like logged.
         */
        fun d(tag: String?, throwable: Throwable? = null, msgBlock: LogMsgBlock = emptyMsgBlock) = Unit


        /**
         * 输入 i 级别日志
         *
         * @param tag Used to identify the source of a log message.  It usually identifies
         *        the class or activity where the log call occurs.
         * @param throwable An exception to log
         * @param msgBlock The block that produce message you would like logged.
         */
        fun i(tag: String?, throwable: Throwable? = null, msgBlock: LogMsgBlock = emptyMsgBlock) = Unit


        /**
         * 输入 w 级别日志
         *
         * @param tag Used to identify the source of a log message.  It usually identifies
         *        the class or activity where the log call occurs.
         * @param throwable An exception to log
         * @param msgBlock The block that produce message you would like logged.
         */
        fun w(tag: String?, throwable: Throwable? = null, msgBlock: LogMsgBlock = emptyMsgBlock) = Unit


        /**
         * 输入 e 级别日志
         *
         * @param tag Used to identify the source of a log message.  It usually identifies
         *        the class or activity where the log call occurs.
         * @param throwable An exception to log
         * @param msgBlock The block that produce message you would like logged.
         */
        fun e(tag: String?, throwable: Throwable? = null, msgBlock: LogMsgBlock = emptyMsgBlock) = Unit
    }

    /**
     * 通过命令行dumpsys命令调用此方法，根据命令来控制日志打印的业务逻辑
     * 控制debug日志开关
     * 控制verbose日志是否打印
     * 控制是否打印线程名称
     * 这个方法是从LogUtil中搬过来的，貌似没有使用了。
     */
    @JvmStatic
    fun dump(pw: PrintWriter, args: Array<String?>) {
        if (args.isEmpty()) {
            return
        }
        var opti = 0
        var verbose = false
        while (opti < args.size) {
            val opt = args[opti]
            if (opt.isNullOrEmpty() || opt[0] != '-') {
                break
            }
            opti++
            when (opt) {
                "-v" -> {
                    verbose = true
                    logger.switchVerbose(true)
                    GProperty.DEBUG = true
                    pw.println("sVerbose open...")
                }

                "-d" -> {
                    GProperty.DEBUG = true
                    pw.println("sDebug open...")
                }

                else -> pw.println("Unknown argument: $opt; use -h for help")
            }
        }
        pw.println("sDebug:" + GProperty.DEBUG + " sVerbose:" + verbose)
    }

    /**
     * 打印堆栈
     * debug包或者命令强行开启的才可以
     * 正式包只打印一条报错信息
     */
    @JvmStatic
    fun printTrack(tag: String, message: String = TextUtil.EMPTY_STRING) {
        if (BuildConfig.DEBUG || DEBUG_FORCE_PRINT_TRACK) {
            w(tag, LogFlag.DL) {
                "track:${Log.getStackTraceString(Throwable(message))}"
            }
        } else {
            w(tag, LogFlag.DL) {
                "track:${Throwable(message).message}"
            }
        }
    }

    /**
     * 打印带超链接的 log
     * log 中变蓝色的部分可以点击跳转源代码
     * 仅 debug 版本使用
     */
    fun printLink(message: String? = null) {
        if (BuildConfig.DEBUG.not()) return
        val stackTrace = Thread.currentThread().stackTrace
        val caller = stackTrace[LINK_STACK]

        val fileName = caller.fileName
        val lineNumber = caller.lineNumber
        val className = caller.javaClass.simpleName
        val methodName = caller.methodName

        e("Link", "debug hit $className.$methodName($fileName:$lineNumber) , msg : $message")
    }
}