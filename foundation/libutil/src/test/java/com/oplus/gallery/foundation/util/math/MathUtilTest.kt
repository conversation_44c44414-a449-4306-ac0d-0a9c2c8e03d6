/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MathUtilTest
 ** Description: MathUtilTest
 ** Version: 1.0
 ** Date : 2022/4/23
 ** Author: dingyong@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  dingyong@Apps.Gallery3D      2022/04/23    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.foundation.util.math

import com.oplus.gallery.foundation.util.math.MathUtil.ceilAlignment
import com.oplus.gallery.foundation.util.math.MathUtil.closestAlignment
import com.oplus.gallery.foundation.util.math.MathUtil.floorAlignment
import com.oplus.gallery.foundation.util.math.MathUtil.toScale
import org.junit.Assert
import org.junit.Test

class MathUtilTest {

    @Test
    fun should_equal_division_number() {
        val result1 = MathUtil.equalDivisionNumber(15000, 6).toString()
        val result2 = MathUtil.equalDivisionNumber(6752, 6).toString()
        val result3 = MathUtil.equalDivisionNumber(1150, 6).toString()
        Assert.assertEquals(result1, "[0, 3000, 6000, 9000, 12000, 15000]")
        Assert.assertEquals(result2, "[0, 1350, 2701, 4051, 5402, 6752]")
        Assert.assertEquals(result3, "[0, 230, 460, 690, 920, 1150]")
    }

    @Test
    fun should_two_float_number_equal() {
        val float1 = 10.00001f
        val float2 = 10.00001f
        val float3 = 10.00002f
        val result1 = MathUtil.equalWithinTolerance(float1, float2)
        val result2 = MathUtil.equalWithinTolerance(float1, float3)
        Assert.assertTrue(result1)
        Assert.assertFalse(result2)
    }

    @Test
    fun should_two_double_number_equal() {
        val double1 = 10.000001
        val double2 = 10.000001
        val double3 = 10.000002
        val result1 = MathUtil.equalWithinTolerance(double1, double2)
        val result2 = MathUtil.equalWithinTolerance(double1, double3)
        Assert.assertTrue(result1)
        Assert.assertFalse(result2)
    }

    @Test
    fun should_get_zero_when_down_to_two_Decimal_with_zero() {
        // given inputNum is zero
        val inputNum = 0.0

        // when
        val resultNum = inputNum.toScale(2)
        //then
        Assert.assertEquals(0, resultNum.compareTo(inputNum))
    }

    @Test
    fun should_get_90_when_down_to_0_decimal_with_89_5() {
        //given 5入
        val inputNum = 89.5
        // when
        val resultNum = inputNum.toScale(0)
        // then
        Assert.assertEquals(0, 90.compareTo(resultNum))
    }


    @Test
    fun should_get_7_35_when_down_to_2_decimal_with_7_347() {
        // given 7进1
        val inputNum = 7.347
        // when
        val resultNum = inputNum.toScale(2)
        // then
        Assert.assertEquals(0, 7.35.compareTo(resultNum))
    }

    @Test
    fun should_get_7_34_when_down_to_2_decimal_with_7_344() {
        //given 4舍
        val inputNum = 7.344
        // when
        val resultNum = inputNum.toScale(2)
        // then
        Assert.assertEquals(0, 7.34.compareTo(resultNum))
    }

    @Test
    fun `should return expected when covariance`() {
        // given
        val expected = 0.25F
        val list = FloatArray(2) { it.toFloat() }
        // when
        val actual = MathUtil.covariance(list, list)

        // then
        Assert.assertEquals(expected, actual)
    }

    @Test
    fun `should return NAN when covariance and input empty array`() {
        // given
        val expected = Float.NaN
        val listA = FloatArray(2) { it.toFloat() }
        val listB = FloatArray(0)
        // when
        val actual = MathUtil.covariance(listA, listB)

        // then
        Assert.assertEquals(expected, actual)
    }

    // ========== closestAlignment 测试 ==========

    @Test
    fun `closestAlignment should return closest power of 2 alignment`() {
        // given & when & then - closestAlignment(2) 对齐到 4 的倍数 (2^2)
        Assert.assertEquals(0, 0.closestAlignment(2))
        Assert.assertEquals(2, 1.closestAlignment(2))
        Assert.assertEquals(2, 2.closestAlignment(2))
        Assert.assertEquals(4, 3.closestAlignment(2))
        Assert.assertEquals(4, 4.closestAlignment(2))
        Assert.assertEquals(6, 5.closestAlignment(2))
        Assert.assertEquals(6, 6.closestAlignment(2))
        Assert.assertEquals(8, 7.closestAlignment(2))
        Assert.assertEquals(8, 8.closestAlignment(2))
    }

    @Test
    fun `closestAlignment should return closest power of 4 alignment`() {
        // given & when & then - closestAlignment(4) 对齐到 16 的倍数 (2^4)
        Assert.assertEquals(0, 0.closestAlignment(4))
        Assert.assertEquals(0, 1.closestAlignment(4))
        Assert.assertEquals(4, 2.closestAlignment(4))
        Assert.assertEquals(4, 3.closestAlignment(4))
        Assert.assertEquals(4, 4.closestAlignment(4))
        Assert.assertEquals(4, 5.closestAlignment(4))
        Assert.assertEquals(8, 6.closestAlignment(4))
        Assert.assertEquals(8, 7.closestAlignment(4))
        Assert.assertEquals(8, 8.closestAlignment(4))
        Assert.assertEquals(16, 15.closestAlignment(4))
        Assert.assertEquals(16, 16.closestAlignment(4))
    }

    @Test
    fun `closestAlignment should return closest power of 8 alignment`() {
        // given & when & then - closestAlignment(8) 对齐到 256 的倍数 (2^8)
        Assert.assertEquals(0, 0.closestAlignment(8))
        Assert.assertEquals(0, 1.closestAlignment(8))
        Assert.assertEquals(0, 2.closestAlignment(8))
        Assert.assertEquals(0, 3.closestAlignment(8))
        Assert.assertEquals(8, 4.closestAlignment(8))
        Assert.assertEquals(104, 100.closestAlignment(8))
        Assert.assertEquals(200, 200.closestAlignment(8))
        Assert.assertEquals(256, 255.closestAlignment(8))
        Assert.assertEquals(256, 256.closestAlignment(8))
        Assert.assertEquals(256, 257.closestAlignment(8))
    }

    @Test
    fun `closestAlignment should handle negative numbers`() {
        // given & when & then
        Assert.assertEquals(0, (-1).closestAlignment(2))
        Assert.assertEquals(0, (-2).closestAlignment(4))
        Assert.assertEquals(0, (-5).closestAlignment(8))
    }

    @Test
    fun `closestAlignment should handle large numbers`() {
        // given & when & then
        Assert.assertEquals(1000, 1000.closestAlignment(2))
        Assert.assertEquals(1020, 1020.closestAlignment(4))
        Assert.assertEquals(1024, 1024.closestAlignment(8))
    }

    // ========== floorAlignment 测试 ==========

    @Test
    fun `floorAlignment should return floor power of 2 alignment`() {
        // given & when & then - floorAlignment(2) 向下对齐到 4 的倍数 (2^2)
        Assert.assertEquals(0, 0.floorAlignment(2))
        Assert.assertEquals(0, 1.floorAlignment(2))
        Assert.assertEquals(0, 2.floorAlignment(2))
        Assert.assertEquals(0, 3.floorAlignment(2))
        Assert.assertEquals(4, 4.floorAlignment(2))
        Assert.assertEquals(4, 5.floorAlignment(2))
        Assert.assertEquals(4, 6.floorAlignment(2))
        Assert.assertEquals(4, 7.floorAlignment(2))
        Assert.assertEquals(8, 8.floorAlignment(2))
    }

    @Test
    fun `floorAlignment should return floor multiple of 4`() {
        // given & when & then
        Assert.assertEquals(0, 0.floorAlignment(4))
        Assert.assertEquals(0, 1.floorAlignment(4))
        Assert.assertEquals(0, 2.floorAlignment(4))
        Assert.assertEquals(0, 3.floorAlignment(4))
        Assert.assertEquals(4, 4.floorAlignment(4))
        Assert.assertEquals(4, 5.floorAlignment(4))
        Assert.assertEquals(4, 6.floorAlignment(4))
        Assert.assertEquals(4, 7.floorAlignment(4))
        Assert.assertEquals(8, 8.floorAlignment(4))
    }

    @Test
    fun `floorAlignment should return floor multiple of 8`() {
        // given & when & then
        Assert.assertEquals(0, 0.floorAlignment(8))
        Assert.assertEquals(0, 1.floorAlignment(8))
        Assert.assertEquals(0, 2.floorAlignment(8))
        Assert.assertEquals(0, 3.floorAlignment(8))
        Assert.assertEquals(0, 4.floorAlignment(8))
        Assert.assertEquals(0, 5.floorAlignment(8))
        Assert.assertEquals(0, 6.floorAlignment(8))
        Assert.assertEquals(0, 7.floorAlignment(8))
        Assert.assertEquals(8, 8.floorAlignment(8))
        Assert.assertEquals(8, 9.floorAlignment(8))
        Assert.assertEquals(8, 15.floorAlignment(8))
        Assert.assertEquals(16, 16.floorAlignment(8))
    }

    @Test
    fun `floorAlignment should handle negative numbers`() {
        // given & when & then
        Assert.assertEquals(0, (-1).floorAlignment(2))
        Assert.assertEquals(0, (-2).floorAlignment(4))
        Assert.assertEquals(0, (-5).floorAlignment(8))
    }

    @Test
    fun `floorAlignment should handle large numbers`() {
        // given & when & then
        Assert.assertEquals(1000, 1000.floorAlignment(2))
        Assert.assertEquals(1020, 1020.floorAlignment(4))
        Assert.assertEquals(1024, 1024.floorAlignment(8))
        Assert.assertEquals(1016, 1023.floorAlignment(8))
    }

    // ========== ceilAlignment 测试 ==========

    @Test
    fun `ceilAlignment should return ceil power of 2 alignment`() {
        // given & when & then - ceilAlignment(2) 向上对齐到 4 的倍数 (2^2)
        Assert.assertEquals(0, 0.ceilAlignment(2))
        Assert.assertEquals(4, 1.ceilAlignment(2))
        Assert.assertEquals(4, 2.ceilAlignment(2))
        Assert.assertEquals(4, 3.ceilAlignment(2))
        Assert.assertEquals(4, 4.ceilAlignment(2))
        Assert.assertEquals(8, 5.ceilAlignment(2))
        Assert.assertEquals(8, 6.ceilAlignment(2))
        Assert.assertEquals(8, 7.ceilAlignment(2))
        Assert.assertEquals(8, 8.ceilAlignment(2))
    }

    @Test
    fun `ceilAlignment should return ceil multiple of 4`() {
        // given & when & then
        Assert.assertEquals(0, 0.ceilAlignment(4))
        Assert.assertEquals(4, 1.ceilAlignment(4))
        Assert.assertEquals(4, 2.ceilAlignment(4))
        Assert.assertEquals(4, 3.ceilAlignment(4))
        Assert.assertEquals(4, 4.ceilAlignment(4))
        Assert.assertEquals(8, 5.ceilAlignment(4))
        Assert.assertEquals(8, 6.ceilAlignment(4))
        Assert.assertEquals(8, 7.ceilAlignment(4))
        Assert.assertEquals(8, 8.ceilAlignment(4))
    }

    @Test
    fun `ceilAlignment should return ceil multiple of 8`() {
        // given & when & then
        Assert.assertEquals(0, 0.ceilAlignment(8))
        Assert.assertEquals(8, 1.ceilAlignment(8))
        Assert.assertEquals(8, 2.ceilAlignment(8))
        Assert.assertEquals(8, 3.ceilAlignment(8))
        Assert.assertEquals(8, 4.ceilAlignment(8))
        Assert.assertEquals(8, 5.ceilAlignment(8))
        Assert.assertEquals(8, 6.ceilAlignment(8))
        Assert.assertEquals(8, 7.ceilAlignment(8))
        Assert.assertEquals(8, 8.ceilAlignment(8))
        Assert.assertEquals(16, 9.ceilAlignment(8))
        Assert.assertEquals(16, 15.ceilAlignment(8))
        Assert.assertEquals(16, 16.ceilAlignment(8))
    }

    @Test
    fun `ceilAlignment should handle negative numbers`() {
        // given & when & then
        Assert.assertEquals(0, (-1).ceilAlignment(2))
        Assert.assertEquals(0, (-2).ceilAlignment(4))
        Assert.assertEquals(0, (-5).ceilAlignment(8))
    }

    @Test
    fun `ceilAlignment should handle large numbers`() {
        // given & when & then
        Assert.assertEquals(1002, 1000.ceilAlignment(2))
        Assert.assertEquals(1024, 1020.ceilAlignment(4))
        Assert.assertEquals(1024, 1024.ceilAlignment(8))
        Assert.assertEquals(1024, 1023.ceilAlignment(8))
    }

    // ========== 扩展函数测试 ==========

    @Test
    fun `Number extension functions should work correctly`() {
        // given
        val intValue = 5
        val floatValue = 5.7f
        val doubleValue = 5.3

        // when & then - closestAlignment
        Assert.assertEquals(4, intValue.closestAlignment(2))
        Assert.assertEquals(4, floatValue.closestAlignment(2))
        Assert.assertEquals(4, doubleValue.closestAlignment(2))

        // when & then - floorAlignment
        Assert.assertEquals(4, intValue.floorAlignment(2))
        Assert.assertEquals(4, floatValue.floorAlignment(2))
        Assert.assertEquals(4, doubleValue.floorAlignment(2))

        // when & then - ceilAlignment
        Assert.assertEquals(6, intValue.ceilAlignment(2))
        Assert.assertEquals(6, floatValue.ceilAlignment(2))
        Assert.assertEquals(6, doubleValue.ceilAlignment(2))
    }

    @Test
    fun `alignment methods should handle edge cases`() {
        // given & when & then - zero values
        Assert.assertEquals(0, 0.closestAlignment(2))
        Assert.assertEquals(0, 0.floorAlignment(2))
        Assert.assertEquals(0, 0.ceilAlignment(2))

        // given & when & then - exact multiples
        Assert.assertEquals(8, 8.closestAlignment(8))
        Assert.assertEquals(8, 8.floorAlignment(8))
        Assert.assertEquals(8, 8.ceilAlignment(8))

        // given & when & then - power of 2 multiples
        Assert.assertEquals(16, 15.closestAlignment(4))
        Assert.assertEquals(12, 15.floorAlignment(4))
        Assert.assertEquals(16, 15.ceilAlignment(4))
    }


}