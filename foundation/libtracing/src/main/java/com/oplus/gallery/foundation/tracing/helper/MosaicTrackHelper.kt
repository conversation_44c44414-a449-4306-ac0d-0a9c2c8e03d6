/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MosaicTrackHelper.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2022/9/23
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lima<PERSON>@Apps.Gallery              2022/9/23      1.0		   INIT
 *********************************************************************************/
package com.oplus.gallery.foundation.tracing.helper

import androidx.annotation.WorkerThread
import com.oplus.gallery.foundation.tracing.BuildConfig
import com.oplus.gallery.foundation.tracing.Tracker
import com.oplus.gallery.foundation.tracing.constant.MosaicTrackConstant
import com.oplus.gallery.foundation.tracing.constant.MosaicTrackConstant.KEY.REGION_CLICK_REMOVE_COUNT
import com.oplus.gallery.foundation.tracing.constant.MosaicTrackConstant.KEY.AUTO_MOSAIC_VERSION
import com.oplus.gallery.foundation.tracing.constant.MosaicTrackConstant.KEY.IS_MANUAL_USED
import com.oplus.gallery.foundation.tracing.constant.MosaicTrackConstant.KEY.REGION_CLICK_ADD_COUNT
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant.Key.PAGE_CLICK_PHOTO_LABEL
import com.oplus.gallery.foundation.tracing.track.SingleTrack
import com.oplus.gallery.foundation.util.debug.GLog

/**
 * 一键打码埋点的辅助类
 */
object MosaicTrackHelper {

    private const val TAG = "MosaicTrackHelper"

    /**
     * 隐私打码按钮使用情况埋点事件
     *
     * @param privacyMosaicSwitch
     * @param photoLabel 图片的标签类型
     * @param callType 自动打码开关变化的类型
     */
    @JvmStatic
    fun trackPrivacyMosaicUsage(privacyMosaicSwitch: Boolean, photoLabel: String?, callType: Int) {
        if (BuildConfig.DEBUG) {
            GLog.d(
                TAG, "privacyMosaicSwitch:$privacyMosaicSwitch, photoLabel:$photoLabel, callType:$callType"
            )
        }
        track(MosaicTrackConstant.EventId.AUTO_MOSAIC_USAGE) { track ->
            track.putProperty(MosaicTrackConstant.KEY.AUTO_MOSAIC_SWITCH, privacyMosaicSwitch)
            track.putProperty(MosaicTrackConstant.KEY.CALL_TYPE, callType)
            photoLabel?.let {
                track.putProperty(PAGE_CLICK_PHOTO_LABEL, it)
            }
            track.save()
        }
    }

    /**
     * 自动打码样式偏好埋点事件
     *
     * @param autoMosaicPattern
     */
    @JvmStatic
    fun trackAutoMosaicPatternPref(autoMosaicPattern: String) {
        if (BuildConfig.DEBUG) {
            GLog.d(TAG, "autoMosaicPattern:$autoMosaicPattern")
        }
        track(MosaicTrackConstant.EventId.AUTO_MOSAIC_PATTERN_PREF) { track ->
            track.putProperty(MosaicTrackConstant.KEY.AUTO_MOSAIC_PATTERN, autoMosaicPattern)
            track.save()
        }
    }

    /**
     * 【自动打码】按钮出现，自动触发
     */
    @JvmStatic
    fun trackShowAutoMosaicButton(photoLabel: String?, privacyParserSkillVersion: Int) {
        if (BuildConfig.DEBUG) {
            GLog.d(TAG, "privacyParserSkillVersion:$privacyParserSkillVersion, photoLabel:$photoLabel")
        }
        track(MosaicTrackConstant.EventId.AUTO_MOSAIC_SHOW) { track ->
            photoLabel?.let {
                track.putProperty(PAGE_CLICK_PHOTO_LABEL, it)
            }
            track.putProperty(AUTO_MOSAIC_VERSION, privacyParserSkillVersion)
            track.save()
        }
    }

    /**
     * 统计马赛克各功能的使用情况
     * @param photoLabel 图片标签
     * @param isManualUsed 是否使用过手动打码
     * @param addCount 单击图片区域添加马赛克的数量
     * @param removeCount 单击图片区域移除马赛克的数量
     */
    @JvmStatic
    fun trackMosaicFunctionUsage(
        photoLabel: String?,
        isManualUsed: Boolean,
        addCount: Int,
        removeCount: Int
    ) {
        if (BuildConfig.DEBUG) {
            GLog.d(TAG, "hasManual:$isManualUsed,clickAutoMosaicCount:$addCount,unClickAutoMosaicCount:$removeCount")
        }
        track(MosaicTrackConstant.EventId.MANUAL_MOSAIC_USAGE) { track ->
            photoLabel?.let {
                track.putProperty(PAGE_CLICK_PHOTO_LABEL, it)
            }
            track.putProperty(REGION_CLICK_ADD_COUNT, addCount)
            track.putProperty(REGION_CLICK_REMOVE_COUNT, removeCount)
            track.putProperty(IS_MANUAL_USED, isManualUsed)
            track.save()
        }
    }

    private fun track(eventId: String, @WorkerThread func: ((SingleTrack) -> Unit)? = null) {
        Tracker.trackStore.createSingleTrack(
            event = eventId,
            type = MosaicTrackConstant.TYPE_PICTURE_EDIT,
            func = func
        )
    }
}

/**
 * 马赛克各功能使用情况统计，仅用作埋点统计
 */
class MosaicUsageStatistics {

    /**
     * 单击图片区域添加马赛克的数量
     */
    var addCount = 0

    /**
     * 单击图片区域移除马赛克的数量
     */
    var removeCount = 0

    /**
     * 是否使用过手动打码
     */
    var isManualUsed = false
}