/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - TrackStore.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/12/15
 ** Author: hucanh<PERSON>@Apps.Gallery
 ** TAG: OPLUS_TRACK
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** huca<PERSON><PERSON>@Apps.Gallery		2020/02/24		1.0		INIT
 *********************************************************************************/

package com.oplus.gallery.foundation.tracing

import androidx.annotation.WorkerThread
import androidx.collection.ArrayMap
import com.oplus.gallery.foundation.tracing.template.DcsSingleTrackTemplate
import com.oplus.gallery.foundation.tracing.template.SingleTrackTemplate
import com.oplus.gallery.foundation.tracing.template.StatisticTrackTemplate
import com.oplus.gallery.foundation.tracing.track.LinkTrack
import com.oplus.gallery.foundation.tracing.track.SingleTrack
import com.oplus.gallery.foundation.tracing.track.StatisticTrack
import com.oplus.gallery.foundation.tracing.track.Track
import com.oplus.gallery.standard_lib.app.TrackScope
import kotlinx.coroutines.launch
import kotlin.reflect.KClass

/**
 * 埋点的存储库
 */
class TrackStore {

    private val trackMap: MutableMap<KClass<*>, MutableMap<String, Track>> = ArrayMap()

    @Suppress("UNCHECKED_CAST")
    @Synchronized
    fun <T : Track> getTrack(kClass: KClass<out T>, trackId: String): T? {
        return trackMap.computeIfAbsent(kClass) {
            ArrayMap()
        }[trackId] as? T
    }

    fun <T : Track> getTrackAsync(kClass: KClass<out T>, trackId: String, @WorkerThread func: ((T) -> Unit)) {
        TrackScope.launch {
            getTrack(kClass, trackId)?.apply(func)
        }
    }

    @Suppress("UNCHECKED_CAST")
    @Synchronized
    fun <T : Track> getTracks(kClass: KClass<out T>): Collection<T> {
        return trackMap[kClass]?.values as? Collection<T> ?: emptyList()
    }

    @Suppress("UNCHECKED_CAST")
    @Synchronized
    fun getAllTracks(): MutableMap<KClass<*>, MutableMap<String, Track>> {
        val map = HashMap<KClass<*>, MutableMap<String, Track>>()
        trackMap.forEach { (key, value) ->
            map[key] = HashMap(value)
        }
        return map
    }

    /**
     * 创建一个单点埋点
     * @param event 事件的id，对应埋点的事件名称
     * @param type 事件的大类，对应埋点所在后台的分类，如时间轴埋点，大图埋点等
     * @param template 埋点的模板
     * @param autoSaveWhenLaunch 当埋点没有执行上报（save）时，会在应用下次启动时进行上报
     * @return 返回一个SingleTrack对象，可通过此对象添加埋点字段
     */
    fun createSingleTrack(
        event: String,
        type: String,
        template: SingleTrackTemplate = DcsSingleTrackTemplate(),
        autoSaveWhenLaunch: Boolean = false,
        @WorkerThread func: ((SingleTrack) -> Unit)? = null
    ): SingleTrack {
        val track = SingleTrack(event, type, template, autoSaveWhenLaunch)
        TrackScope.launch {
            addTrack(SingleTrack::class, track)
            func?.let { it(track) }
        }
        return track
    }

    /**
     * 创建一个单点埋点
     * @param event 事件的id，对应埋点的事件名称
     * @param type 事件的大类，对应埋点所在后台的分类，如时间轴埋点，大图埋点等
     * @param template 埋点的模板
     * @param autoSaveWhenLaunch 当埋点没有执行上报（save）时，会在应用下次启动时进行上报
     * @return 返回一个LinkTrack对象，可通过此对象添加埋点字段
     */
    fun createLinkTrack(
        event: String,
        type: String,
        template: SingleTrackTemplate = SingleTrackTemplate.INSTANCE,
        autoSaveWhenLaunch: Boolean = false,
        @WorkerThread func: ((LinkTrack) -> Unit)? = null
    ): LinkTrack {
        val track = LinkTrack(event, type, template, autoSaveWhenLaunch)
        addTrack(LinkTrack::class, track)
        func?.apply {
            TrackScope.launch { this@apply(track) }
        }
        return track
    }

    /**
     * 创建一个单点埋点
     * @param event 事件的id，对应埋点的事件名称
     * @param type 事件的大类，对应埋点所在后台的分类，如时间轴埋点，大图埋点等
     * @param template 埋点的模板
     * @param autoSaveWhenLaunch 当埋点没有执行上报（save）时，会在应用下次启动时进行上报
     * @return 返回一个StatisticTrack对象，可通过此对象添加埋点字段
     */
    fun createStatisticTrack(
        event: String,
        type: String,
        template: StatisticTrackTemplate = StatisticTrackTemplate.INSTANCE,
        autoSaveWhenLaunch: Boolean = true,
        @WorkerThread func: ((StatisticTrack) -> Unit)? = null
    ): StatisticTrack {
        val track = StatisticTrack(event, type, template, autoSaveWhenLaunch)
        addTrack(StatisticTrack::class, track)
        func?.apply {
            TrackScope.launch { this@apply(track) }
        }
        return track
    }

    @Synchronized
    fun <T : Track> addTrack(kClass: KClass<T>, track: T) {
        trackMap.computeIfAbsent(kClass) {
            ArrayMap()
        }[track.trackId] = track
    }

    @Synchronized
    fun <T : Track> removeTrack(kClass: KClass<T>, trackId: String): Track? {
        return trackMap[kClass]?.remove(trackId)
    }
}