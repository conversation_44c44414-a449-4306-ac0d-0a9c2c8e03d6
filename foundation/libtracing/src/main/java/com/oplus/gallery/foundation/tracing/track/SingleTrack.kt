/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - NormalTrack.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/12/15
 ** Author: hucanh<PERSON>@Apps.Gallery
 ** TAG: OPLUS_TRACK
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** hucanh<PERSON>@Apps.Gallery		2020/02/24		1.0		INIT
 *********************************************************************************/

package com.oplus.gallery.foundation.tracing.track

import com.google.gson.annotations.SerializedName
import com.oplus.gallery.foundation.tracing.TrackEntry
import com.oplus.gallery.foundation.tracing.Tracker
import com.oplus.gallery.foundation.tracing.template.SingleTrackTemplate
import java.util.Collections

open class SingleTrack(
    event: String,
    type: String,
    /**
     * only for gson var ,others val
     */
    @SerializedName("template")
    private var template: SingleTrackTemplate = SingleTrackTemplate.INSTANCE,
    autoSaveWhenLaunch: Boolean = false
) : Track(
    event = event,
    type = type,
    autoSaveWhenLaunch = autoSaveWhenLaunch
) {
    /**
     * only for gson
     */
    constructor() : this(NONE_EVENT, NONE_TYPE)

    @SerializedName("parentTrackId")
    internal var parentTrackId: String? = null

    @SerializedName("trackItems")
    private val trackItems = Collections.synchronizedMap(HashMap<String, Any?>())

    @SerializedName("tmpItems")
    private val tmpItems = Collections.synchronizedMap(HashMap<String, Any?>())

    override fun dealPack(): TrackEntry {
        return template.pack(trackId, type, event, trackItems)
    }

    fun putProperty(key: String, value: Any?) {
        trackItems[key] = value
    }

    fun putProperties(tracks: Map<String, String?>) {
        trackItems.putAll(tracks)
    }

    fun putItemTimeCostStart(key: String) {
        tmpItems[key] = System.currentTimeMillis()
    }

    fun putItemTimeCostEnd(key: String) {
        (tmpItems.remove(key) as? Long)?.apply {
            trackItems[key] = (System.currentTimeMillis() - this).toString()
        }
    }

    fun putItemCountIncrease(key: String, increase: Int = 1) {
        val value = (trackItems[key] as? Int) ?: 0
        trackItems[key] = (value + increase).toString()
    }

    fun save() {
        isComplete = true
        parentTrackId?.apply {
            Tracker.trackStore.getTrack(StatisticTrack::class, this)?.apply {
                save(this@SingleTrack)
            }
        } ?: Tracker.send(this)
    }
}
