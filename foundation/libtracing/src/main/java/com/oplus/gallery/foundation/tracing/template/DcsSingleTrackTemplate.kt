/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DcsSingleTrackTemplate.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/12/24
 ** Author: <EMAIL>
 ** TAG: OPLUS_TRACK
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** h<PERSON><PERSON><PERSON>@Apps.Gallery		2020/02/24		1.0		INIT
 *********************************************************************************/

package com.oplus.gallery.foundation.tracing.template

import com.google.gson.annotations.SerializedName
import com.oplus.gallery.foundation.tracing.TrackEntry

open class DcsSingleTrackTemplate : SingleTrackTemplate() {
    @SerializedName("clientTimeStamp")
    private var clientTimeStamp = System.currentTimeMillis()

    override fun pack(trackId: String, type: String, event: String, entry: MutableMap<String, Any?>): TrackEntry {
        return super.pack(trackId, type, event, entry)
    }
}