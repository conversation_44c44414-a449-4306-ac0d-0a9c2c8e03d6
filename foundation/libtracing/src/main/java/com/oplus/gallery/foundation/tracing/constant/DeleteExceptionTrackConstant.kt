/********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DeleteExceptionTrackConstant
 ** Description:
 ** Version: 1.0
 ** Date: 2020-12-29
 ** Author: zhongxuechang@Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_REFACTOR
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** zhongxuechang@Apps.Gallery3D    2020/12/29     1.0        OPLUS_FEATURE_REFACTOR
 ********************************************************************************/

package com.oplus.gallery.foundation.tracing.constant

object DeleteExceptionTrackConstant {

    const val TYPE_DELETE_EXCEPTION = "2006010"

    /**
     * 事件的id，对应埋点的事件名称
     */
    object EventId {
        const val RECYCLE_DATA_FILE = "2006010002"

        /**
         * 彻底删除回收站失败
         */
        const val DELETE_RECYCLE_FAILED = "2006010006"
    }

    /**
     * 事件字段的 key
     */
    object Key {
        const val IS_SDCARD = "is_sdcard"
        const val TOTAL_COUNT = "total_count"
        const val CURRENT_INDEX = "current"
        const val IS_EUEX = "euex"
        const val FILE_PATH = "file_path"
        /**
         * 通过媒体库删除回收站文件失败的数量
         */
        const val MEDIA_STORE_DELETE_FAILED_COUNT = "media_store_delete_failed_count"
        const val MEDIA_STORE_DELETE_FAILED_IMAGE_COUNT = "media_store_delete_failed_image_count"
        const val MEDIA_STORE_DELETE_FAILED_VIDEO_COUNT = "media_store_delete_failed_video_count"
    }
}