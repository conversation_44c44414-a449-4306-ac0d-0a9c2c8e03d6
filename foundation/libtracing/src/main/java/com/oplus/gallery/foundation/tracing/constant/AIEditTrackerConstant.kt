/********************************************************************************
 ** Copyright (C), 2008-2024, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: - AIEditTrackerConstant.kt
 ** Description: 画质增强、去反光、修复埋点事件常量
 ** Version: 1.0
 ** Date : 2024/08/13
 ** Author: lizhenya
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  lizhenya                      2024/08/13      1.0      build this module
 *********************************************************************************/
package com.oplus.gallery.foundation.tracing.constant

/**
 * 画质增强、去反光、修复埋点事件常量
 */
object AIEditTrackerConstant {
    /**
     * 图片编辑分类ID
     */
    const val PICTURE_EDIT_TYPE = "2006025"

    /**
     * 事件名称：单次增强（去反光、修复）功能事件埋点（每次增强、去反光、修复完成时触发）
     */
    const val EVENT_ID_ENHANCE_REPAIR_DEBLUR = "2006025040"

    object Key {
        /**
         * 图片分辨率(long:0-1000000000000)
         */
        const val IMG_WH = "img_wh"

        /**
         * AI画质增强(或者AI去反光、去修复)耗时（int:0-10000）
         */
        const val COST_TIME = "time"

        /**
         * 是否保存增强（或去反光、修复）后的图片 boolean:true or false
         */
        const val IS_SAVE_IMG = "is_save"

        /**
         * 类型
         * 0：去模糊
         * 1：去反光
         * 2: 画质增强
         */
        const val AI_TYPE = "type"

        /**
         * 增强入口类型
         * 0：编辑菜单入口
         * 1：大图推荐入口
         * 2：旋转裁切增强入口
         * 3: 大图沉浸入口
         */
        const val ENTER_TYPE = "etype"
    }

    object Value {
        /**
         * 进入方式:从编辑菜单进入
         */
        const val ENTER_TYPE_MENU = 0

        /**
         * 进入方式:从大图推荐进入
         */
        const val ENTER_TYPE_RECOMMEND = 1

        /**
         * 进入方式:从裁剪增强进入
         */
        const val ENTER_TYPE_CROP = 2

        /**
         * 进入方式:从大图沉浸进入
         */
        const val ENTER_TYPE_IMMERSE = 3

        /**
         * 修复模糊操作类型
         */
        const val AI_REPAIR_DE_BLUR_TYPE = 0

        /**
         * 去反光操作类型
         */
        const val AI_REPAIR_DE_REFLECTION_TYPE = 1

        /**
         * 画质增强操作类型
         */
        const val AI_ENHANCE_TYPE = 2

        /**
         * realme去眩光操作类型
         */
        const val AI_REPAIR_DE_GLARE_TYPE = 3

        /**
         * 去雾去蒙操作类型
         */
        const val AI_REPAIR_DE_FOG_MONGO_TYPE = 4

        /**
         * 异常类型:无效值
         */
        const val ERROR_INVALID_VALUE = -1

        /**
         * 异常code:下载失败
         */
        const val ERROR_CODE_GALLERY_DOWNLOAD = 1

        /**
         * 异常code:算法下线
         */
        const val ERROR_CODE_GALLERY_OFFLINE = 2

        /**
         * 异常code:禁用状态
         */
        const val ERROR_CODE_GALLERY_BANNED = 3
    }
}

/**
 * 相册AI编辑（画质增强、修复、去反光）导致失败的枚举
 * code:失败code
 * msg:失败描述（自己定义msg描述清楚错误场景即可）
 */
enum class AIEditGalleryError(val code: Int, val msg: String) {
    ERROR_DOWNLOAD_PREVIEW_BITMAP(
        AIEditTrackerConstant.Value.ERROR_CODE_GALLERY_DOWNLOAD,
        "download preview bitmap failed"
    ),
    ERROR_ALGORITHM_OFFLINE(
        AIEditTrackerConstant.Value.ERROR_CODE_GALLERY_OFFLINE,
        "current Algorithm has offline"
    ),
    ERROR_FUNCTION_BANNED(
        AIEditTrackerConstant.Value.ERROR_CODE_GALLERY_BANNED,
        "current function has banned"
    )
}