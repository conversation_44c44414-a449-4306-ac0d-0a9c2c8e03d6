plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-kapt'
}

apply from: "${rootDir}/gradle/libCommon.gradle"

// 这部分一定要在android的前面
ext {
    mavenDescription = "相册埋点工具库"
    mavenGroupId = mavenGroupName
}

android {
    compileSdkVersion prop_compileSdkVersion
    buildToolsVersion prop_buildToolsVersion
    resourcePrefix project.name + "_"
    namespace 'com.oplus.gallery.foundation.tracing'

    defaultConfig {
        minSdkVersion prop_minSdkVersion
        targetSdkVersion prop_targetSdkVersion

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    //忽略，olint扫描项:代码混淆风险
    lintOptions {
        disable  "MinifyEnabled"
    }
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])
    implementationProject(':foundation:libutil')
    implementation "com.oplus.statistics:track:${staticticsVersion}"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:$kotlinxCoroutinesCore"
    implementation "com.google.code.gson:gson:$gsonVersion"
    implementation "androidx.fragment:fragment-ktx:$fragmentKtxVersion"
}