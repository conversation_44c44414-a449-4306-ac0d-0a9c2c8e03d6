/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PageViewModel.kt
 ** Description : 切片页面ViewModel架构抽象
 ** Version     : 1.0
 ** Date        : 2022/04/19
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>@Apps.Gallery              2022/04/19  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.foundation.arch.sectionpage

/**
 * 切片页面ViewModel
 */
interface ISectionViewModel {

    /**
     * 被此页面切片依附的主页面的ViewModel
     */
    val pageViewModel: IPageViewModel

    /**
     * 在Lifecycle.EVENT.ON_CREATE时回调
     */
    fun onCreate(): Unit = Unit

    /**
     * 当SectionViewModel被首次调用时回调
     */
    fun onBind(): Unit = Unit

    /**
     * 在Lifecycle.EVENT.ON_START时回调
     */
    fun onStart(): Unit = Unit

    /**
     * 在Lifecycle.EVENT.ON_RESUME时回调
     */
    fun onResume(): Unit = Unit

    /**
     * 在Lifecycle.EVENT.ON_PAUSE时回调
     */
    fun onPause(): Unit = Unit

    /**
     * 在Lifecycle.EVENT.ON_STOP时回调
     */
    fun onStop(): Unit = Unit

    /**
     * 在Lifecycle.EVENT.ON_DESTROY时回调
     */
    fun onDestroy(): Unit = Unit

    /**
     * PageViewModel在onClear时会统一回调各个SectionViewModel，
     * 以便统一达成onClear的事件
     */
    fun onCleared(): Unit = Unit
}