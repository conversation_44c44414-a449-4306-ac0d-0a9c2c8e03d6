/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PluginInstallErrorCode.kt
 ** Description:插件安装错误码
 ** Version: 1.0
 ** Date: 2023/3/16
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/3/16      1.0		   INIT
 *********************************************************************************/
package com.oplus.gallery.foundation.arch.plugin.install

/**
 * 插件安装错误码
 */
object PluginInstallErrorCode {
    /**
     * 没有发生错误
     */
    const val NO_ERROR = 0

    /**
     * 激活的安装任务太多了，超出了限制
     */
    const val ACTIVE_SESSIONS_LIMIT_EXCEEDED = -1

    /**
     * 请求安装的模块不可用
     */
    const val MODULE_UNAVAILABLE = -2

    /**
     * 请求不合法
     */
    const val INVALID_REQUEST = -3

    /**
     * 请求的会话未找到
     */
    const val SESSION_NOT_FOUND = -4

    /**
     * 插件安装的API不可用
     */
    const val API_NOT_AVAILABLE = -5

    /**
     * 网络错误：无法获取插件详情
     */
    const val NETWORK_ERROR = -6

    /**
     * 当前环境下下载被拒绝了
     */
    const val ACCESS_DENIED = -7

    /**
     * 会话发生冲突:新的session和旧的session有冲突
     */
    const val INCOMPATIBLE_WITH_EXISTING_SESSION = -8

    /**
     * 服务挂掉了
     */
    const val SERVICE_DIED = -9

    /**
     * 未知错误
     */
    const val UNKNOWN_ERROR = -100
}