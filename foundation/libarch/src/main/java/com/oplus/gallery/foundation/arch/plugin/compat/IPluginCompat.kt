/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IPluginCompat.kt
 ** Description:插件动态加载的兼容接口
 ** Version: 1.0
 ** Date: 2023/3/16
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/3/16      1.0		   INIT
 *********************************************************************************/
package com.oplus.gallery.foundation.arch.plugin.compat

import android.app.Activity
import android.content.Context

/**
 * 插件动态加载的兼容。
 * 为了让您的应用从已下载的插件apk访问其代码和资源，需要：
 * 1.为基本应用启用SplitCompat模式：在应用的Application的attachBaseContext方法中调用[IPluginCompat.install]方法
 * 2.为插件中每个Activity启动SplitCompat模式：需要为应用在插件下载的每个Activity启用SplitCompat模式，在Activity的attachBaseContext调用
 * [IPluginCompat.installActivity]方法。
 */
internal interface IPluginCompat {

    /**
     * 插件动态加载的兼容接口, 用于Application中调用以让资源和代码立即生效
     *
     * @param appContext 上下文
     */
    fun install(appContext: Context)

    /**
     * 插件动态加载的兼容接口, 用于Activity中调用以让资源和代码立即生效
     *
     * @param activity Activity对象
     */
    fun installActivity(activity: Activity)
}