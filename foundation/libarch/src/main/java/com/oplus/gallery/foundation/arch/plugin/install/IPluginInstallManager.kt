/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IPluginInstallManager.kt
 ** Description: 插件安装管理对外接口
 ** Version: 1.0
 ** Date: 2023/3/16
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/3/16      1.0		   INIT
 *********************************************************************************/
package com.oplus.gallery.foundation.arch.plugin.install

import android.app.Activity

/**
 * 插件安装管理对外接口
 */
internal interface IPluginInstallManager {

    /**
     * 注册插件安装监听器
     *
     * @param listener 监听器
     */
    fun registerListener(listener: IPluginInstallListener)

    /**
     * 注册插件安装监听器
     *
     * @param listener 监听器
     */
    fun unregisterListener(listener: IPluginInstallListener)

    /**
     * 安装插件
     *
     * @param request 安装请求
     * @return 任务ID
     */
    fun install(request: PluginInstallRequest): Int

    /**
     * 取消安装
     *
     * @param sessionId 安装任务sessionID
     */
    fun cancelInstall(sessionId: Int)

    /**
     * 卸载插件列表
     *
     * @param pluginNames 插件名列表
     */
    fun uninstall(pluginNames: List<String>)

    /**
     * 获取已安装的插件
     *
     * @return 已安装插件集合，不可重复
     */
    fun getInstalledPlugins(): Set<String>

    /**
     * 判断某个插件是否已经安装
     *
     * @return true表示已安装，false表示未安装
     */
    fun isInstalled(pluginName: String): Boolean

    /**
     * 当下载的插件apk的size超过阈值（150MB），开发者必须要调用[startConfirmationDialogForResult]方法，
     * 该方法将会弹出一个让用户确认的确认的Dialog。
     */
    fun startConfirmationDialogForResult(activity: Activity, requestCode: Int)

    companion object {
        const val INVALID_SESSION_ID: Int = -1
    }
}