/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DfmResPlugin.groovy
 ** Description: 动态功能模块的资源检查和替换插件
 ** Version: 1.0
 ** Date: 2023/04/24
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lima<PERSON>@Apps.Gallery              2023/04/24      1.0		   INIT
 *********************************************************************************/
package com.oplus.gallery.libarch.dfmres

import org.gradle.api.GradleException
import org.gradle.api.InvalidUserDataException
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.api.UnknownTaskException

/**
 * 动态功能模块的资源检查和替换插件
 * 该插件应该仅在需要进行dfm模块资源引用的检查和替换时使用，仅作为提升效率的工作使用，不应该在发布版本开启
 */
class DfmResPlugin implements Plugin<Project> {

    def TAG = "DfmResPlugin"

    @Override
    void apply(Project project) {
        if (!project.getPlugins().hasPlugin("com.android.dynamic-feature")) {
            throw new GradleException("DfmResPlugin: Dynamic-feature plugin required")
        }

        project.afterEvaluate {
            def android = project.extensions.android
            android.applicationVariants.all { baseVariant ->
                def variantName = baseVariant.name.capitalize()
                println "${TAG}----variantName:" + variantName
                try {
                    def processResourcesTask = project.tasks.getByName("process${variantName}Resources")
                    def dfmResTask = project.tasks.create("dfm${variantName}ResTask", DfmResTask)
                    dfmResTask.variantName = variantName
                    processResourcesTask.dependsOn dfmResTask
                } catch (UnknownTaskException e) {
                    println "${TAG}--------find task failed:" + "${e.message}"
                } catch (InvalidUserDataException e1) {
                    println "${TAG}--------create task failed:" + "${e1.message}"
                }

            }
        }
    }
}
