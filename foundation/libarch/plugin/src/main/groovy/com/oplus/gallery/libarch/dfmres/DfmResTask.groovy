/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DfmResTask.groovy
 ** Description: 动态功能模块的资源检查和替换的任务
 ** Version: 1.0
 ** Date: 2023/04/24
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lima<PERSON>@Apps.Gallery              2023/04/24      1.0		   INIT
 *********************************************************************************/
package com.oplus.gallery.libarch.dfmres

import org.gradle.api.DefaultTask
import org.gradle.api.tasks.Input
import org.gradle.api.tasks.Internal
import org.gradle.api.tasks.TaskAction

import java.util.regex.Matcher
import java.util.regex.Pattern

/**
 * 动态功能模块的资源检查和替换的任务
 */
class DfmResTask extends DefaultTask {

    @Input
    String variantName

    @Internal
    def TAG = "DfmResPlugin"

    @Internal
    def dfmResNameList = new ArrayList()

    static def BASE_R_PKG_NAME = "com.oplus.gallery.basebiz."
    static def BASE_R_ALIAS = "BaseR"
    static def ANDROID_R_PKG_NAME = "android."
    static def DEBUG = true

    static def convertVariant(String variantName) {
        char[] c = variantName.toCharArray()
        c[0] = Character.toLowerCase(c[0])
        String variant = new String(c)
        return variant
    }

    def parseRtxt(String rtxtFilePath) {
        File rtxtFile = new File(rtxtFilePath)
        Scanner scanner = new Scanner(rtxtFile)
        while (scanner.hasNextLine()) {
            String line = scanner.nextLine()
            if (line.contains("int[]")) {
                // 只处理数组资源
                String[] splits = line.split("\\{")
                def part1 = splits[0]
                def part1Splits = part1.split(" ")
                String resName = part1Splits[2]
                cacheResId(resName)
            } else {
                String[] splits = line.split(" ")
                String resName = splits[2]
                cacheResId(resName)
            }
        }
        if (DEBUG) println "${TAG}------cache resNameSet:" + dfmResNameList
    }

    def cacheResId(String resName) {
        if (resName.isEmpty()) return
        dfmResNameList.add(resName)
    }

    def clearCacheResId() {
        dfmResNameList.clear()
    }

    @TaskAction
    void doTask() {
        long startTime = System.currentTimeMillis()
        StringBuilder stringBuilder = new StringBuilder(project.getBuildDir().getAbsolutePath())
        stringBuilder.append("${File.separator}intermediates${File.separator}runtime_symbol_list${File.separator}")
                .append(convertVariant(variantName)).append("${File.separator}R.txt")
        clearCacheResId()
        parseRtxt(stringBuilder.toString())
        replaceWrongResourceRef()
        long endTime = System.currentTimeMillis()
        if (DEBUG) println "${TAG} cost time:" + (endTime - startTime)
    }

    def replaceWrongResourceRef() {
        String sourceDir = project.getProjectDir().getAbsolutePath() +
                "${File.separator}src${File.separator}main${File.separator}java${File.separator}"
        File srcDirectory = new File(sourceDir)
        srcDirectory.eachFileRecurse { file ->
            if (!file.isDirectory()) {
                if (DEBUG) println "${TAG}-------source file:" + file.getName()
                replaceWrongResourceRefForFile(file, dfmResNameList)
            }
        }
        // dfm模块的子项目
        project.getSubprojects().each { project ->
            String subSourceDir = project.getProjectDir().getAbsolutePath() +
                    "${File.separator}src${File.separator}main${File.separator}java${File.separator}"
            if (DEBUG) println "${TAG}-------------subSourceDir:${subSourceDir}"
            File subSrcDirectory = new File(subSourceDir)
            subSrcDirectory.eachFileRecurse { subFile ->
                if (!subFile.isDirectory()) {
                    if (DEBUG) println "${TAG}-------sub source file:" + subFile.getName()
                    replaceWrongResourceRefForFile(subFile, dfmResNameList)
                }
            }
        }
    }

    def isNeedAddNewLineAfterLastLine(File sourceFile) {
        RandomAccessFile raf = new RandomAccessFile(sourceFile, 'r')
        try {
            raf.seek(sourceFile.length() - 1)
            byte lastByte = raf.readByte()
            println "${TAG}------last byte:${Integer.toHexString(lastByte)}"
            if (lastByte == (byte) 0x0A) {
                return true
            }
        } catch (Exception e) {
            e.printStackTrace()
            println "${TAG}---------exception:${e.message}"
        } finally {
            raf.close()
        }
        return false
    }

    def replaceWrongResourceRefForFile(File sourceFile, List<String> dfmResNames) {
        List<String> newCodeLines = getNewCodeLines(sourceFile, dfmResNames)
        def isNeedAddNewLine = isNeedAddNewLineAfterLastLine(sourceFile)
        println "${TAG}----------isNeedAddNewLine:${isNeedAddNewLine}"
        BufferedWriter bw = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(sourceFile, false)))
        for (int i = 0; i < newCodeLines.size(); i++) {
            if (i == newCodeLines.size() - 1) {
                if (isNeedAddNewLine) {
                    bw.writeLine(newCodeLines[i])
                } else {
                    bw.write(newCodeLines[i])
                }
            } else {
                bw.writeLine(newCodeLines[i])
            }
        }
        bw.flush()
        bw.close()
    }

    static def getNewCodeLines(File sourceFile, List<String> dfmResNameSet) {
        List<String> codeLines = new ArrayList<>()
        FileInputStream inputStream = new FileInputStream(sourceFile)
        // 此处存在一个问题：对于文件末尾最后一个空行 readLine读取的是null；文件末尾没有空行时，readline最后一次循环读到的也是null，因此无法区分源文件的最后是否有一个空行
        inputStream.readLines().each { codeLine ->
            List<String> resNamesInLine = extractResRef(codeLine)
            if (!resNamesInLine.isEmpty()) {
                resNamesInLine.each { resRefName ->
                    String[] splits = resRefName.split("\\.")
                    String resName = splits[2]
                    if (!dfmResNameSet.contains(resName)) {
                        codeLine = codeLine.replace(resRefName, BASE_R_PKG_NAME + resRefName)
                    }
                }
            }
            codeLines.add(codeLine)
        }
        return codeLines
    }

    static def extractResRef(String codeLine) {
        String regexAllR = "R\\.[a-z]*\\.[a-z|A-Z|_|\\d]*"
        Pattern patternAllR = Pattern.compile(regexAllR)
        Matcher matcherAllR = patternAllR.matcher(codeLine)
        List<String> resRefsAll = new ArrayList<>()
        while (matcherAllR.find()) {
            def result = codeLine.substring(matcherAllR.start(), matcherAllR.end())
            resRefsAll.add(result)
        }

        List<String> resRefsBaseR = new ArrayList<>()
        String regexQualifiedR = "${BASE_R_PKG_NAME}R\\.[a-z]*\\.[a-z|A-Z|_|\\d]*"
        Pattern patternQualifiedR = Pattern.compile(regexQualifiedR)
        Matcher matcherQualifiedR = patternQualifiedR.matcher(codeLine)
        while (matcherQualifiedR.find()) {
            String result = codeLine.substring(matcherQualifiedR.start(), matcherQualifiedR.end()).replace(BASE_R_PKG_NAME, "")
            resRefsBaseR.add(result)
        }

        List<String> resRefsBaseR2 = new ArrayList<>()
        String regexAlisa = "${BASE_R_ALIAS}\\.[a-z]*\\.[a-z|A-Z|_|\\d]*"
        Pattern patternAlisa = Pattern.compile(regexAlisa)
        Matcher matcherAlias = patternAlisa.matcher(codeLine)
        while (matcherAlias.find()) {
            String result = codeLine.substring(matcherAlias.start(), matcherAlias.end()).replace("${BASE_R_ALIAS}.", "R.")
            resRefsBaseR2.add(result)
        }

        List<String> resRefsAndroidR = new ArrayList<>()
        String regexAndroidR = "${ANDROID_R_PKG_NAME}R\\.[a-z]*\\.[a-z|A-Z|_|\\d]*"
        Pattern patternAndroidR = Pattern.compile(regexAndroidR)
        Matcher matcherAndroidR = patternAndroidR.matcher(codeLine)
        while (matcherAndroidR.find()) {
            String result = codeLine.substring(matcherAndroidR.start(), matcherAndroidR.end()).replace(ANDROID_R_PKG_NAME, "")
            resRefsAndroidR.add(result)
        }

        resRefsAll.removeAll(resRefsBaseR)
        resRefsAll.removeAll(resRefsBaseR2)
        resRefsAll.removeAll(resRefsAndroidR)

        return resRefsAll
    }
}