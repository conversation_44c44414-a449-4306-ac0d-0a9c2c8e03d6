/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : GStartupConfig.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/4/21 19:10
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2022/4/21      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.foundation.gstartup

import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_STARTUP_THREAD_COUNT
import java.util.concurrent.ExecutorService
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.ThreadFactory
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicInteger

/**
 * GStartup的配置类
 */
object GStartupConfig {
    private const val KEEP_ALIVE_TIME_SECOND = 10L
    private const val STARTUP_THREAD_COUNT_DEFAULT = 4

    /**
     * 设置执行`task`的线程池的核心线程数，默认是CPU数。
     */
    private val coreThreadCount = if (DEBUG) DEBUG_STARTUP_THREAD_COUNT else STARTUP_THREAD_COUNT_DEFAULT

    /**
     * 设置执行`task`的线程池的`ThreadFactory`，默认会有一个`ThreadFactory`，将创建的
     * `Thread`命名为“GStartup Thread #num”。
     */
    private val threadFactory: ThreadFactory by lazy {
        object : ThreadFactory {
            private val mCount = AtomicInteger(1)
            override fun newThread(r: Runnable): Thread {
                return Thread(r, "GStartup#${mCount.getAndIncrement()}")
            }
        }
    }

    /**
     * 设置执行`task`的线程池，默认线程池，核心线程池数是CPU数，缓存队列无穷大。包括核心线程在内，当线程空闲
     * 超过1分钟，会将线程释放。
     */
    val executor: ExecutorService by lazy {
        ThreadPoolExecutor(
            coreThreadCount, coreThreadCount,
            KEEP_ALIVE_TIME_SECOND, TimeUnit.SECONDS,
            LinkedBlockingQueue(),
            threadFactory
        ).apply {
            allowCoreThreadTimeOut(true)
        }
    }
}