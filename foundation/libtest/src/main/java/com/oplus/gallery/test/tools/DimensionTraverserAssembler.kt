/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : DimensionTraverserAssembler.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/05/26
 ** Author      : <EMAIL>
 ** TAG         :
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                         <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>           2022/05/26       1.0      create module
 *********************************************************************************/
package com.oplus.gallery.test.tools

/**
 * 维度数据
 *
 * @param name 维度的名称
 * @param values 维度的数据
 */
data class DimensionData<T>(val name: String, val values: Iterable<T>) {

    companion object {

        fun <T> of(name: String, values: Iterable<T>): DimensionData<T> = DimensionData(name, values)

        fun <T> of(name: String, vararg values: T): DimensionData<T> = DimensionData(name, values.toList())
    }
}


/**
 * 维度数据遍历组装器，用于将多个维度的数据组装起来
 *
 * 使用示例：
 *
 * ```kotlin
 * data class TestData(val invokeFrom: InvokeFrom, val isAboveKeyguardPage: Boolean)
 *
 * enum class InvokeFrom {
 *     CAMERA, MAP
 * }
 *
 * fun main(args: Array<String>) {
 *     val testData: List<Array<TestData>> = DimensionTraverserAssembler()
 *         // 添加 invokeFrom 维度
 *         .dimensionOf("invokeFrom", InvokeFrom.CAMERA, InvokeFrom.MAP)
 *         // 添加 isAboveKeyguardPage 维度
 *         .dimensionOf("isAboveKeyguardPage", true, false)
 *         // 组装创建对象
 *         .assemble {
 *             val invokeFrom = it["invokeFrom"] as InvokeFrom
 *             val isAboveKeyguardPage = it["isAboveKeyguardPage"] as Boolean
 *             arrayOf(
 *                 TestData(
 *                     invokeFrom = invokeFrom,
 *                     isAboveKeyguardPage = isAboveKeyguardPage
 *                 )
 *             )
 *         }
 *
 * 	// 结果：
 * 	// [
 * 	//   [TestData(invokeFrom=MAP, isAboveKeyguardPage=false)],
 * 	// 	 [TestData(invokeFrom=CAMERA, isAboveKeyguardPage=false)],
 * 	// 	 [TestData(invokeFrom=MAP, isAboveKeyguardPage=true)],
 * 	// 	 [TestData(invokeFrom=CAMERA, isAboveKeyguardPage=true)]
 * 	// ]
 *     println(testData.map { it.toList() })
 * }
 * ```
 */
class DimensionTraverserAssembler {

    private val dimensionData: MutableList<DimensionData<*>> = mutableListOf()

    fun <T> dimensionOf(name: String, values: Iterable<T>): DimensionTraverserAssembler = apply {
        dimensionData.add(DimensionData(name, values))
    }

    fun <T> dimensionOf(name: String, vararg values: T): DimensionTraverserAssembler = apply {
        dimensionData.add(DimensionData(name, values.toList()))
    }

    fun <T> dimensionOf(vararg data: DimensionData<T>): DimensionTraverserAssembler = apply {
        dimensionData.addAll(data)
    }

    fun dimensionOfBoolean(name: String): DimensionTraverserAssembler = apply {
        dimensionData.add(DimensionData.of(name, true, false))
    }

    /**
     * Marked by: 2022/5/30 lichengli 优化为流式 Sequence 实现
     */
    fun <T> assemble(block: (Map<String, Any?>) -> T): List<T> {
        if (dimensionData.isEmpty()) return emptyList()

        val dataMappings = mutableListOf<MutableMap<String, Any?>>(mutableMapOf())
        for ((dimensionName, dimensionValues) in dimensionData) {
            // 走到这里，说明开始了新维度的数据遍历

            // 先把之前的数据保存一下作为存根，重复利用
            val dataMappingsStub = dataMappings.toList()

            // 开始遍历该维度的数据
            val dimensionIterator = dimensionValues.iterator()
            while (dimensionIterator.hasNext()) {
                val dimensionValue = dimensionIterator.next()
                if (dimensionIterator.hasNext()) {
                    // 遍历到此处，说明 Iterator 后面还有数据。则根据存根创建新的数据 Map，然后追加本次数据，并保存到组合列表中
                    dataMappings.addAll(dataMappingsStub.map {
                        it.toMutableMap().apply {
                            put(dimensionName, dimensionValue)
                        }
                    }
                    )
                } else {
                    // 如果遍历到此处，说明此次遍历为最后一次。为了重复利用集合，使用存根 Map 追加本次数据，重复利用
                    dataMappingsStub.forEach {
                        it[dimensionName] = dimensionValue
                    }
                }
            }
        }

        // 所有的组合都被拼凑为 Map 形式的列表了，然后执行 block 创建实体
        return dataMappings.map(block)
    }
}
