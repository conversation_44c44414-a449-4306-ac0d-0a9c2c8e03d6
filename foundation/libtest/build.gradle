plugins {
    id 'com.android.library'
    id 'kotlin-android'
}

apply from: "${rootDir}/gradle/libCommon.gradle"

android {
    compileSdkVersion prop_compileSdkVersion
    buildToolsVersion prop_buildToolsVersion
    resourcePrefix project.name + "_"
    namespace 'com.oplus.gallery.foundation.test'

    defaultConfig {
        minSdkVersion prop_minSdkVersion
        targetSdkVersion prop_targetSdkVersion

        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    //忽略，olint扫描项:代码混淆风险
    lintOptions {
        disable  "MinifyEnabled"
    }
}