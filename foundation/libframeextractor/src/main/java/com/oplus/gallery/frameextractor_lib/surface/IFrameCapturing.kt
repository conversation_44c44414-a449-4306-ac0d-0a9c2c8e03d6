/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IFrameCapturing.kt.kt
 ** Description:帧画面的截取，可以是视频帧也可以是静态的View画面，具体依赖子类的实现
 ** Version: 1.0
 ** Date : 2023/6/5
 ** Author: xiewujie@Apps.Gallery3D
 ** TAG: IFrameCapturing
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  xie<PERSON><PERSON><PERSON>@Apps.Gallery3D      2023/06/05    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.frameextractor_lib.surface

import android.graphics.Bitmap

/**
 * 帧画面的截取，可以是视频帧也可以是静态的View画面,具体依赖子类的实现
 */
interface IFrameCapturing {

    /**
     * 执行截帧
     *
     * @return Bitmap
     */
    fun capture(): Bitmap?
}