/*******************************************************************************
 ** Copyright (C), 2024-2034, OPLUS Mobile Comm Corp., Ltd
 ** OPLUS_EDIT, All rights reserved.
 **
 ** File: - RegisterTransform.kt
 ** Description: plugin.java调用transform转换类
 ** Version: 1.0
 ** Date: 2025/3/31
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>       2025/3/31    1.0         first create
 *******************************************************************************/
package com.oplus.gallery.router_lib.plugin

import com.android.build.api.artifact.ScopedArtifact
import com.android.build.api.variant.ApplicationAndroidComponentsExtension
import com.android.build.api.variant.ScopedArtifacts
import com.android.build.gradle.internal.plugins.AppPlugin
import org.gradle.api.Project

object RegisterTransform {

    fun transform(project: Project) {
        project.plugins.withType(AppPlugin::class.java) {
            val androidComponents = project.extensions.getByType(ApplicationAndroidComponentsExtension::class.java)
            androidComponents.onVariants { variant ->
                val task = project.tasks.register("${variant.name}RouterClassesTask", RouterClassesTask::class.java)
                variant.artifacts.forScope(ScopedArtifacts.Scope.ALL)
                    .use(task)
                    .toTransform(
                        ScopedArtifact.CLASSES,
                        RouterClassesTask::jars,
                        RouterClassesTask::dirs,
                        RouterClassesTask::output
                    )
            }
        }
    }
}