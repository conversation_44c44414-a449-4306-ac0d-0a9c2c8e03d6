/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - GenerateAppUtil.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/12/03
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>						<date>			<version>		<desc>
 ** ------------------------------------------------------------------------------
 ** h<PERSON><PERSON><PERSON>@Apps.Gallery		2020/02/24		1.0			OPLUS_ARCH_EXTENDS
 *********************************************************************************/

package com.oplus.gallery.router_lib.plugin

import org.objectweb.asm.ClassVisitor
import org.objectweb.asm.Label
import org.objectweb.asm.MethodVisitor
import org.objectweb.asm.Opcodes.ACC_PROTECTED
import org.objectweb.asm.Opcodes.ACC_PUBLIC
import org.objectweb.asm.Opcodes.ALOAD
import org.objectweb.asm.Opcodes.ILOAD
import org.objectweb.asm.Opcodes.INVOKESPECIAL
import org.objectweb.asm.Opcodes.INVOKESTATIC
import org.objectweb.asm.Opcodes.RETURN

object GenerateMethodUtil {
    private const val TAG = "GenerateMethodUtil"

    @JvmStatic
    fun generateAttachBaseContext(className: String, classVisitor: ClassVisitor, hasMethod: Boolean = false) {
        val methodVisitor = classVisitor.visitMethod(
            ACC_PROTECTED,
            Constants.METHOD_ATTACH_BASE_CONTEXT,
            "(L${Constants.CLASS_CONTEXT};)V",
            null,
            null
        )
        methodVisitor.visitCode()
        generateAttachBaseContext(className, methodVisitor, hasMethod)
        methodVisitor.visitEnd()
    }

    @Suppress("LongMethod")
    @JvmStatic
    fun generateAttachBaseContext(className: String, methodVisitor: MethodVisitor, hasMethod: Boolean = false) {
        Log.d(TAG, "generateAttachBaseContext, hasMethod: $hasMethod")
        val label0 = Label()
        methodVisitor.visitLabel(label0)
        if (!hasMethod) {
            methodVisitor.visitVarInsn(ALOAD, 0)
            methodVisitor.visitVarInsn(ALOAD, 1)
            methodVisitor.visitMethodInsn(
                INVOKESPECIAL,
                Constants.CLASS_APPLICATION,
                Constants.METHOD_ATTACH_BASE_CONTEXT,
                "(L${Constants.CLASS_CONTEXT};)V",
                false
            )
        }
        val label1 = Label()
        methodVisitor.visitLabel(label1)
        methodVisitor.visitMethodInsn(
            INVOKESTATIC,
            Constants.CLASS_ROUTER_NODES_INIT,
            Constants.METHOD_INIT_APP, "()V", false
        )
        val label2 = Label()
        methodVisitor.visitLabel(label2)
        methodVisitor.visitMethodInsn(
            INVOKESTATIC,
            Constants.CLASS_ROUTER_NODES_INIT,
            Constants.METHOD_INIT_ROUTER, "()V", false
        )
        val label3 = Label()
        methodVisitor.visitLabel(label3)
        methodVisitor.visitMethodInsn(
            INVOKESTATIC,
            Constants.CLASS_ROUTER_NODES_INIT,
            Constants.METHOD_INIT_COMPONENT, "()V", false
        )
        val label4 = Label()
        methodVisitor.visitLabel(label4)
        methodVisitor.visitVarInsn(ALOAD, 0)
        methodVisitor.visitVarInsn(ALOAD, 1)
        methodVisitor.visitMethodInsn(
            INVOKESTATIC,
            Constants.CLASS_APP_INIT_HELPER,
            Constants.METHOD_FOR_ATTACH_BASE_CONTEXT,
            "(L${Constants.CLASS_APPLICATION};L${Constants.CLASS_CONTEXT};)V",
            false
        )
        val label5 = Label()
        methodVisitor.visitLabel(label5)
        methodVisitor.visitInsn(RETURN)
        val label6 = Label()
        methodVisitor.visitLabel(label6)
        methodVisitor.visitLocalVariable("this", "L$className;", null, label0, label6, 0)
        methodVisitor.visitLocalVariable(
            "base",
            "L${Constants.CLASS_CONTEXT};",
            null,
            label0,
            label6,
            1
        )
        methodVisitor.visitMaxs(2, 2)
    }

    @JvmStatic
    fun generateOnCreate(className: String, classVisitor: ClassVisitor, hasMethod: Boolean = false) {
        val methodVisitor = classVisitor.visitMethod(ACC_PUBLIC, Constants.METHOD_ON_CREATE, "()V", null, null)
        methodVisitor.visitCode()
        generateOnCreate(className, methodVisitor, hasMethod)
        methodVisitor.visitEnd()
    }

    @JvmStatic
    fun generateOnCreate(className: String, methodVisitor: MethodVisitor, hasMethod: Boolean = false) {
        Log.d(TAG, "generateOnCreate, hasMethod: $hasMethod")
        val label0 = Label()
        methodVisitor.visitLabel(label0)
        if (!hasMethod) {
            methodVisitor.visitVarInsn(ALOAD, 0)
            methodVisitor.visitMethodInsn(
                INVOKESPECIAL,
                Constants.CLASS_APPLICATION,
                Constants.METHOD_ON_CREATE,
                "()V",
                false
            )
        }
        val label1 = Label()
        methodVisitor.visitLabel(label1)
        methodVisitor.visitVarInsn(ALOAD, 0)
        methodVisitor.visitMethodInsn(
            INVOKESTATIC,
            Constants.CLASS_APP_INIT_HELPER,
            Constants.METHOD_FOR_CREATE, "(L${Constants.CLASS_APPLICATION};)V", false
        )
        val label2 = Label()
        methodVisitor.visitLabel(label2)
        methodVisitor.visitInsn(RETURN)
        val label3 = Label()
        methodVisitor.visitLabel(label3)
        methodVisitor.visitLocalVariable("this", "L$className;", null, label0, label3, 0)
        methodVisitor.visitMaxs(1, 1)
    }

    @JvmStatic
    fun generateOnConfigurationChanged(className: String, classVisitor: ClassVisitor, hasMethod: Boolean = false) {
        val methodVisitor = classVisitor.visitMethod(
            ACC_PUBLIC,
            Constants.METHOD_ON_CONFIGURATION_CHANGED,
            "(L${Constants.CLASS_CONFIGURATION};)V",
            null,
            null
        )
        methodVisitor.visitCode()
        generateOnConfigurationChanged(className, methodVisitor, hasMethod)
        methodVisitor.visitEnd()
    }

    @JvmStatic
    fun generateOnConfigurationChanged(className: String, methodVisitor: MethodVisitor, hasMethod: Boolean = false) {
        Log.d(TAG, "generateOnConfigurationChanged, hasMethod: $hasMethod")
        val label0 = Label()
        methodVisitor.visitLabel(label0)
        if (!hasMethod) {
            methodVisitor.visitVarInsn(ALOAD, 0)
            methodVisitor.visitVarInsn(ALOAD, 1)
            methodVisitor.visitMethodInsn(
                INVOKESPECIAL,
                Constants.CLASS_APPLICATION,
                Constants.METHOD_ON_CONFIGURATION_CHANGED,
                "(L${Constants.CLASS_CONFIGURATION};)V",
                false
            )
        }
        val label1 = Label()
        methodVisitor.visitLabel(label1)
        methodVisitor.visitVarInsn(ALOAD, 0)
        methodVisitor.visitVarInsn(ALOAD, 1)
        methodVisitor.visitMethodInsn(
            INVOKESTATIC,
            Constants.CLASS_APP_INIT_HELPER,
            Constants.METHOD_FOR_CONFIGURATION_CHANGED,
            "(L${Constants.CLASS_APPLICATION};L${Constants.CLASS_CONFIGURATION};)V",
            false
        )
        val label2 = Label()
        methodVisitor.visitLabel(label2)
        methodVisitor.visitInsn(RETURN)
        val label3 = Label()
        methodVisitor.visitLabel(label3)
        methodVisitor.visitLocalVariable("this", "L$className;", null, label0, label3, 0)
        methodVisitor.visitLocalVariable(
            "newConfig",
            "L${Constants.CLASS_CONFIGURATION};",
            null,
            label0,
            label3,
            1
        )
        methodVisitor.visitMaxs(2, 2)
    }

    @JvmStatic
    fun generateOnLowMemory(className: String, classVisitor: ClassVisitor, hasMethod: Boolean = false) {
        val methodVisitor = classVisitor.visitMethod(ACC_PUBLIC, Constants.METHOD_ON_LOW_MEMORY, "()V", null, null)
        methodVisitor.visitCode()
        generateOnLowMemory(className, methodVisitor, hasMethod)
        methodVisitor.visitEnd()
    }

    @JvmStatic
    fun generateOnLowMemory(className: String, methodVisitor: MethodVisitor, hasMethod: Boolean = false) {
        Log.d(TAG, "generateOnLowMemory, hasMethod: $hasMethod")
        val label0 = Label()
        methodVisitor.visitLabel(label0)
        if (!hasMethod) {
            methodVisitor.visitVarInsn(ALOAD, 0)
            methodVisitor.visitMethodInsn(
                INVOKESPECIAL,
                Constants.CLASS_APPLICATION,
                Constants.METHOD_ON_LOW_MEMORY,
                "()V",
                false
            )
        }
        val label1 = Label()
        methodVisitor.visitLabel(label1)
        methodVisitor.visitVarInsn(ALOAD, 0)
        methodVisitor.visitMethodInsn(
            INVOKESTATIC,
            Constants.CLASS_APP_INIT_HELPER,
            Constants.METHOD_FOR_LOW_MEMORY, "(L${Constants.CLASS_APPLICATION};)V", false
        )
        val label2 = Label()
        methodVisitor.visitLabel(label2)
        methodVisitor.visitInsn(RETURN)
        val label3 = Label()
        methodVisitor.visitLabel(label3)
        methodVisitor.visitLocalVariable("this", "L$className;", null, label0, label3, 0)
        methodVisitor.visitMaxs(1, 1)
    }

    @JvmStatic
    fun generateOnTrimMemory(className: String, classVisitor: ClassVisitor, hasMethod: Boolean = false) {
        val methodVisitor = classVisitor.visitMethod(ACC_PUBLIC, Constants.METHOD_ON_TRIM_MEMORY, "(I)V", null, null)
        methodVisitor.visitCode()
        generateOnTrimMemory(className, methodVisitor, hasMethod)
        methodVisitor.visitEnd()
    }

    @JvmStatic
    fun generateOnTrimMemory(className: String, methodVisitor: MethodVisitor, hasMethod: Boolean = false) {
        Log.d(TAG, "generateOnTrimMemory, hasMethod: $hasMethod")
        val label0 = Label()
        methodVisitor.visitLabel(label0)
        if (!hasMethod) {
            methodVisitor.visitVarInsn(ALOAD, 0)
            methodVisitor.visitVarInsn(ILOAD, 1)
            methodVisitor.visitMethodInsn(
                INVOKESPECIAL,
                Constants.CLASS_APPLICATION,
                Constants.METHOD_ON_TRIM_MEMORY,
                "(I)V",
                false
            )
        }
        val label1 = Label()
        methodVisitor.visitLabel(label1)
        methodVisitor.visitVarInsn(ALOAD, 0)
        methodVisitor.visitVarInsn(ILOAD, 1)
        methodVisitor.visitMethodInsn(
            INVOKESTATIC,
            Constants.CLASS_APP_INIT_HELPER,
            Constants.METHOD_FOR_TRIM_MEMORY, "(L${Constants.CLASS_APPLICATION};I)V", false
        )
        val label2 = Label()
        methodVisitor.visitLabel(label2)
        methodVisitor.visitInsn(RETURN)
        val label3 = Label()
        methodVisitor.visitLabel(label3)
        methodVisitor.visitLocalVariable("this", "L$className;", null, label0, label3, 0)
        methodVisitor.visitLocalVariable("level", "I", null, label0, label3, 1)
        methodVisitor.visitMaxs(2, 2)
    }
}