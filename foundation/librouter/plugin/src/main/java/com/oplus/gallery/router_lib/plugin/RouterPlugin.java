/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - RouterPlugin.java
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/12/03
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>						<date>			<version>		<desc>
 ** ------------------------------------------------------------------------------
 ** h<PERSON><PERSON><PERSON>@Apps.Gallery		2020/02/24		1.0			OPLUS_ARCH_EXTENDS
 *********************************************************************************/

package com.oplus.gallery.router_lib.plugin;

import org.gradle.api.Plugin;
import org.gradle.api.Project;

/**
 * 路由的插件，需在ApplicationProject中使用，插件插入在构建class完成后阶段，进行对class的补充以及修改。
 * <br/>
 * 目前此插件的意义在于将路由组件统一收集，并在Application中通过asm插入代码，从而达到应用启动后初始化路由组件的目的
 */
public class RouterPlugin implements Plugin<Project> {

    @Override
    public void apply(Project project) {
        System.out.println("register transform");
        RegisterTransform.INSTANCE.transform(project);
    }
}
