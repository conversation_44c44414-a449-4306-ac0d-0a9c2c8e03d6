/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - RouterClassesTask.kt
 ** Description: RouterTransform原操作适配至Gradle8.1
 **
 ** Version: 2.0
 ** Date: 2025/3/31
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>						<date>			<version>		<desc>
 ** ------------------------------------------------------------------------------
 ** h<PERSON><PERSON><PERSON>@Apps.Gallery		2020/02/24		1.0			OPLUS_ARCH_EXTENDS
 ** <EMAIL>   2025/3/31       2.0         Gradle8.1 transform
 *********************************************************************************/
package com.oplus.gallery.router_lib.plugin

import org.apache.commons.io.IOUtils
import org.gradle.api.DefaultTask
import org.gradle.api.file.Directory
import org.gradle.api.file.RegularFile
import org.gradle.api.file.RegularFileProperty
import org.gradle.api.provider.ListProperty
import org.gradle.api.tasks.InputFiles
import org.gradle.api.tasks.Internal
import org.gradle.api.tasks.OutputFile
import org.gradle.api.tasks.TaskAction
import org.objectweb.asm.ClassReader
import org.objectweb.asm.ClassVisitor
import org.objectweb.asm.ClassWriter
import org.objectweb.asm.Opcodes
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.util.Collections
import java.util.concurrent.ConcurrentHashMap
import java.util.jar.JarEntry
import java.util.jar.JarFile
import java.util.jar.JarOutputStream

abstract class RouterClassesTask : DefaultTask() {
    /**
     * 所有输入jar
     */
    @get:InputFiles
    abstract val jars: ListProperty<RegularFile>

    /**
     * 所有输入dir
     */
    @get:InputFiles
    abstract val dirs: ListProperty<Directory>

    /**
     * task输出
     */
    @get:OutputFile
    abstract val output: RegularFileProperty

    /**
     * 已处理的条目
     */
    @get:Internal
    val processedEntries: ConcurrentHashMap.KeySetView<String, Boolean> = ConcurrentHashMap.newKeySet()

    /**
     * 流程处理的方法，处理流程为：
     *
     * 1、扫描所有的class，收集页面路由、动态组件路由、初始化路由；
     * 同时在application中的[Application.attachBaseContext]中插入类[Constants.CLASS_ROUTER_NODES_INIT]方法
     * [Constants.METHOD_INIT_APP]、[Constants.METHOD_INIT_COMPONENT]、[Constants.METHOD_INIT_COMPONENT]；
     * 同时调用[AppInitHelper]的各个方法在Application的各个生命周期方法中。
     *
     * 2、生成一个新的class，类名为[Constants.CLASS_ROUTER_NODES_INIT]，并将扫描的三种类型路由初始化操作写入此文件中；
     *
     */
    @TaskAction
    fun taskAction() {
        println("transform taskAction: start...")
        val startTime = System.currentTimeMillis()

        val initApps = Collections.newSetFromMap<String>(ConcurrentHashMap())
        val initRouters = Collections.newSetFromMap<String>(ConcurrentHashMap())
        val initComponents = Collections.newSetFromMap<String>(ConcurrentHashMap())

        val outputFile = output.get().asFile.apply { parentFile?.mkdirs() }

        JarOutputStream(FileOutputStream(outputFile)).use { jarOutput ->
            // 1. 主动创建唯一的MANIFEST.MF
            createManifestIfNecessary(jarOutput)
            // 2. 处理所有输入JAR文件
            scanJars(jarOutput, initApps, initRouters, initComponents)
            // 3. 处理目录中的文件
            scanDirs(jarOutput, initApps, initRouters, initComponents)
            // 4. 生成初始化类并写入JAR
            generateInitClass(jarOutput, initApps, initRouters, initComponents)
        }
        Log.d(TAG, "transform taskAction: cost ${System.currentTimeMillis() - startTime} ms")
    }

    /**
     * 创建唯一的MANIFEST.MF
     * 避免遍历时重复manifest
     */
    private fun createManifestIfNecessary(jarOutput: JarOutputStream) {
        if (!processedEntries.contains(JarFile.MANIFEST_NAME)) {
            jarOutput.putNextEntry(JarEntry(JarFile.MANIFEST_NAME)).also {
                jarOutput.write("Manifest-Version: 1.0\nCreated-By: ${Constants.NAME}Plugin\n".toByteArray())
                jarOutput.closeEntry()
                processedEntries.add(JarFile.MANIFEST_NAME)
            }
        }
    }

    private fun scanJars(
        jarOutput: JarOutputStream,
        initApps: MutableSet<String>,
        initRouters: MutableSet<String>,
        initComponents: MutableSet<String>
    ) {
        jars.get().forEach outer@{ regularFile ->
            val jarFile = regularFile.asFile
            JarFile(jarFile).use { inputJar ->
                inputJar.entries().iterator().forEach { entry ->
                    when {
                        entry.isDirectory -> return@forEach
                        // 跳过所有输入的清单文件
                        entry.name == JarFile.MANIFEST_NAME -> return@forEach
                        processedEntries.contains(entry.name) -> {
                            logger.warn("Skipping duplicate entry: ${entry.name}")
                            return@forEach
                        }
                        else -> processEntry(inputJar, entry, jarOutput, initApps, initRouters, initComponents)
                    }
                }
            }
        }
    }

    private fun scanDirs(
        jarOutput: JarOutputStream,
        initApps: MutableSet<String>,
        initRouters: MutableSet<String>,
        initComponents: MutableSet<String>
    ) {
        dirs.get().forEach outer@{ directory ->
            directory.asFile.walk().forEach { file ->
                if (file.isFile) {
                    // 确保文件路径格式统一
                    val entryName = directory.asFile.toURI().relativize(file.toURI()).path
                        .replace(File.separatorChar, Constants.SLASH)

                    when {
                        entryName.equals(JarFile.MANIFEST_NAME, ignoreCase = true) -> return@forEach
                        processedEntries.contains(entryName) -> {
                            logger.warn("Skipping duplicate directory entry: $entryName")
                            return@forEach
                        }
                        else -> processDirectoryFile(file, entryName, jarOutput, initApps, initRouters, initComponents)
                    }
                }
            }
        }
    }

    /**
     * 扫描由注解生成器生成到包 [Constants.GEN_PKG] 里的初始化类
     */
    private fun processEntry(
        jarFile: JarFile,
        entry: JarEntry,
        jarOutput: JarOutputStream,
        initApps: MutableSet<String>,
        initRouters: MutableSet<String>,
        initComponents: MutableSet<String>
    ) {
        jarFile.getInputStream(entry).use { input ->
            val bytes = if (isClassFile(entry.name)) {
                visitClass(input, initApps, initRouters, initComponents)
            } else {
                IOUtils.toByteArray(input)
            }
            writeJarEntry(jarOutput, entry.name, bytes)
            processedEntries.add(entry.name)
        }
    }

    /**
     * 扫描由注解生成器生成到包 [Constants.GEN_PKG] 里的初始化类
     */
    private fun processDirectoryFile(
        file: File,
        entryName: String,
        jarOutput: JarOutputStream,
        initApps: MutableSet<String>,
        initRouters: MutableSet<String>,
        initComponents: MutableSet<String>
    ) {
        file.inputStream().use { input ->
            val bytes = if (isClassFile(entryName)) {
                visitClass(input, initApps, initRouters, initComponents)
            } else {
                IOUtils.toByteArray(input)
            }
            writeJarEntry(jarOutput, entryName, bytes)
            processedEntries.add(entryName)
        }
    }

    private fun visitClass(
        input: InputStream,
        initApps: MutableSet<String>,
        initRouters: MutableSet<String>,
        initComponents: MutableSet<String>
    ): ByteArray {
        val classReader = ClassReader(input.readBytes())
        val classWriter = ClassWriter(classReader, ClassWriter.COMPUTE_MAXS)
        val routerClassVisitor = RouterClassVisitor(classWriter)

        classReader.accept(routerClassVisitor, ClassReader.EXPAND_FRAMES)
        when {
            routerClassVisitor.className.startsWith(Constants.GEN_PKG_APP_INIT) -> {
                Log.d(TAG, "visit: app init :${routerClassVisitor.className}")
                initApps.add(routerClassVisitor.className)
            }
            routerClassVisitor.className.startsWith(Constants.GEN_PKG_ROUTER) -> {
                Log.d(TAG, "visit: router init :${routerClassVisitor.className}")
                initRouters.add(routerClassVisitor.className)
            }
            routerClassVisitor.className.startsWith(Constants.GEN_PKG_COMPONENT) -> {
                Log.d(TAG, "visit: module init :${routerClassVisitor.className}")
                initComponents.add(routerClassVisitor.className)
            }
        }
        return classWriter.toByteArray()
    }

    private fun writeJarEntry(jarOutput: JarOutputStream, name: String, bytes: ByteArray) {
        jarOutput.putNextEntry(JarEntry(name)).also {
            jarOutput.write(bytes)
            jarOutput.closeEntry()
        }
    }

    /**
     * 生成一个新的class，类名为[Constants.CLASS_ROUTER_NODES_INIT]，并生成静态方法方法[Constants.METHOD_INIT_APP]、
     * [Constants.METHOD_INIT_ROUTER]、[Constants.METHOD_INIT_COMPONENT]。
     *
     * 将扫描到的各个module生成的router相关类执行调用
     *
     * @param jarOutput jar的OutputStream
     * @param initApps 扫描到的所有apt生成模块初始化类名称
     * @param initRouters 扫描到的所有apt生成页面路由类名称
     * @param initComponents 扫描到的所有apt生成动态模块类名称
     */
    private fun generateInitClass(
        jarOutput: JarOutputStream,
        initApps: Set<String>,
        initRouters: Set<String>,
        initComponents: Set<String>
    ) {
        Log.d(
            TAG, "generateInitMethods, initApps: ${initApps.size}, " +
                    "initRouters: ${initRouters.size}, initComponents: ${initComponents.size}"
        )
        val className = "${Constants.CLASS_ROUTER_NODES_INIT}.class"
        if (processedEntries.contains(className)) return

        val writer = ClassWriter(ClassWriter.COMPUTE_FRAMES or ClassWriter.COMPUTE_MAXS)
        val cv: ClassVisitor = object : ClassVisitor(Opcodes.ASM9, writer) {}
        cv.visit(Opcodes.V1_8, Opcodes.ACC_PUBLIC, Constants.CLASS_ROUTER_NODES_INIT, null, Constants.CLASS_OBJECT, null)

        // 生成方法 initApp
        generateInitAppMethod(cv, initApps)
        // 生成方法 initRouter
        generateInitRouterMethod(cv, initRouters)
        // 生成方法 initComponent
        generateInitComponentMethod(cv, initComponents)

        cv.visitEnd()

        writeJarEntry(jarOutput, className, writer.toByteArray())
        processedEntries.add(className)
        Log.d(TAG, "generateInitMethods: end")
    }

    /**
     * 生成方法 initComponent
     */
    private fun generateInitComponentMethod(cv: ClassVisitor, initModules: Set<String>) {
        val mvInitModules = cv.visitMethod(
            Opcodes.ACC_PUBLIC or Opcodes.ACC_STATIC,
            Constants.METHOD_INIT_COMPONENT,
            "()V",
            null,
            null
        )
        mvInitModules.visitCode()
        // 将扫描到的模块初始化类的 init 方法执行插入到 initComponent 中
        for (clazz in initModules) {
            Log.d(TAG, "module init className: $clazz")
            mvInitModules.visitMethodInsn(
                Opcodes.INVOKESTATIC,
                clazz.dotToSlash(),
                Constants.METHOD_INIT,
                "()V",
                false
            )
        }
        mvInitModules.visitMaxs(0, 0)
        mvInitModules.visitInsn(Opcodes.RETURN)
        mvInitModules.visitEnd()
    }

    /**
     * 生成方法 initRouter
     */
    private fun generateInitRouterMethod(cv: ClassVisitor, initRouters: Set<String>) {
        val mvInitRouter = cv.visitMethod(
            Opcodes.ACC_PUBLIC or Opcodes.ACC_STATIC,
            Constants.METHOD_INIT_ROUTER,
            "()V",
            null,
            null
        )
        mvInitRouter.visitCode()
        // 将扫描到的模块初始化类的 init 方法执行插入到 initRouter 中
        for (clazz in initRouters) {
            Log.d(TAG, "router init className: $clazz")
            mvInitRouter.visitMethodInsn(
                Opcodes.INVOKESTATIC,
                clazz,
                Constants.METHOD_INIT,
                "()V",
                false
            )
        }
        mvInitRouter.visitMaxs(0, 0)
        mvInitRouter.visitInsn(Opcodes.RETURN)
        mvInitRouter.visitEnd()
    }

    /**
     * 生成方法 initApp
     */
    private fun generateInitAppMethod(cv: ClassVisitor, initApps: Set<String>) {
        val mvInitApp = cv.visitMethod(
            Opcodes.ACC_PUBLIC or Opcodes.ACC_STATIC,
            Constants.METHOD_INIT_APP,
            "()V",
            null,
            null
        )
        mvInitApp.visitCode()
        // 将扫描到的模块初始化类的init方法执行插入到initApp中
        for (clazz in initApps) {
            Log.d(TAG, "app init className: $clazz")
            mvInitApp.visitMethodInsn(
                Opcodes.INVOKESTATIC,
                clazz.dotToSlash(),
                Constants.METHOD_INIT,
                "()V",
                false
            )
        }
        mvInitApp.visitMaxs(0, 0)
        mvInitApp.visitInsn(Opcodes.RETURN)
        mvInitApp.visitEnd()
    }

    private fun isClassFile(name: String): Boolean {
        return name.endsWith(".class") && !name.startsWith("R\$") && "R.class" != name && "BuildConfig.class" != name
    }

    companion object {
        private const val TAG = "RouterClassesTask"
    }
}