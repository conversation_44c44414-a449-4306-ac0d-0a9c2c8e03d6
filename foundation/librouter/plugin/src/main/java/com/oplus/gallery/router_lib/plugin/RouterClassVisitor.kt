/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AppClassVisitor.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/12/03
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>						<date>			<version>		<desc>
 ** ------------------------------------------------------------------------------
 ** h<PERSON><PERSON><PERSON>@Apps.Gallery		2020/02/24		1.0			OPLUS_ARCH_EXTENDS
 *********************************************************************************/

package com.oplus.gallery.router_lib.plugin

import org.objectweb.asm.AnnotationVisitor
import org.objectweb.asm.ClassVisitor
import org.objectweb.asm.MethodVisitor
import org.objectweb.asm.Opcodes
import org.objectweb.asm.commons.AdviceAdapter

class RouterClassVisitor(classVisitor: ClassVisitor) : ClassVisitor(Opcodes.ASM9, classVisitor) {
    lateinit var className: String
        private set
    var superName: String? = null
        private set
    private var isApp = false

    private var hasMethodAttachBaseContext = false
    private var hasMethodOnCreate = false
    private var hasMethodOnLowMemory = false
    private var hasMethodOnTrimMemory = false
    private var hasMethodOnConfigurationChange = false

    override fun visit(version: Int, access: Int, name: String, signature: String?, superName: String?, interfaces: Array<out String>?) {
        super.visit(version, access, name, signature, superName, interfaces)
        this.className = name
        this.superName = superName
    }

    override fun visitAnnotation(descriptor: String?, visible: Boolean): AnnotationVisitor? {
        if (Constants.CLASS_NAME_APPLICATION_ASM == descriptor) {
            Log.d(TAG, "visitAnnotation: application name is: $className")
            isApp = true
        }
        return super.visitAnnotation(descriptor, visible)
    }

    override fun visitMethod(
        access: Int,
        name: String,
        descriptor: String?,
        signature: String?,
        exceptions: Array<out String>?
    ): MethodVisitor? {
        return super.visitMethod(access, name, descriptor, signature, exceptions)?.let {
            return@let if (isApp) {
                when {
                    name.startsWith(Constants.METHOD_ATTACH_BASE_CONTEXT) -> {
                        hasMethodAttachBaseContext = true
                        object : AdviceAdapter(Opcodes.ASM9, it, access, name, descriptor) {
                            override fun onMethodExit(opcode: Int) {
                                GenerateMethodUtil.generateAttachBaseContext(className, it, true)
                            }
                        }
                    }
                    name.startsWith(Constants.METHOD_ON_CREATE) -> {
                        hasMethodOnCreate = true
                        object : AdviceAdapter(Opcodes.ASM9, it, access, name, descriptor) {
                            override fun onMethodExit(opcode: Int) {
                                GenerateMethodUtil.generateOnCreate(className, it, true)
                            }
                        }
                    }
                    name.startsWith(Constants.METHOD_ON_LOW_MEMORY) -> {
                        hasMethodOnLowMemory = true
                        object : AdviceAdapter(Opcodes.ASM9, it, access, name, descriptor) {
                            override fun onMethodExit(opcode: Int) {
                                GenerateMethodUtil.generateOnLowMemory(className, it, true)
                            }
                        }
                    }
                    name.startsWith(Constants.METHOD_ON_TRIM_MEMORY) -> {
                        hasMethodOnTrimMemory = true
                        object : AdviceAdapter(Opcodes.ASM9, it, access, name, descriptor) {
                            override fun onMethodExit(opcode: Int) {
                                GenerateMethodUtil.generateOnTrimMemory(className, it, true)
                            }
                        }
                    }
                    name.startsWith(Constants.METHOD_ON_CONFIGURATION_CHANGED) -> {
                        hasMethodOnConfigurationChange = true
                        object : AdviceAdapter(Opcodes.ASM9, it, access, name, descriptor) {
                            override fun onMethodExit(opcode: Int) {
                                GenerateMethodUtil.generateOnConfigurationChanged(className, it, true)
                            }
                        }
                    }
                    else -> it
                }
            } else it
        }
    }

    override fun visitEnd() {
        if (isApp) {
            if (!hasMethodAttachBaseContext) {
                GenerateMethodUtil.generateAttachBaseContext(className, cv, false)
            }
            if (!hasMethodOnCreate) {
                GenerateMethodUtil.generateOnCreate(className, cv, false)
            }
            if (!hasMethodOnLowMemory) {
                GenerateMethodUtil.generateOnLowMemory(className, cv, false)
            }
            if (!hasMethodOnTrimMemory) {
                GenerateMethodUtil.generateOnTrimMemory(className, cv, false)
            }
            if (!hasMethodOnConfigurationChange) {
                GenerateMethodUtil.generateOnConfigurationChanged(className, cv, false)
            }
        }
        super.visitEnd()
    }

    companion object {
        private const val TAG = "RouterClassVisitor"
    }
}