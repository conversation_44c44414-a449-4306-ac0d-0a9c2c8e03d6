/*********************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - Const.java
 * Description:
 *
 * Version: 1.0
 * Date: 2020/08/05
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_ROUTER
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>    <desc>
 * ------------------------------------------------------------------------------
 ** huca<PERSON><PERSON>@Apps.Gallery		2020/02/24		1.0			OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.router_lib.plugin

import java.io.File

object Constants {
    const val NAME = "GalleryRouter"
    const val PKG = "com.oplus.gallery.router_lib"
    const val GEN_PKG = "$PKG.generated"

    const val DOT = '.'
    const val SLASH = '/'

    const val METHOD_INIT = "init"
    const val METHOD_INIT_APP = "initApp"
    const val METHOD_INIT_ROUTER = "initRouter"
    const val METHOD_INIT_COMPONENT = "initComponent"

    const val METHOD_FOR_ATTACH_BASE_CONTEXT = "forAttachBaseContext"
    const val METHOD_FOR_CREATE = "forCreate"
    const val METHOD_FOR_CONFIGURATION_CHANGED = "forConfigurationChanged"
    const val METHOD_FOR_LOW_MEMORY = "forLowMemory"
    const val METHOD_FOR_TRIM_MEMORY = "forTrimMemory"

    const val METHOD_ATTACH_BASE_CONTEXT = "attachBaseContext"
    const val METHOD_ON_CREATE = "onCreate"
    const val METHOD_ON_LOW_MEMORY = "onLowMemory"
    const val METHOD_ON_TRIM_MEMORY = "onTrimMemory"
    const val METHOD_ON_CONFIGURATION_CHANGED = "onConfigurationChanged"

    const val CLASS_NAME_APPLICATION_ASM = "Lcom/oplus/gallery/router_lib/annotations/ApplicationAsm;"

    val CLASS_ROUTER_NODES_INIT = "$GEN_PKG.RouterNodesInit".dotToSlash()
    val CLASS_APPLICATION = "android.app.Application".dotToSlash()
    val CLASS_CONTEXT = "android.content.Context".dotToSlash()
    val CLASS_CONFIGURATION = "android.content.res.Configuration".dotToSlash()
    val CLASS_OBJECT = "java.lang.Object".dotToSlash()
    val CLASS_APP_INIT_HELPER = "$PKG.utils.AppInitHelper".dotToSlash()

    val GEN_PKG_COMPONENT = "$GEN_PKG.component".dotToSlash()
    val GEN_PKG_ROUTER = "$GEN_PKG.router".dotToSlash()
    val GEN_PKG_APP_INIT = "$GEN_PKG.init".dotToSlash()
}

fun String.dotToSlash(): String {
    return replace(Constants.DOT, Constants.SLASH)
}

fun String.dotToSeparator(): String {
    return replace(Constants.DOT, File.separatorChar)
}