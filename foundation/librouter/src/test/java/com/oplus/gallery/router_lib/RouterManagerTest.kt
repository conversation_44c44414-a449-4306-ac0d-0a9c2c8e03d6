/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - RouterManagerTest.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/08/05
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_ROUTER
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** h<PERSON><PERSON><PERSON>@Apps.Gallery		2020/02/24		1.0		OPLUS_ARCH_ROUTER
 *********************************************************************************/

package com.oplus.gallery.router_lib

import android.app.Application
import android.content.res.Configuration
import android.net.Uri
import com.oplus.gallery.router_lib.annotations.RouterNormal
import com.oplus.gallery.router_lib.center.BaseAppInit
import com.oplus.gallery.router_lib.classfactory.DefaultFactory
import com.oplus.gallery.router_lib.meta.ComponentMeta
import com.oplus.gallery.router_lib.meta.InitMeta
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.router_lib.meta.RouterMeta
import com.oplus.gallery.router_lib.utils.Constants
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockkClass
import io.mockk.mockkStatic
import org.junit.Assert.assertNull
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNotNull
import org.junit.Before
import org.junit.Test

class RouterManagerTest {

    @MockK
    lateinit var mockApp: Application

    @Before
    fun setUp() {
        MockKAnnotations.init(this)

        var argument: String? = null
        mockkStatic(Uri::class)
        val mockkClass = mockkClass(Uri::class)
        every { Uri.parse(any()) }.answers {
            argument = arg(0)
            mockkClass
        }
        every { mockkClass.scheme }.answers { argument?.substringBefore(Constants.SYMBOL_URI_HEAD) }
    }

    @Test
    fun should_have_router_when_router_add() {
        //given
        val uri = Constants.URI_ROUTER + "test"
        //when
        RouterManager.routerCenter.add(RouterNormal::class.java, RouterMeta(uri, String::class.java.name))
        //then
        val router = RouterManager.routerCenter.getRouter(PostCard(uri))
        assertNotNull(router)
        assertNotNull(router!!.key)
        assertNotNull(router.clazz)
    }

    @Test
    fun should_no_router_when_router_add() {
        //given wrong prefix
        val uri = "test"
        //when
        RouterManager.routerCenter.add(RouterNormal::class.java, RouterMeta(uri, String::class.java.name))
        //then
        val router = RouterManager.routerCenter.getRouter(PostCard(uri))
        assertNull(router)
    }

    @Test
    fun should_have_component_when_component_add() {
        //given
        val clazz = ITestComponent::class.java
        val obj = TestComponent::class.java
        //when
        RouterManager.componentCenter.add(ComponentMeta(clazz.name, obj.name))
        //then
        val component = RouterManager.componentCenter.getComponent(clazz, DefaultFactory)
        assertNotNull(component)
    }

    @Test(expected = IllegalArgumentException::class)
    fun should_no_component_when_component_add() {
        //given wrong prefix
        val clazz = String::class.java
        val obj = String::class.java
        //when
        RouterManager.componentCenter.add(ComponentMeta(clazz.name, obj.name))
        //then
        RouterManager.componentCenter.getComponent(clazz, DefaultFactory)
    }

    @Test
    fun should_have_appinit_when_appinit_add() {
        //given
        val name = TestAppInit::class.java.name
        //when
        RouterManager.appInitCenter.add(InitMeta(name))
        //then
        val allInits = RouterManager.appInitCenter.getAllInits(mockApp)
        assertEquals(1, allInits.size)
        RouterManager.appInitCenter.clear()
    }

    @Test
    fun should_no_appinit_when_appinit_add() {
        //given wrong prefix
        val name = String::class.java.name
        //when
        RouterManager.appInitCenter.add(InitMeta(name))
        //then
        val allInits = RouterManager.appInitCenter.getAllInits(mockApp)
        assertEquals(0, allInits.size)
        RouterManager.appInitCenter.clear()
    }

    class TestAppInit(app: Application) : BaseAppInit(app) {
        override fun onLowMemory() {
        }

        override fun onConfigurationChanged(newConfig: Configuration) {
        }

        override fun onTrimMemory(level: Int) {
        }
    }

    interface ITestComponent

    class TestComponent : ITestComponent
}