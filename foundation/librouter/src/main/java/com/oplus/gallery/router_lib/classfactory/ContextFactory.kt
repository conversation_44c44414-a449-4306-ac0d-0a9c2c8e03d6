/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ContextFactory.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/08/05
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_ROUTER
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** h<PERSON><PERSON><PERSON>@Apps.Gallery		2020/02/24		1.0		OPLUS_ARCH_ROUTER
 *********************************************************************************/

package com.oplus.gallery.router_lib.classfactory

import android.content.Context
import com.oplus.gallery.foundation.util.debug.GLog

class ContextFactory(private val context: Context) : IFactory {

    override fun <T> create(clazz: Class<T>): T? {
        return runCatching {
            clazz.getConstructor(Context::class.java).newInstance(context)
        }.onFailure {
            GLog.e(TAG, "create: ", it)
        }.getOrNull()
    }

    companion object {
        private const val TAG = "ContextFactory"
    }
}