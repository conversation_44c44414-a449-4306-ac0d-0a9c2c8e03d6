/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - Starter.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/08/05
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_ROUTER
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** h<PERSON><PERSON><PERSON>@Apps.Gallery		2020/02/24		1.0		OPLUS_ARCH_ROUTER
 *********************************************************************************/

package com.oplus.gallery.router_lib

import android.app.Activity
import android.app.ActivityOptions
import android.app.Application
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.Bundle
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import com.oplus.gallery.addon.osense.CpuFrequencyManager
import com.oplus.gallery.addon.utils.OSVersionUtils
import com.oplus.gallery.addon.utils.OSVersionUtils.OPLUS_OS_13_2
import com.oplus.gallery.addon.view.FlexibleWindowManagerWrapper
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils
import com.oplus.gallery.foundation.util.thread.ThreadUtils
import com.oplus.gallery.router_lib.classfactory.DefaultFactory
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.standard_lib.util.os.ContextGetter

sealed class Starter<R>(val postCard: PostCard) {

    open fun start(): R? {
        GLog.d(TAG, "start: $postCard")
        return null
    }

    companion object {
        private const val TAG = "Starter"
    }


    /**
     * activity的路由启动方式
     *
     * @param startContext 必须是[Activity]/[Fragment]/[Application]其中的一种
     * @param bundle 传递给activity的参数，参数位于intent中
     * @param postCard 需要启动的对应[Activity]的路由[PostCard]
     * @param requestCode 请求的requestCode，如果需要[Activity.startActivityForResult]请传入值，如果需要调用[Activity.startActivity]请传入null
     * @param activityOptions 调用[Activity.startActivity]或[Activity.startActivityForResult]传入的options
     * @param isFlexibleStart 是否以跟手面板形式启动
     * @param isFlexibleDescendant 是否继承启动. 再次标准启动Activity——Context#startActivity时，activity是否跟随上一个面板（跟手）显示
     * @param onIntentCreated 构造[Intent]后，会回调到此函数，可通过此回调函数设置intent中的参数
     */
    class ActivityStarter(
        private val startContext: Any?,
        private val bundle: Bundle = Bundle(),
        postCard: PostCard,
        private val requestCode: Int? = null,
        private var activityOptions: Bundle? = null,
        private val isFlexibleStart: Boolean = false,
        private val isFlexibleDescendant: Boolean = false,
        private val onIntentCreated: ((Intent) -> Unit)? = null
    ) : Starter<Unit>(postCard) {
        @Suppress("deprecation")
        override fun start() {
            super.start()
            runCatching {
                val routerClass = RouterManager.routerCenter.getRouter(postCard)?.getRouterClass() ?: return@runCatching
                GLog.d(TAG) { "[Starter] isFlexibleStart =$isFlexibleStart, startContext =$startContext, requestCode =$requestCode" }
                if (OSVersionUtils.isAtLeast(OPLUS_OS_13_2) && isFlexibleStart) {
                    activityOptions = FlexibleWindowManagerWrapper.setExtraBundle(ActivityOptions.makeBasic(),
                        (activityOptions ?: Bundle()).apply {
                        putBoolean(FlexibleWindowManagerWrapper.KEY_FLEXIBLE_START_ACTIVITY, true)
                        putInt(
                            FlexibleWindowManagerWrapper.KEY_FLEXIBLE_ACTIVITY_POSITION,
                            if (ResourceUtils.isRTL(ContextGetter.context)) {
                                FlexibleWindowManagerWrapper.FLEXIBLE_ACTIVITY_POSITION_LEFT
                            } else {
                                FlexibleWindowManagerWrapper.FLEXIBLE_ACTIVITY_POSITION_RIGHT
                            }
                        )
                        putBoolean(FlexibleWindowManagerWrapper.KEY_FLEXIBLE_ACTIVITY_DESCENDANT, isFlexibleDescendant)
                    })
                }
                when (startContext) {
                    is Activity -> {
                        val intent = Intent(startContext, routerClass).putExtras(bundle)
                        onIntentCreated?.invoke(intent)
                        requestCode?.apply {
                            startContext.startActivityForResult(intent, this, activityOptions)
                        } ?: startContext.startActivity(intent, activityOptions)
                    }
                    is Fragment -> {
                        val intent = Intent(startContext.requireContext(), routerClass).putExtras(bundle)
                        onIntentCreated?.invoke(intent)
                        requestCode?.apply {
                            startContext.startActivityForResult(intent, this, activityOptions)
                        } ?: startContext.startActivity(intent, activityOptions)
                    }
                    is Application -> {
                        val intent = Intent(startContext, routerClass).putExtras(bundle)
                        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                        onIntentCreated?.invoke(intent)
                        startContext.startActivity(intent)
                    }
                    else -> GLog.e(TAG, "start: startContext<$startContext> is not one of Activity, Fragment, Application.")
                }
            }.onFailure {
                GLog.e(TAG, "[Starter] start failed.", it)
            }
        }

        companion object {
            private const val TAG = "ActivityStarter"
        }
    }

    /**
     * [Context.startService]的路由启动方式
     *
     * @param context [Context]上下文
     * @param bundle 传入[Service]的参数，参数通过[Intent]携带
     * @param postCard 需要启动的对应[Service]的路由[PostCard]
     */
    class ServiceStarter(
        val context: Context,
        val bundle: Bundle = Bundle(),
        postCard: PostCard
    ) : Starter<ComponentName>(postCard) {
        override fun start(): ComponentName? {
            super.start()
            return RouterManager.routerCenter.getRouter(postCard)?.getRouterClass()?.run {
                context.startService(Intent(context, this).putExtras(bundle))
            }
        }

        companion object {
            private const val TAG = "ServiceStarter"
        }
    }

    /**
     * [Context.bindService]的路由启动方式
     *
     * @param context [Context]上下文
     * @param bundle 传入[Service]的参数，参数通过[Intent]携带
     * @param postCard 需要启动的对应[Service]的路由[PostCard]
     * @param conn 绑定对应[Service]的[ServiceConnection]
     * @param flag [Context.bindService]的flag参数
     */
    class ServiceBindStarter(
        val context: Context?,
        val bundle: Bundle = Bundle(),
        postCard: PostCard,
        private val conn: ServiceConnection,
        val flag: Int
    ) : Starter<Boolean>(postCard) {
        override fun start(): Boolean? {
            super.start()
            return RouterManager.routerCenter.getRouter(postCard)?.getRouterClass()?.run {
                context?.bindService(Intent(context, this).putExtras(bundle), conn, flag) ?: run {
                    GLog.e(TAG, "start: ServiceBindStarter context is null !")
                    false
                }
            }
        }

        companion object {
            private const val TAG = "ServiceBindStarter"
        }
    }

    /**
     * [DialogFragment]的路由启动方式
     *
     * @param fm 对应的[FragmentManager]
     * @param bundle 传入[DialogFragment]对应的参数，参数通过[DialogFragment.mArguments]传递
     * @param postCard 需要启动的对应[DialogFragment]的路由[PostCard]
     * @param fTag 启动对应[DialogFragment]的tag值
     */
    class DialogFragmentStarter<T : DialogFragment>(
        private val fm: FragmentManager?,
        private val bundle: Bundle? = null,
        postCard: PostCard,
        private var fTag: String? = null
    ) : Starter<T>(postCard) {
        @Suppress("UNCHECKED_CAST")
        override fun start(): T? {
            super.start()
            if ((fm == null) || fm.isStateSaved) {
                GLog.e(TAG, "start: postCard<$postCard> , fm is null or isStateSaved, return null")
                return null
            }
            val routerClass = RouterManager.routerCenter.getRouter(postCard)?.getRouterClass() ?: run {
                GLog.e(TAG, "start: postCard not found class<$postCard>")
                return null
            }
            return (DefaultFactory.create(routerClass) as? T)?.apply {
                arguments = bundle
                val realTag = fTag ?: javaClass.name
                CpuFrequencyManager.setAction(CpuFrequencyManager.Action.GAME_BOOST_L3, CpuFrequencyManager.TIMEOUT_120MS)
                ThreadUtils.assertInMainThread()
                show(fm, realTag)
            } ?: let {
                GLog.e(TAG, "start: postCard<$postCard> class is not DialogFragment")
                null
            }
        }

        companion object {
            private const val TAG = "DialogFragmentStarter"
        }
    }
}