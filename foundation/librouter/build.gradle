plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-kapt'
}

apply from: "${rootDir}/gradle/libCommon.gradle"

// 这部分一定要在android的前面
ext {
    mavenDescription = "相册路由库"
    mavenGroupId = mavenGroupName
}

//一定要是"version"关键字，否则版本号会出错
if (buildForCache.toBoolean()) {
    version = versionMavenCache
} else {
    version = routerLibVersion + "${versionSuffix}"
}
//apply from: "${rootDir}/gradle/mavenAar.gradle"

android {
    compileSdkVersion prop_compileSdkVersion
    buildToolsVersion prop_buildToolsVersion
    resourcePrefix project.name + "_"
    namespace 'com.oplus.gallery.foundation.router'

    defaultConfig {
        minSdkVersion prop_minSdkVersion
        targetSdkVersion prop_targetSdkVersion

        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    //忽略，olint扫描项:代码混淆风险
    lintOptions {
        disable  "MinifyEnabled"
    }
}

dependencies {
    compileOnly "com.oplus.gallery.router_lib:annotations:$routerAnnotationsVersion"
    implementationProject(':foundation:libutil')
    implementationProject(':foundation:libsysapi')
    implementation "androidx.fragment:fragment-ktx:$fragmentKtxVersion"

    //For test only
    testImplementation "com.oplus.gallery.router_lib:annotations:$routerAnnotationsVersion"
    kaptTest "com.oplus.gallery.router_lib:compiler:$routerCompilerVersion"
}