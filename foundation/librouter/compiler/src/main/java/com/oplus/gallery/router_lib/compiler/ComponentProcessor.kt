/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ComponentProcessor.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/08/05
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_ROUTER
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** h<PERSON><PERSON><PERSON>@Apps.Gallery		2020/02/24		1.0		OPLUS_ARCH_ROUTER
 *********************************************************************************/

package com.oplus.gallery.router_lib.compiler

import com.google.auto.service.AutoService
import com.oplus.gallery.router_lib.annotations.Component
import com.squareup.kotlinpoet.ClassName
import com.squareup.kotlinpoet.CodeBlock
import com.sun.tools.javac.code.Symbol.ClassSymbol
import java.util.HashMap
import java.util.HashSet
import javax.annotation.processing.Processor
import javax.annotation.processing.RoundEnvironment
import javax.annotation.processing.SupportedSourceVersion
import javax.lang.model.SourceVersion
import javax.lang.model.element.TypeElement

/**
 * 根据注解[Component]，生成动态组件动态源文件
 */
@AutoService(Processor::class)
@SupportedSourceVersion(SourceVersion.RELEASE_8)
class ComponentProcessor : BaseProcessor() {
    override fun getSupportedAnnotationTypes(): Set<String> {
        val set: MutableSet<String> = HashSet()
        set.add(Component::class.java.name)
        return set
    }

    override fun process(annotations: Set<TypeElement?>, roundEnv: RoundEnvironment): Boolean {
        if (annotations.isNullOrEmpty()) {
            println("component is empty")
            return false
        }
        val builder: CodeBlock.Builder = CodeBlock.builder()
        var hash: String? = null
        val cacheMap = HashMap<String, String?>()
        for (element in roundEnv.getElementsAnnotatedWith(Component::class.java)) {
            if (element !is ClassSymbol) {
                continue
            }
            val cls = element

            val component = cls.getAnnotation(Component::class.java) ?: continue
            if (hash.isNullOrEmpty()) {
                hash = hash(cls.className())
            }
            val path = component.interfaceName
            if (cacheMap.containsKey(path) && cacheMap[path] != cls.className()) {
                throw RuntimeException("the service path is same : path: $path class1: ${cacheMap[path]} class2: ${cls.className()}")
            }
            cacheMap[path] = cls.className()
            builder.addStatement(
                "%1T.componentCenter.add(%2L)", ClassName.bestGuess(Constants.ROUTER_CLASS),
                buildRouterMeta(path, cls)
            )
        }
        if (hash.isNullOrEmpty()) {
            hash = randomHash()
        }
        buildHandlerInitClass(
            Constants.GEN_PKG_COMPONENT,
            builder.build(),
            Constants.COMPONENT_INIT_PREFIX + Constants.SPLITTER + hash
        )
        return true
    }

    fun buildRouterMeta(path: String?, cls: ClassSymbol): CodeBlock {
        val b: CodeBlock.Builder = CodeBlock.builder()
        b.add("%1T(%2S, %3S)", ClassName.bestGuess(Constants.COMPONENT_META_CLASS), path, cls.className())
        return b.build()
    }
}