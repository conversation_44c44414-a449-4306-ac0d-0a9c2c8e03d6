/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - RouterProcessor.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/08/05
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_ROUTER
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** h<PERSON><PERSON><PERSON>@Apps.Gallery		2020/02/24		1.0		OPLUS_ARCH_ROUTER
 *********************************************************************************/

package com.oplus.gallery.router_lib.compiler

import com.google.auto.service.AutoService
import com.oplus.gallery.router_lib.annotations.RouterNormal
import com.oplus.gallery.router_lib.annotations.RouterRegex
import com.squareup.kotlinpoet.ClassName
import com.squareup.kotlinpoet.CodeBlock
import com.sun.tools.javac.code.Symbol.ClassSymbol
import java.lang.reflect.InvocationTargetException
import java.util.HashMap
import java.util.HashSet
import javax.annotation.processing.Processor
import javax.annotation.processing.RoundEnvironment
import javax.annotation.processing.SupportedSourceVersion
import javax.lang.model.SourceVersion
import javax.lang.model.element.TypeElement

/**
 * 根据注解[RouterNormal]和[RouterRegex]，生成页面路由源文件
 */
@AutoService(Processor::class)
@SupportedSourceVersion(SourceVersion.RELEASE_8)
class RouterProcessor : BaseProcessor() {
    private var clazzes = arrayOf(RouterNormal::class.java, RouterRegex::class.java)
    override fun getSupportedAnnotationTypes(): Set<String> {
        val set: MutableSet<String> = HashSet()
        for (clazz in clazzes) {
            set.add(clazz.name)
        }
        return set
    }

    override fun process(annotations: Set<TypeElement?>, roundEnv: RoundEnvironment): Boolean {
        if (annotations.isNullOrEmpty()) {
            println("router normal or regex is empty")
            return false
        }
        val builder: CodeBlock.Builder = CodeBlock.builder()
        val hash = StringBuilder()
        for (clazz in clazzes) {
            buildCodes(roundEnv, hash, builder, clazz)
        }
        if (hash.isEmpty()) {
            hash.append(randomHash())
        }
        buildHandlerInitClass(
            Constants.GEN_PKG_ROUTER,
            builder.build(),
            Constants.ROUTER_INIT_PREFIX + Constants.SPLITTER + hash
        )
        return true
    }

    @Throws(
        NoSuchMethodException::class,
        InvocationTargetException::class,
        IllegalAccessException::class
    )
    @Suppress("TooGenericExceptionThrown")
    private fun buildCodes(
        roundEnv: RoundEnvironment,
        hash: StringBuilder,
        builder: CodeBlock.Builder,
        annotationClass: Class<out Annotation>
    ) {
        val cacheMap = HashMap<String, String?>()
        for (element in roundEnv.getElementsAnnotatedWith(annotationClass)) {
            if (element !is ClassSymbol) {
                continue
            }
            val cls = element
            val clsAnnotation = cls.getAnnotation(annotationClass) ?: continue
            val path = annotationClass.getMethod("path").invoke(clsAnnotation) as String
            if (cacheMap.containsKey(path) && cacheMap[path] != cls.className()) {
                throw RuntimeException("the page path is same : path: $path class1: <${cacheMap[path]}> class2: <${cls.className()}>")
            }
            if (!path.startsWith(Constants.URI_ROUTER) && !path.startsWith(Constants.URI_ROUTER_REGEX)) {
                throw RuntimeException("the page path must start with '${Constants.SCHEME_ROUTER}' " +
                        "or '${Constants.URI_ROUTER_REGEX}' class: <${cls.className()}>")
            }
            cacheMap[path] = cls.className()
            println("class name : " + cls.className())
            if (hash.isEmpty()) {
                hash.append(hash(cls.className()))
            }
            builder.addStatement(
                "%1T.routerCenter.add(%2T::class.java, %3L)", ClassName.bestGuess(
                    Constants.ROUTER_CLASS
                ),
                ClassName.bestGuess(annotationClass.name),
                buildRouterMeta(path, cls)
            )
        }
    }

    private fun buildRouterMeta(path: String, cls: ClassSymbol): CodeBlock {
        val b: CodeBlock.Builder = CodeBlock.builder()
        b.add("%1T(%2S, %3S)", ClassName.bestGuess(Constants.ROUTER_META_CLASS), path, cls.className())
        return b.build()
    }
}