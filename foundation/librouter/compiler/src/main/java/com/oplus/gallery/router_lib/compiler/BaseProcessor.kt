/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - BaseProcessor.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/08/05
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_ROUTER
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** h<PERSON><PERSON><PERSON>@Apps.Gallery		2020/02/24		1.0		OPLUS_ARCH_ROUTER
 *********************************************************************************/

package com.oplus.gallery.router_lib.compiler

import com.squareup.kotlinpoet.CodeBlock
import com.squareup.kotlinpoet.FileSpec
import com.squareup.kotlinpoet.FunSpec
import com.squareup.kotlinpoet.KModifier
import com.squareup.kotlinpoet.TypeSpec
import com.squareup.kotlinpoet.UNIT
import java.math.BigInteger
import java.nio.charset.StandardCharsets
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException
import java.util.UUID
import javax.annotation.processing.AbstractProcessor
import javax.annotation.processing.ProcessingEnvironment

abstract class BaseProcessor : AbstractProcessor() {

    @Synchronized
    override fun init(processingEnvironment: ProcessingEnvironment) {
        super.init(processingEnvironment)
    }

    fun buildHandlerInitClass(packageName: String, code: CodeBlock, genClassName: String) {
        val funSpec: FunSpec = FunSpec.builder(Constants.INIT_METHOD)
            .addModifiers(KModifier.PUBLIC)
            .addAnnotation(JvmStatic::class.java)
            .returns(UNIT)
            .addCode(code)
            .build()
        val typeSpec: TypeSpec = TypeSpec.objectBuilder(genClassName)
            .addModifiers(KModifier.PUBLIC)
            .addFunction(funSpec)
            .build()
        FileSpec.builder(packageName, genClassName)
            .addType(typeSpec)
            .build()
            .writeTo(processingEnv.filer)
    }

    companion object {
        private const val RADIX_16 = 16
        private const val RADIX_1 = 1

        @JvmStatic
        fun randomHash(): String {
            return hash(UUID.randomUUID().toString())
        }

        @JvmStatic
        fun hash(str: String): String {
            return try {
                val md = MessageDigest.getInstance("MD5")
                md.update(str.toByteArray(StandardCharsets.UTF_8))
                BigInteger(RADIX_1, md.digest()).toString(RADIX_16)
            } catch (e: NoSuchAlgorithmException) {
                Integer.toHexString(str.hashCode())
            }
        }
    }
}