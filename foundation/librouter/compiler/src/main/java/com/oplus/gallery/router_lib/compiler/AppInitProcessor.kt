/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AppInitProcessor.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/08/05
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_ROUTER
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** h<PERSON><PERSON><PERSON>@Apps.Gallery		2020/02/24		1.0		OPLUS_ARCH_ROUTER
 *********************************************************************************/

package com.oplus.gallery.router_lib.compiler

import com.google.auto.service.AutoService
import com.oplus.gallery.router_lib.annotations.AppInit
import com.squareup.kotlinpoet.ClassName
import com.squareup.kotlinpoet.CodeBlock
import com.sun.tools.javac.code.Symbol.ClassSymbol
import java.util.HashSet
import javax.annotation.processing.Processor
import javax.annotation.processing.RoundEnvironment
import javax.annotation.processing.SupportedSourceVersion
import javax.lang.model.SourceVersion
import javax.lang.model.element.TypeElement

/**
 * 根据注解[AppInit]，生成模块初始化的源文件
 */
@AutoService(Processor::class)
@SupportedSourceVersion(SourceVersion.RELEASE_8)
class AppInitProcessor : BaseProcessor() {
    override fun getSupportedAnnotationTypes(): Set<String> {
        val set: MutableSet<String> = HashSet()
        set.add(AppInit::class.java.name)
        return set
    }

    override fun process(annotations: Set<TypeElement?>, roundEnv: RoundEnvironment): Boolean {
        if (annotations.isNullOrEmpty()) {
            println("appInit is empty")
            return false
        }
        val builder: CodeBlock.Builder = CodeBlock.builder()
        var hash: String? = null
        for (element in roundEnv.getElementsAnnotatedWith(AppInit::class.java)) {
            if (element !is ClassSymbol) {
                continue
            }
            val appInit = element.getAnnotation(AppInit::class.java) ?: continue
            if (hash.isNullOrBlank()) {
                hash = hash(element.className())
            }
            builder.addStatement(
                "%1T.appInitCenter.add(%2L)",
                ClassName.bestGuess(Constants.ROUTER_CLASS),
                buildRouterMeta(element, appInit.sort, appInit.process)
            )
        }
        if (hash.isNullOrBlank()) {
            hash = randomHash()
        }
        buildHandlerInitClass(Constants.GEN_PKG_INIT, builder.build(), Constants.APP_INIT_PREFIX + Constants.SPLITTER + hash)
        return true
    }

    private fun buildRouterMeta(cls: ClassSymbol, sort: Int, initMeta: Array<String>): CodeBlock {
        val b: CodeBlock.Builder = CodeBlock.builder()
        val sb = StringBuilder()
        for (i in initMeta.indices) {
            if (i != 0) {
                sb.append(",")
            }
            sb.append("\"").append(initMeta[i]).append("\"")
        }
        b.add("%1T(%2S, %3L, arrayOf(%4L))", ClassName.bestGuess(Constants.INIT_META_CLASS), cls.className(), sort, sb.toString())
        return b.build()
    }
}