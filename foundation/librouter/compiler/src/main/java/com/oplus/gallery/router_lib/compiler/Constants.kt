/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - Const.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/08/05
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_ROUTER
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** h<PERSON><PERSON><PERSON>@Apps.Gallery		2020/02/24		1.0		OPLUS_ARCH_ROUTER
 *********************************************************************************/

package com.oplus.gallery.router_lib.compiler

object Constants {
    private const val PKG = "com.oplus.gallery.router_lib"
    private const val GEN_PKG = "$PKG.generated"
    const val GEN_PKG_COMPONENT = "$GEN_PKG.component"
    const val GEN_PKG_ROUTER = "$GEN_PKG.router"
    const val GEN_PKG_INIT = "$GEN_PKG.init"
    const val SPLITTER = "_"
    const val ROUTER_META_CLASS = "$PKG.meta.RouterMeta"
    const val COMPONENT_META_CLASS = "$PKG.meta.ComponentMeta"
    const val INIT_META_CLASS = "$PKG.meta.InitMeta"
    const val ROUTER_CLASS = "$PKG.RouterManager"
    const val APP_INIT_PREFIX = "AppInit"
    const val ROUTER_INIT_PREFIX = "RouterInit"
    const val COMPONENT_INIT_PREFIX = "ComponentInit"
    const val INIT_METHOD = "init"
    const val SYMBOL_URI_HEAD = "://"
    const val SCHEME_ROUTER = "router"
    const val SCHEME_ROUTER_REGEX = "routerRegex"
    const val URI_ROUTER = SCHEME_ROUTER + SYMBOL_URI_HEAD
    const val URI_ROUTER_REGEX = SCHEME_ROUTER_REGEX + SYMBOL_URI_HEAD
}