/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - GalleryMonitorTest.kt
 * Description: 监控器单元测试类
 * Version: 1.0
 * Date: 2022/3/30
 ** Author: <EMAIL>
 * TAG:
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                   <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * <EMAIL>      2022/3/30     1.0
 *************************************************************************************************/
package com.oplus.gallery.foundation.taskscheduling.monitor

import android.content.Context
import android.os.Debug
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockkStatic
import io.mockk.spyk
import org.junit.After
import org.junit.Before
import org.junit.Test

class GalleryMonitorTest {

    @MockK
    lateinit var mockContext: Context

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        mockkStatic(Debug::class)
    }

    @After
    fun tearDown() {
        // do nothing
    }

    @Test
    fun `should return true when runMonitorTask with threshold`() {
        // given
        val scanMonitor = spyk(GalleryMonitor())
        val monitorStrategy = MemoryMonitorStrategy()
        scanMonitor.addMonitorStrategy(monitorStrategy)
        every { Debug.getNativeHeapSize() } returns MemoryMonitorStrategy.NATIVE_HEAP_SIZE_THRESHOLD_HIGH
        var isMonitorRecord = false
        val scanMonitorListener = object : IMonitorListener {
            override fun onMonitorRecord(event: MonitorEvent, reachThreshold: Boolean) {
                isMonitorRecord = true
            }
        }
        monitorStrategy.registerRecordListener(scanMonitorListener)
        // when
        scanMonitor.runMonitorTask()
        monitorStrategy.unregisterRecordListener(scanMonitorListener)
        // than
        assert(isMonitorRecord)
    }
}