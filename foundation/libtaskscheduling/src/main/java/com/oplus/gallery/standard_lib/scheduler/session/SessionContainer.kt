/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ContainerSession.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/09/11
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_SCHEDULER
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** h<PERSON><PERSON><PERSON>@Apps.Gallery		2020/09/11		1.0		init
 *********************************************************************************/

package com.oplus.gallery.standard_lib.scheduler.session

import com.oplus.gallery.standard_lib.scheduler.PriorityScheduler
import com.oplus.gallery.standard_lib.scheduler.Worker
import java.util.concurrent.locks.ReentrantReadWriteLock
import kotlin.concurrent.read
import kotlin.concurrent.write

/**
 * session的容器，创建的session会先储存在容器中
 */
class SessionContainer internal constructor(
    name: String?,
    level: SessionLevel,
    lock: ReentrantReadWriteLock,
    scheduler: PriorityScheduler
) : Session<WorkerSession>(name, Int.MAX_VALUE, level, lock, scheduler) {
    /**
     * 添加的session会在此存放
     */
    private val sessionMap = HashMap<String, WorkerSession>()

    private val needRunSessions = LinkedHashSet<Session<*>>()

    override fun size(): Int = lock.read {
        return sessionMap.values.sumBy {
            it.size()
        }
    }

    /**
     * 添加的session，添加后会注册session变化时的通知接口[observe]，当[WorkerSession]有任务添加时，会回调此接口。
     * 由于[WorkerSession]没有在[SessionStack]中，所以其在栈中的行为只能让[SessionContainer]容器代理，
     * 所以添加的任务只能让[SessionContainer]容器代为添加到[Session.queue]中，存储的不是真正的任务，而是哪个Session，取任务时再在Session中获取
     */
    fun addSession(session: WorkerSession): Unit = lock.write {
        sessionMap[session.tag] = session
        session.observe {
            lock.write {
                if (it.canPollWorker()) {
                    needRunSessions.add(it)
                    sessionChange?.invoke(this)
                } else {
                    needRunSessions.remove(it)
                }
            }
        }
        if (session.canPollWorker()) {
            needRunSessions.add(session)
        }
    }

    /**
     * 从容器中移除session
     */
    fun removeSession(session: WorkerSession): Unit = lock.write {
        sessionMap.remove(session.tag)
        needRunSessions.remove(session)
        session.observe(null)
    }

    /**
     * 获取任务，由于容器[SessionContainer]是没有具体任务的，所以获取的任务[Worker]需要从存储的[WorkerSession]中获取
     */
    override fun pollWorker(): Worker<*>? = lock.write {
        val iterator = needRunSessions.iterator()
        while (iterator.hasNext()) {
            val next = iterator.next()
            val worker = next.pollWorker()
            if (!next.canPollWorker()) {
                iterator.remove()
            }
            if (worker != null) {
                return@write worker
            }
        }
        return@write null
    }

    /**
     * 通过tag查找session
     */
    fun findSession(tag: String): WorkerSession? = lock.read {
        return sessionMap[tag]
    }

    override fun toString(): String {
        return "SessionContainer(sessionMap=$sessionMap) ${super.toString()}"
    }
}