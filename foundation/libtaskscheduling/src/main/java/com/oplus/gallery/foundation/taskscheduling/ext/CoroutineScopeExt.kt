/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - CoroutineScopeExt.kt
 ** Description:
 ** CoroutineScope的扩展方法类
 ** Version: 1.0
 ** Date: 2022/06/10
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/06/10    1.0                create
 ******************************************************************************/

package com.oplus.gallery.foundation.taskscheduling.ext

import android.os.Looper
import com.oplus.gallery.standard_lib.app.CPU
import com.oplus.gallery.standard_lib.thread.Future
import com.oplus.gallery.standard_lib.thread.FutureListener
import com.oplus.gallery.standard_lib.thread.ThreadPool
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.CoroutineStart
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.async
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlin.coroutines.CoroutineContext
import kotlin.coroutines.EmptyCoroutineContext

/**
 * Marked by zhangwenming. 该类最终是放在libtaskscheduling中。但因taskscheduling还未迁移，目前还不能直接放到该lib中
 * 否则会出现与standard_lib循环依赖。包名暂时设定为最终值，待libtaskscheduling一起迁移。
 */
private const val TAG = "CoroutineScopeExt"

private class FutureImpl<T>(val jobWrapper: JobWrapper, val t: T?, val isCancel: Boolean) : Future<T> {
    override fun cancel() = Unit

    override fun isCancelled(): Boolean = isCancel

    override fun isDone(): Boolean = true

    override fun get(): T? = t

    override fun waitDone() = Unit
    override fun getJob(): Job? {
        return jobWrapper.job
    }
}

private class JobWrapper {
    companion object {
        private const val MAX_WAIT_TIME = 100L //ms
    }

    private val lock = Object()

    @Volatile
    var job: Job? = null
        set(value) {
            synchronized(lock) {
                field = value
                lock.notifyAll()
            }
        }
        get() {
            synchronized(lock) {
                if (field == null) {
                    runCatching {
                        lock.wait(MAX_WAIT_TIME)
                    }.onFailure {
                        GLog.e(TAG, LogFlag.DL, "job get : ", it)
                    }
                }
            }
            return field
        }
}

fun <T> CoroutineScope.async(
    context: CoroutineContext = EmptyCoroutineContext,
    start: CoroutineStart = CoroutineStart.DEFAULT,
    block: ThreadPool.Job<T>,
    futureListener: FutureListener<T>? = null
): Deferred<T> {
    val jobWrapper = JobWrapper()
    return async(context, start) {
        val result = runCatching {
            block.run(object : ThreadPool.JobContext {
                override fun isCancelled(): Boolean {
                    return !<EMAIL>
                }

                override fun setCancelListener(listener: ThreadPool.CancelListener?) = Unit

                override fun setMode(mode: Int): Boolean = true
            })
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "async: ", it)
        }
        futureListener?.onFutureDone(FutureImpl(jobWrapper, result.getOrNull(), !<EMAIL>))
        result.getOrThrow()
    }.also {
        jobWrapper.job = it
    }
}

fun <T> CoroutineScope.launch(
    context: CoroutineContext = EmptyCoroutineContext,
    start: CoroutineStart = CoroutineStart.DEFAULT,
    block: ThreadPool.Job<T>,
    futureListener: FutureListener<T>? = null
): Job {
    val jobWrapper = JobWrapper()
    return launch(context, start) {
        val result = runCatching {
            block.run(object : ThreadPool.JobContext {
                override fun isCancelled(): Boolean {
                    return !<EMAIL>
                }

                override fun setCancelListener(listener: ThreadPool.CancelListener?) = Unit

                override fun setMode(mode: Int): Boolean = true
            })
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "launch: ", it)
        }
        futureListener?.onFutureDone(FutureImpl(jobWrapper, result.getOrNull(), !<EMAIL>))
        result.getOrThrow()
    }.also {
        jobWrapper.job = it
    }
}

/**
 * 如果当前线程为主线程则将任务放在新的协程中处理。
 * 如果当前线程不是主线程则继续在原线程中处理block块中的任务。
 */
@Suppress("UnSignedMustLowCaseRule")
fun CoroutineScope.runNotOnUiThread(
    newCoroutineDispatchers: CoroutineDispatcher = Dispatchers.CPU,
    block: () -> Unit
) {
    if (Looper.getMainLooper()?.isCurrentThread == true) {
        launch(newCoroutineDispatchers) {
            block()
        }
    } else {
        block()
    }
}