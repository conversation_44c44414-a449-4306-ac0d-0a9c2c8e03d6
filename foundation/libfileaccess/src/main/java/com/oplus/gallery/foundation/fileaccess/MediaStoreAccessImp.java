/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File        :
 ** Description : MediaStoreAccessImp.
 ** Version     : V1.0
 ** Date        : 2020-03-10
 ** Author      : dingyong@Apps.Gallery3D
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                    <date>          <version>      <desc>
 **  dingyong@Apps.Gallery3D     2020-03-10       V1.0          MediaStoreAccessImp
 ***********************************************************************/
package com.oplus.gallery.foundation.fileaccess;

import android.content.Context;
import android.net.Uri;
import android.os.ParcelFileDescriptor;

import androidx.annotation.Nullable;

import com.oplus.gallery.foundation.util.thread.ThreadUtils;
import com.oplus.gallery.foundation.fileaccess.data.CopyFileRequest;
import com.oplus.gallery.foundation.fileaccess.data.DeleteFileRequest;
import com.oplus.gallery.foundation.fileaccess.data.NewFileRequest;
import com.oplus.gallery.foundation.fileaccess.data.OpenFileRequest;
import com.oplus.gallery.foundation.fileaccess.data.RenameToFileRequest;
import com.oplus.gallery.foundation.fileaccess.data.FileResponse;
import com.oplus.gallery.foundation.fileaccess.helper.MediaStoreApplyBatchMoveFileHelper;
import com.oplus.gallery.foundation.fileaccess.helper.MediaStoreCopyFileHelper;
import com.oplus.gallery.foundation.fileaccess.helper.MediaStoreCreateFileHelper;
import com.oplus.gallery.foundation.fileaccess.helper.MediaStoreDeleteFileHelper;
import com.oplus.gallery.foundation.fileaccess.helper.MediaStoreMoveFileHelper;
import com.oplus.gallery.foundation.fileaccess.helper.MediaStoreOpenFileHelper;
import com.oplus.gallery.standard_lib.file.File;
import com.oplus.gallery.foundation.util.permission.RuntimePermissionUtils;
import com.oplus.gallery.foundation.util.debug.GLog;

import java.io.FileNotFoundException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;

public final class MediaStoreAccessImp implements IFileAccessInterface {
    public static final String TAG = "MediaStoreAccessImp";
    private static volatile MediaStoreAccessImp sInstance;

    private MediaStoreAccessImp() {

    }

    public static MediaStoreAccessImp getInstance() {
        if (sInstance == null) {
            synchronized (MediaStoreAccessImp.class) {
                if (sInstance == null) {
                    sInstance = new MediaStoreAccessImp();
                }
            }
        }
        return sInstance;
    }

    @Override
    public ParcelFileDescriptor openFile(Context context, OpenFileRequest request)
            throws FileNotFoundException {
        checkIsMainThread();
        if (context == null) {
            GLog.e(TAG, "openFile context is null");
            return null;
        }
        if (request == null) {
            GLog.e(TAG, "openFile request is null");
            return null;
        }
        return MediaStoreOpenFileHelper.openFile(context, request);
    }

    @Override
    public ParcelFileDescriptor openFile(Context context, Uri uri) throws FileNotFoundException {
        if ((context == null) || (uri == null)) {
            return null;
        }
        ParcelFileDescriptor parcelFileDescriptor = null;
        try {
            parcelFileDescriptor = context.getContentResolver().openFileDescriptor(uri, FileConstants.FileMode.MODE_READ);
        } catch (Throwable th) {
            GLog.e(TAG, "openFile, th = " + th);
        }
        return parcelFileDescriptor;
    }


    @Override
    public FileResponse newCreateFile(Context context, NewFileRequest request) {
        if (context == null) {
            GLog.e(TAG, "newCreateFile context is null");
            return null;
        }
        if (request == null) {
            GLog.e(TAG, "newCreateFile request is null");
            return null;
        }
        checkIsMainThread();
        return MediaStoreCreateFileHelper.newCreateFile(context, request);
    }

    @Override
    public boolean delete(Context context, DeleteFileRequest request) {
        if (context == null) {
            GLog.e(TAG, "newCreateFile context is null");
            return false;
        }
        if (request == null) {
            GLog.e(TAG, "newCreateFile request is null");
            return false;
        }
        checkIsMainThread();
        return MediaStoreDeleteFileHelper.delete(context, request);
    }


    @Override
    @Nullable
    public FileResponse renameTo(Context context, RenameToFileRequest renameRequest) {
        if (context == null) {
            GLog.e(TAG, "renameTo context is null");
            return null;
        }
        if (renameRequest == null) {
            GLog.e(TAG, "renameTo request is null");
            return null;
        }
        checkIsMainThread();
        return MediaStoreMoveFileHelper.renameTo(context, renameRequest);
    }

    @Override
    public boolean mkdirs(Context context, File file) {
        return true;
    }

    @Override
    public InputStream getInputStream(Context context, OpenFileRequest request)
            throws FileNotFoundException {
        if (context == null) {
            GLog.e(TAG, "getInputStream context is null");
            return null;
        }
        if (request == null) {
            GLog.e(TAG, "getInputStream request is null");
            return null;
        }
        checkIsMainThread();
        return MediaStoreOpenFileHelper.getInputStream(context, request);
    }

    @Override
    public OutputStream getOutStream(Context context, OpenFileRequest request)
            throws FileNotFoundException {
        if (context == null) {
            GLog.e(TAG, "getOutStream context is null");
            return null;
        }
        if (request == null) {
            GLog.e(TAG, "getOutStream request is null");
            return null;
        }
        checkIsMainThread();
        return MediaStoreOpenFileHelper.getOutStream(context, request);

    }

    @Override
    @Nullable
    public FileResponse copyFile(Context context, CopyFileRequest request) {
        if (context == null) {
            GLog.e(TAG, "copyFile context is null");
            return null;
        }
        if (request == null) {
            GLog.e(TAG, "copyFile request is null");
            return null;
        }
        checkIsMainThread();
        return MediaStoreCopyFileHelper.copyFile(context, request);
    }

    @Override
    public List<FileResponse> renameFromPrivateToPublic(Context context, List<RenameToFileRequest> requestList) {
        checkIsMainThread();
        if (RuntimePermissionUtils.hasManageExternalStorage()) {
            return MediaStoreApplyBatchMoveFileHelper.privateToPublicHasManagerExternal(context, requestList);
        } else {
            return MediaStoreApplyBatchMoveFileHelper.privateToPublicNoManagerExternal(context, requestList);
        }
    }

    @Override
    public List<FileResponse> renameFromPublicToPrivate(Context context, List<RenameToFileRequest> requestList) {
        checkIsMainThread();
        if (RuntimePermissionUtils.hasManageExternalStorage()) {
            return MediaStoreApplyBatchMoveFileHelper.publicToPrivateHasManagerExternal(context, requestList);
        } else {
            return MediaStoreApplyBatchMoveFileHelper.publicToPrivateNoManagerExternal(context, requestList);
        }
    }

    private void checkIsMainThread() {
        ThreadUtils.assertInSubThread();
    }
}
