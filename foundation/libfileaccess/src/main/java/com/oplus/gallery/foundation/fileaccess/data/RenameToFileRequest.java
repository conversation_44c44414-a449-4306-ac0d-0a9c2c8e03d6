/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File        :
 ** Description : OpenFileRequest.
 ** Version     : V1.0
 ** Date        : 2020-03-10
 ** Author      : dingyong@Apps.Gallery3D
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                    <date>          <version>      <desc>
 **  dingyong@Apps.Gallery3D     2020-03-10       V1.0          RenameToFileRequest
 ***********************************************************************/
package com.oplus.gallery.foundation.fileaccess.data;

import android.content.ContentValues;
import android.net.Uri;

import com.oplus.gallery.foundation.fileaccess.FileConstants;
import com.oplus.gallery.standard_lib.file.File;


public class RenameToFileRequest {
    /**
     * 源文件，必传参数
     */
    private File mSrcFile;

    /**
     * 目标文件，必传参数
     */
    private File mTargetFile;
    /**
     * 源文件的完整uri
     * 沙箱上打开文件，必须指定文件的完整uri
     * 图片：content://media/volumeName/images/media/123
     * 视频：content://media/volumeName/video/media/123
     * <p>
     * 非必传参数
     * 如果调用处能拿到uri，尽量传入，否则后面需要通过filePath和mediaId 去获取
     */
    private Uri mUri;

    /**
     * 源文件是否为图片，重命名的时候需要读写文件。写文件之前，要通过uri打开文件
     * 沙箱模式下，媒体库打开文件的时候 必须要指定uri类型
     * 如果是图片，则必须用  image的uri ：MediaStore.Images.Media.EXTERNAL_CONTENT_URI
     * 如果是视频，则必须用  video的uri ：MediaStore.Video.Media.EXTERNAL_CONTENT_URI
     * 不能用file类型的uri
     * <p>
     * 非必传参数，
     * <p>
     * 如有调用处能够知道是图片还是视频，则尽量传入该参数，
     * 否则后续需要通过接口来获取，有一定的耗时
     */
    private Boolean mIsImage;

    /**
     * 源文件的 mediaId
     * 沙箱上打开文件，必须指定文件的完整uri
     * 通过 Uri.withAppendedPath(baseUri, mMediaId)  去拼接完整uri
     * 非必传参数：如果已经传入了Uri 则不需要传入该值。
     */
    private String mMediaId;

    /**
     * 非必传参数，如果不传入，新建文件的时候，会默认填充一些默认数据。
     * 1.从 DCIM Pictures Movies 移动文件到 Documents和Download目录下，需要先复制文件，
     * 然后插入到Documents和Download目录下，因此可能需要mContentValues
     * 2.从私有目录移动到公共目录下，需要插入媒体库。，因此可能需要mContentValues
     */
    private ContentValues mContentValues;
    /**
     * 当目标文件存在时，是否需要覆盖文件
     */
    private boolean mIsCoverFile;
    /**
     * 重命名后是否需要删除源文件
     */
    private boolean mIsDeleteSrcFile = true;

    /**
     * 只写入流 不操作媒体库
     */
    private boolean mOnlyWriteStreams = false;

    /**
     * 是否是otg文件
     */
    private boolean mIsOtg = false;
    /**
     * 是否是外置存储文件(如T卡)
     */
    private boolean mIsSdCard = false;

    public RenameToFileRequest(Builder builder) {
        mSrcFile = builder.mSrcFile;
        mIsImage = builder.mIsImage;
        mTargetFile = builder.mTargetFile;
        mMediaId = builder.mMediaId;
        mUri = builder.mUri;
        mContentValues = builder.mContentValues;
        mIsCoverFile = builder.mIsCoverFile;
        mIsDeleteSrcFile = builder.mIsDeleteSrcFile;
        mOnlyWriteStreams = builder.mOnlyWriteStreams;
        mIsOtg = builder.mIsOtg;
        mIsSdCard = builder.mIsSdCard;
    }


    public boolean isCoverFile() {
        return mIsCoverFile;
    }

    public void setUri(Uri mUri) {
        this.mUri = mUri;
    }

    public void setIsImage(Boolean mIsImage) {
        this.mIsImage = mIsImage;
    }

    public void setMediaId(String mMediaId) {
        this.mMediaId = mMediaId;
    }

    public void setContentValues(ContentValues mContentValues) {
        this.mContentValues = mContentValues;
    }

    public void setIsDeleteSrcFile(boolean isDeleteSrcFile) {
        mIsDeleteSrcFile = isDeleteSrcFile;
    }

    public void setIsCoverFile(boolean mIsCoverFile) {
        this.mIsCoverFile = mIsCoverFile;
    }

    public boolean isDeleteSrcFile() {
        return mIsDeleteSrcFile;
    }

    public boolean isOnlyWriteStreams() {
        return mOnlyWriteStreams;
    }

    public boolean isIsOtg() {
        return mIsOtg;
    }

    public boolean isIsSdCard() {
        return mIsSdCard;
    }
    /**
     * 构建必要参数的 request
     *
     * @param srcFile    File
     * @param targetFile File
     * @return RenameToFileRequest
     */
    public static RenameToFileRequest buildRequest(File srcFile, File targetFile) {
        return new RenameToFileRequest.Builder()
                .setSrcFile(srcFile)
                .setTargetFile(targetFile)
                .builder();
    }

    public static class Builder {
        private File mSrcFile;
        private Boolean mIsImage;
        private File mTargetFile;
        private String mMediaId;
        private Uri mUri;
        private ContentValues mContentValues;
        private boolean mIsCoverFile;
        private boolean mIsDeleteSrcFile = true;
        /**
         * 只写入流 不操作媒体库
         */
        private boolean mOnlyWriteStreams = false;
        /**
         * 是否是otg文件
         */
        private boolean mIsOtg = false;
        /**
         * 是否是外置存储文件(如T卡)
         */
        private boolean mIsSdCard = false;

        public Builder setSrcFile(File file) {
            mSrcFile = file;
            return this;
        }

        public Builder setSrcFile(String path) {
            mSrcFile = new File(path);
            return this;
        }

        public Builder setImage(Boolean image) {
            mIsImage = image;
            return this;
        }

        public Builder setImage(int mediaType) {
            mIsImage = (mediaType == FileConstants.MediaType.MEDIA_TYPE_IMAGE);
            return this;
        }

        public Builder setTargetFile(File targetFile) {
            mTargetFile = targetFile;
            return this;
        }

        public Builder setMediaId(String mediaId) {
            mMediaId = mediaId;
            return this;
        }

        public Builder setMediaId(int mediaId) {
            mMediaId = String.valueOf(mediaId);
            return this;
        }

        public Builder setUri(Uri uri) {
            mUri = uri;
            return this;
        }

        public Builder setContentValues(ContentValues contentValues) {
            mContentValues = contentValues;
            return this;
        }

        public Builder setIsDeleteSrcFile(boolean isDeleteSrcFile) {
            mIsDeleteSrcFile = isDeleteSrcFile;
            return this;
        }

        public Builder setCoverFile(boolean isCoverFile) {
            mIsCoverFile = isCoverFile;
            return this;
        }

        public Builder setOnlyWriteStreams(boolean onlyWriteStreams) {
            mOnlyWriteStreams = onlyWriteStreams;
            return this;
        }

        public Builder setIsOtg(boolean isOtg) {
            mIsOtg = isOtg;
            return this;
        }

        public Builder setIsSdcard(boolean isSdcard) {
            mIsSdCard = isSdcard;
            return this;
        }

        public RenameToFileRequest builder() {
            return new RenameToFileRequest(this);
        }
    }

    public Boolean isImage() {
        return mIsImage;
    }

    public File getSrcFile() {
        return mSrcFile;
    }

    public File getTargetFile() {
        return mTargetFile;
    }

    public String getMediaId() {
        return mMediaId;
    }

    public Uri getUri() {
        return mUri;
    }

    public ContentValues getContentValues() {
        return mContentValues;
    }

    public String getSrcFilePath() {
        return mSrcFile.getAbsolutePath();
    }

    public String getTargetFilePath() {
        return getTargetFile().getAbsolutePath();
    }


    @Override
    public String toString() {
        return "RenameToFileRequest{"
                + "mSrcFile=" + mSrcFile
                + ", mTargetFile=" + mTargetFile
                + ", mUri=" + mUri
                + ", mIsImage=" + mIsImage
                + ", mMediaId='" + mMediaId
                + ", mContentValues=" + mContentValues
                + '}';
    }


}
