plugins {
    id 'java-library'
    id 'kotlin'
}

jar {
    manifest {
        attributes("Lint-Registry": "com.oplus.gallery.foundation.codecheck.GalleryIssueRegistry")
        attributes("Lint-Registry-v2": "com.oplus.gallery.foundation.codecheck.GalleryIssueRegistry")
    }
}

java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

dependencies {
    implementation fileTree(include: ['.jar'], dir: 'libs')
    //noinspection GradleDependency
   compileOnly "com.android.tools.lint:lint-api:${prop_anLintVersion}"
    //noinspection GradleDependency
    compileOnly "com.android.tools.lint:lint-checks:${prop_anLintVersion}"
    // Lint Testing
    //noinspection GradleDependency
    testImplementation "com.android.tools.lint:lint:${prop_anLintVersion}"
    testImplementation "com.android.tools.lint:lint-tests:${prop_anLintVersion}"

    //Olint 本地代码漏洞扫描检查， 自动加载最新版本,可以获取最新扫描规则
    compileOnly "com.sectools.check:olint:${olintVersion}"
}
