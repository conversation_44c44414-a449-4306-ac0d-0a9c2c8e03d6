/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File        :  - ColorSpaceFilterManager.java
 ** Description : XXXXXXXXXXXXXXXXXXXXX.
 ** Version     : 1.0
 ** Date        : 2020/11/19
 ** Author      : YongQ<PERSON>.Liu@Apps.Gallery3D
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                 <data>      <version>  <desc>
 **  YongQi.Liu@Apps.Gallery3D  2020/11/19  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.foundation.effect

import android.graphics.ColorSpace
import com.oplus.gallery.foundation.opengl.filter.IFilter
import com.oplus.gallery.foundation.util.debug.GLog

object ColorSpaceFilterManager {
    fun getFilter(source: ColorSpace, target: ColorSpace): IFilter {
        when (source) {
            ColorSpace.get(ColorSpace.Named.DCI_P3) -> {
                when (target) {
                    ColorSpace.get(ColorSpace.Named.SRGB) -> return DCIP3ToSrgbFilter()
                    ColorSpace.get(ColorSpace.Named.DISPLAY_P3) -> return DCIP3ToDisplayP3Filter()
                }
            }
            ColorSpace.get(ColorSpace.Named.SRGB) -> {
                when (target) {
                    ColorSpace.get(ColorSpace.Named.DISPLAY_P3) -> return SrgbToDisPlayP3Filter()
                    ColorSpace.get(ColorSpace.Named.DCI_P3) -> return SrgbToDCIP3Filter()
                }
            }
            ColorSpace.get(ColorSpace.Named.DISPLAY_P3) -> {
                when (target) {
                    ColorSpace.get(ColorSpace.Named.DCI_P3) -> return DisPlayP3ToDCIP3Filter()
                    ColorSpace.get(ColorSpace.Named.SRGB) -> return DisPlayP3ToSrgbFilter()
                }
            }
        }
        GLog.e(ColorSpaceFilter.TAG, " getFilter EmptyFilter")
        return EmptyFilter()
    }
}