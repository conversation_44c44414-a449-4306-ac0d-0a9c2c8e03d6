/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IActivityLifecycleObserver
 ** Description:
 ** Version: 1.0
 ** Date : 2024/1/26
 ** Author: wudanyang
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  wudanyang                      2024/1/26    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.foundation.archv2.lifecycle

/**
 * 生命周期观察者，来源有二：
 * 1、activity/fragment的生命周期；
 * 2、页面切换时模拟的activity/fragment生命周期。
 */
interface ILifecycleObserver {

    /**
     * 创建时回调
     */
    fun onCreate(): Unit = Unit

    /**
     * 首次创建或onStop后恢复时回调
     */
    fun onStart(): Unit = Unit

    /**
     * 首次创建或onPause后恢复时回调
     */
    fun onResume(): Unit = Unit

    /**
     * onPause
     */
    fun onPause(): Unit = Unit

    /**
     * onStop
     */
    fun onStop(): Unit = Unit

    /**
     * onDestroy
     */
    fun onDestroy(): Unit = Unit
}

