/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File        :  - IFilter.java
 ** Description : XXXXXXXXXXXXXXXXXXXXX.
 ** Version     : 1.0
 ** Date        : 2020/11/18
 ** Author      : YongQi.Liu@Apps.Gallery3D
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                 <data>      <version>  <desc>
 **  YongQi.Liu@Apps.Gallery3D  2020/11/18  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.foundation.opengl.filter

import android.graphics.Rect
import com.oplus.gallery.foundation.opengl.renderer.GLCanvas
import com.oplus.gallery.foundation.opengl.renderer.IBaseTexture

interface IFilter {
    fun draw(texture: IBaseTexture, canvas: GLCanvas): Int
    fun draw(texture: IBaseTexture, canvas: GLCanvas, rect: ArrayList<Rect>): Int
    fun combine(filter: IFilter)
}