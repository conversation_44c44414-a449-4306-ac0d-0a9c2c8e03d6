/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File        :  - IUploader.java
 ** Description : XXXXXXXXXXXXXXXXXXXXX.
 ** Version     : 1.0
 ** Date        : 2020/10/20
 ** Author      : YongQi.Liu@Apps.Gallery3D
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                 <data>      <version>  <desc>
 **  YongQi.Liu@Apps.Gallery3D  2020/10/20  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.foundation.opengl.uploader

import com.oplus.gallery.foundation.opengl.renderer.GLCanvas
import com.oplus.gallery.foundation.opengl.renderer.IBaseTexture
import com.oplus.gallery.standard_lib.codec.ImageData

/**
 * Marked by zhangwenming. 该类最合适的位置，还是放在libopengl中(包名com.oplus.opengl.foundation.renderer)。但前提是要去掉对ImageData的依赖。即不要依赖于libcodec。
 * 考虑将imageData参数设置为泛型，改造成 interface IUploader<T>。但这个改造需要结合ImageData、YuvUploader、BitmapUploader一起重构。
 */
interface IUploader {
    fun getTarget(): Int

    fun uploadSubTexture(canvas: GLCanvas, texture: IBaseTexture, xOffset: Int, yOffset: Int, imageData: ImageData)

    fun uploadSubTexture(canvas: GLCanvas, texture: IBaseTexture, xOffset: Int, yOffset: Int, imageData: ImageData, listener: OnUploadedListener)

    fun setTextureParameters(canvas: GLCanvas, texture: IBaseTexture)

    fun uploadTexture(canvas: GLCanvas, texture: IBaseTexture, imageData: ImageData)

    fun uploadTexture(canvas: GLCanvas, texture: IBaseTexture, imageData: ImageData, listener: OnUploadedListener)


    fun initializeTextureSize(canvas: GLCanvas, texture: IBaseTexture, imageData: ImageData, texId: Int)


    interface OnUploadedListener {
        fun onTextureUploaded()
    }
}