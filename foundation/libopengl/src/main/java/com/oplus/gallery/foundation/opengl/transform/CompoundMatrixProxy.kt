/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - CompoundMatrixProxy.kt
 * Description:
 * Version: 1.0
 * Date: 2021/2/20
 ** Author: <EMAIL>
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                   <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * <EMAIL>   2021/2/20      1.0          build this module
 *************************************************************************************************/
package com.oplus.gallery.foundation.opengl.transform

import android.graphics.RectF
import com.oplus.gallery.standard_lib.app.AppConstants.Number

class CompoundMatrixProxy : Matrix() {

    companion object {
        const val TAG = "CompoundMatrixProxy"
        const val INDEX_Z_TRANSLATE = 14
    }

    /**
     * 当前包含的单次旋转及之前的所有镜像效果信息
     */
    var axisAngleTransform: AxisAngleTransform = AxisAngleTransform.Empty

    /**
     * 梯形校正的数据对象封装
     */
    var tiltTransform: TiltTransform = TiltTransform.InitTransform

    /**
     * 应用绘制局部区域效果
     */
    var drawPartialArea = false

    /**
     * 局部裁剪的点的坐标,为4*4矩阵
     * 其中(0,0)(0,1)存储第一个点,(1,0)(1,1)存储第二个点,(2,0)(2,1)存储第三个点,(3,0)(3,1)存储第四个点
     * 记录的四个点从texture画面左上角开始依次逆时针记录
     */
    private val points: FloatArray = floatArrayOf(
        0f, 0f, 0f, 0f,
        0f, 1f, 0f, 0f,
        1f, 1f, 0f, 0f,
        1f, 0f, 0f, 0f
    )

    /**
     * 将当前的普通变换和旋转变换叠加组成一个只有最终变换(finalTransform)的Matrix()
     */
    var finalMatrix: Matrix = Matrix()
        .apply {
            System.arraycopy(<EMAIL>, 0, this.mMatrix, 0, MATRIX_COUNT_WITH_TEMP)
        }
        private set

    /**
     * [finalMatrix]除了梯形变换的所有变换的矩阵
     */
    val finalMatrixWithoutTilt: Matrix
        get() = Matrix().apply {
            set(finalMatrix)
            val tiltInvert = tempMatrix
            tiltInvert.reset()
            tiltTransform.transform.invert(tiltInvert)
            preConcat(tiltInvert)
        }

    private val tempMatrix = finalMatrix

    override fun reset() {
        super.reset()
        axisAngleTransform.reset()
        tiltTransform.reset()
        updateFinalMatrix(this)
    }

    /**
     * 输出裁剪的四边形区域具体位置
     */
    fun getPartialAreaPointList(imageWidth: Float, imageHeight: Float, clipRectF: RectF): FloatArray? {
        val finalInvert = tempMatrix
        finalInvert.reset()
        finalMatrix.invert(finalInvert)
        decodeClipRect(finalInvert, clipRectF, imageWidth, imageHeight)
        return if (drawPartialArea) {
            points
        } else {
            null
        }
    }

    fun applyAxisAngleTransform(matrixProxy: CompoundMatrixProxy) {
        axisAngleTransform.apply(matrixProxy.axisAngleTransform)
    }

    fun applyTiltTransform(matrixProxy: CompoundMatrixProxy) {
        tiltTransform.apply(matrixProxy.tiltTransform)
    }

    private fun cloneBaseMatrix(): CompoundMatrixProxy {
        return CompoundMatrixProxy().apply {
            System.arraycopy(<EMAIL>, 0, this.mMatrix, 0, MATRIX_COUNT_WITH_TEMP)
        }
    }

    fun makeFinalMatrix(centerX: Float, centerY: Float) {
        val rotateConfig = axisAngleTransform

        val oriTrans = this.cloneBaseMatrix()
        val formatDegree = rotateConfig.formatAngle
        val axis = rotateConfig.axis

        // 1. 计算原本变换下的图像中心点
        val oriTransInvert = tempMatrix
        oriTransInvert.reset()
        oriTrans.invert(oriTransInvert)
        val oriCenterPoint = Vector(
            centerX,
            centerY,
            0f,
            Vector.POINT
        )
        oriTransInvert.mapVector(oriCenterPoint)
        //oriCenterPoint为图像中的点,这个点是未来的画面中点

        // 2. 在原本变换的基础上,移动图片中心至原点
        val finalTrans = this.cloneBaseMatrix()
        finalTrans.translate(-oriCenterPoint.x, -oriCenterPoint.y)

        // 3. 应用过去的所有镜像旋转及当前正动画中的镜像
        finalTrans.preConcat(rotateConfig.allPastTransform)
        finalTrans.rotate(formatDegree, axis.x, axis.y, axis.z)

        // 4. 计算新变换中的中点是图像中那个点
        val withRotateTransInvert = tempMatrix
        withRotateTransInvert.reset()
        finalTrans.invert(withRotateTransInvert)
        val finalCenterPoint = Vector(
            centerX,
            centerY,
            0f,
            Vector.POINT
        )
        withRotateTransInvert.mapVector(finalCenterPoint)

        // 5. 恢复原本的中点到画面中心
        finalTrans.translate(
            finalCenterPoint.x - oriCenterPoint.x,
            finalCenterPoint.y - oriCenterPoint.y,
            finalCenterPoint.z - oriCenterPoint.z
        )

        // 6.应用梯形变换的效果
        finalTrans.preConcat(tiltTransform.transform)

        updateFinalMatrix(finalTrans)
    }

    private fun updateFinalMatrix(target: Matrix) {
        finalMatrix = Matrix().apply {
            set(target)
        }
    }

    /**
     * 应用最终变换后,要对图像进行绝对世界坐标系的移动
     * 通过逆运算中点得出需要先偏移的变换量
     */
    fun calculateTranslateTransform(moveX: Float, moveY: Float, centerX: Float, centerY: Float): Matrix? {
        val oriCenterPoint = Vector(
            centerX,
            centerY,
            0f,
            Vector.POINT
        )
        val finalMatrixInvert =
            Matrix()
        finalMatrix.invert(finalMatrixInvert)
        finalMatrixInvert.mapVector(oriCenterPoint)
        val newCenter = Vector(
            centerX - moveX,
            centerY - moveY,
            0f,
            Vector.POINT
        )
        finalMatrixInvert.mapVector(newCenter)
        val dx = newCenter.x - oriCenterPoint.x
        val dy = newCenter.y - oriCenterPoint.y
        val dz = newCenter.z - oriCenterPoint.z
        val transform =
            Matrix()
        transform.translate(dx, dy, dz)
        return transform
    }

    /**
     * 计算世界坐标系的y轴基于currentMatrix变换的向量
     */
    fun calculateRelativelyYAxis(): Vector {
        val matrixInvert =
            Matrix()
        val matrix = obtain()
        matrix.set(finalMatrix)
        val tiltInvert = obtain()
        tiltTransform.transform.invert(tiltInvert)
        matrix.preConcat(tiltInvert)
        matrix.invert(matrixInvert)
        val o = matrixInvert.mapVector(
            Vector(
                0f,
                0f,
                0f,
                Vector.POINT
            )
        )
        val y = matrixInvert.mapVector(
            Vector(
                0f,
                1f,
                0f,
                Vector.POINT
            )
        )
        return Vector(
            y.x - o.x,
            y.y - o.y,
            y.z - o.z,
            Vector.POINT
        )
    }

    private fun decodeClipRect(oriTransInvert: Matrix, clipRectF: RectF, imageWidth: Float, imageHeight: Float) {
        if ((imageHeight <= 0f) || (imageWidth <= 0f)) {
            return
        }
        val pointLT = Vector(
            clipRectF.left,
            clipRectF.top,
            0f,
            Vector.POINT
        )
        val pointLB = Vector(
            clipRectF.left,
            clipRectF.bottom,
            0f,
            Vector.POINT
        )
        val pointRT = Vector(
            clipRectF.right,
            clipRectF.top,
            0f,
            Vector.POINT
        )
        val pointRB = Vector(
            clipRectF.right,
            clipRectF.bottom,
            0f,
            Vector.POINT
        )

        oriTransInvert.mapVector(pointLT)
        oriTransInvert.mapVector(pointLB)
        oriTransInvert.mapVector(pointRT)
        oriTransInvert.mapVector(pointRB)

        if (!drawPartialArea) {
            points[Number.NUMBER_0] = pointLT.x / imageWidth
            points[Number.NUMBER_1] = pointLT.y / imageHeight
            points[Number.NUMBER_4] = pointLB.x / imageWidth
            points[Number.NUMBER_5] = pointLB.y / imageHeight
            points[Number.NUMBER_8] = pointRB.x / imageWidth
            points[Number.NUMBER_9] = pointRB.y / imageHeight
            points[Number.NUMBER_12] = pointRT.x / imageWidth
            points[Number.NUMBER_13] = pointRT.y / imageHeight
        }
    }

    override fun toString(): String {
        return "${super.toString()}\n rotateConfig = $axisAngleTransform\n tilt=$tiltTransform "
    }
}