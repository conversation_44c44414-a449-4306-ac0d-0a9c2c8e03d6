/*
 * Copyright (C) 2010 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.oplus.gallery.foundation.opengl.renderer;

import android.graphics.Bitmap;
import android.graphics.Rect;
import android.graphics.RectF;

//
// GLCanvas gives a convenient interface to draw using OpenGL.
//
// When a rectangle is specified in this interface, it means the region
// [x, x+width) * [y, y+height)
//
public interface GLCanvas {

     boolean SWITCHER_DISABLE_CLIP_WHEN_ZONE = false;
     int FLOAT_SIZE = Float.SIZE / Byte.SIZE;
     int INTEGER_SIZE = Integer.SIZE / Byte.SIZE;
     int BYTE_SIZE = Byte.SIZE / Byte.SIZE;
     int MATRIX_SIZE = 16;
     int VECTOR_SIZE = 4;
     int INVALID_BUFFER = 0;
     int SAVE_FLAG_ALL = 0xFFFFFFFF;
     int SAVE_FLAG_ALPHA = 0x01;
     int SAVE_FLAG_MATRIX = 0x02;
     int SAVE_FLAG_CLIP = 0x04;

     int VECT_ELEM_COUNT = 2;
     int RECT_ELEM_COUNT = 4;
     int RECT_VERT_ELEM_COUNT = 4;
     int LINE_VERT_ELEM_COUNT = 2;
     int VERT_ELEM_COUNT = 4;
     int MAT_ELEM_COUNT = 16;
     int RGBA_ELEM_COUNT = 4;
     int ARGB_ELEM_COUNT = 4;
     int MAT4_ROW_ELEM_COUNT = 4;
     int MAT4_COL_ELEM_COUNT = 4;
     int MAT3_ROW_ELEM_COUNT = 3;
     int MAT3_COL_ELEM_COUNT = 3;
     int INVALID_TEXTURE_ID = -1;
    int INVALID_PROGRAM_ID = 0;
     float MIN_RATIO = 0.01f;
     float SQUARE_RATIO = 1.0f;
     float COLOR_COMPONENT_DEPTH = 255f;
     float COLOR_RGBA_DEPTH = 65535f;

    int getError();

    GLId getGLId();

    void resume();

    void pause();

    boolean registerStateChangedListener(OnStateChangedListener listener);

    boolean unregisterStateChangedListener(OnStateChangedListener listener);

    /**
     * Tells GLCanvas the size of the underlying GL surface. This should be
     * called before first drawing and when the size of GL surface is changed.
     * This is called by GLRoot and should not be called by the clients
     * who only want to draw on the GLCanvas. Both width and height must be
     * nonnegative
     *
     * @param width
     * @param height
     */
    public void setSize(int width, int height);

    public int getViewportWidth();

    public int getViewportHeight();

    /**
     * Clear the drawing buffers.
     * This should only be used by GLRoot.
     */
    public void clearBuffer();

    /**
     * Set clear color
     *
     * @param color
     */
    public void setClearColor(int color);

    public void clearBuffer(float[] argb);

    public float getAlpha();

    /**
     * Sets and gets the current alpha, alpha must be in [0, 1].
     *
     * @param alpha
     */
    public void setAlpha(float alpha);

    /**
     * (current alpha) = (current alpha) * alpha
     *
     * @param alpha
     */
    public void multiplyAlpha(float alpha);

    /**
     * Change the current transform matrix.
     *
     * @param x
     * @param y
     * @param z
     */
    public void translate(float x, float y, float z);

    public void translate(float x, float y);

    public void scale(float sx, float sy, float sz);

    public void rotate(float angle, float x, float y, float z);

    public void multiplyMatrix(float[] mMatrix, int offset);

    /**
     * 是否使用纹理复用池[TexturePool]
     * 用于在使用过程中若涉及纹理替换时，则可利用TexturePool回收纹理复用
     * 如在执行draw时，会调用[FilterWrapper]进行转换，可能会涉及纹理替换
     * @param useTexturePool
     */
    void setUseTexturePool(boolean useTexturePool);

    /**
     * 获取当前是否使用纹理复用池
     * @return
     */
    boolean getUseTexturePool();

    /**
     * Modifies the current clip with the specified rectangle.
     * (current clip) = (current clip) intersect (specified rectangle).
     *
     * @param left
     * @param top
     * @param right
     * @param bottom
     * @return true if the result clip is non-empty.
     */
    public boolean clipRect(int left, int top, int right, int bottom);

    /**
     * Pushes the configuration state (matrix, and alpha) onto
     * a private stack.
     */
    public void save();

    /**
     * Same as save(), but only save those specified in saveFlags.
     *
     * @param saveFlags
     */
    public void save(int saveFlags);

    /**
     * Pops from the top of the stack as current configuration state (matrix,
     * alpha, and clip). This call balances a previous call to save(), and is
     * used to remove all modifications to the configuration state since the
     * last save call.
     */
    public void restore();

    /**
     * Draws a line using the specified paint from (x1, y1) to (x2, y2).
     * (Both end points are included).
     *
     * @param x1
     * @param y1
     * @param x2
     * @param y2
     * @param paint
     */
    public void drawLine(float x1, float y1, float x2, float y2, GLPaint paint);

    /**
     * Stroke the specified rectangle with the specified color.
     *
     * @param x1
     * @param y1
     * @param width
     * @param height
     * @param paint
     */
    public void drawRect(float x1, float y1, float width, float height, GLPaint paint);

    /**
     * Fills the specified rectangle with the specified color.
     *
     * @param x
     * @param y
     * @param width
     * @param height
     * @param color
     */
    public void fillRect(float x, float y, float width, float height, int color);

    void updateProjectionMatrixZ(float maxZ);

    void resetProjectionMatrix();

    /**
     * Draws a texture to the specified rectangle.
     * @param texture
     * @param x
     * @param y
     * @param width
     * @param height
     */
    public void drawTexture(IBaseTexture texture, int x, int y, int width, int height);

    public void drawTexture(IBaseTexture texture, int x, int y, int width, int height,
                            int coverColor);

    public void drawTexture(IBaseTexture texture, int x, int y, int width, int height,
                            int coverColor, int changeColor);

    public void drawMesh(IBaseTexture tex, int x, int y, int xyBuffer,
                         int uvBuffer, int indexBuffer, int indexCount);

    public void drawMesh(IBaseTexture tex, int x, int y, int xyBuffer,
                         int uvBuffer, int indexBuffer, int indexCount, float alpha);

    /**
     * Draws the source rectangle part of the texture to the target rectangle.
     * @param texture
     * @param source
     * @param target
     */
    public void drawTexture(IBaseTexture texture, RectF source, RectF target);

    /**
     * Draw a texture with a specified texture transform.
     * @param texture
     * @param mTextureTransform
     * @param x
     * @param y
     * @param w
     * @param h
     */
    public void drawTexture(IBaseTexture texture, float[] mTextureTransform,
                            int x, int y, int w, int h);

    /**
     * 绘制texture的局部内容, 由4个点确定绘制的局部四边形范围, 范围内显示texture内容, 范围外由fillColor填充
     * region为4*4矩阵,其中(0,0)(0,1)存储第一个点,(1,0)(1,1)存储第二个点,(2,0)(2,1)存储第三个点,(3,0)(3,1)存储第四个点
     * region记录的四个点从texture画面左上角开始依次逆时针记录
     *
     * @param texture
     * @param fillColor
     * @param region
     * @param x
     * @param y
     * @param width
     * @param height
     */
    void drawRegionTexture(IBaseTexture texture, int fillColor, float[] region, int x, int y, int width, int height);

    void drawRegionTexture(IBaseTexture texture, int fillColor, float[] region, RectF target);

    /**
     * Draw two textures to the specified rectangle. The actual texture used is
     * from * (1 - ratio) + to * ratio
     * The two textures must have the same size.
     *
     * @param from
     * @param toColor
     * @param ratio
     * @param x
     * @param y
     * @param w
     * @param h
     */
    public void drawMixed(IBaseTexture from, int toColor,
                          float ratio, int x, int y, int w, int h);

    /**
     * Draw a region of a texture and a specified color to the specified
     * rectangle. The actual color used is from * (1 - ratio) + to * ratio.
     * The region of the texture is defined by parameter "src". The target
     * rectangle is specified by parameter "target".
     * @param from
     * @param toColor
     * @param ratio
     * @param src
     * @param target
     */
    public void drawMixed(IBaseTexture from, int toColor,
                          float ratio, RectF src, RectF target);

    /**
     * Draw texture with lut texture mixed
     * @param texture
     * @param x
     * @param y
     * @param width
     * @param height
     * @param lutTexture
     */
    public void drawWithLutMixed(IBaseTexture texture, int x, int y, int width, int height, IBaseTexture lutTexture);

    /**
     * Draw texture with lut texture mixed
     * @param texture
     * @param mTextureTransform
     * @param x
     * @param y
     * @param w
     * @param h
     * @param lutTexture
     */
    public void drawWithLutMixed(IBaseTexture texture, float[] mTextureTransform,
                                 int x, int y, int w, int h, IBaseTexture lutTexture);

    /**
     * 仅适用于GradientTexture绘制渐变效果
     *
     * @param texture
     * @param x
     * @param y
     * @param width
     * @param height
     */
    public void drawGradient(IBaseTexture texture, int x, int y, int width, int height);

    public void drawGradient(IBaseTexture texture, RectF source, RectF target);

    /**
     * Unloads the specified texture from the canvas. The resource allocated
     * to draw the texture will be released. The specified texture will return
     * to the unloaded state. This function should be called only from
     * IBaseTexture or its descendant
     * @param texture
     * @return
     */
    public boolean unloadTexture(IBaseTexture texture);

    public void unloadTexture(int texId);

    /**
     * Delete the specified buffer object, similar to unloadTexture.
     *
     * @param bufferId
     */
    public void deleteBuffer(int bufferId);

    /**
     * Delete the textures and buffers in GL side. This function should only be
     * called in the GL thread.
     */
    public void deleteRecycledResources();

    /**
     * Dump statistics information and clear the counters.
     * For debug only.
     */
    public void dumpStatisticsAndClear();

    public void beginRenderTarget(IRawTexture texture);

    public void endRenderTarget();

    /**
     * Sets texture parameters to use GL_CLAMP_TO_EDGE for both
     * GL_TEXTURE_WRAP_S and GL_TEXTURE_WRAP_T. Sets texture parameters to be
     * GL_LINEAR for GL_TEXTURE_MIN_FILTER and GL_TEXTURE_MAG_FILTER.
     * bindTexture() must be called prior to this.
     * @param texture The texture to set parameters on.
     */
    public void setTextureParameters(IBaseTexture texture);

    /**
     * Initializes the texture to a size by calling texImage2D on it.
     * @param texture The texture to initialize the size.
     * @param format The texture format (e.g. GL_RGBA)
     * @param type The texture type (e.g. GL_UNSIGNED_BYTE)
     */
    public void initializeTextureSize(IBaseTexture texture, int format, int type);

    /**
     * Initializes the texture to a size by calling texImage2D on it.
     * @param texture The texture to initialize the size.
     * @param bitmap The bitmap to initialize the bitmap with.
     */
    public void initializeTexture(IBaseTexture texture, Bitmap bitmap);

    /**
     * Calls glTexSubImage2D to upload a bitmap to the texture.
     * @param texture The target texture to write to.
     * @param xOffset Specifies a texel offset in the x direction within the texture array.
     * @param yOffset Specifies a texel offset in the y direction within the texture array.
     * @param format The texture format (e.g. GL_RGBA)
     * @param type The texture type (e.g. GL_UNSIGNED_BYTE)
     */
    public void texSubImage2D(IBaseTexture texture, int xOffset, int yOffset,
                              Bitmap bitmap,
                              int format, int type);

    /**
     * Generates buffers and uploads the buffer data.
     * @param buffer The buffer to upload
     * @param bufferId the bufferid that buffer will be uploaded to
     * @return The buffer ID that was uploaded to.
     */
    public int uploadBuffer(java.nio.FloatBuffer buffer, int bufferId);

    /**
     * Generates buffers and uploads the element array buffer data.
     * @param buffer The buffer to upload
     * @param bufferId the bufferid that buffer will be uploaded to
     * @return The buffer ID that was uploaded to.
     */
    public int uploadBuffer(java.nio.IntBuffer buffer, int bufferId);

    /**
     * Generates buffers and uploads the element array buffer data.
     * @param buffer The buffer to upload
     * @param bufferId the bufferid that buffer will be uploaded to
     * @return The buffer ID that was uploaded to.
     */
    public int uploadBuffer(java.nio.ByteBuffer buffer, int bufferId);

    /**
     * Generates buffers and uploads the element array buffer data.
     * @param buffer The buffer to upload
     * @param elementSize the buffer byte size
     * @param bufferId the bufferid that buffer will be uploaded to
     * @return The buffer ID that was uploaded to.
     */
    public int uploadElementBuffer(java.nio.Buffer buffer, int elementSize, int bufferId);

    /**
     * After LightCycle makes GL calls, this method is called to restore the GL
     * configuration to the one expected by GLCanvas.
     */
    public void recoverFromLightCycle();

    /**
     * Gets the bounds given by x, y, width, and height as well as the internal
     * matrix state. There is no special handling for non-90-degree rotations.
     * It only considers the lower-left and upper-right corners as the bounds.
     *
     * @param bounds The output bounds to write to.
     * @param x      The left side of the input rectangle.
     * @param y      The bottom of the input rectangle.
     * @param width  The width of the input rectangle.
     * @param height The height of the input rectangle.
     */
    public void getBounds(Rect bounds, int x, int y, int width, int height);

    /**
     * Setup blend function mix factor
     *
     * @param sfactor factor of src blending color
     * @param dfactor factor of dest blending color
     */
    public void setBlendFactor(int sfactor, int dfactor);

    /**
     * Set the line width
     *
     * @param lineWidth
     */
    public void setLineWidth(float lineWidth);

    /**
     * Read the max texture dimension what is the GL supported
     * @return
     */
    public int getMaxTextureSize();

    /**
     * Set dither enabled
     *
     * @param enabled
     */
    public void setDitherEnabled(boolean enabled);

    public void deleteBuffers(int[] buffers, int bufferSize);

    public static enum StencilOperation {
        GL_KEEP,
        GL_ZERO,
        GL_REPLACE,
        GL_INCR,
        GL_INCR_WRAP,
        GL_DECR,
        GL_DECR_WRAP,
        GL_INVERT
    }

    public static enum StencilCondition {
        GL_NEVER,
        GL_ALWAYS,
        GL_LESS,
        GL_LEQUAL,
        GL_EQUAL,
        GL_NOTEQUAL,
        GL_GEQUAL,
        GL_GREATER
    }

    public static interface OnStateChangedListener {
        void onResume();

        void onPause();
    }
}
