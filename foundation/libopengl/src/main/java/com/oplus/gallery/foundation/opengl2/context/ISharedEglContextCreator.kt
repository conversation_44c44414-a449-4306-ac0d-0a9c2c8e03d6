/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IGLContextCreator
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2024/5/28 11:20
 ** Author: Wanglian
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** Wanglian                        2024/5/28		  1.0		 IGLContextCreator
 *********************************************************************************/
package com.oplus.gallery.foundation.opengl2.context

import android.opengl.EGLContext

interface ISharedEglContextCreator {
    /**
     * 获取共享的[EGLContext]
     */
    fun getSharedContext(): EGLContext

    /**
     * 销毁EGLContext
     */
    fun destroySharedContext()
}