/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - GainTexture.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/11/01
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * huca<PERSON><PERSON>@Apps.Gallery		2024/11/01		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.oplus.gallery.foundation.opengl2.texture

import android.graphics.ColorSpace
import android.opengl.GLES30
import com.oplus.gallery.addon.graphics.EMPTY_ULTRA_HDR_INFO
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt.SRGB
import com.oplus.gallery.standard_lib.codec.drawable.IHdrMetadataPack
import com.oplus.gallery.standard_lib.codec.drawable.UHdrMetadataPack

/**
 * 增益图的Texture，其中要包含增益纹理以及增益参数
 *
 * @param width 纹理宽
 * @param height 纹理高
 * @param colorSpace 纹理色域, 默认[SRGB]
 * @param texConfig 纹理类型，默认[TexConfig.ARGB_8888]
 * @param clamp 纹理范围放大倍率
 * @param texTarget 纹理绘制目标，默认[GLES30.GL_TEXTURE_2D]
 * @param metadataPack 增益参数
 */
class GainRawTexture(
    width: Int,
    height: Int,
    colorSpace: ColorSpace = SRGB,
    texConfig: TexConfig = TexConfig.ARGB_8888,
    clamp: Float = 1.0f,
    texTarget: Int = GLES30.GL_TEXTURE_2D,
    override var metadataPack: IHdrMetadataPack = UHdrMetadataPack(EMPTY_ULTRA_HDR_INFO),
) : RawTexture(width, height, colorSpace, texConfig, clamp, texTarget), IMutableGainTexture {
    override val tag: String = TAG

    override fun buildString(map: MutableMap<String, Any?>) {
        map["metadataPack"] = metadataPack
    }

    companion object {
        private const val TAG = "GainRawTexture"
    }
}