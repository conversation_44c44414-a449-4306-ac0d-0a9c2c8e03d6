/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - VirtualTexture.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/10/24
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * huca<PERSON><PERSON>@Apps.Gallery		2024/10/24		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.oplus.gallery.foundation.opengl2.texture

import com.oplus.gallery.foundation.util.io.IRecyclable
import com.oplus.gallery.foundation.util.io.IReusable

/**
 * 虚拟的Texture类，真正的texture需作为参数传入，此类的[IReusable]以及[IRecyclable]接口为空实现
 *
 * @param texture 输入真正的纹理
 */
class VirtualTexture(private val texture: Texture) : ITexture by texture {
    override fun reuse() = Unit
    override fun recycle() = Unit
    override fun getTexture(): Texture {
        return texture
    }

    override fun toVirtual(): ITexture {
        return this
    }
}