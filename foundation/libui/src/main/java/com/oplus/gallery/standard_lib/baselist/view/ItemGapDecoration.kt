/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ItemGapDecoration.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/07/30
 ** Author: <EMAIL>
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** dengchuk<PERSON>@Apps.Gallery	2020/07/30		1.0		create
 *********************************************************************************/
package com.oplus.gallery.standard_lib.baselist.view

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.oplus.gallery.standard_lib.baselist.bean.LayoutDetail
import kotlin.math.max

open class ItemGapDecoration(protected val layoutDetail: LayoutDetail) : RecyclerView.ItemDecoration() {

    override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
        /*
         * 父类源码中使用的是(view.layoutParams as RecyclerView.LayoutParams).viewLayoutPosition 这个position得到的是没有移动动画之前的position
         * viewAdapterPosition则是得到move动画之后的目标position,这样动画结束时对应item移动得位置的间隙计算才是正确的
         */
        var itemPosition = (view.layoutParams as RecyclerView.LayoutParams).viewAdapterPosition
        // 如果此item已经被remove，删除动画前得到的adapterPosition为-1，设置offsets时被判定为header。layoutPosition为当前布局中位置
        if (itemPosition == RecyclerView.NO_POSITION) {
            itemPosition = (view.layoutParams as RecyclerView.LayoutParams).viewLayoutPosition
        }
        outRect.top = layoutDetail.itemDecorationGapPx.top.toInt()
        outRect.bottom = layoutDetail.itemDecorationGapPx.bottom.toInt()
        outRect.left = layoutDetail.itemDecorationGapPx.left.toInt()
        outRect.right = layoutDetail.itemDecorationGapPx.right.toInt()
        /*
         * state.itemCount为当前状态布局中的数量。不使用adapter.itemCount，避免adapter数量减少时将item误判为footer，offset计算出错。
         * 增加item时使用adapter.itemCount避免最后一个item被误判为footer;删除时使用state.itemCount
         */
        val itemCount = parent.adapter?.itemCount?.let {
            max(it, state.itemCount)
        } ?: let {
            state.itemCount
        }
        when {
            (itemPosition < layoutDetail.headerCount) -> getHeaderItemOffsets(outRect, view, parent, itemPosition)
            (itemPosition > (itemCount - layoutDetail.footerCount - 1)) -> getFooterItemOffsets(outRect, view, parent, itemPosition)
            else -> getNormalItemOffsets(outRect, view, parent, itemPosition)
        }
    }

    open fun getHeaderItemOffsets(outRect: Rect, view: View, parent: RecyclerView, itemPosition: Int): Unit = Unit

    open fun getNormalItemOffsets(outRect: Rect, view: View, parent: RecyclerView, itemPosition: Int): Unit = Unit

    open fun getFooterItemOffsets(outRect: Rect, view: View, parent: RecyclerView, itemPosition: Int): Unit = Unit
}