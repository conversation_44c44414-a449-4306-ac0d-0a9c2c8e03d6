/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SingleActionTopTipView.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2021/03/25
 ** Author: zhengyirui@Apps.Gallery3D
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** z<PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D		2021/03/25		1.0		  create
 *********************************************************************************/
package com.oplus.gallery.standard_lib.ui.prompt

import android.content.Context
import android.util.AttributeSet
import android.widget.TextView
import androidx.annotation.AttrRes
import androidx.annotation.StringRes
import androidx.annotation.StyleRes
import com.oplus.gallery.foundation.ui.R

/**
 * 顶部提示控件，带有一个按钮
 */
class SingleActionTopTipView@JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    @AttrRes defStyleAttr: Int = 0,
    @StyleRes defStyleRes: Int = 0
) : ActionTopTipView(context, attrs, defStyleAttr, defStyleRes, R.layout.common_top_tips_single_action) {

    /**
     * Set the action text and its onClick listener
     * @param actionTextRes The action text resource id
     * @param listener The onClick listen of action
     */
    fun setOnAction(@StringRes actionTextRes: Int, listener: OnClickListener?) {
        setOnAction(resources.getString(actionTextRes), listener)
    }

    /**
     * Set the action text and its onClick listener
     * @param actionText The text of action button
     * @param listener The onClick listener of action button
     */
    fun setOnAction(actionText: String?, listener: OnClickListener?) {
        setOnAction(actionText, listener, findViewById(R.id.tv_top_tip_action_btn))
    }

    fun setActionTextColor(color: Int) {
        findViewById<TextView>(R.id.tv_top_tip_action_btn)?.apply {
            setTextColor(color)
        }
    }
}