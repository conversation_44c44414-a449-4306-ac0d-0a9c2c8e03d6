/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ActionTopTipView.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2021/03/25
 ** Author: zhengyirui@Apps.Gallery3D
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** z<PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D		2021/03/25		1.0		  create
 *********************************************************************************/

package com.oplus.gallery.standard_lib.ui.prompt

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.view.MotionEvent
import android.widget.TextView
import androidx.annotation.AttrRes
import androidx.annotation.StyleRes

/**
 * 带有一个或多个按钮的顶部提示的基类
 */
abstract class ActionTopTipView@JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    @AttrRes defStyleAttr: Int = 0,
    @StyleRes defStyleRes: Int = 0,
    layout: Int
) : AdditionTopTipView(context, attrs, defStyleAttr, defStyleRes, layout) {

    /**
     * Set the action text and its onClick listener
     * @param actionText The text of action button
     * @param listener The onClick listener of action button
     */
    protected fun setOnAction(actionText: String?, listener: OnClickListener?, actionButton: TextView) {
        if (TextUtils.isEmpty(actionText)) {
            actionButton.apply {
                visibility = GONE
                setOnClickListener(null)
            }
        } else {
            actionButton.apply {
                visibility = VISIBLE
                text = actionText
                if (listener != null) {
                    setOnClickListener { v ->
                        listener.onClick(v)
                    }
                }
            }
        }
        layoutSetEnable = true
        invalidate()
    }

    override fun onInterceptTouchEvent(ev: MotionEvent?): Boolean {
        if (alpha == 0.0f) {
            // BUG 5159430如果透明，子布局就不要再处理点击事件了，避免透明层非预期的拦截了touchEvent。
            return true
        }
        return super.onInterceptTouchEvent(ev)
    }
}