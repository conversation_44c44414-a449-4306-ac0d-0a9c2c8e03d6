/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - BaseListItemAnimator.kt
 * Description:
 * Version: 1.0
 * Date: 2021/01/25
 ** Author: <EMAIL>
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                   <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * Xueying.Wang@Apps.Gallery3D      2021/01/25      1.0          build this module
 *************************************************************************************************/
package com.oplus.gallery.standard_lib.baselist.view

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.view.animation.PathInterpolator
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.SimpleItemAnimator
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag.Companion.DL

/**
 * 基础列表Item动画器，
 * 相关参数由设计定义在COD，
 */
class BaseListItemAnimator : SimpleItemAnimator() {
    companion object {
        private const val TAG = "BaseListItemAnimator"
        // 新增动画起始Scale和删除动画结束Scale
        private const val MIN_SCALE = 0.7f
        private const val MAX_SCALE = 1f
        private const val ALPHA_TRANSPARENT = 0f
        private const val ALPHA_OPAQUE = 1f
        private val DEFAULT_INTERPOLATOR = ValueAnimator().interpolator

        private val REMOVE_INTERPOLATOR = PathInterpolator(0.33f, 0f, 0.67f, 1f)
        private val INSERT_INTERPOLATOR = PathInterpolator(0.3f, 0f, 0.1f, 1f)
        private val MOVE_INTERPOLATOR = PathInterpolator(0.31f, 0f, 0.11f, 1f)

        const val ADD_DURATION = 400L
        const val REMOVE_DURATION = 167L
        const val MOVE_DURATION = 500L
    }

    private val onAnimationListenersList = mutableListOf<OnAnimationListener>()
    private val pendingAdditions = mutableListOf<RecyclerView.ViewHolder>()
    private val pendingRemovals = mutableListOf<RecyclerView.ViewHolder>()
    private val pendingMoves = mutableListOf<MoveInfo>()

    private val addAnimations = mutableListOf<RecyclerView.ViewHolder>()
    private val moveAnimations = mutableListOf<RecyclerView.ViewHolder>()
    private val removeAnimations = mutableListOf<RecyclerView.ViewHolder>()

    private class MoveInfo(
        var holder: RecyclerView.ViewHolder,
        var fromX: Int,
        var fromY: Int,
        var toX: Int,
        var toY: Int
    )

    init {
        addDuration = ADD_DURATION
        removeDuration = REMOVE_DURATION
        moveDuration = MOVE_DURATION
        supportsChangeAnimations = false
    }

    /**
     * 设置动画开始与结束时的回调
     */
    fun addAnimationListener(listener: OnAnimationListener) {
        onAnimationListenersList.add(listener)
    }

    override fun runPendingAnimations() {
        onAnimationListenersList.forEach { listener ->
            listener.onAnimationStart()
        }
        for (holder: RecyclerView.ViewHolder in pendingRemovals) {
            animateRemoveImpl(holder)
        }
        pendingRemovals.clear()
        for (moveInfo: MoveInfo in pendingMoves) {
            animateMoveImpl(
                moveInfo.holder,
                moveInfo.fromX, moveInfo.fromY,
                moveInfo.toX, moveInfo.toY
            )
        }
        pendingMoves.clear()
        for (holder: RecyclerView.ViewHolder in pendingAdditions) {
            animateAddImpl(holder)
        }
        pendingAdditions.clear()
    }

    override fun animateRemove(holder: RecyclerView.ViewHolder): Boolean {
        resetAnimation(holder)
        pendingRemovals.add(holder)
        return true
    }

    private fun animateRemoveImpl(holder: RecyclerView.ViewHolder) {
        holder.setIsRecyclable(true)
        val view = holder.itemView
        val animation = view.animate()
        removeAnimations.add(holder)
        animation.setDuration(removeDuration)
            .alpha(ALPHA_TRANSPARENT)
            .scaleX(MIN_SCALE)
            .scaleY(MIN_SCALE)
            .setInterpolator(REMOVE_INTERPOLATOR)
            .setListener(
                object : AnimatorListenerAdapter() {
                    override fun onAnimationStart(animator: Animator) {
                        dispatchRemoveStarting(holder)
                    }

                    override fun onAnimationEnd(animator: Animator) {
                        animation.setListener(null)
                        view.alpha = ALPHA_OPAQUE
                        view.scaleX = MAX_SCALE
                        view.scaleY = MAX_SCALE
                        dispatchRemoveFinished(holder)
                        removeAnimations.remove(holder)
                        dispatchFinishedWhenDone()
                    }
                }).start()
    }

    override fun animateAdd(holder: RecyclerView.ViewHolder): Boolean {
        resetAnimation(holder)
        holder.itemView.alpha = ALPHA_TRANSPARENT
        holder.itemView.scaleX = MIN_SCALE
        holder.itemView.scaleY = MIN_SCALE
        pendingAdditions.add(holder)
        return true
    }

    private fun animateAddImpl(holder: RecyclerView.ViewHolder) {
        val view = holder.itemView
        val animation = view.animate()
        addAnimations.add(holder)
        animation.setDuration(addDuration)
            .alpha(ALPHA_OPAQUE)
            .scaleX(MAX_SCALE)
            .scaleY(MAX_SCALE)
            .setInterpolator(INSERT_INTERPOLATOR)
            .setListener(object : AnimatorListenerAdapter() {
                override fun onAnimationStart(animator: Animator) {
                    dispatchAddStarting(holder)
                }

                override fun onAnimationCancel(animator: Animator) {
                    view.alpha = ALPHA_OPAQUE
                    view.scaleX = MAX_SCALE
                    view.scaleY = MAX_SCALE
                }

                override fun onAnimationEnd(animator: Animator) {
                    animation.setListener(null)
                    dispatchAddFinished(holder)
                    addAnimations.remove(holder)
                    dispatchFinishedWhenDone()
                }
            }).start()
    }

    override fun animateMove(holder: RecyclerView.ViewHolder, fromX: Int, fromY: Int, toX: Int, toY: Int): Boolean {
        var fromX = fromX
        var fromY = fromY
        val view = holder.itemView
        fromX += holder.itemView.translationX.toInt()
        fromY += holder.itemView.translationY.toInt()
        resetAnimation(holder)
        val deltaX = toX - fromX
        val deltaY = toY - fromY
        if ((deltaX == 0) && (deltaY == 0)) {
            dispatchMoveFinished(holder)
            return false
        }
        if (deltaX != 0) {
            view.translationX = -deltaX.toFloat()
        }
        if (deltaY != 0) {
            view.translationY = -deltaY.toFloat()
        }
        pendingMoves.add(MoveInfo(holder, fromX, fromY, toX, toY))
        return true
    }

    private fun animateMoveImpl(
        holder: RecyclerView.ViewHolder,
        fromX: Int,
        fromY: Int,
        toX: Int,
        toY: Int
    ) {
        val view = holder.itemView
        val deltaX = toX - fromX
        val deltaY = toY - fromY
        if (deltaX != 0) {
            view.animate().translationX(0f)
        }
        if (deltaY != 0) {
            view.animate().translationY(0f)
        }
        val animation = view.animate()
        moveAnimations.add(holder)
        animation.setDuration(moveDuration)
            .setInterpolator(MOVE_INTERPOLATOR)
            .setListener(object : AnimatorListenerAdapter() {
                override fun onAnimationStart(animator: Animator) {
                    dispatchMoveStarting(holder)
                }

                override fun onAnimationCancel(animator: Animator) {
                    if (deltaX != 0) {
                        view.translationX = 0f
                    }
                    if (deltaY != 0) {
                        view.translationY = 0f
                    }
                }

                override fun onAnimationEnd(animator: Animator) {
                    animation.setListener(null)
                    dispatchMoveFinished(holder)
                    moveAnimations.remove(holder)
                    dispatchFinishedWhenDone()
                }
            }).start()
    }

    override fun animateChange(
        oldHolder: RecyclerView.ViewHolder,
        newHolder: RecyclerView.ViewHolder,
        fromX: Int,
        fromY: Int,
        toX: Int,
        toY: Int
    ): Boolean {
        if (oldHolder === newHolder) {
            return animateMove(oldHolder, fromX, fromY, toX, toY)
        }
        return false
    }

    override fun endAnimation(item: RecyclerView.ViewHolder) {
        val view = item.itemView
        // this will trigger end callback which should set properties to their target values.
        view.animate().cancel()
        for (i in pendingMoves.indices.reversed()) {
            val moveInfo: MoveInfo = pendingMoves[i]
            if (moveInfo.holder === item) {
                view.translationY = 0f
                view.translationX = 0f
                dispatchMoveFinished(item)
                pendingMoves.removeAt(i)
            }
        }
        if (pendingRemovals.remove(item)) {
            view.alpha = ALPHA_OPAQUE
            dispatchRemoveFinished(item)
        }
        if (pendingAdditions.remove(item)) {
            view.alpha = ALPHA_OPAQUE
            view.scaleX = MAX_SCALE
            view.scaleY = MAX_SCALE

            dispatchAddFinished(item)
        }
        if (!isRunning) {
            dispatchAnimationsFinished()
        }
    }

    override fun endAnimations() {
        var count = pendingMoves.size
        for (i in count - 1 downTo 0) {
            val item: MoveInfo = pendingMoves[i]
            val view = item.holder.itemView
            view.translationY = 0f
            view.translationX = 0f
            dispatchMoveFinished(item.holder)
            pendingMoves.removeAt(i)
        }
        count = pendingRemovals.size
        for (i in count - 1 downTo 0) {
            val item = pendingRemovals[i]
            dispatchRemoveFinished(item)
            pendingRemovals.removeAt(i)
        }
        count = pendingAdditions.size
        for (i in count - 1 downTo 0) {
            val item = pendingAdditions[i]
            item.itemView.alpha = ALPHA_OPAQUE
            item.itemView.scaleX = MAX_SCALE
            item.itemView.scaleY = MAX_SCALE
            dispatchAddFinished(item)
            pendingAdditions.removeAt(i)
        }
        cancelAllAnimations()
    }

    private fun cancelAllAnimations() {
        if (!isRunning) {
            return
        }
        for (i in removeAnimations.indices.reversed()) {
            removeAnimations[i].itemView.animate().cancel()
        }
        for (i in moveAnimations.indices.reversed()) {
            moveAnimations[i].itemView.animate().cancel()
        }
        for (i in addAnimations.indices.reversed()) {
            addAnimations[i].itemView.animate().cancel()
        }
        dispatchAnimationsFinished()
    }

    override fun isRunning(): Boolean {
        return ((pendingAdditions.isNotEmpty()
            || pendingMoves.isNotEmpty()
            || pendingRemovals.isNotEmpty()
            || moveAnimations.isNotEmpty()
            || removeAnimations.isNotEmpty()
            || addAnimations.isNotEmpty()))
    }

    private fun resetAnimation(holder: RecyclerView.ViewHolder) {
        holder.itemView.animate().interpolator = DEFAULT_INTERPOLATOR
        endAnimation(holder)
    }

    private fun dispatchFinishedWhenDone() {
        if (!isRunning) {
            GLog.d(TAG, DL) { "dispatchFinishedWhenDone: animation is finished" }
            dispatchAnimationsFinished()
            onAnimationListenersList.forEach { listener ->
                listener.onAnimationFinished()
            }
        }
    }
}

/**
 * 列表differ动画的监听器
 */
interface OnAnimationListener {
    /**
     * 动画开始的回调
     */
    fun onAnimationStart(): Unit = Unit

    /**
     * 动画结束的回调
     */
    fun onAnimationFinished(): Unit = Unit
}