/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - FullScreenFastScroller.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/04/30
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: --------------------------------------------------------
 **  <author>                   <data>         <version >     <desc>
 **  <EMAIL>    2021/04/30     1.0            Add file head
 **  <PERSON><PERSON>@Apps.Gallery           2024/08/10     1.1           Modify UI
 **
 *************************************************************************************************/
package com.oplus.gallery.standard_lib.ui.scroller

import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.animation.Interpolator
import android.view.animation.PathInterpolator
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.core.view.marginEnd
import androidx.core.view.marginStart
import com.oplus.gallery.foundation.ui.R
import com.oplus.gallery.standard_lib.ui.ViewVisibilityAlphaAnimatorHelper

class FullScreenFastScroller @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : FrameLayout(context, attrs, defStyleAttr) {

    private var maxScrollHeight: Int = 0
    private var minScrollHeight: Int = 0
    private var scrollerMsgViewAnimatorHelper: ViewVisibilityAlphaAnimatorHelper? = null

    private val scrollerImgView: ImageView by lazy {
        findViewById(R.id.full_screen_fast_scroller_img)
    }

    private val scrollerMsgView: TextView by lazy {
        findViewById(R.id.full_screen_fast_scroller_msg)
    }

    private val scrollerAnimatorSet: AnimatorSet by lazy {
        setScrollerPivot()
        val animators: MutableList<Animator> = ArrayList<Animator>().apply {
            add(ObjectAnimator.ofFloat(scrollerImgView, SCALE_X, 1F, SCALE_X_VALUE).setScaleTranslateConfig())
            add(ObjectAnimator.ofFloat(scrollerImgView, SCALE_Y, 1F, SCALE_Y_VALUE).setScaleTranslateConfig())
            add(ObjectAnimator.ofFloat(scrollerImgView, TRANSLATION_X, 0F, animationTranX).setScaleTranslateConfig())
        }
        AnimatorSet().apply {
            playTogether(animators)
        }
    }

    private val reverseScrollerAnimatorSet: AnimatorSet by lazy {
        setScrollerPivot()
        val animators: MutableList<Animator> = ArrayList<Animator>().apply {
            add(ObjectAnimator.ofFloat(scrollerImgView, SCALE_X, SCALE_X_VALUE, 1F).setScaleTranslateConfig())
            add(ObjectAnimator.ofFloat(scrollerImgView, SCALE_Y, SCALE_Y_VALUE, 1F).setScaleTranslateConfig())
            add(ObjectAnimator.ofFloat(scrollerImgView, TRANSLATION_X, animationTranX, 0F).setScaleTranslateConfig())
        }
        AnimatorSet().apply {
            playTogether(animators)
        }
    }

    private fun setScrollerPivot() {
        scrollerImgView.pivotX = (scrollerImgView.width / PIVOT_VALUE).toFloat()
        scrollerImgView.pivotY = (scrollerImgView.height / PIVOT_VALUE).toFloat()
    }

    private val animationTranX = -context.resources.getDimensionPixelSize(R.dimen.common_full_screen_scroller_translation_x).toFloat()

    /**
     * 整体消失时回调
     */
    var onHide: (() -> Unit)? = null

    init {
        LayoutInflater.from(context).inflate(R.layout.common_full_screen_scroller_layout, this, true)
    }

    override fun setVisibility(visibility: Int) {
        if (visibility == View.VISIBLE) {
            doShowAnimation()
        } else {
            doHideAnimation(visibility)
        }
    }

    fun setVisibilityWithoutAnimation(visibility: Int) {
        super.setVisibility(visibility)
        if (visibility != VISIBLE) onHide?.invoke()
        scrollerMsgViewAnimatorHelper?.cancel()
    }

    private fun doShowAnimation() {
        if (isVisible) {
            return
        }
        super.setVisibility(VISIBLE)
        alpha = 0F
        this.animate().apply {
            alpha(1F)
            duration = ALPHA_ANIMATION_DURING_TIME
            interpolator = ALPHA_INTERPOLATOR
        }.start()
    }

    private fun doHideAnimation(visibility: Int) {
        if (!isVisible) {
            return
        }
        this.animate().apply {
            alpha(0F)
            duration = ALPHA_ANIMATION_DURING_TIME
            interpolator = ALPHA_INTERPOLATOR
            withEndAction {
                super.setVisibility(visibility)
                if (visibility != VISIBLE) onHide?.invoke()
                scrollerMsgViewAnimatorHelper?.cancel()
            }
        }.start()
    }

    /**
     * 获取滑块图片（有箭头的部分）区域的宽度
     */
    fun getScrollerImgAreaWidth(): Int {
        return scrollerImgView.marginStart + scrollerImgView.width + scrollerImgView.marginEnd
    }

    /**
     * 设置滑块提示消息文本
     */
    fun setMessage(msg: String) {
        if (scrollerMsgView.isVisible && scrollerMsgView.text.equals(msg).not()) scrollerMsgView.text = msg
    }

    /**
     * 滑块提示消息是否显示
     */
    fun isMessageVisible(): Boolean {
        return scrollerMsgView.isVisible
    }

    /**
     * 设置滑块提示消息显示或隐藏
     */
    fun setMessageVisibility(visibility: Int) {
        if (scrollerMsgViewAnimatorHelper == null) {
            scrollerMsgViewAnimatorHelper = ViewVisibilityAlphaAnimatorHelper(scrollerMsgView, ALPHA_INTERPOLATOR, ALPHA_ANIMATION_DURING_TIME)
        }
        scrollerMsgViewAnimatorHelper?.setVisibilityWithAlphaAnimation(if (visibility == VISIBLE) VISIBLE else GONE)
    }

    override fun dispatchSetPressed(pressed: Boolean) {
        super.dispatchSetPressed(pressed)
        doTouchAnimation(!pressed)
    }

    /**
     * 设置快滑条可滑动区域
     * @param minScrollHeight   快滑条最小滚动高度
     * @param maxScrollHeight   快滑条最大滚动高度
     */
    fun setScrollHeightRange(minScrollHeight: Int, maxScrollHeight: Int) {
        this.minScrollHeight = minScrollHeight
        this.maxScrollHeight = maxScrollHeight
    }

    override fun dispatchTouchEvent(event: MotionEvent?): Boolean {
        super.dispatchTouchEvent(event)
        when (event?.action) {
            MotionEvent.ACTION_CANCEL, MotionEvent.ACTION_UP       -> isPressed = false
            MotionEvent.ACTION_DOWN                                -> isPressed = true
        }
        return true
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        cancelAllAnimators()
    }

    private fun cancelAllAnimators() {
        scrollerAnimatorSet.cancel()
        reverseScrollerAnimatorSet.cancel()
    }

    private fun doTouchAnimation(isReverse: Boolean) {
        if (isReverse) {
            scrollerAnimatorSet.cancel()
            reverseScrollerAnimatorSet.start()
        } else {
            reverseScrollerAnimatorSet.cancel()
            scrollerAnimatorSet.start()
        }
    }

    private fun ObjectAnimator.setScaleTranslateConfig() = apply {
        interpolator = SCALE_INTERPOLATOR
        duration = SCALE_ANIMATION_DURING_TIME
    }

    companion object {
        private const val ALPHA_ANIMATION_DURING_TIME           = 350L
        private const val SCALE_ANIMATION_DURING_TIME           = 500L
        private const val SCALE_X_VALUE                         = 1.36F
        private const val SCALE_Y_VALUE                         = 1.27F
        private const val PIVOT_VALUE                           = 2
        private val ALPHA_INTERPOLATOR: Interpolator = PathInterpolator(0.33f, 0f, 0.67f, 1f)
        private val SCALE_INTERPOLATOR: Interpolator = PathInterpolator(0.3f, 0f, 0.1f, 1f)
    }
}