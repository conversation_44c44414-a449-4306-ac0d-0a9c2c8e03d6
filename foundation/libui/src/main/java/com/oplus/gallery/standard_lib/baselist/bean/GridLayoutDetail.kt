/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - LayoutDetail.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/07/21
 ** Author: <EMAIL>
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>	2020/07/21		1.0		create
 *********************************************************************************/
package com.oplus.gallery.standard_lib.baselist.bean

import com.oplus.gallery.foundation.util.debug.GLog
import kotlin.properties.Delegates

open class GridLayoutDetail : LayoutDetail() {

    /**
     * 一系列item的间隙，默认两边的item的最边上的间隙为0.
     */
    var itemHorizontalGaps: FloatArray? = null
        private set

    fun replaceGaps(gaps: FloatArray?) {
        itemHorizontalGaps = gaps
    }

    open class HorizontalGapsBuilder {

        companion object {
            private const val TAG = "HorizontalGapsBuilder"
            private const val MIN_SPAN_COUNT = 1
            private const val ITEM_HORIZONTAL_GAP_SIDE = 2
        }

        var spanCount: Int by Delegates.notNull()
        var parentWidth: Int by Delegates.notNull()
        var gapWidth: Int by Delegates.notNull()

        // 边界的宽度，默认是0
        var edgeWidth: Int = 0

        protected open fun createLayoutDetail(): GridLayoutDetail = GridLayoutDetail()

        open fun build(): GridLayoutDetail {
            return createLayoutDetail().apply {
                spanCount = <EMAIL>
                itemHorizontalGaps = initItemHorizontalGaps()
                itemWidth = if (spanCount > 0) (parentWidth - edgeWidth * 2 - gapWidth * (spanCount - 1)) / spanCount else 0
            }
        }

        /**
         * 获取一系列item的间隙，默认两边的item的最边上的间隙为0.
         *
         */
        private fun initItemHorizontalGaps(): FloatArray? {
            if (spanCount < MIN_SPAN_COUNT) {
                GLog.e(TAG, "initItemHorizontalGaps spanCount=$spanCount MIN_SPAN_COUNT=$MIN_SPAN_COUNT")
                return null
            }
            val size = spanCount * ITEM_HORIZONTAL_GAP_SIDE
            val result = FloatArray(size)
            result[0] = edgeWidth.toFloat()
            if (spanCount > MIN_SPAN_COUNT) {
                val itemMaxGap = (edgeWidth * 2 + gapWidth * (spanCount - 1)) / spanCount
                // 第0个item的right
                result[1] = itemMaxGap - edgeWidth.toFloat()

                for (i in 1 until spanCount) {
                    result[ITEM_HORIZONTAL_GAP_SIDE * i] = gapWidth - result[ITEM_HORIZONTAL_GAP_SIDE * i - 1]
                    result[ITEM_HORIZONTAL_GAP_SIDE * i + 1] = itemMaxGap - result[ITEM_HORIZONTAL_GAP_SIDE * i]
                }
            }
            result[size - 1] = edgeWidth.toFloat()
            return result
        }
    }
}