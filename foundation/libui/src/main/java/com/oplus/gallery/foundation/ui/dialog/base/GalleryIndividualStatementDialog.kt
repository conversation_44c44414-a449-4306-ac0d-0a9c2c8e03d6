/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - GalleryIndividualStatementDialog
 ** Description: GalleryIndividualStatementDialog
 ** Version: 1.0
 ** Date : 2023/8/7
 ** Author: dingyong@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  dingyong@Apps.Gallery3D      2023/08/07    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.foundation.ui.dialog.base

import android.content.Context
import com.coui.appcompat.statement.COUIIndividualStatementDialog
import com.oplus.gallery.foundation.util.thread.ThreadUtils

open class GalleryIndividualStatementDialog @JvmOverloads constructor(
    context: Context,
    theme: Int = 0,
    frequency: Float = 0.0f,
    dampingRatio: Float = 0.0f
) : COUIIndividualStatementDialog(context, theme, frequency, dampingRatio) {

    override fun show() {
        super.show()
        ThreadUtils.assertInMainThread()
    }
}






