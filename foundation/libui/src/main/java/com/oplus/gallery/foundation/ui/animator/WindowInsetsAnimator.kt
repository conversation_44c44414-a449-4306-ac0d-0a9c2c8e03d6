/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : WindowInsetsAnimator.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/09/12  11:16
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/09/12      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.foundation.ui.animator

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.view.animation.PathInterpolator
import androidx.core.view.WindowInsetsAnimationControllerCompat
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag

/**
 * WindowInsets动画  系统导航为SystemBarController添加动画
 *
 */
abstract class WindowInsetsAnimator(val isShown: Boolean) : ValueAnimator() {
    var controller: WindowInsetsAnimationControllerCompat? = null

    init {
        setFloatValues(PATH_ANIMATOR_START, PATH_ANIMATOR_END)
        interpolator = NAVI_INTERPOLATOR
        duration = ANIMATOR_DURATION
    }

    override fun start() {
        clean()
        addUpdateListener {
            onUpdate(controller, animatedFraction)
        }
        addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationStart(animation: Animator) {
                super.onAnimationStart(animation)
                onStart(controller)
            }

            override fun onAnimationEnd(animation: Animator, isReverse: Boolean) {
                super.onAnimationEnd(animation, isReverse)
                onEnd(controller)
                clean()
            }
        })
        super.start()
    }

    fun clean() {
        cancel()
        removeAllListeners()
        removeAllUpdateListeners()
    }

    /**
     * 动画开始
     */
    abstract fun onStart(controller: WindowInsetsAnimationControllerCompat?)

    /**
     * 动画进行
     * @param progress 进度
     */
    abstract fun onUpdate(controller: WindowInsetsAnimationControllerCompat?, progress: Float)

    /**
     * 动画结束
     */
    abstract fun onEnd(controller: WindowInsetsAnimationControllerCompat?)

    companion object {
        private const val PATH_ANIMATOR_START = 0f
        private const val PATH_ANIMATOR_END = 1f
        private const val ANIMATOR_DURATION = 333L
        private val NAVI_INTERPOLATOR = PathInterpolator(0f, 0f, 0.1f, 1f)
    }
}

/**
 * WindowInsets 淡入显示动画
 *
 */
class WindowInsetsFadeInAnimator : WindowInsetsAnimator(true) {

    override fun onStart(controller: WindowInsetsAnimationControllerCompat?) {}

    override fun onUpdate(controller: WindowInsetsAnimationControllerCompat?, progress: Float) {
        runCatching {
            val isReady = controller?.isReady ?: false
            if (isReady) {
                controller?.setInsetsAndAlpha(
                    controller.shownStateInsets,
                    progress,
                    progress
                )
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "onUpdate. onFailure=$it")
        }
    }

    override fun onEnd(controller: WindowInsetsAnimationControllerCompat?) {
        controller?.finish(isShown)
    }

    companion object {
        private const val TAG = "WindowInsetsFadeInAnimator"
    }
}

/**
 * WindowInsets 淡出退出动画
 */
class WindowInsetsFadeOutAnimator : WindowInsetsAnimator(false) {

    override fun onStart(controller: WindowInsetsAnimationControllerCompat?) {}

    override fun onUpdate(controller: WindowInsetsAnimationControllerCompat?, progress: Float) {
        runCatching {
            //当 controller 未已准备好时执行动画,会出现IllegalStateException异常,需要先判断 controller 是否已准备好
            val isReady = controller?.isReady ?: false
            if (isReady) {
                controller?.setInsetsAndAlpha(
                    controller.shownStateInsets,
                    1 - progress,
                    progress
                )
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "onUpdate. onFailure=$it")
        }
    }

    override fun onEnd(controller: WindowInsetsAnimationControllerCompat?) {
        controller?.finish(isShown)
    }

    companion object {
        private const val TAG = "WindowInsetsFadeOutAnimator"
    }
}