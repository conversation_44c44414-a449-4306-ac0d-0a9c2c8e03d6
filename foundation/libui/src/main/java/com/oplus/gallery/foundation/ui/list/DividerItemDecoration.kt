/********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DividerItemDecoration
 ** Description:用于线性类型布局的分隔装饰绘制器
 ** Version: 1.0
 ** Date: 2024-05-20
 ** Author: zhongxuechang@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** zhongxuechang@Apps.Gallery3D    2024-05-20     1.0
 ********************************************************************************/
package com.oplus.gallery.foundation.ui.list

import android.graphics.Canvas
import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import kotlin.math.roundToInt

/**
 * 用于线性类型布局的分隔装饰绘制器
 * @param orientation 线性布局方向，例如[LinearLayoutManager.VERTICAL]
 * @param reverseLayout 是否反转，反转则调换[isDrawHead]和[isDrawTail]的效果
 * @param dividerSize 设置item间分隔的大小
 */
class DividerItemDecoration(
    private val orientation: Int,
    private val reverseLayout: Boolean,
    private val dividerSize: Int
) : RecyclerView.ItemDecoration() {

    /**
     * 是否绘制头部
     */
    var isDrawHead = false
    /**
     * 是否绘制尾部
     */
    var isDrawTail = false
    /**
     * 设置item间分隔的绘制图样
     */
    var dividerDrawable: Drawable? = null
    /**
     * 分隔绘制的回调，返回false代表不绘制该项的分隔
     */
    var dividerDraw: ((position: Int) -> Boolean)? = null
    private val bounds = Rect()
    private val outRect = Rect()

    override fun onDraw(canvas: Canvas, parent: RecyclerView, state: RecyclerView.State) {
        if (dividerSize <= 0) return
        canvas.save()
        var left = 0
        var top = 0
        var right: Int = parent.width
        var bottom = parent.height
        if (parent.clipToPadding) {
            left = parent.paddingLeft
            top = parent.paddingTop
            right = parent.width - parent.paddingRight
            bottom = parent.height - parent.paddingBottom
            canvas.clipRect(left, top, right, bottom)
        }

        val divider = dividerDrawable ?: return
        for (i in 0 until parent.childCount) {
            val child = parent.getChildAt(i)
            parent.getDecoratedBoundsWithMargins(child, bounds)
            getItemOffsets(outRect, child, parent)
            if ((outRect.left <= 0) &&
                (outRect.top <= 0) &&
                (outRect.right <= 0) &&
                (outRect.bottom <= 0)) continue
            if (outRect.left > 0) {
                left = bounds.left + child.translationX.roundToInt()
                right = left + outRect.left
            }
            if (outRect.top > 0) {
                top = bounds.top + child.translationY.roundToInt()
                bottom = top + outRect.top
            }
            if (outRect.right > 0) {
                right = bounds.right + child.translationX.roundToInt()
                left = right - outRect.right
            }
            if (outRect.bottom > 0) {
                bottom = bounds.bottom + child.translationY.roundToInt()
                top = bottom - outRect.bottom
            }
            divider.setBounds(left, top, right, bottom)
            divider.draw(canvas)
        }
        canvas.restore()
    }

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        if (dividerSize <= 0) return
        getItemOffsets(outRect, view, parent)
    }

    private fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView
    ) {
        outRect.setEmpty()
        val position = (view.layoutParams as RecyclerView.LayoutParams).absoluteAdapterPosition
        if (position < 0 || (dividerDraw?.invoke(position) == false)) return
        if (orientation == LinearLayoutManager.VERTICAL) {
            getItemOffsetsOnVertical(outRect, parent, position)
        } else {
            getItemOffsetsOnHorizontal(outRect, parent, position)
        }
    }

    private fun getItemOffsetsOnVertical(
        outRect: Rect,
        parent: RecyclerView,
        position: Int
    ) {
        val totalCount = parent.adapter?.itemCount ?: return

        if (reverseLayout) {
            // 如果布局反转，则代表头尾也反转
            if (isDrawHead || (position != 0)) {
                outRect.bottom = dividerSize
            }
            if (isDrawTail && (position == totalCount - 1)) {
                outRect.top = dividerSize
            }
        } else {
            if (isDrawHead || (position != 0)) {
                outRect.top = dividerSize
            }
            if (isDrawTail && (position == totalCount - 1)) {
                outRect.bottom = dividerSize
            }
        }
    }

    private fun getItemOffsetsOnHorizontal(
        outRect: Rect,
        parent: RecyclerView,
        position: Int
    ) {
        val totalCount = parent.adapter?.itemCount ?: return

        if (reverseLayout) {
            // 如果布局反转，则代表头尾也反转
            if (isDrawHead || (position != 0)) {
                outRect.right = dividerSize
            }
            if (isDrawTail && (position == totalCount - 1)) {
                outRect.left = dividerSize
            }
        } else {
            if (isDrawHead || (position != 0)) {
                outRect.left = dividerSize
            }
            if (isDrawTail && (position == totalCount - 1)) {
                outRect.right = dividerSize
            }
        }
    }
}