/*****************************************************************
 * Copyright (C), 2022-2022, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 * File: AccessSqlSimplifierTest.kt
 * Description:
 * Version: 1.0
 * Date: 2023/7/28
 * Author: Kun.Qin
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>         <date>          <version>       <desc>
 * ------------------------------------------------------------------------------
 * Kun.Qin          2023/7/28         1.0             ---
 ****************************************************************/

package com.oplus.gallery.foundation.database.util.simplifier

import com.oplus.gallery.foundation.database.store.GalleryStore
import org.junit.Assert
import org.junit.Test

class AccessSqlSimplifierTest {
    @Test
    fun should_same_as_original_sql_when_makeSimple_with_sql_not_included_target_table() {
        // given
        val originalTable = GalleryStore.GalleryMedia.TAB
        val targetTable = GalleryStore.GalleryMedia.FILTER_LOCAL_VIEW
        val originalSql = buildSql(originalTable + "_view")

        // when
        val accessSqlSimplifier = AccessSqlSimplifier.create(originalSql)
        accessSqlSimplifier.setOnMakeSimpleValidator(object : AccessSqlSimplifier.OnMakeSimpleValidator {
            override fun isMakeSimpleSql(simpleSql: String): Boolean {
                return simpleSql.contains(originalTable)
            }
        })
        accessSqlSimplifier.makeSimple()
        val newSql = accessSqlSimplifier.revertSimpleSql { _, ownerSimpleSql ->
            return@revertSimpleSql ownerSimpleSql?.replace(("\\b$originalTable\\b").toRegex(), targetTable)
        }

        // then
        Assert.assertEquals(originalSql, newSql)
    }

    @Test
    fun should_only_replace_table_when_makeSimple_with_string_value_included_target_table() {
        // given
        val originalTable = GalleryStore.GalleryMedia.TAB
        val targetTable = GalleryStore.GalleryMedia.FILTER_LOCAL_VIEW
        val originalSql = buildSql(originalTable)
        val targetSql = buildSql(targetTable)

        // when
        val accessSqlSimplifier = AccessSqlSimplifier.create(originalSql)
        accessSqlSimplifier.setOnMakeSimpleValidator(object : AccessSqlSimplifier.OnMakeSimpleValidator {
            override fun isMakeSimpleSql(simpleSql: String): Boolean {
                return simpleSql.contains(originalTable)
            }
        })
        val result = accessSqlSimplifier.makeSimple()
        val newSql = accessSqlSimplifier.revertSimpleSql { _, ownerSimpleSql ->
            return@revertSimpleSql ownerSimpleSql?.replace(("\\b$originalTable\\b").toRegex(), targetTable)
        }

        // then
        Assert.assertTrue(result)
        Assert.assertEquals(targetSql, newSql)
    }

    private fun buildSql(targetTable: String): String {
        return """
            SELECT $targetTable._id, senior_media.version, SUM($targetTable._size) AS total_size,
            CASE
                WHEN $targetTable.media_score >= 0.0 AND $targetTable.media_score < 1.0 THEN '0.0'
            END
            AS score
            FROM $targetTable 
            LEFT JOIN senior_media 
            ON senior_media._data = $targetTable._data 
            WHERE (
                $targetTable.media_id > 0 
                AND $targetTable._data != '/storage/emulated/0/${GalleryStore.GalleryMedia.TAB}_view/screen_shot_1_1.png' 
                AND $targetTable._id IN (
                    SELECT $targetTable._id 
                    FROM $targetTable 
                    WHERE ($targetTable.cshot_id != 0) 
                    GROUP BY $targetTable.cshot_id HAVING $targetTable._data = MIN ($targetTable._data)
                ) 
                AND (
                    ($targetTable._data REGEXP "
                        .+/(DCIM/Camera/|DCIM/MyAlbums/${GalleryStore.GalleryMedia.TAB}/[^/]+/|Pictures/MyCollections/[^/]+/)IMG_\d{8}_\d{6}_\d{3}\..+
                    ") 
                    OR ($targetTable.relative_path REGEXP "(\?i)(DCIM/Camera/Cshot/)([1-9][0-9]*)/|(DCIM/MyAlbums/[^/]+/Cshot/)([1-9][0-9]*)/")
                ) 
                AND (
                    $targetTable.day IN (
                        SELECT festival.date 
                        FROM festival 
                        WHERE (festival.filterType & 1 = 1 AND festival.region LIKE 'cn%') 
                    )
                )
            ) 
            GROUP BY $targetTable.month  
            ORDER BY $targetTable._id DESC
            """.trimIndent()
    }
}