/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MyAlbumSceneSqlUtil.kt
 ** Description:根据场景生成真正的查询条件
 ** Version: 1.0
 ** Date: 2025/5/13
 ** Author: <PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON><EMAIL>     2025/5/13     1.0         OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.foundation.database.album.query.generator.scene.condition.myalbums

import android.os.Bundle
import com.oplus.gallery.foundation.database.album.query.generator.scene.getDisplayCountSql
import com.oplus.gallery.foundation.database.album.query.generator.scene.group.BaseAlbumSetSqlGenerator.Companion.ALBUM_DISTINCT_KEY
import com.oplus.gallery.foundation.database.album.query.generator.scene.group.BaseAlbumSetSqlGenerator.Companion.CONDITION_EXCLUDE_ALBUM_FLAGS_KEY
import com.oplus.gallery.foundation.database.album.query.generator.scene.group.BaseAlbumSetSqlGenerator.Companion.CONDITION_INCLUDE_ALBUM_FLAGS_KEY
import com.oplus.gallery.foundation.database.helper.Constants.Album.AlbumTableFlag.FLAG_MEDIA_TYPE_ALBUM
import com.oplus.gallery.foundation.database.helper.Constants.Album.AlbumTableFlag.FLAG_REAL_ALBUM
import com.oplus.gallery.foundation.database.helper.Constants.Album.AlbumTableFlag.FLAG_VIRTUAL_ALBUM
import com.oplus.gallery.foundation.database.helper.Constants.Album.RealTable.TABLE_ALBUM
import com.oplus.gallery.foundation.database.helper.Constants.Album.SceneProjection.ALBUM_PROJECTION
import com.oplus.gallery.foundation.database.helper.Constants.Album.SceneProjection.DISPLAY_COUNT
import com.oplus.gallery.foundation.database.helper.Constants.Album.SpecialAlbumFlag.FLAG_ALL_PICTURE_ALBUM
import com.oplus.gallery.foundation.database.helper.Constants.Album.SpecialAlbumFlag.FLAG_CAMERA_ALBUM
import com.oplus.gallery.foundation.database.helper.Constants.Album.SpecialAlbumFlag.FLAG_CARD_CASE_ALBUM
import com.oplus.gallery.foundation.database.helper.Constants.Album.SpecialAlbumFlag.FLAG_CLEAR
import com.oplus.gallery.foundation.database.helper.Constants.Album.SpecialAlbumFlag.FLAG_MAP_ALBUM
import com.oplus.gallery.foundation.database.helper.Constants.Album.SpecialAlbumFlag.FLAG_VIDEO_ALBUM
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.ALL_PICTURE_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.ALL_VIDEO_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.CAMERA_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.CARD_ID_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.MAP_ALBUM_ID
import com.oplus.gallery.foundation.database.store.CacheStore.AlbumColumns.ALBUM_ID
import com.oplus.gallery.foundation.database.store.CacheStore.AlbumColumns.FLAGS
import com.oplus.gallery.foundation.database.store.CacheStore.AlbumColumns.ORDERED_POSITION
import com.oplus.gallery.foundation.database.store.CacheStore.AlbumColumns.ORDERED_SUB_POSITION
import com.oplus.gallery.foundation.database.store.CacheStore.AlbumColumns.TOTAL_COUNT
import com.oplus.gallery.foundation.database.util.DatabaseUtils.QUERY_ARG_SQL_LIMIT
import com.oplus.gallery.foundation.database.util.DatabaseUtils.QUERY_ARG_SQL_ORDER_BY
import com.oplus.gallery.foundation.database.util.SQLGrammar.AND
import com.oplus.gallery.foundation.database.util.SQLGrammar.COMMA
import com.oplus.gallery.foundation.database.util.SQLGrammar.COUNT_ALL
import com.oplus.gallery.foundation.database.util.SQLGrammar.COUNT_DISTINCT
import com.oplus.gallery.foundation.database.util.SQLGrammar.DOT
import com.oplus.gallery.foundation.database.util.SQLGrammar.GREATER_THAN_ZERO
import com.oplus.gallery.foundation.database.util.SQLGrammar.LEFT_BRACKETS
import com.oplus.gallery.foundation.database.util.SQLGrammar.OR
import com.oplus.gallery.foundation.database.util.SQLGrammar.RIGHT_BRACKETS
import com.oplus.gallery.foundation.database.util.SQLSelectBuilder
import com.oplus.gallery.foundation.util.ext.bracket
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING

/**
 * 我的图集构造sql util
 */
internal object MyAlbumSceneSqlUtil {

    /**
     * 将默认的orderBy写入参数中
     */
    @JvmStatic
    internal fun putOrderBy(queryArgs: Bundle) {
        if (queryArgs.getString(QUERY_ARG_SQL_ORDER_BY).isNullOrEmpty()) {
            queryArgs.putString(QUERY_ARG_SQL_ORDER_BY, "$TABLE_ALBUM$DOT$ORDERED_POSITION,$TABLE_ALBUM$DOT$ORDERED_SUB_POSITION")
        }
    }

    /**
     * 拼接orderBy和limit
     */
    @JvmStatic
    private fun appendOrderByAndLimitIfNeed(queryArgs: Bundle, sqlBuilder: SQLSelectBuilder) {
        queryArgs.getString(QUERY_ARG_SQL_ORDER_BY)?.takeIf { it.trim().isNotEmpty() }?.let { sqlBuilder.orderBy(it) }
        queryArgs.getString(QUERY_ARG_SQL_LIMIT)?.takeIf { it.trim().isNotEmpty() }?.let { sqlBuilder.limit(it) }
    }

    /**
     * 获取图集列表数据的查询sql语句
     */
    @JvmStatic
    internal fun buildRawSql(conditionColumn: String, projections: Array<String>?, queryArgs: Bundle): String {
        putOrderBy(queryArgs)
        return buildMediaConditionSql(conditionColumn, queryArgs, projections, "*", isQueryData = true)
    }

    /**
     * 获取图集列表总数
     */
    @JvmStatic
    internal fun buildCountRawSqlForMediaCondition(conditionColumn: String, queryArgs: Bundle): String {
        val isDistinct = queryArgs.getBoolean(ALBUM_DISTINCT_KEY, false)
        // 获取后重置去重标记，以防止后面查询sql加上错误的group by语句导致查询错误
        queryArgs.putBoolean(ALBUM_DISTINCT_KEY, false)
        return buildMediaConditionSql(
            conditionColumn,
            queryArgs,
            arrayOf(getCountAllProjection(isDistinct)),
            COUNT_ALL,
            isQueryData = false
        )
    }

    /**
     * 根据媒体文件类型，筛选出我的图集
     * 如：获取数据的sql
     *  SELECT .... 若是查总数 则为 countAll，若是查数据则为列
     *  FROM ( SELECT album.album_id,
     * 			( SELECT sum(album_count.jpeg + album_count.heif)
     * 			  FROM album_count
     * 			  WHERE EXISTS (SELECT 1 FROM JSON_EACH(bucket_ids) AS item WHERE item.value=bucket_id)
     * 			)  AS  displayCount
     * 		  FROM album
     * 		  WHERE (displayCount > 0  AND (album.flags & 32 != 32 AND  (album.flags & 1 == 1  OR  album.flags & 2 == 2)) AND ( album_id NOT IN (2064266562,-134776996,1552915770) )
     *     )
     * 根据媒体类型作为过滤条件的一级列表查询语句:用于查询数据或数量
     * @param conditionColumn 媒体类型条件,作为列,用于计算总数 如：image-gif
     * @param queryArgs 查询参数
     * @param projections 返回列
     * @param defaultProjection 若projections为null,则返回此默认列
     */
    @JvmStatic
    private fun buildMediaConditionSql(
        conditionColumn: String,
        queryArgs: Bundle,
        projections: Array<String>?,
        defaultProjection: String,
        isQueryData: Boolean
    ): String {
        val albumTab = TABLE_ALBUM

        val displayCountSql = getDisplayCountSql(conditionColumn)

        val subWhereSb = StringBuilder().apply {
            append(LEFT_BRACKETS)
            append("$DISPLAY_COUNT $GREATER_THAN_ZERO ")
            append(AND)
            append(bracket(getAlbumFlagWhere(FLAG_REAL_ALBUM or FLAG_VIRTUAL_ALBUM, albumTab)))
            appendExcludeAlbumByFlag(queryArgs)?.let { excludeWhere ->
                append(AND)
                append(excludeWhere)
            }
            append(RIGHT_BRACKETS)
        }
        appendRequiredAlbumByFlag(queryArgs, subWhereSb)

        val subSqlBuilder = SQLSelectBuilder.build()
            .select(buildSubProjectionsIfNeed(isQueryData, projections, defaultProjection))
            .append("$displayCountSql, $ALBUM_ID")
            .from(albumTab)
            .where(subWhereSb.toString())
        appendGroupByIfNeed(queryArgs, subSqlBuilder, ALBUM_ID)
        appendOrderByAndLimitIfNeed(queryArgs, subSqlBuilder)

        //查找图集列表总数
        return SQLSelectBuilder.build()
            //将 totalCount的值替换成displayCount的
            .select(buildDisplayCountAsTotalCountProjections(projections, defaultProjection))
            .from(bracket(subSqlBuilder.toString()))
            .toString()
    }

    /**
     * 子查询的列,先计算displayCount,将displayCount和列一起返回
     * @param isQueryData 是否查询数据,true则返回空,false:则返回子列
     */
    @JvmStatic
    private fun buildSubProjectionsIfNeed(isQueryData: Boolean, projections: Array<String>?, defaultProjection: String): String {
        if (isQueryData.not()) {
            return EMPTY_STRING
        }
        return buildProjection(projections, defaultProjection) + COMMA
    }

    /**
     * 增加一定要返回的图集
     */
    @JvmStatic
    private fun appendRequiredAlbumByFlag(queryArgs: Bundle, querySb: StringBuilder) {
        val requiredAlbumFlags = queryArgs.getInt(CONDITION_INCLUDE_ALBUM_FLAGS_KEY)
        if (requiredAlbumFlags and FLAG_CARD_CASE_ALBUM == FLAG_CARD_CASE_ALBUM) {
            //包含随身卡包图集(只要开关开启，不管是否有照片都需要显示)
            querySb.append("OR $ALBUM_ID = $CARD_ID_ALBUM_ID ")
        }
        if (requiredAlbumFlags and FLAG_MAP_ALBUM == FLAG_MAP_ALBUM) {
            //包含地图图集(地图图集图集数量为0，只要开关开启,必须显示）
            querySb.append("OR $ALBUM_ID = $MAP_ALBUM_ID ")
        }
    }

    /**
     * 构建返回的列数,并将totalCount的值替换成真实的displayCount,避免不同场景返回的都是totalCount
     */
    @JvmStatic
    private fun buildDisplayCountAsTotalCountProjections(projections: Array<String>?, default: String): String {
        projections ?: return default
        var tmpProjections = projections
        if (isAllProjection(tmpProjections)) {
            tmpProjections = ALBUM_PROJECTION
        }
        val projectionSb = StringBuilder()
        val lastIndex = tmpProjections.size - 1
        tmpProjections.forEachIndexed { index, projection ->
            if (TOTAL_COUNT == projection.trim()) {
                projectionSb.append("$DISPLAY_COUNT AS $TOTAL_COUNT")
            } else {
                projectionSb.append(projection)
            }
            if (lastIndex != index) {
                projectionSb.append(COMMA)
            }
        }
        return projectionSb.toString()
    }

    /**
     * 查询我的图集列表内容的sql
     * SELECT _id,album_id,album_path,display_name,bucket_ids,item_sort_type,total_count,trashed_count,flags,ordered_position,ordered_sub_position,date_taken,date_modified,cover_info
     * FROM album
     * WHERE (totalCount > 0
     *      AND flags & 32 != 32
     *      AND album_id not in (CAMERA_BUCKET_ID, ALL_PICTURE_VIRTUAL_BUCKET_ID, VIDEO_VIRTUAL_BUCKET_ID)
     *      )
     *      OR ( 特殊情况 随身卡包/地图开启）
     * ORDER BY album.ordered_position,album.ordered_sub_position
     *
     * @param albumFlag AlbumTableFlag 图集类型flag：如实体图集flag、虚拟图集flag、特殊图集flag（随身卡包、地图图集）、媒体类型图集、更多图集类型
     * @see com.oplus.gallery.foundation.database.helper.Constants.Album.AlbumTableFlag 中的
     */
    @JvmStatic
    internal fun buildAllOrRealAlbumSql(
        queryArgs: Bundle,
        projections: Array<String>?,
        defaultProjection: String,
        albumFlag: Int
    ): String {
        val albumTab = TABLE_ALBUM
        val whereSb = StringBuilder().apply {
            append(LEFT_BRACKETS)
            append("$albumTab.$TOTAL_COUNT > 0 ")
            append(AND)
            append(bracket(getAlbumFlagWhere(albumFlag, albumTab)))
            appendExcludeAlbumByFlag(queryArgs)?.let { excludeWhere ->
                append(AND)
                append(excludeWhere)
            }
            append(RIGHT_BRACKETS)
        }
        appendRequiredAlbumByFlag(queryArgs, whereSb)
        val sqlBuilder = SQLSelectBuilder.build()
            .select(buildProjection(projections, defaultProjection))
            .from(albumTab)
            .where(whereSb.toString())
        appendGroupByIfNeed(queryArgs, sqlBuilder, "$albumTab.$ALBUM_ID")
        appendOrderByAndLimitIfNeed(queryArgs, sqlBuilder)
        return sqlBuilder.toString()
    }

    @JvmStatic
    private fun appendExcludeAlbumByFlag(queryArgs: Bundle): String? {
        val excludeFlags = queryArgs.getInt(CONDITION_EXCLUDE_ALBUM_FLAGS_KEY, FLAG_CLEAR)
        val excludeAlbumIdSb = StringBuilder()
        if (excludeFlags > FLAG_CLEAR) {
            if (excludeFlags and FLAG_CAMERA_ALBUM == FLAG_CAMERA_ALBUM) {
                excludeAlbumIdSb.append("$CAMERA_ALBUM_ID")
            }
            if (excludeFlags and FLAG_ALL_PICTURE_ALBUM == FLAG_ALL_PICTURE_ALBUM) {
                excludeAlbumIdSb.append(if (excludeAlbumIdSb.isNotEmpty()) ",$ALL_PICTURE_ALBUM_ID" else "$ALL_PICTURE_ALBUM_ID")
            }
            if (excludeFlags and FLAG_VIDEO_ALBUM == FLAG_VIDEO_ALBUM) {
                excludeAlbumIdSb.append(if (excludeAlbumIdSb.isNotEmpty()) ",$ALL_VIDEO_ALBUM_ID" else "$ALL_VIDEO_ALBUM_ID")
            }
        }
        if (excludeAlbumIdSb.isEmpty()) {
            return null
        }
        return "$LEFT_BRACKETS $ALBUM_ID NOT IN ($excludeAlbumIdSb) $RIGHT_BRACKETS"
    }

    /**
     * 根据传入的flag,设置支持的图集类型：我的图集：不支持媒体类型图集,只要查出实体图集或虚拟图集或全部
     *
     * flags & $FLAG_MEDIA_TYPE_ALBUM != $FLAG_MEDIA_TYPE_ALBUM
     * AND
     * (FLAGS & FLAG_REAL_ALBUM == FLAG_REAL_ALBUM OR FLAGS & FLAG_VIRTUAL_ALBUM == FLAG_VIRTUAL_ALBUM)
     */
    @JvmStatic
    private fun getAlbumFlagWhere(albumFlag: Int, table: String): String {
        return StringBuilder("$table.$FLAGS & $FLAG_MEDIA_TYPE_ALBUM != $FLAG_MEDIA_TYPE_ALBUM").apply {
            val needRealAlbum = (albumFlag and FLAG_REAL_ALBUM) == FLAG_REAL_ALBUM
            val needVirtualAlbum = (albumFlag and FLAG_VIRTUAL_ALBUM) == FLAG_VIRTUAL_ALBUM
            val flagWhere = when {
                needRealAlbum && needVirtualAlbum -> {
                    "$AND ($table.$FLAGS & $FLAG_REAL_ALBUM == $FLAG_REAL_ALBUM $OR $table.$FLAGS & $FLAG_VIRTUAL_ALBUM == $FLAG_VIRTUAL_ALBUM)"
                }

                needRealAlbum -> "$AND ($table.$FLAGS & $FLAG_REAL_ALBUM == $FLAG_REAL_ALBUM)"
                needVirtualAlbum -> "$AND ($table.$FLAGS & $FLAG_VIRTUAL_ALBUM == $FLAG_VIRTUAL_ALBUM)"
                else -> EMPTY_STRING
            }
            append(flagWhere)
        }.toString()
    }

    /**
     * 构建查询结果列
     */
    @JvmStatic
    private fun buildProjection(projections: Array<String>?, default: String): String {
        projections ?: return default
        var tmpProjections = projections
        if (isAllProjection(tmpProjections)) {
            tmpProjections = ALBUM_PROJECTION
        }
        val projectionSb = StringBuilder()
        val lastIndex = tmpProjections.size - 1
        tmpProjections.forEachIndexed { index, projection ->
            projectionSb.append(projection)
            if (lastIndex != index) {
                projectionSb.append(COMMA)
            }
        }
        return projectionSb.toString()
    }

    /**
     * 是否传入的是*
     */
    @JvmStatic
    private fun isAllProjection(tmpProjections: Array<String>) =
        (tmpProjections.size == 1) &&
                ((tmpProjections[0].trim() == "*") || (tmpProjections[0].trim() == "$TABLE_ALBUM.*"))

    @JvmStatic
    private fun appendGroupByIfNeed(queryArgs: Bundle, sqlBuilder: SQLSelectBuilder, groupBy: String) {
        if (queryArgs.getBoolean(ALBUM_DISTINCT_KEY, false)) {
            sqlBuilder.groupBy(groupBy)
        }
    }

    /**
     * 根据是否需要去重返回不同的count语句
     * @param isDistinct 是否去重，由业务传递决定
     * @return count语句
     */
    @JvmStatic
    internal fun getCountAllProjection(isDistinct: Boolean): String {
        return if (isDistinct) {
            "$COUNT_DISTINCT $ALBUM_ID $RIGHT_BRACKETS"
        } else {
            COUNT_ALL
        }
    }
}