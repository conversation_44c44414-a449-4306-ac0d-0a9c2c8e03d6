/***********************************************************
 * * Copyright (C), 2018-2028, OPlus Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File       :  - ${FILE_NAME}
 * * Description:
 * * Version: 1.0
 * * Date : 2020/03/24
 * * Author: biao.chen@Apps.Gallery3D
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>                 <data>     <version>  <desc>
 * *  biao.chen@Apps.Gallery3D  2020/03/24        build this module
 ****************************************************************/
package com.oplus.gallery.foundation.database.table;

import android.content.ContentValues;
import android.database.sqlite.SQLiteDatabase;

import com.oplus.gallery.foundation.database.store.GalleryStore;
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns;
import com.oplus.gallery.foundation.database.util.DatabaseUtils;
import com.oplus.gallery.foundation.database.util.SensitiveFieldsConverter;
import com.oplus.gallery.foundation.util.mask.PathMask;
import com.oplus.gallery.foundation.util.debug.GLog;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import static com.oplus.gallery.foundation.database.util.ConstantUtils.AFTER_DELETE_ON;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.AFTER_UPDATE_OF;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.AND;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.BEGIN;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.CREATE_INDEX;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.CREATE_TABLE;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.CREATE_TRIGGER;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.DELETE_FROM;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.DROP_TRIGGER;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.END;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.EQUAL;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.INDEX_ON;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.INTEGER;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.NEW_DOT;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.OLD_DOT;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.ON;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.PRIMARY_KEY;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.TEXT;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.UNIQUE;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.WHERE;

public class AppendToAlbumTable implements BaseTable, SensitiveFieldsConverter {
    private static final String TAG = DatabaseUtils.DATA_TAG + "AppendToAlbumTable";
    private static final String[] SENSITIVE_FIELDS = new String[]{
            GalleryStore.AppendToAlbumColumns.DATA,
            GalleryStore.AppendToAlbumColumns.TARGET,
    };

    private String getTableName() {
        return GalleryStore.AppendToAlbum.TAB;
    }

    /*
     * append_to_album table
     */
    private String getCreateTableSql() {
        return CREATE_TABLE
                + GalleryStore.AppendToAlbum.TAB
                + " ("
                + GalleryStore.AppendToAlbumColumns._ID + INTEGER + PRIMARY_KEY + ","

                + GalleryStore.AppendToAlbumColumns.DATA + TEXT + UNIQUE + ","
                + GalleryStore.AppendToAlbumColumns.STATE + INTEGER + ","
                + GalleryStore.AppendToAlbumColumns.TARGET + TEXT
                + ");";
    }

    private void updateIndex(SQLiteDatabase db) {
        db.execSQL(CREATE_INDEX + GalleryStore.AppendToAlbum.TAB + "_" + GalleryStore.AppendToAlbumColumns.DATA
                + INDEX_ON + GalleryStore.AppendToAlbum.TAB + "(" + GalleryStore.AppendToAlbumColumns.DATA + ")");
        db.execSQL(CREATE_INDEX + GalleryStore.AppendToAlbum.TAB + "_" + GalleryStore.AppendToAlbumColumns.TARGET
                + INDEX_ON + GalleryStore.AppendToAlbum.TAB + "(" + GalleryStore.AppendToAlbumColumns.TARGET + ")");
    }

    private void updateTrigger(SQLiteDatabase db) {
        db.execSQL(DROP_TRIGGER + " append_to_album_delete ");
        db.execSQL(CREATE_TRIGGER + " append_to_album_delete_sync_with_gallery_media " + AFTER_DELETE_ON
                + GalleryStore.GalleryMedia.TAB
                + BEGIN
                + DELETE_FROM + GalleryStore.AppendToAlbum.TAB
                + WHERE + GalleryStore.AppendToAlbumColumns.DATA + EQUAL + OLD_DOT + LocalColumns.ORIGINAL_DATA + ";"
                + END);
        db.execSQL(CREATE_TRIGGER + " append_to_album_delete_when_recycle " + AFTER_UPDATE_OF
                + LocalColumns.IS_TRASHED
                + ON
                + GalleryStore.GalleryMedia.TAB
                + BEGIN
                + DELETE_FROM + GalleryStore.AppendToAlbum.TAB
                + WHERE + GalleryStore.AppendToAlbumColumns.DATA + EQUAL + OLD_DOT + LocalColumns.DATA
                + AND + NEW_DOT + LocalColumns.IS_TRASHED + EQUAL + LocalColumns.IS_TRASHED_TRUE
                + ";"
                + END);
    }

    @Override
    public void updateDatabase(SQLiteDatabase db, int oldVersion, int newVersion) {
        GLog.d(TAG, "updateDatabase oldVersion: " + oldVersion + ", newVersion: " + newVersion);
        db.execSQL(getCreateTableSql());
        updateIndex(db);
        updateTrigger(db);
    }

    @Override
    public void downDatabase(SQLiteDatabase db, int oldVersion, int newVersion) {
        GLog.w(TAG, "downDatabase oldVersion: " + oldVersion + ", newVersion: " + newVersion);
        // 数据库降级，已全部在provider處理
        /*db.execSQL(DROP_TABLE + getTableName());*/
    }

    @Nullable
    @Override
    public String convertContentValueToLog(@NotNull ContentValues values, @NotNull String key) {
        switch (key) {
            case GalleryStore.AppendToAlbumColumns.DATA:
            case GalleryStore.AppendToAlbumColumns.TARGET:
                return PathMask.INSTANCE.mask(values.getAsString(key));
            default:
                return values.getAsString(key);
        }
    }

    @Nullable
    @Override
    public String[] getSensitiveFields() {
        return SENSITIVE_FIELDS;
    }
}