/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - QueryMediaTypeOnlyImageSqlGenerator.kt
 ** Description:媒体类型图集：查找含有图片的图集集合
 ** Version: 1.0
 ** Date: 2024/4/11
 ** Author: <PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery     2024/4/11     1.0         OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.foundation.database.album.query.generator.scene.condition.mediatype

import android.os.Bundle
import com.oplus.gallery.foundation.database.album.query.generator.scene.ISceneSqlGenerator
import com.oplus.gallery.foundation.database.store.CacheStore.AlbumCountColumns.IMAGE

/**
 * 媒体类型图集：生成含有图片的图集集合的sql语句
 * 如随身卡包图集--选图
 */
class QueryMediaTypeOnlyImageSqlGenerator : ISceneSqlGenerator {

    override fun getQueryRawSql(projections: Array<String>?, queryArgs: Bundle): String {
        return MediaTypeSceneSqlUtil.buildRawSql(conditionColumn = SCENE_CONDITION, projections, queryArgs)
    }

    override fun getCountRawSql(queryArgs: Bundle): String {
        return MediaTypeSceneSqlUtil.buildCountRawSqlForMediaCondition(SCENE_CONDITION, queryArgs)
    }

    companion object {
        /**
         * 场景条件列
         */
        private const val SCENE_CONDITION = IMAGE
    }
}