/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - CountAllConvert.java
 * Description:
 * Version: 1.0
 * Date: 2020/8/11
 * Author: Wangrunxin@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * Wangrunxin@Apps.Gallery3D      2020/8/12      1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/
package com.oplus.gallery.foundation.dbaccess.convert

import android.database.Cursor
import com.oplus.gallery.foundation.util.debug.GLog

class CountAllConvert : IConvert<Cursor, Int> {

    companion object {
        private const val TAG = "CountAllConvert"
    }

    override fun convert(cursor: Cursor?): Int {
        return if (cursor?.moveToFirst() == true) {
            cursor.getInt(0)
        } else {
            GLog.e(TAG, "convert fail")
            0
        }
    }
}