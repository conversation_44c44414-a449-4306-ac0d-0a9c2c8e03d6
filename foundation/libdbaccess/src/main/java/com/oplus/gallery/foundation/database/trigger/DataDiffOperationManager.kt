/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File       : DataDiffOperationManager.kt
 ** Description: 数据diff操作管理器
 ** Version    : 1.0
 ** Date       : 2024/11/25
 ** Author     : <PERSON><PERSON><PERSON>@Apps.Gallery
 **
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** <PERSON><PERSON><PERSON>@Apps.Gallery        2024/11/25    1.0              init
 *  pusong                     2005/01/09    1.1              add
 *************************************************************************************************/
package com.oplus.gallery.foundation.database.trigger

import com.oplus.gallery.foundation.database.trigger.operator.AlbumDataOperationProcessor
import com.oplus.gallery.foundation.database.trigger.operator.IDataChangeObserver
import com.oplus.gallery.foundation.database.trigger.operator.IDataChangeRegister
import com.oplus.gallery.foundation.database.trigger.operator.ITableOperationProcessor
import com.oplus.gallery.foundation.database.trigger.operator.ObservableDataOperationProcessor
import java.util.concurrent.ConcurrentHashMap

/**
 * 数据diff操作管理器
 * 1. 提供操作处理器。数据变更后通过[getProcessor]接口获取[ITableOperationProcessor]对象进行数据处理
 * 2. 提供数据diff监听器注册。业务启动后，按需通过[registerObserver]接口获取[IDataChangeRegister]注册数据diff监听
 *
 * 一般需要直接处理diff数据的，需要通过此类进行通信，获取diff数据，
 * 否则数据缓存可以直接在此包内部处理好，在异步通知给对应的业务。
 */
object DataDiffOperationManager {
    private val processors: ConcurrentHashMap<DataOperationProcessorType, ObservableDataOperationProcessor> = ConcurrentHashMap()

    /**
     * 获取数据diff的处理器，数据变更后将数据分发出去，让 数据层缓存、业务 进行处理
     *
     * @param processorType 据操作处理器类型
     * @return ITableOperationProcessor 数据库数据操作处理器
     */
    internal fun getProcessor(processorType: DataOperationProcessorType): ITableOperationProcessor {
        return computeIfAbsentProcessor(processorType)
    }

    /**
     * 获取数据diff变更的监听器并注册监听
     * @param processorType 数据操作处理器类型
     * @param observer 监听器
     * @param immediately 是否立即通知
     */
    fun registerObserver(
        processorType: DataOperationProcessorType,
        observer: IDataChangeObserver,
        immediately: Boolean = false
    ) {
        computeIfAbsentProcessor(processorType).dataChangeRegister.registerObserver(observer, immediately)
    }

    /**
     * 获取数据diff变更的监听器并注册监听
     * @param processorType 数据操作处理器类型
     * @param observer 监听器
     * @param immediately 是否立即通知
     */
    fun unRegisterObserver(
        processorType: DataOperationProcessorType,
        observer: IDataChangeObserver,
        immediately: Boolean = false
    ) {
        computeIfAbsentProcessor(processorType).dataChangeRegister.unRegisterObserver(observer, immediately)
    }

    /**
     * 获取或者创建一个Processor
     */
    private fun computeIfAbsentProcessor(processorType: DataOperationProcessorType) = when (processorType) {
        DataOperationProcessorType.ALBUM_MEDIA_DIFF -> processors.computeIfAbsent(processorType) { AlbumDataOperationProcessor() }
        else -> processors.computeIfAbsent(processorType) { createDefaultProcessor(processorType) }
    }

    /**
     * 创建默认的数据diff处理器
     */
    private fun createDefaultProcessor(processorType: DataOperationProcessorType) = ObservableDataOperationProcessor(processorType.tag)
}

/**
 * 数据操作处理器类型声明。用来对不同的数据业务做diff处理。
 *
 * 1. 可以将数据分发给业务处理
 * 2. 可以数据层将数据diff处理后写入其他的数据缓存
 */
enum class DataOperationProcessorType(val tag: String) {
    /**
     * LocalMedia表数据处理器，LocalMedia数据变更后，回调业务diff数据
     */
    LOCAL_MEDIA_DIFF("LocalMediaDiffChangeProcessor"),

    /**
     * 图集表数据处理器，LocalMedia数据变更后，回调diff数据，进行图集缓存刷新
     */
    ALBUM_MEDIA_DIFF("AlbumMediaDiffChangeProcessor"),

    /**
     * 编辑表数据处理器，LocalMedia数据变更后，回调diff数据
     */
    EDIT_MEDIA_DIFF("EditMediaDiffChangeProcessor"),

    /**
     * ocr表数据处理器，ocr内容数据变更后，同步dmp
     */
    OCR_PAGES_DIFF("OcrPagesDiffChangeProcessor"),

    /**
     * label表数据处理器，label内容数据变更后，同步dmp
     */
    SCAN_LABEL_DIFF("ScanLabelDiffChangeProcessor")
}