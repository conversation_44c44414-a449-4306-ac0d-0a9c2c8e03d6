/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - GalleryProvider.java
 ** Description: 相册主数据，存储所有相册需要显示的数据，方便连表查询和管理
 * ----------------------------------------------------------------------------------------------
 ** 升级规范： 数据库版本号和apk版本号必须保证高版本的apk，其数据库版本要高于低版本的apk的数据库版本，保证不会出现版本回退
 *  apk version 7.7，版本号范围 [-, 810)，目前使用的是3D的代码，其DB版本不得超过810
 *  apk version 7.8，版本号范围 [811, 820)，计划使用2D代码，其DB版本不得超过820
 *  apk version 8.0，版本号范围 [821, 830)，计划使用3D代码，其DB版本不得超过830
 *  apk version 8.1，版本号范围 [831, 840)，计划使用2D代码，其DB版本不得超过840
 *  apk version 8.2，版本号范围 [841, 850)，计划使用2D代码，需要覆盖所有分支，需要兼容[7.7, 8.0]所有版本的升级
 *  apk version 12.x 版本号范围 [1201,1299) release12分支使用12xx的版本号
 *  apk version 13.x 版本号范围 [1300,1399) master以及release13分支使用13xx的版本号
 *  apk version 15.x 版本号范围 [1500,1599) master以及release15分支使用15xx的版本号
 *  ----------------------------------------------------------------------------------------------
 *  注意：
 *  1 apk版本号和数据库版本号必须保持：apk版本号大的，其数据库版本号也大。
 *  2 有master和release这种多分支发布的apk，数据库版本升级需要考虑其余分支升级的兼容性问题。
 *  3 设计数据库升级的代码迁移时，要将当前版本及之前所有的数据库升级内容全部移植。
 *
 ** Version: 1.0
 ** Date: 2020/12/30
 ** Author: Dajian@Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 ** biao.chen@Apps.Gallery3D   2020/03/10    1.0              add version 800  init
 ** Dajian@Apps.Gallery3D      2020/12/30    1.1              add version 810
 ** yeguangjin@Apps.Gallery3D  2022/04/26    1.2              add version 1300
 *  dingyong@Apps.Gallery3D    2022/04/27    1.3              add version 1301
 *  zhengyirui@Apps.Gallery3D  2022/05/28    1.3              add version 1302
 *  wuhengze@Apps.Gallery3D    2022/08/24    1.4              add version 1303
 *  dingyong@Apps.Gallery3D    2022/07/29    1.5              add version 1304
 *  xiewujie@Apps.Gallery3D    2022/10/11    1.6              add version 1307
 *  wuhengze@Apps.Gallery3D    2023/03/01    1.7              add version 1318
 *************************************************************************************************/

package com.oplus.gallery.foundation.database.provider;

import static com.oplus.gallery.foundation.database.store.GalleryStore.AUTHORITY;
import static com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.INVALID;
import static com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.INVALID_SURVIVAL_SHORT;
import static com.oplus.gallery.foundation.database.trigger.monitor.ITableSendEventKt.EVENT_FULL_SYNC_ALBUM_END;
import static com.oplus.gallery.foundation.database.trigger.monitor.ITableSendEventKt.EVENT_MIGRATE_ALBUM_END;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.AND;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.DROP_TABLE;
import static com.oplus.gallery.foundation.util.debug.LogFlag.DF;

import android.annotation.SuppressLint;
import android.content.ContentProvider;
import android.content.ContentProviderOperation;
import android.content.ContentProviderResult;
import android.content.ContentResolver;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.content.OperationApplicationException;
import android.content.UriMatcher;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.database.sqlite.SQLiteQueryBuilder;
import android.net.Uri;
import android.os.Bundle;
import android.os.CancellationSignal;
import android.text.TextUtils;
import android.util.Pair;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.oplus.gallery.foundation.database.datacheck.LocalTableColumnVerifier;
import com.oplus.gallery.foundation.database.migrate.MigrateManager;
import com.oplus.gallery.foundation.database.migrate.store.MigrateMediaStore;
import com.oplus.gallery.foundation.database.migrate.table.RecycleMediaTable;
import com.oplus.gallery.foundation.database.notifier.UriNotifier;
import com.oplus.gallery.foundation.database.statistics.StatisticsConstant;
import com.oplus.gallery.foundation.database.store.GalleryStore;
import com.oplus.gallery.foundation.database.store.GalleryStore.ScanMultiModal;
import com.oplus.gallery.foundation.database.table.AlbumHideOnTimeLinePageTable;
import com.oplus.gallery.foundation.database.table.AppendToAlbumTable;
import com.oplus.gallery.foundation.database.table.BaseTable;
import com.oplus.gallery.foundation.database.table.ButtonConfigResourceTable;
import com.oplus.gallery.foundation.database.table.CloudSyncAbandonDataTable;
import com.oplus.gallery.foundation.database.table.DownloadUrlTable;
import com.oplus.gallery.foundation.database.table.EditInfoTable;
import com.oplus.gallery.foundation.database.table.FestivalTable;
import com.oplus.gallery.foundation.database.table.FileInfoTable;
import com.oplus.gallery.foundation.database.table.GeoRouteTable;
import com.oplus.gallery.foundation.database.table.LocalMediaTable;
import com.oplus.gallery.foundation.database.table.LockedPicturesTable;
import com.oplus.gallery.foundation.database.table.MemoriesTable;
import com.oplus.gallery.foundation.database.table.OcrPagesTable;
import com.oplus.gallery.foundation.database.table.OtherAlbumSetTable;
import com.oplus.gallery.foundation.database.table.PersonPetGroupTable;
import com.oplus.gallery.foundation.database.table.PersonalInfoCollectionTable;
import com.oplus.gallery.foundation.database.table.PoiGridTable;
import com.oplus.gallery.foundation.database.table.PetTable;
import com.oplus.gallery.foundation.database.table.PoiGridTable;
import com.oplus.gallery.foundation.database.table.ScanAbortFileTable;
import com.oplus.gallery.foundation.database.table.ScanCaptionTable;
import com.oplus.gallery.foundation.database.table.ScanFaceTable;
import com.oplus.gallery.foundation.database.table.ScanLabelTable;
import com.oplus.gallery.foundation.database.table.ScanMultiModalTable;
import com.oplus.gallery.foundation.database.table.ScanOcrEmbeddingTable;
import com.oplus.gallery.foundation.database.table.ScanTravelTable;
import com.oplus.gallery.foundation.database.table.SceneCrashTable;
import com.oplus.gallery.foundation.database.table.SearchHistoryTable;
import com.oplus.gallery.foundation.database.table.SeniorMediaTable;
import com.oplus.gallery.foundation.database.table.SharedAlbumTable;
import com.oplus.gallery.foundation.database.table.SimilarFeatureTable;
import com.oplus.gallery.foundation.database.table.StudyTable;
import com.oplus.gallery.foundation.database.table.WatermarkMasterResourceTable;
import com.oplus.gallery.foundation.database.table.WidgetTable;
import com.oplus.gallery.foundation.database.trigger.monitor.DmpOcrPagesContentMonitor;
import com.oplus.gallery.foundation.database.trigger.monitor.DmpScanLabelMonitor;
import com.oplus.gallery.foundation.database.trigger.monitor.ITableMonitor;
import com.oplus.gallery.foundation.database.trigger.monitor.ITableSendEvent;
import com.oplus.gallery.foundation.database.trigger.monitor.LocalMediaMonitor;
import com.oplus.gallery.foundation.database.trigger.monitor.TableEvent;
import com.oplus.gallery.foundation.database.util.DBLogHelper;
import com.oplus.gallery.foundation.database.util.DatabaseUtils;
import com.oplus.gallery.foundation.database.util.DistinctUtils;
import com.oplus.gallery.foundation.database.util.LocalInsertStateUtils;
import com.oplus.gallery.foundation.database.util.SensitiveFieldsConverter;
import com.oplus.gallery.foundation.database.util.simplifier.AccessSqlSimplifier;
import com.oplus.gallery.foundation.security.EncryptUtils;
import com.oplus.gallery.foundation.util.BuildConfig;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.GProperty;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.standard_lib.app.AppScope;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import kotlin.coroutines.CoroutineContext;
import kotlinx.coroutines.BuildersKt;
import kotlinx.coroutines.CoroutineStart;
import kotlinx.coroutines.Dispatchers;

public class GalleryProvider extends ContentProvider {
    public static final int VERSION_800 = 800;
    public static final int VERSION_801 = 801;
    public static final int VERSION_802 = 802;
    /**
     * 2D版本规划版本号为7.8，将数据库升级到810，预留部分版本号给3D 7.7版本使用
     * local_media增加 scan_error字段，标记此文件读写Exif失败情况，避免相册的数据被媒体库错误数据覆盖
     * memories_setmap增加触发器，在 is_recycled 变化时刷新 memories_set.num_of_meta(当前回忆中的图片数量)
     */
    public static final int VERSION_810 = 810;
    public static final int VERSION_841 = 841;
    /**
     * 需求需求629488 【智能场景照片移出】scan_label/scan_label_backup增加is_manual字段
     */
    public static final int VERSION_842 = 842;
    /**
     * 需求960463：breakpad改造，新增scene_crash表
     */
    public static final int VERSION_1201 = 1201;
    /**
     * 需求1467548：随身卡包，local_media、recycle_media表 增加 additional_card_type 字段
     */
    public static final int VERSION_1202 = 1202;
    /**
     * 需求2209691：添加similar_feature表和local_media表添加quality_score字段，用于存储相似图算法的图像特征及质量美学算法的图片得分
     * 需求1414468: 精选画廊，添加senior_media 表
     */
    public static final int VERSION_1203 = 1203;
    /**
     * 需求：桌面卡片，新增widget_set、widget_display_list表
     */
    public static final int VERSION_1204 = 1204;
    /**
     * 需求：桌面卡片，widget_set、widget_display_list调整
     */
    public static final int VERSION_1205 = 1205;
    /**
     * 需求：智能裁剪，新增字段:
     * crop_rect 裁剪区域数据
     * crop_rect_version 裁剪的版本号，更新算法时加1可触发重扫
     */
    public static final int VERSION_1300 = 1300;

    /**
     * CloudKit 接入
     */
    public static final int VERSION_1302 = 1302;

    /**
     * 增加两个扩展字段，一个字段记录标签，一个字段记录扫描状态
     */
    public static final int VERSION_1303 = 1303;

    /**
     * 杜比视界,添加[GalleryStore.CODEC_TYPE]字段，记录媒体的编码类型
     */
    public static final int VERSION_1305 = 1305;

    /**
     * 共享图集
     */
    public static final int VERSION_1306 = 1306;

    /**
     * 超级文本2.0在13.0使用tagflags(1<<16，1<<17)，master上不符合定义被优化掉了，有留存数据，
     * 后续人像景深需求使用了tagflags（1<<16）产生冲突,需要升级数据库版本让其重新解析tagFlags
     */
    public static final int VERSION_1307 = 1307;

    /**
     * senior_media增加[SSA_QUALITY_SCORE][SSA_QUALITY_SCORE_VERSION][SENIOR_RESULT][SENIOR_RESULT_VERSION][SENIOR_TOP_LABEL_ID]
     * similar_feature 增加[feature_version]
     */
    public static final int VERSION_1308 = 1308;

    /**
     * 节日表festival增加filterType字段，表示日期的过滤类型，int型，1表示精选日
     */
    public static final int VERSION_1319 = 1319;

    /**
     * 共享图集增加年月日字段的触发器
     */
    public static final int VERSION_1320 = 1320;

    /**
     * 端云数据融合，将cloud_media和recycle_media的字段搬到local_media。
     */
    public static final int VERSION_1321 = 1321;

    /**
     * 多模态搜索[图文检索SDK]
     */
    public static final int VERSION_1322 = 1322;

    /**
     * 删除速度优化<p>
     *
     * - 新建对DATE_MODIFIED字段的索引<p>
     * 云同步性能同步优化需求之后，由于media id可能为0，多使用_id进行排序因此<p>
     * - date_modified DESC ,media_id DESC索引改为 date_modified DESC ,_id DESC<p>
     * - datetaken DESC ,media_id DESC 改为 datetaken DESC ,_id DESC
     */
    public static final int VERSION_1323 = 1323;

    /**
     * SCAN_FACE_VIEW表增加locale_id字段
     * 为了解决personAlbum中排序不一致的问题，（在dataTaken相同时，有的用local_media._id排序，有的用scan_face._id排序）
     * 在SCAN_FACE_VIEW表增加locale_id字段，这个字段的值的来源是local_media._id，这样就可以都指定以这个作为排序了
     */
    public static final int VERSION_1324 = 1324;

    /**
     * 增加 EditInfo 表
     */
    public static final int VERSION_1500 = 1500;

    /**
     * 新增监听LocalMedia表的触发器来更新图集表、图集数量表
     */
    public static final int VERSION_1325 = 1325;

    /**
     * 更新监听LocalMedia表的触发器(增加generation_modified字段)来更新图集表、图集数量表
     */
    public static final int VERSION_1501 = 1501;

    /**
     * 搬家插入精彩回忆的数据时，触发器[num_of_meta_add]的触发条件未判断数据是否未被删除[is_recycled = 0]。
     * 最终导致搬家后数据异常，在精彩回忆中出现空白的回忆图集。
     */
    public static final int VERSION_1502 = 1502;

    /**
     * 人物图集关联到了media id<=0的数据，需要更改视图SQL，过滤掉media id为0的数据
     * 最终导致搬家后数据异常，在精彩回忆中出现空白的回忆图集。
     */
    public static final int VERSION_1503 = 1503;

    /**
     * 增加ocr_pages_v2表，在ocr_pages表的基础上增加model_version字段
     */
    public static final int VERSION_1504 = 1504;

    /**
     * 修正编辑覆盖原图后无法把删除操作同步到云端的原图记录
     */
    public static final int VERSION_1505 = 1505;

    /**
     * 新增个人信息清单收集情况数据表
     */
    public static final int VERSION_1506 = 1506;

    /**
     * 新增回忆相关的触发器，文件路径修改后修正回忆关联的路径数据
     */
    public static final int VERSION_1507 = 1507;

    /**
     * 新增水印大师资源信息数据表
     */
    public static final int VERSION_1508 = 1508;

    /**
     * 修正回忆相关触发器错误
     */
    public static final int VERSION_1509 = 1509;

    /**
     * 数据变更后，将diff通知给图集缓存，更新缓存后再通知业务去缓存获取数据，减少数据库查询。
     * <p>
     * 触发器修改点：
     * 1、增加了监视字段，拓展到可以覆盖MediaItem
     * 2、修改了分隔符，从":"改为"::"，避免和数据库中的字段值冲突
     * <p>
     * 注意：
     * [LocalMediaMonitor]中监听了LocalMedia表的diff，如果修改了数据库字段影响MediaItem，需要同步修改[LocalMediaMonitor.PROJECTIONS]
     */
    public static final int VERSION_1510 = 1510;

    /**
     * 水印大师资源增加pop window 引导半幅弹框数据
     */
    public static final int VERSION_1511 = 1511;
    /**
     * 增加ocr_emdedding表
     */
    public static final int VERSION_1512 = 1512;

    /**
     * 增加OCR_PAGES_V2和SCAN_LABEL表触发器，同步数据到dmp
     */
    public static final int VERSION_1513 = 1513;

    /**
     * 限定水印按键增加TIPS_ALREADY_SHOW字段，记录角标显示状态
     */
    public static final int VERSION_1514 = 1514;

    /**
     * release分支上数据库版本已经是1514了，与当前的master数据库版本相同
     * 但是master上还多了一个需求[优化相机图集加载速度，升级了版本号，见VERSION_1510的注释说明]
     * 因此master版本号需要升级
     */
    public static final int VERSION_1515 = 1515;

    /**
     * 超清导出增加 extra_msg 记录文件额外数据
     * 4K120Fps也利用此字段存储视频帧率信息
     */
    public static final int VERSION_1516 = 1516;

    /**
     * 增加poi_grid_route数据库
     */
    public static final int VERSION_1517 = 1517;

    /**
     * 增加触发器监听字段relative_path
     */
    public static final int VERSION_1518 = 1518;

    /**
     * 表情优化功能需要在scan_face表中新增字段
     */
    public static final int VERSION_1519 = 1519;

    /**
     * 增加触发器监听字段relative_path，同1518一样的目的，刷新LocalMediaMonitor中的触发器。<p>
     * 引入原因：<p>
     * zhuque分支1519比1518先发布，数据库版本从1517跳到1519，1518后合入，没有触发数据库升级，导致触发器没有更新注册，故这里重新加版本号再触发一次
     */
    public static final int VERSION_1520 = 1520;


    /**
     * 增加不显示在照片页的图集的数据库表 album_hide_on_time_line_page
     */
    public static final int VERSION_1521 = VERSION_1520 + 1;
    /**
     * 云同步端云一致性新增表升级数据库
     */
    public static final int VERSION_1522 = VERSION_1521 + 1;

    /**
     * 修改引入数据库升级失败，问题修复后再升级一下数据库版本，解决用户问题
     */
    public static final int VERSION_1523 = VERSION_1522 + 1;
    /**
     * 主题分类
     * 1.创建宠物、人宠群组的数据表
     * 2.创建旅行图集数据表
     * 3.创建学习图集数据表
     */
    public static final int VERSION_1524 = VERSION_1523 + 1;

    /**
     * 旅程图集数据表更新
     */
    public static final int VERSION_1525 = VERSION_1524 + 1;

    /**
     * 升级添加人宠合照表的字段
     */
    public static final int VERSION_1526 = VERSION_1525 + 1;

    /**
     * 增加scan_caption表，存储caption扫描数据
     * poi_grid_route 新增模型版本列
     */
    public static final int VERSION_1527 = VERSION_1526 + 1;

    /**
     * 图集表监听local表的触发器中,缺少字段LocalColumns.GENERATION_MODIFIED,需要补充回来
     */
    public static final int VERSION_1528 = VERSION_1527 + 1;

    public static final int DATABASE_VERSION = VERSION_1528;

    public static final int NOTIFY_NO_DELAY = 1 << 15;

    private static final String TAG = DatabaseUtils.DATA_TAG + "GalleryProvider";
    private static final String LOG_APPEND_COST = ", cost:";
    private static final UriMatcher URI_MATCHER = new UriMatcher(UriMatcher.NO_MATCH);
    private static final int GALLERY_MEDIA = 1;
    private static final int GALLERY_MEDIA_ID = GALLERY_MEDIA + 1;
    private static final int LOCAL_MEDIA = GALLERY_MEDIA_ID + 1;
    private static final int LOCAL_MEDIA_ID = LOCAL_MEDIA + 1;
    private static final int RECYCLE_MEDIA = LOCAL_MEDIA_ID + 1;
    private static final int RECYCLE_MEDIA_ID = RECYCLE_MEDIA + 1;
    private static final int CLOUD_MEDIA = RECYCLE_MEDIA_ID + 1;
    private static final int CLOUD_MEDIA_ID = CLOUD_MEDIA + 1;

    private static final int MEMORIES_SET = CLOUD_MEDIA_ID + 1;
    private static final int MEMORIES_SET_ID = MEMORIES_SET + 1;
    private static final int MEMORIES_SETMAP = MEMORIES_SET_ID + 1;
    private static final int MEMORIES_SETMAP_ID = MEMORIES_SETMAP + 1;
    private static final int MEMORIES_SETMAP_VIEW = MEMORIES_SETMAP_ID + 1;
    private static final int MEMORIES_SETMAP_VIEW_ID = MEMORIES_SETMAP_VIEW + 1;

    private static final int SCAN_FACE = MEMORIES_SETMAP_VIEW_ID + 1;
    private static final int SCAN_FACE_ID = SCAN_FACE + 1;
    private static final int SCAN_FACE_BACKUP = SCAN_FACE_ID + 1;
    private static final int SCAN_FACE_BACKUP_ID = SCAN_FACE_BACKUP + 1;
    private static final int SCAN_FACE_VIEW = SCAN_FACE_BACKUP_ID + 1;
    private static final int SCAN_FACE_VIEW_ID = SCAN_FACE_VIEW + 1;
    private static final int SCAN_FACE_GROUP_VIEW = SCAN_FACE_VIEW_ID + 1;
    private static final int SCAN_FACE_GROUP_VIEW_ID = SCAN_FACE_GROUP_VIEW + 1;
    private static final int SCAN_LABEL = SCAN_FACE_GROUP_VIEW_ID + 1;
    private static final int SCAN_LABEL_ID = SCAN_LABEL + 1;
    private static final int SCAN_LABEL_BACKUP = SCAN_LABEL_ID + 1;
    private static final int SCAN_LABEL_BACKUP_ID = SCAN_LABEL_BACKUP + 1;
    private static final int SCAN_ABORT_FILE = SCAN_LABEL_BACKUP_ID + 1;
    private static final int SCAN_ABORT_FILE_ID = SCAN_ABORT_FILE + 1;

    private static final int PET = SCAN_ABORT_FILE_ID + 1;
    private static final int PET_ID = PET + 1;
    private static final int PET_VIEW = PET_ID + 1;
    private static final int PET_VIEW_ID = PET_VIEW + 1;
    private static final int PERSON_PET_LIST_VIEW = PET_VIEW_ID + 1;
    private static final int PERSON_PET_LIST_VIEW_ID = PERSON_PET_LIST_VIEW + 1;
    private static final int PERSON_PET_GROUP = PERSON_PET_LIST_VIEW_ID + 1;
    private static final int PERSON_PET_GROUP_ID = PERSON_PET_GROUP + 1;
    private static final int PERSON_PET_GROUP_DETAIL_VIEW = PERSON_PET_GROUP_ID + 1;
    private static final int PERSON_PET_GROUP_DETAIL_VIEW_ID = PERSON_PET_GROUP_DETAIL_VIEW + 1;
    private static final int PERSON_PET_GROUP_LIST_VIEW = PERSON_PET_GROUP_DETAIL_VIEW_ID + 1;
    private static final int PERSON_PET_GROUP_LIST_VIEW_ID = PERSON_PET_GROUP_LIST_VIEW + 1;

    private static final int GEO_ROUTE = PERSON_PET_GROUP_LIST_VIEW_ID + 1;
    private static final int GEO_ROUTE_ID = GEO_ROUTE + 1;
    private static final int APPEND_TO_ALBUM = GEO_ROUTE_ID + 1;
    private static final int APPEND_TO_ALBUM_ID = APPEND_TO_ALBUM + 1;
    private static final int OCR_PAGES = APPEND_TO_ALBUM_ID + 1;
    private static final int OCR_PAGES_ID = OCR_PAGES + 1;
    private static final int OTHER_ALBUM_SET = OCR_PAGES_ID + 1;
    private static final int OTHER_ALBUM_SET_ID = OTHER_ALBUM_SET + 1;
    private static final int LOCKED_PICTURES = OTHER_ALBUM_SET_ID + 1;
    private static final int LOCKED_PICTURES_ID = LOCKED_PICTURES + 1;
    private static final int SEARCH_HISTORY = LOCKED_PICTURES_ID + 1;
    private static final int SEARCH_HISTORY_ID = SEARCH_HISTORY + 1;
    private static final int DOWNLOAD_URL = SEARCH_HISTORY_ID + 1;
    private static final int DOWNLOAD_URL_ID = DOWNLOAD_URL + 1;
    private static final int FESTIVAL = DOWNLOAD_URL_ID + 1;
    private static final int FESTIVAL_ID = FESTIVAL + 1;
    private static final int SIMILAR_FEATURE = FESTIVAL_ID + 1;
    private static final int SIMILAR_FEATURE_ID = SIMILAR_FEATURE + 1;
    private static final int SENIOR_MEDIA = SIMILAR_FEATURE_ID + 1;
    private static final int SENIOR_MEDIA_ID = SENIOR_MEDIA + 1;
    /*
    add for raw query
    */
    private static final int RAW_QUERY = SENIOR_MEDIA_ID + 1;
    private static final int SCENE_CRASH = RAW_QUERY + 1;
    private static final int SCENE_CRASH_ID = SCENE_CRASH + 1;
    private static final int WIDGET_SET = SCENE_CRASH_ID + 1;
    private static final int WIDGET_SET_ID = WIDGET_SET + 1;
    private static final int WIDGET_DISPLAY_LIST = WIDGET_SET_ID + 1;
    private static final int WIDGET_DISPLAY_LIST_ID = WIDGET_DISPLAY_LIST + 1;
    private static final int FILE_INFO = WIDGET_DISPLAY_LIST_ID + 1;
    private static final int FILE_INFO_ID = FILE_INFO + 1;
    private static final int SHARED_ALBUM = FILE_INFO_ID + 1;
    private static final int SHARED_ALBUM_ID = SHARED_ALBUM + 1;
    private static final int SHARED_MEDIA = SHARED_ALBUM_ID + 1;
    private static final int SHARED_MEDIA_ID = SHARED_MEDIA + 1;
    private static final int SHARED_INVITE = SHARED_MEDIA_ID + 1;
    private static final int SHARED_INVITE_ID = SHARED_INVITE + 1;
    private static final int HIGHLIGHT = SHARED_INVITE_ID + 1;
    private static final int HIGHLIGHT_ID = HIGHLIGHT + 1;
    private static final int OLD_CLOUD_MEDIA = HIGHLIGHT_ID + 1;
    private static final int FILTER_LOCAL_VIEW = OLD_CLOUD_MEDIA + 1;
    private static final int FILTER_LOCAL_VIEW_ID = FILTER_LOCAL_VIEW + 1;
    private static final int SCAN_MULTI_MODAL = FILTER_LOCAL_VIEW_ID + 1;
    private static final int SCAN_MULTI_MODAL_ID = SCAN_MULTI_MODAL + 1;
    private static final int EDIT_INFO = SCAN_MULTI_MODAL_ID + 1;
    private static final int EDIT_INFO_ID = EDIT_INFO + 1;
    private static final int PERSONAL_INFO_COLLECTION = EDIT_INFO_ID + 1;
    private static final int WATERMARK_MASTER_RESOURCE = PERSONAL_INFO_COLLECTION + 1;
    private static final int BUTTON_CONFIG_RESOURCE = WATERMARK_MASTER_RESOURCE + 1;
    private static final int SCAN_OCR_EMBEDDING = BUTTON_CONFIG_RESOURCE + 1;
    private static final int SCAN_OCR_EMBEDDING_ID = SCAN_OCR_EMBEDDING + 1;
    private static final int SCAN_TRAVEL = SCAN_OCR_EMBEDDING_ID + 1;
    private static final int SCAN_TRAVEL_ID = SCAN_TRAVEL + 1;
    private static final int SCAN_TRAVEL_VIEW = SCAN_TRAVEL_ID + 1;
    private static final int SCAN_TRAVEL_VIEW_ID = SCAN_TRAVEL_VIEW + 1;
    private static final int POI_GRID_ROUTE = SCAN_TRAVEL_VIEW_ID + 1;
    private static final int POI_GRID_ROUTE_ID = POI_GRID_ROUTE + 1;
    private static final int ALBUM_HIDE_ON_TIME_LINE_PAGE = POI_GRID_ROUTE_ID + 1;
    private static final int ALBUM_HIDE_ON_TIME_LINE_PAGE_ID = ALBUM_HIDE_ON_TIME_LINE_PAGE + 1;
    private static final int SCAN_STUDY = ALBUM_HIDE_ON_TIME_LINE_PAGE_ID + 1;
    private static final int SCAN_STUDY_ID = SCAN_STUDY + 1;
    private static final int CLOUD_SYNC_ABANDON_DATA = SCAN_STUDY_ID + 1;
    private static final int CLOUD_SYNC_ABANDON_DATA_ID = CLOUD_SYNC_ABANDON_DATA + 1;
    private static final int SCAN_CAPTION = CLOUD_SYNC_ABANDON_DATA_ID + 1;
    private static final int SCAN_CAPTION_ID = SCAN_CAPTION + 1;

    /**
     * 记录批量插入数据的异常
     */
    private static String sBulkInsertError = "";

    private DatabaseHelper mDatabase;

    static {
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.GalleryMedia.TAB, GALLERY_MEDIA);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.GalleryMedia.TAB + "/#", GALLERY_MEDIA_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.GalleryMedia.LOCAL_TAB_INTERNAL, LOCAL_MEDIA);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.GalleryMedia.LOCAL_TAB_INTERNAL + "/#", LOCAL_MEDIA_ID);
        URI_MATCHER.addURI(AUTHORITY, MigrateMediaStore.RecycleMedia.TAB, RECYCLE_MEDIA);
        URI_MATCHER.addURI(AUTHORITY, MigrateMediaStore.RecycleMedia.TAB + "/#", RECYCLE_MEDIA_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.GalleryMedia.CLOUD_TAB_INTERNAL, CLOUD_MEDIA);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.GalleryMedia.CLOUD_TAB_INTERNAL + "/#", CLOUD_MEDIA_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.MemoriesSet.TAB, MEMORIES_SET);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.MemoriesSet.TAB + "/#", MEMORIES_SET_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.MemoriesSetmap.TAB, MEMORIES_SETMAP);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.MemoriesSetmap.TAB + "/#", MEMORIES_SETMAP_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.MemoriesSetmapView.TAB, MEMORIES_SETMAP_VIEW);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.MemoriesSetmapView.TAB + "/#", MEMORIES_SETMAP_VIEW_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.ScanFace.TAB, SCAN_FACE);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.ScanFace.TAB + "/#", SCAN_FACE_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.ScanFaceBackup.TAB, SCAN_FACE_BACKUP);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.ScanFaceBackup.TAB + "/#", SCAN_FACE_BACKUP_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.ScanFaceView.TAB, SCAN_FACE_VIEW);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.ScanFaceView.TAB + "/#", SCAN_FACE_VIEW_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.ScanFaceGroupView.TAB, SCAN_FACE_GROUP_VIEW);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.ScanFaceGroupView.TAB + "/#", SCAN_FACE_GROUP_VIEW_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.ScanLabel.TAB, SCAN_LABEL);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.ScanLabel.TAB + "/#", SCAN_LABEL_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.ScanLabelBackup.TAB, SCAN_LABEL_BACKUP);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.ScanLabelBackup.TAB + "/#", SCAN_LABEL_BACKUP_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.ScanAbortFile.TAB, SCAN_ABORT_FILE);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.ScanAbortFile.TAB + "/#", SCAN_ABORT_FILE_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.Pet.TAB, PET);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.Pet.TAB + "/#", PET_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.PetView.TAB, PET_VIEW);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.PetView.TAB + "/#", PET_VIEW_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.PersonPetListView.TAB, PERSON_PET_LIST_VIEW);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.PersonPetListView.TAB + "/#", PERSON_PET_LIST_VIEW_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.PersonPetGroup.TAB, PERSON_PET_GROUP);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.PersonPetGroup.TAB + "/#", PERSON_PET_GROUP_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.PersonPetGroupDetailView.TAB, PERSON_PET_GROUP_DETAIL_VIEW);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.PersonPetGroupDetailView.TAB + "/#", PERSON_PET_GROUP_DETAIL_VIEW_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.PersonPetGroupListView.TAB, PERSON_PET_GROUP_LIST_VIEW);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.PersonPetGroupListView.TAB + "/#", PERSON_PET_GROUP_LIST_VIEW_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.GeoRoute.TAB, GEO_ROUTE);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.GeoRoute.TAB + "/#", GEO_ROUTE_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.AppendToAlbum.TAB, APPEND_TO_ALBUM);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.AppendToAlbum.TAB + "/#", APPEND_TO_ALBUM_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.OcrPages.TAB, OCR_PAGES);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.OcrPages.TAB + "/#", OCR_PAGES_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.OtherAlbumSet.TAB, OTHER_ALBUM_SET);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.OtherAlbumSet.TAB + "/#", OTHER_ALBUM_SET_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.LockedPictures.TAB, LOCKED_PICTURES);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.LockedPictures.TAB + "/#", LOCKED_PICTURES_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.SearchHistory.TAB, SEARCH_HISTORY);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.SearchHistory.TAB + "/#", SEARCH_HISTORY_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.DownloadUrl.TAB, DOWNLOAD_URL);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.DownloadUrl.TAB + "/#", DOWNLOAD_URL_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.Festival.TAB, FESTIVAL);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.Festival.TAB + "/#", FESTIVAL_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.SimilarFeature.TAB, SIMILAR_FEATURE);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.SimilarFeature.TAB + "/#", SIMILAR_FEATURE_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.SeniorMedia.TAB, SENIOR_MEDIA);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.SeniorMedia.TAB + "/#", SENIOR_MEDIA_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.RawQuery.TAB, RAW_QUERY);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.SceneCrash.TAB, SCENE_CRASH);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.SceneCrash.TAB + "/#", SCENE_CRASH_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.WidgetSet.TAB, WIDGET_SET);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.WidgetSet.TAB + "/#", WIDGET_SET_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.WidgetDisplayList.TAB, WIDGET_DISPLAY_LIST);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.WidgetDisplayList.TAB + "/#", WIDGET_DISPLAY_LIST_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.FileInfo.TAB, FILE_INFO);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.FileInfo.TAB + "/#", FILE_INFO_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.SharedAlbum.TAB, SHARED_ALBUM);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.SharedAlbum.TAB + "/#", SHARED_ALBUM_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.SharedMedia.TAB, SHARED_MEDIA);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.SharedMedia.TAB + "/#", SHARED_MEDIA_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.SharedInvite.TAB, SHARED_INVITE);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.SharedInvite.TAB + "/#", SHARED_INVITE_ID);
        URI_MATCHER.addURI(AUTHORITY, MigrateMediaStore.CloudMedia.TAB, OLD_CLOUD_MEDIA);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.GalleryMedia.FILTER_LOCAL_VIEW, FILTER_LOCAL_VIEW);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.GalleryMedia.FILTER_LOCAL_VIEW + "/#", FILTER_LOCAL_VIEW_ID);
        URI_MATCHER.addURI(AUTHORITY, ScanMultiModal.TAB, SCAN_MULTI_MODAL);
        URI_MATCHER.addURI(AUTHORITY, ScanMultiModal.TAB + "/#", SCAN_MULTI_MODAL_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.EditInfo.TAB, EDIT_INFO);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.EditInfo.TAB + "/#", EDIT_INFO_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.PersonalInfoCollection.TAB, PERSONAL_INFO_COLLECTION);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.WatermarkMasterResource.TAB, WATERMARK_MASTER_RESOURCE);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.ButtonConfigResource.TAB, BUTTON_CONFIG_RESOURCE);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.ScanOcrEmbedding.TAB, SCAN_OCR_EMBEDDING);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.ScanOcrEmbedding.TAB + "/#", SCAN_OCR_EMBEDDING_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.ScanTravel.TAB, SCAN_TRAVEL);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.ScanTravel.TAB + "/#", SCAN_TRAVEL_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.ScanTravelView.TAB, SCAN_TRAVEL_VIEW);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.ScanTravelView.TAB + "/#", SCAN_TRAVEL_VIEW_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.PoiGridRoute.TAB, POI_GRID_ROUTE);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.PoiGridRoute.TAB + "/#", POI_GRID_ROUTE_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.AlbumHideOnTimeLinePage.TAB, ALBUM_HIDE_ON_TIME_LINE_PAGE);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.AlbumHideOnTimeLinePage.TAB + "/#", ALBUM_HIDE_ON_TIME_LINE_PAGE_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.ScanStudy.TAB, SCAN_STUDY);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.ScanStudy.TAB + "/#", SCAN_STUDY_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.CloudSyncAbandonData.TAB, CLOUD_SYNC_ABANDON_DATA);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.CloudSyncAbandonData.TAB + "/#", CLOUD_SYNC_ABANDON_DATA_ID);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.ScanCaption.TAB, SCAN_CAPTION);
        URI_MATCHER.addURI(AUTHORITY, GalleryStore.ScanCaption.TAB + "/#", SCAN_CAPTION_ID);
    }

    private String getMatchTable(Uri uri) {
        return getMatchTable(uri, false);
    }

    private String getMatchTable(Uri uri, boolean onlyQuery) {
        int match = URI_MATCHER.match(uri);
        switch (match) {
            case GALLERY_MEDIA:
            case GALLERY_MEDIA_ID:
            case LOCAL_MEDIA:
            case LOCAL_MEDIA_ID:
            case CLOUD_MEDIA:
            case CLOUD_MEDIA_ID:
                // 端云融合后查询local，cloud 都使用local_media
                return GalleryStore.GalleryMedia.TAB;
            case RECYCLE_MEDIA:
            case RECYCLE_MEDIA_ID:
                return MigrateMediaStore.RecycleMedia.TAB;
            case MEMORIES_SET:
            case MEMORIES_SET_ID:
                return GalleryStore.MemoriesSet.TAB;
            case MEMORIES_SETMAP:
            case MEMORIES_SETMAP_ID:
                return GalleryStore.MemoriesSetmap.TAB;
            case SCAN_FACE:
            case SCAN_FACE_ID:
                return GalleryStore.ScanFace.TAB;
            case SCAN_FACE_BACKUP:
            case SCAN_FACE_BACKUP_ID:
                return GalleryStore.ScanFaceBackup.TAB;
            case SCAN_LABEL:
            case SCAN_LABEL_ID:
                return GalleryStore.ScanLabel.TAB;
            case SCAN_LABEL_BACKUP:
            case SCAN_LABEL_BACKUP_ID:
                return GalleryStore.ScanLabelBackup.TAB;
            case SCAN_ABORT_FILE:
            case SCAN_ABORT_FILE_ID:
                return GalleryStore.ScanAbortFile.TAB;
            case PET:
            case PET_ID:
                return GalleryStore.Pet.TAB;
            case PET_VIEW:
            case PET_VIEW_ID:
                return GalleryStore.PetView.TAB;
            case PERSON_PET_LIST_VIEW:
            case PERSON_PET_LIST_VIEW_ID:
                return GalleryStore.PersonPetListView.TAB;
            case PERSON_PET_GROUP:
            case PERSON_PET_GROUP_ID:
                return GalleryStore.PersonPetGroup.TAB;
            case PERSON_PET_GROUP_DETAIL_VIEW:
            case PERSON_PET_GROUP_DETAIL_VIEW_ID:
                return GalleryStore.PersonPetGroupDetailView.TAB;
            case PERSON_PET_GROUP_LIST_VIEW:
            case PERSON_PET_GROUP_LIST_VIEW_ID:
                return GalleryStore.PersonPetGroupListView.TAB;
            case GEO_ROUTE:
            case GEO_ROUTE_ID:
                return GalleryStore.GeoRoute.TAB;
            case APPEND_TO_ALBUM:
            case APPEND_TO_ALBUM_ID:
                return GalleryStore.AppendToAlbum.TAB;
            case OCR_PAGES:
            case OCR_PAGES_ID:
                return GalleryStore.OcrPages.TAB;
            case OTHER_ALBUM_SET:
            case OTHER_ALBUM_SET_ID:
                return GalleryStore.OtherAlbumSet.TAB;
            case LOCKED_PICTURES:
            case LOCKED_PICTURES_ID:
                return GalleryStore.LockedPictures.TAB;
            case SEARCH_HISTORY:
            case SEARCH_HISTORY_ID:
                return GalleryStore.SearchHistory.TAB;
            case DOWNLOAD_URL:
            case DOWNLOAD_URL_ID:
                return GalleryStore.DownloadUrl.TAB;
            case FESTIVAL:
            case FESTIVAL_ID:
                return GalleryStore.Festival.TAB;
            case SIMILAR_FEATURE:
            case SIMILAR_FEATURE_ID:
                return GalleryStore.SimilarFeature.TAB;
            case SENIOR_MEDIA:
            case SENIOR_MEDIA_ID:
                return GalleryStore.SeniorMedia.TAB;
            case SCENE_CRASH:
            case SCENE_CRASH_ID:
                return GalleryStore.SceneCrash.TAB;
            case WIDGET_SET:
            case WIDGET_SET_ID:
                return GalleryStore.WidgetSet.TAB;
            case WIDGET_DISPLAY_LIST:
            case WIDGET_DISPLAY_LIST_ID:
                return GalleryStore.WidgetDisplayList.TAB;
            case FILE_INFO:
            case FILE_INFO_ID:
                return GalleryStore.FileInfo.TAB;
            case SHARED_ALBUM:
            case SHARED_ALBUM_ID:
                return GalleryStore.SharedAlbum.TAB;
            case SHARED_MEDIA:
            case SHARED_MEDIA_ID:
                return GalleryStore.SharedMedia.TAB;
            case SHARED_INVITE:
            case SHARED_INVITE_ID:
                return GalleryStore.SharedInvite.TAB;
            case OLD_CLOUD_MEDIA:
                return MigrateMediaStore.CloudMedia.TAB;
            case SCAN_MULTI_MODAL:
            case SCAN_MULTI_MODAL_ID:
                return ScanMultiModal.TAB;
            case EDIT_INFO:
            case EDIT_INFO_ID:
                return GalleryStore.EditInfo.TAB;
            case WATERMARK_MASTER_RESOURCE:
                return GalleryStore.WatermarkMasterResource.TAB;
            case PERSONAL_INFO_COLLECTION:
                return GalleryStore.PersonalInfoCollection.TAB;
            case BUTTON_CONFIG_RESOURCE:
                return GalleryStore.ButtonConfigResource.TAB;
            case SCAN_OCR_EMBEDDING:
            case SCAN_OCR_EMBEDDING_ID:
                return GalleryStore.ScanOcrEmbedding.TAB;
            case SCAN_TRAVEL:
            case SCAN_TRAVEL_ID:
                return GalleryStore.ScanTravel.TAB;
            case SCAN_TRAVEL_VIEW:
            case SCAN_TRAVEL_VIEW_ID:
                return GalleryStore.ScanTravelView.TAB;
            case POI_GRID_ROUTE:
            case POI_GRID_ROUTE_ID:
                return GalleryStore.PoiGridRoute.TAB;
            case ALBUM_HIDE_ON_TIME_LINE_PAGE:
            case ALBUM_HIDE_ON_TIME_LINE_PAGE_ID:
                return GalleryStore.AlbumHideOnTimeLinePage.TAB;
            case SCAN_STUDY:
            case SCAN_STUDY_ID:
                return GalleryStore.ScanStudy.TAB;
            case CLOUD_SYNC_ABANDON_DATA:
            case CLOUD_SYNC_ABANDON_DATA_ID:
                return GalleryStore.CloudSyncAbandonData.TAB;
            case SCAN_CAPTION:
            case SCAN_CAPTION_ID:
                return GalleryStore.ScanCaption.TAB;
            default:
                break;
        }
        if (onlyQuery) {
            switch (match) {
                case MEMORIES_SETMAP_VIEW:
                case MEMORIES_SETMAP_VIEW_ID:
                    return GalleryStore.MemoriesSetmapView.TAB;
                case SCAN_FACE_VIEW:
                case SCAN_FACE_VIEW_ID:
                    return GalleryStore.ScanFaceView.TAB;
                case SCAN_FACE_GROUP_VIEW:
                case SCAN_FACE_GROUP_VIEW_ID:
                    return GalleryStore.ScanFaceGroupView.TAB;
                case FILTER_LOCAL_VIEW:
                case FILTER_LOCAL_VIEW_ID:
                    return GalleryStore.GalleryMedia.FILTER_LOCAL_VIEW;
                default:
                    break;
            }
        }

        return null;
    }

    private boolean hasMatchWhere(Uri uri) {
        boolean hasMatch = false;
        int match = URI_MATCHER.match(uri);
        switch (match) {
            case LOCAL_MEDIA_ID:
            case RECYCLE_MEDIA_ID:
            case CLOUD_MEDIA_ID:
            case MEMORIES_SET_ID:
            case MEMORIES_SETMAP_ID:
            case MEMORIES_SETMAP_VIEW_ID:
            case SCAN_FACE_ID:
            case SCAN_FACE_BACKUP_ID:
            case SCAN_FACE_VIEW_ID:
            case SCAN_FACE_GROUP_VIEW_ID:
            case SCAN_LABEL_ID:
            case SCAN_LABEL_BACKUP_ID:
            case SCAN_ABORT_FILE_ID:
            case GEO_ROUTE_ID:
            case APPEND_TO_ALBUM_ID:
            case OCR_PAGES_ID:
            case OTHER_ALBUM_SET_ID:
            case LOCKED_PICTURES_ID:
            case SEARCH_HISTORY_ID:
            case DOWNLOAD_URL_ID:
            case FESTIVAL_ID:
            case SCENE_CRASH_ID:
            case SHARED_ALBUM_ID:
            case SHARED_MEDIA_ID:
            case SHARED_INVITE_ID:
            case FILTER_LOCAL_VIEW_ID:
            case EDIT_INFO_ID:
            case POI_GRID_ROUTE_ID:
            case ALBUM_HIDE_ON_TIME_LINE_PAGE_ID:
            case SCAN_CAPTION_ID:
                hasMatch = true;
                break;
            default:
                break;
        }

        return hasMatch;
    }

    @Override
    public boolean onCreate() {
        mDatabase = new DatabaseHelper(getContext());
        GLog.w(TAG, "onCreate");
        return true;
    }

    @Override
    public void shutdown() {
        super.shutdown();
    }

    private Uri insertInternal(Uri uri, String table, ContentValues initialValues, boolean singleInsert) {
        Uri newUri = null;
        SQLiteDatabase db = mDatabase.getWritableDatabase();
        if (db == null) {
            throw new IllegalStateException("insertInternal, Couldn't open database for table: " + table);
        }
        if (TextUtils.isEmpty(table)) {
            throw new IllegalStateException("insertInternal, Unknown table: " + table);
        } else {
            boolean isLocalMedia = table.equals(GalleryStore.GalleryMedia.TAB);
            ContentValues values = initialValues;
            if (singleInsert && isLocalMedia) {
                values = new ContentValues(values);
                LocalTableColumnVerifier.checkAndFillValuesWhenInsert(values);
            }
            long time = (GProperty.DEBUG && singleInsert) ? System.currentTimeMillis() : 0;
            long rowId = db.insert(table, null, values);
            if (GProperty.DEBUG && singleInsert) {
                GLog.d(TAG, LogFlag.DL, "insertInternal cost: " + GLog.getTime(time)
                        + ", " + DBLogHelper.INSTANCE.insertToLog(table, values, mDatabase.getConverter(table))
                        + ", rowId: " + rowId);
            }
            if (rowId > 0) {
                newUri = ContentUris.withAppendedId(uri, rowId);
                if (singleInsert) {
                    if (isLocalMedia) {
                        LocalInsertStateUtils.setInsertLocalDataState(true);
                    }
                }
            }
        }
        return newUri;
    }

    @SuppressLint("ContentProviderCall")
    @Override
    public Bundle call(@NonNull String method, @Nullable String arg, @Nullable Bundle extras) {
        if (DatabaseUtils.METHOD_RAW_EXECUTE_SQL.equals(method) && (extras != null)) {
            String sql = extras.getString(DatabaseUtils.KEY_SQL);
            String[] args = extras.getStringArray(DatabaseUtils.KEY_ARGS);
            return rawExecuteSQL(sql, args);
        }
        if (DatabaseUtils.METHOD_NOTIFY_ALBUM_CACHE_SYNC_END.equals(method)) {
            mDatabase.sendTableEvent(EVENT_MIGRATE_ALBUM_END);
            return null;
        }
        if (DatabaseUtils.METHOD_NOTIFY_ALBUM_FULL_SYNC_END.equals(method)) {
            mDatabase.sendTableEvent(EVENT_FULL_SYNC_ALBUM_END);
            return null;
        }
        return null;
    }

    /**
     * 执行增删改SQL语句，不能执行查询
     * @param sql SQL语句
     * @param bindArgs SQL参数
     * @return 当前原生没有公开返回具体数量的接口，无法填进bundle里，暂时只要不为null就是成功
     */
    private Bundle rawExecuteSQL(String sql, Object[] bindArgs) {
        if (TextUtils.isEmpty(sql)) {
            throw new IllegalArgumentException("rawExecuteSQL, sql is null or empty");
        }
        SQLiteDatabase db = mDatabase.getWritableDatabase();
        if (db == null) {
            throw new IllegalStateException("rawExecuteSQL, Couldn't open database for table");
        }
        long time = System.currentTimeMillis();
        try {
            db.execSQL(sql, (bindArgs == null) ? new Object[] {} : bindArgs);
            return new Bundle();
        } catch (Exception e) {
            GLog.e(TAG, "rawExecuteSQL error:", e);
        } finally {
            if (GProperty.DEBUG) {
                GLog.d(TAG, String.format("rawExecuteSQL, %s,\n sArgs:%s,\n cost:%s",
                        EncryptUtils.mixPath(sql),
                        EncryptUtils.mixPath(Arrays.toString(bindArgs)),
                        GLog.getTime(time))
                );
            }
        }
        return null;
    }

    @Override
    public Uri insert(@NonNull Uri uri, ContentValues values) {
        Uri newUri = null;
        final String table = getMatchTable(uri);
        try {
            newUri = insertInternal(uri, table, values, true);
            if ((newUri != null) && (getContext() != null)) {
                UriNotifier.INSTANCE.notifyUri(getContext().getContentResolver(), newUri, null, table);
            }
        } catch (final Exception e) {
            GLog.e(TAG, "insert uri: " + uri + ", exception: ", e);
            throw e;
        }
        return newUri;
    }

    @Override
    public int bulkInsert(@NonNull Uri uri, @NonNull ContentValues[] initialValues) {
        int numInserted = 0;
        final String table = getMatchTable(uri);
        ContentValues[] values = null;
        int rowCount = 0;
        boolean isDataAligned = false;
        if (TextUtils.isEmpty(table)) {
            GLog.e(TAG, "bulkInsert, table is null or empty");
            return 0;
        }
        long time = System.currentTimeMillis();
        try {
            SQLiteDatabase db = mDatabase.getWritableDatabase();
            if (db == null) {
                throw new IllegalStateException("bulkInsert, Couldn't open database for table: " + table);
            }
            rowCount = initialValues.length;
            if (rowCount <= 0) {
                GLog.e(TAG, "bulkInsert, initialValues is null or empty. table: " + table);
                return 0;
            }

            final boolean isLocalMedia = table.equals(GalleryStore.GalleryMedia.TAB);
            if (isLocalMedia) {
                values = new ContentValues[rowCount];
                ContentValues newValues = null;
                for (int i = 0; i < rowCount; i++) {
                    newValues = new ContentValues(initialValues[i]);
                    LocalTableColumnVerifier.checkAndFillValuesWhenInsert(newValues);
                    values[i] = newValues;
                }
            } else {
                values = initialValues;
            }

            isDataAligned = GProperty.getDEBUG_HIGH_PERFORMANCE_BULK_INSERT() && isDataAligned(values);
            if (isDataAligned) {
                numInserted = bulkInsertWhenDataAligned(db, table, values);
            } else {
                numInserted = bulkInsertWhenDataNoAligned(uri, db, table, values);
            }
            if (numInserted > 0) {
                if (isLocalMedia) {
                    LocalInsertStateUtils.setInsertLocalDataState(true);
                }
                if (getContext() != null) {
                    UriNotifier.INSTANCE.notifyUri(getContext().getContentResolver(), uri, null, table);
                }
            }
        } catch (final Exception e) {
            GLog.e(TAG, LogFlag.DL, "bulkInsert uri: " + uri + ", exception: ", e);
            setsBulkInsertError(e.getMessage());
            throw e;
        } finally {
            if (GProperty.DEBUG) {
                logEveryInsertValues(table, values);
                GLog.d(TAG, LogFlag.DL, "bulkInsert, table:" + table
                        + ", uri:" + uri
                        + ", count:" + rowCount
                        + ", numInserted:" + numInserted
                        + ", isDataAligned:" + isDataAligned
                        + LOG_APPEND_COST + (System.currentTimeMillis() - time));
            }
        }
        return numInserted;
    }

    private void logEveryInsertValues(String table, ContentValues[] values) {
        if (values != null) {
            SensitiveFieldsConverter converter = mDatabase.getConverter(table);
            for (int i = 0; i < values.length; i++) {
                if (i >= GProperty.getBULK_INSERT_LOG_MAX_COUNT()) {
                    break;
                }
                GLog.d(TAG, DBLogHelper.INSTANCE.insertToLog(table, values[i], converter));
            }
        }
    }

    /**
     * 检测是否数据对齐（该批插入的values字段数量和类型完全一致）
     */
    private boolean isDataAligned(@NonNull ContentValues[] values) {
        Set<String> first = values[0].keySet();
        for (int i = 1; i < values.length; i++) {
            if (!first.equals(values[i].keySet())) {
                return false;
            }
        }
        return true;
    }

    private int bulkInsertWhenDataNoAligned(
            @NonNull Uri uri,
            @NonNull SQLiteDatabase db,
            @NonNull String table,
            @NonNull ContentValues[] values
    ) {
        int numInserted = 0;
        try {
            db.beginTransaction();
            for (ContentValues value : values) {
                if (value != null) {
                    if (insertInternal(uri, table, value, false) != null) {
                        numInserted++;
                    }
                }
            }
            db.setTransactionSuccessful();
        } finally {
            db.endTransaction();
        }
        return numInserted;
    }

    /**
     * 数据对齐的情况下，使用合并插入语句提升插入的速度。
     * 注意：尝试过预编译SQL的方式(db.compileStatement())几乎没有优化效果。
     * @param db
     * @param table
     * @param values
     * @return
     * @throws IllegalArgumentException
     */
    private int bulkInsertWhenDataAligned(@NonNull SQLiteDatabase db, @NonNull String table, @NonNull ContentValues[] values)
            throws IllegalArgumentException {
        final Object[] projections = values[0].keySet().toArray();
        final int projectionLen = projections.length;
        if (projectionLen <= 0) {
            throw new IllegalArgumentException("bulkInserWhenDataAligned, projections is null or empty. table: " + table);
        }

        final int rowCount = values.length;
        final StringBuilder sqlBuilder = new StringBuilder();
        final int argsCount = rowCount * projectionLen;
        // 尽量使用Object类型，例如改成String可能导致在SQLiteConnection#bindArguments中有额外的消耗，从而db.execSQL执行更久
        Object[] bindArgs = null;
        int numInserted = 0;

        // 1、往stringBuilder填充SQL -- insert into stable(id, 年龄, 名字) values
        fillBulkInsertHeadSQL(sqlBuilder, table, projections);
        // 如果占位符数量超过Sqlite限制，分批处理
        if (argsCount > DatabaseUtils.SQLITE_MAX_VARIABLE_NUMBER) {
            int headSqlLen = sqlBuilder.length();
            // 每批插入的记录数
            int batchCount = DatabaseUtils.SQLITE_MAX_VARIABLE_NUMBER / projectionLen;
            // 每批写入的起始结束位置
            int start = 0;
            int end = batchCount;
            // 2、stringBuilder追加SQL -- (?, ?, ?),(?, ?, ?),(?, ?, ?)
            appendValuesVariableSQL(sqlBuilder, batchCount, projectionLen);
            final String sql = sqlBuilder.toString(); // 只用于while循环里的执行，那里都是一样的sql语句
            bindArgs = new Object[batchCount * projectionLen];
            db.beginTransaction();
            try {
                while (end <= rowCount) {
                    // 3、填充数据
                    fillBindArgs(bindArgs, projections, values, start, end);
                    // 4、插入数据库
                    db.execSQL(sql, bindArgs);
                    start = end;
                    end += batchCount;
                }
                if (start < rowCount) {
                    // 把剩余的记录插入数据库
                    sqlBuilder.setLength(headSqlLen);
                    int remainRowCount = rowCount - start; // 剩余未插入的记录数
                    appendValuesVariableSQL(sqlBuilder, remainRowCount, projectionLen);
                    bindArgs = new Object[remainRowCount * projectionLen];
                    fillBindArgs(bindArgs, projections, values, start, rowCount);
                    db.execSQL(sqlBuilder.toString(), bindArgs);
                }
                numInserted = rowCount;
                db.setTransactionSuccessful();
            } finally {
                db.endTransaction();
            }
        } else {
            appendValuesVariableSQL(sqlBuilder, rowCount, projectionLen);
            bindArgs = new Object[argsCount];
            fillBindArgs(bindArgs, projections, values, 0, rowCount);
            db.execSQL(sqlBuilder.toString(), bindArgs);
            numInserted = rowCount;
        }
        return numInserted;
    }

    /**
     * 填充需要绑定的参数到bindArgs中
     *
     * @param projections /
     * @param values      /
     * @param start       the beginning index, inclusive.
     * @param end         the ending index, exclusive.
     */
    private void fillBindArgs(Object[] bindArgs, Object[] projections, ContentValues[] values, int start, int end) {
        final int projectionCount = projections.length;
        ContentValues val = null;
        int index = 0;
        for (int r = start; r < end; r++) {
            val = values[r];
            for (int p = 0; p < projectionCount; p++) {
                bindArgs[index++] = val.get((String) projections[p]);
            }
        }
    }

    /**
     * insert into stable(id, 年龄, 名字) values
     * @param sql
     * @param table
     * @param projections
     */
    private void fillBulkInsertHeadSQL(StringBuilder sql, String table, Object[] projections) {
        sql.append("INSERT INTO ");
        sql.append(table);
        sql.append('(');

        for (Object name : projections) {
            sql.append(name);
            sql.append(",");
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(')');
        sql.append(" VALUES ");
    }

    /**
     * (1, 40, "水哥"),(2, 33, "达哥"),(3, 30, "勇哥")
     * @param sql
     * @param rowCount
     * @param columnCount
     */
    private void appendValuesVariableSQL(StringBuilder sql, int rowCount, int columnCount) {
        for (int r = 0; r < rowCount; r++) {
            sql.append("(");
            for (int c = 0; c < columnCount; c++) {
                sql.append("?,");
            }
            sql.deleteCharAt(sql.length() - 1);
            sql.append("),");
        }
        sql.deleteCharAt(sql.length() - 1);
    }

    private String getOperationType(@NonNull ArrayList<ContentProviderOperation> operations) {
        String operationType = null;
        if (operations.get(0).isInsert()) {
            operationType = (operations.size() == 1)
                    ? StatisticsConstant.OperationType.TYPE_INSERT : StatisticsConstant.OperationType.TYPE_INSERT_MULTI;
        } else if (operations.get(0).isDelete()) {
            operationType = (operations.size() == 1)
                    ? StatisticsConstant.OperationType.TYPE_DELETE : StatisticsConstant.OperationType.TYPE_DELETE_MULTI;
        } else if (operations.get(0).isUpdate()) {
            operationType = (operations.size() == 1)
                    ? StatisticsConstant.OperationType.TYPE_UPDATE : StatisticsConstant.OperationType.TYPE_UPDATE_MULTI;
        }

        return operationType;
    }

    @NonNull
    @Override
    public ContentProviderResult[] applyBatch(@NonNull ArrayList<ContentProviderOperation> operations)
            throws OperationApplicationException {
        String table = null;
        String operationType = null;
        ContentProviderResult[] results = null;
        try {
            final int numOperations = operations.size();
            if (numOperations == 0) {
                throw new IllegalStateException("applyBatch, operations is null or empty.");
            }
            table = getMatchTable(operations.get(0).getUri());
            SQLiteDatabase db = mDatabase.getWritableDatabase();
            if (db == null) {
                throw new IllegalStateException("applyBatch, Couldn't open database.");
            }
            operationType = getOperationType(operations);
            final HashSet<Uri> uris = new HashSet<>();
            results = new ContentProviderResult[numOperations];
            db.beginTransaction();
            try {
                for (int i = 0; i < numOperations; i++) {
                    final ContentProviderOperation operation = operations.get(i);
                    final Uri uri = operation.getUri();
                    if (applyOperation(results, operation, uri, i)) {
                        uris.add(uri);
                    }
                }
                db.setTransactionSuccessful();
            } finally {
                db.endTransaction();
            }
            if (getContext() != null) {
                ContentResolver contentResolver = getContext().getContentResolver();
                for (Uri uri : uris) {
                    UriNotifier.INSTANCE.notifyUri(contentResolver, uri, null, getMatchTable(uri));
                }
            }

        } catch (final Exception e) {
            GLog.e(TAG, "applyBatch, error: ", e);
            throw e;
        }
        return results;
    }

    private boolean applyOperation(ContentProviderResult[] results, ContentProviderOperation operation,
                                   Uri uri, int index) throws OperationApplicationException {
        if (uri.getBooleanQueryParameter(DatabaseUtils.EXCEPTION_ALLOWED, false)) {
            try {
                results[index] = operation.apply(this, results, index);
            } catch (Exception e) {
                GLog.e(TAG, "applyOperation allowed exception, error:", e);
                results[index] = new ContentProviderResult(0);
                return false;
            }
        } else {
            results[index] = operation.apply(this, results, index);
        }
        return true;
    }

    private boolean copyTableIfNeed(Uri uri, SQLiteDatabase db) {
        String copyType = uri.getQueryParameter(GalleryStore.COPY_TABLE_TYPE);
        if (GalleryStore.BACKUP_SCAN_FACE_TABLE.equals(copyType)) {
            db.execSQL("DELETE FROM " + GalleryStore.ScanFaceBackup.TAB);
            db.execSQL("INSERT INTO " + GalleryStore.ScanFaceBackup.TAB + " SELECT * FROM " + GalleryStore.ScanFace.TAB);
            return true;
        } else if (GalleryStore.ROLLBACK_SCAN_FACE_TABLE.equals(copyType)) {
            db.execSQL("DELETE FROM " + GalleryStore.ScanFace.TAB);
            db.execSQL("INSERT INTO " + GalleryStore.ScanFace.TAB + " SELECT * FROM " + GalleryStore.ScanFaceBackup.TAB);
            return true;
        }
        return false;
    }

    @Override
    public int update(@NonNull Uri uri, ContentValues values, String selection, String[] selectionArgs) {
        int count = 0;
        final String table = getMatchTable(uri);
        try {
            long time = System.currentTimeMillis();
            SQLiteDatabase db = mDatabase.getWritableDatabase();
            if (db == null) {
                throw new IllegalStateException("update, Couldn't open database for table: " + table);
            }
            if (TextUtils.isEmpty(table)) {
                throw new IllegalStateException("update, Unknown table: " + table);
            }
            if (copyTableIfNeed(uri, db)) {
                return 0;
            }
            String where = hasMatchWhere(uri) ? "_id=" + uri.getPathSegments().get(1) : null;

            // 拼接业务输入的查询条件[selection]
            if (!TextUtils.isEmpty(selection)) {
                if (!TextUtils.isEmpty(where)) {
                    where = where + " AND (" + selection + ")";
                } else {
                    where = selection;
                }
            }
            if (table.equals(GalleryStore.GalleryMedia.TAB)) {
                LocalTableColumnVerifier.checkAndFillValuesWhenUpdate(values);
            }
            try {
                count = db.update(table, values, where, selectionArgs);
            } finally {
                if (GProperty.DEBUG) {
                    GLog.d(TAG, LogFlag.DL, "update cost: " + GLog.getTime(time)
                            + ", " + DBLogHelper.INSTANCE.updateToLog(table, values, selection, selectionArgs, mDatabase.getConverter(table))
                            + ", count: " + count
                    );
                }
            }

            if (count > 0) {
                if (!db.inTransaction() && (getContext() != null)) {
                    UriNotifier.INSTANCE.notifyUri(getContext().getContentResolver(), uri, values, table);
                }
            }
        } catch (final Exception e) {
            GLog.e(TAG, "update, error: ", e);
            throw e;
        }
        return count;
    }

    @Override
    public int delete(@NonNull Uri uri, String selection, String[] selectionArgs) {
        int count = 0;
        final String table = getMatchTable(uri);
        try {
            long time = System.currentTimeMillis();
            SQLiteDatabase db = mDatabase.getWritableDatabase();
            if (db == null) {
                throw new IllegalStateException("delete, Couldn't open database for table: " + table);
            }
            if (TextUtils.isEmpty(table)) {
                throw new IllegalStateException("delete, Unknown table: " + table);
            }
            String where = hasMatchWhere(uri) ? "_id=" + uri.getPathSegments().get(1) : null;

            // Add in the user requested WHERE clause, if needed
            if (!TextUtils.isEmpty(selection)) {
                if (!TextUtils.isEmpty(where)) {
                    where = where + " AND (" + selection + ")";
                } else {
                    where = selection;
                }
            }

            // 云端数据不能直接删除，需标记invalid=1，非云端可以直接删除
            boolean needUpdateInvalid = false;
            int match = URI_MATCHER.match(uri);
            if ((GALLERY_MEDIA == match) || (GALLERY_MEDIA_ID == match) || (LOCAL_MEDIA == match) || (LOCAL_MEDIA_ID == match)) {
                // 相册图集等使用local_media删除记录时，未指定gid条件，则采用更新方式标记invalid=1
                needUpdateInvalid = ((where == null) || !where.contains(GalleryStore.GalleryColumns.CloudColumns.GLOBAL_ID));
            }
            try {
                int deleteCount = 0;
                int updateInvalidCount = 0;
                if (needUpdateInvalid) {
                    String deleteWhere = (where == null)
                            ? DatabaseUtils.getNoncloudDataWhere()
                            : where + AND + DatabaseUtils.getNoncloudDataWhere();
                    deleteCount = db.delete(table, deleteWhere, selectionArgs);

                    ContentValues values = new ContentValues();
                    values.put(GalleryStore.GalleryColumns.LocalColumns.INVALID, INVALID_SURVIVAL_SHORT);
                    values.put(GalleryStore.GalleryColumns.LocalColumns.GENERATION_MODIFIED, System.currentTimeMillis());
                    String updateWhere = (where == null)
                            ? DatabaseUtils.getOnlyCloudWhere()
                            : where + AND + DatabaseUtils.getOnlyCloudWhere();
                    updateInvalidCount = db.update(table, values, updateWhere, selectionArgs);
                    GLog.w(TAG, LogFlag.DL, "delete, deleteCount=" + deleteCount + " updateInvalidCount=" + updateInvalidCount);
                } else {
                    deleteCount = db.delete(table, where, selectionArgs);
                    GLog.w(TAG, LogFlag.DL, "delete, deleteCount=" + deleteCount);
                }
                count = deleteCount + updateInvalidCount;
            } finally {
                if (GProperty.DEBUG) {
                    GLog.d(TAG, LogFlag.DL, "update cost: " + GLog.getTime(time)
                            + ", " + DBLogHelper.INSTANCE.deleteToLog(table, selection, selectionArgs, mDatabase.getConverter(table))
                            + ", count: " + count);
                }
            }
            /*
             * in a transaction, the code that began the transaction should be
             * taking care of notifications once it ends the transaction successfully
             */
            if (count > 0) {
                if (!db.inTransaction() && (getContext() != null)) {
                    UriNotifier.INSTANCE.notifyUri(getContext().getContentResolver(), uri, null, table);
                }
            }
        } catch (final Exception e) {
            GLog.e(TAG, "delete, uri: " + uri + ", error: ", e);
            throw e;
        }
        return count;
    }

    @Override
    public Cursor query(Uri uri, String[] projection, String selection, String[] selectionArgs, String sortOrder) {
        String limit = uri.getQueryParameter("limit");
        Bundle queryArgs = DatabaseUtils.createSqlQueryBundle(selection, selectionArgs, sortOrder, limit);
        return query(uri, projection, queryArgs, null);
    }

    private Cursor rawQuery(SQLiteDatabase db, String table,
                            Bundle queryArgs, CancellationSignal signal) {
        long time = System.currentTimeMillis();
        String rawSql = queryArgs.getString(DatabaseUtils.QUERY_RAW_SQL);
        if (TextUtils.isEmpty(rawSql)) {
            GLog.e(TAG, "rawQuery, rawSql is null, table: " + table);
            return null;
        }

        final String[] selectionArgs = queryArgs.getStringArray(DatabaseUtils.QUERY_ARG_SQL_SELECTION_ARGS);

        Pair<String, Boolean> sqlCheckResult = checkRawQuerySqlString(rawSql);
        Cursor cursor = null;
        try {
            cursor = db.rawQuery(sqlCheckResult.first, selectionArgs, signal);
        } catch (Exception e) {
            // 非debug包下，如果sql语句变更后query出现异常，尝试重新用原有语句进行查询
            if (sqlCheckResult.second && !BuildConfig.DEBUG) {
                cursor = db.rawQuery(rawSql, selectionArgs, signal);
            }
        } finally {
            if (GProperty.DEBUG) {
                GLog.d(TAG, LogFlag.DL, "rawQuery cost: " + GLog.getTime(time)
                        + ", table: " + table
                        + ", rawSql: " + EncryptUtils.mixPath(rawSql)
                        + ", sqlStr: " + ((sqlCheckResult.second) ? EncryptUtils.mixPath(sqlCheckResult.first) : "Same as rawSql")
                        + ", sArgs: " + EncryptUtils.mixPath(Arrays.toString(selectionArgs))
                        + ", count: " + ((cursor == null) ? "null" : cursor.getCount()));
            }
        }
        return cursor;
    }

    private Pair<String, Boolean> checkRawQuerySqlString(String sqlStr) {
        if (!TextUtils.isEmpty(sqlStr)
                && sqlStr.contains(GalleryStore.GalleryMedia.TAB)
                && DatabaseUtils.isSelectStatement(sqlStr)
                && DatabaseUtils.isQueryOnlyAllowLocalFile()) {
            AccessSqlSimplifier accessSqlSimplifier = AccessSqlSimplifier.create(sqlStr);
            accessSqlSimplifier.setOnMakeSimpleValidator(simpleSql -> simpleSql.contains(GalleryStore.GalleryMedia.TAB));
            if (accessSqlSimplifier.makeSimple()) {
                final boolean[] replaceTableResults = {false};
                String newSql = accessSqlSimplifier.revertSimpleSql((simplifier, ownerSimpleSql) -> {
                    if (ownerSimpleSql != null) {
                        replaceTableResults[0] = true;
                        String newTable = GalleryStore.GalleryMedia.FILTER_LOCAL_VIEW;
                        return ownerSimpleSql.replaceAll("\\b" + GalleryStore.GalleryMedia.TAB + "\\b", newTable);
                    }
                    return ownerSimpleSql;
                });
                return new Pair<>(newSql, replaceTableResults[0]);
            }
        }
        return new Pair<>(sqlStr, false);
    }

    private Cursor queryInternal(@NonNull Uri uri, SQLiteDatabase db, String table,
                                 String[] projection, Bundle queryArgs) {
        if (TextUtils.isEmpty(table)) {
            throw new IllegalStateException("query, Unknown uri: " + uri);
        }
        long time = System.currentTimeMillis();
        final String[] selectionArgs = queryArgs.getStringArray(DatabaseUtils.QUERY_ARG_SQL_SELECTION_ARGS);
        final String selection = queryArgs.getString(DatabaseUtils.QUERY_ARG_SQL_SELECTION);
        final String sortOrder = queryArgs.getString(DatabaseUtils.QUERY_ARG_SQL_SORT_ORDER);
        final String groupBy = queryArgs.getString(DatabaseUtils.QUERY_ARG_SQL_GROUP_BY);
        final String having = queryArgs.getString(DatabaseUtils.QUERY_ARG_SQL_HAVING);
        final String limit = queryArgs.getString(DatabaseUtils.QUERY_ARG_SQL_LIMIT);

        List<String> prependArgs = new ArrayList<>();
        SQLiteQueryBuilder qb = new SQLiteQueryBuilder();
        DistinctUtils.setDistinct(uri, qb);
        qb.setTables(table);
        if (hasMatchWhere(uri)) {
            qb.appendWhere("_id=?");
            prependArgs.add(uri.getPathSegments().get(1));
        }
        int match = URI_MATCHER.match(uri);
        boolean ignoreInvalid = false;
        switch (match) {
            case LOCAL_MEDIA:
            case LOCAL_MEDIA_ID:
                ignoreInvalid = uri.getBooleanQueryParameter(DatabaseUtils.IGNORE_INVALID, false);
                if (LocalTableColumnVerifier.isNeedAppendField(selection, INVALID) && !ignoreInvalid) {
                    qb.appendWhereStandalone(DatabaseUtils.getDataValidWhere());
                }
                // 查询本地数据需要添加过滤条件 is_trasehd == 0
                if (LocalTableColumnVerifier.isNeedAppendField(selection, GalleryStore.GalleryColumns.LocalColumns.IS_TRASHED)) {
                    qb.appendWhereStandalone(DatabaseUtils.getFilterOutTrashedWhere(GalleryStore.GalleryMedia.TAB));
                }
                break;
            default:
                break;
        }

        /*
         如果当前处于儿童空间，此时云同步功能不可用，因此query仅返回本地图片或视频数据.
         需要注意的是where字段可能存在明确指定 local_media 字段，因此不能直接替换成 filter_local_view 视图表
         */
        if (GalleryStore.GalleryMedia.TAB.equals(table) && DatabaseUtils.isQueryOnlyAllowLocalFile()) {
            /*
             不管当前selection语句内是否已经有了(local_file_status >= 4)，只要不满足对应条件我们都应该默认给其加上，
             因为语句内包含的不一定是(local_file_status >= 4)，
             即使是，也不一定是对最终数据进行的过滤，有可能出现(whereA OR (local_file_status >= 4))这种或运算表达式之类的情况
             */
            qb.appendWhereStandalone(DatabaseUtils.getOnlyLocalFileWhere());
        }

        String[] sArgs = combine(prependArgs, selectionArgs);
        Cursor cursor = null;
        try {
            cursor = qb.query(db, projection, selection, sArgs, groupBy, having, sortOrder, limit);
        } finally {
            if (GProperty.DEBUG) {
                GLog.d(TAG, LogFlag.DL, "queryInternal  count: " + ((cursor == null) ? "null" : cursor.getCount())
                        + ", cost: " + GLog.getTime(time)
                        + ", " + DBLogHelper.INSTANCE.queryToLog(table, projection, selection,
                        sArgs, groupBy, having, sortOrder, limit, mDatabase.getConverter(table))
                        + ", ignoreInvalid: " + ignoreInvalid
                );
            }
        }
        return cursor;
    }

    @Override
    public Cursor query(@NonNull Uri uri, String[] projection, Bundle queryArgs, CancellationSignal signal) {
        final String table = getMatchTable(uri, true);
        try {
            SQLiteDatabase db = mDatabase.getReadableDatabase();
            if (db == null) {
                throw new IllegalStateException("query, Couldn't open database for table: " + table);
            }

            queryArgs = (queryArgs != null) ? queryArgs : Bundle.EMPTY;
            if (URI_MATCHER.match(uri) == RAW_QUERY) {
                return rawQuery(db, GalleryStore.RawQuery.TAB, queryArgs, signal);
            } else {
                return queryInternal(uri, db, table, projection, queryArgs);
            }
        } catch (final Exception e) {
            GLog.e(TAG, "query, uri: " + uri + ", error: ", e);
            throw e;
        }
    }

    @Override
    public String getType(@NonNull Uri uri) {
        return null;
    }

    private String[] combine(List<String> prepend, String[] userArgs) {
        int preSize = prepend.size();
        if (preSize == 0) {
            return userArgs;
        }
        int userSize = (userArgs != null) ? userArgs.length : 0;
        String[] combined = new String[preSize + userSize];
        for (int i = 0; i < preSize; i++) {
            combined[i] = prepend.get(i);
        }
        for (int i = 0; i < userSize; i++) {
            combined[preSize + i] = userArgs[i];
        }
        return combined;
    }

    static final class DatabaseHelper extends SQLiteOpenHelper {
        static final String DATABASE_NAME = "gallery.db";
        private static final Map<String, SensitiveFieldsConverter> FIELDS_CONVERTER_MAP = new HashMap();

        /**
         * 表更新监视器:内部创建触发器+自定义函数监听
         */
        private final List<ITableMonitor> mTableMonitorList = new ArrayList<>();

        DatabaseHelper(Context context) {
            super(context, DATABASE_NAME, null, DATABASE_VERSION);
            initFieldsConverterMap();
            initTableMonitorList(context);
        }

        private void initTableMonitorList(Context context) {
            mTableMonitorList.add(new LocalMediaMonitor(context));
            mTableMonitorList.add(new DmpOcrPagesContentMonitor(context));
            mTableMonitorList.add(new DmpScanLabelMonitor(context));
        }

        @Override
        public void onOpen(SQLiteDatabase db) {
            super.onOpen(db);
            db.enableWriteAheadLogging();
            db.execSQL("PRAGMA synchronous = NORMAL");
            GLog.d(TAG, "onOpen, is wal = " + db.isWriteAheadLoggingEnabled());
        }

        @Override
        public void onConfigure(SQLiteDatabase db) {
            super.onConfigure(db);
            createCustomFunction(db);
        }

        @Override
        public void onCreate(SQLiteDatabase db) {
            GLog.d(TAG, LogFlag.DL, "onCreate");
            updateDatabase(db, 0, DATABASE_VERSION);
        }

        @Override
        public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
            GLog.d(TAG, LogFlag.DL, "onUpgrade");
            updateDatabase(db, oldVersion, newVersion);
        }

        @Override
        public void onDowngrade(SQLiteDatabase db, int oldVersion, int newVersion) {
            GLog.d(TAG, LogFlag.DL, "onDowngrade");
            downDatabase(db, oldVersion, newVersion);
        }

        private void updateDatabase(SQLiteDatabase db, int fromVersion, int toVersion) {
            GLog.w(TAG, "updateDatabase, from = " + fromVersion + ", to = " + toVersion);
            if (fromVersion < toVersion) {
                ArrayList<BaseTable> tables = createTables();
                for (BaseTable table : tables) {
                    try {
                        table.updateDatabase(db, fromVersion, toVersion);
                    } catch (final Exception e) {
                        GLog.e(TAG, "updateDatabase, e: ", e);
                        // debug模式下主动抛出异常，便于开发发现问题
                        if (BuildConfig.DEBUG && GProperty.DEBUG) {
                            throw e;
                        }
                    }
                }
                // 为避免每次启动进程都进行调用，只在数据库升级时检查删除表
                dropTableIfNeed(db);
            }
            createCommonTrigger(db);
        }

        /**
         * 清除所有表、索引等，再重建
         */
        private void downDatabase(SQLiteDatabase db, int fromVersion, int toVersion) {
            GLog.w(TAG, "downDatabase, from = " + fromVersion + ", to = " + toVersion);
            // 清除數據庫所有信息
            DatabaseUtils.dropDatabaseAll(db);
            // 再重建
            updateDatabase(db, 0, toVersion);
        }

        private void dropTableIfNeed(SQLiteDatabase db) {
            if (MigrateManager.getInstance().isMigrateDone()) {
                db.execSQL(DROP_TABLE + "cloud_media");
                db.execSQL(DROP_TABLE + "recycle_media");
            }
        }

        private ArrayList<BaseTable> createTables() {
            ArrayList<BaseTable> tables = new ArrayList<>();
            // 1.add local media table
            tables.add(new LocalMediaTable());
            // 2.add recycle media table
            tables.add(new RecycleMediaTable());
            // 4.add memories table
            tables.add(new MemoriesTable());
            // 5.add scan face table
            tables.add(new ScanFaceTable());
            // 6.add scan label table
            tables.add(new ScanLabelTable());
            // 7.add scan abort file table
            tables.add(new ScanAbortFileTable());
            // 8.add geo route table
            tables.add(new GeoRouteTable());
            // 9.add append to album table
            tables.add(new AppendToAlbumTable());
            // 10.add ocr pages table
            tables.add(new OcrPagesTable());
            // 11.add other album set table
            tables.add(new OtherAlbumSetTable());
            // 12.add locked pictures table
            tables.add(new LockedPicturesTable());
            // 13.add search history table
            tables.add(new SearchHistoryTable());
            // 14.add downloadUrl table
            tables.add(new DownloadUrlTable());
            // 15.add festival table
            tables.add(new FestivalTable());
            // 16.add scene crash table
            tables.add(new SceneCrashTable());
            // 17.add similar feature table
            tables.add(new SimilarFeatureTable());
            // 18.add senior media table
            tables.add(new SeniorMediaTable());
            // 19.add widget table
            tables.add(new WidgetTable());
            // 20.add FileExtendTable table
            tables.add(new FileInfoTable());
            // 21.add shareAlbum table
            tables.add(new SharedAlbumTable());
            // 22.add scan MultiModal table
            tables.add(new ScanMultiModalTable());
            // 24.add EditInfo table
            tables.add(new EditInfoTable());
            // 25.add PersonalInfoCollection table
            tables.add(new PersonalInfoCollectionTable());
            // 26.add WatermarkMasterResource table
            tables.add(new WatermarkMasterResourceTable());
            // 27.add ButtonConfigResource table
            tables.add(new ButtonConfigResourceTable());
            // 28.add scan OcrEmbedding table
            tables.add(new ScanOcrEmbeddingTable());
            // 29.add scan Pet table
            tables.add(new PetTable());
            // 30.add scan PersonPetGroup table
            tables.add(new PersonPetGroupTable());
            // 31.add scan travel table
            tables.add(new ScanTravelTable());
            // 32.add poiGrid table
            tables.add(new PoiGridTable());
            // 33.add AlbumHideOnTimeLinePageTable table
            tables.add(new AlbumHideOnTimeLinePageTable());
            // 34.add study table
            tables.add(new StudyTable());
            // 35.add sync abandon data table
            tables.add(new CloudSyncAbandonDataTable());
            // 36.add scan caption table
            tables.add(new ScanCaptionTable());
            return tables;
        }

        /**
         * 如果没有提供敏感字段信息，将不输出selection、selectionArgs、values等可能包含敏感信息的部分
         */
        private void initFieldsConverterMap() {
            FIELDS_CONVERTER_MAP.put(GalleryStore.GalleryMedia.TAB, new LocalMediaTable());
            FIELDS_CONVERTER_MAP.put(MigrateMediaStore.RecycleMedia.TAB, new RecycleMediaTable());

            MemoriesTable memoriesTable = new MemoriesTable();
            FIELDS_CONVERTER_MAP.put(GalleryStore.MemoriesSet.TAB, memoriesTable);
            FIELDS_CONVERTER_MAP.put(GalleryStore.MemoriesSetmap.TAB, memoriesTable);
            FIELDS_CONVERTER_MAP.put(GalleryStore.MemoriesSetmapView.TAB, memoriesTable);

            FIELDS_CONVERTER_MAP.put(GalleryStore.AppendToAlbum.TAB, new AppendToAlbumTable());
            FIELDS_CONVERTER_MAP.put(GalleryStore.OtherAlbumSet.TAB, new OtherAlbumSetTable());
            FIELDS_CONVERTER_MAP.put(GalleryStore.LockedPictures.TAB, new LockedPicturesTable());
            FIELDS_CONVERTER_MAP.put(GalleryStore.DownloadUrl.TAB, new DownloadUrlTable());

            WidgetTable widgetTable = new WidgetTable();
            FIELDS_CONVERTER_MAP.put(GalleryStore.WidgetSet.TAB, widgetTable);
            FIELDS_CONVERTER_MAP.put(GalleryStore.WidgetDisplayList.TAB, widgetTable);

            SharedAlbumTable sharedAlbumTable = new SharedAlbumTable();
            FIELDS_CONVERTER_MAP.put(GalleryStore.SharedAlbum.TAB, sharedAlbumTable);
            FIELDS_CONVERTER_MAP.put(GalleryStore.SharedMedia.TAB, sharedAlbumTable);
        }

        public SensitiveFieldsConverter getConverter(String tableName) {
            return FIELDS_CONVERTER_MAP.get(tableName);
        }

        private void createCommonTrigger(SQLiteDatabase db) {
            for (ITableMonitor tableMonitor : mTableMonitorList) {
                tableMonitor.createTrigger(db);
            }
        }

        private void createCustomFunction(SQLiteDatabase db) {
            for (ITableMonitor tableMonitor : mTableMonitorList) {
                tableMonitor.createCustomFunction(db);
            }
        }

        void sendTableEvent(@TableEvent int event) {
            GLog.d(TAG, DF, "sendTableEvent " + event);
            for (ITableMonitor tableMonitor : mTableMonitorList) {
                if (tableMonitor instanceof ITableSendEvent) {
                    ((ITableSendEvent) tableMonitor).sendEvent(event);
                }
            }
        }
    }

    public static String getBulkInsertError() {
        return sBulkInsertError;
    }

    private static void setsBulkInsertError(String bulkInsertError) {
        sBulkInsertError = bulkInsertError;
    }
}