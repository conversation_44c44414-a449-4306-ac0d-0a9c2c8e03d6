/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ISceneSqlGenerator.kt
 ** Description:根据场景生成真正的查询条件
 ** Version: 1.0
 ** Date: 2024/4/10
 ** Author: <PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2024/4/10     1.0         OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.foundation.database.album.query.generator.scene

import android.os.Bundle
import com.oplus.gallery.foundation.database.album.getJsonColumSqlCondition
import com.oplus.gallery.foundation.database.helper.Constants
import com.oplus.gallery.foundation.database.store.CacheStore
import com.oplus.gallery.foundation.database.util.SQLGrammar
import com.oplus.gallery.foundation.database.util.SQLSelectBuilder

/**
 * 按场景生成真正的查询sql
 */
interface ISceneSqlGenerator {
    /**
     * 根据场景:获取查询数据的raw的sql语句
     * @param projections 查询是需要返回的列
     * @param queryArgs 查询参数
     */
    fun getQueryRawSql(projections: Array<String>?, queryArgs: Bundle): String

    /**
     * 获取图集列表总数的查询语句
     */
    fun getCountRawSql(queryArgs: Bundle): String
}

/**
 * 通过bucket_ids查询album_count表的总数
 * 根据group by bucket_id去重,避免逻辑异常,导致album_count表有重复记录
 * (
 *   SELECT sum(album_count.image - album_count.gif)
 *   FROM ( SELECT *
 *          FROM album_count
 *          WHERE EXISTS ( SELECT 1 FROM JSON_EACH(bucket_ids) AS item WHERE item.value=bucket_id)
 *           GROUP BY bucket_id
 *        )
 *  )  AS  displayCount
 *
 *  @param conditionColumn  如 "JPEG + HEIF"
 */
internal fun getDisplayCountSql(conditionColumn: String): String {
    val displayCountSubSql =
        SQLSelectBuilder
            .build()
            .append(SQLGrammar.LEFT_BRACKETS)
            .select("*")
            .from(Constants.Album.RealTable.TABLE_ALBUM_COUNT)
            .where(getJsonColumSqlCondition(CacheStore.AlbumColumns.BUCKET_IDS, CacheStore.AlbumCountColumns.BUCKET_ID))
            .groupBy(CacheStore.AlbumCountColumns.BUCKET_ID)
            .append(SQLGrammar.RIGHT_BRACKETS)
            .toString()

    val displayCountSql =
        SQLSelectBuilder
            .build()
            .append(SQLGrammar.LEFT_BRACKETS)
            .select("${SQLGrammar.SUM}($conditionColumn)")
            .from(displayCountSubSql)
            .append(SQLGrammar.RIGHT_BRACKETS)
            .append("${SQLGrammar.AS} ${Constants.Album.SceneProjection.DISPLAY_COUNT}")
            .toString()
    return displayCountSql
}