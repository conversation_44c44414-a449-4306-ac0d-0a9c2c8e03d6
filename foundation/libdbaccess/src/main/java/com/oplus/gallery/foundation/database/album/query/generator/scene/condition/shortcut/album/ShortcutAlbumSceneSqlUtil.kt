/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ShortcutAlbumSceneSqlUtil.kt
 ** Description:常用图集：根据场景生成真正的查询条件
 ** Version: 1.0
 ** Date: 2025/5/13
 ** Author: <PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery     2025/5/13     1.0         OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.foundation.database.album.query.generator.scene.condition.shortcut.album

import android.os.Bundle
import com.oplus.gallery.foundation.database.album.query.generator.scene.getDisplayCountSql
import com.oplus.gallery.foundation.database.album.query.generator.scene.group.BaseAlbumSetSqlGenerator.Companion.ALBUM_DISTINCT_KEY
import com.oplus.gallery.foundation.database.album.query.generator.scene.group.BaseAlbumSetSqlGenerator.Companion.CONDITION_EXCLUDE_ALBUM_FLAGS_KEY
import com.oplus.gallery.foundation.database.album.query.generator.scene.group.BaseAlbumSetSqlGenerator.Companion.CONDITION_INCLUDE_ALBUM_FLAGS_KEY
import com.oplus.gallery.foundation.database.helper.Constants.Album.RealTable.TABLE_ALBUM
import com.oplus.gallery.foundation.database.helper.Constants.Album.RealTable.TABLE_SHORTCUT
import com.oplus.gallery.foundation.database.helper.Constants.Album.SceneProjection.ALBUM_PROJECTION
import com.oplus.gallery.foundation.database.helper.Constants.Album.SceneProjection.DISPLAY_COUNT
import com.oplus.gallery.foundation.database.helper.Constants.Album.SceneProjection.SHORTCUT_ALBUM_PROJECTION
import com.oplus.gallery.foundation.database.helper.Constants.Album.SpecialAlbumFlag.FLAG_CARD_CASE_ALBUM
import com.oplus.gallery.foundation.database.helper.Constants.Album.SpecialAlbumFlag.FLAG_CLEANUP_ALBUM
import com.oplus.gallery.foundation.database.helper.Constants.Album.SpecialAlbumFlag.FLAG_CLEAR
import com.oplus.gallery.foundation.database.helper.Constants.Album.SpecialAlbumFlag.FLAG_DOLBY_ALBUM
import com.oplus.gallery.foundation.database.helper.Constants.Album.SpecialAlbumFlag.FLAG_LOG_VIDEO_ALBUM
import com.oplus.gallery.foundation.database.helper.Constants.Album.SpecialAlbumFlag.FLAG_MAP_ALBUM
import com.oplus.gallery.foundation.database.helper.Constants.Album.SpecialAlbumFlag.FLAG_OLIVE_ALBUM
import com.oplus.gallery.foundation.database.helper.Constants.Album.SpecialAlbumFlag.FLAG_PORTRAIT_BLUR_ALBUM
import com.oplus.gallery.foundation.database.helper.Constants.Album.SpecialAlbumFlag.FLAG_RECYCLE_BIN_ALBUM
import com.oplus.gallery.foundation.database.helper.Constants.Album.SpecialAlbumFlag.FLAG_SAFE_BOX_ALBUM
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.CARD_ID_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.CLEAN_UP_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.DOLBY_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.LOG_VIDEO_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.MAP_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.OLIVE_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.PORTRAIT_BLUR_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.RECYCLE_BIN_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.SAFE_BOX_ALBUM_ID
import com.oplus.gallery.foundation.database.store.CacheStore.AlbumColumns.ALBUM_ID
import com.oplus.gallery.foundation.database.store.CacheStore.AlbumColumns.TOTAL_COUNT
import com.oplus.gallery.foundation.database.store.CacheStore.AlbumShortcut
import com.oplus.gallery.foundation.database.util.DatabaseUtils.QUERY_ARG_SQL_LIMIT
import com.oplus.gallery.foundation.database.util.DatabaseUtils.QUERY_ARG_SQL_ORDER_BY
import com.oplus.gallery.foundation.database.util.SQLGrammar.AND
import com.oplus.gallery.foundation.database.util.SQLGrammar.COMMA
import com.oplus.gallery.foundation.database.util.SQLGrammar.COMMA_SPACE
import com.oplus.gallery.foundation.database.util.SQLGrammar.COUNT_ALL
import com.oplus.gallery.foundation.database.util.SQLGrammar.COUNT_DISTINCT
import com.oplus.gallery.foundation.database.util.SQLGrammar.GREATER_THAN_ZERO
import com.oplus.gallery.foundation.database.util.SQLGrammar.LEFT_BRACKETS
import com.oplus.gallery.foundation.database.util.SQLGrammar.RIGHT_BRACKETS
import com.oplus.gallery.foundation.database.util.SQLSelectBuilder
import com.oplus.gallery.foundation.util.ext.bracket

/**
 * 常用图集构造sql的util
 */
internal object ShortcutAlbumSceneSqlUtil {

    /**
     * 将默认的orderBy写入参数中
     */
    @JvmStatic
    internal fun putOrderBy(queryArgs: Bundle) {
        if (queryArgs.getString(QUERY_ARG_SQL_ORDER_BY).isNullOrEmpty()) {
            queryArgs.putString(QUERY_ARG_SQL_ORDER_BY, AlbumShortcut.SORT)
        }
    }

    /**
     * 拼接orderBy和limit
     */
    @JvmStatic
    private fun appendOrderByAndLimitIfNeed(queryArgs: Bundle, sqlBuilder: SQLSelectBuilder) {
        queryArgs.getString(QUERY_ARG_SQL_ORDER_BY)?.takeIf { it.trim().isNotEmpty() }?.let { sqlBuilder.orderBy(it) }
        queryArgs.getString(QUERY_ARG_SQL_LIMIT)?.takeIf { it.trim().isNotEmpty() }?.let { sqlBuilder.limit(it) }
    }

    /**
     * 获取图集列表数据的查询sql语句
     */
    @JvmStatic
    internal fun buildRawSql(conditionColumn: String, projections: Array<String>?, queryArgs: Bundle): String {
        putOrderBy(queryArgs)
        return buildMediaConditionSql(conditionColumn, queryArgs, projections, isQueryData = true)
    }

    /**
     * 获取图集列表总数
     */
    @JvmStatic
    internal fun buildMediaConditionCountSql(conditionColumn: String, queryArgs: Bundle): String {
        val isDistinct = queryArgs.getBoolean(ALBUM_DISTINCT_KEY, false)
        // 获取后重置去重标记，以防止后面查询sql加上错误的group by语句导致查询错误
        queryArgs.putBoolean(ALBUM_DISTINCT_KEY, false)
        return buildMediaConditionSql(
            conditionColumn,
            queryArgs,
            arrayOf(getCountAllProjection(isDistinct)),
            isQueryData = false
        )
    }

    /**
     * 获取一级图集列表(图集tab)总数的rawSql查询语句
     *
     * 如：获取数据的sql
     *  SELECT .... 若是查总数 则为 countAll，若是查数据则为列
     *  FROM (
     * 		  SELECT *, shortcut_album_id,
     * 		         (
     *                SELECT sum(album_count.image - album_count.gif)
     *                FROM ( SELECT *
     *                       FROM album_count
     *                       WHERE EXISTS ( SELECT 1 FROM JSON_EACH(bucket_ids) AS item WHERE item.value=bucket_id)
     *                       GROUP BY bucket_id
     *                     )
     *              )  AS  displayCount
     *       FROM
     *          ( SELECT album_shortcut.shortcut_flags AS shortcutFlags,album_shortcut.shortcut_album_id AS shortcutAlbumId,album.*,album_shortcut.*
     *           FROM  album_shortcut left join album
     *           on album.album_id  = shortcut_album_id
     *           )
     *      WHERE
     *       ( displayCount  > 0
     *        AND
     *        shortcutAlbumId NOT IN (2064266562,-134776996,1552915770)
     *       )
     *       OR
     *       shortcutAlbumId IN ('1371924575', '370657253','1314986163')
     *
     * 	  order by shortcut_sort
     *  )
     * 根据媒体类型作为过滤条件的一级列表查询语句:用于查询数据或数量
     * @param conditionColumn 媒体类型条件,作为列,用于计算总数 如：image-gif
     * @param queryArgs 查询参数
     * @param projections 返回列
     * @param defaultProjection 若projections为null,则返回此默认列
     */
    @JvmStatic
    private fun buildMediaConditionSql(
        conditionColumn: String,
        queryArgs: Bundle,
        projections: Array<String>?,
        isQueryData: Boolean
    ): String {
        val albumTab = TABLE_ALBUM
        val shortcutTab = TABLE_SHORTCUT
        // 1.根据album_count表的bucketId和album表的bucketIds关联,查找album_count表的sum(xxx_count)作为一个图集的总数
        val displayCountSql = getDisplayCountSql(conditionColumn)
        // 2.构造where:  (DISPLAY_COUNT>0 and albumId not in(xxx,xxx)) or albumId=xxx or albumId=xxx2
        val subWhere = getMediaConditionSubWhere(queryArgs)

        val shortcutAlbumId = AlbumShortcut.ALBUM_ID
        val albumTableId = ALBUM_ID

        //3.常用图集表和图集表根据albumId左关联
        val subTable = SQLSelectBuilder
            .build()
            .select("$shortcutTab.*,$albumTab.*")
            .from(shortcutTab)
            .leftJoin(albumTab)
            .on("$shortcutAlbumId = $albumTableId")
            .toString()

        //4.构建实际查询语句
        val subSqlBuilder = SQLSelectBuilder.build().apply {
            val subProjections = buildSubProjectionsIfNeed(isQueryData, projections)
            val extraProjections = "$displayCountSql, $shortcutAlbumId"
            if (subProjections.isNullOrEmpty()) {
                select(extraProjections)
            } else {
                select(subProjections)
                append("$COMMA_SPACE $extraProjections")
            }
            from(bracket(subTable))
            where(subWhere)
        }

        // 5.追加group by 和order by 以及limit
        appendGroupByIfNeed(queryArgs, subSqlBuilder, shortcutAlbumId)
        appendOrderByAndLimitIfNeed(queryArgs, subSqlBuilder)

        //6 根据查询语句结果,按照传入的projections返回结果:将displayCount重命名为totalCount
        return SQLSelectBuilder.build()
            //将 totalCount的值替换成displayCount的
            .select(buildProjectionsAndRename(projections))
            .from(bracket(subSqlBuilder.toString()))
            .toString()
    }

    /**
     * 根据媒体文件类型获取对应的where查询语句
     *  (DISPLAY_COUNT>0 and albumId not in(xxx,xxx)) or albumId=xxx or albumId=xxx2
     */
    @JvmStatic
    private fun getMediaConditionSubWhere(queryArgs: Bundle) = StringBuilder().apply {
        append(LEFT_BRACKETS)
        append("$DISPLAY_COUNT $GREATER_THAN_ZERO ")
        // 1 过滤掉不需要的图集
        appendExcludeAlbumByFlag(queryArgs)?.let { excludeWhere ->
            append(AND)
            append(excludeWhere)
        }
        append(RIGHT_BRACKETS)
        // 2追加必要的图集
        appendRequiredAlbumByFlag(queryArgs, this)
    }.toString()

    /**
     * 子查询的列,先计算displayCount,将displayCount和列一起返回
     * @param isQueryData 是否查询数据,true则返回空,false:则返回子列
     */
    @JvmStatic
    private fun buildSubProjectionsIfNeed(isQueryData: Boolean, projections: Array<String>?): String? {
        if (isQueryData.not()) {
            return null
        }
        return buildProjection(projections)
    }

    /**
     * 增加一定要返回的图集
     * OR ALBUM_ID = CARD_CASE_ALBUM_ID
     * OR ALBUM_ID = MAP_ALBUM_ID
     * OR ALBUM_ID = CLEAN_UP_ALBUM_ID
     * OR ALBUM_ID = SAFE_BOX_ALBUM_ID
     * OR ALBUM_ID = MAP_ALBUM_ID
     */
    @JvmStatic
    private fun appendRequiredAlbumByFlag(queryArgs: Bundle, querySb: StringBuilder) {
        val requiredAlbumFlags = queryArgs.getInt(CONDITION_INCLUDE_ALBUM_FLAGS_KEY)
        val albumId = AlbumShortcut.ALBUM_ID

        if (requiredAlbumFlags and FLAG_CARD_CASE_ALBUM == FLAG_CARD_CASE_ALBUM) {
            //包含随身卡包图集(只要开关开启，不管是否有照片都需要显示)
            querySb.append(" OR $albumId = $CARD_ID_ALBUM_ID ")
        }
        if (requiredAlbumFlags and FLAG_MAP_ALBUM == FLAG_MAP_ALBUM) {
            //包含地图图集(地图图集图集数量为0，只要开关开启,必须显示）
            querySb.append(" OR $albumId = $MAP_ALBUM_ID ")
        }
        if (requiredAlbumFlags and FLAG_CLEANUP_ALBUM == FLAG_CLEANUP_ALBUM) {
            //包含清理建议
            querySb.append(" OR $albumId = $CLEAN_UP_ALBUM_ID ")
        }
        if (requiredAlbumFlags and FLAG_SAFE_BOX_ALBUM == FLAG_SAFE_BOX_ALBUM) {
            //包含私密图集
            querySb.append(" OR $albumId = $SAFE_BOX_ALBUM_ID ")
        }
        if (requiredAlbumFlags and FLAG_RECYCLE_BIN_ALBUM == FLAG_RECYCLE_BIN_ALBUM) {
            //回收站
            querySb.append(" OR $albumId = $RECYCLE_BIN_ALBUM_ID ")
        }
    }

    /**
     * 构建返回的列数,并将totalCount的值替换成真实的displayCount,避免不同场景返回的都是totalCount
     * 由于是常用图集,以常用图集表的album_id为准,避免使用album表的albumId返回的是null:因为表是左连接album表的albumId可能为null
     */
    @JvmStatic
    private fun buildProjectionsAndRename(projections: Array<String>?): String {
        val tmpProjections =
            if ((projections == null) || isAllProjection(projections)) ALBUM_PROJECTION + SHORTCUT_ALBUM_PROJECTION else projections
        val projectionSb = StringBuilder()
        val lastIndex = tmpProjections.size - 1
        tmpProjections.forEachIndexed { index, projection ->
            val trimProjection = projection.trim()
            when {
                TOTAL_COUNT == trimProjection -> projectionSb.append("$DISPLAY_COUNT AS $TOTAL_COUNT")
                ALBUM_ID == trimProjection -> projectionSb.append("${AlbumShortcut.ALBUM_ID} AS $ALBUM_ID")
                else -> projectionSb.append(projection)
            }
            if (lastIndex != index) {
                projectionSb.append(COMMA)
            }
        }
        return projectionSb.toString()
    }

    /**
     * 查询我的图集列表内容的sql 这里会将shortcutAlbumId as albumId,因left join album表的albumId可能会为null
     * select *
     * from album_shortcut
     * left join album
     * on album.album_id = album_shortcut.album_id
     * where (album.total_count>0 )
     *       OR
     *       album_shortcut.album_id IN ('SAFE_BOX_ALBUM_ID', 'CLEAN_UP_ALBUM_ID','RECYCLE_BIN_ALBUM_ID')
     */
    @JvmStatic
    internal fun buildAllOrRealAlbumSql(
        queryArgs: Bundle,
        projections: Array<String>?
    ): String {
        val albumTab = TABLE_ALBUM
        val shortcutTab = TABLE_SHORTCUT
        val whereSb = StringBuilder().apply {
            append(LEFT_BRACKETS)
            append("$albumTab.$TOTAL_COUNT $GREATER_THAN_ZERO")
            appendExcludeAlbumByFlag(queryArgs)?.let { excludeWhere ->
                append(AND)
                append(excludeWhere)
            }
            append(RIGHT_BRACKETS)
        }
        appendRequiredAlbumByFlag(queryArgs, whereSb)

        val shortcutAlbumId = AlbumShortcut.ALBUM_ID
        val albumTableId = ALBUM_ID
        val sqlBuilder = SQLSelectBuilder
            .build()
            .select(buildProjection(projections, true))
            .from(shortcutTab)
            .leftJoin(albumTab)
            .on("$albumTableId = $shortcutAlbumId")
            .where(whereSb.toString())
        appendGroupByIfNeed(queryArgs, sqlBuilder, shortcutAlbumId)
        appendOrderByAndLimitIfNeed(queryArgs, sqlBuilder)
        return sqlBuilder.toString()
    }

    /**
     * 不需要那些图集
     * ALBUM_ID NOT IN (CAMERA_ALBUM_ID,ALL_PICTURE_ALBUM_ID,ALL_PICTURE_ALBUM_ID)
     */
    @JvmStatic
    private fun appendExcludeAlbumByFlag(queryArgs: Bundle): String? {
        val excludeFlags = queryArgs.getInt(CONDITION_EXCLUDE_ALBUM_FLAGS_KEY, FLAG_CLEAR)
        val excludeAlbumIdSb = StringBuilder()
        if (excludeFlags > FLAG_CLEAR) {
            if (excludeFlags and FLAG_MAP_ALBUM == FLAG_MAP_ALBUM) {
                excludeAlbumIdSb.append(if (excludeAlbumIdSb.isNotEmpty()) ",$MAP_ALBUM_ID" else "$MAP_ALBUM_ID")
            }
            if (excludeFlags and FLAG_CLEANUP_ALBUM == FLAG_CLEANUP_ALBUM) {
                excludeAlbumIdSb.append(if (excludeAlbumIdSb.isNotEmpty()) ",$CLEAN_UP_ALBUM_ID" else "$CLEAN_UP_ALBUM_ID")
            }
            if (excludeFlags and FLAG_SAFE_BOX_ALBUM == FLAG_SAFE_BOX_ALBUM) {
                excludeAlbumIdSb.append(if (excludeAlbumIdSb.isNotEmpty()) ",$SAFE_BOX_ALBUM_ID" else "$SAFE_BOX_ALBUM_ID")
            }
            if (excludeFlags and FLAG_RECYCLE_BIN_ALBUM == FLAG_RECYCLE_BIN_ALBUM) {
                excludeAlbumIdSb.append(if (excludeAlbumIdSb.isNotEmpty()) ",$RECYCLE_BIN_ALBUM_ID" else "$RECYCLE_BIN_ALBUM_ID")
            }
            if (excludeFlags and FLAG_OLIVE_ALBUM == FLAG_OLIVE_ALBUM) {
                excludeAlbumIdSb.append(if (excludeAlbumIdSb.isNotEmpty()) ",$OLIVE_ALBUM_ID" else "$OLIVE_ALBUM_ID")
            }
            if (excludeFlags and FLAG_PORTRAIT_BLUR_ALBUM == FLAG_PORTRAIT_BLUR_ALBUM) {
                excludeAlbumIdSb.append(if (excludeAlbumIdSb.isNotEmpty()) ",$PORTRAIT_BLUR_ALBUM_ID" else "$PORTRAIT_BLUR_ALBUM_ID")
            }
            if (excludeFlags and FLAG_DOLBY_ALBUM == FLAG_DOLBY_ALBUM) {
                excludeAlbumIdSb.append(if (excludeAlbumIdSb.isNotEmpty()) ",$DOLBY_ALBUM_ID" else "$DOLBY_ALBUM_ID")
            }
            if (excludeFlags and FLAG_LOG_VIDEO_ALBUM == FLAG_LOG_VIDEO_ALBUM) {
                excludeAlbumIdSb.append(if (excludeAlbumIdSb.isNotEmpty()) ",$LOG_VIDEO_ALBUM_ID" else "$LOG_VIDEO_ALBUM_ID")
            }
        }
        if (excludeAlbumIdSb.isEmpty()) {
            return null
        }
        return "$LEFT_BRACKETS ${AlbumShortcut.ALBUM_ID} NOT IN ($excludeAlbumIdSb) $RIGHT_BRACKETS"
    }

    /**
     * 构建查询结果列
     * @param useShortcutAlbumId 是否返回shortcut表的albumId
     */
    @JvmStatic
    private fun buildProjection(projections: Array<String>?, useShortcutAlbumId: Boolean = false): String {
        val tmpProjections = if ((projections == null) || isAllProjection(projections)) SHORTCUT_ALBUM_PROJECTION + ALBUM_PROJECTION else projections
        val projectionSb = StringBuilder()
        val lastIndex = tmpProjections.size - 1
        tmpProjections.forEachIndexed { index, projection ->
            if (useShortcutAlbumId && (ALBUM_ID == projection.trim())) {
                projectionSb.append("${AlbumShortcut.ALBUM_ID} AS $ALBUM_ID")
            } else {
                projectionSb.append(projection)
            }
            if (lastIndex != index) {
                projectionSb.append(COMMA)
            }
        }
        return projectionSb.toString()
    }

    /**
     * 是否传入的是*或null 都当做返回所有
     */
    @JvmStatic
    private fun isAllProjection(projections: Array<String>): Boolean {
        if (projections.size == 1) {
            val projection = projections[0].trim()
            return (projection == "*") || (projection == "$TABLE_SHORTCUT.*") || (projection == "$TABLE_ALBUM.*")
        }
        return false
    }

    @JvmStatic
    private fun appendGroupByIfNeed(queryArgs: Bundle, sqlBuilder: SQLSelectBuilder, groupBy: String) {
        if (queryArgs.getBoolean(ALBUM_DISTINCT_KEY, false)) {
            sqlBuilder.groupBy(groupBy)
        }
    }

    /**
     * 根据是否需要去重返回不同的count语句
     * @param isDistinct 是否去重，由业务传递决定
     * @return count语句
     */
    @JvmStatic
    internal fun getCountAllProjection(isDistinct: Boolean): String {
        return if (isDistinct) {
            "$COUNT_DISTINCT ${AlbumShortcut.ALBUM_ID} $RIGHT_BRACKETS"
        } else {
            COUNT_ALL
        }
    }
}