/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IDao.java
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/05/22
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** h<PERSON><PERSON><PERSON>@Apps.Gallery		2020/02/24		1.0		OPLUS_ARCH_EXTENDS
 *********************************************************************************/

package com.oplus.gallery.foundation.dbaccess.dao;

import android.net.Uri;

import androidx.annotation.IntDef;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.WorkerThread;

import com.oplus.gallery.foundation.dbaccess.bean.BatchResult;
import com.oplus.gallery.foundation.dbaccess.req.BatchReq;
import com.oplus.gallery.foundation.dbaccess.req.BulkInsertReq;
import com.oplus.gallery.foundation.dbaccess.req.DataReq;
import com.oplus.gallery.foundation.dbaccess.req.DeleteReq;
import com.oplus.gallery.foundation.dbaccess.req.InsertReq;
import com.oplus.gallery.foundation.dbaccess.req.QueryReq;
import com.oplus.gallery.foundation.dbaccess.req.RawQueryReq;
import com.oplus.gallery.foundation.dbaccess.req.UpdateReq;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

@WorkerThread
public interface IDao {
    int ID_NONE = -1;
    int BATCH_SIZE = 500;
    String STR_CONTENT = "content";
    String STR_LIMIT = "limit";

    @Retention(RetentionPolicy.SOURCE)
    @IntDef({DaoType.GALLERY, DaoType.CLOUD, DaoType.CONFIG, DaoType.MEDIA_STORE, DaoType.CACHE, DaoType.WATERMARK_MASTER})
    @interface DaoType {
        int GALLERY = 0;
        int CLOUD = 1;
        int CONFIG = 2;
        int MEDIA_STORE = 3;
        int CACHE = 4;
        int WATERMARK_MASTER = 5;
    }

    @WorkerThread
    int bulkInsert(BulkInsertReq req);

    @WorkerThread
    BatchResult[] applyBatch(BatchReq req);

    @WorkerThread
    @Nullable
    Uri insert(InsertReq req);

    @WorkerThread
    int delete(DeleteReq req);

    @WorkerThread
    int update(UpdateReq req);

    @WorkerThread
    @Nullable
    <Result> Result query(QueryReq<Result> req);

    <Result> Result rawQuery(RawQueryReq<Result> req);

    @WorkerThread
    void registerNotify(@Nullable String volumeName, int tableType, boolean notifyForDescendants, DaoObserver observer);

    @WorkerThread
    void unregisterNotify(DaoObserver observer);

    boolean containsTable(int tableType);

    @NonNull
    Uri getDaoUri(@Nullable String volumeName, int tableType);

    @NonNull
    <Result> Uri getDaoUri(@NonNull DataReq<Result> req);

    @NonNull
    Uri.Builder getUriBuilder();

    @NonNull
    String getDaoTableByType(int tableType);
}
