/***********************************************************
 * * Copyright (C), 2018-2028, OPlus Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File       :  ScanTravelTable.kt
 * * Description:  创建旅行数据扫描的数据表、视图等
 * * Version: 1.0
 * * Date : 2025/04/09
 * * Author: pusong
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>         <version>  <desc>
 * *  pusong        2025/04/09     1.0        create
 ************************************************************/
package com.oplus.gallery.foundation.database.table

import android.database.sqlite.SQLiteDatabase
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryMedia
import com.oplus.gallery.foundation.database.store.GalleryStore.ScanTravel
import com.oplus.gallery.foundation.database.store.GalleryStore.ScanTravelColumns
import com.oplus.gallery.foundation.database.store.GalleryStore.ScanTravelView
import com.oplus.gallery.foundation.database.store.GalleryStore.ScanTravelViewColumns
import com.oplus.gallery.foundation.database.util.SQLGrammar.AFTER_DELETE_ON
import com.oplus.gallery.foundation.database.util.SQLGrammar.AFTER_UPDATE_OF
import com.oplus.gallery.foundation.database.util.SQLGrammar.AND
import com.oplus.gallery.foundation.database.util.SQLGrammar.AS
import com.oplus.gallery.foundation.database.util.SQLGrammar.BEGIN
import com.oplus.gallery.foundation.database.util.SQLGrammar.CASE
import com.oplus.gallery.foundation.database.util.SQLGrammar.COMMA
import com.oplus.gallery.foundation.database.util.SQLGrammar.COUNT_DISTINCT
import com.oplus.gallery.foundation.database.util.SQLGrammar.CREATE_INDEX
import com.oplus.gallery.foundation.database.util.SQLGrammar.CREATE_TABLE
import com.oplus.gallery.foundation.database.util.SQLGrammar.CREATE_TRIGGER
import com.oplus.gallery.foundation.database.util.SQLGrammar.CREATE_VIEW
import com.oplus.gallery.foundation.database.util.SQLGrammar.DEFAULT_ZERO
import com.oplus.gallery.foundation.database.util.SQLGrammar.DELETE_FROM
import com.oplus.gallery.foundation.database.util.SQLGrammar.DOT
import com.oplus.gallery.foundation.database.util.SQLGrammar.END
import com.oplus.gallery.foundation.database.util.SQLGrammar.EQUAL
import com.oplus.gallery.foundation.database.util.SQLGrammar.EQUAL_ONE
import com.oplus.gallery.foundation.database.util.SQLGrammar.EQUAL_ZERO
import com.oplus.gallery.foundation.database.util.SQLGrammar.FLOAT
import com.oplus.gallery.foundation.database.util.SQLGrammar.FROM
import com.oplus.gallery.foundation.database.util.SQLGrammar.GREATER_THAN_ZERO
import com.oplus.gallery.foundation.database.util.SQLGrammar.GROUP_BY
import com.oplus.gallery.foundation.database.util.SQLGrammar.INDEX_ON
import com.oplus.gallery.foundation.database.util.SQLGrammar.INNER_JOIN
import com.oplus.gallery.foundation.database.util.SQLGrammar.INTEGER
import com.oplus.gallery.foundation.database.util.SQLGrammar.LEFT_BRACKETS
import com.oplus.gallery.foundation.database.util.SQLGrammar.LEFT_BRACKETS_WITH_BLANK
import com.oplus.gallery.foundation.database.util.SQLGrammar.LONG
import com.oplus.gallery.foundation.database.util.SQLGrammar.NEW_DOT
import com.oplus.gallery.foundation.database.util.SQLGrammar.OLD_DOT
import com.oplus.gallery.foundation.database.util.SQLGrammar.ON
import com.oplus.gallery.foundation.database.util.SQLGrammar.PRIMARY_KEY
import com.oplus.gallery.foundation.database.util.SQLGrammar.RIGHT_BRACKETS
import com.oplus.gallery.foundation.database.util.SQLGrammar.RIGHT_BRACKETS_WITH_SEMICOLON
import com.oplus.gallery.foundation.database.util.SQLGrammar.SELECT
import com.oplus.gallery.foundation.database.util.SQLGrammar.SEMICOLON
import com.oplus.gallery.foundation.database.util.SQLGrammar.SET
import com.oplus.gallery.foundation.database.util.SQLGrammar.SPACE
import com.oplus.gallery.foundation.database.util.SQLGrammar.TEXT
import com.oplus.gallery.foundation.database.util.SQLGrammar.THEN
import com.oplus.gallery.foundation.database.util.SQLGrammar.UNDERLINE
import com.oplus.gallery.foundation.database.util.SQLGrammar.UPDATE
import com.oplus.gallery.foundation.database.util.SQLGrammar.WHEN
import com.oplus.gallery.foundation.database.util.SQLGrammar.WHERE
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag

/**
 * 创建旅行数据表，视图
 */
class ScanTravelTable : BaseTable {
    override fun updateDatabase(
        db: SQLiteDatabase?,
        oldVersion: Int,
        newVersion: Int
    ) {
        GLog.d(TAG, LogFlag.DL) { "updateDatabase, oldVersion: $oldVersion, newVersion: $newVersion" }
        db?.let {
            createTable(it)
            createIndex(it)
            createTrigger(it)
            createView(it)
        }
    }

    override fun downDatabase(
        db: SQLiteDatabase?,
        oldVersion: Int,
        newVersion: Int
    ) {
        GLog.d(TAG, LogFlag.DL) { "downDatabase, oldVersion=$oldVersion, newVersion=$newVersion" }
    }

    /**
     * 创建旅行数据表
     * CREATE TABLE IF NOT EXISTS
     * scan_travel (
     * _id INTEGER PRIMARY KEY AUTOINCREMENT,
     * _data TEXT,
     * media_type INTEGER,
     * invalid INTEGER DEFAULT 0,
     * is_recycled INTEGER DEFAULT 0,
     * travel_destination TEXT,
     * travel_start_time LONG,
     * travel_end_time LONG,
     * travel_id TEXT,
     * score FLOAT DEFAULT 0,
     * is_default_cover INTEGER DEFAULT 0,
     * flash_selection_model_version TEXT
     * );
     */
    private fun createTable(db: SQLiteDatabase) {
        val createTabSql = CREATE_TABLE +
                ScanTravel.TAB + LEFT_BRACKETS_WITH_BLANK +
                ScanTravelColumns._ID + INTEGER + PRIMARY_KEY + COMMA +
                ScanTravelColumns.PATH + TEXT + COMMA +
                ScanTravelColumns.MEDIA_TYPE + INTEGER + COMMA +
                ScanTravelColumns.INVALID + INTEGER + DEFAULT_ZERO + COMMA +
                ScanTravelColumns.IS_RECYCLED + INTEGER + DEFAULT_ZERO + COMMA +
                ScanTravelColumns.TRAVEL_DESTINATION + TEXT + COMMA +
                ScanTravelColumns.TRAVEL_START_TIME + LONG + COMMA +
                ScanTravelColumns.TRAVEL_END_TIME + LONG + COMMA +
                ScanTravelColumns.TRAVEL_ID + TEXT + COMMA +
                ScanTravelColumns.SCORE + FLOAT + DEFAULT_ZERO + COMMA +
                ScanTravelColumns.IS_DEFAULT_COVER + INTEGER + DEFAULT_ZERO + COMMA +
                ScanTravelColumns.FLASH_SELECTION_MODEL_VERSION + TEXT +
                RIGHT_BRACKETS_WITH_SEMICOLON
        db.execSQL(createTabSql)
    }

    /**
     * 创建索引
     * CREATE INDEX IF NOT EXISTS scan_travel__data_idx on scan_travel(_data)
     * CREATE INDEX IF NOT EXISTS scan_travel_travel_travel_destination_idx on scan_travel(travel_destination)
     * CREATE INDEX IF NOT EXISTS scan_travel_travel_travel_start_time_idx on scan_travel(travel_start_time)
     * CREATE INDEX IF NOT EXISTS scan_travel_travel_travel_start_end_idx on scan_travel(travel_end_time)
     * CREATE INDEX IF NOT EXISTS scan_travel_travel_id_idx on scan_travel(travel_id)
     * CREATE INDEX IF NOT EXISTS scan_travel_score_idx on scan_travel(score)
     * CREATE INDEX IF NOT EXISTS scan_travel_is_default_cover_idx on scan_travel(is_default_cover)
     * CREATE INDEX IF NOT EXISTS scan_travel_flash_selection_model_version_idx on scan_travel(flash_selection_model_version)
     */
    private fun createIndex(db: SQLiteDatabase) {
        // 路径
        db.execSQL(
            CREATE_INDEX + ScanTravel.TAB + UNDERLINE + ScanTravelColumns.PATH
                + INDEX_ON + ScanTravel.TAB + LEFT_BRACKETS + ScanTravelColumns.PATH + RIGHT_BRACKETS
        )
        // 旅行目的地
        db.execSQL(
            CREATE_INDEX + ScanTravel.TAB + UNDERLINE + ScanTravelColumns.TRAVEL_DESTINATION
                + INDEX_ON + ScanTravel.TAB + LEFT_BRACKETS + ScanTravelColumns.TRAVEL_DESTINATION + RIGHT_BRACKETS
        )
        // 旅行开始时间
        db.execSQL(
            CREATE_INDEX + ScanTravel.TAB + UNDERLINE + ScanTravelColumns.TRAVEL_START_TIME
                    + INDEX_ON + ScanTravel.TAB + LEFT_BRACKETS + ScanTravelColumns.TRAVEL_START_TIME + RIGHT_BRACKETS
        )
        // 旅行结束时间
        db.execSQL(
            CREATE_INDEX + ScanTravel.TAB + UNDERLINE + ScanTravelColumns.TRAVEL_END_TIME
                    + INDEX_ON + ScanTravel.TAB + LEFT_BRACKETS + ScanTravelColumns.TRAVEL_END_TIME + RIGHT_BRACKETS
        )
        // 旅行id
        db.execSQL(
            CREATE_INDEX + ScanTravel.TAB + UNDERLINE + ScanTravelColumns.TRAVEL_ID
                    + INDEX_ON + ScanTravel.TAB + LEFT_BRACKETS + ScanTravelColumns.TRAVEL_ID + RIGHT_BRACKETS
        )
        // 封面得分
        db.execSQL(
            CREATE_INDEX + ScanTravel.TAB + UNDERLINE + ScanTravelColumns.SCORE
                    + INDEX_ON + ScanTravel.TAB + LEFT_BRACKETS + ScanTravelColumns.SCORE + RIGHT_BRACKETS
        )
        // 是否默认封面
        db.execSQL(
            CREATE_INDEX + ScanTravel.TAB + UNDERLINE + ScanTravelColumns.IS_DEFAULT_COVER
                    + INDEX_ON + ScanTravel.TAB + LEFT_BRACKETS + ScanTravelColumns.IS_DEFAULT_COVER + RIGHT_BRACKETS
        )
        // 闪速选片版本号
        db.execSQL(
            CREATE_INDEX + ScanTravel.TAB + UNDERLINE + ScanTravelColumns.FLASH_SELECTION_MODEL_VERSION
                    + INDEX_ON + ScanTravel.TAB + LEFT_BRACKETS + ScanTravelColumns.FLASH_SELECTION_MODEL_VERSION + RIGHT_BRACKETS
        )
    }

    /**
     * 创建触发器
     */
    private fun createTrigger(db: SQLiteDatabase) {
        db.execSQL(getTriggerSqlForLocalMediaUpdateData())
        db.execSQL(getTriggerSqlForLocalMediaUpdateInvalid())
        db.execSQL(getTriggerSqlForLocalMediaUpdateIsTrashed())
        db.execSQL(getTriggerSqlForLocalMediaDeleteItem())
    }

    /**
     * _data更新触发器
     * CREATE TRIGGER IF NOT EXISTS scan_travel_update_sync_local_media_data
     *  AFTER UPDATE OF _original_data
     *  ON local_media
     *  begin
     *      UPDATE scan_travel
     *      SET _data = new._original_data
     *      where _data = old._original_data;
     *  end
     */
    private fun getTriggerSqlForLocalMediaUpdateData(): String {
        return CREATE_TRIGGER + SCAN_TRAVEL_UPDATE_SYNC_LOCAL_MEDIA_DATA +
                AFTER_UPDATE_OF + GalleryColumns.LocalColumns.ORIGINAL_DATA +
                ON + GalleryMedia.TAB +
                BEGIN +
                UPDATE + ScanTravel.TAB +
                SET + ScanTravelColumns.PATH + EQUAL + NEW_DOT + GalleryColumns.LocalColumns.ORIGINAL_DATA +
                WHERE + ScanTravelColumns.PATH + EQUAL + OLD_DOT + GalleryColumns.LocalColumns.ORIGINAL_DATA + SEMICOLON +
                END
    }

    /**
     * invalid更新触发器
     * CREATE TRIGGER IF NOT EXISTS scan_travel_update_sync_local_media_invalid
     *  AFTER UPDATE OF invalid
     *  ON local_media
     *  begin
     *      UPDATE scan_travel
     *      SET invalid = new.invalid
     *      where _data = new._original_data;
     *  end
     */
    private fun getTriggerSqlForLocalMediaUpdateInvalid(): String {
        return CREATE_TRIGGER + SCAN_TRAVEL_UPDATE_SYNC_LOCAL_MEDIA_INVALID +
                AFTER_UPDATE_OF + GalleryColumns.LocalColumns.INVALID +
                ON + GalleryMedia.TAB +
                BEGIN +
                UPDATE + ScanTravel.TAB +
                SET + ScanTravelColumns.INVALID + EQUAL + NEW_DOT + GalleryColumns.LocalColumns.INVALID +
                WHERE + ScanTravelColumns.PATH + EQUAL + NEW_DOT + GalleryColumns.LocalColumns.ORIGINAL_DATA + SEMICOLON +
                END
    }

    /**
     * is_trashed更新触发器
     * CREATE TRIGGER IF NOT EXISTS scan_travel_update_sync_local_media_is_trashed
     *  AFTER UPDATE OF is_trashed
     *  ON local_media
     *  begin
     *      UPDATE scan_travel
     *      SET is_trashed = new.is_trashed
     *      where _data = new._original_data;
     *  end
     */
    private fun getTriggerSqlForLocalMediaUpdateIsTrashed(): String {
        return CREATE_TRIGGER + SCAN_TRAVEL_UPDATE_SYNC_LOCAL_MEDIA_IS_TRASHED +
                AFTER_UPDATE_OF + GalleryColumns.LocalColumns.IS_TRASHED +
                ON + GalleryMedia.TAB +
                BEGIN +
                UPDATE + ScanTravel.TAB +
                SET + ScanTravelColumns.IS_RECYCLED + EQUAL + NEW_DOT + GalleryColumns.LocalColumns.IS_TRASHED +
                WHERE + ScanTravelColumns.PATH + EQUAL + NEW_DOT + GalleryColumns.LocalColumns.ORIGINAL_DATA + SEMICOLON +
                END
    }

    /**
     * 删除触发器
     * CREATE TRIGGER IF NOT EXISTS scan_travel_delete_sync_local_media_delete_item
     *  AFTER UPDATE ON local_media
     *  begin
     *      DELETE FROM scan_travel
     *      where _data = old._original_data;
     *  end
     */
    private fun getTriggerSqlForLocalMediaDeleteItem(): String {
        return CREATE_TRIGGER + SCAN_TRAVEL_DELETE_SYNC_LOCAL_MEDIA_DELETE_ITEM +
                AFTER_DELETE_ON + GalleryMedia.TAB +
                BEGIN +
                DELETE_FROM + ScanTravel.TAB +
                WHERE + ScanTravelColumns.PATH + EQUAL + OLD_DOT + GalleryColumns.LocalColumns.ORIGINAL_DATA + SEMICOLON +
                END
    }

    /**
     * 创建视图
     * CREATE VIEW IF NOT EXISTS scan_travel_view as
     * select
     *     travel._id,
     *     local._data,
     *     local.media_id,
     *     travel.media_type,
     *     local.datetaken,
     *     travel.travel_destination,
     *     travel.travel_start_time,
     *     travel.travel_end_time,
     *     travel.travel_id,
     *     travel.score,
     *     travel.is_default_cover,
     *     local.is_favorite,
     *     travel_group.count,
     *     travel_group.image_count,
     *     travel_group.video_count,
     *     (travel.is_default_cover * 1000 + local.is_favorite * 100 + travel.score) as best_score,
     * FROM scan_travel travel
     * INNER JOIN local_media local
     * ON travel._data = local._data
     * INNER JOIN (
     *     select
     *         travel.travel_id,
     *         count(DISTINCT travel._data) AS count,
     *         count(DISTINCT  CASE  WHEN travel.media_type = 1  THEN local.media_id END) AS image_count,
     *         count(DISTINCT  CASE  WHEN travel.media_type = 3  THEN local.media_id END) AS video_count
     *     From scan_travel travel
     *     INNER JOIN local_media local
     *     ON travel._data = local._data
     *     WHERE travel.invalid = 0
     *         AND travel.is_trashed = 0
     *         AND local.media_id > 0
     *     GROUP BY travel.travel_id
     * ) travel_group
     * ON travel.travel_id = travel_group.travel_id
     * WHERE travel.invalid = 0
     *     AND travel.is_trashed = 0
     *     AND local.media_id > 0 ;
     */
    private fun createView(db: SQLiteDatabase) {
        val travelGroup = "travel_group"
        val travel = "travel"
        val local = "local"
        val equalThree = " = 3 "
        val createViewSql = CREATE_VIEW + ScanTravelView.TAB + AS +
                SELECT +
                travel + DOT + ScanTravelColumns._ID + COMMA +
                local + DOT + GalleryColumns.LocalColumns.DATA + COMMA +
                local + DOT + GalleryColumns.LocalColumns.MEDIA_ID + COMMA +
                travel + DOT + ScanTravelColumns.MEDIA_TYPE + COMMA +
                local + DOT + GalleryColumns.LocalColumns.DATE_TAKEN + COMMA +
                travel + DOT + ScanTravelColumns.TRAVEL_DESTINATION + COMMA +
                travel + DOT + ScanTravelColumns.TRAVEL_START_TIME + COMMA +
                travel + DOT + ScanTravelColumns.TRAVEL_END_TIME + COMMA +
                travel + DOT + ScanTravelColumns.TRAVEL_ID + COMMA +
                travel + DOT + ScanTravelColumns.SCORE + COMMA +
                travel + DOT + ScanTravelColumns.IS_DEFAULT_COVER + COMMA +
                local + DOT + GalleryColumns.LocalColumns.IS_FAVORITE + COMMA +
                travelGroup + DOT + ScanTravelViewColumns.COUNT  + COMMA +
                travelGroup + DOT + ScanTravelViewColumns.IMAGE_COUNT + COMMA +
                travelGroup + DOT + ScanTravelViewColumns.VIDEO_COUNT + COMMA +
                LEFT_BRACKETS + travel + DOT + ScanTravelColumns.IS_DEFAULT_COVER + "* 1000 +" +
                local + DOT + GalleryColumns.LocalColumns.IS_FAVORITE + "* 100 +" +
                travel + DOT + ScanTravelColumns.SCORE + RIGHT_BRACKETS + AS + ScanTravelViewColumns.FINAL_SCORE +
                FROM + ScanTravel.TAB + SPACE + travel +
                INNER_JOIN + GalleryMedia.TAB + SPACE + local +
                ON + travel + DOT + ScanTravelColumns.PATH + EQUAL + local + DOT + GalleryColumns.LocalColumns.DATA +
                INNER_JOIN + LEFT_BRACKETS +
                SELECT +
                travel + DOT + ScanTravelColumns.TRAVEL_ID + COMMA +
                COUNT_DISTINCT + travel + DOT + ScanTravelColumns.PATH + RIGHT_BRACKETS +
                AS + ScanTravelViewColumns.COUNT + COMMA +
                COUNT_DISTINCT + CASE + WHEN + travel + DOT + ScanTravelColumns.MEDIA_TYPE + EQUAL_ONE +
                THEN + local + DOT + GalleryColumns.LocalColumns.MEDIA_ID + END + RIGHT_BRACKETS +
                AS + ScanTravelViewColumns.IMAGE_COUNT + COMMA +
                COUNT_DISTINCT + CASE + WHEN + travel + DOT + ScanTravelColumns.MEDIA_TYPE + equalThree +
                THEN +  local + DOT + GalleryColumns.LocalColumns.MEDIA_ID + END + RIGHT_BRACKETS +
                AS + ScanTravelViewColumns.VIDEO_COUNT +
                FROM + ScanTravel.TAB + SPACE + travel +
                INNER_JOIN + GalleryMedia.TAB + SPACE + local +
                ON + travel + DOT + ScanTravelColumns.PATH + EQUAL + local + DOT + GalleryColumns.LocalColumns.DATA +
                WHERE + travel + DOT + ScanTravelColumns.INVALID + EQUAL_ZERO +
                AND + travel + DOT + ScanTravelColumns.IS_RECYCLED + EQUAL_ZERO +
                AND + local + DOT + GalleryColumns.LocalColumns.MEDIA_ID + GREATER_THAN_ZERO +
                GROUP_BY + travel + DOT + ScanTravelColumns.TRAVEL_ID +
                RIGHT_BRACKETS + SPACE + travelGroup +
                ON + travel + DOT + ScanTravelColumns.TRAVEL_ID + EQUAL + travelGroup + DOT + ScanTravelColumns.TRAVEL_ID +
                WHERE + travel + DOT + ScanTravelColumns.INVALID + EQUAL_ZERO +
                AND + travel + DOT + ScanTravelColumns.IS_RECYCLED + EQUAL_ZERO +
                AND + local + DOT + GalleryColumns.LocalColumns.MEDIA_ID + GREATER_THAN_ZERO +
                SEMICOLON
        db.execSQL(createViewSql)
    }

    companion object {
        const val TAG = "ScanTravelTable"
        const val SCAN_TRAVEL_UPDATE_SYNC_LOCAL_MEDIA_DATA = "scan_travel_update_sync_local_media_data"
        const val SCAN_TRAVEL_UPDATE_SYNC_LOCAL_MEDIA_INVALID = "scan_travel_update_sync_local_media_invalid"
        const val SCAN_TRAVEL_UPDATE_SYNC_LOCAL_MEDIA_IS_TRASHED = "scan_travel_update_sync_local_media_is_trashed"
        const val SCAN_TRAVEL_DELETE_SYNC_LOCAL_MEDIA_DELETE_ITEM = "scan_travel_delete_sync_local_media_delete_item"
    }
}