/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ConfigStore
 ** Description:
 ** Version: 1.0
 ** Date : 2020/05/16
 ** Author: biao.chen@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  biao.chen@Apps.Gallery3D  2020/05/16        build this module
 ********************************************************************************/

package com.oplus.gallery.foundation.database.store;

import android.net.Uri;
import android.provider.BaseColumns;

public final class ConfigStore {
    public static final String AUTHORITY = "com.oplus.gallery.database.provider.config";
    private static final String CONFIG_CONTENT_URI = "content://" + AUTHORITY;

    private ConfigStore() {
    }

    public static class GalleryAllowListColumns implements BaseColumns {
        public static final String FOLDER_PATH = "folder_path";
        public static final String BUCKET_ID = GalleryStore.GalleryColumns.LocalColumns.BUCKET_ID;
        public static final String IS_FORCE = "is_force";
        public static final String MEDIA_TYPE = GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE;
        public static final String ALBUM_TYPE = "album_type";
        public static final String SET_ORDER = "set_order";
        public static final String FIX_SHOW = "fix_show";
        public static final String MERGE_FLAG = "merge_flag";

        private GalleryAllowListColumns() {
        }
    }

    public static class GalleryBlockListColumns implements BaseColumns {
        public static final String FOLDER_PATH = GalleryAllowListColumns.FOLDER_PATH;
        public static final String BUCKET_ID = GalleryStore.GalleryColumns.LocalColumns.BUCKET_ID;
        public static final String IS_FORCE = "is_force";
        public static final String DATE_MODIFIED = "date_modified";

        private GalleryBlockListColumns() {
        }
    }

    public static class PhotoAllowListColumns implements BaseColumns {
        public static final String FOLDER_PATH = GalleryAllowListColumns.FOLDER_PATH;
        /**
         * This is not the real media_type, it`s media_type_support.
         */
        public static final String MEDIA_TYPE_SUPPORT = GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE;

        private PhotoAllowListColumns() {
        }
    }

    public static class CloudAllowListColumns implements BaseColumns {
        public static final String DIR_NAME = "_dir_name";
        public static final String DIR_PATH = "_dir_path";
        public static final String DIR_TYPE = "_dir_type";
        public static final String DIR_FILE_COUNT = "_dir_file_count";
        public static final String DIR_CONTENT_TYPE = "_dir_content_type";
        public static final String CREATE_TIME = "_create_time";
        public static final String UPDATE_TIME = "_update_time";
        public static final String DIR_MODIFY_TIME = "_modify_time";
        public static final String SWITCH_STATE = "_switch_state";
        public static final String CONFIG_STATUS = "_configStatus";
        public static final String OPERATION = "_operation";
        public static final String GLOBAL_ID = "_global_id";
        public static final String META_DATA = "_meta_data";
        public static final String DIR_DISPLAY_NAME = "_dir_display_name";
        public static final String SYS_VERSION = "_sys_version";

        private CloudAllowListColumns() {
        }
    }

    public static class AlbumNameMultiLangColumns implements BaseColumns {
        public static final String FOLDER_PATH = GalleryAllowListColumns.FOLDER_PATH;
        public static final String BUCKET_ID = GalleryStore.GalleryColumns.LocalColumns.BUCKET_ID;
        public static final String NAME_EN = "name_en";
        public static final String NAME_CN = "name_ch";
        public static final String NAME_TW = "name_tw";
        public static final String CUSTOM_NAME = "custom_name";
        public static final String RENAME_TIME = "rename_time";

        private AlbumNameMultiLangColumns() {
        }
    }

    public static class GeoConfigColumns implements BaseColumns {
        public static final String NAME = "name";
        public static final String MATCHS = "matchs";
        public static final String MATCH_LEVELS = "match_levels";
        public static final String TRANSFORMS = "transforms";
        public static final String BLOCK = "block";
        public static final String COPY = "copy";
        public static final String MOVE = "move";
        public static final String KEEP_SHOW = "keep_show";

        private GeoConfigColumns() {
        }
    }

    // about config
    public static final class GalleryAllowList extends GalleryAllowListColumns {
        public static final String TAB = "gallery_allow_list";

        public static Uri getContentUri() {
            return Uri.parse(CONFIG_CONTENT_URI + "/" + TAB);
        }
    }

    public static final class GalleryBlockList extends GalleryBlockListColumns {
        public static final String TAB = "gallery_block_list";

        public static Uri getContentUri() {
            return Uri.parse(CONFIG_CONTENT_URI + "/" + TAB);
        }
    }

    public static final class PhotoAllowList extends PhotoAllowListColumns {
        public static final String TAB = "photo_allow_list";

        public static Uri getContentUri() {
            return Uri.parse(CONFIG_CONTENT_URI + "/" + TAB);
        }
    }

    public static final class CloudAllowList extends CloudAllowListColumns {
        public static final String TAB = "cloud_allow_list";

        public static Uri getContentUri() {
            return Uri.parse(CONFIG_CONTENT_URI + "/" + TAB);
        }
    }

    public static final class AlbumNameMultiLang extends AlbumNameMultiLangColumns {
        public static final String TAB = "album_name_multi_language";

        public static Uri getContentUri() {
            return Uri.parse(CONFIG_CONTENT_URI + "/" + TAB);
        }
    }

    public static final class GeoConfigList extends GeoConfigColumns {
        public static final String TAB = "geo_config_list";

        public static Uri getContentUri() {
            return Uri.parse(CONFIG_CONTENT_URI + "/" + TAB);
        }
    }

    public static final class RawQuery {
        public static final String TAB = "raw_query";

        private RawQuery() {
        }

        public static Uri getContentUri() {
            return Uri.parse(CONFIG_CONTENT_URI + "/" + TAB);
        }
    }

    public static class LabelDicColumns implements BaseColumns {
        /**
         * 场景ID
         */
        public static final String SCENE_ID = "scene_id";

        /**
         * 场景名
         */
        public static final String NAME = "name";

        /**
         * 关键字
         */
        public static final String KEYWORD = "keyword";

        /**
         * 父级的场景id集(可能有多个) 格式:sceneId1,sceneId2,sceneId3,....
         * 默认-1 表示此场景没有父级
         */
        public static final String PARENT_IDS = "parent_ids";

        /**
         * 子级的场景id集 格式:sceneId1,sceneId2,sceneId3,....
         * 默认-1 表示此场景没有子级
         */
        public static final String CHILD_IDS = "child_ids";

        /**
         * 同义词场景id集（也称作别名场景） 格式:sceneId1,sceneId2,sceneId3,....
         * 默认-1 表示此场景没有别名
         */
        public static final String SYNONYM_IDS = "synonym_ids";
        /**
         * 暂时无用,使用未知
         */
        public static final String IS_SHOW = "is_show";
        /**
         * 此label记录所属的词典类型
         * 如：是关系词典（label_relationship_map.dic），还是同义词词典(label_list_xx_xx.dic 如label_list_zh_tw.dic)
         */
        public static final String DIC_TYPE = "dic_type";

        private LabelDicColumns() {
            // do nothing
        }

    }

    /**
     * 标签词典表
     */
    public static final class LabelDic extends LabelDicColumns {
        public static final int INVALID_SCENE_ID = -1;
        public static final String TAB = "label_dic";

        public static Uri getContentUri() {
            return Uri.parse(CONFIG_CONTENT_URI + "/" + TAB);
        }
    }

    /**
     * 个人信息清单收集情况表
     */
    public static class PersonalInfoCollectionColumns implements BaseColumns {
        /**
         * 表名
         */
        public static final String TAB = "personal_info_collection";

        /**
         * 日期：如20241012
         */
        public static final String DATE = "date_ymd";

        /**
         * 日期毫秒数
         */
        public static final String DATE_MILLIS = "date_millis";

        /**
         * 次数：1
         */
        public static final String COUNT = "request_count";

        /**
         * 字段名称:
         */
        public static final String FIELD_NAME = "field_name";

        /**
         * 文件名称-默认中文
         */
        public static final String FILE_NAME_CN = "file_name_cn";

        /**
         * 文件名称-英文
         */
        public static final String FILE_NAME_EN = "file_name_en";

        /**
         * 字段名称集合，以逗号分隔：文件名称保存时需要使用，可为空
         */
        public static final String FIELD_NAME_LIST = "field_name_list";

        /**
         * 字段名称相关值定义
         */
        public static class FileName {
            /**
             * 手机号码
             */
            public static final String TEL_NUMBER = "tel_number";

            /**
             * 人脸轮廓
             */
            public static final String FACIAL_CONTOUR = "facial_contour";

            /**
             * 经纬度信息
             */
            public static final String LOCATION = "location";

            /**
             * 设备识别码 DUID
             */
            public static final String DUID = "duid";

            /**
             * 设备品牌
             */
            public static final String DEVICE_BRAND = "device_brand";

            /**
             * OS 版本
             */
            public static final String OS_VERSION = "os_version";

            /**
             * 机型
             */
            public static final String MODEL = "model";

            /**
             * 设备型号
             */
            public static final String DEVICE_TYPE = "device_type";

            /**
             * ROM 版本
             */
            public static final String ROM_VERSION = "rom_version";

            /**
             * APP 版本
             */
            public static final String APP_VERSION = "app_version";

            /**
             * 运营商信息
             */
            public static final String OPERATOR_INFORMATION = "operator_information";

            /**
             * 语言和地区设置
             */
            public static final String LANGUAGE_AND_REGIONAL_SETTINGS = "language_and_regional_settings";

            /**
             * 图片中的文字
             */
            public static final String PICTURE_TEXT = "picture_text";

            /**
             * 图片文件
             */
            public static final String PICTURE_FILE = "picture_file";

            /**
             * 错误日志报告
             */
            public static final String ERROR_LOG_REPORT = "error_log_report";

            /**
             * 埋点信息
             */
            public static final String BURIED_INFORMATION = "buried_information";

            /**
             * 反馈内容附件（文字、图片）
             */
            public static final String FEEDBACK_CONTEXT_ATTACHMENT = "feedback_context_attachment";

            /**
             * 功能更新与素材资源下载
             */
            public static final String INFORMATION_SHARE_WITH_FUNCTION = "information_share_with_function";
        }
    }
    public static final class PersonalInfoCollectionPages extends PersonalInfoCollectionColumns {
        public static final String TAB = "personal_info_collection";


        public static Uri getContentUri() {
            return Uri.parse(CONFIG_CONTENT_URI + "/" + TAB);
        }
    }

    /**
     * poi信息表
     */
    public static final class PoiRoute extends PoiColumns {
        public static final String TAB = "poi_route";

        public static Uri getContentUri() {
            return Uri.parse(CONFIG_CONTENT_URI + "/" + TAB);
        }
    }

    /**
     * poi_route表列名
     */
    public static class PoiColumns implements BaseColumns {
        /**
         * poi名称
         */
        public static final String NAME = "name";
        /**
         * 别称，格式：xxx,xxxx,xxxxx
         */
        public static final String ALIAS = "alias";
        /**
         * poi完整地址
         */
        public static final String ADDRESS = "address";
        /**
         * 国家名,例如：CN,US
         */
        public static final String LOCALE = "locale";
        /**
         * 语言名，例如：zh-CN,en-US
         */
        public static final String LANGUAGE = "language";
        /**
         * 维度，例如：30.14564878978
         */
        public static final String LAT = "latitude";
        /**
         * 经度，例如：107.1546788451
         */
        public static final String LNG = "longitude";
        /**
         * 图片gps_key
         */
        public static final String GPS_KEY = "gps_key";
        /**
         * poi唯一值，例如：17451212314
         */
        public static final String POI_HASH = "poi_hash";
        /**
         * poi地址范围
         */
        public static final String RADIUS = "radius";

        private PoiColumns() {
        }
    }
}
