/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - BucketEntry.kt
 ** Description: 图集实体
 ** Version: 1.0
 ** Date: 2024/4/3
 ** Author: <PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery     2024/4/3     1.0         OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.foundation.database.album.entry

import android.database.Cursor
import com.oplus.gallery.foundation.database.album.shouldReplaceCoverInfo
import com.oplus.gallery.foundation.database.store.CacheStore.AlbumCountColumns
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns
import com.oplus.gallery.foundation.database.util.DatabaseUtils
import com.oplus.gallery.foundation.database.util.SQLGrammar.AS
import com.oplus.gallery.foundation.database.util.SQLGrammar.LEFT_BRACKETS
import com.oplus.gallery.foundation.database.util.SQLGrammar.MAX
import com.oplus.gallery.foundation.database.util.SQLGrammar.RIGHT_BRACKETS
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING

/**
 * albumId生成规则：
 *  1. 虚拟图集和占位图集的albumId由硬编码生成 @see com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum
 *  2. 合并图集:在名单定义合并的由mergePath生成
 * 图集实体类
 * @param bucketId
 * 1. 虚拟图集、合并图集、占位图集均已处理为albumId。
 * 2. 单实体图集: id为bucketId
 * @param bucketName 图集显示名称
 * @param bucketPath 相对路径，不带root 如:/Pictures/WeiXin
 * @param dir 实体图集目录,虚拟图集没有目录：如：
 */
open class BucketEntry(
    @JvmField val bucketId: Int,
    @JvmField var bucketName: String,
    @JvmField var bucketPath: String,
    @JvmField var dir: String = EMPTY_STRING
) {
    @JvmField
    val bucketIdList: MutableList<Int> = mutableListOf(bucketId)

    /**
     * 回收站总数(包含云端和本地)
     */
    var trashedCount = 0
        set(value) {
            field = ensureCountPositive(value)
        }

    /**
     * 非回收站总数(不含回收站的):照片和视频总数(正常显示总数):
     *
     * 使用场景:相册-新建图集-选图(支持所有类型)
     */
    var count = 0
        set(value) {
            field = ensureCountPositive(value)
        }

    /**
     * 照片总数(包含云端和本地)
     *
     * 使用场景:随身卡包-选图(仅支持图片)
     */
    var imageCount = 0
        set(value) {
            field = ensureCountPositive(value)
        }

    /**
     * 本地照片总数:三方选图:联系人-选图
     */
    var imageLocalCount = 0
        set(value) {
            field = ensureCountPositive(value)
        }

    /**
     * 本地视频数
     *
     * 使用场景:三方浏览器-选图页面-选择视频
     */
    var videoLocalCount = 0
        set(value) {
            field = ensureCountPositive(value)
        }

    /**
     * GIF总数(包含云端和本地)
     *
     * 使用场景:桌面卡片-选图(不支持gif和视频) 用照片总数-gif数即可
     */
    var gifCount = 0
        set(value) {
            field = ensureCountPositive(value)
        }

    /**
     * jpeg总数(包含云端和本地)
     *
     * 使用场景:新建回忆-选图支持jpeg
     */
    var jpegCount = 0
        set(value) {
            field = ensureCountPositive(value)
        }

    /**
     * HEIF总数(包含云端和本地))
     *
     * 使用场景:新建回忆-选图若手机支持heif,则选图就支持heif,不支持则需要过滤掉heif
     */
    var heifCount = 0
        set(value) {
            field = ensureCountPositive(value)
        }

    /**
     * 单实体图集的封面
     */
    @JvmField
    var coverItem: CoverItem? = null

    init {
        addBucketId(bucketId)
    }

    fun loadCountFromCache(cursor: Cursor, cacheColumnIndexMap: Map<String, Int>) {
        count = DatabaseUtils.getValidInt(cacheColumnIndexMap[AlbumCountColumns.COUNT], cursor)
        imageLocalCount = DatabaseUtils.getValidInt(cacheColumnIndexMap[AlbumCountColumns.LOCAL_IMAGE], cursor)
        videoLocalCount = DatabaseUtils.getValidInt(cacheColumnIndexMap[AlbumCountColumns.LOCAL_VIDEO], cursor)
        imageCount = DatabaseUtils.getValidInt(cacheColumnIndexMap[AlbumCountColumns.IMAGE], cursor)
        gifCount = DatabaseUtils.getValidInt(cacheColumnIndexMap[AlbumCountColumns.GIF], cursor)
        jpegCount = DatabaseUtils.getValidInt(cacheColumnIndexMap[AlbumCountColumns.JPEG], cursor)
        heifCount = DatabaseUtils.getValidInt(cacheColumnIndexMap[AlbumCountColumns.HEIF], cursor)
    }

    fun loadCountAndCoverFromLocal(cursor: Cursor, columnIndexMap: Map<String, Int>) {
        columnIndexMap[COLUMN_TRASHED_COUNT_NAME]?.takeIf { it > INVALID_COLUMN_INDEX }?.let {
            trashedCount = cursor.getInt(it)
        }
        columnIndexMap[COLUMN_COUNT_NAME]?.takeIf { it > INVALID_COLUMN_INDEX }?.let {
            count = cursor.getInt(it)
        }
        columnIndexMap[COLUMN_IMG_COUNT_NAME]?.takeIf { it > INVALID_COLUMN_INDEX }?.let {
            imageCount = cursor.getInt(it)
        }
        columnIndexMap[COLUMN_IMG_LOCAL_COUNT_NAME]?.takeIf { it > INVALID_COLUMN_INDEX }?.let {
            imageLocalCount = cursor.getInt(it)
        }
        columnIndexMap[COLUMN_VIDEO_LOCAL_COUNT_NAME]?.takeIf { it > INVALID_COLUMN_INDEX }?.let {
            videoLocalCount = cursor.getInt(it)
        }
        columnIndexMap[COLUMN_GIF_COUNT_NAME]?.takeIf { it > INVALID_COLUMN_INDEX }?.let {
            gifCount = cursor.getInt(it)
        }
        columnIndexMap[COLUMN_JPEG_COUNT_NAME]?.takeIf { it > INVALID_COLUMN_INDEX }?.let {
            jpegCount = cursor.getInt(it)
        }
        columnIndexMap[COLUMN_HEIF_COUNT_NAME]?.takeIf { it > INVALID_COLUMN_INDEX }?.let {
            heifCount = cursor.getInt(it)
        }
        coverItem = CoverItem.fromCursor(cursor, columnIndexMap)
    }

    fun addBucketId(id: Int) {
        if (!bucketIdList.contains(id)) {
            bucketIdList.add(id)
        }
    }

    fun addBucketId(idList: List<Int>) {
        idList.forEach {
            addBucketId(it)
        }
    }

    fun appendCountAndCover(other: BucketEntry, itemSortType: Int, albumId: Int) {
        count += other.count
        imageLocalCount += other.imageLocalCount
        videoLocalCount += other.videoLocalCount
        imageCount += other.imageCount
        gifCount += other.gifCount
        jpegCount += other.jpegCount
        heifCount += other.heifCount
        if (coverItem?.shouldReplaceCoverInfo(other.coverItem, itemSortType,  albumId) == true) {
            coverItem = other.coverItem
        }
    }

    /**
     * 防呆:确保count的数量不为负数
     */
    private fun ensureCountPositive(value: Int): Int {
        return if (value < 0) 0 else value
    }

    override fun hashCode(): Int {
        return bucketId
    }

    override fun equals(other: Any?): Boolean {
        if (other !is BucketEntry) {
            return false
        }
        if (other.bucketId != bucketId) {
            return false
        }
        val size = other.bucketIdList.size
        if (size != bucketIdList.size) {
            return false
        }
        for (i in 0 until size) {
            if (other.bucketIdList[i] != bucketIdList[i]) {
                return false
            }
        }
        return true
    }

    override fun toString(): String {
        return "B(" +
            "bId=$bucketId," +
            "$bucketPath," +
            "tCount=$trashedCount," +
            "count=$count," +
            "img=$imageCount," +
            "gif=$gifCount," +
            "jpg=$jpegCount," +
            "heif=$heifCount) "
    }

    companion object {
        /**
         * 回收站总数(包含云端和本地)
         */
        const val COLUMN_TRASHED_COUNT_NAME = "trashedCount"

        const val COLUMN_TRASHED_COUNT = "COUNT (CASE WHEN is_trashed = 1 THEN 1 ELSE NULL END) AS $COLUMN_TRASHED_COUNT_NAME"

        /**
         * 照片和视频总数(包含云端和本地)
         *
         * 使用场景:场景新建图集-选图(支持所有类型)
         */
        const val COLUMN_COUNT_NAME = "totalCount"

        const val COLUMN_COUNT = "COUNT (CASE WHEN is_trashed != 1 THEN 1 ELSE NULL END) AS $COLUMN_COUNT_NAME"

        /**
         * 照片总数(包含云端和本地)
         *
         * 使用场景:随身卡包-选图(仅支持图片)
         */
        const val COLUMN_IMG_COUNT_NAME = "imgCount"

        const val COLUMN_IMG_COUNT =
            "COUNT (CASE WHEN is_trashed != 1 AND media_type = 1 THEN 1 ELSE NULL END) AS $COLUMN_IMG_COUNT_NAME"

        /**
         * 本地照片总数
         *
         * 使用场景:三方选图:联系人-选图
         */
        const val COLUMN_IMG_LOCAL_COUNT_NAME = "imgLocalCount"
        const val COLUMN_IMG_LOCAL_COUNT =
            "COUNT (CASE WHEN is_trashed != 1 AND media_type = 1 AND media_id > 0 THEN 1 ELSE NULL END) AS $COLUMN_IMG_LOCAL_COUNT_NAME"

        /**
         * 本地视频数
         *
         * 使用场景:三方浏览器-选图页面-仅支持选择本地视频
         */
        const val COLUMN_VIDEO_LOCAL_COUNT_NAME = "videoLocalCount"
        const val COLUMN_VIDEO_LOCAL_COUNT =
            "COUNT (CASE WHEN is_trashed != 1 AND media_type = 3 AND media_id > 0 THEN 1 ELSE NULL END) AS $COLUMN_VIDEO_LOCAL_COUNT_NAME"

        /**
         * GIF总数(包含云端和本地):
         *
         * 使用场景:桌面卡片-选图(不支持gif和视频) 用照片总数-gif数即可
         */
        const val COLUMN_GIF_COUNT_NAME = "gifCount"

        const val COLUMN_GIF_COUNT =
            ("COUNT (CASE WHEN is_trashed != 1 AND media_type = 1  AND mime_type COLLATE NOCASE = 'image/gif' THEN 1 ELSE NULL END) AS "
                + COLUMN_GIF_COUNT_NAME)

        /**
         * jpeg总数(包含云端和本地)
         *
         * 使用场景:相册-新建回忆-选图支持jpeg
         */
        const val COLUMN_JPEG_COUNT_NAME = "jpegCount"
        const val COLUMN_JPEG_COUNT =
            "COUNT (CASE WHEN is_trashed != 1 AND mime_type COLLATE NOCASE = 'image/jpeg' THEN 1 ELSE NULL END) AS $COLUMN_JPEG_COUNT_NAME"

        /**
         * HEIF总数(包含云端和本地))
         *
         * 使用场景:新建回忆-选图若手机支持heif,则选图就支持heif,不支持则需要过滤掉heif
         */
        const val COLUMN_HEIF_COUNT_NAME = "heifCount"

        const val COLUMN_HEIF_COUNT =
            ("COUNT (CASE WHEN is_trashed != 1 AND (mime_type COLLATE NOCASE = 'image/heif' OR mime_type COLLATE NOCASE = 'image/heic') "
                + "THEN 1 ELSE NULL END) AS " + COLUMN_HEIF_COUNT_NAME)

        const val COLUMN_NAME_MAX_MODIFIED_TIME = "maxModifiedTime"
        /**表中更新记录的最新时间*/
        const val COLUMN_MAX_MODIFIED_TIME =
            MAX + LEFT_BRACKETS + LocalColumns.GENERATION_MODIFIED + RIGHT_BRACKETS + AS + COLUMN_NAME_MAX_MODIFIED_TIME
        const val INVALID_COLUMN_INDEX = -1
    }
}