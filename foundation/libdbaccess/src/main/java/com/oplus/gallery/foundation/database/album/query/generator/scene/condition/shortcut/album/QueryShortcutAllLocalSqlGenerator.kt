/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - QueryShortcutAllLocalSqlGenerator.kt
 ** Description:常用图集：查找含本地所有媒体类型的图集集合
 ** Version: 1.0
 ** Date: 2024/4/11
 ** Author: <PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery     2024/4/11     1.0         OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.foundation.database.album.query.generator.scene.condition.shortcut.album

import android.os.Bundle
import com.oplus.gallery.foundation.database.album.query.generator.scene.ISceneSqlGenerator
import com.oplus.gallery.foundation.database.store.CacheStore.AlbumCountColumns.LOCAL_IMAGE
import com.oplus.gallery.foundation.database.store.CacheStore.AlbumCountColumns.LOCAL_VIDEO

/**
 * 常用图集：
 * 生成含本地所有媒体类型的图集集合的sql语句
 * 如三方选图
 */
class QueryShortcutAllLocalSqlGenerator : ISceneSqlGenerator {

    override fun getQueryRawSql(projections: Array<String>?, queryArgs: Bundle): String {
        return ShortcutAlbumSceneSqlUtil.buildRawSql(conditionColumn = SCENE_CONDITION, projections, queryArgs)
    }

    override fun getCountRawSql(queryArgs: Bundle): String {
        return ShortcutAlbumSceneSqlUtil.buildMediaConditionCountSql(SCENE_CONDITION, queryArgs)
    }

    companion object {
        /**
         * 场景条件列
         */
        private const val SCENE_CONDITION = "$LOCAL_IMAGE + $LOCAL_VIDEO"
    }
}