/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - QueryShortcutAllSqlGenerator.kt
 ** Description: 常用图集：查找含所有媒体类型的图集集合
 ** Version: 1.0
 ** Date: 2024/4/11
 ** Author: <PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery     2024/4/11     1.0         OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.foundation.database.album.query.generator.scene.condition.shortcut.album

import android.os.Bundle
import com.oplus.gallery.foundation.database.album.query.generator.scene.ISceneSqlGenerator
import com.oplus.gallery.foundation.database.album.query.generator.scene.group.BaseAlbumSetSqlGenerator.Companion.ALBUM_DISTINCT_KEY

/**
 * 常用图集：生成含所有媒体类型的图集集合的sql语句
 * 相册内图集tab
 */
class QueryShortcutAllSqlGenerator : ISceneSqlGenerator {
    override fun getQueryRawSql(projections: Array<String>?, queryArgs: Bundle): String {
        ShortcutAlbumSceneSqlUtil.putOrderBy(queryArgs)
        return buildAllAlbumSql(queryArgs, projections)
    }

    /**
     * 获取图集列表总数(其它图集列表页)
     */
    override fun getCountRawSql(queryArgs: Bundle): String {
        val isDistinct = queryArgs.getBoolean(ALBUM_DISTINCT_KEY, false)
        // 获取后重置去重标记，以防止后面查询sql加上错误的group by语句导致查询错误
        queryArgs.putBoolean(ALBUM_DISTINCT_KEY, false)
        return buildAllAlbumSql(queryArgs, arrayOf(ShortcutAlbumSceneSqlUtil.getCountAllProjection(isDistinct)))
    }

    /**
     * 查找所有cache表的图集（除了媒体类型图集和回收站图集）
     */
    private fun buildAllAlbumSql(queryArgs: Bundle, projections: Array<String>?): String {
        return ShortcutAlbumSceneSqlUtil.buildAllOrRealAlbumSql(queryArgs, projections)
    }
}