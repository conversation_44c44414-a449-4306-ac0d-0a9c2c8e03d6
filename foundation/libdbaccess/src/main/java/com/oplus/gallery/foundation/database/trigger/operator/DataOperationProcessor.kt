/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DataOperationProcessor.kt
 ** Description: 将触发器操作（增删改）转化成批操作
 ** Version: 1.0
 ** Date: 2024/4/18
 ** Author: <PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2024/4/18     1.0         OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.foundation.database.trigger.operator

import android.content.ContentValues
import android.os.Handler
import android.os.HandlerThread
import android.os.Looper
import android.os.Message
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag.Companion.DF
import com.oplus.gallery.foundation.util.debug.LogFlag.Companion.DL
import java.util.Collections

/**
 * 数据操作处理器基类，提供数据处理的基础接口和实现。
 *
 * 两种处理方式：
 * 1. 延迟批量处理。将读取到的记录操作，按批收集后延迟处理。批操作生成策略：
 *     1. 首次启动批操作使用500ms延时
 *     2. 当500ms内,不断有记录操作且延时已超过250ms,则设置更新延时为250ms
 *     3. 直到没有更新操作了,则生成一批,后续更新图集表按批处理
 * 2. 单个立即处理并回调给业务。不做延迟，以保证业务及时响应。
 */
abstract class DataOperationProcessor : ITableOperationProcessor {
    /**
     * key-记录的操作类型分类 value:同类的所有记录 Pair.first:存储update的new和delete/insert的记录内容 second仅为update的old
     */
    private val batchMap by lazy {
        mutableMapOf<OperationType, MutableList<Pair<ContentValues, ContentValues?>>>()
    }

    /**
     * batchMap 存在多线程操作时序问题,导致丢失数据,需加锁
     */
    private val batchMapLock = Any()

    protected abstract val tag: String

    private var startBatchTime = 0L

    /**
     * 多线程操作:会因线程1刚remove msg,线程2修改startBatchTime,导致batchTask延长
     *
     * 故增加此标志位,过滤多线程时序而修改startBatchTime的问题
     */
    private var hasBatchTask = false

    /**
     * 统计新增数量,log
     */
    private var insertRowCount = 0

    /**
     * 统计丢失数量,log
     */
    private var dropRowCount = 0

    private val threadHandler: Handler by lazy {
        object : Handler(makeBatchLooper()) {
            override fun handleMessage(msg: Message) {
                when (msg.what) {
                    BATCH_RUN_WHAT -> {
                        hasBatchTask = false
                        val copyBatchMap = copyAndClearBatchMap()
                        GLog.d(tag, DL) {
                            "handlerMessage:doBatch ${msg.obj}->$startBatchTime,cost=${GLog.getTime(startBatchTime)}" +
                                    ",count=$insertRowCount+$dropRowCount,${contentLog(copyBatchMap)} \n"
                        }
                        if (copyBatchMap.isEmpty()) return
                        GTrace.trace("$tag.BATCH_RUN_WHAT.doBatch") {
                            doBatch(copyBatchMap)
                        }
                    }
                }
            }
        }
    }

    private fun copyAndClearBatchMap(): MutableMap<OperationType, MutableList<Pair<ContentValues, ContentValues?>>> {
        if (isBatchEmpty()) return Collections.emptyMap()
        val tmpBatchMap = mutableMapOf<OperationType, MutableList<Pair<ContentValues, ContentValues?>>>()
        synchronized(batchMapLock) {
            tmpBatchMap.putAll(batchMap)
            batchMap.clear()
        }
        return tmpBatchMap
    }

    private fun contentLog(batchMap: MutableMap<OperationType, MutableList<Pair<ContentValues, ContentValues?>>>): String {
        val sb = StringBuilder()
        batchMap.forEach { (key, valueList) ->
            sb.append("$key->${valueList.size},")
        }
        return sb.toString()
    }

    /**
     * 定制批处理的线程
     */
    protected open fun makeBatchLooper(): Looper = batchProcessHandlerThread.looper

    /**
     * 启用批处理功能
     */
    protected open fun isEnabled(): Boolean = true

    /**
     * 丢弃的数据:首次数据搬迁,数据同步到图集表完成之前,需保留此diff数据,同步结束后,才去统计此diff数据
     */
    protected open fun onDropBatchData(operationType: OperationType, contentPair: Pair<ContentValues, ContentValues?>): Unit = Unit

    /**
     * 创建批处理
     */
    protected open fun onCreateBatch(): Unit = Unit

    /**
     * 多个diff快照，合并成批处理完成，要根据业务特性选择
     */
    protected abstract fun doBatch(batchMap: MutableMap<OperationType, MutableList<Pair<ContentValues, ContentValues?>>>)

    /**
     * 单个diff快照处理，对于无需延迟或短暂延迟的使用此回调立即处理
     */
    protected open fun doSingle(diffValuesSnapshot: DataDiffValuesSnapshot) {
        // ignore
    }

    override fun processDiff(diffValuesSnapshot: DataDiffValuesSnapshot) {
        GTrace.trace({ "$tag.processSingleDiff" }) { processSingleDiff(diffValuesSnapshot) }
        GTrace.trace({ "$tag.processBatchDiff" }) { processBatchDiff(diffValuesSnapshot) }
    }

    /**
     * 处理diff数据。立即同步回调给业务进行处理。消息频繁的情况下，切线程耗时，最好是直接调回去。
     *
     * diff变更后，立即调用。在[processBatchDiff]之前调用，优先处理。
     *
     * @param diffValuesSnapshot diff数据
     */
    protected open fun processSingleDiff(diffValuesSnapshot: DataDiffValuesSnapshot) {
        doSingle(diffValuesSnapshot)
    }

    /**
     * 处理批量diff数据。收集diff数据到缓存，提交有延迟的异步回调任务，延迟回调给业务进行处理。
     *
     * diff变更后，立即调用。
     *
     * @param diffValuesSnapshot diff数据
     */
    private fun processBatchDiff(diffValuesSnapshot: DataDiffValuesSnapshot) {
        diffValuesSnapshot.diffValuesList.forEach { (operationType, contentPair) ->
            if (isEnabled().not()) {
                dropRowCount++
                onDropBatchData(operationType, contentPair)
                return
            }
            if (operationType == OperationType.INSERT_LOCAL) insertRowCount++
            // 是否已经开始收集批处理
            val isBatchStarted = checkBatchStarted()
            if (isBatchStarted.not()) {
                // 开始收集批处理
                startBatchTime = System.currentTimeMillis()
                onCreateBatch()
                GLog.d(tag, DF) { "processBatchDiff:start batch cost=${GLog.getTime(startBatchTime)} $startBatchTime" }
            }
            addBatch(operationType, contentPair)
            threadHandler.removeMessages(BATCH_RUN_WHAT)
            //首次使用500ms延时,当刷新间隔时间超过250ms<x<500ms时,使用250ms延时
            threadHandler.sendMessageDelayed(
                Message.obtain().apply {
                    what = BATCH_RUN_WHAT
                    obj = startBatchTime
                },
                getDelayTime(startBatchTime)
            )
        }
    }

    /**
     * 首次使用500ms延时,当刷新间隔时间超过250ms<x<500ms时,使用250ms延时
     *
     * 当超过5s时,则立即执行,避免长时间等待
     */
    open fun getDelayTime(startBatchTime: Long): Long {
        val dTime = GLog.getTime(startBatchTime)
        return when {
            dTime >= MAX_BATCH_DELAY_TIME -> 0L
            dTime >= BATCH_AGAIN_DELAY_TIME -> BATCH_AGAIN_DELAY_TIME
            else -> BATCH_FIRST_DELAY_TIME
        }
    }

    private fun checkBatchStarted(): Boolean {
        hasBatchTask = hasBatchTask || threadHandler.hasMessages(BATCH_RUN_WHAT)
        return hasBatchTask
    }

    private fun addBatch(operationType: OperationType, contentPair: Pair<ContentValues, ContentValues?>) {
        synchronized(batchMapLock) {
            if (interceptBatchData(operationType, contentPair, batchMap).not()) {
                batchMap.computeIfAbsent(operationType) { mutableListOf() }.add(contentPair)
            }
        }
    }

    /**
     * 拦截批处理数据,对数据二次处理
     * @param operationType 记录类型
     * @param contentPair 记录
     * @param outBatchMap 输出批量数据集
     * @return 是否拦截进行二次处理数据
     */
    protected open fun interceptBatchData(
        operationType: OperationType,
        contentPair: Pair<ContentValues, ContentValues?>,
        outBatchMap: MutableMap<OperationType, MutableList<Pair<ContentValues, ContentValues?>>>
    ): Boolean = false

    protected fun isBatchEmpty(): Boolean = batchMap.isEmpty()

    /**
     * 更新记录是否从批处理集合中移除
     * @param filter true:则从batchMap中移除,false:此记录不移除
     */
    protected fun removeRecordIf(filter: (ContentValues) -> Boolean) {
        synchronized(batchMapLock) {
            val iterator = batchMap.iterator()
            var contentList: MutableList<Pair<ContentValues, ContentValues?>>? = null
            while (iterator.hasNext()) {
                val entry = iterator.next()
                contentList = entry.value
                contentList.removeIf { row ->
                    val remove = filter.invoke(row.first)
                    remove
                }
                if (contentList.isEmpty()) {
                    iterator.remove()
                }
            }
        }
    }

    companion object {
        private val batchProcessHandlerThread by lazy {
            val handlerThread = HandlerThread(BATCH_THREAD_NAME)
            handlerThread.start()
            handlerThread
        }
        private const val BATCH_THREAD_NAME = "BatchProcessThread"

        /**
         * 数据库diff信息，收集后批量处理
         */
        private const val BATCH_RUN_WHAT = 100

        /**
         * 第一批数据延迟时间
         */
        private const val BATCH_FIRST_DELAY_TIME = 500L

        /**
         * 非首次数据延迟时间
         */
        private const val BATCH_AGAIN_DELAY_TIME = 250L

        /**
         * 最大延迟时间。如果长时间没有执行过，则立即触发，避免diff数据长时间等待
         */
        private const val MAX_BATCH_DELAY_TIME = 5000L
    }
}