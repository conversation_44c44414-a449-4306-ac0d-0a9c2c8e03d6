/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ContentValuesConvert.java
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/05/22
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** h<PERSON><PERSON><PERSON>@Apps.Gallery		2020/02/24		1.0		OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.foundation.dbaccess.convert;

import android.content.ContentValues;

public class ContentValuesConvert implements IConvert<Void, ContentValues> {

    private final ContentValues mValues;

    public ContentValuesConvert(ContentValues values) {
        mValues = values;
    }

    @Override
    public ContentValues convert(Void aVoid) {
        return mValues;
    }
}
