/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MediaTypeSceneSqlUtil.kt
 ** Description:根据场景生成真正的查询条件
 ** Version: 1.0
 ** Date: 2025/5/13
 ** Author: <PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2025/5/13     1.0         OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.foundation.database.album.query.generator.scene.condition.mediatype

import android.os.Bundle
import com.oplus.gallery.foundation.database.album.query.generator.scene.getDisplayCountSql
import com.oplus.gallery.foundation.database.album.query.generator.scene.group.BaseAlbumSetSqlGenerator.Companion.ALBUM_DISTINCT_KEY
import com.oplus.gallery.foundation.database.album.query.generator.scene.group.BaseAlbumSetSqlGenerator.Companion.CONDITION_EXCLUDE_ALBUM_FLAGS_KEY
import com.oplus.gallery.foundation.database.helper.Constants.Album.AlbumTableFlag.FLAG_MEDIA_TYPE_ALBUM
import com.oplus.gallery.foundation.database.helper.Constants.Album.RealTable.TABLE_ALBUM
import com.oplus.gallery.foundation.database.helper.Constants.Album.SceneProjection.ALBUM_PROJECTION
import com.oplus.gallery.foundation.database.helper.Constants.Album.SceneProjection.DISPLAY_COUNT
import com.oplus.gallery.foundation.database.helper.Constants.Album.SpecialAlbumFlag.FLAG_CLEAR
import com.oplus.gallery.foundation.database.helper.Constants.Album.SpecialAlbumFlag.FLAG_DOLBY_ALBUM
import com.oplus.gallery.foundation.database.helper.Constants.Album.SpecialAlbumFlag.FLAG_LOG_VIDEO_ALBUM
import com.oplus.gallery.foundation.database.helper.Constants.Album.SpecialAlbumFlag.FLAG_OLIVE_ALBUM
import com.oplus.gallery.foundation.database.helper.Constants.Album.SpecialAlbumFlag.FLAG_PORTRAIT_BLUR_ALBUM
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.DOLBY_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.LOG_VIDEO_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.OLIVE_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.PORTRAIT_BLUR_ALBUM_ID
import com.oplus.gallery.foundation.database.store.CacheStore.AlbumColumns.ALBUM_ID
import com.oplus.gallery.foundation.database.store.CacheStore.AlbumColumns.FLAGS
import com.oplus.gallery.foundation.database.store.CacheStore.AlbumColumns.ORDERED_POSITION
import com.oplus.gallery.foundation.database.store.CacheStore.AlbumColumns.ORDERED_SUB_POSITION
import com.oplus.gallery.foundation.database.store.CacheStore.AlbumColumns.TOTAL_COUNT
import com.oplus.gallery.foundation.database.util.DatabaseUtils.QUERY_ARG_SQL_LIMIT
import com.oplus.gallery.foundation.database.util.DatabaseUtils.QUERY_ARG_SQL_ORDER_BY
import com.oplus.gallery.foundation.database.util.SQLGrammar.AND
import com.oplus.gallery.foundation.database.util.SQLGrammar.COMMA
import com.oplus.gallery.foundation.database.util.SQLGrammar.COUNT_ALL
import com.oplus.gallery.foundation.database.util.SQLGrammar.COUNT_DISTINCT
import com.oplus.gallery.foundation.database.util.SQLGrammar.DOT
import com.oplus.gallery.foundation.database.util.SQLGrammar.GREATER_THAN_ZERO
import com.oplus.gallery.foundation.database.util.SQLGrammar.LEFT_BRACKETS
import com.oplus.gallery.foundation.database.util.SQLGrammar.RIGHT_BRACKETS
import com.oplus.gallery.foundation.database.util.SQLSelectBuilder
import com.oplus.gallery.foundation.util.ext.bracket
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING

/**
 * 媒体类型图集构造sql util
 */
internal object MediaTypeSceneSqlUtil {

    /**
     * 将默认的orderBy写入参数中
     */
    @JvmStatic
    internal fun putOrderBy(queryArgs: Bundle) {
        if (queryArgs.getString(QUERY_ARG_SQL_ORDER_BY).isNullOrEmpty()) {
            queryArgs.putString(QUERY_ARG_SQL_ORDER_BY, "$TABLE_ALBUM$DOT$ORDERED_POSITION,$TABLE_ALBUM$DOT$ORDERED_SUB_POSITION")
        }
    }

    /**
     * 拼接orderBy和limit
     */
    @JvmStatic
    private fun appendOrderByAndLimitIfNeed(queryArgs: Bundle, sqlBuilder: SQLSelectBuilder) {
        queryArgs.getString(QUERY_ARG_SQL_ORDER_BY)?.takeIf { it.trim().isNotEmpty() }?.let { sqlBuilder.orderBy(it) }
        queryArgs.getString(QUERY_ARG_SQL_LIMIT)?.takeIf { it.trim().isNotEmpty() }?.let { sqlBuilder.limit(it) }
    }

    /**
     * 获取图集列表数据的查询sql语句
     */
    @JvmStatic
    internal fun buildRawSql(conditionColumn: String, projections: Array<String>?, queryArgs: Bundle): String {
        putOrderBy(queryArgs)
        return buildMediaConditionSql(conditionColumn, queryArgs, projections, "*", isQueryData = true)
    }

    /**
     * 获取图集列表总数
     */
    @JvmStatic
    internal fun buildCountRawSqlForMediaCondition(conditionColumn: String, queryArgs: Bundle): String {
        val isDistinct = queryArgs.getBoolean(ALBUM_DISTINCT_KEY, false)
        // 获取后重置去重标记，以防止后面查询sql加上错误的group by语句导致查询错误
        queryArgs.putBoolean(ALBUM_DISTINCT_KEY, false)
        return buildMediaConditionSql(
            conditionColumn,
            queryArgs,
            arrayOf(getCountAllProjection(isDistinct)),
            COUNT_ALL,
            isQueryData = false
        )
    }

    /**
     * 根据媒体文件类型，筛选出媒体类型图集
     *
     * 如：获取数据的sql
     *  SELECT *
     *  FROM (SELECT
     * 			 ( SELECT sum(album_count.jpeg + album_count.heif)
     * 			   FROM album_count
     * 	           WHERE EXISTS (SELECT 1 FROM JSON_EACH(bucket_ids) AS item WHERE item.value=bucket_id)
     * 	         )  AS  displayCount,album.album_id
     * 		 FROM album
     * 		 WHERE (displayCount > 0
     * 		        AND (album.flags & 32 = 32)
     * 		        AND ( album_id NOT IN (OLIVE_ALBUM_ID,-134776996,DOLBY_ALBUM_ID))
     * 		       )
     *      )
     * @param conditionColumn 媒体类型条件,作为列,用于计算总数 如：image-gif
     * @param queryArgs 查询参数
     * @param projections 返回列
     * @param defaultProjection 若projections为null,则返回此默认列
     */
    @JvmStatic
    private fun buildMediaConditionSql(
        conditionColumn: String,
        queryArgs: Bundle,
        projections: Array<String>?,
        defaultProjection: String,
        isQueryData: Boolean
    ): String {
        val albumTab = TABLE_ALBUM
        val displayCountSql = getDisplayCountSql(conditionColumn)

        val subWhereSb = StringBuilder().apply {
            append(LEFT_BRACKETS)
            append("$DISPLAY_COUNT $GREATER_THAN_ZERO ")
            append(AND)
            append(bracket(getRequiredAlbumFlagWhere(albumTab)))
            appendExcludeAlbumByFlag(queryArgs)?.let { excludeWhere ->
                append(AND)
                append(excludeWhere)
            }
            append(RIGHT_BRACKETS)
        }
        val subSqlBuilder = SQLSelectBuilder.build()
            .select(buildSubProjectionsIfNeed(isQueryData, projections, defaultProjection))
            .append("$displayCountSql, $ALBUM_ID")
            .from(albumTab)
            .where(subWhereSb.toString())
        appendGroupByIfNeed(queryArgs, subSqlBuilder, ALBUM_ID)
        appendOrderByAndLimitIfNeed(queryArgs, subSqlBuilder)

        //查找图集列表总数
        return SQLSelectBuilder.build()
            //将 totalCount的值替换成displayCount的
            .select(buildDisplayCountAsTotalCountProjections(projections, defaultProjection))
            .from(bracket(subSqlBuilder.toString()))
            .toString()
    }

    @JvmStatic
    private fun appendExcludeAlbumByFlag(queryArgs: Bundle): String? {
        val excludeFlags = queryArgs.getInt(CONDITION_EXCLUDE_ALBUM_FLAGS_KEY, FLAG_CLEAR)
        val excludeAlbumIdSb = StringBuilder()
        if (excludeFlags > FLAG_CLEAR) {
            if (excludeFlags and FLAG_OLIVE_ALBUM == FLAG_OLIVE_ALBUM) {
                excludeAlbumIdSb.append("$OLIVE_ALBUM_ID")
            }
            if (excludeFlags and FLAG_PORTRAIT_BLUR_ALBUM == FLAG_PORTRAIT_BLUR_ALBUM) {
                excludeAlbumIdSb.append(if (excludeAlbumIdSb.isNotEmpty()) ",$PORTRAIT_BLUR_ALBUM_ID" else "$PORTRAIT_BLUR_ALBUM_ID")
            }
            if (excludeFlags and FLAG_DOLBY_ALBUM == FLAG_DOLBY_ALBUM) {
                excludeAlbumIdSb.append(if (excludeAlbumIdSb.isNotEmpty()) ",$DOLBY_ALBUM_ID" else "$DOLBY_ALBUM_ID")
            }
            if (excludeFlags and FLAG_LOG_VIDEO_ALBUM == FLAG_LOG_VIDEO_ALBUM) {
                excludeAlbumIdSb.append(if (excludeAlbumIdSb.isNotEmpty()) ",$LOG_VIDEO_ALBUM_ID" else "$LOG_VIDEO_ALBUM_ID")
            }
        }
        if (excludeAlbumIdSb.isEmpty()) {
            return null
        }
        return "$LEFT_BRACKETS $ALBUM_ID NOT IN ($excludeAlbumIdSb) $RIGHT_BRACKETS"
    }

    /**
     * 子查询的列,先计算displayCount,将displayCount和列一起返回
     * @param isQueryData 是否查询数据,true则返回空,false:则返回子列
     */
    @JvmStatic
    private fun buildSubProjectionsIfNeed(isQueryData: Boolean, projections: Array<String>?, defaultProjection: String): String {
        if (isQueryData.not()) {
            return EMPTY_STRING
        }
        return buildProjection(projections, defaultProjection) + COMMA
    }

    /**
     * 构建返回的列数,并将totalCount的值替换成真实的displayCount,避免不同场景返回的都是totalCount
     */
    @JvmStatic
    private fun buildDisplayCountAsTotalCountProjections(projections: Array<String>?, default: String): String {
        projections ?: return default
        var tmpProjections = projections
        if (isAllProjection(tmpProjections)) {
            tmpProjections = ALBUM_PROJECTION
        }
        val projectionSb = StringBuilder()
        val lastIndex = tmpProjections.size - 1
        tmpProjections.forEachIndexed { index, projection ->
            if (TOTAL_COUNT == projection.trim()) {
                projectionSb.append("$DISPLAY_COUNT AS $TOTAL_COUNT")
            } else {
                projectionSb.append(projection)
            }
            if (lastIndex != index) {
                projectionSb.append(COMMA)
            }
        }
        return projectionSb.toString()
    }

    /**
     * 查询图集列表内容的sql
     * SELECT _id,album_id,album_path,display_name,bucket_ids,item_sort_type,total_count,trashed_count,flags,ordered_position,ordered_sub_position,date_taken,date_modified,cover_info
     * FROM album
     * WHERE (totalCount > 0
     *       AND flags & 32 == 32
     *       AND ( album_id NOT IN (OLIVE_ALBUM_ID,-134776996,DOLBY_ALBUM_ID))
     *      )
     * ORDER BY album.ordered_position,album.ordered_sub_position
     */
    @JvmStatic
    internal fun buildAllOrRealAlbumSql(
        queryArgs: Bundle,
        projections: Array<String>?,
        defaultProjection: String
    ): String {
        val albumTab = TABLE_ALBUM
        val whereSb = StringBuilder().apply {
            append(LEFT_BRACKETS)
            append("$albumTab.$TOTAL_COUNT > 0 ")
            append(AND)
            append(bracket(getRequiredAlbumFlagWhere(albumTab)))
            appendExcludeAlbumByFlag(queryArgs)?.let { excludeWhere ->
                append(AND)
                append(excludeWhere)
            }
            append(RIGHT_BRACKETS)
        }
        val sqlBuilder = SQLSelectBuilder.build()
            .select(buildProjection(projections, defaultProjection))
            .from(albumTab)
            .where(whereSb.toString())
        appendGroupByIfNeed(queryArgs, sqlBuilder, "$albumTab.$ALBUM_ID")
        appendOrderByAndLimitIfNeed(queryArgs, sqlBuilder)
        return sqlBuilder.toString()
    }

    /**
     * 获取必要的图集:查找媒体类型图集
     *
     * flags & FLAG_MEDIA_TYPE_ALBUM = FLAG_MEDIA_TYPE_ALBUM
     */
    @JvmStatic
    private fun getRequiredAlbumFlagWhere(table: String): String {
        return "$table.$FLAGS & $FLAG_MEDIA_TYPE_ALBUM = $FLAG_MEDIA_TYPE_ALBUM"
    }

    /**
     * 构建查询结果列
     */
    @JvmStatic
    private fun buildProjection(projections: Array<String>?, default: String): String {
        projections ?: return default
        var tmpProjections = projections
        if (isAllProjection(tmpProjections)) {
            tmpProjections = ALBUM_PROJECTION
        }
        val projectionSb = StringBuilder()
        val lastIndex = tmpProjections.size - 1
        tmpProjections.forEachIndexed { index, projection ->
            projectionSb.append(projection)
            if (lastIndex != index) {
                projectionSb.append(COMMA)
            }
        }
        return projectionSb.toString()
    }

    /**
     * 是否传入的是*
     */
    @JvmStatic
    private fun isAllProjection(tmpProjections: Array<String>) =
        (tmpProjections.size == 1) &&
                ((tmpProjections[0].trim() == "*") || (tmpProjections[0].trim() == "$TABLE_ALBUM.*"))

    @JvmStatic
    private fun appendGroupByIfNeed(queryArgs: Bundle, sqlBuilder: SQLSelectBuilder, groupBy: String) {
        if (queryArgs.getBoolean(ALBUM_DISTINCT_KEY, false)) {
            sqlBuilder.groupBy(groupBy)
        }
    }

    /**
     * 根据是否需要去重返回不同的count语句
     * @param isDistinct 是否去重，由业务传递决定
     * @return count语句
     */
    @JvmStatic
    internal fun getCountAllProjection(isDistinct: Boolean): String {
        return if (isDistinct) {
            "$COUNT_DISTINCT $ALBUM_ID $RIGHT_BRACKETS"
        } else {
            COUNT_ALL
        }
    }
}