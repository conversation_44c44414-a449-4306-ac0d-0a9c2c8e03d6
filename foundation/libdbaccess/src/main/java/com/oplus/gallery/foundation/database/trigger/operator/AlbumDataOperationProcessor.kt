/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AlbumDataDataOperationProcessor.kt
 ** Description: 批量操作处理器
 ** Version: 1.0
 ** Date: 2024/4/18
 ** Author: <PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2024/4/18     1.0         OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.foundation.database.trigger.operator

import android.content.ContentValues
import android.os.Looper
import com.oplus.gallery.foundation.database.album.loadCShotAlbumMap
import com.oplus.gallery.foundation.database.album.loadGenerationModified
import com.oplus.gallery.foundation.database.album.saveAlbumCacheDiffSyncFlag
import com.oplus.gallery.foundation.database.album.saveCShotAlbumMap
import com.oplus.gallery.foundation.database.album.syncAlbumCacheHandlerThread
import com.oplus.gallery.foundation.database.helper.Constants.SP.DEFAULT_GENERATION_MODIFIED
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.INVALID_NORMAL
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.INVALID_NORMAL_NOT_IN_MEDIA_STORE
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.IS_PENDING_FALSE
import com.oplus.gallery.foundation.database.trigger.AlbumInteractionManager
import com.oplus.gallery.foundation.database.trigger.monitor.EVENT_FULL_SYNC_ALBUM_END
import com.oplus.gallery.foundation.database.trigger.monitor.EVENT_MIGRATE_ALBUM_END
import com.oplus.gallery.foundation.database.trigger.monitor.ITableReceiveEvent
import com.oplus.gallery.foundation.database.trigger.operator.OperationRowFieldKey.KEY_BUCKET_ID
import com.oplus.gallery.foundation.database.trigger.operator.OperationRowFieldKey.KEY_BUCKET_PATH
import com.oplus.gallery.foundation.database.trigger.operator.OperationRowFieldKey.KEY_CSHOT_ID
import com.oplus.gallery.foundation.database.trigger.operator.OperationRowFieldKey.KEY_DATA
import com.oplus.gallery.foundation.database.trigger.operator.OperationRowFieldKey.KEY_DISPLAY_NAME
import com.oplus.gallery.foundation.database.trigger.operator.OperationRowFieldKey.KEY_GENERATION_MODIFIED
import com.oplus.gallery.foundation.database.trigger.operator.OperationRowFieldKey.KEY_INVALID
import com.oplus.gallery.foundation.database.trigger.operator.OperationRowFieldKey.KEY_IS_FAVORITE
import com.oplus.gallery.foundation.database.trigger.operator.OperationRowFieldKey.KEY_IS_PENDING
import com.oplus.gallery.foundation.database.trigger.operator.OperationRowFieldKey.KEY_IS_TRASHED
import com.oplus.gallery.foundation.database.trigger.operator.OperationRowFieldKey.KEY_MEDIA_ID
import com.oplus.gallery.foundation.database.trigger.operator.entry.CShotAlbum
import com.oplus.gallery.foundation.database.trigger.operator.task.AlbumCacheOperationTask
import com.oplus.gallery.foundation.database.trigger.operator.task.AlbumCacheTaskDispatcher
import com.oplus.gallery.foundation.database.trigger.operator.task.IOperationTaskListener
import com.oplus.gallery.foundation.database.util.DatabaseUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag.Companion.DF
import com.oplus.gallery.foundation.util.debug.LogFlag.Companion.DL
import com.oplus.gallery.foundation.util.mask.PathMask
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.app.SyncAlbumSetTask
import com.oplus.gallery.standard_lib.file.utils.FilePathUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.lang.StringBuilder

/**
 * 图集表数据处理器。
 *
 * 批量操作处理器
 * 1.批量处理从触发器收集过来的操作
 * 2.将其分发到任务池中更新操作
 */
internal class AlbumDataOperationProcessor : ObservableDataOperationProcessor(TAG), ITableReceiveEvent {
    override val tag: String = TAG
    private val taskDispatcher by lazy {
        AlbumCacheTaskDispatcher<AlbumCacheOperationTask>()
    }

    /**
     * 记录连拍图片的更新记录（如insert/delete/update操作的记录）,用于过滤连拍图
     * 缓存的一定是有效的记录,若新增的是无效记录则不缓存
     * 1. key--bucketId
     * 2. value---<新记录,旧记录>
     */
    private val existCShotMap by lazy {
        mutableMapOf<Int, CShotAlbum>().apply { putAll(loadCShotAlbumMap()) }
    }

    /**
     * 避免多次读写sp
     */
    private var isExistDiffSync = false
    private val isExistDiffSyncLock = Any()

    /**
     * 同步任务监听器
     */
    private val taskListener by lazy {
        object : IOperationTaskListener {
            override fun onEnd() {
                // 若内容不为空,则表示任务执行期间又有Diff数据过来,所以不需要重置标志位
                if (isBatchEmpty()) {
                    synchronized(isExistDiffSyncLock) {
                        isExistDiffSync = false
                        saveAlbumCacheDiffSyncFlag(isExistDiffSync = false)
                    }
                }
            }
        }
    }

    private val cacheDropRowMap by lazy {
        mutableMapOf<OperationType, MutableList<Pair<ContentValues, ContentValues?>>>()
    }

    /**有效记录的最小时间:全量同步时记录的最新生成时间 #loadGenerationModified():为了避免重复计算,小于此时间的记录需要丢弃*/
    private var validRowMinTime: Long? = null
    private var invalidRowCount = 0

    /**
     * 使用和同步同一个线程,减少批次任务生成(如任务在执行过程中,Diff不断增加,在此期间可能会生成多个任务,使用同一个线程则可合成一个任务一起执行)
     */
    override fun makeBatchLooper(): Looper = syncAlbumCacheHandlerThread.looper

    override fun onCreateBatch() {
        if (isExistDiffSync.not()) {
            synchronized(isExistDiffSyncLock) {
                isExistDiffSync = true
                //未完成的批处理,打个flag,批处理任务执行完成后,会将flag重置
                saveAlbumCacheDiffSyncFlag(isExistDiffSync = true)
            }
        }
        if (validRowMinTime == null) {
            validRowMinTime = loadGenerationModified()
            GLog.d(TAG, DL) { "onCreateBatch: validTime=$validRowMinTime" }
        }
    }

    override fun onDropBatchData(operationType: OperationType, contentPair: Pair<ContentValues, ContentValues?>) {
        cacheDropRowMap.computeIfAbsent(operationType) { mutableListOf() }.add(contentPair)
    }

    override fun isEnabled(): Boolean {
        return AlbumInteractionManager.isSynchronizedAlbumToTable()
    }

    override fun doBatch(batchMap: MutableMap<OperationType, MutableList<Pair<ContentValues, ContentValues?>>>) {
        taskDispatcher.addTask(task = AlbumCacheOperationTask(batchMap, taskListener))
        taskDispatcher.execute()
        if (invalidRowCount > 0) {
            GLog.d(TAG, DL) { "doBatch: dropCount=$invalidRowCount" }
            invalidRowCount = 0
        }
    }

    override fun interceptBatchData(
        operationType: OperationType,
        contentPair: Pair<ContentValues, ContentValues?>,
        outBatchMap: MutableMap<OperationType, MutableList<Pair<ContentValues, ContentValues?>>>
    ): Boolean {

        // 当记录的时间小于有效的最小时间时,判定为无效记录,需要丢弃,避免重复统计(如全量同步和增量同步都处理同一条记录)
        validRowMinTime?.takeIf { getRowGenerationTime(contentPair.first) <= it }?.let {
            invalidRowCount++
            return true
        }

        if (interceptCShotIfNeed(operationType, contentPair, outBatchMap)) {
            return true
        }
        if (isDissolveCShot(operationType, contentPair)) {
            //解散连拍,拆成insert和delete,因原连拍数量只统计一次,所以只需要delete一次
            outBatchMap.computeIfAbsent(OperationType.INSERT_LOCAL) { mutableListOf() }.add(Pair(contentPair.first, null))
            contentPair.second?.getAsInteger(KEY_BUCKET_ID)?.also { bucketId ->
                //云端连拍图,只下载了一张到本地，此时关闭云，则这一张会被解除连拍,其它云端的会从local中删除
                existCShotMap[bucketId]?.let { album ->
                    album.totalCount--
                    contentPair.second?.takeIf { album.totalCount <= 0 }?.let {
                        // 需要解除到最后一张,才统计
                        outBatchMap.computeIfAbsent(OperationType.DELETE_LOCAL) { mutableListOf() }.add(Pair(it, null))
                        existCShotMap.remove(bucketId)
                        GLog.d(TAG, DL) { "interceptBatchData: dissolve CShot ${getAlbumLog(album)}" }
                    }
                    saveCShotAlbumMapAsync()
                } ?: GLog.d(TAG, DL) {
                    "interceptBatchData:drop ${getSimpleLog(contentPair.first, "new")},${getSimpleLog(contentPair.first, "old")}"
                }
            }
            return true
        }
        return false
    }

    /**
     * 是否解散连拍
     */
    private fun isDissolveCShot(operationType: OperationType, newRowPair: Pair<ContentValues, ContentValues?>): Boolean {
        if (operationType != OperationType.UPDATE_LOCAL) return false
        val oldShotId = newRowPair.second?.getAsLong(KEY_CSHOT_ID) ?: return false
        val newCShotId = newRowPair.first.getAsLong(KEY_CSHOT_ID)
        return DatabaseUtils.isCShotIdValid(oldShotId) && ((newCShotId == null) || DatabaseUtils.isCShotIdValid(newCShotId).not())
    }

    /**
     * cshot图片仅需要统计一张图片即可
     *
     * 检查chsot图片是否已统计过,没统计过则加入到缓存中并加入统计
     * @return true表示不需要统计cshot,false表示需要统计
     */
    private fun interceptCShotIfNeed(
        operationType: OperationType,
        newRowPair: Pair<ContentValues, ContentValues?>,
        outBatchMap: MutableMap<OperationType, MutableList<Pair<ContentValues, ContentValues?>>>
    ): Boolean {
        val rowNewContent = newRowPair.first
        // 无bucketId ,非法记录,需过滤掉
        val newBucketId = rowNewContent.getAsInteger(KEY_BUCKET_ID) ?: return true
        var cShotId: Long? = null
        if (operationType == OperationType.UPDATE_LOCAL) {
            //非连拍图,不拦截，不做处理
            val newCShotId = checkAndFillCShotId(rowNewContent) ?: return false
            val rowOldContent = newRowPair.second
            val oldCShotId = checkAndFillCShotId(rowOldContent)
            if (oldCShotId == null) {
                GLog.d(TAG, DL) {
                    "interceptCShotIfNeed:split update ${getSimpleLog(rowNewContent, "new")},\n ${getSimpleLog(rowOldContent, "old")}"
                }
                /*
                云端连拍图,仅下载连拍封面图(偶现场景),
                此时关闭云服务,封面图会被当做普通图,图片路径也不会带有/cshot/xxx
                又开启云服务时,此封面又被恢复成连拍，走update记录,new的路径带有/cshot/xxx，old为普通图路径
                需将new为cshot图，old为普通图，拆分成insert和delete记录
                */
                splitUpdateOperation(rowNewContent, rowOldContent, outBatchMap)
                return true
            }
            cShotId = newCShotId
        } else {
            cShotId = checkAndFillCShotId(newRowPair.first)
        }

        return cShotId?.let { cshotId ->
            val oldContent = newRowPair.second
            //1.无效和pengding的cshot记录,则拦截过滤掉,即不需要统计此记录,直接跳过
            if (isInvalidRow(rowNewContent) && isInvalidRow(oldContent)) {
                return true
            }
            if (isPendingRow(rowNewContent) && isPendingRow(oldContent)) {
                return true
            }
            oldContent?.getAsInteger(KEY_BUCKET_ID)?.takeIf { newBucketId != it }?.let { oldBucketId ->
                //2.移动cshot图片,将旧的记录删除,避免再次移回原位置,导致有问题
                existCShotMap.remove(oldBucketId)
            }
            existCShotMap[newBucketId]?.let { existedAlbum ->
                updateExistedCShot(existedAlbum, operationType, newRowPair, newBucketId)
            } ?: run {
                //4.不存在,则为首次操作,对count增加
                existCShotMap[newBucketId] = CShotAlbum(cshotId, newBucketId, operationType, newRowPair).apply {
                    totalCount = 1
                    updateCount = 0
                    localCount = rowNewContent.getAsInteger(KEY_MEDIA_ID)?.takeIf { it > 0 }?.let { 1 } ?: 0
                }
                saveCShotAlbumMapAsync()
                GLog.d(TAG, DL) { "interceptCShotIfNeed: addCShot  ${getSimpleLog(newRowPair.first, operationType.toString())}" }
                false
            }
        } ?: false
    }

    /**
     * 拆分update操作为insert和delete操作
     *
     * 场景：
     * 前提条件：云端连拍图,仅下载连拍封面图,
     *
     * 此时关闭云服务,封面图会被当做普通图,图片路径也不会带有/cshot/xxx
     *
     * 又开启云服务时,此封面又被恢复成连拍，走update记录,new的路径带有/cshot/xxx，old为普通图路径
     */
    private fun splitUpdateOperation(
        rowNewContent: ContentValues,
        rowOldContent: ContentValues?,
        outBatchMap: MutableMap<OperationType, MutableList<Pair<ContentValues, ContentValues?>>>
    ) {
        // new为cshot图，old为普通图，拆分成insert和delete记录
        val insertRow = Pair(rowNewContent, null)
        if (interceptCShotIfNeed(OperationType.INSERT_LOCAL, insertRow, outBatchMap).not()) {
            outBatchMap.computeIfAbsent(OperationType.INSERT_LOCAL) { mutableListOf() }.add(insertRow)
        }
        rowOldContent?.let {
            outBatchMap.computeIfAbsent(OperationType.DELETE_LOCAL) { mutableListOf() }.add(Pair(it, null))
        }
    }

    private fun checkAndFillCShotId(content: ContentValues?): Long? {
        content ?: return null
        val cshotId = content.getAsLong(KEY_CSHOT_ID)
        if ((cshotId != null) && DatabaseUtils.isCShotIdValid(cshotId)) {
            return cshotId
        }
        // 云同步过程中,可能连拍图cshotId=0，但是路径是cshot的路径,需要通过路径生成cshotId
        val relativePath =
            content.getAsString(KEY_BUCKET_PATH)?.takeIf { it.length > 1 }?.let { it.substring(1) + "/" } ?: TextUtil.EMPTY_STRING
        if (relativePath.isNotEmpty() && DatabaseUtils.isMatchCShotDir(relativePath)) {
            return FilePathUtils.getBucketId(content.getAsString(KEY_DATA))?.toLong()?.apply {
                content.put(KEY_CSHOT_ID, this)
                GLog.d(TAG, DL) {
                    "checkAndFillCShotId:make cshot id $this->$relativePath${PathMask.mask(content.getAsString(KEY_DISPLAY_NAME))}"
                }
            }
        }
        return null
    }

    @Suppress("LongMethod")
    private fun updateExistedCShot(
        album: CShotAlbum,
        operationType: OperationType,
        newRowPair: Pair<ContentValues, ContentValues?>,
        newBucketId: Int
    ): Boolean {
        val rowNewContent = newRowPair.first
        var isUpdateCount = false
        when (operationType) {
            OperationType.INSERT_LOCAL -> {
                calculateAlbumCountWhenInsert(album.rowContent?.first, rowNewContent, album)
                if (operationType == album.operationType) {
                    updateExistedContentIfNeed(album, newRowPair)
                }
                saveCShotAlbumMapAsync()
            }

            OperationType.DELETE_LOCAL -> {
                // 关闭云服务,只移除连拍一部分云端图片,需要修正总数,更新总数置为0,避免后续操作时误判
                calculateAlbumCountWhenDelete(rowNewContent, album)
                isUpdateCount = true
            }

            else -> {
                val rowOldContent = newRowPair.second
                // 连拍收藏只会收藏一张照片，取消收藏也只是取消一张,直接返回false即可
                if (rowOldContent?.getAsInteger(KEY_IS_FAVORITE) != rowNewContent.getAsInteger(KEY_IS_FAVORITE)) return false
                if (newBucketId != rowOldContent?.getAsInteger(KEY_BUCKET_ID)) {
                    // 移动连拍图
                    album.totalCount++
                    updateExistedContentIfNeed(album, newRowPair)
                    saveCShotAlbumMapAsync()
                    return true
                }
                isUpdateCount = calculateAlbumCountWhenUpdate(rowOldContent, rowNewContent, album)
            }
        }
        if (isUpdateCount) {
            val isCShotMaxCount = album.updateCount >= album.totalCount
            if (isFirstUpdate(album, operationType)) {
                // 更新的第一张,则更新为当前的操作类型:避免用的还是上一次的type
                album.operationType = operationType
                //首次更新或者关闭云服务,移除连拍场景,不会更改updateCount,可能updateCount=0,只需要赋值一次,否则可能会将要显示连拍内容覆盖
                album.rowContent = newRowPair
            }

            if (isCShotMaxCount.not()) {
                //不是连拍最后一张照片,检测更新直到为封面的内容
                updateExistedContentIfNeed(album, newRowPair)
                return true
            }

            /*
             *1.最后一条记录才去更新,避免云服务只同步连拍一部分云端图,也更新图集数量;
             *2.使用最后一条记录newRowPair去更新表中数量,所以只需更新newRowPair内容,不改变地址:newRowPair是需要加入批处理的map中的
             */
            val existedRowContent = album.rowContent
            album.rowContent = newRowPair
            if (album.updateCount > 1) {
                if (isUseExistReplaceNewContent(newRowPair, existedRowContent)) {
                    GLog.d(TAG, DL) { "updateExistedCShot: replace content " }
                    //使用同组连拍缓存的内容来替代最后一条无效记录内容
                    existedRowContent?.first?.let { newRowPair.first.putAll(it) }
                    existedRowContent?.second?.let { newRowPair.second?.putAll(it) }
                } else {
                    existedRowContent?.let { updateExistedContentIfNeed(album, it) }
                }
            }
            val updateCount = album.updateCount
            album.updateCount = 0
            if (album.totalCount <= 0) {
                existCShotMap.remove(newBucketId)
                GLog.d(TAG, DL) { "updateExistedCShot: remove ${getAlbumLog(album)}" }
            }
            GLog.d(TAG, DL) { "updateExistedCShot:updateCount=$updateCount->0 ${getAlbumLog(album)}" }
            saveCShotAlbumMapAsync()
            return false
        }
        return true
    }

    /**
     *  新增连拍图和本地的连拍图有效性不一致(如一部分在回收站一部分在相机)时,需将updateCount++
     *  场景:开启云-同步一组连拍图(一组3张)-大图下载一张-回到最近项目删除所有照片--关闭云--回到回收站(此时仅剩已下载的那一张)-恢复-开启云
     *  查看相机图集:存在那组连拍图的1张,查看回收站:也存在这组连拍图的1张
     *  此时若从回收站中恢复连拍图，那么相机图集数量计算的不对,因为updateCount<count,故不会统计,需要增加此处理
     */
    private fun calculateAlbumCountWhenInsert(cacheNewContent: ContentValues?, newContent: ContentValues, album: CShotAlbum) {
        album.totalCount++
        cacheNewContent ?: return
        val oldInvalid = cacheNewContent.getAsInteger(KEY_INVALID)
        val newInvalid = newContent.getAsInteger(KEY_INVALID)
        val isChange = when {
            isSameInvalid(oldInvalid, newInvalid).not() -> {
                newInvalid?.takeIf { DatabaseUtils.isDataValid(it) }?.let { album.updateCount++ } ?: album.totalCount--
                true
            }

            cacheNewContent.getAsInteger(KEY_IS_TRASHED) != newContent.getAsInteger(KEY_IS_TRASHED) -> {
                album.updateCount++
                true
            }

            cacheNewContent.getAsInteger(KEY_IS_PENDING) != newContent.getAsInteger(KEY_IS_PENDING) -> {
                if (isPendingRow(newContent)) album.totalCount-- else album.updateCount++
                true
            }

            else -> false
        }
        if (isChange) {
            GLog.d(TAG, DF) { "calculateAlbumCountWhenInsert:classified ${album.operationType} ${album.updateCount}:${album.totalCount}" }
        }
    }

    private fun calculateAlbumCountWhenDelete(newContent: ContentValues, album: CShotAlbum) {
        val newInvalid = newContent.getAsInteger(KEY_INVALID)
        if (((newInvalid != null) && DatabaseUtils.isDataValid(newInvalid)) ||
            (IS_PENDING_FALSE == newContent.getAsInteger(KEY_IS_PENDING))
        ) {
            // delete有效数据,才对totalCount--
            album.totalCount--
        }
    }

    /**
     * 使用缓存的内容(同组连拍的名字倒数第二小的图片内容)覆盖新的内容
     *
     * 连拍数量超过两张,则需要处理如下场景：
     *
     * 开启云-同步连拍-进大图下载连拍封面--会图集-删除连拍到回收站-关闭云--从回收站中恢复连拍--开启云，又同步连拍到回收站,
     * 已下载的封面也回到回收站---回收站中恢复连拍，
     *
     * 偶现结果：已下载的封面被标记为invalid=1 trashed=1,同组的连拍缩图invalid=0 trashed=0
     *
     * 当封面作为最后一条记录，用来计算图集数量时,则由于是无效的,导致图集数量计算少1，计算错误
     * 需将这条记录过滤掉
     */
    private fun isUseExistReplaceNewContent(
        newRowContent: Pair<ContentValues, ContentValues?>,
        existedRowContent: Pair<ContentValues, ContentValues?>?
    ): Boolean {
        existedRowContent ?: return false
        val oldContent = newRowContent.second ?: return false
        val existedOldContent = existedRowContent.second ?: return false
        val newContent = newRowContent.first
        val existedNewContent = existedRowContent.first

        // old有效性一致,new不一致,则使用替换
        return isSameInvalid(oldContent.getAsInteger(KEY_INVALID), existedOldContent.getAsInteger(KEY_INVALID)) &&
                oldContent.getAsInteger(KEY_IS_TRASHED) == existedOldContent.getAsInteger(KEY_IS_TRASHED) &&
                isSameInvalid(newContent.getAsInteger(KEY_INVALID), existedNewContent.getAsInteger(KEY_INVALID)).not() &&
                newContent.getAsInteger(KEY_IS_TRASHED) != existedNewContent.getAsInteger(KEY_IS_TRASHED)
    }

    /**
     * 是否首次更新
     *
     * 1.首次是删除操作
     * 或
     * 2.首次是更新操作
     */
    private fun isFirstUpdate(
        existedAlbum: CShotAlbum,
        operationType: OperationType
    ) = (existedAlbum.updateCount == 1) ||
            ((operationType == OperationType.DELETE_LOCAL) && (existedAlbum.operationType != OperationType.DELETE_LOCAL))

    private fun calculateAlbumCountWhenUpdate(oldContent: ContentValues?, newContent: ContentValues, existedAlbum: CShotAlbum): Boolean {
        val oldInvalid = oldContent?.getAsInteger(KEY_INVALID)
        val newInvalid = newContent.getAsInteger(KEY_INVALID)
        return when {
            isSameInvalid(oldInvalid, newInvalid).not() -> {
                val isChangeToInvalid = newInvalid?.let { DatabaseUtils.isDataValid(it).not() } ?: true
                calculateAlbumCountWhenValidChanged(existedAlbum, isChangeToInvalid)
                true
            }

            oldContent?.getAsInteger(KEY_IS_TRASHED) != newContent.getAsInteger(KEY_IS_TRASHED) -> {
                existedAlbum.updateCount++
                true
            }

            oldContent?.getAsInteger(KEY_IS_PENDING) != newContent.getAsInteger(KEY_IS_PENDING) -> {
                calculateAlbumCountWhenValidChanged(existedAlbum, isPendingRow(newContent))
                true
            }

            else -> false
        }
    }

    /**
     * 有效性变更时,更新count
     * @param isChangeToInvalid 是否从有效变无效
     */
    private fun calculateAlbumCountWhenValidChanged(existedAlbum: CShotAlbum, isChangeToInvalid: Boolean) {
        if (isChangeToInvalid) {
            // 有效->无效,相当于删除
            existedAlbum.totalCount--
            GLog.d(TAG, DF) { "calculateAlbumCountWhenValidChanged: valid to invalid ${getAlbumLog(existedAlbum)}" }
        } else {
            // 无效->有效
            existedAlbum.totalCount++
            existedAlbum.updateCount++
        }
    }

    private fun saveCShotAlbumMapAsync() {
        val cloneCShotMap = mutableMapOf<Int, CShotAlbum>().apply { putAll(existCShotMap) }
        AppScope.launch(Dispatchers.SyncAlbumSetTask) {
            saveCShotAlbumMap(cloneCShotMap)
        }
    }

    /**
     * invalid是否是相同的
     * 0 和 4均为有效,认为是相同的
     */
    private fun isSameInvalid(existedInvalid: Int?, newInvalid: Int?): Boolean {
        if (existedInvalid == newInvalid) {
            return true
        }

        if (existedInvalid != null && newInvalid != null) {
            return DatabaseUtils.isDataValid(existedInvalid) && DatabaseUtils.isDataValid(newInvalid)
        }
        return false
    }

    /**
     * 连拍图取名字最小作为封面,需要将用于统计的pair更新为封面
     */
    private fun updateExistedContentIfNeed(
        existedAlbum: CShotAlbum,
        newRowPair: Pair<ContentValues, ContentValues?>
    ) {
        if (existedAlbum.rowContent === newRowPair) {
            GLog.d(TAG, DF) { "updateExistedContentIfNeed: address same return" }
            return
        }
        val existedNewContent = existedAlbum.rowContent?.first
        if (existedNewContent == null) {
            // 图集表搬迁或全量同步或冷启动读取sp时,existedNewContent为null,直接更新
            existedAlbum.rowContent = newRowPair
            GLog.d(TAG, DL) { "updateExistedContentIfNeed: null:first add" }
            return
        }
        val rowNewContent: ContentValues = newRowPair.first
        existedNewContent.getAsString(KEY_DISPLAY_NAME)?.takeIf { existedDisplayName ->
            // 负值为小于,连拍图取名字最小的作为封面
            (rowNewContent.getAsString(KEY_DISPLAY_NAME)?.compareTo(existedDisplayName) ?: 0) < 0
        }?.let {
            // 更新用于统计的连拍图为文件名最小的一张图,此content已经放入了batchMap,不可更换对象地址,所以需更新内容
            existedNewContent.putAll(rowNewContent)
            val existedOldContent = existedAlbum.rowContent?.second
            if (newRowPair.second?.isEmpty != false) {
                // old记录是update,new是insert,需替换成insert
                existedOldContent?.clear()
                return
            }
            //update操作,替换old
            existedOldContent?.putAll(newRowPair.second)
        }
    }

    private fun isInvalidRow(rowContent: ContentValues?): Boolean {
        rowContent ?: return true
        val invalid = rowContent.getAsInteger(KEY_INVALID) ?: 0
        return (invalid != INVALID_NORMAL) && (invalid != INVALID_NORMAL_NOT_IN_MEDIA_STORE)
    }

    /**
     * pengding = 1的图片不显示在界面上,不需要统计
     */
    private fun isPendingRow(rowContent: ContentValues?): Boolean {
        rowContent ?: return true
        return GalleryStore.GalleryColumns.LocalColumns.IS_PENDING_TRUE == rowContent.getAsInteger(KEY_IS_PENDING)
    }

    private fun getSimpleLog(values: ContentValues?, tag: String): String {
        return "$tag[invalid=${values?.getAsInteger(KEY_INVALID)},trash=${values?.getAsInteger(KEY_IS_TRASHED)}," +
                "fav=${values?.getAsInteger(KEY_IS_FAVORITE)},pending=${values?.getAsInteger(KEY_IS_PENDING)}," +
                "${values?.getAsInteger(KEY_CSHOT_ID)}->${values?.getAsInteger(KEY_BUCKET_ID)}," +
                "${values?.getAsString(KEY_BUCKET_PATH)} ${PathMask.mask(values?.getAsString(KEY_DISPLAY_NAME))}];"
    }

    private fun getAlbumLog(album: CShotAlbum): String {
        return "A[${album.operationType},count=${album.totalCount}:${album.updateCount}:${album.localCount}, ${album.cShotId}->${album.bucketId}," +
                "\n${getSimpleLog(album.rowContent?.first, "new")}\n${getSimpleLog(album.rowContent?.second, "old")}]"
    }

    override fun receiveEvent(event: Int) {
        val lastValidMinGenTime = validRowMinTime
        val fullSyncGenTime = loadGenerationModified()
        validRowMinTime = fullSyncGenTime
        GLog.d(TAG, DL) { "receiveEvent:$event validTime:$lastValidMinGenTime->$validRowMinTime" }
        // 首次搬迁图集表的过程中,存在未统计到的Diff,需要重新处理
        if (EVENT_MIGRATE_ALBUM_END == event) {
            dealDropData(fullSyncGenTime)
            return
        }
        // 全量同步结束
        if (EVENT_FULL_SYNC_ALBUM_END == event) {
            // 首次搬迁图集表的过程中,存在未统计到的Diff,需要重新处理
            filterOutBatchDataIfNeed(fullSyncGenTime)
            return
        }
    }

    /**
     * 过滤掉批处理数据中的脏数据,小于等于maxModifiedTime这个时间的数据,表明这些数据已经在全量同步时,统计过了,需要过滤掉,否则会导致图集数量统计不准确
     *
     * maxModifiedTime:表的最新更新时间
     */
    private fun filterOutBatchDataIfNeed(validMinGenTime: Long) {
        /* 全量同步任务和批处理任务都在同一个队列中,若全量同步任务执行时,有批处理任务待执行,
         * 那么同步结束后,批处理任务执行(此时这批数据是脏数据,全量同步时已经统计),会导致重复统计,最终图集数量统计错误
         * 所以需将已入队的批处理任务都要移除掉
         */
        taskDispatcher.removeAll()
        // 过滤掉还未生成批处理任务的批处理数据中的脏数据:即正在收集的批处理数据,还未到时间,所以还未生成任务
        if (isBatchEmpty()) {
            GLog.d(TAG, DL) { "filterOutBatchDataIfNeed: batch empty,no filter out" }
            return
        }
        if (validMinGenTime > DEFAULT_GENERATION_MODIFIED) {
            removeRecordIf { content -> getRowGenerationTime(content) <= validMinGenTime }
            GLog.d(TAG, DL) { "filterOutBatchDataIfNeed: modified=$validMinGenTime" }
        }
    }

    private fun getRowGenerationTime(newRow: ContentValues) = (newRow.getAsLong(KEY_GENERATION_MODIFIED) ?: 0L)

    private fun dealDropData(validMinGenTime: Long) {
        if (cacheDropRowMap.isEmpty()) {
            GLog.d(TAG, DL) { "dealDropData: drop data is empty,no filter out" }
            return
        }
        if (validMinGenTime > DEFAULT_GENERATION_MODIFIED) {
            addDropDataToBatchIfNeed(validMinGenTime)
            cacheDropRowMap.clear()
        }
    }

    /**
     * 未统计到的diff,需重新触发计算
     * @param maxMigrateModifiedTime 首次搬迁图集表时,搬迁的记录中最新时间
     */
    private fun addDropDataToBatchIfNeed(maxMigrateModifiedTime: Long) {
        val list = mutableListOf<Pair<OperationType, Pair<ContentValues, ContentValues?>>>()
        val sbLog = StringBuilder()
        cacheDropRowMap.forEach { (type, rowList) ->
            rowList.forEach { row ->
                // 缓存的Diff中,若记录的generationModify>maxMigrateModifiedTime 说明是未统计到的数据,需要重新触发对其进行统计
                if (getRowGenerationTime(row.first) > maxMigrateModifiedTime) {
                    list.add(Pair(type, row))
                }
                sbLog.append("$type:${rowList.size};")
            }
        }
        if (list.isNotEmpty()) {
            processDiff(DataDiffValuesSnapshot(diffValuesList = list))
        }
        GLog.d(TAG, DL) { "addDropDataToBatchIfNeed, addCount=${list.size},$sbLog" }
    }

    companion object {
        private const val TAG = "AlbumDataOperationProcessor"
    }
}