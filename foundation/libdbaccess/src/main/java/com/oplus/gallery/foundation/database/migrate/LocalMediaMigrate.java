/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - LocalMediaMigrate.java
 ** Description: migrate to local_media table
 ** Version: 1.0
 ** Date : 2020/04/03
 ** Author: biao.chen@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  biao.chen@Apps.Gallery3D  2020/04/03        build this module
 ********************************************************************************/
package com.oplus.gallery.foundation.database.migrate;

import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.net.Uri;
import android.provider.MediaStore;

import com.oplus.gallery.foundation.database.store.GalleryStore;
import com.oplus.gallery.foundation.database.util.DatabaseUtils;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.standard_lib.util.io.IOUtils;

import java.util.ArrayList;

public class LocalMediaMigrate extends BaseMigrate {
    private static final String TAG = DatabaseUtils.DATA_TAG + "LocalMediaMigrate";

    // only query FileColumns include column
    private static final String[] MEDIA_FILES_TO_LOCAL_MEDIA_PROJECTION = {
            MediaStore.MediaColumns._ID,
            GalleryStore.GalleryColumns.LocalColumns.DATA, // MediaStore.MediaColumns.DATA,
            GalleryStore.GalleryColumns.LocalColumns.SIZE, // MediaStore.MediaColumns.SIZE,
            GalleryStore.GalleryColumns.LocalColumns.DATE_ADDED, // MediaStore.MediaColumns.DATE_ADDED,
            GalleryStore.GalleryColumns.LocalColumns.DATE_MODIFIED, // MediaStore.MediaColumns.DATE_MODIFIED,
            GalleryStore.GalleryColumns.LocalColumns.DATE_TAKEN, //MediaStore.MediaColumns.DATE_TAKEN,
            GalleryStore.GalleryColumns.LocalColumns.MIME_TYPE, // MediaStore.MediaColumns.MIME_TYPE,
            GalleryStore.GalleryColumns.LocalColumns.TITLE, // MediaStore.MediaColumns.TITLE,
            GalleryStore.GalleryColumns.LocalColumns.DISPLAY_NAME, // MediaStore.MediaColumns.DISPLAY_NAME,
            GalleryStore.GalleryColumns.LocalColumns.ORIENTATION, //MediaStore.MediaColumns.ORIENTATION,
            GalleryStore.GalleryColumns.LocalColumns.BUCKET_ID, //MediaStore.MediaColumns.BUCKET_ID,
            GalleryStore.GalleryColumns.LocalColumns.BUCKET_NAME, //MediaStore.MediaColumns.BUCKET_DISPLAY_NAME,
            GalleryStore.GalleryColumns.LocalColumns.DURATION, //MediaStore.MediaColumns.DURATION,
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE, //MediaStore.Files.FileColumns.MEDIA_TYPE,
            GalleryStore.GalleryColumns.LocalColumns.WIDTH, // MediaStore.MediaColumns.WIDTH,
            GalleryStore.GalleryColumns.LocalColumns.HEIGHT, // MediaStore.MediaColumns.HEIGHT,
            GalleryStore.GalleryColumns.LocalColumns.IS_PENDING, //MediaStore.MediaColumns.IS_PENDING,
            GalleryStore.GalleryColumns.LocalColumns.PRIMARY_DIR, //MediaStore.MediaColumns.PRIMARY_DIRECTORY,
            GalleryStore.GalleryColumns.LocalColumns.SECONDARY_DIR, //MediaStore.MediaColumns.SECONDARY_DIRECTORY,
            GalleryStore.GalleryColumns.LocalColumns.RELATIVE_PATH, //MediaStore.MediaColumns.RELATIVE_PATH,
            GalleryStore.GalleryColumns.LocalColumns.VOLUME_NAME //MediaStore.MediaColumns.VOLUME_NAME
    };

    private static final int INDEX_ID = 0;
    private static final int INDEX_DATA = DatabaseUtils.indexIncrease(INDEX_ID);
    private static final int INDEX_SIZE = DatabaseUtils.indexIncrease(INDEX_DATA);
    private static final int INDEX_DATE_ADDED = DatabaseUtils.indexIncrease(INDEX_SIZE);
    private static final int INDEX_DATE_MODIFIED = DatabaseUtils.indexIncrease(INDEX_DATE_ADDED);
    private static final int INDEX_DATE_TAKEN = DatabaseUtils.indexIncrease(INDEX_DATE_MODIFIED);
    private static final int INDEX_MIME_TYPE = DatabaseUtils.indexIncrease(INDEX_DATE_TAKEN);
    private static final int INDEX_TITLE = DatabaseUtils.indexIncrease(INDEX_MIME_TYPE);
    private static final int INDEX_DISPLAY_NAME = DatabaseUtils.indexIncrease(INDEX_TITLE);
    private static final int INDEX_ORIENTATION = DatabaseUtils.indexIncrease(INDEX_DISPLAY_NAME);
    private static final int INDEX_BUCKET_ID = DatabaseUtils.indexIncrease(INDEX_ORIENTATION);
    private static final int INDEX_BUCKET_DISPLAY_NAME = DatabaseUtils.indexIncrease(INDEX_BUCKET_ID);
    private static final int INDEX_DURATION = DatabaseUtils.indexIncrease(INDEX_BUCKET_DISPLAY_NAME);
    private static final int INDEX_MEDIA_TYPE = DatabaseUtils.indexIncrease(INDEX_DURATION);
    private static final int INDEX_WIDTH = DatabaseUtils.indexIncrease(INDEX_MEDIA_TYPE);
    private static final int INDEX_HEIGHT = DatabaseUtils.indexIncrease(INDEX_WIDTH);
    private static final int INDEX_IS_PENDING = DatabaseUtils.indexIncrease(INDEX_HEIGHT);
    private static final int INDEX_PRIMARY_DIRECTORY = DatabaseUtils.indexIncrease(INDEX_IS_PENDING);
    private static final int INDEX_SECONDARY_DIRECTORY = DatabaseUtils.indexIncrease(INDEX_PRIMARY_DIRECTORY);
    private static final int INDEX_RELATIVE_PATH = DatabaseUtils.indexIncrease(INDEX_SECONDARY_DIRECTORY);
    private static final int INDEX_VOLUME_NAME = DatabaseUtils.indexIncrease(INDEX_RELATIVE_PATH);


    @Override
    protected String getTag() {
        return TAG;
    }

    @Override
    protected int getTableType() {
        return 0;
    }

    @Override
    protected String getOldDB() {
        return null;
    }

    @Override
    protected String getOldSql(boolean simpleCheck) {
        return null;
    }

    @Override
    protected Uri getMigrateToUri() {
        return GalleryStore.GalleryMedia.getContentUri();
    }

    @Override
    public boolean canMigrate(Context context) {
        Uri uri = MediaStore.Files.getContentUri(MediaStore.VOLUME_EXTERNAL);
        ContentResolver cr = context.getContentResolver();

        int count = 0;
        Cursor cursor = null;
        try {
            String whereClause = GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE
                    + "  = " + GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE
                    + " or " + GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE
                    + "  = " + GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO;
            cursor = cr.query(uri, new String[]{MediaStore.MediaColumns._ID}, whereClause, null, null);

            count = (cursor != null) ? cursor.getCount() : 0;
        } catch (Exception e) {
            GLog.w(getTag(), "canMigrate, error:", e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
        GLog.d(getTag(), "canMigrate, count: " + count);
        return (count > 0);
    }

    @Override
    protected ContentValues buildContentValues(Cursor cursor) {
        ContentValues cvs = new ContentValues();
        cvs.put(GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID, cursor.getInt(INDEX_ID));
        cvs.put(MEDIA_FILES_TO_LOCAL_MEDIA_PROJECTION[INDEX_DATA], cursor.getString(INDEX_DATA));
        cvs.put(MEDIA_FILES_TO_LOCAL_MEDIA_PROJECTION[INDEX_SIZE], cursor.getLong(INDEX_SIZE));
        cvs.put(MEDIA_FILES_TO_LOCAL_MEDIA_PROJECTION[INDEX_DATE_ADDED], cursor.getLong(INDEX_DATE_ADDED));
        cvs.put(MEDIA_FILES_TO_LOCAL_MEDIA_PROJECTION[INDEX_DATE_MODIFIED], cursor.getLong(INDEX_DATE_MODIFIED));
        cvs.put(MEDIA_FILES_TO_LOCAL_MEDIA_PROJECTION[INDEX_DATE_TAKEN], cursor.getLong(INDEX_DATE_TAKEN));
        cvs.put(MEDIA_FILES_TO_LOCAL_MEDIA_PROJECTION[INDEX_MIME_TYPE], cursor.getString(INDEX_MIME_TYPE));
        cvs.put(MEDIA_FILES_TO_LOCAL_MEDIA_PROJECTION[INDEX_TITLE], cursor.getString(INDEX_TITLE));
        cvs.put(MEDIA_FILES_TO_LOCAL_MEDIA_PROJECTION[INDEX_DISPLAY_NAME], cursor.getString(INDEX_DISPLAY_NAME));
        cvs.put(MEDIA_FILES_TO_LOCAL_MEDIA_PROJECTION[INDEX_ORIENTATION], cursor.getInt(INDEX_ORIENTATION));
        cvs.put(MEDIA_FILES_TO_LOCAL_MEDIA_PROJECTION[INDEX_BUCKET_ID], cursor.getInt(INDEX_BUCKET_ID));
        cvs.put(MEDIA_FILES_TO_LOCAL_MEDIA_PROJECTION[INDEX_BUCKET_DISPLAY_NAME], cursor.getString(INDEX_BUCKET_DISPLAY_NAME));
        cvs.put(MEDIA_FILES_TO_LOCAL_MEDIA_PROJECTION[INDEX_DURATION], cursor.getLong(INDEX_DURATION));
        cvs.put(MEDIA_FILES_TO_LOCAL_MEDIA_PROJECTION[INDEX_MEDIA_TYPE], cursor.getInt(INDEX_MEDIA_TYPE));
        cvs.put(MEDIA_FILES_TO_LOCAL_MEDIA_PROJECTION[INDEX_WIDTH], cursor.getInt(INDEX_WIDTH));
        cvs.put(MEDIA_FILES_TO_LOCAL_MEDIA_PROJECTION[INDEX_HEIGHT], cursor.getInt(INDEX_HEIGHT));
        cvs.put(MEDIA_FILES_TO_LOCAL_MEDIA_PROJECTION[INDEX_IS_PENDING], cursor.getInt(INDEX_IS_PENDING));
        cvs.put(MEDIA_FILES_TO_LOCAL_MEDIA_PROJECTION[INDEX_PRIMARY_DIRECTORY], cursor.getString(INDEX_PRIMARY_DIRECTORY));
        cvs.put(MEDIA_FILES_TO_LOCAL_MEDIA_PROJECTION[INDEX_SECONDARY_DIRECTORY], cursor.getString(INDEX_SECONDARY_DIRECTORY));
        cvs.put(MEDIA_FILES_TO_LOCAL_MEDIA_PROJECTION[INDEX_RELATIVE_PATH], cursor.getString(INDEX_RELATIVE_PATH));
        cvs.put(MEDIA_FILES_TO_LOCAL_MEDIA_PROJECTION[INDEX_VOLUME_NAME], cursor.getString(INDEX_VOLUME_NAME));

        return cvs;
    }

    @Override
    protected int compareAndDeleteSameMedias(Context context, SQLiteDatabase database) {
        return 0;
    }

    @Override
    protected int deleteOldMedias(Context context, SQLiteDatabase database, ContentValues[] toArrays) {
        return 0;
    }

    @Override
    public int migrateTo(Context context) {
        GLog.d(getTag(), "migrateTo, start");
        // MediaStore.VOLUME_EXTERNAL)
        Uri uri = MediaStore.Files.getContentUri(MediaStore.VOLUME_EXTERNAL);
        ContentResolver cr = context.getContentResolver();

        int migrateCnt = 0;
        Cursor cursor = null;
        try {
            long time = System.currentTimeMillis();
            String whereClause = GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE
                    + "  = " + GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE
                    + " or " + GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE
                    + "  = " + GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO;
            cursor = cr.query(uri, MEDIA_FILES_TO_LOCAL_MEDIA_PROJECTION, whereClause, null, null);

            GLog.d(getTag(), "migrateTo, getCount: " + ((cursor != null) ? cursor.getCount() : null));
            GLog.d(getTag(), "migrateTo, query time: " + (System.currentTimeMillis() - time));
            time = System.currentTimeMillis();
            if ((cursor != null) && (cursor.getCount() > 0)) {
                ArrayList<ContentValues> toValues = new ArrayList<>();
                while (cursor.moveToNext()) {
                    ContentValues cvs = buildContentValues(cursor);

                    toValues.add(cvs);
                }
                GLog.d(getTag(), "migrateTo, build time: " + (System.currentTimeMillis() - time));
                time = System.currentTimeMillis();
                if (!toValues.isEmpty()) {
                    migrateCnt = insertOrUpdate(context, null, toValues);
                }
            }
            GLog.d(getTag(), "migrateTo, migrate time: " + (System.currentTimeMillis() - time));
        } catch (Exception e) {
            GLog.w(getTag(), "migrateTo, error:" + e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
        return migrateCnt;
    }
}
