/***********************************************************
 * * Copyright (C), 2018-2028, OPlus Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File       :  TravelCountMediaTypeConvert.kt
 * * Description:  旅行图集数量获取cursor转换类
 * * Version: 1.0
 * * Date : 2025/04/22
 * * Author: pusong
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>         <version>  <desc>
 * *  pusong        2025/04/22     1.0        create
 ************************************************************/
package com.oplus.gallery.foundation.dbaccess.convert

import android.database.Cursor
import com.oplus.gallery.foundation.database.store.GalleryStore

/**
 * 旅行图集数量获取cursor转换类
 */
class TravelCountMediaTypeConvert : CountMediaTypeConvert() {
    override fun convert(cursor: Cursor?): IntArray {
        if ((cursor == null) || (cursor.count <= 0)) {
            return intArrayOf(0, 0, 0)
        } else {
            var count = 0
            var imageCount = 0
            var videoCount = 0
            val countIdx = cursor.getColumnIndex(GalleryStore.ScanTravelViewColumns.COUNT)
            val imageCountIdx = cursor.getColumnIndex(GalleryStore.ScanTravelViewColumns.IMAGE_COUNT)
            val videoCountIdx = cursor.getColumnIndex(GalleryStore.ScanTravelViewColumns.VIDEO_COUNT)
            if (cursor.moveToNext()) {
                count = cursor.getInt(countIdx)
                imageCount = cursor.getInt(imageCountIdx)
                videoCount = cursor.getInt(videoCountIdx)
            }
            return intArrayOf(count, imageCount, videoCount)
        }
    }
}