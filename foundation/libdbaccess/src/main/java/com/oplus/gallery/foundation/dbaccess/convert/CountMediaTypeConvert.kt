/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - CountMediaTypeConvert.kt
 * Description: media type分类计数
 * Version: 1.0
 * Date: 2020/10/28
 * Author: zhengyirui@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_FOOTER
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * zhengyirui@Apps.Gallery3D      2020/10/28      1.0              OPLUS_FEATURE_FOOTER
 *************************************************************************************************/

package com.oplus.gallery.foundation.dbaccess.convert

import android.database.Cursor
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns

open class CountMediaTypeConvert : IConvert<Cursor, IntArray> {
    companion object {
        const val COLUMN_INDEX_MEDIA_TYPE = 0
        const val COLUMN_INDEX_COUNT = 1
    }
    /**
     * returns {allCount, imageCount, videoCount}
     */
    override fun convert(cursor: Cursor?): IntArray {
        if ((cursor == null) || (cursor.count <= 0)) {
            return intArrayOf(0, 0, 0)
        } else {
            var allCount = 0
            var imageCount = 0
            var videoCount = 0
            while (cursor.moveToNext()) {
                when (cursor.getInt(COLUMN_INDEX_MEDIA_TYPE)) {
                    LocalColumns.MEDIA_TYPE_IMAGE -> imageCount = cursor.getInt(COLUMN_INDEX_COUNT)

                    LocalColumns.MEDIA_TYPE_VIDEO -> videoCount = cursor.getInt(COLUMN_INDEX_COUNT)
                }
            }
            allCount = imageCount + videoCount
            return intArrayOf(allCount, imageCount, videoCount)
        }
    }
}