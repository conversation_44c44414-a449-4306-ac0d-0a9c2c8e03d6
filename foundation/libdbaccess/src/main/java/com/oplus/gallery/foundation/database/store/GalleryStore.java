/***********************************************************
 * * Copyright (C), 2018-2028, OPlus Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File       :  - ${FILE_NAME}
 * * Description:
 * * Version: 1.0
 * * Date : 2020/03/10
 * * Author: biao.chen@Apps.Gallery3D
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>                 <data>     <version>  <desc>
 * *  biao.chen@Apps.Gallery3D  2020/03/10        build this module
 ****************************************************************/

package com.oplus.gallery.foundation.database.store;

import android.net.Uri;
import android.provider.BaseColumns;
import android.provider.MediaStore;

import androidx.annotation.IntDef;

import java.util.HashSet;

import kotlin.collections.SetsKt;

public final class GalleryStore {

    public static final String AUTHORITY = "com.oplus.gallery.database.provider.gallery";
    public static final String COPY_TABLE_TYPE = "copy_table_type";
    public static final String BACKUP_SCAN_FACE_TABLE = "backup_scan_face_table";
    public static final String ROLLBACK_SCAN_FACE_TABLE = "rollback_scan_face_table";

    public static final String OP_RECYCLE_BIN_DATABASE_NAME = "recyclebin.db";
    public static final String OP_RECYCLE_BIN_TABLE_NAME = "recyclebin";
    public static final String OP_RECYCLE_BIN_PERMISSION_PATH = "/files";
    public static final String RECYCLER_RELATIVE_PATH_R = ".trashed-";

    private static final String GALLERY_CONTENT_URI = "content://" + AUTHORITY;

    private GalleryStore() {
    }

    public static String getGalleryUri() {
        return GALLERY_CONTENT_URI;
    }

    public static class GalleryMedia extends GalleryColumns {
        /**
         * 端云数据融合需要使用一张表格来，为避免本地数据搬迁的损耗，扩展local_media来容纳cloud和recycle数据，
         * 数据库中的表明仍然沿用之前的名字，不改动
         */
        public static final String TAB = "local_media";
        /**
         * 对应于CODEC_TYPE列，表示尝试获取media codec type时返回值为null；
         * 如果直接写入null值，每次都会重新尝试获取media codec type，造成性能上的额外开销
         */
        public static final String CODEC_TYPE_EMPTY = "";
        /**
         * 本地业务访问标识，业务不应该直接使用，
         * 应使用{@link com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao.TableType.LOCAL_MEDIA}
         * 通过dao来访问数据库
         */
        public static final String LOCAL_TAB_INTERNAL = "local_media_internal";
        /**
         * 云同步业务访问标识，业务不应该直接使用，
         * 应使用{@link com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao.TableType.CLOUD_MEDIA}
         * 通过dao来访问数据库
         */
        public static final String CLOUD_TAB_INTERNAL = "cloud_media_internal";
        /**
         * 和local_media对应，且仅包含本地图片或视频的视图表，业务不应该直接使用，
         * 应使用{@link com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao.TableType.FILTER_LOCAL_VIEW}
         * 通过dao来访问数据库
         */
        public static final String FILTER_LOCAL_VIEW = "filter_local_view";

        private static final Uri GALLERY_URI = Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        private static final Uri LOCAL_URI = Uri.parse(GALLERY_CONTENT_URI + "/" + LOCAL_TAB_INTERNAL);
        private static final Uri CLOUD_URI = Uri.parse(GALLERY_CONTENT_URI + "/" + CLOUD_TAB_INTERNAL);
        private static final Uri FILTER_LOCAL_VIEW_URI = Uri.parse(GALLERY_CONTENT_URI + "/" + FILTER_LOCAL_VIEW);

        /**
         * 获取local_media表的uri
         * <p>
         * local_media表中包含云端和本地的字段，字段较多，刷新会比较频繁，
         * 尽量不要直接监听整个表，
         * 监听本地字段可以使用：{@link GalleryMedia#getLocalContentUri() }
         * 监听云端字段可以使用：{@link GalleryMedia#getCloudContentUri() }
         *
         * @return Uri
         */
        public static final Uri getContentUri() {
            return GALLERY_URI;
        }

        /**
         * 获取本地字段的content uri，本地显示相关的业务建议监听这个uri，以减少界面刷新
         */
        public static final Uri getLocalContentUri() {
            return LOCAL_URI;
        }

        /**
         * 获取filter_local_view视图字段的content uri
         */
        public static Uri getLocalViewContentUri() {
            return FILTER_LOCAL_VIEW_URI;
        }

        /**
         * 获取云端相关的字段的 content uri，云端状态相关的业务建议监听这个uri，以减少界面刷新
         */
        public static final Uri getCloudContentUri() {
            return CLOUD_URI;
        }
    }

    public static class ScanMultiModalColumns implements BaseColumns {
        /**
         * media_id
         */
        public static final String MEDIA_ID = GalleryColumns.LocalColumns.MEDIA_ID;

        /**
         * local_media表中的_id
         * GalleryScanProviderHelper.convertImageToMediaItem()等地方需要用到local_media的_id。否则无法获取bitmap
         */
        public static final String LOCAL_MEDIA_TABLE_ID = "local_media_table_id";

        /**
         * 文件路径
         */
        public static final String DATA = MetaColumns.DATA;

        /**
         * 文件类型
         */
        public static final String MEDIA_TYPE = GalleryColumns.LocalColumns.MEDIA_TYPE;

        /**
         * invalid
         */
        public static final String INVALID = MetaColumns.INVALID;

        /**
         * is_recycled
         */
        public static final String IS_RECYCLED = MetaColumns.IS_RECYCLED;

        /**
         * version
         */
        public static final String VERSION = "version";

        /**
         * 扫描打断 0。数据库不记录此值！！！
         * 扫描成功 1
         * 底层错误 2
         */
        public static final String SCAN_STATE = GalleryColumns.LocalColumns.SCAN_STATE;

        /**
         * date_taken
         */
        public static final String DATE_TAKEN = GalleryColumns.LocalColumns.DATE_TAKEN;

        private ScanMultiModalColumns() {
        }
    }

    public static final class ScanMultiModal extends ScanMultiModalColumns {
        public static final String TAB = "scan_multi_modal";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    /**
     * 编辑信息表的字段集合
     */
    public static class EditInfoColumns implements BaseColumns {
        /**
         * 效果图路径，效果图指编辑后保存的图
         */
        public static final String DATA = "_data";
        /**
         * 原图路径，原图指未经过参数化编辑的图
         */
        public static final String ORIGINAL_DATA = "_original_data";
        /**
         * 内容编辑图片路径，内容编辑图片指最后一次内容编辑后、不包含参数化编辑的原图，
         */
        public static final String CONTENT_EDIT_DATA = "content_edit_data";
        /**
         * 非法状态，0为合法，其他状态为非法，后续可定义
         */
        public static final String INVALID = "invalid";

        /**
         * invalid 字段的默认值，合法
         */
        public static final int INVALID_DEFAULT = 0;
    }

    /**
     * 编辑信息表
     */
    public static final class EditInfo extends EditInfoColumns {
        public static final String TAB = "edit_info";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    /**
     * 水印大师资源信息表
     */
    public static final class WatermarkMasterResource implements BaseColumns {
        /**
         * 表名
         */
        public static final String TAB = "watermark_master_resource";

        /**
         * 资源名称
         */
        public static final String RES_NAME = "res_name";

        /**
         * 资源类型
         */
        public static final String RES_TYPE = "res_type";

        /**
         * 资源url地址
         */
        public static final String RES_URL = "res_url";

        /**
         * 资源文件md5信息
         */
        public static final String MD5 = "md5";

        /**
         * 资源文件路径
         */
        public static final String FILE_PATH = "file_path";

        /**
         * 资源所归属的水印，对应的styleId
         */
        public static final String BELONG_TO_STYLE_ID = "belong_to_style_id";

        /**
         * 引导图资源json
         */
        public static final String POP_UP_RES_JSON = "pop_up_res_json";

        /**
         * 引导图资源json
         */
        public static final String POPUP_ALREADY_SHOW = "popup_already_show";

        /**
         * 水印标题json
         */
        public static final String TITLE_JSON = "title_json";

        public static class Type {
            public static final int NONE = 0;
            public static final int JSON = 1;
            public static final int THUMBNAIL = 2;
            public static final int FONT = 3;
            public static final int IMAGE = 4;
            public static final int BACKGROUND = 5;
            public static final int LARGE_THUMBNAIL = 6;
            public static final int CAMERA_THUMBNAIL = 7;
            public static final int POPUP_IMAGE = 8;
            public static final int PREVIEW_IMAGE = 9;
        }
    }

    /**
     * 水印按键配置信息表
     */
    public static final class ButtonConfigResource implements BaseColumns {
        /**
         * 表名
         */
        public static final String TAB = "button_config_resource";

        /**
         * 按键名称编码
         */
        public static final String BUTTON_TYPE_CODE = "button_type_code";

        /**
         * 按键名称
         */
        public static final String BUTTON_TYPE_NAME = "button_type_name";

        /**
         * 按键背景色
         */
        public static final String BUTTON_PICTURE_COLOR = "button_picture_color";

        /**
         * 按键配图
         */
        public static final String BUTTON_PICTURE_URL = "button_picture_url";

        /**
         * 按键配图路径
         */
        public static final String BUTTON_PICTURE_FILE_PATH = "button_picture_file_path";

        /**
         * 按键2侧图标
         */
        public static final String BUTTON_SIDE_PICTURE_URL = "button_side_picture_url";

        /**
         * 按键2侧图标路径
         */
        public static final String BUTTON_SIDE_PICTURE_FILE_PATH = "button_side_picture_file_path";

        /**
         * 角标图标
         */
        public static final String CORNER_MARK_URL = "corner_mark_url";

        /**
         * 角标图标路径
         */
        public static final String CORNER_MARK_FILE_PATH = "corner_mark_file_path";

        /**
         * 按键字体颜色
         */
        public static final String BUTTON_TEXT_COLOR = "button_text_color";

        /**
         * 按键字体包
         */
        public static final String BUTTON_TEXT_FONT = "button_text_font";

        /**
         * 按键字体包路径
         */
        public static final String BUTTON_TEXT_FONT_PATH = "button_text_font_path";

        /**
         * 按键字体包md5
         */
        public static final String FONT_ZIP_MD5 = "font_zip_md5";

        /**
         * 上线时间
         */
        public static final String ONLINE_TIME = "online_time";

        /**
         * 下线时间
         */
        public static final String OFFLINE_TIME = "offline_time";

        /**
         * 限定水印角标是否已经显示
         * 0：未显示 1：已显示
         */
        public static final String TIPS_ALREADY_SHOW = "tips_already_show";
    }

    /**
     * 个人信息清单收集情况表
     */
    public static final class PersonalInfoCollection implements BaseColumns {
        /**
         * 表名
         */
        public static final String TAB = "personal_info_collection";

        /**
         * 日期：如20241012
         */
        public static final String DATE = "date_ymd";

        /**
         * 日期毫秒数
         */
        public static final String DATE_MILLIS = "date_millis";

        /**
         * 次数：1
         */
        public static final String COUNT = "request_count";

        /**
         * 字段名称:
         */
        public static final String FIELD_NAME = "field_name";

        /**
         * 文件名称-默认中文
         */
        public static final String FILE_NAME_CN = "file_name_cn";

        /**
         * 文件名称-英文
         */
        public static final String FILE_NAME_EN = "file_name_en";

        /**
         * 字段名称集合，以逗号分隔：文件名称保存时需要使用，可为空
         */
        public static final String FIELD_NAME_LIST = "field_name_list";

        /**
         * 字段名称相关值定义
         */
        public static class FileName {
            /**
             * 手机号码
             */
            public static final String TEL_NUMBER = "tel_number";

            /**
             * 人脸轮廓
             */
            public static final String FACIAL_CONTOUR = "facial_contour";

            /**
             * 经纬度信息
             */
            public static final String LOCATION = "location";

            /**
             * 设备识别码 DUID
             */
            public static final String DUID = "duid";

            /**
             * 设备品牌
             */
            public static final String DEVICE_BRAND = "device_brand";

            /**
             * OS 版本
             */
            public static final String OS_VERSION = "os_version";

            /**
             * 机型
             */
            public static final String MODEL = "model";

            /**
             * 设备型号
             */
            public static final String DEVICE_TYPE = "device_type";

            /**
             * ROM 版本
             */
            public static final String ROM_VERSION = "rom_version";

            /**
             * APP 版本
             */
            public static final String APP_VERSION = "app_version";

            /**
             * 运营商信息
             */
            public static final String OPERATOR_INFORMATION = "operator_information";

            /**
             * 语言和地区设置
             */
            public static final String LANGUAGE_AND_REGIONAL_SETTINGS = "language_and_regional_settings";

            /**
             * 图片中的文字
             */
            public static final String PICTURE_TEXT = "picture_text";

            /**
             * 图片文件
             */
            public static final String PICTURE_FILE = "picture_file";

            /**
             * 错误日志报告
             */
            public static final String ERROR_LOG_REPORT = "error_log_report";

            /**
             * 埋点信息
             */
            public static final String BURIED_INFORMATION = "buried_information";

            /**
             * 反馈内容附件（文字、图片）
             */
            public static final String FEEDBACK_CONTEXT_ATTACHMENT = "feedback_context_attachment";

            /**
             * 功能更新与素材资源下载
             */
            public static final String INFORMATION_SHARE_WITH_FUNCTION = "information_share_with_function";
        }
    }

    public static class GalleryColumns {
        /**
         * 公共字段
         */
        private static class GalleryCommonColumns implements BaseColumns {
            /**
             * 文件是删除状态
             */
            public static final int IS_TRASHED_TRUE = 1;
            /**
             * 文件不是删除状态
             */
            public static final int IS_TRASHED_FALSE = 0;
            /**
             * 文件处于待办状态
             */
            public static final int IS_PENDING_TRUE = 1;
            /**
             * 文件处于完成状态
             */
            public static final int IS_PENDING_FALSE = 0;
            /**
             * 文件处于收藏状态
             */
            public static final int IS_FAVORITE_TRUE = 1;

            /********* 媒体库字段，跟媒体库字段是一致的，不要随意改动 **********/
            public static final String MEDIA_ID = "media_id";
            public static final String DATA = "_data";
            public static final String SIZE = "_size";
            public static final String DATE_ADDED = "date_added";// 云端没有
            public static final String DATE_MODIFIED = "date_modified";
            public static final String DATE_TAKEN = "datetaken";
            public static final String MIME_TYPE = "mime_type";
            public static final String TITLE = "title";
            public static final String DESCRIPTION = "description";
            public static final String DISPLAY_NAME = "_display_name";
            public static final String ORIENTATION = "orientation";
            public static final String LATITUDE = "latitude";
            public static final String LONGITUDE = "longitude";
            public static final String BUCKET_ID = "bucket_id";
            public static final String BUCKET_NAME = "bucket_display_name";
            public static final String DURATION = "duration";
            public static final String RESOLUTION = "resolution";
            public static final String MEDIA_TYPE = "media_type";
            public static final String WIDTH = "width";
            public static final String HEIGHT = "height";
            public static final String IS_PENDING = "is_pending";
            public static final String PRIMARY_DIR = "primary_directory";
            public static final String SECONDARY_DIR = "secondary_directory";
            public static final String RELATIVE_PATH = "relative_path";
            public static final String VOLUME_NAME = "volume_name";
            /**
             * 记录最后修改的时间点
             */
            public static final String GENERATION_MODIFIED = "generation_modified";
            /******* 相册自定义的公共字段 *************/
            public static final String CSHOT_ID = "cshot_id";
            /**
             * 删除状态
             */
            public static final String IS_TRASHED = "is_trashed";
            /**
             * 删除时间
             */
            public static final String DATE_RECYCLED = "date_recycled";
            /**
             * 原路径
             * <p>
             * 保存原本的路径，即文件没有进回收站前的路径
             */
            public static final String ORIGINAL_DATA = "_original_data";
            /**
             * 将字段放置在一个set中，用来判断是不是公共字段更新了
             *
             * @see com.oplus.gallery.foundation.database.notifier.UriNotifier.INSTANCE
             */
            public static final HashSet<String> GALLERY_COMMON_COLUMNS_SET = SetsKt.hashSetOf(
                    MEDIA_ID,
                    DATA,
                    SIZE,
                    DATE_MODIFIED,
                    DATE_TAKEN,
                    MIME_TYPE,
                    TITLE,
                    DESCRIPTION,
                    DISPLAY_NAME,
                    ORIENTATION,
                    LATITUDE,
                    LONGITUDE,
                    BUCKET_ID,
                    BUCKET_NAME,
                    DURATION,
                    RESOLUTION,
                    MEDIA_TYPE,
                    WIDTH,
                    HEIGHT,
                    IS_PENDING,
                    PRIMARY_DIR,
                    SECONDARY_DIR,
                    RELATIVE_PATH,
                    VOLUME_NAME,
                    CSHOT_ID,
                    IS_TRASHED,
                    DATE_RECYCLED,
                    ORIGINAL_DATA
            );
        }

        /**
         * 本地字段
         */
        public static class LocalColumns extends GalleryCommonColumns {
            /**
             * 回收站路径已经更新，需要再次更新
             * <p>
             * 用于删除中断时判断是否需要从媒体库获取回收站路径
             */
            public static final int IS_RECYCLE_DATA_UPDATED_TRUE = 1;
            /**
             * 回收站路径需要更新
             * <p>
             * 用于删除中断时判断是否需要从媒体库获取回收站路径
             */
            public static final int IS_RECYCLE_DATA_UPDATED_FALSE = 0;

            /*** sync_status 同步状态，标记是否需要从文件解析属性*****/
            /**
             * 标记已同步
             */
            public static final int SYNC_STATUS_DEFAULT = 0;
            /**
             * 标记未进行同步
             */
            public static final int SYNC_STATUS_FORCE_SCAN = 1;
            // scan_error 记录读写文件信息时出现的错误，例如Exif或视频头信息中的角度、经纬度等
            /**
             * 文件（图片或者视频）能够正常解析属性，没有出现异常
             * 后续再增加头信息相关的scanError字段，要注意同步
             * {@link com.oplus.gallery.foundation.database.util.DatabaseUtils#isSupportExifNormal(int)}
             */
            public static final int SCAN_ERROR_NONE = 0;
            /**
             * 文件不支持Exif
             */
            public static final int SCAN_ERROR_ATTR_NOT_SUPPORT = 1;
            /**
             * 文件Exif中的orientation读取失败
             */
            public static final int SCAN_ERROR_ATTR_ORI_READ_FAILED = 1 << 1;
            /**
             * 文件Exif中的orientation写入失败
             */
            public static final int SCAN_ERROR_ATTR_ORI_WRITE_FAILED = 1 << 2;
            /**
             * 用户手动修改了图片旋转方向
             * 场景：大图页旋转
             * 过程：旋转-更新exif-触发媒体库扫描-更新相册数据库
             * 异常：eixf写成功了，但是媒体库没有解析，导致媒体库中的数据还是之前的角度，如果不加此code，会将相册覆盖
             */
            public static final int SCAN_ERROR_ATTR_ORI_USER_SET = 1 << 3;

            // invalid status
            /**
             * 可显示的正常记录。媒体库记录和本地文件都存在。
             */
            public static final int INVALID_NORMAL = 0;
            /**
             * [内置/外置]存储是mounted情况下，记录被删除后，短暂保留一段时间。<p>
             * - 媒体库可能被其他app先删除后插入，此时的删除可能并非是真正意图<p>
             * - 媒体库清掉后重扫，也会出现先删除后插入的场景<p>
             * 清理策略：<p>
             * - 一分钟后本地文件不存在，则清理；<p>
             * - 一分钟后本地文件存在，则设置为 INVALID_FILE_EXIST，等待文件被删除或者被再次扫描进入媒体库，下一次同步就会处理掉此标记
             */
            public static final int INVALID_SURVIVAL_SHORT = 1;
            /**
             * [内置/外置]存储unmounted后，保留数据库记录。<p>
             * - 保留业务数据，避免[内置/外置]存储unmounted导致收藏等业务数据丢失<p>
             * - 外置存储mounted后，同步媒体库数据时效率高<p>
             * 清理策略：不清理，如果有必要可考虑7天后删除。
             */
            public static final int INVALID_UNMOUNT = 2;
            /**
             * 媒体库记录被清理，但是文件还在，需要保留清理此记录的业务数据。<p>
             * 例如：媒体库记录因“.nomedia”文件导致扫描清理、媒体库异常重扫导致媒体库数据丢失等。
             */
            public static final int INVALID_FILE_EXIST = 3;
            /**
             * 可显示的占位记录。相册记录存在，但是媒体库记录和文件可能都不存在。<p>
             * 例如：相机快拍、云同步等需要提前显示缩图/占位图
             */
            public static final int INVALID_NORMAL_NOT_IN_MEDIA_STORE = 4;
            /**
             * 标记延迟回收。<p>
             * 例如：相机拍照后立刻进入相册把quick图删掉，此时相册只是进行了相册数据库中记录的标记，
             * 等待相机原图生成完成后通过标记识别出需要进行延迟回收的记录，进行实际的回收操作。
             */
            public static final int INVALID_MARK_DELAY_RECYCLE = 5;
            /**
             * 被外部app移入了私密保险箱，隐藏起来，一小段时间后彻底删除
             */
            public static final int INVALID_EXTERNAL_MOVE_TO_SAFE_BOX = 6;
            public static final int STATE_IN_FAVORITED = 1;
            public static final int STATE_IN_UNFAVORITED = 0;

            public static final int INVALID_MEDIA_ID = -1;
            public static final long INVALID_CSHOT_ID = -1L;

            /********* media_type ***********/
            public static final int MEDIA_TYPE_UNKNOW = MediaStore.Files.FileColumns.MEDIA_TYPE_NONE;
            public static final int MEDIA_TYPE_IMAGE = MediaStore.Files.FileColumns.MEDIA_TYPE_IMAGE;
            public static final int MEDIA_TYPE_VIDEO = MediaStore.Files.FileColumns.MEDIA_TYPE_VIDEO;
            public static final int BIT_FORMAT_DEFAULT = -1;
            public static final int BIT_FORMAT_BITMAP = 0;
            public static final int BIT_FORMAT_YUV = 1;
            public static final int CARD_CASE_TYPE_REMOVE = -1;
            public static final int CARD_CASE_TYPE_DEFAULT = 0;

            /**
             * olive中视频的颜色标准，值为 int 型
             */
            public static final String EXTRA_KEY_OLIVE_VIDEO_COLOR_STANDARD = "1";
            /**
             * olive中视频的颜色传输，值为 int 型
             */
            public static final String EXTRA_KEY_OLIVE_VIDEO_COLOR_TRANSFER = "2";
            /**
             * olive中视频的PROFILE，值为 int 型
             */
            public static final String EXTRA_KEY_OLIVE_VIDEO_PROFILE = "3";
            /**
             * olive中视频的宽，值为 int 型
             */
            public static final String EXTRA_KEY_OLIVE_VIDEO_WIDTH = "4";
            /**
             * olive中视频的高，值为 int 型
             */
            public static final String EXTRA_KEY_OLIVE_VIDEO_HEIGHT = "5";
            /**
             * olive中视频的帧率，值为 float 型
             */
            public static final String EXTRA_KEY_OLIVE_VIDEO_FPS = "6";
            /**
             * olive中视频的编码类型，值为 String 型
             */
            public static final String EXTRA_KEY_OLIVE_VIDEO_CODEC_TYPE = "7";
            /**
             * olive中视频的码率，值为 long 型
             */
            public static final String EXTRA_KEY_OLIVE_VIDEO_BIT_RATE = "8";
            /**
             * olive中视频的偏移起点，值为 long 型
             */
            public static final String EXTRA_KEY_OLIVE_VIDEO_START_OFFSET = "9";
            /**
             * olive中视频的长度，值为 long 型
             */
            public static final String EXTRA_KEY_OLIVE_VIDEO_LENGTH = "10";

            /**
             * 视频的平均帧率，值为 float 型
             */
            public static final String EXTRA_KEY_VIDEO_FRAME_RATE = "11";

            /**
             * 图片内容的宽（不包含水印），值为 int 型
             * - Olive 时为封面图
             * - 通过 IWatermarkMasterAbility 解析获取
             */
            public static final String EXTRA_KEY_PICTURE_WIDTH_WITHOUT_WATERMARK = "12";

            /**
             * 图片内容的高（不包含水印），值为 int 型
             * - Olive 时为封面图
             * - 通过 IWatermarkMasterAbility 解析获取
             */
            public static final String EXTRA_KEY_PICTURE_HEIGHT_WITHOUT_WATERMARK = "13";

            /**
             * Olive 视频的宽（不包含水印），值为 int 型
             * - 通过 IWatermarkMasterAbility 解析获取
             */
            public static final String EXTRA_KEY_OLIVE_VIDEO_WIDTH_WITHOUT_WATERMARK = "14";

            /**
             * Olive 视频的高（不包含水印），值为 int 型
             * - 通过 IWatermarkMasterAbility 解析获取
             */
            public static final String EXTRA_KEY_OLIVE_VIDEO_HEIGHT_WITHOUT_WATERMARK = "15";

            /**************** user defined columns ********/
            public static final String INVALID = "invalid";
            public static final String TAGFLAGS = "tagflags";

            public static final String SYNC_STATUS = "sync_status";
            public static final String IS_FAVORITE = "is_favorite";
            public static final String GPS_KEY = "gps_key";
            public static final String MEDIA_SCORE = "media_score";
            public static final String YEAR = "year";
            public static final String MONTH = "month";
            public static final String DAY = "day";
            public static final String BIT_FORMAT = "bit_format";
            public static final String SCAN_ERROR = "scan_error";
            public static final String CARD_CASE_TYPE = "card_case_type";
            public static final String QUALITY_SCORE = "quality_score";
            public static final String QUALITY_VERSION = "quality_version";
            public static final String CROP_RECT = "crop_rect";                 //裁剪区域数据
            public static final String CROP_RECT_VERSION = "crop_rect_version"; //裁剪的版本号，更新算法时加1可触发重扫
            /**
             * 媒体的编码类型,比如:video/dolby-vision、video/avc
             */
            public static final String CODEC_TYPE = "codec_type";

            /**
             * 媒体的额外数据
             */
            public static final String EXTRA_MSG = "extra_msg";

            /***
             *  操作时间
             *
             *  1、相册里面修改路径
             *  2、从回收站恢复
             *  3、放入回收站已经有相同功能的字段
             */
            public static final String OPERATION_TIME = "operation_time";
            /**
             * 扩展TAGFLAGS，用于表明图片的扩展属性，如相册算法归类结果<p>
             * 具体定义见{@link com.oplus.gallery.business_lib.model.data.base.utils.Constants.ExtendAttributes}
             */
            public static final String EXT_TAG_FLAGS = "ext_tag_flags";
            /**
             * 图片扫描状态用于表明图片是否进行过某一项扫描，如已经进行了ocr扫描<p>
             * 具体定义见{@link ScanState}
             */
            public static final String SCAN_STATE = "scan_state";

            /**
             * 采用 is_trashed方案后，recycle_data 会变成
             * /storage/emulated/0/DCIM/.trashed-1580465614-xxx.jpg
             * .trashed-158046561 这个是由媒体库通过 时间戳来控制的，所以当我们update的时候无法获取准确的file路径，所以需要通过媒体库去查询，
             * 当恢复删除中断的时候，有可能recycle_data还没来得及更新，进程就已经中断了，所以通过该字段去判断，等于1说明，已经update过了
             */
            public static final String IS_RECYCLE_DATA_UPDATED = "is_recycle_data_updated";
            /**
             * 将字段放置在一个set中，用来判断是不是本地字段更新了
             *
             * @see com.oplus.gallery.foundation.database.notifier.UriNotifier.INSTANCE
             */
            public static final HashSet<String> COLUMNS_SET = SetsKt.hashSetOf(
                    INVALID,
                    TAGFLAGS,
                    SYNC_STATUS,
                    IS_FAVORITE,
                    GPS_KEY,
                    MEDIA_SCORE,
                    YEAR,
                    MONTH,
                    DAY,
                    BIT_FORMAT,
                    SCAN_ERROR,
                    CARD_CASE_TYPE,
                    QUALITY_SCORE,
                    QUALITY_VERSION,
                    CROP_RECT,
                    CROP_RECT_VERSION,
                    CODEC_TYPE,
                    OPERATION_TIME,
                    EXT_TAG_FLAGS,
                    SCAN_STATE,
                    IS_RECYCLE_DATA_UPDATED,
                    EXTRA_MSG
            );
        }

        /**
         * 云端字段，云服务使用此索引
         */
        public static class CloudColumns extends GalleryCommonColumns {
            /****************************常量定义*****************************/

            /**
             * 在正常路径
             */
            public static final int CLOUD_DATA_STATUS_NORMAL = 0;
            /**
             * 已彻底删除
             */
            public static final int CLOUD_DATA_STATUS_DELETE = 1;
            /**
             * 移入回收站
             */
            public static final int CLOUD_DATA_STATUS_RECYCLE = 2;

            /****************************字段定义*****************************/

            /**
             * 云端原数据唯一标识
             */
            public static final String GLOBAL_ID = "_global_id";
            /**
             * 云端文件唯一标识
             */
            public static final String FILE_ID = "_file_id";
            /**
             * 云端原图大小
             */
            public static final String CLOUD_SIZE = "cloud_size";
            /**
             * 云端原图宽
             */
            public static final String CLOUD_WIDTH = "cloud_width";
            /**
             * 云端原图高
             */
            public static final String CLOUD_HEIGHT = "cloud_height";
            /**
             * 文件的MD5已经改变
             * <p>
             * 文件的MD5已经改变，如旋转后，MD5校验不可信
             */
            public static final String FILE_MD5_CHANGED = "_file_md5_changed";
            /**
             * 元数据json
             * 将一些必要字段打包成json上传到云端
             *
             * @see com.oplus.gallery.framework.abilities.ocloudsync.db.ImageFile#makeUploadData()
             */
            public static final String UPLOAD_DATA = "_upload_data";
            /**
             * 文件下载状态
             *
             * @see com.oplus.gallery.foundation.database.store.CloudSyncStore.FileDownloadStatus
             */
            public static final String FILE_DOWNLOAD_STATUS = "_file_download_status";
            /**
             * 文件上传状态
             *
             * @see com.oplus.gallery.foundation.database.store.CloudSyncStore.FileUploadStatus
             */
            public static final String FILE_UPLOAD_STATUS = "_file_upload_status";
            /**
             * 上传下载失败次数
             * <p>
             * 开始上传或下载时+1，上传或下载成功-1
             */
            public static final String FILE_SYNC_COUNT = "_file_sync_count";
            /**
             * 开始上传下载时间，和[FILE_SYNC_COUNT]配合用来计算改项目可被触发上传的时间
             */
            public static final String FILE_SYNC_TIME = "_file_sync_time";
            /**
             * 元数据操作
             *
             * @see com.oplus.gallery.foundation.database.store.CloudSyncStore.Operation
             */
            public static final String OPERATION = "_operation";
            /**
             * 开始同步时间
             */
            public static final String START_SYNC_TIME = "_start_sync_time";
            /**
             * 已同步百分比
             */
            public static final String SYNC_PERCENT = "_sync_percent";
            /**
             * 上次使用时间，用于限制本地替换缩图
             */
            public static final String LATEST_FILE_USAGE_TIME = "_latest_file_usage_time";

            /**
             * 本地文件的状态
             *
             * @see com.oplus.gallery.foundation.database.store.CloudSyncStore.LocalFileStatus
             */
            public static final String LOCAL_FILE_STATUS = "local_file_status";

            /**
             * 是否支持瘦身(压缩)
             * <p>
             * 某些特殊格式的图片带有扩展信息，需要保持原图，目前10bit，人像景深图片，超级文本图片会保留原图，
             * 不支持瘦身，也不会下载缩图到本地
             *
             * @see CloudSyncStore.IsSupportCompressed
             */
            public static final String IS_SUPPORT_COMPRESSED = "_is_support_compressed";
            /**
             * 自动下载原图
             */
            public static final String AUTO_DOWNLOAD_ORIGIN = "_auto_download_origin";
            /**
             * 元数据版本号 来自云端
             */
            public static final String SYS_VERSION = "_sys_version";
            /**
             * 新增文件校验
             * 文件上传到有云端后，云端会使用该字段校验上传文件与元数据是否对应
             */
            public static final String FILE_CHECK_PAYLOAD = "_file_check_payload";
            /**
             * 文件MD5
             */
            public static final String MD5 = "_md5";
            /**
             * 云端数据状态
             * （0-正常；1-已删除；2-回收站）
             *
             * @see com.oplus.cloudkitlib.base.CloudKitConstants
             */
            public static final String CLOUD_DATA_STATUS = "_cloud_data_status";
            /**
             * 将字段放置在一个set中，用来判断是不是本云端字段更新了
             *
             * @see com.oplus.gallery.foundation.database.notifier.UriNotifier.INSTANCE
             */
            public static final HashSet<String> COLUMNS_SET = SetsKt.hashSetOf(
                    GLOBAL_ID,
                    FILE_ID,
                    CLOUD_SIZE,
                    CLOUD_WIDTH,
                    CLOUD_HEIGHT,
                    FILE_MD5_CHANGED,
                    UPLOAD_DATA,
                    FILE_DOWNLOAD_STATUS,
                    FILE_UPLOAD_STATUS,
                    OPERATION,
                    START_SYNC_TIME,
                    SYNC_PERCENT,
                    LATEST_FILE_USAGE_TIME,
                    LOCAL_FILE_STATUS,
                    IS_SUPPORT_COMPRESSED,
                    AUTO_DOWNLOAD_ORIGIN,
                    SYS_VERSION,
                    FILE_CHECK_PAYLOAD
            );
        }
    }

    public static class MetaColumns {
        public static final String DATA = GalleryColumns.LocalColumns.DATA;
        public static final String INVALID = GalleryColumns.LocalColumns.INVALID;
        public static final String IS_RECYCLED = "is_recycled";
        public static final int IS_RECYCLED_TRUE = 1;
        public static final int IS_RECYCLED_FALSE = 0;

        private MetaColumns() {
        }
    }

    public static class MemoriesSetColumns implements BaseColumns {
        public static final String NAME = "name";
        public static final String TYPE = "type";
        public static final String TAKEN = "taken";
        public static final String IS_DELETE = "is_deleted";
        public static final String GROUP_ID = "group_id";
        public static final String COUNTRY = "country";
        public static final String CITY = "city";
        public static final String IS_FOREIGN = "is_foreign";
        public static final String START_TIME = "start_time";
        public static final String END_TIME = "end_time";
        public static final String COVER_PATH = "cover_path";
        public static final String THEME = "theme";
        public static final String MUSIC = "music";
        public static final String LATITUDE = "latitude";
        public static final String LONGITUDE = "longitude";
        public static final String NAME_TYPE = "name_type";
        public static final String IS_NOTICE = "is_notice";
        public static final String META_NUM = "num_of_meta";
        public static final int NOT_DELETED = 0;
        public static final int HAS_DELETED = 1;
        public static final int NOTICE_DISABLE = 0;
        public static final int NOTICE_ENABLE = 1;


        private MemoriesSetColumns() {
        }
    }

    public static class MemoriesSetmapColumns implements BaseColumns {
        public static final String DATA = MetaColumns.DATA;
        public static final String INVALID = MetaColumns.INVALID;
        public static final String IS_RECYCLED = MetaColumns.IS_RECYCLED;

        public static final String SET_ID = "set_id";
        public static final String IS_COVER = "is_cover";
        public static final String IN_VIDEO = "in_video";
        public static final String TAG_ID = "tag_id";

        private MemoriesSetmapColumns() {
        }
    }

    public static class MemoriesSetmapViewColumns {
        public static final String MEDIA_ID = GalleryColumns.LocalColumns.MEDIA_ID;
        public static final String DATA = GalleryColumns.LocalColumns.DATA;
        public static final String DATE_TAKEN = GalleryColumns.LocalColumns.DATE_TAKEN;
        public static final String MEDIA_TYPE = GalleryColumns.LocalColumns.MEDIA_TYPE;
        public static final String MEDIA_SCORE = GalleryColumns.LocalColumns.MEDIA_SCORE;
        public static final String IS_SINGLE_FACE = ScanFaceColumns.IS_SINGLE_FACE;

        public static final String SET_ID = MemoriesSetmapColumns.SET_ID;
        public static final String IS_COVER = MemoriesSetmapColumns.IS_COVER;
        public static final String IN_VIDEO = MemoriesSetmapColumns.IN_VIDEO;
        public static final String TAG_ID = MemoriesSetmapColumns.TAG_ID;

        private MemoriesSetmapViewColumns() {
        }
    }

    public static class SimilarFeatureColumns implements BaseColumns {
        public static final String DATA = GalleryColumns.LocalColumns.DATA;
        public static final String FEATURE = "feature";
        public static final String FEATURE_VERSION = "feature_version";
    }

    public static class SeniorMediaColumns implements BaseColumns {
        public static final String DATA = GalleryColumns.LocalColumns.DATA;
        /**
         * 优选分数(为有修改的值，原值在local_media的quality_score, 版本号对应local_media的quality_version)
         */
        public static final String SENIOR_SCORE = "senior_score";
        public static final String SIMILAR_GROUP_ID = "similar_group_id";
        /**
         * 加分策略标志位（收藏/编辑/分享，扫描特定标签，会设置此字段
         * 可能写入PickedDayConstant.SENIOR_SELECT_VERSION或者PickedDayConstant.SENIOR_SELECT_TEMP_VERSION
         */
        public static final String VERSION = "version";
        /**
         * 相似图美学评价分数
         */
        public static final String SSA_QUALITY_SCORE = "ssa_quality_score";
        /**
         * 内容优选接口使用标签大类ID
         * 用来计算senior_top_label_id的历史平均质量分给sdk使用
         */
        public static final String SENIOR_TOP_LABEL_ID = "senior_top_label_id";
        /**
         * 相似图美学评价版本号，标识分数是否变化
         */
        public static final String SSA_QUALITY_SCORE_VERSION = "ssa_quality_score_version";
        /**
         * 是否优选图
         * 1：是 0：否（加权计算可能变为优选）, -1： 否（加权计算也非优选）。见[SeniorMediaCondition.buildSenior]的使用
         */
        public static final String SENIOR_RESULT = "senior_result";
        /**
         * 用来判断是否重新获取senior_result
         * 字段举例： {"iqaVersion":3,"ssaVersion":1,"similarFeatureVersion":1,"labelVersion":8,"imageFilterVersion":1}
         */
        public static final String SENIOR_RESULT_VERSION = "senior_result_version";
    }

    public static class ScanFaceColumns implements BaseColumns {
        public static final long GROUP_ID_INVALID = -1L;

        public static final int GROUP_ID_0 = 0; //had not group yet
        public static final int GROUP_ID_1 = 1; //only one face will group to this, we will regroup them
        public static final int GROUP_ID_2 = 2; //can not sure the face is exactness
        public static final int GROUP_ID_3 = 3; //normal group base

        public static final String DATA = MetaColumns.DATA;
        public static final String MEDIA_TYPE = GalleryColumns.LocalColumns.MEDIA_TYPE;
        public static final String INVALID = MetaColumns.INVALID;
        public static final String IS_RECYCLED = MetaColumns.IS_RECYCLED;

        public static final String NO_FACE = "no_face";
        public static final String THUMB_W = "thumb_width";
        public static final String THUMB_H = "thumb_height";
        public static final String GROUP_ID = "group_id";
        public static final String GROUP_NAME = "group_name";
        public static final String GROUP_VISIBLE = "group_visible"; // 0: not modify; 1: show; 2: hide
        public static final String HAS_BIG_FACE = "has_big_face";
        public static final String FEATURE = "feature";
        public static final String SCORE = "score";
        public static final String YAW = "yaw";
        public static final String PITCH = "pitch";
        public static final String ROLL = "roll";
        public static final String EYE_DIST = "eye_dist";
        public static final String AGE = "age";
        public static final String SEX = "sex";
        public static final String RACE = "race";
        public static final String SKIN = "skin";
        public static final String LUMINANCE = "luminance";
        public static final String LEFT = "rect_left";
        public static final String TOP = "rect_top";
        public static final String RIGHT = "rect_right";
        public static final String BOTTOM = "rect_bottom";
        public static final String BEST_SCORE = "best_score";
        public static final String IS_CHOSEN = "is_chosen";
        public static final String IS_COVER = "is_default_cover";
        public static final String IS_SMALL_FACE = "is_small_face";
        public static final String IS_SINGLE_FACE = "is_single_face";
        public static final String SCAN_DATE = "face_scan_date";
        public static final String GROUP_DATE = "group_date";
        public static final String IS_MANUAL = "is_manual";
        public static final String MANUAL_DATE = "manual_date";
        public static final String MODEL_VERSION = "model_version";
        public static final String IS_GROUP_ON_CLOUD = "is_group_on_cloud";
        public static final String FACE_VISIBLE = "face_visible";
        public static final String FACE_REMOVABLE = "face_removable";
        public static final String HANDLE_STATE = "handle_state";

        /**
         * 该角色与我的关系类型。
         * 0：自定义关系。
         * 1~n：关系（自己、爸爸、妈妈、伴侣。。。）
         */
        public static final String RELATION_TYPE = "relation_type";

        /**
         * 该角色与我的自定义关系名称
         */
        public static final String CUSTOM_RELATION = "custom_relation";

        /**
         * 不显示该角色，默认值 false 为显示，true 为隐藏
         */
        public static final String IS_HIDE = "is_hide";
        /**
         * 251点关键点坐标
         * 用于进行表情优化算法，最佳表情需求使用。
         */
        public static final String FACE_LANDMARK = "face_landmark";
        /**
         * 251点关键点可见性
         * 用于进行表情优化算法，最佳表情需求使用。
         */
        public static final String FACE_LANDMARK_VISIBLE = "face_landmark_visible";
        /**
         * 人脸质量分数
         * 用于表情数据验证，最佳表情需求使用。
         */
        public static final String FACE_QUALITY = "face_quality";
        /**
         * 睁眼大小
         * 用于表情数据验证，最佳表情需求使用。
         */
        public static final String EYE_OPEN_WIDTH = "eye_open_width";
        /**
         * 人脸像素平均值
         * 用于表情数据验证，最佳表情需求使用。
         */
        public static final String FACE_PIXEL_MEAN = "face_pixel_mean";
        /**
         * 人脸表情分数
         * 用于表情数据验证，最佳表情需求使用。
         */
        public static final String FACE_EXPRESSION_SCORE = "face_expression_score";
        /**
         * 人脸表情详细细分
         */
        public static final String EXPRESSION_DETAIL = "expression_detail";
        /**
         * 快乐分数
         */
        public static final String HAPPY_SCORE = "happy_score";
        /**
         * 表情分类
         */
        public static final String EMOTION = "emotion";

        /**
         * 人脸作为封面的综合得分
         * 用于单角色封面优选
         */
        public static final String FACE_COVER_SCORE = "face_cover_score";

        /**
         * 合照封面表情得分
         * 用于多角色封面计算（合照图集封面）
         */
        public static final String GROUP_COVER_EXPRESSION_SCORE = "face_cover_expression";

        private ScanFaceColumns() {
        }
    }

    public static class ScanFaceViewColumns implements BaseColumns {
        public static final String MEDIA_ID = GalleryColumns.LocalColumns.MEDIA_ID;
        public static final String DATE_MODIFIED = GalleryColumns.LocalColumns.DATE_MODIFIED;
        public static final String DATE_TAKEN = GalleryColumns.LocalColumns.DATE_TAKEN;
        public static final String MEDIA_TYPE = GalleryColumns.LocalColumns.MEDIA_TYPE;
        public static final String CROP_RECT = GalleryColumns.LocalColumns.CROP_RECT;
        public static final String CODEC_TYPE = GalleryColumns.LocalColumns.CODEC_TYPE;

        public static final String THUMB_W = ScanFaceColumns.THUMB_W;
        public static final String THUMB_H = ScanFaceColumns.THUMB_H;
        public static final String GROUP_ID = ScanFaceColumns.GROUP_ID;
        public static final String AGE = ScanFaceColumns.AGE;
        public static final String FEATURE = ScanFaceColumns.FEATURE;
        public static final String SCORE = ScanFaceColumns.SCORE;
        public static final String YAW = ScanFaceColumns.YAW;
        public static final String PITCH = ScanFaceColumns.PITCH;
        public static final String ROLL = ScanFaceColumns.ROLL;
        public static final String EYE_DIST = ScanFaceColumns.EYE_DIST;
        public static final String LEFT = ScanFaceColumns.LEFT;
        public static final String TOP = ScanFaceColumns.TOP;
        public static final String RIGHT = ScanFaceColumns.RIGHT;
        public static final String BOTTOM = ScanFaceColumns.BOTTOM;
        public static final String BEST_SCORE = ScanFaceColumns.BEST_SCORE;
        public static final String IS_CHOSEN = ScanFaceColumns.IS_CHOSEN;
        public static final String IS_COVER = ScanFaceColumns.IS_COVER;
        public static final String IS_SMALL_FACE = ScanFaceColumns.IS_SMALL_FACE;
        public static final String RELATION_TYPE = ScanFaceColumns.RELATION_TYPE;
        public static final String CUSTOM_RELATION = ScanFaceColumns.CUSTOM_RELATION;
        public static final String FACE_COVER_SCORE = ScanFaceColumns.FACE_COVER_SCORE;
        public static final String FACE_COVER_EXPRESSION = ScanFaceColumns.GROUP_COVER_EXPRESSION_SCORE;
        public static final String LOCAL_ID = "local_id";

        private ScanFaceViewColumns() {
        }
    }

    public static class ScanFaceGroupColumns {
        public static final String MEDIA_TYPE = GalleryColumns.LocalColumns.MEDIA_TYPE;

        public static final String GROUP_ID = ScanFaceColumns.GROUP_ID;
        public static final String GROUP_NAME = ScanFaceColumns.GROUP_NAME;
        public static final String GROUP_VISIBLE = ScanFaceColumns.GROUP_VISIBLE;
        public static final String IS_SINGLE_FACE = ScanFaceColumns.IS_SINGLE_FACE;
        public static final String HAS_BIG_FACE = ScanFaceColumns.HAS_BIG_FACE;
        public static final String IS_MANUAL = ScanFaceColumns.IS_MANUAL;
        public static final String MANUAL_DATE = ScanFaceColumns.MANUAL_DATE;
        public static final String RELATION_TYPE = ScanFaceColumns.RELATION_TYPE;
        public static final String CUSTOM_RELATION = ScanFaceColumns.CUSTOM_RELATION;

        public static final String AVG_AGE = "avg_age";
        public static final String NUM_ALL_FACES = "num_all_faces";
        public static final String NUM_IMAGE_FACES = "num_image_faces";
        public static final String NUM_VIDEO_FACES = "num_video_faces";
        public static final String NUM_BIG_FACES = "num_big_faces";
        public static final int GROUP_VISIBLE_INIT = 0; // not modify
        public static final int GROUP_VISIBLE_SHOW = 1; // show
        public static final int GROUP_VISIBLE_HIDE = 2; // hide
        public static final int HAS_NO_BIG_FACE_STATE = 0; // hide
        public static final int HAS_BIG_FACE_STATE = 1; // show
        public static final int HAS_NO_MANUAL_STATE = 0;
        public static final int HAS_MANUAL_STATE = 1;

        private ScanFaceGroupColumns() {
        }
    }

    public static class ScanLabelColumns implements BaseColumns {
        public static final String DATA = MetaColumns.DATA;
        public static final String MEDIA_TYPE = GalleryColumns.LocalColumns.MEDIA_TYPE;
        public static final String INVALID = MetaColumns.INVALID;
        public static final String IS_RECYCLED = MetaColumns.IS_RECYCLED;

        public static final String SCENE_ID = "scene_id";
        public static final String SCORE = "score";
        public static final String IS_SYNC = "is_sync";
        public static final String IS_MANUAL = "is_manual";

        private ScanLabelColumns() {
        }
    }

    public static class ScanAbortFileColumns implements BaseColumns {
        public static final String FILE_NAME = "file_name";
        public static final String COUNT = "abort_count";

        private ScanAbortFileColumns() {
        }
    }

    public static class SceneCrashColumns implements BaseColumns {
        public static final String SCENE = "scene";
        public static final String CARRIED_MSG = "carried_msg";
        public static final String CRASH_TIME = "crash_time";
        public static final String BACKTRACE = "backtrace";
        public static final String STRERROR = "strerror";
        public static final String SI_SIGNO = "si_signo";
        public static final String SI_ERRNO = "si_errno";
        public static final String SI_CODE = "si_code";
        public static final String SI_TID = "si_tid";
        public static final String SI_OVERRUN = "si_overrun";
        public static final String SI_PID = "si_pid";
        public static final String SI_UID = "si_uid";
        public static final String SI_STATUS = "si_status";
        public static final String SI_BAND = "si_band";
        public static final String SI_FD = "si_fd";

        private SceneCrashColumns() {
        }
    }

    public static class GeoColumns implements BaseColumns {
        public static final String COUNTRY = "country";
        public static final String PROVINCE = "province";
        public static final String CITY = "city";
        public static final String DISTRICT = "district";
        public static final String STREET = "street";
        public static final String STREET_NO = "street_no";
        public static final String ADDRESS = "address";
        public static final String GPS_KEY = "gps_key";

        private GeoColumns() {
        }
    }

    @Deprecated
    public static class AppendToAlbumColumns implements BaseColumns {
        public static final String DATA = GalleryColumns.LocalColumns.DATA;
        public static final String TARGET = "target";
        public static final String STATE = "state";

        private AppendToAlbumColumns() {
        }
    }

    public static class OcrPagesColumns implements BaseColumns {
        public static final String DATA = MetaColumns.DATA;
        public static final String INVALID = MetaColumns.INVALID;
        public static final String IS_RECYCLED = MetaColumns.IS_RECYCLED;

        public static final String CONTENT = "content";
        public static final String IS_SYNC = ScanLabelColumns.IS_SYNC;
        public static final String MODEL_VERSION = "model_version";

        private OcrPagesColumns() {
        }
    }

    public static class OtherAlbumSetColumns implements BaseColumns {
        public static final String BUCKET_ID = GalleryColumns.LocalColumns.BUCKET_ID;
        public static final String ATTRIBUTE = "attribute";

        private OtherAlbumSetColumns() {
        }
    }

    public static class LockedPicturesColumns implements BaseColumns {
        public static final String MEDIA_ID = GalleryColumns.LocalColumns.MEDIA_ID;

        private LockedPicturesColumns() {
        }
    }

    public static class SearchHistoryColumns implements BaseColumns {
        public static final String DISPLAY1 = "display1";
        public static final String QUERY = "query";
        public static final String DATE = "date";

        private SearchHistoryColumns() {
        }
    }

    public static class DownloadUrlColumns implements BaseColumns {
        public static final String SIZE = "_size";
        public static final String CONTENT_URL = "content_url";
        public static final String DATA = "_data";
        public static final String ETAG = "etag";
        public static final String HASH_CODE = "hash_code";
        public static final String LAST_ACCESS = "last_access";
        public static final String LAST_UPDATED = "last_updated";

        private DownloadUrlColumns() {
        }
    }

    public static class FestivalColumns implements BaseColumns {
        public static final String IS_FORCE = "is_force";
        public static final String DATE = "date";
        public static final String FESTIVAL_NAME = "festival_name";
        public static final String LEVEL = "level";
        public static final String FESTIVAL_NICK_NAME = "festival_nick_name";
        public static final String TYPE = "type";
        public static final String COUNTRY_NAME = "country_name";
        public static final String COUNTRY_NICK_NAME = "country_nick_name";
        public static final String REGION = "region";
        /**
         * 用于日期值的过滤，int类型，默认为0，在sql判断时用位运算处理，所以在迭代定义时用二进制定义
         * 现有定义：
         *
         * @FestivalInfo.FILTER_TYPE_DEFAULT 默认，0
         * @FestivalInfo.FILTER_TYPE_COMMON 通用：0b00000001
         * @FestivalInfo.FILTER_TYPE_PICKED_DAY 精选日视图，0b00000010
         * 由于date本身可能符合多种类型，比如“common|pickedDay”,所以取值也是位或的结果，即0b00000011，所以数据库存储的值重点要看对应类型位上是否为1
         */
        public static final String FILTER_TYPE = "filterType";

        private FestivalColumns() {
        }
    }

    public static class SearchInfoColumns implements BaseColumns {
        public static final String MEDIA_ID = "media_id";
        public static final String DATA = "_data";
        public static final String DATE_TAKEN = "datetaken";
        public static final String DATE_MODIFIED = "date_modified";
        public static final String FACE_NAME = "face_name";
        public static final String ADDRESS = "address";
        public static final String SCAPE_POI = "scape_poi";
        public static final String TAGS = "tags";
        /**
         * 英文标签字符串数组，例如["dogs", "cats"]
         * 外销全局搜索获取searchInfo时用到，无视系统语言，都返回英文标签字符串数组
         */
        public static final String ENGLISH_TAGS = "english_tags";
        public static final String OCR = "ocr";
        public static final String BUCKET_NAME = "bucket_name";
        public static final String BUCKET_ID = "bucket_id";
        public static final String DISPLAY_NAME = "_display_name";
        public static final String TITLE = "title";
        public static final String SIZE = "_size";
        public static final String MEDIA_TYPE = "media_type";
        public static final String MIME_TYPE = "mime_type";
        public static final String DURATION = "duration";

        public static final String SIM_HASH = "sim_hash";
        public static final String QUALITY = "quality";

        public static final String LONGITUDE = "longitude";
        public static final String LATITUDE = "latitude";
        public static final String FESTIVAL_NAME = "festival_name";
        public static final String MEMORY_NAME = "memory_name";
        public static final String TAG_FLAGS = "tagflags";

        /**
         * 性别
         */
        public static final String SEX = "sex";

        /**
         * 年龄
         */
        public static final String AGE = "age";

        private SearchInfoColumns() {
        }
    }

    @Deprecated // 使用GalleryMedia
    public static final class LocalMedia {
        public static final String TAB = "local_media";

    }

    public static final class MemoriesSet extends MemoriesSetColumns {
        public static final String TAB = "memories_set";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    public static final class MemoriesSetmap extends MemoriesSetmapColumns {
        public static final String TAB = "memories_setmap";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    public static final class MemoriesSetmapView extends MemoriesSetmapViewColumns {
        public static final String TAB = "memories_setmap_view";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    public static final class SimilarFeature extends SimilarFeatureColumns {
        public static final String TAB = "similar_feature";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    public static final class SeniorMedia extends SeniorMediaColumns {
        public static final String TAB = "senior_media";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    public static final class ScanFace extends ScanFaceColumns implements BaseColumns {
        public static final String TAB = "scan_face";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    /**
     * 人脸扫描时的备份数据表，字段和人脸表保持一致
     */
    public static final class ScanFaceBackup extends ScanFaceColumns {
        public static final String TAB = "scan_face_backup";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    public static final class ScanFaceView extends ScanFaceViewColumns {
        public static final String TAB = "scan_face_view";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    public static final class ScanFaceGroupView extends ScanFaceGroupColumns {
        public static final String TAB = "scan_face_group_view";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    public static final class ScanLabel extends ScanLabelColumns {
        public static final String TAB = "scan_label";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    public static final class ScanLabelBackup extends ScanLabelColumns {
        public static final String TAB = "scan_label_backup";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    public static final class ScanAbortFile extends ScanAbortFileColumns {
        public static final String TAB = "scan_abort_file";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    public static final class SceneCrash extends SceneCrashColumns {
        public static final String TAB = "scene_crash";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    public static final class GeoRoute extends GeoColumns {
        public static final String TAB = "geo_route";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    public static final class AppendToAlbum extends AppendToAlbumColumns {
        public static final String TAB = "append_to_album";
        public static final int STATE_NONE = 0;
        public static final int STATE_DONE = 1;

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    public static final class OcrPages extends OcrPagesColumns {
        public static final String TAB = "ocr_pages_v2";


        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    /**
     * 相册数据同步dmp使用，在OcrPagesContentMonitor为为ocr_pages_v2_content创建触发器
     */
    public static final class OcrPagesContent extends OcrPagesColumns {
        public static final String TAB = "ocr_pages_v2_content";
        public static final String C0_DATA = "c0_data";
        public static final String C1_INVALID = "c1invalid";
        public static final String C3_CONTENT = "c3content";

        public static Uri getContentUri() {
            return null;
        }
    }

    public static final class OtherAlbumSet extends OtherAlbumSetColumns {
        public static final String TAB = "other_album_set";


        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    public static final class LockedPictures extends LockedPicturesColumns {
        public static final String TAB = "locked_pictures";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    public static final class SearchHistory extends SearchHistoryColumns {
        public static final String TAB = "search_history";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    public static final class DownloadUrl extends DownloadUrlColumns {
        public static final String TAB = "download_url";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    public static final class Festival extends FestivalColumns {
        public static final String TAB = "festival";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    public static final class RawQuery {
        public static final String TAB = "raw_query";

        private RawQuery() {
        }

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    public static class WidgetSetColumns implements BaseColumns {
        public static final String WIDGET_CODE = "widget_code";
        public static final String MODE = "mode";
        public static final String LAST_DISPLAY_DATA = "last_display_data";
        public static final String DIRTY = "dirty";
        /**
         * 展示次数json，格式如下：
         * ```
         * {
         * "20211217":3,
         * "20211218":1
         * }
         * ```
         * 按照日期先后排序，最多两天（确保能覆盖到昨天）
         */
        public static final String DISPLAY_COUNT_INFO = "display_count_info";
        public static final int MODE_DEFAULT = 0;
        public static final int MODE_RECOMMENDED = 1;
        public static final int MODE_CUSTOM = 2;

        private WidgetSetColumns() {
        }
    }

    public static final class WidgetSet extends WidgetSetColumns {
        public static final String TAB = "widget_set";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    public static class WidgetDisplayListColumns implements BaseColumns {
        public static final String DATA = "_data";
        public static final String DISPLAY_LIST_ID = "display_list_id";
        public static final String INVALID = "invalid";
        public static final String DISPLAY_LIST_ID_RECOMMENDED = "recommended";
        public static final int INVALID_DEFAULT = 0;
        public static final int INVALID_DELETE = 1;
        public static final int INVALID_EXCLUDED = 2;

        private WidgetDisplayListColumns() {
        }
    }

    public static final class WidgetDisplayList extends WidgetDisplayListColumns {
        public static final String TAB = "widget_display_list";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    public static class FileInfoColumns implements BaseColumns {
        public static final String DATA = MetaColumns.DATA;
        public static final String INVALID = MetaColumns.INVALID;
        public static final String IS_RECYCLED = MetaColumns.IS_RECYCLED;

        public static final String HIGHLIGHT_INFO = "highlight_info";

        private FileInfoColumns() {
        }
    }

    public static final class FileInfo extends FileInfoColumns {
        public static final String TAB = "file_info";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }


    /**
     * 共享图集2.0 - 图集
     */
    public static class SharedAlbumColumns implements BaseColumns {

        /**
         * 图集类型：通用
         */
        public static final int TYPE_NORMAL = 1;

        /**
         * 图集类型：家庭共享
         */
        public static final int TYPE_FAMILY = 2;

        /**
         * 图集id
         */
        public static final String ALBUM_ID = _ID;

        /**
         * 图集名称
         */
        public static final String ALBUM_NAME = "album_name";

        /**
         * 图集创建者用户信息 json格式
         * {"userId":"","userName":"","phone":"","realPhone":"","image":""}
         */
        public static final String ALBUM_CREATOR = "album_creator";

        /**
         * 是否为图集创建者，服务端返回字段
         */
        public static final String IS_CREATOR = "is_album_creator";

        /**
         * 图集类型
         */
        public static final String ALBUM_TYPE = "album_type";

        /**
         * 图集封面文件信息 json格式
         * {"userId":"","fileId":"","filePath":"","globalId":"","type":0,"orientation":0}
         */
        public static final String ALBUM_COVER_FILE = "album_cover_file";

        /**
         * 图集中图片和视频的总数量，服务端返回格式为Map<Int,Int>
         * (101,10) 类型 -> 数量
         */
        public static final String ALBUM_COUNT_MAP = "album_count_map";

        /**
         * 图集内文件总数量
         */
        public static final String ALBUM_FILE_COUNT = "album_file_count";

        /**
         * 修改图集名称权限
         */
        public static final String PERMISSION_CHANGE_NAME = "change_name";

        /**
         * 修改图集封面权限
         */
        public static final String PERMISSION_CHANGE_COVER = "change_cover";

        /**
         * 邀请成员权限
         */
        public static final String PERMISSION_INVITE_OTHER = "invite_other";

        /**
         * 上传文件权限
         */
        public static final String PERMISSION_UPLOAD_FILE = "upload_file";

        /**
         * 保存到数据库的时间
         */
        public static final String SAVE_TIME = "save_time";

        /**
         * 优先级，用于上传
         */
        public static final String PRIORITY = "priority";

        /**
         * 图集是否被删除
         */
        public static final String DELETE = "is_delete";
    }

    public static final class SharedAlbum extends SharedAlbumColumns {

        public static final String TAB = "shared_album";
        private static final String SWITCH = "shared_album_switch";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }

        /**
         * 共享图集开关uri,用来通知AllCacheSet更新数据
         *
         * @return 开关Uri
         */
        public static Uri getSwitchUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + SWITCH);
        }
    }

    @IntDef({SharedMediaColumns.FILE_CHECKING,
            SharedMediaColumns.FILE_CHECK_PASS,
            SharedMediaColumns.FILE_CHECK_NOT_PASS,
            SharedMediaColumns.FILE_OTHER_ABNORMAL,
            SharedMediaColumns.FILE_SIZE_BEYOND})
    public @interface FileCheckState {
    }

    /**
     * 共享图集2.0 - 图集文件
     */
    public static class SharedMediaColumns implements BaseColumns {

        /**
         * 视频文件最大允许上传200MB
         */
        public static final int VIDEO_DATA_LIMIT = 200 * 1024 * 1024;

        /**
         * 图片文件最大允许上传50MB
         */
        public static final int IMAGE_DATA_LIMIT = 50 * 1024 * 1024;

        /**
         * 文件审核状态:审核中
         */
        public static final int FILE_CHECKING = 0;

        /**
         * 文件审核状态:通过
         */
        public static final int FILE_CHECK_PASS = 1;

        /**
         * 文件审核状态:审核未通过
         */
        public static final int FILE_CHECK_NOT_PASS = 2;

        /**
         * 文件审核状态:网络、服务异常上传失败
         */
        public static final int FILE_OTHER_ABNORMAL = 3;

        /**
         * 文件审核状态:文件大小超出
         */
        public static final int FILE_SIZE_BEYOND = 4;

        /**
         * 云端生成唯一id，如果为空或者null表示改文件还没上传到云端
         */
        public static final String GLOBAL_ID = "global_id";

        /**
         * 所属图集id
         */
        public static final String ALBUM_ID = "album_id";

        /**
         * 所属图集创建者id
         */
        public static final String ALBUM_USER_ID = "album_user_id";

        /**
         * 文件所属用户id
         */
        public static final String USER_ID = "user_id";

        /**
         * 文件md5
         */
        public static final String MD5 = "md5";

        /**
         * 文件存储id
         */
        public static final String FILE_ID = "file_id";

        /**
         * 文件上传路径，不允许为空
         */
        public static final String FILE_PATH = "file_path";

        /**
         * 文件类型 已转化成相册同样的类型，参考MediaStore.Files.FileColumns.MEDIA_TYPE_IMAGE
         */
        public static final String MEDIA_TYPE = "media_type";

        /**
         * 视频文件时长
         */
        public static final String DURATION = "duration";

        /**
         * 文件高度
         */
        public static final String HEIGHT = "height";

        /**
         * 文件宽度
         */
        public static final String WIDTH = "width";

        /**
         * 文件大小
         */
        public static final String SIZE = "file_size";

        /**
         * 文件上传状态 FileCheckState
         */
        public static final String FILE_CHECK = "file_check";

        /**
         * 元数据创建时间，用来显示未上传成功文件的时间
         */
        public static final String CREATE_TIME = "create_time";

        /**
         * 文件上传成功时间，用来显示已上传成功文件的时间
         */
        public static final String UPLOAD_TIME = "upload_time";

        /**
         * 年
         */
        public static final String YEAR = "year";

        /**
         * 月
         */
        public static final String MONTH = "month";

        /**
         * 日
         */
        public static final String DAY = "day";

        /**
         * 旋转角度
         */
        public static final String ORIENTATION = "orientation";

        /**
         * 文件是否删除
         */
        public static final String DELETE = "is_delete";

        /**
         * 文件上传失败重试次数
         */
        public static final String RETRY_COUNT = "retry_count";

        /**
         * cloudkit IO 空间校验字段
         */
        public static final String CHECK_PAYLOAD = "check_payload";

        /**
         * 默认保留扩展字段json格式
         */
        public static final String EXTRA_INFO = "extra_info";
    }

    public static final class SharedMedia extends SharedMediaColumns {

        public static final String TAB = "shared_media";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    /**
     * 共享图集2.0 - 邀请记录
     */
    public static class SharedInviteColumns implements BaseColumns {

        /**
         * 手机号，存入时已做编码处理
         */
        public static final String PHONE = _ID;

        /**
         * 显示名字
         */
        public static final String DISPLAY_NAME = "display_name";

        /**
         * 记录更新时间
         */
        public static final String UPDATE_TIME = "update_time";
    }

    public static final class SharedInvite extends SharedInviteColumns {

        public static final String TAB = "shared_invite";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    /**
     * 扫描状态<p>
     * {@link com.oplus.gallery.foundation.database.table.LocalMediaTable}和{@link com.oplus.gallery.foundation.database.table.RecycleMediaTable}
     * 中有一个字段scan_state记录图片是否进行过某一项扫描，
     * 以位的形式存储，这里定义的对应的常量值
     */
    public static final class ScanState {
        /**
         * 是否扫描过文字<p>
         * 1 << 0
         */
        public static final int TEXT_SCANNED = 1;

        /**
         * 是否扫描过证件、卡类、证明材料
         * 1 << 1
         */
        public static final int CREDENTIAL_SCANNED = 1 << 1;

        /**
         * 是否进行过图片矫正扫描（文本矫正功能已废弃）
         * 1 << 2
         */
        @Deprecated
        public static final int PIC_CORRECTION_SCANNED = 1 << 2;
    }

    public static class ScanOcrEmbeddingColumns implements BaseColumns {
        /**
         * media_id
         */
        public static final String MEDIA_ID = GalleryColumns.LocalColumns.MEDIA_ID;

        /**
         * local_media表中的_id
         * GalleryScanProviderHelper.convertImageToMediaItem()等地方需要用到local_media的_id。否则无法获取bitmap
         */
        public static final String LOCAL_MEDIA_TABLE_ID = "local_media_table_id";

        /**
         * 文件路径
         */
        public static final String DATA = MetaColumns.DATA;

        /**
         * 文件类型
         */
        public static final String MEDIA_TYPE = GalleryColumns.LocalColumns.MEDIA_TYPE;

        /**
         * invalid
         */
        public static final String INVALID = MetaColumns.INVALID;

        /**
         * is_recycled
         */
        public static final String IS_RECYCLED = MetaColumns.IS_RECYCLED;

        /**
         * version
         */
        public static final String VERSION = "version";

        /**
         * 扫描打断 0。数据库不记录此值！！！
         * 扫描成功 1
         * 底层错误 2
         */
        public static final String SCAN_STATE = GalleryColumns.LocalColumns.SCAN_STATE;

        /**
         * date_taken
         */
        public static final String DATE_TAKEN = GalleryColumns.LocalColumns.DATE_TAKEN;

        private ScanOcrEmbeddingColumns() {
        }
    }

    public static final class ScanOcrEmbedding extends ScanOcrEmbeddingColumns {
        public static final String TAB = "scan_ocr_embedding";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    /**
     * 宠物表数据列
     */
    public static class PetColumns implements BaseColumns {

        /**
         * had not group yet
         */
        public static final int GROUP_ID_0 = 0;

        /**
         * only one face will group to this, we will regroup them
         */
        public static final int GROUP_ID_1 = 1;

        /**
         * can not sure the face is exactness
         */
        public static final int GROUP_ID_2 = 2;

        /**
         * normal group base
         */
        public static final int GROUP_ID_3 = 3;

        /**
         * 文件路径
         */
        public static final String DATA = MetaColumns.DATA;

        /**
         * 媒体类型
         */
        public static final String MEDIA_TYPE = GalleryColumns.GalleryCommonColumns.MEDIA_TYPE;

        /**
         * 无效状态类型
         */
        public static final String INVALID = MetaColumns.INVALID;

        /**
         * 媒体文件是否被移入到回收站
         */
        public static final String IS_RECYCLED = MetaColumns.IS_RECYCLED;

        /**
         * 缩图像素宽度
         */
        public static final String THUMB_W = "thumb_width";

        /**
         * 缩图像素高度
         */
        public static final String THUMB_H = "thumb_height";

        /**
         * 宠物聚类/分组ID
         */
        public static final String GROUP_ID = "group_id";

        /**
         * 宠物聚类图集名称
         */
        public static final String GROUP_NAME = "group_name";

        /**
         * 该角色与我的关系类型。
         * 0：自定义关系。
         * 1 ~ n：默认关系（无、自己、爸爸、妈妈、伴侣。。。）
         */
        public static final String RELATION_TYPE = "relation_type";

        /**
         * 该角色与我的关系 - 自定义的名称。需 [RELATION_TYPE] = 0 时，才会生效。
         */
        public static final String CUSTOM_RELATION = "custom_relation";

        /**
         * 不显示该角色，默认值 false 为显示，true 为隐藏
         */
        public static final String IS_HIDE = "is_hide";

        /**
         * 宠物特征数据
         */
        public static final String FEATURE = "feature";

        /**
         * 置信度，用于筛除负例，与人脸照片质量无关，值越高表示置信度越高
         */
        public static final String SCORE = "score";

        /**
         * 宠物脸最终评分，最终的封面评分来源
         */
        public static final String FINAL_SCORE = "final_score";

        /**
         * 用于表示在实时宠物跟踪中的相同宠物在不同帧多次出现  0：有  1：没有
         */
        public static final String APPEAR_MUL = "appear_mul";

        /**
         * 封面分数，用于计算 [FINAL_SCORE] 分数
         */
        public static final String PET_COVER_SCORE = "pet_cover_score";

        /**
         * 封面权重，用于计算 [FINAL_SCORE] 分数
         */
        public static final String PET_COVER_WEIGHT = "pet_cover_weight";

        /**
         * 宠物脸部区域离左边界的距离
         */
        public static final String LEFT = "rect_left";

        /**
         * 宠物脸部区域离上边界的距离
         */
        public static final String TOP = "rect_top";

        /**
         * 宠物脸部区域离右边界的距离
         */
        public static final String RIGHT = "rect_right";

        /**
         * 宠物脸部区域离下边界的距离
         */
        public static final String BOTTOM = "rect_bottom";

        /**
         * 是否默认封面
         */
        public static final String IS_DEFAULT_COVER = "is_default_cover";

        /**
         * 是否自选的封面
         */
        public static final String IS_CHOSEN = "is_chosen";

        /**
         * 该图左扫描的时间戳
         */
        public static final String SCAN_DATE = "scan_date";

        /**
         * 聚类的时间戳
         */
        public static final String GROUP_DATE = "group_date";

        /**
         * 是否手动移入
         */
        public static final String IS_MANUAL = "is_manual";

        /**
         * 手动移入时间戳
         */
        public static final String MANUAL_DATE = "manual_date";

        /**
         * 扫描所使用的算法模型版本
         */
        public static final String MODEL_VERSION = "model_version";

        private PetColumns() {
        }
    }

    /**
     * 宠物表
     */
    public static final class Pet extends PetColumns {
        public static final String TAB = "pet";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    /**
     * 宠物图集详情数据列
     */
    public static class PetViewColumns implements BaseColumns {
        public static final String MEDIA_ID = GalleryColumns.GalleryCommonColumns.MEDIA_ID;
        public static final String DATE_MODIFIED = GalleryColumns.GalleryCommonColumns.DATE_MODIFIED;
        public static final String DATE_TAKEN = GalleryColumns.GalleryCommonColumns.DATE_TAKEN;
        public static final String MEDIA_TYPE = GalleryColumns.GalleryCommonColumns.MEDIA_TYPE;
        public static final String CROP_RECT = GalleryColumns.LocalColumns.CROP_RECT;
        public static final String CODEC_TYPE = GalleryColumns.LocalColumns.CODEC_TYPE;

        public static final String THUMB_W = PetColumns.THUMB_W;
        public static final String THUMB_H = PetColumns.THUMB_H;
        public static final String GROUP_ID = PetColumns.GROUP_ID;
        public static final String FEATURE = PetColumns.FEATURE;
        public static final String FINAL_SCORE = PetColumns.FINAL_SCORE;
        public static final String LEFT = PetColumns.LEFT;
        public static final String TOP = PetColumns.TOP;
        public static final String RIGHT = PetColumns.RIGHT;
        public static final String BOTTOM = PetColumns.BOTTOM;
        public static final String IS_CHOSEN = PetColumns.IS_CHOSEN;
        public static final String IS_DEFAULT_COVER = PetColumns.IS_DEFAULT_COVER;
        public static final String RELATION_TYPE = PetColumns.RELATION_TYPE;
        public static final String CUSTOM_RELATION = PetColumns.CUSTOM_RELATION;
        public static final String LOCAL_ID = "local_id";

        private PetViewColumns() {
        }
    }

    /**
     * 宠物图集详情视图
     */
    public static final class PetView extends PetViewColumns {
        public static final String TAB = "pet_view";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    /**
     * 人宠图集列表数据列
     */
    public static class PersonPetListViewColumns {
        public static final String MEDIA_TYPE = GalleryColumns.GalleryCommonColumns.MEDIA_TYPE;
        public static final String GROUP_ID = PetColumns.GROUP_ID;
        public static final String GROUP_NAME = PetColumns.GROUP_NAME;
        public static final String IS_MANUAL = PetColumns.IS_MANUAL;
        public static final String MANUAL_DATE = PetColumns.MANUAL_DATE;
        public static final String RELATION_TYPE = PetColumns.RELATION_TYPE;
        public static final String CUSTOM_RELATION = PetColumns.CUSTOM_RELATION;
        public static final String ROLE_TYPE = "role_type";
        public static final String NUM_ALL_FACES = "num_all_faces";
        public static final String NUM_IMAGE_FACES = "num_image_faces";
        public static final String NUM_VIDEO_FACES = "num_video_faces";

        private PersonPetListViewColumns() {
        }
    }

    /**
     * 人宠图集列表视图
     */
    public static final class PersonPetListView extends PersonPetListViewColumns {
        public static final String TAB = "person_pet_list_view";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    /**
     * 人宠群组表的列
     */
    public static class PersonPetGroupColumns implements BaseColumns {
        /**
         * had not group yet
         */
        public static final int GROUP_ID_0 = 0;

        /**
         * only one face will group to this, we will regroup them
         */
        public static final int GROUP_ID_1 = 1;

        /**
         * can not sure the face is exactness
         */
        public static final int GROUP_ID_2 = 2;

        /**
         * normal group base
         */
        public static final int GROUP_ID_3 = 3;

        /**
         * 文件路径
         */
        public static final String DATA = MetaColumns.DATA;

        /**
         * 媒体类型
         */
        public static final String MEDIA_TYPE = GalleryColumns.GalleryCommonColumns.MEDIA_TYPE;

        /**
         * 无效状态类型
         */
        public static final String INVALID = MetaColumns.INVALID;

        /**
         * 媒体文件是否被移入到回收站
         */
        public static final String IS_RECYCLED = MetaColumns.IS_RECYCLED;

        /**
         * 群组创建的时间戳
         */
        public static final String MERGE_GROUP_CREATE_DATE = "merge_group_create_date";

        /**
         * 群组是否是手动创建的。0 表示手动创建；1 表示自动创建。
         */
        public static final String MERGE_GROUP_CREATE_TYPE = "merge_group_create_type";

        /**
         * 该媒体项所属的群组名称：根据合并的人宠图集的名称来决定
         */
        public static final String MERGE_GROUP_NAME = "merge_group_name";

        /**
         * 群组图集ID
         */
        public static final String MERGE_GROUP_ID = "merge_group_id";

        /**
         * 构建群组时的人物图集ID列表
         */
        public static final String PERSON_GROUP_IDS = "person_group_ids";

        /**
         * 构建群组时的宠物图集ID列表
         */
        public static final String PET_GROUP_IDS = "pet_group_ids";

        /**
         * 图片作为群组封面的评分
         */
        public static final String SCORE = "score";

        /**
         * 缩图像素宽度
         */
        public static final String THUMB_W = "thumb_width";

        /**
         * 缩图像素高度
         */
        public static final String THUMB_H = "thumb_height";

        /**
         * 合照中全部角色脸部区域离左边界的距离
         */
        public static final String RECT_LEFT = "rect_left";

        /**
         * 合照中全部角色脸部区域离上边界的距离
         */
        public static final String RECT_TOP = "rect_top";

        /**
         * 合照中全部角色脸部区域离右边界的距离
         */
        public static final String RECT_RIGHT = "rect_right";

        /**
         * 合照中全部角色脸部区域离下边界的距离
         */
        public static final String RECT_BOTTOM = "rect_bottom";

        /**
         * 是否默认封面
         */
        public static final String IS_DEFAULT_COVER = "is_default_cover";

        /**
         * 是否自选的封面
         */
        public static final String IS_CHOSEN = "is_chosen";

        /**
         * 是否已解散
         */
        public static final String IS_DISBAND = "is_disband";

        private PersonPetGroupColumns() {
        }
    }

    /**
     * 人宠群组表
     */
    public static final class PersonPetGroup extends PersonPetGroupColumns {
        public static final String TAB = "person_pet_group";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    /**
     * 人宠群组图集详情数据列
     */
    public static class PersonPetGroupDetailViewColumns implements BaseColumns {
        public static final String MEDIA_ID = GalleryColumns.GalleryCommonColumns.MEDIA_ID;
        public static final String DATE_MODIFIED = GalleryColumns.GalleryCommonColumns.DATE_MODIFIED;
        public static final String DATE_TAKEN = GalleryColumns.GalleryCommonColumns.DATE_TAKEN;
        public static final String MEDIA_TYPE = GalleryColumns.GalleryCommonColumns.MEDIA_TYPE;
        public static final String CROP_RECT = GalleryColumns.LocalColumns.CROP_RECT;
        public static final String CODEC_TYPE = GalleryColumns.LocalColumns.CODEC_TYPE;

        public static final String MERGE_GROUP_ID = PersonPetGroupColumns.MERGE_GROUP_ID;
        public static final String MERGE_GROUP_NAME = PersonPetGroupColumns.MERGE_GROUP_NAME;
        public static final String SCORE = PersonPetGroupColumns.SCORE;
        public static final String IS_CHOSEN = PersonPetGroupColumns.IS_CHOSEN;
        public static final String IS_DEFAULT_COVER = PersonPetGroupColumns.IS_DEFAULT_COVER;
        public static final String THUMB_W = PersonPetGroupColumns.THUMB_W;
        public static final String THUMB_H = PersonPetGroupColumns.THUMB_H;
        public static final String RECT_LEFT = PersonPetGroupColumns.RECT_LEFT;
        public static final String RECT_TOP = PersonPetGroupColumns.RECT_TOP;
        public static final String RECT_RIGHT = PersonPetGroupColumns.RECT_RIGHT;
        public static final String RECT_BOTTOM = PersonPetGroupColumns.RECT_BOTTOM;
        public static final String LOCAL_ID = "local_id";

        private PersonPetGroupDetailViewColumns() {
        }
    }

    /**
     * 人宠群组图集详情视图
     */
    public static final class PersonPetGroupDetailView extends PersonPetGroupDetailViewColumns {
        public static final String TAB = "person_pet_group_detail_view";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    /**
     * 人宠群组图集列表数据列
     */
    public static class PersonPetGroupListViewColumns {
        public static final String MERGE_GROUP_ID = PersonPetGroupColumns.MERGE_GROUP_ID;
        public static final String MERGE_GROUP_NAME = PersonPetGroupColumns.MERGE_GROUP_NAME;
        public static final String PERSON_GROUP_IDS = PersonPetGroupColumns.PERSON_GROUP_IDS;
        public static final String PET_GROUP_IDS = PersonPetGroupColumns.PET_GROUP_IDS;
        public static final String MERGE_GROUP_CREATE_TYPE = PersonPetGroupColumns.MERGE_GROUP_CREATE_TYPE;
        public static final String MERGE_GROUP_CREATE_DATE = PersonPetGroupColumns.MERGE_GROUP_CREATE_DATE;
        public static final String MEDIA_TYPE = GalleryColumns.GalleryCommonColumns.MEDIA_TYPE;
        public static final String NUM_ALL_FACES = "num_all_faces";
        public static final String NUM_IMAGE_FACES = "num_image_faces";
        public static final String NUM_VIDEO_FACES = "num_video_faces";

        private PersonPetGroupListViewColumns() {
        }
    }

    /**
     * 人宠群组图集列表视图
     */
    public static final class PersonPetGroupListView extends PersonPetGroupListViewColumns {
        public static final String TAB = "person_pet_group_list_view";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    public static class ScanTravelColumns implements BaseColumns {
        /**
         * 路径
         */
        public static final String PATH = GalleryColumns.GalleryCommonColumns.DATA;

        /**
         * 媒体类型
         */
        public static final String MEDIA_TYPE = GalleryColumns.GalleryCommonColumns.MEDIA_TYPE;

        /**
         * 无效状态类型
         */
        public static final String INVALID = GalleryColumns.LocalColumns.INVALID;

        /**
         * 文件是否移入回收站
         */
        public static final String IS_RECYCLED = GalleryColumns.GalleryCommonColumns.IS_TRASHED;

        /**
         * 该图集的排序方式
         */
        public static final String ORDER_TYPE = "order_type";

        /**
         * 旅行的目的地——等同于旅行图集的名称
         */
        public static final String TRAVEL_DESTINATION = "travel_destination";

        /**
         * 旅行开始时间
         */
        public static final String TRAVEL_START_TIME = "travel_start_time";

        /**
         * 旅行结束时间
         */
        public static final String TRAVEL_END_TIME = "travel_end_time";

        /**
         * 旅行图集唯一id: 旅行目的地:开始时间-结束时间
         */
        public static final String TRAVEL_ID = "travel_id";

        /**
         * 图片综合评分
         */
        public static final String SCORE = "score";

        /**
         * 是否为默认封面
         * IS_DEFAULT_COVER = 1 用户手动设置该图片为封面
         */
        public static final String IS_DEFAULT_COVER = "is_default_cover";

        /**
         * 当前图集的封面
         * IS_CURRENT_COVER = 1 是当前的封面
         */
        public static final String IS_CURRENT_COVER = "is_current_cover";

        /**
         * 封面打分算法版本号
         */
        public static final String MODEL_VERSION = "model_version";
    }

    public static class ScanTravel extends ScanTravelColumns {
        public static final String TAB = "scan_travel";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    public static class ScanTravelViewColumns implements BaseColumns {
        /**
         * 文件路径
         */
        public static final String PATH = GalleryColumns.GalleryCommonColumns.DATA;
        /**
         * 媒体id
         */
        public static final String MEDIA_ID = GalleryColumns.LocalColumns.MEDIA_ID;

        /**
         * 媒体类型
         */
        public static final String MEDIA_TYPE = GalleryColumns.GalleryCommonColumns.MEDIA_TYPE;

        /**
         * 媒体创建时间戳
         */
        public static final String DATETAKEN = GalleryColumns.GalleryCommonColumns.DATE_TAKEN;

        /**
         * 媒体更新事件戳
         */
        public static final String DATE_MODIFIED = GalleryColumns.GalleryCommonColumns.DATE_MODIFIED;

        /**
         * 文件名称
         */
        public static final String TITLE = GalleryColumns.GalleryCommonColumns.TITLE;

        /**
         * 该图集的排序方式
         */
        public static final String ORDER_TYPE = "order_type";

        /**
         * 旅行的目的地——等同于旅行图集的名称
         */
        public static final String TRAVEL_DESTINATION = "travel_destination";

        /**
         * 旅行开始时间
         */
        public static final String TRAVEL_START_TIME = "travel_start_time";

        /**
         * 旅行结束时间
         */
        public static final String TRAVEL_END_TIME = "travel_end_time";

        /**
         * 旅行图集唯一id: 旅行目的地:开始时间-结束时间
         */
        public static final String TRAVEL_ID = "travel_id";

        /**
         * 图片综合评分
         */
        public static final String SCORE = "score";

        /**
         * 是否为默认封面
         * IS_DEFAULT_COVER = 1 用户手动设置该图片为封面
         */
        public static final String IS_DEFAULT_COVER = "is_default_cover";

        /**
         * 当前图集的封面
         * IS_CURRENT_COVER = 1 是当前的封面
         */
        public static final String IS_CURRENT_COVER = "is_current_cover";

        /**
         * 是否收藏
         */
        public static final String IS_FAVORITE = GalleryColumns.LocalColumns.IS_FAVORITE;

        /**
         * 图集里媒体数量
         */
        public static final String COUNT = "count";

        /**
         * 图集里照片媒体数量
         */
        public static final String IMAGE_COUNT = "image_count";

        /**
         * 图集里视频媒体数量
         */
        public static final String VIDEO_COUNT = "video_count";
    }

    public static class ScanTravelView extends ScanTravelViewColumns {
        public static final String TAB = "scan_travel_view";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    /**
     * poi_grid_route列名
     */
    public static class PoiGridColumns implements BaseColumns {
        /**
         * poi_route表的_id
         */
        public static final String POI_ID = "poi_id";
        /**
         * 图片gps_key
         */
        public static final String GPS_KEY = "gps_key";

        /**
         * poi 模型版本号
         */
        public static final String MODEL_VERSION = "model_version";

        private PoiGridColumns() {
        }
    }

    /**
     * poi_grid_route表，关联poi和图片信息
     */
    public static final class PoiGridRoute extends PoiGridColumns {
        public static final String TAB = "poi_grid_route";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    /**
     * 不显示在照片页的图集表的字段集合
     */
    public static class AlbumHideOnTimeLinePageColumns implements BaseColumns {
        /**
         * 图集分组id 同CacheStore.AlbumColumns.album_id
         */
        public static final String ALBUM_ID = "album_id";

        /**
         * relative_path 同GalleryStore.GalleryColumns.LocalColumns.RELATIVE_PATH
         */
        public static final String RELATIVE_PATH = "relative_path";
    }

    /**
     * 不显示在照片页的图集表
     */
    public static final class AlbumHideOnTimeLinePage extends AlbumHideOnTimeLinePageColumns {
        public static final String TAB = "album_hide_on_time_line_page";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    /**
     * 学习表字段
     */
    public static class StudyColumns implements BaseColumns {
        /**
         * 路径
         */
        public static final String DATA = GalleryColumns.GalleryCommonColumns.DATA;

        /**
         * 媒体类型
         */
        public static final String MEDIA_TYPE = GalleryColumns.GalleryCommonColumns.MEDIA_TYPE;

        /**
         * 经过算法扫描后的结果，即：是否为学习记录
         */
        public static final String IS_STUDY = "is_study";

        /**
         * 无效状态类型
         */
        public static final String INVALID = GalleryColumns.LocalColumns.INVALID;

        /**
         * 文件是否移入回收站
         */
        public static final String IS_RECYCLED = MetaColumns.IS_RECYCLED;

        /**
         * 是否手动操作标记为移除
         */
        public static final String IS_MANUAL = "is_manual";

        /**
         * 扫描时间
         */
        public static final String SCAN_DATE = "scan_date";

        /**
         * 扫描所用的模型版本号
         */
        public static final String MODEL_VERSION = "model_version";
    }

    public static class ScanStudy extends StudyColumns {
        public static final String TAB = "scan_study";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    /**
     * 这个uri用于通知地图动态封面加载成功的变化
     */
    public static class DynamicMapCover {
        public static final String TAB = "dynamic_map_cover";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }


    public static class CloudSyncAbandonDataColumns implements BaseColumns {
        /**
         * sysRecordId, 就是globalId
         */
        public static final String GLOBAL_ID = GalleryColumns.CloudColumns.GLOBAL_ID;

        /**
         * 对应sysVersion
         *
         */
        public static final String SYS_VERSION = GalleryColumns.CloudColumns.SYS_VERSION;

        /**
         * 对应云端sysUniqueId
         */
        public static final String SYS_UNIQUE_ID = "sys_unique_id";

        /**
         * 对应云端sysProtocolVersion
         */
        public static final String SYS_PROTOCOL_VERSION = "sys_protocol_version";

        /**
         * 对应云端sysRecordType
         */
        public static final String SYS_RECORD_TYPE = "sys_record_type";

        /**
         * sysDataType
         */
        public static final String SYS_DATATYPE = "sys_datatype";

        /**
         * operatorType
         */
        public static final String OPERATOR_TYPE = "operator_type";

        /**
         * sysStatus
         */
        public static final String SYS_STATUS = "_sys_status";

        /**
         * fields
         */
        public static final String FILELDS = "fileds";

        private CloudSyncAbandonDataColumns() {
        }
    }

    public static final class CloudSyncAbandonData extends CloudSyncAbandonDataColumns {
        public static final String TAB = "cloud_sync_abandon_data";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }

    public static class ScanCaptionColumns implements BaseColumns {
        /**
         * local_media表中的_id
         */
        public static final String LOCAL_MEDIA_TABLE_ID = "local_media_table_id";

        /**
         * 文件路径
         */
        public static final String DATA = MetaColumns.DATA;

        /**
         * invalid
         */
        public static final String INVALID = MetaColumns.INVALID;

        /**
         * is_recycled
         */
        public static final String IS_RECYCLED = MetaColumns.IS_RECYCLED;

        /**
         * 节日，若有则存储
         */
        public static final String FESTIVAL = "festival";

        /**
         * GPS_KEY
         */
        public static final String GPS_KEY = GalleryColumns.LocalColumns.GPS_KEY;

        /**
         * 地址
         */
        public static final String LOCATION = "location";

        /**
         * POI NAME
         */
        public static final String POI_NAME = "poi_name";

        /**
         * 扫描打断 0。数据库不记录此值！！！
         * 扫描成功 1
         * 底层错误 2
         */
        public static final String SCAN_STATE = GalleryColumns.LocalColumns.SCAN_STATE;

        /**
         * date_taken
         */
        public static final String DATE_TAKEN = GalleryColumns.LocalColumns.DATE_TAKEN;

        /**
         * caption内容 caption扫描生成四份内容，每份内容需要单独做embedding
         */
        public static final String CAPTION_CONTENT0 = "caption_content0";
        public static final String CAPTION_CONTENT1 = "caption_content1";
        public static final String CAPTION_CONTENT2 = "caption_content2";
        public static final String CAPTION_CONTENT3 = "caption_content3";

        /**
         * 做embedding的内容 由caption扫描的文本 + 节日 + 地址
         */
        public static final String EMBEDDING_CONTENT0 = "embedding_content0";
        public static final String EMBEDDING_CONTENT1 = "embedding_content1";
        public static final String EMBEDDING_CONTENT2 = "embedding_content2";
        public static final String EMBEDDING_CONTENT3 = "embedding_content3";

        /**
         * caption的版本号
         */
        public static final String CAPTION_VERSION = "caption_version";

        /**
         * embedding模型版本号
         */
        public static final String EMBEDDING_VERSION = "embedding_version";

        private ScanCaptionColumns() {
        }
    }

    public static final class ScanCaption extends ScanCaptionColumns {
        public static final String TAB = "scan_caption";

        public static Uri getContentUri() {
            return Uri.parse(GALLERY_CONTENT_URI + "/" + TAB);
        }
    }
}
