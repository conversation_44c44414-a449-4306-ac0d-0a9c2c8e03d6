/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - LocalMediaMonitor.kt
 ** Description: local表监视器。当表字段发生变化时,交由此监视器处理
 ** Version: 1.0
 ** Date: 2024/4/17
 ** Author: <PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery     2024/4/17     1.0         OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.foundation.database.trigger.monitor

import android.content.ContentValues
import android.content.Context
import android.net.Uri
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.CloudColumns
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns
import com.oplus.gallery.foundation.database.trigger.DataDiffOperationManager
import com.oplus.gallery.foundation.database.trigger.DataOperationProcessorType
import com.oplus.gallery.foundation.database.trigger.operator.ITableOperationProcessor
import com.oplus.gallery.foundation.database.trigger.operator.OperationRowFieldKey.KEY_BUCKET_PATH
import com.oplus.gallery.foundation.database.trigger.operator.OperationRowFieldKey.KEY_DATA
import com.oplus.gallery.foundation.database.trigger.operator.OperationRowFieldKey.KEY_RELATIVE_PATH
import com.oplus.gallery.standard_lib.file.utils.FilePathUtils

/**
 * local表监视器。当表字段发生变化时,交由此监视器处理
 * 如:处理后更新图集表
 */
internal class LocalMediaMonitor(context: Context) : TableMonitor(context) {
    private val operationProcessorList = mutableListOf<ITableOperationProcessor>().apply {
        // 按照业务优先级add processor，优先处理 LOCAL_MEDIA_DIFF
        add(DataDiffOperationManager.getProcessor(DataOperationProcessorType.LOCAL_MEDIA_DIFF))
        add(DataDiffOperationManager.getProcessor(DataOperationProcessorType.ALBUM_MEDIA_DIFF))
        add(DataDiffOperationManager.getProcessor(DataOperationProcessorType.EDIT_MEDIA_DIFF))
    }

    override fun getTag(): String {
        return TAG
    }

    override fun getInsertOrDelFieldCount(): Int {
        return INSERT_OR_DELETE_FIELD_COUNT
    }

    override fun getProjections(): List<Pair<String, String>> {
        return PROJECTIONS
    }

    override fun getTableUri(): Uri {
        return GalleryStore.GalleryMedia.getLocalContentUri()
    }

    override fun getTriggerName(type: Int): String {
        return when (type) {
            INSERT -> TRIGGER_TYPE_INSERT
            DELETE -> TRIGGER_TYPE_DELETE
            UPDATE -> TRIGGER_TYPE_UPDATE
            else -> ""
        }
    }

    override fun getTableName(): String {
        return GalleryStore.GalleryMedia.TAB
    }

    override fun getCustomFunctionName(type: Int): String {
        return when (type) {
            INSERT -> CUSTOM_FUNCTION_NAME_INSERT
            DELETE -> CUSTOM_FUNCTION_NAME_DELETE
            UPDATE -> CUSTOM_FUNCTION_NAME_UPDATE
            else -> ""
        }
    }

    override fun getOperationProcessorList(): List<ITableOperationProcessor> {
        return operationProcessorList
    }

    override fun getUpdateRowValues(
        rowFieldsList: List<String>,
        startIndex: Int,
        tag: String,
        projections: List<Pair<String, String>>
    ): ContentValues? {
        return super.getUpdateRowValues(rowFieldsList, startIndex, tag, projections)?.apply {
            put(KEY_BUCKET_PATH, getBucketPath(getAsString(KEY_RELATIVE_PATH), getAsString(KEY_DATA)))
        }
    }

    /**
     * 获取bucketPath，先取relative_path再取_data
     *
     * 目前存在必须从relative_path获取的情况：
     * - 从谷歌云同步数据，没有文件路径只有文件名称且文件名称可能重复，相册把这些数据都归在DCIM/Restored/目录下，并且为了避免数据库_data冲突所以_data用uuid拼接，
     * 也就是说relative_path中才有期望的bucketPath而_data中没有
     */
    private fun getBucketPath(relative: String?, data: String?): String {
        return relative?.takeIf { it.trim().isNotEmpty() }?.let {
            FilePathUtils.getBucketPathByRelativePath(it)
        } ?: FilePathUtils.getBucketPathFromData(data)
    }

    companion object {
        private const val TAG = "LocalMediaMonitor"

        /**
         * 数据库变更时，业务关心的字段。至少包含构建LocalMediaItem的所有字段，能够覆盖相册所有的业务场景，可以直接刷新给业务使用。
         * - 注意：修改此字段后，要升级版本号，重新注册触发器；更新[INSERT_OR_DELETE_FIELD_COUNT]
         */
        private val PROJECTIONS = listOf(
            Pair(LocalColumns.DATA, IF_TEXT_NOT_NULL),
            Pair(LocalColumns._ID, IF_INT_NOT_NULL),
            Pair(LocalColumns.MEDIA_ID, IF_INT_NOT_NULL),
            Pair(LocalColumns.SIZE, IF_LONG_NULL),
            Pair(LocalColumns.DATE_ADDED, IF_LONG_NULL),
            Pair(LocalColumns.DATE_MODIFIED, IF_LONG_NULL),
            Pair(LocalColumns.DATE_TAKEN, IF_LONG_NULL),
            Pair(LocalColumns.MIME_TYPE, IF_TEXT_NULL),
            Pair(LocalColumns.TITLE, IF_TEXT_NULL),
            Pair(LocalColumns.ORIENTATION, IF_INT_NULL),
            Pair(LocalColumns.BUCKET_ID, IF_INT_NULL),
            Pair(LocalColumns.BUCKET_NAME, IF_TEXT_NULL),
            Pair(LocalColumns.DURATION, IF_INT_NULL),
            Pair(LocalColumns.RESOLUTION, IF_TEXT_NULL),
            Pair(LocalColumns.MEDIA_TYPE, IF_INT_NULL),
            Pair(LocalColumns.WIDTH, IF_INT_NULL),
            Pair(LocalColumns.HEIGHT, IF_INT_NULL),
            Pair(LocalColumns.CSHOT_ID, IF_LONG_NULL),
            Pair(LocalColumns.TAGFLAGS, IF_LONG_NULL),
            Pair(LocalColumns.SYNC_STATUS, IF_INT_NULL),
            Pair(LocalColumns.BIT_FORMAT, IF_INT_NULL),
            Pair(LocalColumns.IS_FAVORITE, IF_INT_NULL),
            Pair(LocalColumns.LATITUDE, IF_DOUBLE_NULL),
            Pair(LocalColumns.LONGITUDE, IF_DOUBLE_NULL),
            Pair(LocalColumns.SCAN_ERROR, IF_INT_NULL),
            Pair(LocalColumns.CROP_RECT, IF_TEXT_NULL),
            Pair(LocalColumns.CROP_RECT_VERSION, IF_INT_NULL),
            Pair(LocalColumns.EXT_TAG_FLAGS, IF_LONG_NULL),
            Pair(LocalColumns.SCAN_STATE, IF_INT_NULL),
            Pair(LocalColumns.CODEC_TYPE, IF_TEXT_NULL),
            Pair(CloudColumns.GLOBAL_ID, IF_TEXT_NULL),
            Pair(CloudColumns.FILE_ID, IF_TEXT_NULL),
            Pair(CloudColumns.MD5, IF_TEXT_NULL),
            Pair(CloudColumns.CLOUD_SIZE, IF_LONG_NULL),
            Pair(CloudColumns.CLOUD_WIDTH, IF_INT_NULL),
            Pair(CloudColumns.CLOUD_HEIGHT, IF_INT_NULL),
            Pair(CloudColumns.IS_SUPPORT_COMPRESSED, IF_INT_NULL),
            Pair(CloudColumns.LOCAL_FILE_STATUS, IF_INT_NULL),
            Pair(LocalColumns.YEAR, IF_INT_NULL),
            Pair(LocalColumns.MONTH, IF_INT_NULL),
            Pair(LocalColumns.DAY, IF_INT_NULL),
            Pair(LocalColumns.IS_TRASHED, IF_INT_NULL),
            Pair(LocalColumns.INVALID, IF_INT_NULL),
            Pair(LocalColumns.EXTRA_MSG, IF_TEXT_NULL),
            Pair(LocalColumns.RELATIVE_PATH, IF_TEXT_NULL),
            Pair(LocalColumns.GENERATION_MODIFIED, IF_LONG_NULL)
        )

        /**
         * 自定义增删改函数监听各字段数量
         */
        private const val INSERT_OR_DELETE_FIELD_COUNT = 46

        /**
         * 触发器分类
         */
        private const val TRIGGER_TYPE_INSERT = "tg_insert_local_media_monitor"
        private const val TRIGGER_TYPE_DELETE = "tg_delete_local_media_monitor"
        private const val TRIGGER_TYPE_UPDATE = "tg_update_local_media_monitor"

        /**
         * 自定义函数名
         */
        private const val CUSTOM_FUNCTION_NAME_INSERT = "_insert_into_local_media"
        private const val CUSTOM_FUNCTION_NAME_DELETE = "_delete_from_local_media"
        private const val CUSTOM_FUNCTION_NAME_UPDATE = "_update_to_local_media"
    }
}