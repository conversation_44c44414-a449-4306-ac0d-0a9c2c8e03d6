/***********************************************************
 * * Copyright (C), 2018-2028, OPlus Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File       :  DmpScanLabelProcessor.kt
 * * Description:  处理scan_label表的数据变化
 * * Version: 1.0
 * * Date : 2025/01/11
 * * Author: pusong
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>         <version>  <desc>
 * *  pusong        2025/01/11     1.0        create
 ************************************************************/
package com.oplus.gallery.foundation.database.helper.processor

import android.content.ContentValues
import com.oplus.gallery.foundation.database.helper.DmpSearchSyncHelper.TASK_ENQUEUE_DATA_UPDATE_QUEUE
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.INVALID_NORMAL
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag

/**
 * 处理scan_label表的数据变化信息，回调给DmpSearchSyncHelper
 */
class DmpScanLabelProcessor : DmpTableChangeProcessor() {
    override fun getTag(): String {
        return TAG
    }

    override fun processInsert(contentValuePairList: MutableList<Pair<ContentValues, ContentValues?>>) {
        if (contentValuePairList.isEmpty()) return
        GLog.d(TAG, LogFlag.DL) { "processInsert size:${contentValuePairList.size}" }
        val pathSet = HashSet<String>()
        contentValuePairList.forEach {
            val path = it.first.getAsString(GalleryStore.ScanLabel.DATA)
            val invalid = it.first.getAsInteger(GalleryStore.ScanLabel.INVALID)
            val sceneId = it.first.getAsInteger(GalleryStore.ScanLabel.SCENE_ID)
            if ((invalid == INVALID_NORMAL) && (sceneId != LABEL_UNKNOWN)) {
                pathSet.add(path)
            }
        }
        onTableChange(pathSet, TASK_ENQUEUE_DATA_UPDATE_QUEUE)
    }

    override fun processDelete(contentValuePairList: MutableList<Pair<ContentValues, ContentValues?>>) {
        if (contentValuePairList.isEmpty()) return
        GLog.d(TAG, LogFlag.DL) { "processDelete size:${contentValuePairList.size}" }
        contentValuePairList.mapNotNull {
            it.first.getAsString(GalleryStore.ScanLabel.DATA).takeIf { !it.isNullOrEmpty() }
        }.toHashSet().let { onTableChange(it, TASK_ENQUEUE_DATA_UPDATE_QUEUE) }
    }

    override fun processUpdate(contentValuePairList: MutableList<Pair<ContentValues, ContentValues?>>) {
        if (contentValuePairList.isEmpty()) return
        GLog.d(TAG, LogFlag.DL) { "processUpdate size:${contentValuePairList.size}" }
            val pathSet = HashSet<String>()
            contentValuePairList.forEach {
            val newPath = it.first.getAsString(GalleryStore.ScanLabel.DATA)
            val oldPath = it.second?.getAsString(GalleryStore.ScanLabel.DATA)
            val newSceneId = it.first.getAsString(GalleryStore.ScanLabel.SCENE_ID)
            val oldSceneId = it.second?.getAsString(GalleryStore.ScanLabel.SCENE_ID)

            if (oldPath == null) {
                return@forEach
            }

            if (newSceneId != oldSceneId) {
                pathSet.add(newPath)
            }
        }
        onTableChange(pathSet, TASK_ENQUEUE_DATA_UPDATE_QUEUE)
    }

    companion object {
        private const val TAG = "DmpScanLabelProcessor"
        private const val LABEL_UNKNOWN = 0
    }
}