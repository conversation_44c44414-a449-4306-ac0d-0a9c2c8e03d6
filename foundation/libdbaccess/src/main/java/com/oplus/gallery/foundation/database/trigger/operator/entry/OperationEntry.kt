/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OperationEntry.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2024/6/25
 ** Author: <PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery     2024/6/25     1.0         OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.foundation.database.trigger.operator.entry

import android.content.ContentValues
import android.database.Cursor
import com.google.gson.annotations.SerializedName
import com.oplus.gallery.foundation.database.album.entry.AlbumEntry
import com.oplus.gallery.foundation.database.album.entry.BucketEntry
import com.oplus.gallery.foundation.database.album.entry.CoverItem
import com.oplus.gallery.foundation.database.album.isTargetFlag
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.EXT_SHOT_BY_SAME_DEVICE_PHOTO
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.SLOG_VIDEO_TITLE_PREFIX
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.SLOW_MOTION_TITLE_PREFIX
import com.oplus.gallery.foundation.database.migrate.store.MigrateMediaStore.RecycleMediaColumns.IS_TRASHED_TRUE
import com.oplus.gallery.foundation.database.store.CacheStore
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO
import com.oplus.gallery.foundation.database.trigger.operator.OperationRowFieldKey
import com.oplus.gallery.foundation.database.trigger.operator.OperationType
import com.oplus.gallery.foundation.database.util.DatabaseUtils
import com.oplus.gallery.foundation.exif.oplus.OplusExifTag.EXIF_TAG_ENHANCE_TEXT
import com.oplus.gallery.foundation.exif.oplus.OplusExifTag.EXIF_TAG_FAST_VIDEO
import com.oplus.gallery.foundation.exif.oplus.OplusExifTag.EXIF_TAG_OLIVE
import com.oplus.gallery.foundation.exif.oplus.OplusExifTag.EXIF_TAG_PANORAMA
import com.oplus.gallery.foundation.exif.oplus.OplusExifTag.EXIF_TAG_PORTRAIT_BLUR
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.foundation.util.text.TextUtil
import java.io.Serializable

class OperationAlbumEntry(
    albumId: Int,
    albumName: String? = null,
    albumPath: String = TextUtil.EMPTY_STRING,
    flags: Int = 0,
    var useType: UseType = UseType.UNKNOWN
) : AlbumEntry(albumId, albumName, albumPath, flags) {
    constructor(albumId: Int, useType: UseType, album: AlbumEntry) : this(albumId, useType = useType) {
        set(album)
    }

    private fun set(album: AlbumEntry) {
        albumName = album.albumName
        albumPath = album.albumPath
        flags = album.flags
        bucketIdList.addAll(album.bucketIdList)
        bucketEntryList.addAll(album.bucketEntryList)
        trashedCount = album.trashedCount
        totalCount = album.totalCount
        itemSortType = album.itemSortType
        orderedPosition = album.orderedPosition
        orderedSubPosition = album.orderedSubPosition
        dateTaken = album.dateTaken
        dateModified = album.dateModified
        coverItem = album.coverItem
        isNeedCompatSort = album.isNeedCompatSort
    }

    companion object {
        fun createAlbumEntry(columnIndexMap: Map<String, Int>, cursor: Cursor): OperationAlbumEntry {
            return OperationAlbumEntry(
                albumId = DatabaseUtils.getValidInt(columnIndexMap[CacheStore.AlbumColumns.ALBUM_ID], cursor),
                albumName = DatabaseUtils.getNotNullString(columnIndexMap[CacheStore.AlbumColumns.DISPLAY_NAME], cursor),
                albumPath = DatabaseUtils.getNotNullString(columnIndexMap[CacheStore.AlbumColumns.ALBUM_PATH], cursor),
                flags = DatabaseUtils.getValidInt(columnIndexMap[CacheStore.AlbumColumns.FLAGS], cursor),
            ).apply { setup(columnIndexMap, cursor) }
        }
    }
}

class OperationBucketEntry(
    bucketId: Int,
    bucketName: String? = TextUtil.EMPTY_STRING,
    bucketPath: String? = TextUtil.EMPTY_STRING,
    var useType: UseType = UseType.UNKNOWN
) : BucketEntry(bucketId, bucketName ?: TextUtil.EMPTY_STRING, bucketPath ?: TextUtil.EMPTY_STRING) {
    constructor(bucketId: Int, useType: UseType, bucket: BucketEntry) : this(bucketId, useType = useType) {
        set(bucket)
    }

    private fun set(bucket: BucketEntry) {
        bucketName = bucket.bucketName
        bucketPath = bucket.bucketPath
        addBucketId(bucket.bucketIdList)
        trashedCount = bucket.trashedCount
        count = bucket.count
        imageCount = bucket.imageCount
        imageLocalCount = bucket.imageLocalCount
        videoLocalCount = bucket.videoLocalCount
        gifCount = bucket.gifCount
        jpegCount = bucket.jpegCount
        heifCount = bucket.heifCount
        coverItem = bucket.coverItem
    }
}


/**
 * OperationAlbumEntry和用处类型
 */
enum class UseType {
    /**
     * 默认:来源于数据库,若是UNKNOWN,则不需要处理
     */
    UNKNOWN,

    /**
     * 将Album或BucketEntry数量插入到图集表或图集数量表
     */
    INSERT,

    /**
     * 将Album或BucketEntry数量从图集表或图集数量表中删除
     */
    DELETE,

    /**
     * 将Album或BucketEntry数量更新到图集表或图集数量表
     */
    UPDATE
}

/**
 * local表更新的记录对应的信息
 */
data class RowMediaItem(
    val data: String,
    val bucketId: Int,
    val bucketPath: String,
    val isTrashed: Boolean,
    val isFavorite: Boolean,
    val isPending: Boolean,
    val tagFlags: Int,
    val isInvalid: Boolean,
    val bucketName: String = TextUtil.EMPTY_STRING,
    val mimeType: String,
    val mediaType: Int,
    val cShotId: Long,
    val dateTaken: Long = 0L,
    val dateModified: Long = 0L,
    val orientation: Int = 0,
    val mediaId: Int = 0,
    val localId: Int = 0,
    val codecType: String,
    val extTagFlags: Int = 0,
    val title: String
) {
    val isVideo by lazy { mediaType == MEDIA_TYPE_VIDEO }
    val isDolbyVideo by lazy { MimeTypeUtils.isDolbyVideo(codecType) }
    val isFastVideo by lazy { isTargetFlag(tagFlags, EXIF_TAG_FAST_VIDEO) }
    val isSlowMotion by lazy { title.contains(SLOW_MOTION_TITLE_PREFIX) }
    val isLogVideo by lazy { title.contains(SLOG_VIDEO_TITLE_PREFIX) }
    val isEnhanceText by lazy { (mediaType == MEDIA_TYPE_IMAGE) && isTargetFlag(tagFlags, EXIF_TAG_ENHANCE_TEXT) }
    val isOlivePhoto by lazy { (mediaType == MEDIA_TYPE_IMAGE) && isTargetFlag(tagFlags, EXIF_TAG_OLIVE) }
    val isGif by lazy { MimeTypeUtils.isGif(mimeType) }
    val isPortraitBlur by lazy { isTargetFlag(tagFlags, EXIF_TAG_PORTRAIT_BLUR) && isTargetFlag(extTagFlags, EXT_SHOT_BY_SAME_DEVICE_PHOTO) }
    val isRaw by lazy {
        (MimeTypeUtils.MIME_TYPE_IMAGE_RAW.equals(mimeType, true))
            || (data.endsWith(".${MimeTypeUtils.MIME_TYPE_IMAGE_DNG_SUFFIX}", true))
    }
    val isCShot by lazy { DatabaseUtils.isCShotIdValid(cShotId) }
    val isPanorama by lazy { isTargetFlag(tagFlags, EXIF_TAG_PANORAMA) }
    val isJpeg by lazy { MimeTypeUtils.isJpeg(mimeType) }
    val isHeif by lazy { MimeTypeUtils.isHeifOrHeic(mimeType) }


    fun createCoverItem(): CoverItem {
        return CoverItem(localId, data, dateModified, dateTaken)
    }

    companion object {
        fun ContentValues.createRowMediaItem(): RowMediaItem {
            return RowMediaItem(
                getAsString(OperationRowFieldKey.KEY_DATA) ?: TextUtil.EMPTY_STRING,
                getAsInteger(OperationRowFieldKey.KEY_BUCKET_ID) ?: 0,
                getAsString(OperationRowFieldKey.KEY_BUCKET_PATH) ?: TextUtil.EMPTY_STRING,
                IS_TRASHED_TRUE == getAsInteger(OperationRowFieldKey.KEY_IS_TRASHED),
                GalleryStore.GalleryColumns.LocalColumns.IS_FAVORITE_TRUE == getAsInteger(OperationRowFieldKey.KEY_IS_FAVORITE),
                GalleryStore.GalleryColumns.LocalColumns.IS_PENDING_TRUE == getAsInteger(OperationRowFieldKey.KEY_IS_PENDING),
                getAsInteger(OperationRowFieldKey.KEY_TAG_FLAGS) ?: 0,
                DatabaseUtils.isDataValid(getAsInteger(OperationRowFieldKey.KEY_INVALID) ?: 0).not(),
                getAsString(OperationRowFieldKey.KEY_BUCKET_NAME) ?: TextUtil.EMPTY_STRING,
                getAsString(OperationRowFieldKey.KEY_MIME_TYPE) ?: TextUtil.EMPTY_STRING,
                getAsInteger(OperationRowFieldKey.KEY_MEDIA_TYPE) ?: 0,
                getAsLong(OperationRowFieldKey.KEY_CSHOT_ID) ?: 0L,
                getAsLong(OperationRowFieldKey.KEY_DATE_TAKEN) ?: 0L,
                getAsLong(OperationRowFieldKey.KEY_DATE_MODIFIED) ?: 0L,
                getAsInteger(OperationRowFieldKey.KEY_ORIENTATION) ?: 0,
                getAsInteger(OperationRowFieldKey.KEY_MEDIA_ID) ?: 0,
                getAsInteger(OperationRowFieldKey.KEY_LOCAL_ID) ?: 0,
                getAsString(OperationRowFieldKey.KEY_CODEC_TYPE) ?: TextUtil.EMPTY_STRING,
                getAsInteger(OperationRowFieldKey.KEY_EXT_TAG_FLAGS) ?: 0,
                getAsString(OperationRowFieldKey.KEY_TITLE) ?: TextUtil.EMPTY_STRING
            )
        }
    }
}

data class CShotAlbum(
    @SerializedName("cShotId") val cShotId: Long,
    @SerializedName("bucketId") val bucketId: Int,
    /**此次操作类型*/
    @SerializedName("operationType") var operationType: OperationType,
    /**此次操作更新记录：first:insert/delete/update,second:update*/
    @SerializedName("rowContent") var rowContent: Pair<ContentValues, ContentValues?>? = null
) : Serializable {
    /**连拍图总数*/
    @SerializedName("totalCount")
    var totalCount: Int = 0
        set(value) {
            field = ensureCountPositive(value)
        }

    /**更新数量:当达到count数量时,则统计*/
    @SerializedName("updateCount")
    var updateCount: Int = 0
        set(value) {
            field = ensureCountPositive(value)
        }

    /**本地数量:统计非云端缩图*/
    @SerializedName("localCount")
    var localCount: Int = 0
        set(value) {
            field = ensureCountPositive(value)
        }

    private fun ensureCountPositive(value: Int): Int {
        return if (value < 0) 0 else value
    }

    companion object {
        private const val serialVersionUID = 1L
    }
}