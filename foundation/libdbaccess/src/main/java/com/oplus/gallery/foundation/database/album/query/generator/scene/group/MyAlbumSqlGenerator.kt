/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: -
 ** Description: 我的图集的sqlGenerator
 **
 ** Version: 1.0
 ** Date: 2025/3/26
 ** Author: 80407954@OppoGallery3D
 ** TAG: xx
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  80407954@OppoGallery3D  2025/3/26  1.0        我的图集的sqlGenerator
 *********************************************************************************/
package com.oplus.gallery.foundation.database.album.query.generator.scene.group

import com.oplus.gallery.foundation.database.album.query.generator.scene.ISceneSqlGenerator
import com.oplus.gallery.foundation.database.album.query.generator.scene.condition.myalbums.QueryMyAlbumAllLocalSqlGenerator
import com.oplus.gallery.foundation.database.album.query.generator.scene.condition.myalbums.QueryMyAlbumAllSqlGenerator
import com.oplus.gallery.foundation.database.album.query.generator.scene.condition.myalbums.QueryMyAlbumOnlyImageNotGifSqlGenerator
import com.oplus.gallery.foundation.database.album.query.generator.scene.condition.myalbums.QueryMyAlbumOnlyImageSqlGenerator
import com.oplus.gallery.foundation.database.album.query.generator.scene.condition.myalbums.QueryMyAlbumOnlyJpegAndHeifSqlGenerator
import com.oplus.gallery.foundation.database.album.query.generator.scene.condition.myalbums.QueryMyAlbumOnlyJpegSqlGenerator
import com.oplus.gallery.foundation.database.album.query.generator.scene.condition.myalbums.QueryMyAlbumOnlyLocalImageSqlGenerator
import com.oplus.gallery.foundation.database.album.query.generator.scene.condition.myalbums.QueryMyAlbumOnlyLocalVideoSqlGenerator
import com.oplus.gallery.foundation.database.album.query.generator.scene.condition.myalbums.QueryMyAlbumOnlyRealAlbumSqlGenerator
import com.oplus.gallery.foundation.database.helper.Constants.Album.AlbumSetSceneCondition.SUPPORT_ALL_MEDIA
import com.oplus.gallery.foundation.database.helper.Constants.Album.AlbumSetSceneCondition.SUPPORT_LOCAL_ALL_MEDIA
import com.oplus.gallery.foundation.database.helper.Constants.Album.AlbumSetSceneCondition.SUPPORT_ONLY_IMAGE
import com.oplus.gallery.foundation.database.helper.Constants.Album.AlbumSetSceneCondition.SUPPORT_ONLY_IMAGE_EXCLUDE_GIF
import com.oplus.gallery.foundation.database.helper.Constants.Album.AlbumSetSceneCondition.SUPPORT_ONLY_JPEG
import com.oplus.gallery.foundation.database.helper.Constants.Album.AlbumSetSceneCondition.SUPPORT_ONLY_JPEG_AND_HEIF
import com.oplus.gallery.foundation.database.helper.Constants.Album.AlbumSetSceneCondition.SUPPORT_ONLY_LOCAL_IMAGE
import com.oplus.gallery.foundation.database.helper.Constants.Album.AlbumSetSceneCondition.SUPPORT_ONLY_LOCAL_VIDEO
import com.oplus.gallery.foundation.database.helper.Constants.Album.AlbumSetSceneCondition.SUPPORT_ONLY_REAL_ALBUM

/**
 * 我的图集列表页的sql语句生成器
 */
class MyAlbumSqlGenerator : BaseAlbumSetSqlGenerator() {

    override val tag: String = TAG

    override fun getSceneSqlGenerator(sceneConditionType: Int): ISceneSqlGenerator? {
        return sceneSqlGeneratorMap[sceneConditionType]
    }

    companion object {
        private val sceneSqlGeneratorMap by lazy {
            mutableMapOf<Int, ISceneSqlGenerator>().apply {
                this[SUPPORT_ALL_MEDIA] = QueryMyAlbumAllSqlGenerator()
                this[SUPPORT_LOCAL_ALL_MEDIA] = QueryMyAlbumAllLocalSqlGenerator()
                this[SUPPORT_ONLY_IMAGE] = QueryMyAlbumOnlyImageSqlGenerator()
                this[SUPPORT_ONLY_LOCAL_IMAGE] = QueryMyAlbumOnlyLocalImageSqlGenerator()
                this[SUPPORT_ONLY_LOCAL_VIDEO] = QueryMyAlbumOnlyLocalVideoSqlGenerator()
                this[SUPPORT_ONLY_IMAGE_EXCLUDE_GIF] = QueryMyAlbumOnlyImageNotGifSqlGenerator()
                this[SUPPORT_ONLY_JPEG] = QueryMyAlbumOnlyJpegSqlGenerator()
                this[SUPPORT_ONLY_JPEG_AND_HEIF] = QueryMyAlbumOnlyJpegAndHeifSqlGenerator()
                this[SUPPORT_ONLY_REAL_ALBUM] = QueryMyAlbumOnlyRealAlbumSqlGenerator()
            }
        }
        private const val TAG = "MyAlbumSqlGenerator"
    }
}