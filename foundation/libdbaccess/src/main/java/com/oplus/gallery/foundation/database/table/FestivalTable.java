package com.oplus.gallery.foundation.database.table;

import android.database.sqlite.SQLiteDatabase;

import com.oplus.gallery.foundation.database.store.GalleryStore;
import com.oplus.gallery.foundation.database.util.DatabaseUtils;
import com.oplus.gallery.foundation.util.debug.GLog;

import static com.oplus.gallery.foundation.database.provider.GalleryProvider.VERSION_1319;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.CREATE_INDEX;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.CREATE_TABLE;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.DEFAULT_ZERO;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.INDEX_ON;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.INTEGER;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.PRIMARY_KEY;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.TEXT;

public class FestivalTable implements BaseTable {
    private static final String TAG = DatabaseUtils.DATA_TAG + "FestivalTable";

    private String getTableName() {
        return GalleryStore.Festival.TAB;
    }

    /*
     * festival table
     */
    private String getCreateTableSql() {
        return CREATE_TABLE
                + GalleryStore.Festival.TAB
                + " ("
                + GalleryStore.FestivalColumns._ID + INTEGER + PRIMARY_KEY + ","
                + GalleryStore.FestivalColumns.IS_FORCE + INTEGER + ","
                + GalleryStore.FestivalColumns.DATE + TEXT + ","
                + GalleryStore.FestivalColumns.FESTIVAL_NAME + TEXT + ","
                + GalleryStore.FestivalColumns.LEVEL + INTEGER + ","
                + GalleryStore.FestivalColumns.FESTIVAL_NICK_NAME + TEXT + ","
                + GalleryStore.FestivalColumns.TYPE + INTEGER + ","
                + GalleryStore.FestivalColumns.COUNTRY_NAME + TEXT + ","
                + GalleryStore.FestivalColumns.COUNTRY_NICK_NAME + TEXT + ","
                + GalleryStore.FestivalColumns.REGION + TEXT + ","
                + GalleryStore.FestivalColumns.FILTER_TYPE + INTEGER + DEFAULT_ZERO
                + ");";
    }

    private void updateIndex(SQLiteDatabase db) {
        // date
        db.execSQL(CREATE_INDEX + GalleryStore.Festival.TAB + "_" + GalleryStore.FestivalColumns.DATE
                + INDEX_ON + GalleryStore.Festival.TAB + "(" + GalleryStore.FestivalColumns.DATE + ")");
        // region
        db.execSQL(CREATE_INDEX + GalleryStore.Festival.TAB + "_" + GalleryStore.FestivalColumns.REGION
                + INDEX_ON + GalleryStore.Festival.TAB + "(" + GalleryStore.FestivalColumns.REGION + ")");
    }

    @Override
    public void updateDatabase(SQLiteDatabase db, int oldVersion, int newVersion) {
        GLog.d(TAG, "updateDatabase oldVersion: " + oldVersion + ", newVersion: " + newVersion);
        db.execSQL(getCreateTableSql());
        updateIndex(db);
        if (oldVersion < VERSION_1319) {
            addFilterType(db);
        }
    }

    private static void addFilterType(SQLiteDatabase db) {
        // 在数据库的创建语句中加入了FILTER_TYPE字段，这里再添加会失败，预期内，不抛出异常
        DatabaseUtils.execSQLSafe(db, "ALTER TABLE " + GalleryStore.Festival.TAB + " ADD COLUMN "
                + GalleryStore.Festival.FILTER_TYPE + INTEGER + DEFAULT_ZERO, true);
    }

    @Override
    public void downDatabase(SQLiteDatabase db, int oldVersion, int newVersion) {
        GLog.w(TAG, "downDatabase oldVersion: " + oldVersion + ", newVersion: " + newVersion);
        // 数据库降级，已全部在provider處理
        // db.execSQL(DROP_TABLE + getTableName());
    }
}