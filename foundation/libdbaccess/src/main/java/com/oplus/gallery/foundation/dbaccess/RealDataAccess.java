/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - RealDataAccess.java
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/05/22
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** h<PERSON><PERSON><PERSON>@Apps.Gallery		2020/02/24		1.0		OPLUS_ARCH_EXTENDS
 *********************************************************************************/

package com.oplus.gallery.foundation.dbaccess;

import android.content.Context;
import android.net.Uri;

import androidx.annotation.Nullable;

import com.oplus.gallery.foundation.dbaccess.bean.BatchResult;
import com.oplus.gallery.foundation.dbaccess.dao.CacheDbDao;
import com.oplus.gallery.foundation.dbaccess.dao.CloudDbDao;
import com.oplus.gallery.foundation.dbaccess.dao.ConfigDbDao;
import com.oplus.gallery.foundation.dbaccess.dao.DaoObserver;
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao;
import com.oplus.gallery.foundation.dbaccess.dao.IDao;
import com.oplus.gallery.foundation.dbaccess.dao.MediaStoreDbDao;
import com.oplus.gallery.foundation.dbaccess.dao.WatermarkMasterDbDao;
import com.oplus.gallery.foundation.dbaccess.req.BatchReq;
import com.oplus.gallery.foundation.dbaccess.req.BulkInsertReq;
import com.oplus.gallery.foundation.dbaccess.req.DeleteReq;
import com.oplus.gallery.foundation.dbaccess.req.InsertReq;
import com.oplus.gallery.foundation.dbaccess.req.QueryReq;
import com.oplus.gallery.foundation.dbaccess.req.RawQueryReq;
import com.oplus.gallery.foundation.dbaccess.req.UpdateReq;
import com.oplus.gallery.foundation.dbaccess.util.DbAccessCheck;

public class RealDataAccess implements IDataAccess {
    private final IDao[] mDaos;

    RealDataAccess(Context context) {
        mDaos = new IDao[]{
                new GalleryDbDao(context),
                new CloudDbDao(context),
                new ConfigDbDao(context),
                new MediaStoreDbDao(context),
                new CacheDbDao(context),
                new WatermarkMasterDbDao(context)
        };
    }

    @Override
    public int bulkInsert(BulkInsertReq req) {
        return getDao(req.getDaoType()).bulkInsert(req);
    }

    @Override
    public BatchResult[] applyBatch(BatchReq req) {
        return getDao(req.getDaoType()).applyBatch(req);
    }

    @Override
    public Uri insert(InsertReq req) {
        return getDao(req.getDaoType()).insert(req);
    }

    @Override
    public int delete(DeleteReq req) {
        return getDao(req.getDaoType()).delete(req);
    }

    @Override
    public int update(UpdateReq req) {
        return getDao(req.getDaoType()).update(req);
    }

    @Override
    public <Result> Result query(QueryReq<Result> req) {
        return getDao(req.getDaoType()).query(req);
    }

    @Override
    public <Result> Result rawQuery(RawQueryReq<Result> req) {
        return getDao(req.getDaoType()).rawQuery(req);
    }

    @Override
    public void registerNotify(@Nullable String volumeName, @IDao.DaoType int dbType, int tableType, boolean notifyForDescendants,
                               DaoObserver observer) {
        getDao(dbType).registerNotify(volumeName, tableType, notifyForDescendants, observer);
    }

    @Override
    public void unregisterNotify(@IDao.DaoType int dbType, DaoObserver observer) {
        getDao(dbType).unregisterNotify(observer);
    }

    private IDao getDao(@IDao.DaoType int dbType) {
        DbAccessCheck.checkArgumentInRange(dbType, 0, mDaos.length - 1, "mDbType");
        return mDaos[dbType];
    }

}
