/*********************************************************************************
 ** Copyright (C), 2022-2032, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : HomeRecentListener
 ** Description : Home按键、最近任务按键监听器
 ** Version     : 1.0
 ** Date        : 2023/3/1
 ** Author      : <EMAIL>
 ** TAG         :
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                         <date>         <version>         <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>      2023/3/1       1.0               new file
 *********************************************************************************/

package com.oplus.gallery.foundation.uikit.broadcast.bus

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.ext.runOnWorkThread
import com.oplus.gallery.standard_lib.app.SINGLE_UN_BUSY
import kotlinx.coroutines.Dispatchers

/**
 * Home按键、最近任务按键监听器
 * @param reasons 关闭原因[REASON_HOME_KEY], [REASON_RECENT_APPS]
 * @param callback 监听到符合原因后的，回调处理
 */
class HomeRecentListener(
    private val reasons: Array<String>,
    private val callback: (String) -> Unit
) {
    companion object {
        private const val TAG = "HomeRecentListener"
        private const val SYSTEM_DIALOG_REASON_KEY = "reason"

        /**
         * 监听事件：home按键
         */
        const val REASON_HOME_KEY = "homekey"

        /**
         * 监听事件：最近任务
         */
        const val REASON_RECENT_APPS = "recentapps"
    }

    private val receiver by lazy {
        object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                val reason = intent?.getStringExtra(SYSTEM_DIALOG_REASON_KEY)
                GLog.d(TAG) { "onReceive:reason = $reason ,support reasons = $reasons" }
                if (reason != null && reasons.contains(reason)) {
                    callback.invoke(reason)
                }
            }
        }
    }

    /**
     * 注册监听器
     * @param context 上下文
     */
    fun register(context: Context) {
        GLog.d(TAG) { "register:" }
        /*
         ACTION_CLOSE_SYSTEM_DIALOGS虽然从S开始该广播被Deprecated了，但其限制的是三方应用通过发送该广播，关闭System Dialog，
         而接收广播并没有限制，详细描述可参考如下链接
         https://developer.android.com/about/versions/12/behavior-changes-all#close-system-dialogs
         */
        runOnWorkThread(coroutineContext = Dispatchers.SINGLE_UN_BUSY) {
            GTrace.trace("$TAG - register") {
                BroadcastDispatcher.registerExportedReceiver(context, receiver, IntentFilter(Intent.ACTION_CLOSE_SYSTEM_DIALOGS))
            }
        }
    }

    /**
     * 注销监听器
     */
    fun unregister(context: Context) {
        GLog.d(TAG, "unRegister: ")
        runOnWorkThread(coroutineContext = Dispatchers.SINGLE_UN_BUSY) {
            GTrace.trace("$TAG - unregister") {
                BroadcastDispatcher.unregisterReceiver(context, receiver)
            }
        }
    }
}