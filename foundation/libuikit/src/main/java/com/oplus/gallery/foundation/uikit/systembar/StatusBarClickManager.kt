/***********************************************************
 * * Copyright (C), 2024-2034, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - StatusClickManager.java
 * * Description: 状态栏点击监听的管理器
 * * Version: 1.0
 * * Date : 2024/04/26
 * * Author: houdonggu@Apps.Gallery3D
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	                      <data> 	  <version >	   <desc>
 * *  houdonggu@Apps.Gallery3D       2024/04/26    1.0       build this module
 ****************************************************************/

package com.oplus.gallery.foundation.uikit.systembar

import android.annotation.SuppressLint
import android.os.Handler
import android.os.HandlerThread
import androidx.annotation.UiThread
import com.coui.appcompat.statusbar.COUIStatusBarResponseUtil
import com.coui.appcompat.statusbar.COUIStatusBarResponseUtil.StatusBarClickListener
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.ui.util.AsyncObj
import com.oplus.gallery.standard_lib.util.os.ContextGetter

/**
 * 状态栏点击监听的管理器
 *
 * 单例去系统注册广播监听即可
 * 不同页面注册或反注册到这个单例，避免多个页面同时注册到系统中
 */
object StatusBarClickManager {
    private const val TAG_HANDLER_ASYNC = "handlerAsync"
    private const val TAG = "StatusClickManager"
    private val listenerList = mutableListOf<StatusBarClickListener>()
    private val clickListener = StatusBarClickListener {
        listenerList.forEach {
            it.onStatusBarClicked()
        }
    }

    // 以runnable运行，确保跑在子线程
    private val registerStatusBarResponseAction by lazy {
        Runnable {
            runCatching {
                GLog.d(TAG) { "registerStatusBarResponseAction before onResume utils:$statusBarResponseUtil size:${listenerList.size}" }
                statusBarResponseUtil?.onResume()
            }.onFailure {
                GLog.e(TAG, "registerStatusBarResponseAction", it)
            }
        }
    }

    // 由于状态栏的sdk{@link COUIStatusBarResponseUtil}中需要looper，只能在HandlerThread异步调用相关接口
    private val statusBarHandlerAsyncObj: AsyncObj<Handler> by lazy {
        AsyncObj(AppScope, TAG_HANDLER_ASYNC) {
            Handler(HandlerThread(TAG_HANDLER_ASYNC).apply { start() }.looper)
        }
    }

    @SuppressLint("StaticFieldLeak")
    private var statusBarResponseUtil: COUIStatusBarResponseUtil? = null

    fun init() {
        if (statusBarResponseUtil == null) {
            statusBarResponseUtil = COUIStatusBarResponseUtil(ContextGetter.context).apply {
                setStatusBarClickListener(clickListener)
            }
            statusBarHandlerAsyncObj.getIt {
                if (it.hasCallbacks(registerStatusBarResponseAction).not()) {
                    it.post(registerStatusBarResponseAction)
                }
            }
        }
    }

    /**
     * 注册监听
     */
    @UiThread
    fun registerListener(listener: StatusBarClickListener) {
        if (listenerList.contains(listener).not()) {
            listenerList.add(listener)
        }
    }

    /**
     * 反注册监听
     */
    @UiThread
    fun unRegisterListener(listener: StatusBarClickListener) {
        if (listenerList.contains(listener)) {
            listenerList.remove(listener)
        }
    }
}