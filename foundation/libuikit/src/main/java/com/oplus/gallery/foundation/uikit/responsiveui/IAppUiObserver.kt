/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : IAppUiBridge.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2021/07/01 10:21
 ** Author      : z<PERSON><PERSON><PERSON>@Apps.Gallery
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** z<PERSON><PERSON><PERSON>@Apps.Gallery     2021/07/01      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.foundation.uikit.responsiveui

interface IAppUiObserver {
    fun onAppUiStateChanged(uiConfig: AppUiResponder.AppUiConfig)
}