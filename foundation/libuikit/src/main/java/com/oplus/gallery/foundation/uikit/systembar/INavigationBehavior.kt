/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - INavigationBehavior.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2021/11/18
 ** Author: WUDANYANG
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** WUDANYANG  2021/11/18		1.0		INavigationBehavior
 *********************************************************************************/
package com.oplus.gallery.foundation.uikit.systembar

import android.content.Context

interface INavigationBehavior {

    /**
     * 获取当前导航栏高度，无论当前导航栏是否显示
     * 可传入导航栏模式，没有传入会获取当前导航栏模式
     */
    fun getNavigationBarHeight(context: Context, navigationMode: NavigationMode = getNavigationMode()): Int

    /**
     * 当前导航栏是否显示
     */
    fun isNavigationBarShowing(context: Context): Boolean

    /**
     * 导航栏模式，虚拟按键或手势条或其它
     */
    fun getNavigationMode(): NavigationMode

    /**
     * 获取displayId
     */
    fun getDisplayId(context: Context): Int?
}

abstract class BaseNavigationBehavior : INavigationBehavior {

    /**
     * 系统导航栏相关数据字典，注意在不同版本上的参数存在差异
     */
    companion object {

        /**
         * 是否显示导航栏
         */
        const val KEY_MANUAL_HIDE_NAVIGATION_BAR = "manual_hide_navigationbar"

        // 0表示显示
        const val SHOW_NAVIGATION_BAR = 0

        /**
         * Q&R版本
         * 可以通过读Secure数据库的值得到当前导航方式：
         * 0 = 虚拟按键
         * 1 = 虚拟按键且隐藏
         * 2 = 上滑手势
         * 3 = 两侧滑动手势
         */
        const val KEY_NAV_STATE = "hide_navigationbar_enable"
        const val NAV_STATE_VIRTUAL_KEY = 0
        const val NAV_STATE_VIRTUAL_KEY_AND_HIDE = 1
        const val NAV_STATE_SWIPE_UP_GESTURE = 2
        const val NAV_STATE_SWIPE_SIDE_GESTURE = 3


        /**
         * S版本 回归原生
         * Google原生用存储在Settings.Secure中的"navigation_mode"区分导航方式：
         * Navigation bar mode.
         * 0 = 3 button
         * 1 = 2 button P上有的两键式的导航方式
         * 2 = fully gestural
         */
        const val NAVIGATION_MODE = "navigation_mode"
        const val NAVIGATION_MODE_3_BUTTONS = 0
        const val NAVIGATION_MODE_2_BUTTONS = 1
        const val NAVIGATION_MODE_GESTURAL = 2
    }
}

/**
 * 导航栏方向（主要是虚拟按键模式下，切换横竖屏，导航栏出现方向可能出现在屏幕两侧）
 */
enum class NavigationBarDirection {
    UNKNOWN,
    LEFT,
    TOP,
    RIGHT,
    BOTTOM
}

/**
 * 自定义的导航栏模式参数，用于各android版本不同参数做最终的统一输出，目前分为两种模式：虚拟按键和手势条
 * 新增方法注意命名，虚拟按键为VirtualKey，手势条Gesture，避免跟navigationBar混在一起
 */
enum class NavigationMode {
    VIRTUAL_KEY,
    GESTURE
}