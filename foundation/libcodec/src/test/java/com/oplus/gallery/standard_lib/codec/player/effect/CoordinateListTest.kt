package com.oplus.gallery.standard_lib.codec.player.effect

import com.oplus.gallery.standard_lib.codec.player.adapter.PlayerAdapter
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkClass
import io.mockk.verify
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class CoordinateListTest {

    private lateinit var coordinateList: CoordinateList<String, PlayerAdapter>
    private lateinit var mockFactory: CoordinateList.CoordinatorFactory<PlayerAdapter>
    private lateinit var mockPlayerAdapter: PlayerAdapter

    @Before
    fun setUp() {
        coordinateList = CoordinateList()
        mockPlayerAdapter = mockkClass(PlayerAdapter::class)
        mockFactory = mockk<CoordinateList.CoordinatorFactory<PlayerAdapter>>()

        // 设置默认的factory行为
        every { mockFactory.createCoordinator() } returns mockPlayerAdapter
    }

    @Test
    fun testInitialState() {
        Assert.assertTrue("New list should be empty", coordinateList.isEmpty())
        Assert.assertEquals("Size should be 0", 0, coordinateList.size)
    }

    @Test
    fun testAddElementWithFactory() {
        // 设置factory
        coordinateList.coordinate("key1", mockFactory)

        // 添加元素
        val result = coordinateList.add("key1")

        Assert.assertTrue("Add should return true", result)
        Assert.assertEquals("Size should be 1", 1, coordinateList.size)
        Assert.assertTrue("List should contain the key", coordinateList.contains("key1"))

        // 验证factory被调用
        verify { mockFactory.createCoordinator() }
    }

    @Test
    fun testAddDuplicateElement() {
        coordinateList.coordinate("key1", mockFactory)
        coordinateList.add("key1")

        // 尝试添加重复元素
        val result = coordinateList.add("key1")

        Assert.assertFalse("Add duplicate should return false", result)
        Assert.assertEquals("Size should still be 1", 1, coordinateList.size)
    }

    @Test
    fun testAddElementWithoutFactory() {
        // 不设置factory直接添加
        val result = coordinateList.add("key1")

        Assert.assertFalse("Add without factory should return false", result)
        Assert.assertTrue("List should still be empty", coordinateList.isEmpty())
    }

    @Test
    fun testAddAtIndex() {
        coordinateList.coordinate("key1", mockFactory)
        coordinateList.coordinate("key2", mockFactory)
        coordinateList.add("key1")

        // 在索引0处添加key2
        coordinateList.add(0, "key2")

        Assert.assertEquals("First element should be key2", "key2", coordinateList[0])
        Assert.assertEquals("Second element should be key1", "key1", coordinateList[1])
        Assert.assertEquals("Size should be 2", 2, coordinateList.size)
    }

    @Test
    fun testAddAllCollection() {
        coordinateList.coordinate("key1", mockFactory)
        coordinateList.coordinate("key2", mockFactory)

        val result = coordinateList.addAll(listOf("key1", "key2"))

        Assert.assertTrue("AddAll should return true", result)
        Assert.assertEquals("Size should be 2", 2, coordinateList.size)
        Assert.assertTrue(
            "List should contain both keys",
            coordinateList.containsAll(listOf("key1", "key2"))
        )
    }

    @Test
    fun testAddAllAtIndex() {
        coordinateList.coordinate("key1", mockFactory)
        coordinateList.coordinate("key2", mockFactory)
        coordinateList.coordinate("key3", mockFactory)
        coordinateList.add("key1")

        val result = coordinateList.addAll(0, listOf("key2", "key3"))

        Assert.assertTrue("AddAll at index should return true", result)
        Assert.assertEquals("Size should be 3", 3, coordinateList.size)
        Assert.assertEquals("First element should be key2", "key2", coordinateList[0])
    }

    @Test
    fun testRemoveElement() {
        coordinateList.coordinate("key1", mockFactory)
        coordinateList.add("key1")

        val result = coordinateList.remove("key1")

        Assert.assertTrue("Remove should return true", result)
        Assert.assertTrue("List should be empty", coordinateList.isEmpty())
    }

    @Test
    fun testRemoveNonExistentElement() {
        coordinateList.coordinate("key1", mockFactory)
        coordinateList.add("key1")

        val result = coordinateList.remove("key2")

        Assert.assertFalse("Remove non-existent should return false", result)
        Assert.assertEquals("Size should still be 1", 1, coordinateList.size)
    }

    @Test
    fun testClearList() {
        coordinateList.coordinate("key1", mockFactory)
        coordinateList.coordinate("key2", mockFactory)
        coordinateList.add("key1")
        coordinateList.add("key2")

        coordinateList.clear()

        Assert.assertTrue("List should be empty after clear", coordinateList.isEmpty())
    }

    @Test
    fun testMuteKeyPreventsAddition() {
        coordinateList.coordinate("key1", mockFactory)
        coordinateList.mute("key1")

        val result = coordinateList.add("key1")

        Assert.assertFalse("Add muted key should return false", result)
        Assert.assertTrue("List should be empty", coordinateList.isEmpty())
    }

    @Test
    fun testIteratorRemove() {
        coordinateList.coordinate("key1", mockFactory)
        coordinateList.coordinate("key2", mockFactory)
        coordinateList.add("key1")
        coordinateList.add("key2")

        val iterator = coordinateList.iterator()
        while (iterator.hasNext()) {
            if (iterator.next() == "key1") {
                iterator.remove()
            }
        }

        Assert.assertEquals("Size should be 1 after iterator remove", 1, coordinateList.size)
        Assert.assertFalse("List should not contain removed key", coordinateList.contains("key1"))
    }

    @Test(expected = IndexOutOfBoundsException::class)
    fun testGetWithInvalidIndex() {
        coordinateList.get(0)
    }

    @Test
    fun testIndexOf() {
        coordinateList.coordinate("key1", mockFactory)
        coordinateList.add("key1")

        val index = coordinateList.indexOf("key1")
        Assert.assertEquals("Index should be 0", 0, index)
    }

    @Test
    fun testLastIndexOf() {
        coordinateList.coordinate("key1", mockFactory)
        coordinateList.add("key1")
        coordinateList.add("key1") // 添加重复元素

        val index = coordinateList.lastIndexOf("key1")
        Assert.assertEquals("Last index should be 0", 0, index)
    }

    @Test
    fun testRetainAll() {
        coordinateList.coordinate("key1", mockFactory)
        coordinateList.coordinate("key2", mockFactory)
        coordinateList.add("key1")
        coordinateList.add("key2")

        val result = coordinateList.retainAll(listOf("key1"))

        Assert.assertTrue("RetainAll should return true", result)
        Assert.assertEquals("Size should be 1", 1, coordinateList.size)
        Assert.assertTrue("List should contain key1", coordinateList.contains("key1"))
    }

    @Test
    fun testRemoveAll() {
        coordinateList.coordinate("key1", mockFactory)
        coordinateList.coordinate("key2", mockFactory)
        coordinateList.add("key1")
        coordinateList.add("key2")

        val result = coordinateList.removeAll(listOf("key1"))

        Assert.assertTrue("RemoveAll should return true", result)
        Assert.assertEquals("Size should be 1", 1, coordinateList.size)
        Assert.assertFalse("List should not contain removed key", coordinateList.contains("key1"))
    }

    @Test
    fun testSetElement() {
        coordinateList.coordinate("key1", mockFactory)
        coordinateList.coordinate("key2", mockFactory)
        coordinateList.add("key1")

        val previous = coordinateList.set(0, "key2")

        Assert.assertEquals("Previous element should be key1", "key1", previous)
        Assert.assertEquals("New element should be key2", "key2", coordinateList[0])
    }

    @Test
    fun testToString() {
        coordinateList.coordinate("key1", mockFactory)
        coordinateList.add("key1")

        val result = coordinateList.toString()
        Assert.assertTrue("String should contain the key", result.contains("key1"))
    }
}