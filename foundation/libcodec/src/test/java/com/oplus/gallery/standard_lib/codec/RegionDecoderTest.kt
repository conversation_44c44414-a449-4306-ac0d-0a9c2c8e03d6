/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - RegionDecoderTest.kt
 * Description:
 * Version:
 * Date: 2022/5/1
 * Author: duchengsong@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * duchengsong@Apps.Gallery3D      2022/5/1     1.0     OPLUS_FEATURE_APP_LOG_MONITOR
</desc></version></date></author> */
package com.oplus.gallery.standard_lib.codec

import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import org.junit.Assert
import org.junit.Test

class RegionDecoderTest {

    @Test
    fun should_return_false_when_IsSupportMultiRegionDecode_with_empty_mimeType() {
        // given & when
        val ans1 = RegionDecoder.isSupportMultiRegionDecode(null)
        val ans2 = RegionDecoder.isSupportMultiRegionDecode("")

        // then
        Assert.assertTrue(ans1)
        Assert.assertTrue(ans2)
    }

    @Test
    fun should_return_true_when_IsSupportMultiRegionDecode_with_heif_mimeType() {
        // given & when
        val ans1 = RegionDecoder.isSupportMultiRegionDecode(MimeTypeUtils.MIME_TYPE_IMAGE_HEIC)
        val ans2 = RegionDecoder.isSupportMultiRegionDecode(MimeTypeUtils.MIME_TYPE_IMAGE_HEIF)

        // then
        Assert.assertFalse(ans1)
        Assert.assertFalse(ans2)
    }

    @Test
    fun should_return_false_when_IsSupportMultiRegionDecode_with_not_heif_mimeType() {
        // given & when
        val ans1 = RegionDecoder.isSupportMultiRegionDecode(MimeTypeUtils.MIME_TYPE_IMAGE_JPEG)
        val ans2 = RegionDecoder.isSupportMultiRegionDecode(MimeTypeUtils.MIME_TYPE_IMAGE_PNG)
        val ans3 = RegionDecoder.isSupportMultiRegionDecode(MimeTypeUtils.MIME_TYPE_IMAGE_WEBP)

        // then
        Assert.assertTrue(ans1)
        Assert.assertTrue(ans2)
        Assert.assertTrue(ans3)
    }
}