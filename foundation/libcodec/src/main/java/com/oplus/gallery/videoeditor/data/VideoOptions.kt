/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 * File: - Options.kt
 * Description:
 * Version: 1.0
 * Date: 2025/08/04
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>    <desc>
 * ------------------------------------------------------------------------------
 * huca<PERSON><PERSON>@Apps.Gallery        2025/08/04      1.0        OPLUS_ARCH_EXTENDS
 ********************************************************************************/

package com.oplus.gallery.videoeditor.data

/**
 * @param mimeType 视频文件媒体类型
 * @param isEncoder 是否查询编码器规格，为false时查询解码器规格
 * @param isDesireHardwareAccelerated 是否要求此编解码器支持硬件加速，true则会过滤掉不支持硬件加速的编解码器，false则不做处理
 */
data class VideoOptions(
    var mimeType: String,
    var isEncoder: Boolean = false,
    var isDesireHardwareAccelerated: Boolean = false
)
