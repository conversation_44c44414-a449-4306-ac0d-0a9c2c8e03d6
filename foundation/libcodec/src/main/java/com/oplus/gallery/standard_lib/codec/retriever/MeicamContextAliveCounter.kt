/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MeicamContextAliveCounter.kt
 ** Description:美摄sdk上下文存活时间的管理类
 ** Version: 1.0
 ** Date : 2022/12/8
 ** Author: xie<PERSON><PERSON><PERSON>@Apps.Gallery3D
 ** TAG: MeicamContextAliveCounter
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  x<PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D      2022/12/08    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.standard_lib.codec.retriever

import android.content.Context
import com.meicam.sdk.NvsStreamingContext
import com.meicam.sdk.NvsStreamingContext.STREAMING_ENGINE_INTERRUPT_STOP
import com.oplus.gallery.foundation.util.brandconfig.AppBrandConstants
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.app.MeicamEdit
import com.oplus.gallery.videoeditor.engine.meicam.MeicamContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 美摄sdk上下文存活时间的计数类，采用计数的形式来决定是否回收此上下文
 *
 * Mark by：谢武杰
 * 即将设计成: 美摄取帧模块和美摄sdk解耦,可以单独使用取帧,就不需要在取帧的时候还要考虑NvsSteamContext的创建和关闭
 * 现状原因:美摄还不支持两者解耦，需要采用计数的形式来维护NvsSteamContext的创建和关闭
 * 解决计划:等待商务推动美摄完成模块解耦后再接入
 */
object MeicamContextAliveCounter {

    private const val TAG = "MeicamContextAliveCounter"
    private val DEFAULT_FLAG = MeicamContext.NvsContextFlagBuilder()
        .minimumMemoryUsage()
        .support4K()
        .support8K()
        .build()

    /**
     * NvsStreamingContext的空闲存活时间，单位毫秒
     */
    private const val MEICAM_CONTEXT_KEEP_ALIVE_TIME_MILLISECOND = 10000L

    private var lastCloseJob: Job? = null

    @Volatile
    private var aliveCount = 0

    @Volatile
    var isClosing = false

    /**
     * 以线程安全的方式请求美摄sdk的context，且会计数+1，
     * 每请求一次[requestContext]应对应一次[tryCloseContext]
     * @param context Context
     * @param flags 请求NvsStreamingContext需要设定的标志位
     * @param tag 用于记录调用位置，发现多线程问题时方便定位
     * @return NvsStreamingContext
     */
    @JvmOverloads
    @Synchronized
    fun requestContext(context: Context, flags: Int = DEFAULT_FLAG, tag: String): NvsStreamingContext? {
        if (isClosing) {
            GLog.e(TAG, "[requestContext] isClosing error!!! $tag, $isClosing, $aliveCount $flags")
            return null
        }
        aliveCount++
        lastCloseJob?.cancel()
        lastCloseJob = null
        GLog.d(TAG, LogFlag.DL, "[requestContext] called from $tag")
        return NvsStreamingContext.init(
            context,
            AppBrandConstants.License.MEICAM_LICENSE_ASSET,
            flags
        )
    }

    /**
     * 试图回收NvsStreamingContext，会先计数-1，如果计数为0则会执行close
     * 每请求一次[tryCloseContext]前应先执行一次[requestContext]
     * @param tag 用于记录调用位置，发现多线程问题时方便定位
     */
    @Synchronized
    fun tryCloseContext(tag: String) {
        aliveCount--
        if (aliveCount == 0) {
            lastCloseJob?.cancel()
            GLog.d(TAG, LogFlag.DL, "[tryCloseContext] called from $tag")
            // 如果NvsStreamingContext空闲，即aliveCount == 0,则等待特点时间后执行close
            lastCloseJob = AppScope.launch(Dispatchers.MeicamEdit) {
                delay(MEICAM_CONTEXT_KEEP_ALIVE_TIME_MILLISECOND)
                closeQuicklyIfIdle(tag)
            }
        } else if (aliveCount < 0) {
            // 如果小于0了证明调用close的次数多了，出现了异常，打印异常信息
            GLog.e(
                TAG,
                "[tryCloseContext] close error because alive count less than 0",
                IllegalStateException("[tryCloseContext] and [requestContext] method calls do not match")
            )
        } else {
            GLog.d(TAG, LogFlag.DL, "[tryCloseContext] can't close, because context is used")
        }
    }

    /**
     * 实际关闭context的方法
     * @param tag 用于记录调用位置，发现多线程问题时方便定位
     */
    @Synchronized
    private fun closeQuicklyIfIdle(tag: String) {
        if (aliveCount == 0) {
            GLog.d(TAG, "[closeQuicklyIfIdle],called from $tag start close NvsStreamContext. $aliveCount $isClosing")
            /*
             * 美摄不确定调close时，是否已经stop了，所以会内部调一次，若此时有解帧等动作
             * 会温和地等待这些动作完成后，才开始真正close
             * 这里增加中断式的stop,规避close耗时过长问题(后续让美摄集成在close内部)
             */
            isClosing = true
            NvsStreamingContext.getInstance().stop(STREAMING_ENGINE_INTERRUPT_STOP)
            NvsStreamingContext.close()
            isClosing = false
            GLog.d(TAG, "[closeQuicklyIfIdle], called from $tag close NvsStreamContext success. $aliveCount $isClosing")
        } else {
            GLog.d(TAG, "[closeQuicklyIfIdle], can't close NvsStreamingContext, the requestNvsContext method may be called duration")
        }
    }

    /**
     * 通过 [block] 使用请求的 context，执行完毕后内部会自行 [tryCloseContext]
     *
     * @param context Context
     * @param flags 请求 NvsStreamingContext 需要设定的标志位
     * @param tag 用于记录调用位置，发现多线程问题时方便定位
     * @param block 使用请求到美摄的 context，返回值会用于此方法的返回，获取不到美摄context时不会被调用
     * @return 如果能请求到美摄context则返回 [block] 的返回值，否则返回 null
     *
     * @see requestContext
     */
    fun <T> useContext(context: Context, flags: Int = DEFAULT_FLAG, tag: String, block: (NvsStreamingContext) -> T): T? {
        val nvsContext = requestContext(context, flags, tag) ?: let {
            GLog.e(TAG, LogFlag.DL) { "[useContext] nvsContext is null" }
            return null
        }

        return try {
            block(nvsContext)
        } finally {
            tryCloseContext(tag)
        }
    }
}