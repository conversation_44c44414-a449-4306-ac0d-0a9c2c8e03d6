/*********************************************************************************
 ** Copyright (C), 2022-2032, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : EffectWrapper.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2025/07/30
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>               2025/07/30  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.standard_lib.codec.player.effect

import com.oplus.tbl.exoplayer2.Effect
import java.util.concurrent.locks.ReentrantReadWriteLock
import kotlin.concurrent.read
import kotlin.concurrent.write

/**
 * Effect List 的包装类
 */
class EffectWrapper : CoordinateList<String, Effect>() {

    /**
     * 是否可用
     */
    val enable: Boolean
        get() = forceEnable || size > 0

    /**
     * 强制可用开关
     */
    var forceEnable = false
}

/**
 * 创建 [Effect] 的工厂
 */
interface EffectFactory : CoordinateList.CoordinatorFactory<Effect>

/**
 * 联动的 List
 *
 * - 实现了 [MutableList] 接口的所有方法,可以通过 [Key] 来调整集合
 * - [coordinate] 将 [Key] 与 [Coordinator] 关联, [Key] 的增删与 [Coordinator] 的增删同步
 */
open class CoordinateList<Key, Coordinator> : MutableList<Key> {

    override val size: Int get() = _keys.size

    val keys: List<Key> get() = _keys
    private val _keys: MutableList<Key> = mutableListOf()

    val coordinators: List<Coordinator> get() = _coordinators
    private val _coordinators: MutableList<Coordinator> = mutableListOf()

    private val muteKeys: MutableSet<Key> = mutableSetOf()

    private val factories: MutableMap<Key, CoordinatorFactory<Coordinator>> = mutableMapOf()

    private val lock = ReentrantReadWriteLock()

    override fun contains(element: Key): Boolean = lock.read {
        element in _keys
    }

    override fun add(element: Key): Boolean = lock.write {
        if (muteKeys.contains(element)) return false
        if (element in _keys) return false
        val factory = factories[element] ?: return false
        val coordinator = factory.createCoordinator() ?: return false
        _keys.add(element)
        _coordinators.add(coordinator)
        return true
    }

    override fun add(index: Int, element: Key) = lock.write {
        if (muteKeys.contains(element)) return
        if (element in _keys) return
        val factory = factories[element] ?: return
        val coordinator = factory.createCoordinator() ?: return
        _keys.add(index, element)
        _coordinators.add(index, coordinator)
    }

    override fun addAll(index: Int, elements: Collection<Key>): Boolean = lock.write {
        if (elements.isEmpty()) return@write false
        if (index < 0 || index > _keys.size) return@write false

        val keyList = mutableListOf<Key>()
        val coordinatorList = mutableListOf<Coordinator>()
        elements.forEach { key ->
            if (muteKeys.contains(key)) return@forEach
            if (key !in factories) return@forEach
            if (key in _keys) return@forEach
            val factory = factories[key]
            val coordinator = factory?.createCoordinator() ?: return@forEach
            keyList.add(key)
            coordinatorList.add(coordinator)
        }

        if (keyList.isEmpty()) return@write false
        _keys.addAll(index, keyList)
        _coordinators.addAll(index, coordinatorList)
        return@write true
    }

    override fun addAll(elements: Collection<Key>): Boolean = lock.write {
        if (elements.isEmpty()) return@write false

        val keyList = mutableListOf<Key>()
        val coordinatorList = mutableListOf<Coordinator>()
        elements.forEach { key ->
            if (muteKeys.contains(key)) return@forEach
            if (key !in factories) return@forEach
            if (key in _keys) return@forEach
            val factory = factories[key]
            val coordinator = factory?.createCoordinator() ?: return@forEach
            keyList.add(key)
            coordinatorList.add(coordinator)
        }

        _keys.addAll(keyList)
        _coordinators.addAll(coordinatorList)
        return@write true
    }

    override fun clear(): Unit = lock.write {
        _keys.clear()
        _coordinators.clear()
    }

    override fun get(index: Int): Key = lock.read {
        if (index !in _keys.indices) throw IndexOutOfBoundsException("Index: $index, Size: ${_keys.size}")
        _keys[index]
    }

    override fun isEmpty(): Boolean = lock.read {
        _keys.isEmpty()
    }

    override fun iterator(): MutableIterator<Key> = lock.write {
        val keyIterator = ArrayList(_keys).listIterator()
        object : MutableIterator<Key> {
            override fun hasNext(): Boolean = keyIterator.hasNext()
            override fun next(): Key = keyIterator.next()
            override fun remove() {
                val key = keyIterator.previous()
                val index = _keys.indexOf(key)
                if (index >= 0) {
                    _keys.removeAt(index)
                    _coordinators.removeAt(index)
                }
                keyIterator.remove()
            }
        }
    }

    @Throws(UnsupportedOperationException::class)
    override fun listIterator(): MutableListIterator<Key> = lock.read {
        throw UnsupportedOperationException("listIterator is not supported due to coordination constraints.")
    }

    @Throws(UnsupportedOperationException::class)
    override fun listIterator(index: Int): MutableListIterator<Key> = lock.read {
        throw UnsupportedOperationException("listIterator is not supported due to coordination constraints.")
    }

    override fun removeAt(index: Int): Key = lock.write {
        if (index !in _keys.indices) throw IndexOutOfBoundsException("Index: $index, Size: ${_keys.size}")
        _coordinators.removeAt(index)
        _keys.removeAt(index)
    }

    @Throws(UnsupportedOperationException::class)
    override fun subList(fromIndex: Int, toIndex: Int): MutableList<Key> = lock.read {
        throw UnsupportedOperationException("subList is not supported due to coordination constraints.")
    }

    override fun set(index: Int, element: Key): Key = lock.write {
        if (muteKeys.contains(element)) return@write element
        if (index !in _keys.indices) throw IndexOutOfBoundsException("Index: $index, Size: ${_keys.size}")
        if (_keys[index] == element) return@write element

        val currIndex = _keys.indexOf(element)
        val coordinator = if (currIndex != -1) {
            _coordinators[currIndex]
        } else {
            val factory = factories[element] ?: return@write element
            factory.createCoordinator() ?: return@write element
        }

        _coordinators[index] = coordinator
        _keys.set(index, element)
    }

    override fun retainAll(elements: Collection<Key>): Boolean = lock.write {
        if (elements.isEmpty()) {
            val changed = _keys.isNotEmpty()
            clear()
            return@write changed
        }

        var changed = false
        val iterator = _keys.listIterator()
        while (iterator.hasNext()) {
            val element = iterator.next()
            if (element !in elements) {
                val index = iterator.previousIndex()
                iterator.remove()
                _coordinators.removeAt(index)
                changed = true
            }
        }
        changed
    }

    override fun removeAll(elements: Collection<Key>): Boolean = lock.write {
        if (elements.isEmpty()) return@write false

        var changed = false
        val iterator = _keys.listIterator()
        while (iterator.hasNext()) {
            val element = iterator.next()
            if (element in elements) {
                val index = iterator.previousIndex()
                iterator.remove()
                _coordinators.removeAt(index)
                changed = true
            }
        }
        changed
    }

    override fun remove(element: Key): Boolean = lock.write {
        val index = _keys.indexOf(element).takeIf { it >= 0 } ?: return@write false
        _keys.removeAt(index)
        _coordinators.removeAt(index)
        true
    }

    override fun lastIndexOf(element: Key): Int = lock.read {
        _keys.lastIndexOf(element)
    }

    override fun indexOf(element: Key): Int = lock.read {
        _keys.indexOf(element)
    }

    override fun containsAll(elements: Collection<Key>): Boolean = lock.read {
        _keys.containsAll(elements)
    }

    override fun toString(): String {
        return keys.toString()
    }

    fun coordinate(key: Key, factory: CoordinatorFactory<Coordinator>): Unit = lock.write {
        factories[key] = factory
    }

    fun mute(key: Key): Unit = lock.write {
        muteKeys.add(key)
    }

    fun interface CoordinatorFactory<Coordinator> {
        fun createCoordinator(): Coordinator?
    }

    private companion object {
        private const val TAG = "CoordinateList"
    }
}

