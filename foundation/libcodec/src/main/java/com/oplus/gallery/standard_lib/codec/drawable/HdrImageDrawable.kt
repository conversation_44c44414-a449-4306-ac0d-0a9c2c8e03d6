/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - HdrImageDrawable.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2023/10/10
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>                     <date>          <version>    <desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery       2023/10/10        1.0        OPLUS_ARCH_EXTENDS
 ********************************************************************************/

package com.oplus.gallery.standard_lib.codec.drawable

import android.annotation.TargetApi
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.ColorFilter
import android.graphics.Gainmap
import android.graphics.PixelFormat
import android.graphics.drawable.Drawable
import android.os.Build
import android.util.ArrayMap
import com.oplus.gallery.foundation.util.ext.getConfigSafely
import com.oplus.gallery.addon.graphics.toGainmap
import com.oplus.gallery.foundation.util.io.IStringBuild

/**
 * 仅包含HDR内容的Drawable类。
 */
open class HdrImageDrawable(
    /**
     * 获取HDR文件的Linear Mask灰图信息，以Bitmap的形式返回。
     */
    override val grayImage: Bitmap,

    /**
     * 获取HDR文件的Meta信息。
     */
    override val metadata: IHdrMetadataPack
) : Drawable(), IHdrImageContent, IStringBuild {
    override val tag: String = TAG

    constructor(hdrImageContent: IHdrImageContent) : this(hdrImageContent.grayImage, hdrImageContent.metadata)

    override fun draw(canvas: Canvas) = Unit

    override fun setAlpha(alpha: Int) = Unit

    override fun setColorFilter(colorFilter: ColorFilter?) = Unit

    override fun getOpacity(): Int = PixelFormat.OPAQUE

    fun copy(): HdrImageDrawable {
        return HdrImageDrawable(
            grayImage.copy(grayImage.getConfigSafely(), true),
            metadata.copy()
        )
    }

    fun size(): Int {
        return grayImage.allocationByteCount + metadata.size()
    }

    /**
     * 获取hdr 样式
     * @return 返回对应的样式，详见 [HdrImageType]
     */
    fun getType(): HdrImageType {
        return metadata.getHdrType()
    }

    /**
     * 转换成Gainmap对象
     */
    @TargetApi(Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
    fun toGainmap(): Gainmap? {
        return (metadata as? UHdrMetadataPack)?.metadata?.toGainmap(grayImage)
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as HdrImageDrawable

        if (grayImage != other.grayImage) return false
        if (metadata != other.metadata) return false

        return true
    }

    override fun hashCode(): Int {
        var result = grayImage.hashCode()
        result = 31 * result + metadata.hashCode()
        return result
    }

    override fun buildString(map: MutableMap<String, Any?>) {
        map["grayImage"] = grayImage
        map["metadata"] = metadata
    }

    override fun toString(): String {
        return "$tag(${ArrayMap<String,Any?>().also(::buildString).asIterable().joinToString()})"
    }

    companion object {
        private const val TAG = "HdrImageDrawable"
    }
}

/**
 * 判定当前[HdrImageDrawable]所表示的HDR内容，是否有效。
 *
 * @return
 */
fun IHdrImageContent.isHdrContentValid(): Boolean = this.grayImage.isRecycled.not() && (this.metadata.size() > 0)
