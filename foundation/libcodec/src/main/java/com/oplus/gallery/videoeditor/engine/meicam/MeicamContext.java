/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File        :  - MeicamContext.java
 ** Description : XXXXXXXXXXXXXXXXXXXXX.
 ** Version     : 1.0
 ** Date        : 2019/12/19
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                 <data>      <version>  <desc>
 **  <EMAIL>  2019/12/19  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditor.engine.meicam;

import static com.meicam.sdk.NvsStreamingContext.CLEAR_CACHE_FLAG_AVFILE_INFO;
import static com.meicam.sdk.NvsStreamingContext.CLEAR_CACHE_FLAG_AVFILE_KEYFRAME_INFO;
import static com.meicam.sdk.NvsStreamingContext.CLEAR_CACHE_FLAG_CAPTION_FONT_INFO;
import static com.meicam.sdk.NvsStreamingContext.CLEAR_CACHE_FLAG_STREAMING_ENGINE;
import static com.meicam.sdk.NvsStreamingContext.CLEAR_CACHE_FLAG_WAVEFORM_ENGINE;

import android.content.Context;

import com.meicam.sdk.NvsStreamingContext;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.standard_lib.codec.retriever.MeicamContextAliveCounter;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;

public class MeicamContext {
    private static final String TAG = "MeicamContext";
    private static final int MASTER_CONTEXT = 0;
    private static final int SLAVE_CONTEXT = 1;
    private static final boolean ASYNC_CLEAR = true;
    private static final boolean SYNC_CLEAR = false;

    private static final List<LinkedList<MeicamContext>> mContextStack = Arrays.asList(
        new LinkedList<MeicamContext>(), // this stack for master context
        new LinkedList<MeicamContext>()  // this stack for slavery contexts
    );

    private final NvsStreamingContext mNvsStreamingContextRef;
    private boolean mIsRecycled = false;

    private MeicamContext(NvsStreamingContext nvsContext) {
        mNvsStreamingContextRef = nvsContext;
        mIsRecycled = false;
    }

    private static synchronized MeicamContext requestContext(Context context, int flag) {
        if (context == null) {
            return null;
        }

        final List<MeicamContext> masterStack = mContextStack.get(MASTER_CONTEXT);
        final List<MeicamContext> slaveStack = mContextStack.get(SLAVE_CONTEXT);

        if (masterStack.isEmpty()) {
            // create a master context
            NvsStreamingContext nvsStreamingContext = MeicamContextAliveCounter.INSTANCE.requestContext(context.getApplicationContext(), flag, TAG);
            GLog.i(TAG, "[requestContext] create a master streaming context: " + nvsStreamingContext);
            if (nvsStreamingContext != null) {
                MeicamContext nvsContext = new MeicamContext(nvsStreamingContext);
                masterStack.add(nvsContext);
                return nvsContext;
            }
            GLog.e(TAG, "[requestContext] create context is null! ");
            return null;
        } else {
            // create a slavery context
            GLog.i(TAG, "[requestContext] create a slave streaming context");
            NvsStreamingContext masterContext = masterStack.get(MASTER_CONTEXT).getInnerNvsStreamingContextRef();
            MeicamContext slaveContext = new MeicamContext(masterContext.createAuxiliaryStreamingContext(flag));
            slaveStack.add(slaveContext);
            return slaveContext;
        }
    }

    private static synchronized void removeFromContextPool(MeicamContext nvsContext) {
        if (nvsContext == null) {
            return;
        }
        if (nvsContext.isRecycled()) {
            return;
        }
        nvsContext.mIsRecycled = true;

        final List<MeicamContext> masterStack = mContextStack.get(MASTER_CONTEXT);
        final List<MeicamContext> slaveStack = mContextStack.get(SLAVE_CONTEXT);

        if (masterStack.contains(nvsContext)) {
            if (slaveStack.isEmpty()) {
                destroyMasterContextIfNeed();
            } else {
                GLog.i(TAG, String.format(Locale.ENGLISH,
                    "[removeFromContextPool] %d slave streaming context are still working, "
                        + "destroy the master streaming context later", slaveStack.size()));
            }
        } else if (slaveStack.contains(nvsContext)) {
            // Destroy this slave context
            final NvsStreamingContext masterNvsContext = masterStack.get(MASTER_CONTEXT).getInnerNvsStreamingContextRef();
            masterNvsContext.destoryAuxiliaryStreamingContext(nvsContext.getInnerNvsStreamingContextRef());
            GLog.i(TAG, "[removeFromContextPool] slave streaming context has been destroyed");

            // Remove entry from slave stack
            slaveStack.remove(nvsContext);

            // Destroy master context if need
            destroyMasterContextIfNeed();
        } else {
            // Something wrong happens here, this NvsStreamingContext neither Master context nor Slave context
            GLog.e(TAG, "[removeFromContextPool] Something wrong happens here, "
                + "this NvsStreamingContext neither Master context nor Slave context, "
                + "this invoking stack as follow:", new Throwable());

            // Treat as Slave context for default
            if (!masterStack.isEmpty()) {
                try {
                    final NvsStreamingContext masterNvsContext = masterStack.get(MASTER_CONTEXT).getInnerNvsStreamingContextRef();
                    masterNvsContext.destoryAuxiliaryStreamingContext(nvsContext.getInnerNvsStreamingContextRef());
                    destroyMasterContextIfNeed();
                } catch (Throwable thr) {
                    GLog.e(TAG, "[removeFromContextPool] Something wrong happens here, "
                            + "this NvsStreamingContext neither Master context nor Slave context, "
                            + "we treat this NvsStreamingContext as a slave context and destroy it, but an error occurred",
                        new Throwable());
                }
            }
        }
    }

    private static void destroyMasterContextIfNeed() {
        final List<MeicamContext> masterStack = mContextStack.get(MASTER_CONTEXT);
        final List<MeicamContext> slaveStack = mContextStack.get(SLAVE_CONTEXT);

        if (slaveStack.isEmpty()) {
            for (MeicamContext masterContext : masterStack) {
                final NvsStreamingContext masterNvsContext = masterContext.getInnerNvsStreamingContextRef();
                if (masterContext.isRecycled()) {
                    /*
                       Bug【9572059】是由于这里是耗时操作导致
                       与美摄沟通，该处暂时不清理缩图缓存，后期美摄会更新内部异步清理的sdk后再修改回来
                     */
                    masterNvsContext.clearCachedResources(ASYNC_CLEAR, CLEAR_CACHE_FLAG_STREAMING_ENGINE
                            | CLEAR_CACHE_FLAG_WAVEFORM_ENGINE | CLEAR_CACHE_FLAG_AVFILE_INFO
                            | CLEAR_CACHE_FLAG_AVFILE_KEYFRAME_INFO | CLEAR_CACHE_FLAG_CAPTION_FONT_INFO);
                    MeicamContextAliveCounter.INSTANCE.tryCloseContext(TAG);
                    if (masterContext == masterStack.get(masterStack.size() - 1)) {
                        masterStack.clear();
                    }
                    GLog.i(TAG, "[destroyMasterContextIfNeed] no slave streaming context are still working, "
                            + "accept the destroy request for master streaming context");
                }
            }
        }
    }

    public static MeicamContext newInstance(Context context, int flag) {
        return requestContext(context, flag);
    }

    public void recycle() {
        removeFromContextPool(this);
    }

    public boolean isRecycled() {
        return mIsRecycled;
    }

    public NvsStreamingContext getNvsStreamingContext() {
        return mIsRecycled ? null : mNvsStreamingContextRef;
    }

    private NvsStreamingContext getInnerNvsStreamingContextRef() {
        return mNvsStreamingContextRef;
    }

    /**
     * NvsStreamingContext flag builder
     */
    public static class NvsContextFlagBuilder {

        private int mFlag = 0;

        public NvsContextFlagBuilder disableHardwareCodec() {
            mFlag |= NvsStreamingContext.STREAMING_CONTEXT_FLAG_NO_HARDWARE_VIDEO_READER;
            return NvsContextFlagBuilder.this;
        }

        public NvsContextFlagBuilder initializeNvsContextAsynchronized() {
            mFlag |= NvsStreamingContext.STREAMING_CONTEXT_FLAG_ASYNC_INITIALIZED;
            return NvsContextFlagBuilder.this;
        }

        public NvsContextFlagBuilder apiInvokeAsynchronized() {
            mFlag |= NvsStreamingContext.STREAMING_CONTEXT_FLAG_ASYNC_ENGINE_STATE;
            return NvsContextFlagBuilder.this;
        }

        public NvsContextFlagBuilder support4K() {
            mFlag |= NvsStreamingContext.STREAMING_CONTEXT_FLAG_SUPPORT_4K_EDIT;
            return NvsContextFlagBuilder.this;
        }

        public NvsContextFlagBuilder support8K() {
            mFlag |= NvsStreamingContext.STREAMING_CONTEXT_FLAG_SUPPORT_8K_EDIT;
            return NvsContextFlagBuilder.this;
        }

        public NvsContextFlagBuilder minimumMemoryUsage() {
            mFlag |= NvsStreamingContext.STREAMING_CONTEXT_FLAG_COMPACT_MEMORY_MODE;
            return NvsContextFlagBuilder.this;
        }

        public NvsContextFlagBuilder enableHdrDisplay() {
            mFlag |= NvsStreamingContext.STREAMING_CONTEXT_FLAG_ENABLE_HDR_DISPLAY_WHEN_SUPPORTED;
            return NvsContextFlagBuilder.this;
        }

        /**
         * ANR的大部分原因就是GOP比较长，当Seek到GOP的最后一帧的时候要解码前面所有的帧
         * 添加这个标志后当要停止的时候，直接就退出解码，不会等到最后一帧解码结束
         * @return NvsContextFlagBuilder
         */
        public NvsContextFlagBuilder supportForceStop() {
            mFlag |= NvsStreamingContext.STREAMING_CONTEXT_FLAG_INTERRUPT_STOP_FOR_INTERNAL_STOP;
            return  NvsContextFlagBuilder.this;
        }

        public int build() {
            return mFlag;
        }
    }
}
