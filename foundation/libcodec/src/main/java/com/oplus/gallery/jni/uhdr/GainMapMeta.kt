/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - GainMapMeta
 ** Description: UHdr的元数据
 **
 ** Version: 1.0
 ** Date: 2023/12/01
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  Yegua<PERSON><PERSON>@Apps.Gallery3D  2023/12/01  1.0        build this module
 *********************************************************************************/

package com.oplus.gallery.jni.uhdr

/**
 * UHdr的元数据
 * 此类路径须与libLocalHdrRus-jni.so内声明的一致
 */
data class GainMapMeta(

    /**
     * 以下5个浮点型数组都是按RGBA存储增益, 默认增益1.0f
     */
    val ratioMin: FloatArray = floatArrayOf(DEFAULT_GAIN, DEFAULT_GAIN, DEFAULT_GAIN, DEFAULT_GAIN),

    val ratioMax: FloatArray = floatArrayOf(DEFAULT_GAIN, DEFAULT_GAIN, DEFAULT_GAIN, DEFAULT_GAIN),

    val gamma: FloatArray = floatArrayOf(DEFAULT_GAIN, DEFAULT_GAIN, DEFAULT_GAIN, DEFAULT_GAIN),

    val epsilonSdr: FloatArray = floatArrayOf(DEFAULT_GAIN, DEFAULT_GAIN, DEFAULT_GAIN, DEFAULT_GAIN),

    val epsilonHdr: FloatArray = floatArrayOf(DEFAULT_GAIN, DEFAULT_GAIN, DEFAULT_GAIN, DEFAULT_GAIN),

    val displayRatioSdr: Float = 0f,

    val displayRatioHdr: Float = 0f,

    val scale: Float = 0f
)

private const val DEFAULT_GAIN = 1.0f
