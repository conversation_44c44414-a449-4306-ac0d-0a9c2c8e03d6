/*********************************************************************************
 ** Copyright (C), 2022-2032, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : EisInfo.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2025/07/30
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>               2025/07/30  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.standard_lib.codec.player.effect

import com.google.gson.annotations.SerializedName

/**
 * 视频中插帧数据信息类
 */
data class EisInfo(
    @SerializedName("desc")
    val desc: String? = null,

    @SerializedName("cropRect")
    val cropRect: List<Int>? = null,

    @SerializedName("eisCropFactor")
    val eisCropFactor: List<Float>? = null,

    @SerializedName("videoOrientation")
    val videoOrientation: Int? = null,

    @SerializedName("videoSize")
    val videoSize: List<Int>? = null,

    @SerializedName("matrices")
    val matrices: Map<String, List<Float>>? = null,

    @SerializedName("version")
    val version: Int? = null
)