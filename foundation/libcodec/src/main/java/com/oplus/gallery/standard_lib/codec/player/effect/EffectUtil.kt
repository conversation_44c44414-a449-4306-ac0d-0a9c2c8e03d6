/*********************************************************************************
 ** Copyright (C), 2022-2032, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : EffectUtil.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2025/07/30
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>               2025/07/30  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.standard_lib.codec.player.effect

import android.net.Uri
import com.google.common.reflect.TypeToken
import com.oplus.gallery.foundation.fileaccess.FileAccessManager
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.text.JsonUtil
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.tblplayer.mediainfo.Mp4Util
import java.io.ByteArrayInputStream
import java.io.File
import java.io.FileInputStream
import java.lang.reflect.Type

/**
 * TBL 视频播放特效工具类
 */
object EffectUtil {
    private const val TAG = "EffectUtil"

    /**
     * 获取视频的原始防抖数据
     */
    fun getEisData(uri: Uri = Uri.EMPTY, filePath: String = TextUtil.EMPTY_STRING, offset: Long = 0L): String? {
        val eisData = if (filePath.isEmpty().not()) {
            if (File(filePath).exists().not()) {
                GLog.e(TAG, LogFlag.DL) { "[getEisData] can not get eis data , cause video file no exist" }
                return null
            }

            if ((File(filePath).length() < offset) || (offset < 0)) {
                GLog.e(TAG, LogFlag.DL) { "[getEisData] can not get eis data , cause offset error , offset : $offset " }
                return null
            }
            FileInputStream(filePath).use {
                it.skip(offset)
                it.readBytes()
            }.let(::ByteArrayInputStream)
        } else {
            if ((uri == Uri.EMPTY) || (offset < 0)) {
                GLog.e(TAG, LogFlag.DL) { "[getEisData] can not get eis data , cause illegal input. offset = $offset, uri = $uri" }
                return null
            }
            FileAccessManager.getInstance().openFile(ContextGetter.context, uri)?.use { pfd ->
                FileInputStream(pfd.fileDescriptor).use {
                    it.skip(offset)
                    it.readBytes()
                }.let(::ByteArrayInputStream)
            }
        }?.use(Mp4Util::getLivePhotoExtension) ?: TextUtil.EMPTY_STRING
        return eisData
    }

    /**
     * 获取视频的防抖信息
     */
    fun getEisInfo(eisData: String): EisInfo? {
        val eisType: Type = object : TypeToken<EisInfo>() {}.type
        val eisInfo = JsonUtil.fromJson<EisInfo>(eisData, eisType)
        return eisInfo
    }

    /**
     * 获取视频的防抖信息
     */
    fun getEisInfo(uri: Uri = Uri.EMPTY, filePath: String = TextUtil.EMPTY_STRING, offset: Long = 0L): EisInfo? {
        val eisData = getEisData(uri, filePath, offset) ?: return null
        val eisInfo = getEisInfo(eisData)
        return eisInfo
    }
}