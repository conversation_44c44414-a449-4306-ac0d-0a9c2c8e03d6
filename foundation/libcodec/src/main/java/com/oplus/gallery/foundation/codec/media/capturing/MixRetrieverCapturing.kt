/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MixRetrieverCapturing.kt
 ** Description: 使用系统截帧，如果系统截帧失败则使用美摄解码
 ** Version: 1.0
 ** Date : 2022/12/23
 ** Author: xie<PERSON><PERSON><PERSON>@Apps.Gallery3D
 ** TAG: MixRetrieverCapturing
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  x<PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D      2022/12/23    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.foundation.codec.media.capturing

import android.content.Context
import android.graphics.Bitmap
import android.media.MediaMetadataRetriever
import android.net.Uri
import android.os.Handler
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.standard_lib.codec.videoframe.DecoderType
import com.oplus.gallery.standard_lib.codec.videoframe.MeicamVideoThumbnailDecoder
import com.oplus.gallery.standard_lib.codec.videoframe.VideoThumbnailDecoder

/**
 * 使用系统或美摄的取帧接口提供取帧能力,优先使用系统接口,如果系统接口取帧失败,则使用美摄取帧
 * 之所以要使用美摄兜底是因为系统取帧存在失败的情况,以下为可能的路径
 * 场景1:非杜比视频->系统接口取帧->取帧成功->结束
 * 场景2:Android S以下的设备->杜比视频->系统接口取帧->取帧失败->美摄接口取帧->取帧成功->结束
 * 场景3:其他情况导致系统截帧失败->美摄接口取帧->取帧结束
 * @param context Context
 * @param uri Uri
 * @param callbackHandler 回调结果的Handler
 * @param isSupportDolbyGPUDecoder 是否支持杜比视频GPU方案
 */
class MixRetrieverCapturing(
    private val context: Context,
    private val uri: Uri,
    private val callbackHandler: Handler?,
    private val isSupportDolbyGPUDecoder: Boolean
) : ICapturing {

    private val mediaMetadataRetriever = MediaMetadataRetriever().apply {
        setDataSource(context, uri)
    }
    private var meicamVideoThumbnailDecoder: MeicamVideoThumbnailDecoder? = null

    override fun capture(frameAtTimeUs: Long, onCaptured: (Bitmap?) -> Unit) {
        val startTime = System.currentTimeMillis()
        mediaMetadataRetriever.getFrameAtTime(frameAtTimeUs, MediaMetadataRetriever.OPTION_CLOSEST)?.let { bitmap ->
            GLog.d(TAG, "capture with MediaMeteDataRetriever, cost time:${System.currentTimeMillis() - startTime}")
            callbackHandler?.post { onCaptured(bitmap) } ?: onCaptured(bitmap)
        } ?: MeicamVideoThumbnailDecoder(DecoderType.SOFT, isSupportDolbyGPUDecoder).apply {
            GLog.w(TAG, "capture with MediaMeteDataRetriever fail, try with MeicamVideoThumbnailDecoder")
            meicamVideoThumbnailDecoder = this
            setDataSource(context, uri)
            decodeFrameBitmapAtTime(frameAtTimeUs, VideoThumbnailDecoder.INVALID_SIZE)?.let { bitmap ->
                GLog.d(TAG) { "capture with MeicamVideoThumbnailDecoder, cost time:${System.currentTimeMillis() - startTime}" }
                callbackHandler?.post { onCaptured(bitmap) } ?: onCaptured(bitmap)
            } ?: run {
                GLog.w(TAG) {
                    "capture with MeicamVideoThumbnailDecoder fail, return null! cost time:${System.currentTimeMillis() - startTime}"
                }
                onCaptured(null)
            }
        }
    }

    override fun close() {
        mediaMetadataRetriever.close()
        meicamVideoThumbnailDecoder?.close()
    }

    companion object {
        private const val TAG = "MixRetrieverCapturing"
    }
}