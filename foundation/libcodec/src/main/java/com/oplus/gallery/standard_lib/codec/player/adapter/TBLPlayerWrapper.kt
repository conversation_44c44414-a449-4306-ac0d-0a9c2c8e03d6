/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - TBLPlayerWrapper.kt
 * Description:
 * Version: 1.0
 * Date: 2020/09/04
 * Author: duchengsong@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * duchengsong@Apps.Gallery3D  2020/09/04           1.0           OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/
package com.oplus.gallery.standard_lib.codec.player.adapter

import android.content.Context
import android.graphics.SurfaceTexture
import android.media.MediaMetadataRetriever
import android.os.Bundle
import android.util.Size
import android.view.Surface
import android.view.SurfaceHolder
import android.view.SurfaceView
import androidx.annotation.MainThread
import com.oplus.gallery.foundation.fileaccess.FileAccessManager
import com.oplus.gallery.foundation.fileaccess.FileConstants
import com.oplus.gallery.foundation.fileaccess.data.OpenFileRequest
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.isTimeRange
import com.oplus.gallery.standard_lib.app.AppConstants
import com.oplus.gallery.standard_lib.app.AppConstants.Degree.DEGREE_90
import com.oplus.gallery.standard_lib.codec.SlowMotionVideoUtils
import com.oplus.gallery.standard_lib.codec.player.AVController
import com.oplus.gallery.standard_lib.codec.player.AVController.Companion.INFO_HDR_VIDEO
import com.oplus.gallery.standard_lib.codec.player.AVPlayer
import com.oplus.gallery.standard_lib.codec.player.AVPlayer.VideoSourceDescriptor.MIX
import com.oplus.gallery.standard_lib.codec.player.AVPlayer.VideoSourceDescriptor.NORMAL
import com.oplus.gallery.standard_lib.codec.player.adapter.PlayerAdapter.Companion.NULL_INFO
import com.oplus.gallery.standard_lib.codec.player.effect.EffectWrapper
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.tbl.exoplayer2.Effect
import com.oplus.tblplayer.Constants
import com.oplus.tblplayer.IMediaPlayer
import com.oplus.tblplayer.IMediaPlayer.Video_DROP_FRAME_POLICY_FORCE_120FPS
import com.oplus.tblplayer.TBLPlayerManager
import com.oplus.tblplayer.config.GlobalsConfig
import com.oplus.tblplayer.config.PlayerConfiguration
import com.oplus.tblplayer.misc.MediaUrl
import com.oplus.tblplayer.monitor.ErrorCodeProvider
import com.oplus.tblplayer.monitor.Report
import com.oplus.tblplayer.retriever.TBLMediaMetadataRetriever
import java.util.concurrent.atomic.AtomicBoolean

class TBLPlayerWrapper(
    context: Context,
    private val options: AVPlayer.AVPlayerOptions?
) : PlayerAdapter,
    IMediaPlayer.OnInfoListener,
    IMediaPlayer.OnErrorListener,
    IMediaPlayer.OnPreparedListener,
    IMediaPlayer.OnCompletionListener,
    IMediaPlayer.OnBufferingUpdateListener,
    IMediaPlayer.OnSeekCompleteListener,
    IMediaPlayer.OnPlaybackResultListener,
    IMediaPlayer.OnVideoSizeChangedListener {

    private val application: Context = context.applicationContext

    private val videoPlayEffect: EffectWrapper? = options?.videoEffect as? EffectWrapper

    /**
     * 播放是否包含特效
     */
    private val videoEffectEnable = videoPlayEffect?.enable == true

    /**
     * 双视频：视频播放类型，如果一个文件中有两个视频
     */
    private var mixVideoPlayType: AVPlayer.VideoPlayType? = null

    /**
     * 业务方设置的特效key值列表，为了解决业务方重复设置同样特效，这里记录业务设置过来的key值列表，在设置特效时和旧值进行对比用
     */
    private var oldEffectKeys: List<String>? = null

    /**
     * 标志位，用来记录当前的surfaceView是否有效
     * 解决多个线程对同一个surfaceView进行多次setSurface(null)操作
     * true：表示有效  false：表示无效
     */
    private var isSurfaceSyncVaild = AtomicBoolean(false)

    private val tblPlayer: IMediaPlayer by lazy {
        /*
         * TBL版本从Gallery版本升级到PRO版本， PRO版本会有一些默认的参数值发生了改变，
         * 需要设置低内存和高性能模式，bufferSize和每次解码出帧循环之间的间隔属性值大小会
         * 与旧版本的GALLERY分支保持一致
         */

        val playerConfiguration = PlayerConfiguration.Builder()
            .setLowMemoryModeEnabled(true)
            .setHighPerformanceEnabled(true)
            .setExtractorMode(if (videoEffectEnable) Constants.SOURCE_EXTRACTOR_MODE_EXO else Constants.SOURCE_EXTRACTOR_MODE_ALL)
            .setVideoEffectModeEnabled(videoEffectEnable) //开始特效框架
            .build()
        TBLPlayerManager.createPlayer(application, playerConfiguration)
    }

    /**
     * 对外的播放器事件回调,详见：
     * 1.[IMediaPlayer.OnInfoListener]
     * 2.[IMediaPlayer.OnErrorListener]
     * 3.[IMediaPlayer.OnPreparedListener]
     * 4.[IMediaPlayer.OnCompletionListener]
     * 5.[IMediaPlayer.OnBufferingUpdateListener]
     * 6.[IMediaPlayer.OnSeekCompleteListener]
     */
    private var eventListener: PlayerAdapter.OnPlayerEventListener? = null

    private var isDataSourceValid = false
    private var pixelWidthHeightRatio = 1f

    /**
     * TBL入参：输出分辨率
     * 详见：[getVideoOutputResolution]
     */
    private var videoOutputResolution: Size? = null

    init {
        initPlayerGlobalsConfig(application)
        setInternalEventListener(this)
    }

    private fun setInternalEventListener(listener: TBLPlayerWrapper?) {
        tblPlayer.run {
            setOnBufferingUpdateListener(listener)
            setOnErrorListener(listener)
            setOnInfoListener(listener)
            setOnPreparedListener(listener)
            setOnCompletionListener(listener)
            setOnPlaybackResultListener(listener)
            setOnSeekCompleteListener(listener)
            setOnVideoSizeChangedListener(listener)
        }
    }

    override fun setDataSource(videoSource: AVPlayer.VideoSource) {
        isDataSourceValid = kotlin.runCatching {
            when (videoSource.videoSourceDescriptor) {
                NORMAL -> {
                    val mediaUrl = MediaUrl.Builder(videoSource.uri).apply {
                        val trimRange = videoSource.trimRangeMs
                        if ((trimRange != null) && trimRange.isEmpty().not() && (trimRange.first >= 0)) {
                            GLog.d(TAG) {
                                "<setDataSource> setClipStartPositionMs:${trimRange.first}, setClipEndPositionMs:${trimRange.last}"
                            }
                            setClipStartPositionMs(trimRange.first)
                            setClipEndPositionMs(trimRange.last)
                        }
                        if (videoSource.slowMotionInfo?.slowMotionType == SlowMotionVideoUtils.SlowMotion.TYPE_HSR) {
                            setOverrideExtension(videoSource.slowMotionInfo.tag)
                        }
                    }.build()
                    tblPlayer.setDataSource(mediaUrl)
                }

                is MIX -> {
                    val openFileRequest = OpenFileRequest.Builder().apply {
                        setImage(true)
                        setModeType(FileConstants.FileModeType.MODE_READ)
                        setUri(videoSource.uri)
                        setFile(videoSource.path)
                    }.builder()

                    val videoDescriptor = videoSource.videoSourceDescriptor
                    // 当期望的为srgb色彩空间时，强制tbl播放器关闭广色域支持
                    if (videoDescriptor.targetColorSpace?.isSrgb == true) {
                        GLog.d(TAG) { "[setDataSourceForMix] targetColorSpace = SRGB, disableVideoCodecWCG" }
                        // 注意：此方法只能在setDataSource之前使用
                        tblPlayer.setVideoOutputColorInfo(true)
                        tblPlayer.disableVideoCodecWCG()
                    }

                    FileAccessManager.getInstance().openFile(ContextGetter.context, openFileRequest)?.use { pfd ->
                        if ((pfd.fileDescriptor == null) || pfd.fileDescriptor.valid().not() || (videoDescriptor.length <= 0)) {
                            throw IllegalArgumentException(
                                "Invalid resource, " +
                                        "fileDescriptor = ${pfd.fileDescriptor} , " +
                                        "isFileDescriptorValid = ${pfd.fileDescriptor?.valid()}, " +
                                        "length = ${videoDescriptor.length}}"
                            )
                        }

                        if ((videoDescriptor.playType == AVPlayer.VideoPlayType.SUB) && (videoDescriptor.subVideoInfo != null)) {
                            mixVideoPlayType = AVPlayer.VideoPlayType.SUB
                            tblPlayer.setDataSource(pfd.fileDescriptor, videoDescriptor.subVideoInfo.offset, videoDescriptor.subVideoInfo.length)
                        } else {
                            mixVideoPlayType = AVPlayer.VideoPlayType.MAIN
                            tblPlayer.setDataSource(pfd.fileDescriptor, videoDescriptor.offset, videoDescriptor.length)
                        }

                        GLog.d(TAG) {
                            "[setDataSourceForMix] playType=$mixVideoPlayType " +
                                    "offset:${videoDescriptor.offset}, length:${videoDescriptor.length}, " +
                                    "subVideoInfo= ${videoDescriptor.subVideoInfo}}"
                        }

                        /**
                         * bug fix 9231706 连接蓝牙耳机播放实况卡顿掉帧 at 2025-6-12
                         */
                        tblPlayer.disableRendererClock(true)
                    } ?: {
                        throw IllegalArgumentException("Invalid resource, ParcelFileDescriptor is null")
                    }
                }
            }
        }.onFailure {
            GLog.e(TAG) { "[setDataSource] videoSourceDescriptor ${videoSource.videoSourceDescriptor} err: ${it.message}" }
        }.isSuccess

        val lastVideoOutputResolution = Size(videoOutputResolution?.width ?: 0, videoOutputResolution?.height ?: 0)
        videoOutputResolution = getVideoOutputResolution(videoSource)
        // 如果surface有效（绑定surface的时候会去设置输出output分辨率），如果重新设置资源后分辨率不同了，则需要重新设置output分辨率，否则会出现视频缩小显示在左下角等场景
        if ((videoOutputResolution != lastVideoOutputResolution) && (isSurfaceSyncVaild.get())) {
            setVideoOutputResolutionIfNeed()
        }
    }

    /**
     * 获取播放器输出分辨率：当前指定的为视频资源默认分辨率
     * 支持：
     * 1.包含特效播放且是olive视频播放场景的版本
     */
    private fun getVideoOutputResolution(videoSource: AVPlayer.VideoSource): Size? {
        if (videoSource.videoSourceDescriptor !is MIX || !videoEffectEnable) {
            return null
        }

        kotlin.runCatching {
            val openFileRequest = OpenFileRequest.Builder().apply {
                setImage(true)
                setModeType(FileConstants.FileModeType.MODE_READ)
                setUri(videoSource.uri)
                setFile(videoSource.path)
            }.builder()

            FileAccessManager.getInstance().openFile(ContextGetter.context, openFileRequest)?.use { pfd ->
                if ((pfd.fileDescriptor == null) || pfd.fileDescriptor.valid().not()) {
                    GLog.e(TAG) {
                        "<getVideoSourceSize> fileDescriptor=${pfd.fileDescriptor} isValid=${pfd.fileDescriptor?.valid()} " +
                                "source is invalid, skip retrieve olive video size."
                    }
                    return null
                }

                TBLMediaMetadataRetriever().let { retriever ->
                    try {
                        if (mixVideoPlayType == AVPlayer.VideoPlayType.SUB) {
                            videoSource.videoSourceDescriptor.subVideoInfo?.let { subVideoInfo ->
                                retriever.setDataSource(pfd.fileDescriptor, subVideoInfo.offset, subVideoInfo.length)
                            } ?: {
                                throw IllegalArgumentException("Invalid resource, subVideoInfo is null")
                            }
                        } else {
                            retriever.setDataSource(pfd.fileDescriptor,
                                videoSource.videoSourceDescriptor.offset,
                                videoSource.videoSourceDescriptor.length)
                        }

                        val videoWidth = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH)?.toInt() ?: 0
                        val videoHeight = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT)?.toInt() ?: 0
                        val videoRotation = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_ROTATION)?.toInt() ?: 0
                        if ((videoWidth == 0) || (videoHeight == 0)) {
                            GLog.e(TAG, "getVideoSourceSize error $videoWidth $videoHeight, no need do more")
                            return null
                        }
                        GLog.d(TAG, "getVideoSourceSize: $videoWidth  $videoHeight $videoRotation")

                        //如果视频角度是90或者270时，需要将视频宽高互换
                        return if (((videoRotation + DEGREE_90) % AppConstants.Degree.DEGREE_180) == 0) {
                            Size(videoHeight, videoWidth)
                        } else {
                            Size(videoWidth, videoHeight)
                        }
                    } catch (e: IllegalArgumentException) {
                        GLog.e(TAG, "getVideoSourceSize, process failed!", e)
                    } finally {
                        retriever.release()
                    }
                }
            }
        }.onFailure {
            GLog.e(TAG, "[getVideoSourceSize] failed: ${it.message}")
        }

        return null
    }

    /**
     * 支持插帧的版本时：需要在surface bind时机之后 setVideoOutputResolution
     * 注意：此方法必须要在bind surface之后，才可以让播放器支持插帧且不影响原有api的流程
     */
    private fun setVideoOutputResolutionIfNeed() {
        videoOutputResolution?.takeIf { videoEffectEnable }?.let {
            tblPlayer.setVideoOutputResolution(it.width, it.height)
        }
    }

    /**此接口是TBL播放器为了规避TBL崩溃问题，需要在主线程当中调用
     * 已和播放器人确认，不会阻塞主线程
     * 需要在surfaceview销毁的地方设为true，false由TBLPlayer内部在收到新设置设置下来的surface设
     */
    @MainThread
    override fun setSurfaceViewDestroy(flag: Boolean) {
        tblPlayer.setSurfaceViewDestroy(flag)
    }

    override fun bindVideoSink(surface: Surface) {
        tblPlayer.setSurface(surface)
        setVideoOutputResolutionIfNeed()
        isSurfaceSyncVaild.set(true)
    }

    override fun bindVideoSink(surfaceTexture: SurfaceTexture) {
        tblPlayer.setSurface(Surface(surfaceTexture))
        setVideoOutputResolutionIfNeed()
        isSurfaceSyncVaild.set(true)
    }

    override fun bindVideoSink(surfaceHolder: SurfaceHolder) {
        tblPlayer.setDisplay(surfaceHolder)
        setVideoOutputResolutionIfNeed()
        isSurfaceSyncVaild.set(true)
    }

    override fun bindVideoSink(surfaceView: SurfaceView) {
        tblPlayer.setVideoSurfaceView(surfaceView)
        setVideoOutputResolutionIfNeed()
        isSurfaceSyncVaild.set(true)
    }

    override fun unbindVideoSink() {
        if (isSurfaceSyncVaild.compareAndSet(true, false)) {
            tblPlayer.setSurfaceViewDestroy(true)
            tblPlayer.setSurface(null)
            tblPlayer.setDisplay(null)
        } else {
            GLog.d(TAG, LogFlag.DL) { "[unbindVideoSink] surface has arleady been set null" }
        }
    }

    /**
     * clearSurfaceView原有逻辑 ，这里改成了unbindVideoSink
     */
    override fun unbindVideoSink(surfaceView: SurfaceView) {
        tblPlayer.clearVideoSurfaceView(surfaceView)
    }

    override fun getSupports() = Bundle().apply {
        putBoolean(AVController.SUPPORT_SLOW_MOTION, true)
    }

    override fun getPlayerVendor() = PlayerAdapter.PlayerVendor.TBL

    override fun getVideoWidth() = tblPlayer.videoWidth

    override fun getVideoHeight() = tblPlayer.videoHeight

    override fun prepare(position: Long) {
        val tblEffectList = mutableListOf<Effect>()
        GLog.d(TAG, LogFlag.DL) { "[prepare] current option : $options , videoPlayEffect : $videoPlayEffect" }
        videoPlayEffect?.coordinators?.forEach { effect ->
            (effect as? Effect)?.let {
                tblEffectList.add(it)
            }
        }
        tblEffectList.takeIf { it.isNotEmpty() }?.also {
            it.forEach { effect ->
                GLog.i(TAG, LogFlag.DL) { "[prepare] set video effect : $effect" }
            }
            tblPlayer.setVideoEffects(it)
        }

        if (position <= 0) {
            tblPlayer.prepareAsync()
        } else {
            seekTo(position)
            tblPlayer.prepareAsync()
        }

        // 尝试开启120FPS播放
        enableDropFramePolicyIfNeed()
    }

    override fun start(positionRange: LongRange?) {
        if ((positionRange != null) && positionRange.isTimeRange()) {
            start(positionRange.first, positionRange.last)
        } else {
            tblPlayer.start()
        }
    }

    private fun start(startPosition: Long, endPosition: Long) {
        GLog.d(TAG, "start. $startPosition, $endPosition")
        seekTo(startPosition)
        tblPlayer.setEndPositionUs(endPosition * ONE_MS_IN_US)
        tblPlayer.start()
    }

    private fun setEndPosition(endPosition: Long) {
        tblPlayer.setEndPosition(endPosition)
    }

    override fun setVideoEffects(effectKeys: List<String>) {
        //如果特效列表和当前再用的特效一致，不需要进行设置
        if (effectKeys.toSet() == oldEffectKeys?.toSet()) {
            GLog.d(TAG, LogFlag.DL) { "[setVideoEffects] same effects, no need to set" }
            return
        }

        //创建特效对象列表并设置给播放器
        videoPlayEffect?.apply {
            addAll(effectKeys)
            retainAll(effectKeys)
            val tblEffectList = mutableListOf<Effect>()
            coordinators.forEach { effect ->
                (effect as? Effect)?.let {
                    tblEffectList.add(it)
                }
            }
            tblEffectList.takeIf { it.isNotEmpty() }?.also {
                it.forEach { effect ->
                    GLog.i(TAG, LogFlag.DL) { "[setVideoEffects] set video effect : $effect" }
                }
                tblPlayer.setVideoEffects(it)
            }
        }
        oldEffectKeys = effectKeys
    }

    override fun pause() {
        tblPlayer.pause()
    }

    override fun stop() {
        tblPlayer.stop()
    }

    override fun reset() {
        tblPlayer.reset()
    }

    override fun release() {
        setInternalEventListener(null)
        eventListener = null
        tblPlayer.release()
    }

    override fun seekTo(position: Long, seekType: AVController.SeekType?) {
        tblPlayer.fastSeekTo(position, (seekType == AVController.SeekType.ENABLE_PREVIEW))
    }

    override fun getDuration() = tblPlayer.duration

    override fun getVideoFrameRate(): Float = tblPlayer.mediaInfo.videoFps

    override fun getCurrentPosition() = tblPlayer.currentPosition

    override fun isPlaying() = tblPlayer.isPlaying

    override fun isValid(): Boolean = isDataSourceValid

    override fun setVolume(leftVolume: Float, rightVolume: Float) {
        // FIXME: 2020/9/3 tbl不支持独立的左右声道音量设置，确认是否加需求？
        tblPlayer.setVolume((leftVolume + rightVolume) / 2.0f)
    }

    override fun setSpeedRate(speedRate: Float) {
        tblPlayer.setPlaybackRate(speedRate)
    }

    override fun setLooping(isLooping: Boolean) {
        tblPlayer.isLooping = isLooping
    }

    override fun isLooping() = tblPlayer.isLooping

    override fun setEventListener(listener: PlayerAdapter.OnPlayerEventListener) {
        eventListener = listener
    }

    override fun enableMiniView(enable: Boolean) {
        tblPlayer.enableMiniView(enable)
    }

    override fun setVideoScalingMode(mode: Int) {
        tblPlayer.setVideoScalingMode(mode)
    }

    override fun onBufferedUpdate(p0: IMediaPlayer?, p1: Int) {
        // do nothing
    }

    override fun onInfo(player: IMediaPlayer?, what: Int, extras: Array<Any?>?): Boolean {
        // 首帧准备好时回调
        if (what == IMediaPlayer.MEDIA_INFO_RENDERED_FIRST_FRAME) {
            eventListener?.onPrepared(this)
            return true
        }
        /*
         和sdk沟通，先取index==0即可,目前这个只有HDR的判断会用到，播放HDR视频时：
         extras[0]代表的是视频的色彩转换曲线
         */
        val extra = if (extras?.isNotEmpty() == true) (extras[0] as? Int) ?: 0 else 0
        eventListener?.onInfo(this, transportToAVPlayerInfo(what), extra)
        return true
    }

    override fun onError(player: IMediaPlayer?, what: Int, extra: Int, details: String?): Boolean {
        eventListener?.onError(this, transportToAVPlayerError(what), transportToAVPlayerError(extra), details ?: NULL_INFO)
        return true
    }

    /**
     * onPrepared调用表示tblPlayer的状态走到了prepared,prepared表示播放器的、解码器实例创建成功了，
     * 但是第一帧的准备不在这里，需要监听onInfo的[IMediaPlayer.MEDIA_INFO_RENDERED_FIRST_FRAME]状态，
     * 不要在这里使用[eventListener?.onPrepared]
     * @param player 播放器
     */
    override fun onPrepared(player: IMediaPlayer?) {}

    override fun onCompletion(player: IMediaPlayer?) {
        eventListener?.onCompletion(this)
    }

    override fun onBufferingUpdate(player: IMediaPlayer?, percent: Int) {
        eventListener?.onBufferingUpdate(this, percent)
    }

    override fun onSeekComplete(player: IMediaPlayer?) {
        eventListener?.onSeekComplete(this)
    }

    override fun onPlaybackResult(player: IMediaPlayer?, report: Report?): Boolean {
        GLog.v(TAG, "onPlaybackResult $player $report")
        return true
    }

    override fun onVideoSizeChanged(
        mp: IMediaPlayer?,
        width: Int,
        height: Int,
        unappliedRotationDegrees: Int,
        pixelWidthHeightRatio: Float
    ) {
        if (pixelWidthHeightRatio != 1f) {
            GLog.w(TAG) { "[onVideoSizeChanged] width: $width, height: $height, pixelWidthHeightRatio: $pixelWidthHeightRatio" }
        }
        this.pixelWidthHeightRatio = pixelWidthHeightRatio
    }

    override fun getPixelWidthHeightRatio(): Float {
        return pixelWidthHeightRatio
    }

    /**
     * 将TBL异常码转译成 AVController通用异常码：目前仅支持本地媒体资源异常
     *
     * @param error tbl返回的遗产码
     *
     * 备注：TBL异常码详见[https://odocs.myoas.com/sheets/VMAPV26l6OUJ7Oqg/MODOC]
     */
    private fun transportToAVPlayerError(error: Int) = when (error) {
        /*
         * 依次解释如下：
         * AssetDataSourceException，asset类型资源异常
         * FileDataSourceException，本地file类型资源异常
         * MediaSource未确认异常
         */
        ErrorCodeProvider.REASON_DS_ASSET,
        ErrorCodeProvider.REASON_DS_FILE,
        ErrorCodeProvider.REASON_MS_OTHERS -> AVController.ERROR_LOC_MEDIA_SOURCE_EXCEPTION

        else -> error
    }

    private fun transportToAVPlayerInfo(info: Int): Int {
        return when (info) {
            IMediaPlayer.MEDIA_INFO_VIDEO_HDR_INFO -> INFO_HDR_VIDEO
            else -> info
        }
    }

    /**
     * 如果当前支持120FPS播放，则开启120FPS播放。
     * - 是否支持开启由外部通过[AVPlayer.AVPlayerOptions]指定，foundation层不做业务校验。
     */
    private fun enableDropFramePolicyIfNeed() {
        if (options?.shouldPlaybackAt120Fps != true) {
            return
        }
        tblPlayer.enableDropFramePolicy(true, Video_DROP_FRAME_POLICY_FORCE_120FPS)
    }

    companion object {
        private const val TAG = "TBLPlayerWrapper"
        private const val ONE_MS_IN_US = 1000L

        private var initialized = false

        private fun initPlayerGlobalsConfig(context: Context) {
            if (initialized) {
                return
            }
            runCatching {
                val globalsConfig = GlobalsConfig.Builder(context).run {
                    setDebug(GProperty.DEBUG_TBL_PLAYER_DEBUG)
                    setOkhttpEnable(false)
                    setPreCacheEnable(false)
                    // 双耳录音视频播放支持
                    setBinauralCaptureVideoEnable(true)
                    build()
                }
                TBLPlayerManager.initGlobals(context, globalsConfig)
                initialized = true
            }.onFailure {
                GLog.e(TAG, it) { "[initTBLPlayer] config failed!" }
            }
        }
    }
}