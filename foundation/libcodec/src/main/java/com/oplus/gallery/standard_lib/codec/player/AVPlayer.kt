/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - AVPlayer.kt
 * Description:AVPlayer拥有全部的权限，包括持有player操作，设置资源文件，绑定界面，关闭player等
 *              AVPlayer实现AVController接口，Controller只拥有控制媒体播放，获取播放信息，获取播放回调的能力
 *              AVController一般由业务功能持有，比如seekBar，DLNA，自动播放功能等
 * Version: 1.0
 * Date: 2020/09/01
 * Author: Jin<PERSON><PERSON>@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * JinPeng@Apps.Gallery3D       2020/09/01          1.0         build this module
 * duchengsong@Apps.Gallery3D   2020/09/04          1.1         OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/
package com.oplus.gallery.standard_lib.codec.player

import android.content.Context
import android.graphics.ColorSpace
import android.graphics.SurfaceTexture
import android.media.MediaFormat
import android.media.MediaFormat.COLOR_TRANSFER_HLG
import android.media.MediaPlayer
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.HandlerThread
import android.os.Looper
import android.util.ArrayMap
import android.view.Surface
import android.view.SurfaceHolder
import android.view.SurfaceView
import androidx.annotation.VisibleForTesting
import androidx.media3.session.SessionToken
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.mask.PathMask
import com.oplus.gallery.standard_lib.codec.SlowMotionVideoUtils
import com.oplus.gallery.standard_lib.codec.player.AVController.LoadingState
import com.oplus.gallery.standard_lib.codec.player.AVController.PlaybackState
import com.oplus.gallery.standard_lib.codec.player.adapter.EmptyPlayerWrapper
import com.oplus.gallery.standard_lib.codec.player.adapter.PlatformPlayerWrapper
import com.oplus.gallery.standard_lib.codec.player.adapter.PlayerAdapter
import com.oplus.gallery.standard_lib.codec.player.adapter.PlayerAdapter.Companion.INVALID_POSITION
import com.oplus.gallery.standard_lib.codec.player.adapter.PlayerAdapter.Companion.NULL_INFO
import com.oplus.gallery.standard_lib.codec.player.adapter.SessionPlayerWrapper
import com.oplus.gallery.standard_lib.codec.player.adapter.TBLPlayerWrapper
import com.oplus.gallery.standard_lib.codec.player.effect.EffectWrapper

class AVPlayer(
    private val context: Context,
    private val playerVendor: PlayerAdapter.PlayerVendor = PlayerAdapter.PlayerVendor.TBL,
    private val options: AVPlayerOptions?
) : AVController, PlayerAdapter.OnPlayerEventListener {

    companion object {
        private const val TAG = "AVPlayer"
        private const val MIN_VOL = 0.0f
        private const val MAX_VOL = 1.0f
        private const val EMPTY_PATH = ""

        private const val TAG_START = "start"
        private const val TAG_PLAY = "play"
        private const val TAG_PAUSE = "pause"
        private const val TAG_STOP = "stop"
        private const val TAG_PREPARE = "prepare"
        private const val TAG_SEEK = "seek"
        private const val TAG_RESET = "reset"
        private const val TAG_RELEASE = "release"
        private const val TAG_SET_VOLUME = "setVolume"
        private const val TAG_SET_SPEED = "setSpeed"
        private const val TAG_SET_LOOPING = "setLooping"
        private const val TAG_BIND_VIDEO_SINK = "bindVideoSink"
        private const val TAG_UNBIND_VIDEO_SINK = "unbindVideoSink"
        private const val TAG_SET_DATA_SOURCE = "setDataSource"
        private const val TAG_ENABLE_MINI_VIEW = "enableMiniView"
        private const val TAG_SET_SCALING_MODE = "setVideoScalingMode"
        private const val TAG_SET_SURFACE_VIEW = "setSurfaceView"
        private const val TAG_CLEAR_SURFACE_VIEW = "clearSurfaceView"

        private const val MEDIA_ERROR_SYSTEM = -2147483648

        @JvmStatic
        fun errorCodeToString(error: Int): String {
            return when (error) {
                MEDIA_ERROR_SYSTEM -> "MEDIA_ERROR_SYSTEM"
                MediaPlayer.MEDIA_ERROR_IO -> "MEDIA_ERROR_IO"
                MediaPlayer.MEDIA_ERROR_UNKNOWN -> "MEDIA_ERROR_UNKNOWN"
                MediaPlayer.MEDIA_ERROR_TIMED_OUT -> "MEDIA_ERROR_TIMED_OUT"
                MediaPlayer.MEDIA_ERROR_MALFORMED -> "MEDIA_ERROR_MALFORMED"
                MediaPlayer.MEDIA_ERROR_SERVER_DIED -> "MEDIA_ERROR_SERVER_DIED"
                MediaPlayer.MEDIA_ERROR_UNSUPPORTED -> "MEDIA_ERROR_UNSUPPORTED"
                MediaPlayer.MEDIA_ERROR_NOT_VALID_FOR_PROGRESSIVE_PLAYBACK -> "MEDIA_ERROR_NOT_VALID_FOR_PROGRESSIVE_PLAYBACK"
                AVController.ERROR_LOC_MEDIA_SOURCE_EXCEPTION -> "LOC_MEDIA_SOURCE_EXCEPTION"
                else -> "MEDIA_ERROR_UNKNOWN($error)"
            }
        }

        @JvmStatic
        fun infoCodeToString(info: Int): String {
            return when (info) {
                MediaPlayer.MEDIA_INFO_UNKNOWN -> "MEDIA_INFO_UNKNOWN"
                MediaPlayer.MEDIA_INFO_AUDIO_NOT_PLAYING -> "MEDIA_INFO_AUDIO_NOT_PLAYING"
                MediaPlayer.MEDIA_INFO_BAD_INTERLEAVING -> "MEDIA_INFO_BAD_INTERLEAVING"
                MediaPlayer.MEDIA_INFO_BUFFERING_START -> "MEDIA_INFO_BUFFERING_START"
                MediaPlayer.MEDIA_INFO_BUFFERING_END -> "MEDIA_INFO_BUFFERING_END"
                MediaPlayer.MEDIA_INFO_METADATA_UPDATE -> "MEDIA_INFO_METADATA_UPDATE"
                MediaPlayer.MEDIA_INFO_NOT_SEEKABLE -> "MEDIA_INFO_NOT_SEEKABLE"
                MediaPlayer.MEDIA_INFO_STARTED_AS_NEXT -> "MEDIA_INFO_STARTED_AS_NEXT"
                MediaPlayer.MEDIA_INFO_SUBTITLE_TIMED_OUT -> "MEDIA_INFO_SUBTITLE_TIMED_OUT"
                MediaPlayer.MEDIA_INFO_UNSUPPORTED_SUBTITLE -> "MEDIA_INFO_UNSUPPORTED_SUBTITLE"
                MediaPlayer.MEDIA_INFO_VIDEO_NOT_PLAYING -> "MEDIA_INFO_VIDEO_NOT_PLAYING"
                MediaPlayer.MEDIA_INFO_VIDEO_TRACK_LAGGING -> "MEDIA_INFO_VIDEO_TRACK_LAGGING"
                AVController.INFO_READY_TO_PLAY -> "MEDIA_INFO_READY_TO_PLAY"
                AVController.INFO_PLAYING_INFO_READY -> "MEDIA_INFO_PLAYING_INFO_READY"
                AVController.INFO_START_RENDER -> "MEDIA_INFO_START_RENDERING"
                AVController.INFO_PLAY_COMPLETE -> "MEDIA_INFO_PLAY_COMPLETE"
                AVController.INFO_HDR_VIDEO -> "MEDIA_INFO_HDR_VIDEO"
                AVController.INFO_SOUND_CHANGED -> "MEDIA_INFO_SOUND_CHANGED"
                AVController.INFO_BUFFERING_START -> "MEDIA_INFO_BUFFERING_START"
                AVController.INFO_BUFFERING_END -> "MEDIA_INFO_BUFFERING_END"
                AVController.INFO_SEEK_COMPLETE -> "MEDIA_INFO_SEEK_COMPLETE"
                else -> "MEDIA_INFO_UNKNOWN($info)"
            }
        }

        /**
         * 静态方法创建对象。主要为了避免直接使用构造函数，导致单元测试时mock过于复杂（部分null实现会导致构建失败，影响单元测试）。
         * @param context 上下文
         * @param playerVendor 播放器类别
         * @param videoPlayEffect 播放特效
         * @return 构建的AVPlayer对象
         */
        fun create(
            context: Context,
            playerVendor: PlayerAdapter.PlayerVendor = PlayerAdapter.PlayerVendor.TBL,
            playerOptions: AVPlayerOptions? = null
        ): AVPlayer {
            return AVPlayer(context, playerVendor, playerOptions)
        }
    }

    /**
     * 视频的播放源，包含视频源的信息
     * @param context Context
     * @param uri 视频的Uri
     * @param path 视频的路径
     * @param slowMotionInfo SlowMotion
     * @param trimRangeMs 视频的播放区间，包含开始和结束的时间，单位毫秒
     * @param media3Source 谷歌云播放器media3Source,用于在线播放 -- 空间维度：本地，usb，线上 讲道理这里需要抽象，不然后面一堆参数，不知道哪里的，难用
     * @param videoSourceDescriptor 可播放资源对视频部分的源文件结构描述 默认为[VideoSourceDescriptor.NORMAL] 详见[VideoSourceDescriptor]
     */
    data class VideoSource(
        val context: Context,
        val uri: Uri,
        val path: String,
        val slowMotionInfo: SlowMotionVideoUtils.SlowMotion? = null,
        val trimRangeMs: LongRange? = null,
        val media3Source: Media3Source? = null,
        val videoSourceDescriptor: VideoSourceDescriptor = VideoSourceDescriptor.NORMAL
    ) {

        val isHDRVideo: Boolean
            get() = (colorTransfer == MediaFormat.COLOR_TRANSFER_ST2084) || (colorTransfer == COLOR_TRANSFER_HLG)

        var colorTransfer = MediaFormat.COLOR_TRANSFER_SDR_VIDEO

        override fun toString(): String {
            return "VideoSource[uri=$uri, path=${PathMask.mask(path)}, " +
                    "slowMotionInfo=$slowMotionInfo, colorTransfer=$colorTransfer," +
                    "media3Source=$media3Source]"
        }
    }

    /**
     * 在线视频所需要的数据源
     * @param token 在线播放器所必须的token 作用相当于path/uri
     * @param mediaWidth 源视频宽
     * @param mediaHeight 源视频高
     * @param duration 视频时长
     */
    data class Media3Source(
        val token: SessionToken? = null,
        val mediaWidth: Int = 0,
        val mediaHeight: Int = 0,
        val duration: Long = 0,
    )

    /**
     * 播放器配置：开放给业务端来指定播放器行为
     * - 注：此class区别于AvPlayerGetOptions，后者是资源请求过程中的参数，前者是播放器自己的参数。
     * @see com.oplus.gallery.framework.abilities.resourcing.options.AvPlayerGetOptions
     */
    data class AVPlayerOptions(
        /**
         * 色彩空间
         */
        val colorSpace: ColorSpace? = null,

        /**
         * 视频特效
         */
        val videoEffect: MutableList<String>? = null,

        /**
         * 是否需要以120FPS播放
         */
        val shouldPlaybackAt120Fps: Boolean = false,

        /**
         * 播放类型
         */
        var playType: VideoPlayType? = null,

        /**
         *子视频路径
         */
        var subVideoInfo: VideoInfo? = null
    )

    /**
     * 当播放器release时触发回调
     * 主要用与同步player去release surfaceTexture
     */
    var onPlayerRelease: (() -> Unit)? = null
    var dataSource = VideoSource(context, Uri.EMPTY, EMPTY_PATH)
        private set
    var player: PlayerAdapter = EmptyPlayerWrapper
        private set
    var playbackState = PlaybackState.IDLE
        private set
    var onEventListener = ArrayMap<AVController.OnEventListener, Handler?>()
        private set

    private val playbackThread: HandlerThread =
        HandlerThread("${playerVendor.name}Player_Thread@${this.hashCode()}").apply { start() }
    private val playbackHandler: Handler = Handler(playbackThread.looper)

    private var bufferProcess = 0
    private var shouldStartAfterPrepared = false
    private var targetPositionRange: LongRange? = null

    /**
     * 调用prepareAsync后调用stop，可能存在多线程问题
     * stop调用时onPrepared已经在message队列，导致调用了stop 还是回调了onPrepared
     * 已同步tbl在下一版本做双校验修复，本地暂时使用以下标志位修复
     * 用这个标志位保证stop后不响应onPrepared
     */
    private var stopWhenPreparing = false

    /**
     * videoWidth,videoHeight,duration在播放器prepared之后会自动更新
     * isMute,isLooping,playSpeed,loadingState在状态变化时自动更新
     * isPlaying,currentPosition在获取时更新
     */
    var playingInfo = AVController.PlayingInfo()
        private set
        get() {
            field.apply {
                isPlaying = isPlaying()
                currentPosition = getCurrentPosition()
                shouldPlayAfterPrepared = shouldStartAfterPrepared
                colorTransfer = dataSource.colorTransfer
                isValidPlayer = player.isValid()
                playbackState = <EMAIL>
            }
            return field
        }

    val debugInfo: String
        get() = " hash=${this.hashCode()} Uri=${dataSource.uri}, state=$playbackState media3Source=${dataSource.media3Source} "

    init {
        initPlayer()
    }

    private fun initPlayer() {
        runOnPlayerThread(playerVendor.name) {
            player = when (playerVendor) {
                PlayerAdapter.PlayerVendor.TBL -> TBLPlayerWrapper(context, options)
                PlayerAdapter.PlayerVendor.SYSTEM -> PlatformPlayerWrapper()
                PlayerAdapter.PlayerVendor.MEDIA3 -> SessionPlayerWrapper(context)
                else -> TBLPlayerWrapper(context, options)
            }
            player.setEventListener(this)
            updatePlaybackState(PlaybackState.IDLE)
        }
    }

    @VisibleForTesting
    @Suppress("TooGenericExceptionCaught")
    fun runOnPlayerThread(
        functionTag: String,
        errorAction: ((Exception) -> Unit)? = null,
        runAction: () -> Unit
    ) {
        fun realRunAction(
            functionTag: String,
            errorAction: ((Exception) -> Unit)? = null,
            runAction: () -> Unit
        ) {
            GTrace.trace({ "${TAG}_$functionTag" }) {
                val start = System.currentTimeMillis()
                try {
                    runAction()
                } catch (e: Exception) {
                    GLog.e(TAG, "<$functionTag> $debugInfo Player error $e")
                    errorAction?.invoke(e)
                }
                GLog.i(TAG, "<$functionTag> timeSpent ${System.currentTimeMillis() - start} $debugInfo")
            }
        }

        // release之后，线程被释放，监听器被清空，所有操作均无效
        if (playbackState == PlaybackState.END) {
            GLog.e(TAG, "<$functionTag> This player has been released $debugInfo")
            return
        }

        if (Looper.myLooper() == playbackThread.looper) {
            realRunAction(functionTag, errorAction, runAction)
        } else {
            playbackHandler.post { realRunAction(functionTag, errorAction, runAction) }
        }
    }

    fun getVideoWidth(): Int = (playingInfo.videoWidth * player.getPixelWidthHeightRatio()).toInt()

    fun getVideoHeight(): Int = playingInfo.videoHeight

    @VisibleForTesting
    fun isUriInvalid(uri: Uri): Boolean {
        return uri == Uri.EMPTY
    }

    /**此接口是TBL播放器为了规避TBL崩溃问题，需要在主线程当中调用
     * 已和播放器人确认，不会阻塞主线程
     * 需要在surfaceview销毁的地方设为true，false由TBLPlayer内部在收到新设置设置下来的surface设
     */
    fun setSurfaceViewDestroy(flag: Boolean) {
        player.setSurfaceViewDestroy(flag)
    }

    fun prepare(position: Long = INVALID_POSITION) {
        val source = dataSource
        if (isSourceInvalid(source)) {
            GLog.e(TAG, "<prepare> can't prepare $debugInfo")
            if (GProperty.DEBUG) {
                throw IllegalAccessException("<prepare> source uri is null")
            }
            return
        }
        when (playbackState) {
            PlaybackState.ERROR -> {
                reset()
                setDataSource(source)
            }

            PlaybackState.IDLE -> setDataSource(source)
            PlaybackState.INITIALIZED,
            PlaybackState.STOPPED -> {
            }

            else -> {
                GLog.v(TAG, "<prepare> no need to prepare again, it can be playback, return true directly,$debugInfo")
                return
            }
        }

        preparePlayer(position)
    }

    /**
     * 视频路径是无效的
     */
    private fun isSourceInvalid(source: VideoSource): Boolean {
        return isUriInvalid(source.uri) && (source.media3Source?.token == null)
    }

    fun preparePlayer(position: Long = INVALID_POSITION) {
        runOnPlayerThread(
            functionTag = TAG_PREPARE,
            errorAction = {
                GLog.e(TAG, "<preparePlayer> cannot prepare media, media is unsupported or media is broken $debugInfo $it")
                updatePlaybackState(PlaybackState.ERROR)
            },
            runAction = {
                player.prepare(position)
                stopWhenPreparing = false
                updatePlaybackState(PlaybackState.PREPARING)
            }
        )
    }

    override fun play(positionRange: LongRange?) {
        targetPositionRange = null
        runOnPlayerThread(TAG_PLAY) {
            when (playbackState) {
                PlaybackState.INITIALIZED,
                PlaybackState.STOPPED,
                PlaybackState.ERROR -> {
                    preparePlayer()
                    shouldStartAfterPrepared = true
                    targetPositionRange = positionRange
                }

                PlaybackState.PREPARED,
                PlaybackState.PAUSED,
                PlaybackState.COMPLETED -> startPlayer(positionRange)

                PlaybackState.PREPARING -> {
                    GLog.i(TAG, "<play> request to play , but currentState is PREPARING")
                    shouldStartAfterPrepared = true
                    targetPositionRange = positionRange
                }

                else -> GLog.i(TAG, "<play> ignore because $debugInfo")
            }
        }
    }

    override fun setVideoEffects(effectKeys: List<String>) {
        runOnPlayerThread(TAG_START) {
            player.setVideoEffects(effectKeys)
        }
    }

    /**
     * @param type  播放类型
     * 设置视频播放类型，主视频或是小视频，视频切换时需要重置视频源
     */
    override fun setPlayVideoType(type: VideoPlayType) {
        val videoSourceDescriptor = dataSource.videoSourceDescriptor
        if (videoSourceDescriptor !is VideoSourceDescriptor.MIX) {
            GLog.w(TAG, LogFlag.DL) { "[setPlayVideoType] not a olive video" }
            return
        }

        //设置类型相同
        if (videoSourceDescriptor.playType == type) {
            return
        }

        //设置小视频，但是小视频信息为空，禁止设置
        if ((type == VideoPlayType.SUB)
            && (videoSourceDescriptor.subVideoInfo == null)) {
            GLog.w(TAG, LogFlag.DL) { "[setPlayVideoType] set sub video, but sub video info is null" }
            return
        }

        stop()
        reset()
        videoSourceDescriptor.playType = type
        setDataSource(dataSource)
    }

    @VisibleForTesting
    fun startPlayer(positionRange: LongRange? = null) {
        runOnPlayerThread(TAG_START) {
            /**
             * 播放结束后并没有seek(0)，所以在播放结束后重新播放需要先seek(0)，
             * 不然直接播放TBLPlayer会回调Prepared状态上来，导致状态错乱
             */
            if (getCurrentPosition() >= getDuration()) {
                player.seekTo(0L)
                GLog.d(TAG, "[startPlayer] seek(0) because current position large than duration")
            }
            player.start(positionRange)
            updatePlaybackState(PlaybackState.STARTED)
        }
    }

    override fun pause() {
        if (isPlaying()) {
            GLog.i(TAG, "<pause> isPlayerPlaying = ${isPlayerPlaying()} $debugInfo")
            runOnPlayerThread(TAG_PAUSE) {
                player.pause()
                updatePlaybackState(PlaybackState.PAUSED)
            }
        } else {
            GLog.e(TAG, "<pause> video is not playing")
        }
        if (shouldStartAfterPrepared) {
            GLog.i(TAG, "<pause> video is preparing for play $debugInfo")
            shouldStartAfterPrepared = false
            targetPositionRange = null
        }
    }

    override fun setMute(isMute: Boolean) {
        if (playingInfo.isMute == isMute) {
            return
        }
        val volume = if (isMute) MIN_VOL else MAX_VOL
        playingInfo.isMute = isMute
        setVolume(volume, volume)
    }

    override fun isPlaying(): Boolean {
        return playbackState == PlaybackState.STARTED
    }

    /**
     * 查询当前播放器的真实播放状态
     * 由于播放器内部调用可能存在耗时情况，可能和状态机的STARTED状态不同
     */
    @VisibleForTesting
    @Suppress("TooGenericExceptionCaught")
    fun isPlayerPlaying(): Boolean {
        return try {
            player.isPlaying()
        } catch (e: Exception) {
            GLog.e(TAG, "<isPlayerPlaying> get playing state error : $e $debugInfo")
            false
        }
    }

    override fun isMute() = playingInfo.isMute

    override fun stop() {
        when (playbackState) {
            PlaybackState.PREPARED,
            PlaybackState.PREPARING,
            PlaybackState.STARTED,
            PlaybackState.PAUSED,
            PlaybackState.COMPLETED -> {
                runOnPlayerThread(TAG_STOP) {
                    player.stop()
                    stopWhenPreparing = (playbackState == PlaybackState.PREPARING)
                    updatePlaybackState(PlaybackState.STOPPED)
                }
            }

            else -> reset()
        }
    }

    fun reset() {
        playingInfo.loadingState = LoadingState.INIT
        runOnPlayerThread(TAG_RESET) {
            player.reset()
            updatePlaybackState(PlaybackState.IDLE)
        }
    }

    fun release() {
        runOnPlayerThread(TAG_RELEASE) {
            player.release()
            onPlayerRelease?.invoke()
            updatePlaybackState(PlaybackState.END)
            playbackThread.quitSafely()
            playbackHandler.removeCallbacksAndMessages(null)
        }
        removeAllOnEventListener()
    }

    /**
     * 为了保证isSeeking的状态保持, 请在持续seek时先seek,再pause <p>
     *     这是因为部分场景在操作端持续seek时ui表现有区别, 所以isSeeking的判断要更新在pause前
     */
    override fun seekTo(position: Long, seekType: AVController.SeekType?) {
        when (playbackState) {
            PlaybackState.PREPARED,
            PlaybackState.STARTED,
            PlaybackState.PAUSED,
            PlaybackState.COMPLETED -> {
                runOnPlayerThread(TAG_SEEK) {
                    player.seekTo(position, seekType)
                    when (seekType) {
                        AVController.SeekType.ENABLE_PREVIEW -> playingInfo.isSeeking = true
                        AVController.SeekType.DISABLE_PREVIEW,
                        AVController.SeekType.NORMAL -> playingInfo.isSeeking = false
                        else -> GLog.d(TAG, "seekTo: none")
                    }
                }
            }

            else -> GLog.v(TAG, "<seekTo> error State $debugInfo")
        }
    }

    override fun getDuration() = playingInfo.duration

    @Suppress("TooGenericExceptionCaught")
    override fun getCurrentPosition(): Long {
        return when (playbackState) {
            PlaybackState.PREPARED,
            PlaybackState.STARTED,
            PlaybackState.PAUSED,
            PlaybackState.COMPLETED -> {
                try {
                    player.getCurrentPosition()
                } catch (e: Exception) {
                    GLog.e(TAG, "<getCurrentPosition> error $e $debugInfo")
                    0L
                }
            }

            else -> 0L
        }
    }

    fun getLoadingState(): LoadingState = playingInfo.loadingState

    /**
     * 设置数据源，支持只播放一段
     * 注意：本方法暂时只支持TBL播放器，系统播放器会有异常日志。
     */
    fun setDataSource(videoSource: VideoSource) {
        if ((dataSource == videoSource) && (getLoadingState() == LoadingState.LOADING)) {
            GLog.w(TAG, "<setDataSource> setDataSource already running.")
            return
        }
        dataSource = videoSource
        playingInfo.loadingState = LoadingState.LOADING
        runOnPlayerThread(
            functionTag = TAG_SET_DATA_SOURCE,
            errorAction = {
                GLog.e(TAG, "<setDataSource> error $it, $debugInfo")
                onError(player, AVController.ERROR_PLAYBACK_FAILURE, AVController.ERROR_UNKNOWN, NULL_INFO)
            },
            runAction = {
                player.setDataSource(videoSource)
                GLog.i(TAG, "<setDataSource> videoSource = $videoSource, player = $player")
                updatePlaybackState(PlaybackState.INITIALIZED)
            }
        )
    }

    fun bindVideoSink(surface: Surface) {
        runOnPlayerThread(TAG_BIND_VIDEO_SINK) {
            player.bindVideoSink(surface)
        }
    }

    fun bindVideoSink(surfaceTexture: SurfaceTexture) {
        runOnPlayerThread(TAG_BIND_VIDEO_SINK) {
            player.bindVideoSink(surfaceTexture)
        }
    }

    /**
     * 绑定渲染容器
     * @param surfaceHolder
     */
    fun bindVideoSink(surfaceHolder: SurfaceHolder) {
        runOnPlayerThread(TAG_BIND_VIDEO_SINK) {
            player.bindVideoSink(surfaceHolder)
        }
    }

    /**
     * 解绑SurfaceView和播放器
     * asnyc：是否需要异步调用，默认值为true
     */
    fun unbindVideoSink(asnyc: Boolean = true) {
        if (asnyc) {
            runOnPlayerThread(TAG_UNBIND_VIDEO_SINK) {
                GLog.d(TAG, LogFlag.DF) { "unbindVideoSink async." }
                player.unbindVideoSink()
            }
        } else {
            GTrace.trace({ "$TAG.unbindVideoSink" }) {
                GLog.d(TAG, LogFlag.DF) { "unbindVideoSink synchronized." }
                player.unbindVideoSink()
            }
        }
    }

    /**
     * setSurfaceView是绑定渲染容器 与 bindVideoSink方法概念冲突，应该重写bindVideoSink
     * 标记下，有缘人再改吧，用的地方有点多，先不动
     */
    override fun setSurfaceView(surfaceView: SurfaceView) {
        runOnPlayerThread(TAG_SET_SURFACE_VIEW) {
            player.bindVideoSink(surfaceView)
        }
    }

    /**
     * 同上 应重写 unbindVideoSink
     */
    override fun clearSurfaceView(surfaceView: SurfaceView) {
        runOnPlayerThread(TAG_CLEAR_SURFACE_VIEW) {
            player.unbindVideoSink(surfaceView)
        }
    }

    fun getPlayerVendor(): PlayerAdapter.PlayerVendor = player.getPlayerVendor()

    fun setVolume(leftVolume: Float, rightVolume: Float) {
        runOnPlayerThread(
            functionTag = TAG_SET_VOLUME,
            errorAction = {
                GLog.e(TAG, "<setVolume> leftVolume = $leftVolume rightVolume = $rightVolume , debugInfo = $debugInfo ,$it")
            },
            runAction = {
                GLog.d(
                    TAG, "<setVolume> set as ${playingInfo.isMute}, " +
                            "leftVolume = $leftVolume rightVolume = $rightVolume, debugInfo = $debugInfo "
                )
                player.setVolume(leftVolume, rightVolume)
                onInfo(player, AVController.INFO_SOUND_CHANGED, AVController.INFO_UNKNOWN, NULL_INFO)
            }
        )
    }

    fun getSupports(): Bundle = player.getSupports()

    /**
     * 获取播放器特效
     */
    fun getVideoPlayEffect(): EffectWrapper? = options?.videoEffect as? EffectWrapper

    /**
     * 在非INIT及END状态调用均有效
     */
    override fun setSpeedRate(speedRate: Float) {
        val isPlaying = isPlaying()
        runOnPlayerThread(TAG_SET_SPEED) {
            if (!isPlaying) {
                player.pause()
            }
            player.setSpeedRate(speedRate)
        }
    }

    override fun addOnEventListener(
        listener: AVController.OnEventListener,
        handler: Handler?,
        isSticky: Boolean
    ) {
        synchronized(onEventListener) {
            onEventListener[listener] = handler
        }

        if (isSticky.not()) {
            GLog.d(TAG, "[addOnEventListener] ignore! isSticky = false, return")
            return
        }
        // 后续增加的监听器，添加后会上报当前播放器状态，错误状况及信息准备通知
        sendEvent(listener) {
            listener.onPlaybackStateChanged(this, playbackState)
            when (playbackState) {
                PlaybackState.ERROR ->
                    listener.onError(this, AVController.ERROR_PLAYBACK_FAILURE, AVController.ERROR_UNKNOWN, NULL_INFO)
                PlaybackState.PREPARED,
                PlaybackState.COMPLETED,
                PlaybackState.PAUSED,
                PlaybackState.STOPPED,
                PlaybackState.STARTED -> {
                    listener.onInfo(this, AVController.INFO_PLAYING_INFO_READY, AVController.INFO_UNKNOWN, NULL_INFO)
                }
                else -> GLog.v(TAG, "addOnEventListener, No information needs to be reported $debugInfo")
            }
        }
    }

    override fun removeOnEventListener(listener: AVController.OnEventListener) {
        synchronized(onEventListener) {
            onEventListener.remove(listener)
        }
    }

    fun removeAllOnEventListener() {
        synchronized(onEventListener) {
            onEventListener.clear()
        }
    }

    /**
     * 在非INIT及END状态调用均有效
     */
    override fun setLooping(looping: Boolean) {
        runOnPlayerThread(TAG_SET_LOOPING) {
            if (playingInfo.isLooping == looping) {
                return@runOnPlayerThread
            }
            player.setLooping(looping)
            playingInfo.isLooping = looping
        }
    }

    override fun getCurrentPlayingInfo() = playingInfo.copy()

    override fun enableMiniView(enable: Boolean) {
        runOnPlayerThread(TAG_ENABLE_MINI_VIEW) {
            player.enableMiniView(enable)
        }
    }

    override fun setVideoScalingMode(mode: Int) {
        runOnPlayerThread(TAG_SET_SCALING_MODE) {
            player.setVideoScalingMode(mode)
        }
    }

    @VisibleForTesting
    fun updatePlaybackState(state: PlaybackState) {

        GLog.i(TAG, "updatePlaybackState, current state is $state")
        synchronized(playbackState) {
            playbackState = state
        }
        val keepStartFlag = (playbackState == PlaybackState.INITIALIZED) ||
                (playbackState == PlaybackState.PREPARING) ||
                (playbackState == PlaybackState.PREPARED)
        if (shouldStartAfterPrepared && !keepStartFlag) {
            shouldStartAfterPrepared = false
            targetPositionRange = null
        }
        sendEvent {
            it.onPlaybackStateChanged(this, state)
        }
    }

    override fun onError(player: PlayerAdapter, what: Int, extra: Int, details: String) {
        playingInfo.loadingState = LoadingState.FAILED
        updatePlaybackState(PlaybackState.ERROR)
        GLog.e(TAG, "<onError> ${errorCodeToString(what)} $what $extra, $debugInfo")
        sendEvent {
            it.onError(this, what, extra, NULL_INFO)
        }
    }

    @VisibleForTesting
    fun sendEvent(
        specifyListener: AVController.OnEventListener? = null,
        function: (AVController.OnEventListener) -> Unit
    ) {
        specifyListener?.let {
            sendEventToListener(onEventListener[specifyListener], it, function)
        } ?: let {
            val eventListenerMap = ArrayMap<AVController.OnEventListener, Handler?>().apply {
                synchronized(onEventListener) {
                    putAll(onEventListener)
                }
            }
            for (listenerItem in eventListenerMap) {
                sendEventToListener(listenerItem.value, listenerItem.key, function)
            }
        }
    }

    @VisibleForTesting
    inline fun sendEventToListener(
        handler: Handler?,
        listener: AVController.OnEventListener?,
        crossinline function: (AVController.OnEventListener) -> Unit
    ) {
        listener ?: return
        handler?.post {
            var checkListener: Boolean
            synchronized(onEventListener) {
                checkListener = onEventListener.contains(listener)
            }
            if (checkListener) {
                function(listener)
            }
        } ?: let {
            var checkListener: Boolean
            synchronized(onEventListener) {
                checkListener = onEventListener.contains(listener)
            }
            if (checkListener) {
                function(listener)
            }
        }
    }

    /**
     * Player上报信息的第一处理区域，如果有行为依赖信息回调，在这里处理
     */
    override fun onInfo(player: PlayerAdapter, what: Int, extra: Int, details: String) {

        GLog.i(
            TAG, "<onInfo> player = $player what = ${infoCodeToString(what)} " +
                    "extra = ${infoCodeToString(extra)} details = $details"
        )

        when (what) {
            AVController.INFO_HDR_VIDEO -> {
                dataSource.colorTransfer = extra
                GLog.i(TAG, "<onInfo> videoSource = $dataSource $debugInfo")
            }

            AVController.INFO_BUFFERING_START -> playingInfo.loadingState = LoadingState.LOADING

            AVController.INFO_BUFFERING_END -> playingInfo.loadingState = LoadingState.LOADED
        }

        sendEvent {
            it.onInfo(this, what, extra, details)
        }
    }

    override fun onPrepared(player: PlayerAdapter) {
        if (stopWhenPreparing) {
            GLog.e(TAG, "<onPrepared> player had stopped; $debugInfo")
            stopWhenPreparing = false
            return
        }
        playingInfo.loadingState = LoadingState.LOADED

        playingInfo.run {
            try {
                videoHeight = player.getVideoHeight()
                videoWidth = player.getVideoWidth()
                duration = player.getDuration()
                videoFrameRate = player.getVideoFrameRate()
                isPlaybackAt120Fps = options?.shouldPlaybackAt120Fps ?: false
                GLog.d(TAG, "<onPrepared>  playingInfo=$playingInfo ")
            } catch (e: java.lang.Exception) {
                GLog.e(TAG, "<onPrepared> error $e $debugInfo")
            }
        }
        updatePlaybackState(PlaybackState.PREPARED)
        GLog.i(TAG, "<onPrepared> video is prepared for playback, $debugInfo")
        onInfo(player, AVController.INFO_PLAYING_INFO_READY, AVController.INFO_UNKNOWN, NULL_INFO)
        onInfo(player, AVController.INFO_READY_TO_PLAY, AVController.INFO_UNKNOWN, NULL_INFO)

        if (shouldStartAfterPrepared) {
            startPlayer(targetPositionRange)
            shouldStartAfterPrepared = false
            targetPositionRange = null
        }
    }

    override fun onCompletion(player: PlayerAdapter) {
        onInfo(player, AVController.INFO_PLAY_COMPLETE, AVController.INFO_UNKNOWN, NULL_INFO)
        updatePlaybackState(PlaybackState.COMPLETED)
    }

    override fun onSeekComplete(player: PlayerAdapter) {
        onInfo(player, AVController.INFO_SEEK_COMPLETE, AVController.INFO_UNKNOWN, NULL_INFO)
    }

    override fun onBufferingUpdate(player: PlayerAdapter, percent: Int) {
        bufferProcess = percent
    }

    /**
     * 视频源描述：这里是以文件资源本身的结构区分 ，线上线下等空间等维度不在范围内
     * 职责：描述可以播放的资源文件内的视频必要信息
     */
    sealed class VideoSourceDescriptor {

        /**
         * 常规/常规视频文件描述：MP4,avi等 ，播放器使用标准的解析方式即可解码出对应播放内容
         */
        object NORMAL : VideoSourceDescriptor()

        /**
         * 混合视频文件描述，即：源文件里包含可以播放的内容
         * 例如：OLivePhoto
         */
        class MIX(
            var playType: VideoPlayType? = null, // 设置播放主视频或是小视频
            val offset: Long, // 主视频  距文件开始的位置
            val length: Long, // 主视频  从offset + length 为视频源区域结束位置
            val targetColorSpace: ColorSpace? = null, // 期望渲染时使用的色彩空间
            val subVideoInfo: VideoInfo? = null // 小视频信息，部分实况照片存在小视频在扩展数据中
        ) : VideoSourceDescriptor()
    }

    /**
     * 视频播放类型
     */
    enum class VideoPlayType {
        /**
         * 实况照片中的主视频（3s视频）
         */
        MAIN,

        /**
         * 实况照片中的小视频（扩展数据写入的1s视频）
         */
        SUB
    }

    /**
     * 视频信息描述,用于视频在文件的内部
     */
    data class VideoInfo(
        /**
         * 视频区域开始的位置
         */
        val offset: Long,

        /**
         * 从offset + length 为视频源区域结束位置
         */
        val length: Long
    )
}


