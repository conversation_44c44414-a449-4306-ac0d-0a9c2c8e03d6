/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - OplusUhdrConfig.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/10/31
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * huca<PERSON><PERSON>@Apps.Gallery		2024/10/31		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.oplus.gallery.foundation.codec.extend

import com.oplus.gallery.addon.graphics.UltraHdrInfo
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag

/**
 * oplus自定义的uhdr图的增益信息格式（当前是heif）
 * 由于heif是oppo自己定义的uhdr的格式，所以数据格式不能用google通用的，需要自己定义和实现
 */
class OplusUhdrInfoStruct(byteArray: ByteArray) : ExtendStruct<UltraHdrInfo>(byteArray) {
    var version: Float32 = Float32()
    var ratioMin: Array<Float32> = array(arrayOfNulls<Float32>(INFO_FLOAT_NUM_3))
    var ratioMax: Array<Float32> = array(arrayOfNulls<Float32>(INFO_FLOAT_NUM_3))
    var gamma: Array<Float32> = array(arrayOfNulls<Float32>(INFO_FLOAT_NUM_3))
    var epsilonSdr: Array<Float32> = array(arrayOfNulls<Float32>(INFO_FLOAT_NUM_3))
    var epsilonHdr: Array<Float32> = array(arrayOfNulls<Float32>(INFO_FLOAT_NUM_3))
    var displayRatioSdr: Float32 = Float32()
    var displayRatioHdr: Float32 = Float32()
    var scale: Float32 = Float32()
    var baseImageType: Signed32 = Signed32()

    override fun toData(): UltraHdrInfo? {
        return runCatching {
            UltraHdrInfo(
                ratioMin.get(),
                ratioMax.get(),
                gamma.get(),
                epsilonSdr.get(),
                epsilonHdr.get(),
                displayRatioSdr.get(),
                displayRatioHdr.get(),
                scale.get(),
                baseImageType.get()
            )
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, it) { "toData: is error." }
        }.getOrNull()
    }

    companion object {
        private const val TAG = "OplusUhdrInfoStruct"
        private const val INFO_FLOAT_NUM_3 = 3
    }
}