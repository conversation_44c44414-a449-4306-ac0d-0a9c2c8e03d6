/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - CodecSupportParser.kt
 * Description:
 * Version:
 * Date: 2022/5/19
 * Author: duchengsong@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * duchengsong@Apps.Gallery3D      2022/5/19     1.0     OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/
package com.oplus.gallery.videoeditor.utils

import android.media.MediaCodecInfo
import android.media.MediaCodecList
import android.media.MediaFormat
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.videoeditor.data.VideoSpec
import kotlin.math.max
import kotlin.math.min

/**
 * 查询平台的编解码支持规格
 */
class CodecSupportParser {

    private var cacheCapabilities: MediaCodecInfo.VideoCapabilities? = null
    private var cacheOptions: Options? = null

    /**
     *  获取此option规格下的视频编解码能力
     *  此接口会缓存结果，在同样的option下可以快速返回结果
     */
    private fun getVideoCapabilities(options: Options): MediaCodecInfo.VideoCapabilities? {
        if (options == cacheOptions) {
            return cacheCapabilities
        }
        val codecInfos = MediaCodecList(MediaCodecList.REGULAR_CODECS).codecInfos ?: run {
            GLog.e(TAG, "getVideoCapabilities,options = $options ,mediaCodecList.getCodecInfos() == null")
            return null
        }

        runCatching {
            // 手机可能不支持部分codecType 视频播放，但是可以向下转换, 要再次尝试 codecType 是video/dolby-vision，将视频codecType转成video/hevc
            return querySupportVideoCapabilities(options, codecInfos) ?: convertCodecType(options.mimeType)?.run {
                options.mimeType = this
                querySupportVideoCapabilities(options, codecInfos)
            }
        }.onFailure {
            GLog.e(TAG, "getVideoCapabilities failed")
        }

        GLog.w(TAG, "getVideoCapabilities,options = $options ,no support information found for this format")
        return null
    }

    /**
     *  获取此option规格下的视频编解码能力
     *
     */
    private fun querySupportVideoCapabilities(options: Options, codecInfos: Array<MediaCodecInfo>): MediaCodecInfo.VideoCapabilities? {
        for (codecInfo in codecInfos) {
            if (codecInfo.isEncoder != options.isEncoder) {
                continue
            }
            if (options.isDesireHardwareAccelerated && !codecInfo.isHardwareAccelerated) {
                continue
            }
            val types = codecInfo.supportedTypes ?: let {
                GLog.e(TAG, "getVideoCapabilities,options = $options ,codecInfo.getSupportedTypes() == null")
                return null
            }

            types.forEach { type ->
                if (options.mimeType.equals(type, ignoreCase = true)) {
                    val capabilities = codecInfo.getCapabilitiesForType(options.mimeType).videoCapabilities
                    cacheOptions = options.copy()
                    cacheCapabilities = capabilities
                    GLog.d(
                        TAG, "querySupportVideoCapabilities,options.mimeType = ${options.mimeType} , " +
                            "supportedHeights: ${capabilities?.supportedHeights} supportedWidths ${capabilities?.supportedWidths}" +
                            " supportedFrameRates: ${capabilities?.supportedFrameRates}"
                    )
                    return capabilities
                }
            }
        }
        GLog.w(TAG, "querySupportVideoCapabilities,options = $options ,no support information found for this format")
        return null
    }

    /**
     * 视频 codecType 转换
     */
    private fun convertCodecType(codecType: String): String? {
        return when (codecType) {
            MediaFormat.MIMETYPE_VIDEO_DOLBY_VISION -> {
                GLog.i(TAG, "convertCodecType, phone has no support dolby_vision so make options.mimeType trans to hevc")
                MediaFormat.MIMETYPE_VIDEO_HEVC
            }

            else -> {
                GLog.w(TAG, "convertCodecType,no support codecType to convert correct")
                null
            }
        }
    }

    /**
     * 获取option配置下specification宽高的视频所能支持到的最大fps
     * 返回支持的最大fps，如果没有查询到支持的规格，则返回0fps
     * @param specification
     * @param options
     * @return
     */
    fun getSpecificationMaxFps(
        specification: VideoSpec,
        options: Options
    ): Float {
        val capabilities = getVideoCapabilities(options) ?: return 0f

        val longestEdge: Int = Math.max(specification.width, specification.height)
        val shortestEdge: Int = Math.min(specification.width, specification.height)
        runCatching {
            capabilities.getSupportedFrameRatesFor(longestEdge, shortestEdge)?.let {
                val fps = it.upper.toFloat()
                GLog.d(TAG, "getSpecificationMaxFps $longestEdge, $shortestEdge, fps: $fps")
                return fps
            }
        }.onFailure { GLog.e(TAG, "[getSpecificationMaxFps] unSupport size , return 0 fps") }
        return 0f
    }

    /**
     * 返回option配置下specification宽高的视频的分辨率在当前平台是否支持
     * @param specification
     * @param options
     * @return
     */
    fun isSupportSize(
        specification: VideoSpec,
        options: Options
    ): Boolean? {
        val capabilities = getVideoCapabilities(options) ?: return null

        runCatching {
            val longestEdge: Int = max(specification.width, specification.height)
            val shortestEdge: Int = min(specification.width, specification.height)
            return capabilities.isSizeSupported(longestEdge, shortestEdge)
        }.onFailure { GLog.e(TAG, "[isSupportSize] isSupportSize error") }
        return false
    }

    /**
     * 根据options配置获取平台编解码器能力的信息。
     * @param options 编解码器选项
     */
    fun dumpVideoCapabilities(options: Options): String? {
        val capabilities = getVideoCapabilities(options) ?: return null
        return "query options: $options, " +
            "supportedWidths: ${capabilities.supportedWidths} (alignment: ${capabilities.widthAlignment}), " +
            "supportedHeights: ${capabilities.supportedHeights} (alignment: ${capabilities.heightAlignment}), " +
            "supportedFrameRates: ${capabilities.supportedFrameRates}"
    }

    /**
     * 平台是否支持指定格式和规格的硬件编解码能力
     *
     * @param mimetype 视频编解码格式
     * @param specification 视频规格
     * @return 是否支持
     */
    fun isPlatformSupportCodec(mimetype: String, specification: VideoSpec): Boolean {
        val decodeOption = Options(mimetype, false)
        val encodeOption = Options(mimetype, true)
        val isSupport = CodecSupportParser().let {
            (it.isSupportSize(specification, decodeOption) == true)
                && (it.isSupportSize(specification, encodeOption) == true)
        }
        GLog.d(TAG, LogFlag.DL, "[isPlatformSupportCodec] isSupport:$isSupport")
        return isSupport
    }

    /**
     * @param mimeType 视频文件媒体类型
     * @param isEncoder 是否查询编码器规格，为false时查询解码器规格
     * @param isDesireHardwareAccelerated 是否要求此编解码器支持硬件加速，true则会过滤掉不支持硬件加速的编解码器，false则不做处理
     */
    data class Options(
        var mimeType: String,
        var isEncoder: Boolean = false,
        var isDesireHardwareAccelerated: Boolean = false
    )


    companion object {
        private const val TAG = "CodecSupportParser"
    }
}