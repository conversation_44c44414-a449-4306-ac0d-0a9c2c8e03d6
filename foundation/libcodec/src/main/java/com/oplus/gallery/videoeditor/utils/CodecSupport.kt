/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 * File: - CodecSupport.kt
 * Description:
 * Version: 1.0
 * Date: 2025/08/04
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>    <desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery        2025/08/04      1.0        OPLUS_ARCH_EXTENDS
 ********************************************************************************/

package com.oplus.gallery.videoeditor.utils

import android.media.MediaCodecInfo
import android.media.MediaCodecInfo.VideoCapabilities.PerformancePoint.FHD_120
import android.media.MediaCodecInfo.VideoCapabilities.PerformancePoint.FHD_240
import android.media.MediaCodecInfo.VideoCapabilities.PerformancePoint.FHD_30
import android.media.MediaCodecInfo.VideoCapabilities.PerformancePoint.FHD_60
import android.media.MediaCodecInfo.VideoCapabilities.PerformancePoint.HD_120
import android.media.MediaCodecInfo.VideoCapabilities.PerformancePoint.HD_240
import android.media.MediaCodecInfo.VideoCapabilities.PerformancePoint.HD_30
import android.media.MediaCodecInfo.VideoCapabilities.PerformancePoint.HD_60
import android.media.MediaCodecInfo.VideoCapabilities.PerformancePoint.UHD_120
import android.media.MediaCodecInfo.VideoCapabilities.PerformancePoint.UHD_240
import android.media.MediaCodecInfo.VideoCapabilities.PerformancePoint.UHD_30
import android.media.MediaCodecInfo.VideoCapabilities.PerformancePoint.UHD_60
import android.media.MediaCodecList
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.videoeditor.data.VideoOptions
import com.oplus.gallery.videoeditor.data.VideoSpec
import kotlin.math.max
import kotlin.math.min

object CodecSupport {
    private const val TAG = "CodecSupport"
    private var codecInfos: Array<MediaCodecInfo>? = null

    val PERFORMANCE_POINTS = listOf(
        UHD_240,
        UHD_120,
        UHD_60,
        UHD_30,
        FHD_240,
        FHD_120,
        FHD_60,
        FHD_30,
        HD_240,
        HD_120,
        HD_60,
        HD_30,
    )

    /**
     * 保证获取到的codecInfos不为null
     * @return Array<MediaCodecInfo>
     */
    private fun ensureCodecInfos(): Array<MediaCodecInfo> {
        return codecInfos ?: MediaCodecList(MediaCodecList.REGULAR_CODECS).codecInfos.also { codecInfos = it }
    }

    /**
     * 获取平台视频能力
     * @param videoOptions 视频选项
     * @return MediaCodecInfo.VideoCapabilities?
     */
    private fun getVideoCapabilities(videoOptions: VideoOptions): MediaCodecInfo.VideoCapabilities? {
        return ensureCodecInfos().find {
            it.name == videoOptions.mimeType &&
                it.isEncoder == videoOptions.isEncoder &&
                if (videoOptions.isDesireHardwareAccelerated) it.isHardwareAccelerated else true
        }?.getCapabilitiesForType(videoOptions.mimeType)?.videoCapabilities
    }

    fun getPerformancePoints(videoOptions: VideoOptions): List<MediaCodecInfo.VideoCapabilities.PerformancePoint>? {
        return getVideoCapabilities(videoOptions)?.supportedPerformancePoints
    }

    /**
     * 获取平台支持的最大分辨率
     * @return Pair<Int, Int>?
     */
    fun getMaxResolution(videoOptions: VideoOptions): Pair<Int, Int>? {
        return getVideoCapabilities(videoOptions)?.run { supportedWidths.upper to getSupportedHeightsFor(supportedWidths.upper).upper }
    }

    /**
     * 获取平台支持的最大帧率
     * @return Int?
     */
    fun getMaxFps(videoOptions: VideoOptions): Int? {
        return getVideoCapabilities(videoOptions)?.supportedFrameRates?.upper
    }

    /**
     * 判断平台是否支持指定的分辨率和帧率
     * @return Boolean
     */
    fun isSupport(spec: VideoSpec, videoOptions: VideoOptions): Boolean {
        return getVideoCapabilities(videoOptions)?.areSizeAndRateSupported(
            max(spec.width, spec.height),
            min(spec.width, spec.height),
            spec.fps.toDouble()
        ) ?: false
    }

    /**
     * 平台是否支持指定格式和规格的硬件编解码能力
     *
     * @param mimetype 视频编解码格式
     * @param spec 视频规格
     * @return 是否支持
     */
    fun isSupportCodec(mimetype: String, spec: VideoSpec): Boolean {
        val decodeOption = VideoOptions(mimetype, false)
        val encodeOption = VideoOptions(mimetype, true)
        return isSupport(spec, decodeOption) && isSupport(spec, encodeOption).apply {
            GLog.d(TAG, LogFlag.DL) { "isPlatformSupportCodec: mimetype:$mimetype, spec:$spec, result:$this" }
        }
    }

    /**
     * 根据options配置获取平台编解码器能力的信息。
     * @param options 编解码器选项
     */
    fun dumpVideoCapabilities(options: VideoOptions): String? {
        val capabilities = getVideoCapabilities(options) ?: return null
        return "dumpVideoCapabilities: $options, " +
            "supportedWidths: ${capabilities.supportedWidths} (alignment: ${capabilities.widthAlignment}), " +
            "supportedHeights: ${capabilities.supportedHeights} (alignment: ${capabilities.heightAlignment}), " +
            "supportedFrameRates: ${capabilities.supportedFrameRates}"
    }
}
