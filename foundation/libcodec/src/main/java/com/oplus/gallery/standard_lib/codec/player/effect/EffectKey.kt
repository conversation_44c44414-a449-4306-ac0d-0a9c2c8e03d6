/*********************************************************************************
 ** Copyright (C), 2022-2032, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : EffectKey.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2025/07/30
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>               2025/07/30  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.standard_lib.codec.player.effect

/**
 * TBL播放特效的 key
 */
object EffectKey {
    /**
     * 缩放特效
     */
    const val TBL_SCALE_EFFECT = "TBL_SCALE_EFFECT"

    /**
     *  自定义裁剪特效，指定视频大小和裁切范围
     */
    const val TBL_CUSTOM_CROP_EFFECT = "TBL_CUSTOM_CROP_EFFECT"

    /**
     * eis数据中的裁切特效
     */
    const val TBL_CROP_EFFECT = "TBL_CROP_EFFECT"

    /**
     * 补帧特效
     */
    const val TBL_FRAME_FILL_EFFECT = "TBL_FRAME_FILL_EFFECT"

    /**
     * FOV特效
     */
    const val TBL_FOV_EFFECT = "TBL_FOV_EFFECT"

    /**
     * 显示特效,在不显示的内容位置填充黑色
     */
    const val TBL_EDGE_EFFECT = "TBL_EDGE_EFFECT"

    /**
     * 提亮特效
     */
    const val TBL_CUSTOM_EFFECT_TRANSFORM = "TBL_CUSTOM_EFFECT_TRANSFORM"
}
