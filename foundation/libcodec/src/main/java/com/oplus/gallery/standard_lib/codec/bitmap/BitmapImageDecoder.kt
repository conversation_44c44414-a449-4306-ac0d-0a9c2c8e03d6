/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File        :  - BitmapImageDecoder.java
 ** Description : XXXXXXXXXXXXXXXXXXXXX.
 ** Version     : 1.0
 ** Date        : 2020/10/08
 ** Author      : YongQ<PERSON>.Liu@Apps.Gallery3D
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                 <data>      <version>  <desc>
 **  YongQi.Liu@Apps.Gallery3D  2020/10/08  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.standard_lib.codec.bitmap

import android.graphics.BitmapFactory
import com.oplus.gallery.foundation.util.display.ColorModelManager
import com.oplus.gallery.standard_lib.codec.IImageDecoder
import com.oplus.gallery.standard_lib.codec.ImageData
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.LogFlag
import java.io.FileDescriptor
import java.io.InputStream

class BitmapImageDecoder : IImageDecoder {
     companion object {
        private const val TAG = "BitmapImageDecoder"
    }

    override fun createDecoder() {
        //for bitmap, if use android ImageDecoder, please implement it
    }

    override fun destroyDecoder() {
        //for bitmap, if use android ImageDecoder, please implement it
    }

    override fun isYuvImage(fd: Int): Boolean {
        return false
    }

    override fun decode(fd: FileDescriptor): ImageData? {
        return decode(fd, null)
    }

    override fun decode(fd: FileDescriptor, options: BitmapFactory.Options?): ImageData? {
        val time = System.currentTimeMillis()
        val bitmap = BitmapFactory.decodeFileDescriptor(fd, null, options)
        if ((options != null) && options.inJustDecodeBounds) {
            return null
        }
        if (bitmap == null) {
            GLog.e(TAG, LogFlag.DL) { "decode by fileDescriptor bitmap is null" }
            return null
        }
        if (GProperty.LOG_OPEN_DEBUG_DECODE) {
            GLog.d(TAG, LogFlag.DL) { "decode by FileDescriptor cost time = ${System.currentTimeMillis() - time}" }
        }
        ColorModelManager.adjustBitmapColorSpaceIfNeeded(bitmap)
        return ImageData(bitmap)
    }

    override fun decode(inputStream: InputStream): ImageData? {
        return decode(inputStream, null)
    }

    override fun decode(inputStream: InputStream, options: BitmapFactory.Options?): ImageData? {
        val time = System.currentTimeMillis()
        val bitmap = BitmapFactory.decodeStream(inputStream, null, options)
        if ((options != null) && options.inJustDecodeBounds) {
            return null
        }
        if (bitmap == null) {
            GLog.e(TAG, LogFlag.DL) { "decode by inputStream bitmap is null" }
            return null
        }
        if (GProperty.LOG_OPEN_DEBUG_DECODE) {
            GLog.d(TAG, LogFlag.DL) { "decode by InputStream cost time = ${System.currentTimeMillis() - time}" }
        }
        ColorModelManager.adjustBitmapColorSpaceIfNeeded(bitmap)
        return ImageData(bitmap)
    }

    override fun decode(filePath: String): ImageData? {
        return decode(filePath, null)
    }

    override fun decode(filePath: String, options: BitmapFactory.Options?): ImageData? {
        val time = System.currentTimeMillis()
        val bitmap = BitmapFactory.decodeFile(filePath, options)
        if ((options != null) && options.inJustDecodeBounds) {
            return null
        }
        if (bitmap == null) {
            GLog.e(TAG, LogFlag.DL) { "decode by filePath bitmap is null" }
            return null
        }
        if (GProperty.LOG_OPEN_DEBUG_DECODE) {
            GLog.d(TAG, LogFlag.DL) { "decode by filePath cost time = ${System.currentTimeMillis() - time}" }
        }
        ColorModelManager.adjustBitmapColorSpaceIfNeeded(bitmap)
        return ImageData(bitmap)
    }

    override fun getDecoderName(): String = TAG
}