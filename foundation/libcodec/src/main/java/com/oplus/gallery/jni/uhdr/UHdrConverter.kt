/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - UHdrConverter
 ** Description: Hdr转换器，负责将LHdr数据转换成UHdr数据
 **
 ** Version: 1.0
 ** Date: 2023/12/01
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D  2023/12/01  1.0        build this module
 *********************************************************************************/

package com.oplus.gallery.jni.uhdr

import android.graphics.Bitmap
import com.oplus.gallery.addon.graphics.UltraHdrInfo
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.standard_lib.codec.drawable.HdrImageDrawable
import com.oplus.gallery.standard_lib.codec.drawable.UHdrMetadataPack
import com.oplus.gallery.standard_lib.util.loadLibrary.LoadLibraryTool

/**
 * UHdr转换器，负责将LHdr数据转换成UHdr数据
 * 此类路径须与libUhdrUtils-jni.so内声明的一致
 */
object UHdrConverter {
    private const val TAG = "UHdrConverter"

    init {
        LoadLibraryTool.loadLibrary("UhdrUtils-jni")
    }

    /**
     * 将LHdr转换为UHdr的Drawable对象
     * @param source 原始图片
     * @param lHdrImageDrawable LHdr的Drawable对象
     * @return 返回UHdr的Drawable对象
     */
    fun convert(
        source: Bitmap,
        lHdrImageDrawable: HdrImageDrawable?,
    ): HdrImageDrawable? {
        lHdrImageDrawable ?: run {
            GLog.e(TAG, "convert lHdrImageDrawable is null.")
            return null
        }
        val lHdrMetaData = lHdrImageDrawable.metadata.metadata as? ByteArray ?: run {
            GLog.e(TAG, "convert lHdrMetaData ${lHdrImageDrawable.metadata.metadata}")
            return null
        }
        val lHdrMask = lHdrImageDrawable.grayImage
        val gainMapMeta = GainMapMeta()
        val uHdrBitmap = BitmapUtils.createBitmapWithErrorCatchRetry(lHdrMask.width, lHdrMask.height, lHdrMask.config)
        if (lHdrMask.isRecycled || source.isRecycled || (uHdrBitmap == null) || uHdrBitmap.isRecycled) {
            GLog.d(TAG, LogFlag.DL) { "convert: failed, lHdrMask = $lHdrMask , source = $source , uHdrBitmap = $uHdrBitmap" }
            return null
        }
        runCatching {
            val time = System.currentTimeMillis()
            GLog.d(TAG, LogFlag.DL) {
                "convert: lHdrMetaData size = ${lHdrMetaData.size} , gainMapMeta = $gainMapMeta , " +
                    "lHdrMask.isRecycled = ${lHdrMask.isRecycled} , source.isRecycled = ${source.isRecycled} , " +
                    "uHdrBitmap.isRecycled = ${uHdrBitmap.isRecycled}"
            }
            val code = mask2map(lHdrMetaData, gainMapMeta, lHdrMask, source, uHdrBitmap)
            GLog.d(TAG, LogFlag.DL) {
                "convert code:$code, cost time:${System.currentTimeMillis() - time}, source:${source.width}x${source.height}" +
                    " uHdrBitmap:${uHdrBitmap.width}x${uHdrBitmap.height}, $gainMapMeta"
            }
            return HdrImageDrawable(uHdrBitmap, UHdrMetadataPack(gainMapMeta.toUltraHdrInfo()))
        }.onFailure {
            GLog.e(TAG, "convert: ${it.message}")
        }
        return null
    }

    private fun GainMapMeta.toUltraHdrInfo(): UltraHdrInfo {
        return UltraHdrInfo(
            ratioMin,
            ratioMax,
            gamma,
            epsilonSdr,
            epsilonHdr,
            displayRatioSdr,
            displayRatioHdr,
            scale
        )
    }

    /**
     * 转换处理：mask2map
     * 目前人像景深Uhdr需求尚未完成开发，此接口调整为本地接口
     * @param metaDataLHDR  保存在LocalHdr图像中的原始数据
     * @param gainMapMeta  UHdr meta（预留空间存放）
     * @param bitmapMask    LHdr  的mask图
     * @param bitmapSdr     大图
     * @param bitmapMap     UHdr 的map图（预留空间存放）
     * @return Int: process success return 0, process fail return -1
     *               转换正常返回0，处理异常返回-1
     */
    private external fun mask2map(
        metaDataLHDR: ByteArray,
        gainMapMeta: GainMapMeta,
        bitmapMask: Bitmap,
        bitmapSdr: Bitmap,
        bitmapMap: Bitmap
    ): Int
}