/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - ViewWrapper.kt
 * Description: oplus view 的封装类，用于防呆
 * Version: 1.0
 * Date: 2022/12/24
 * Author: chenzengxin@Apps.Gallery3D
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                   <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * chenzengxin@Apps.Gallery3D 2022/12/24     1.0              create
 **************************************************************************************************/
package com.oplus.gallery.addon.wrapper.view

import com.oplus.compat.view.ViewNative
import com.oplus.gallery.addon.utils.SLog
import com.oplus.gallery.addon.utils.VersionHelper
import com.oplus.wrapper.view.View

object ViewWrapper {

    private const val TAG = "ViewWrapper"

    /**
     * Computes whether this view is visible to the user. Such a view is
     * attached, visible, all its predecessors are visible, it is not clipped
     * entirely by its predecessors, and has an alpha greater than zero.
     *
     * @return Whether the view is visible on the screen.
     * @since 26.1
     */
    @Suppress("MagicNumber")
    @JvmStatic
    fun isVisibleToUser(view: android.view.View): Boolean {
        return runCatching {
            if (VersionHelper.isShouldUseOSDKApi(26, 1)) {
                View(view).isVisibleToUser
            } else {
                ViewNative.isVisibleToUser(view)
            }
        }.onFailure {
            SLog.e(TAG, "isVisibleToUser, e:", it)
        }.getOrDefault(false)
    }
}