/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - BrightnessScaleListenerWrapper.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2023/02/28
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2023/02/28    1.0                create
 ******************************************************************************/

package com.oplus.gallery.addon.view

import com.oplus.gallery.addon.utils.SLog
import java.lang.reflect.InvocationHandler
import java.lang.reflect.Method
import java.lang.reflect.Proxy

/**
 * 封装[com.oplus.view.IBrightnessScaleListener]，主要用于接受提亮状态信息的上报。如提亮是否有效果，提亮倍数等。
 * Marked by zhangwenming 该接口目前定义与framework中，尚未集成到OSDK中，因此需要反射实现。
 */
internal class BrightnessScaleListenerWrapper private constructor(
    private val outerListener: IBrightnessScaleListener
) : InvocationHandler {
    override fun invoke(proxy: Any?, method: Method?, args: Array<out Any>?): Any? {
        when (method?.name) {
            /**
             * 目前方法[METHOD_ON_BRIGHTNESS_SCALE]在framework中的定义如下
             * ```
             * public void onBrightnessScale(long timestampNanos, long physicalDisplayId, int toggle, float scale, boolean scaleApplyImmediately)
             * ```
             */
            METHOD_ON_BRIGHTNESS_SCALE -> {
                args?.also { argsArray ->
                    if (argsArray.size >= ARGS_COUNT_ON_BRIGHTNESS_SCALE) {
                        val toggle = argsArray[ARGS_INDEX_TOGGLE_IN_ON_BRIGHTNESS_SCALE] as? Int
                        val scale = argsArray[ARGS_INDEX_SCALE_IN_ON_BRIGHTNESS_SCALE] as? Float
                        if ((toggle != null) && (scale != null)) {
                            // 只有参数数量、参数类型都对应，才允许回调到业务端。
                            outerListener.onBrightnessScale(toggle, scale)
                        } else {
                            SLog.e(
                                TAG, "[invoke], method $METHOD_ON_BRIGHTNESS_SCALE args is invalid." +
                                    "toggle ${argsArray[ARGS_INDEX_TOGGLE_IN_ON_BRIGHTNESS_SCALE]}," +
                                    "scale ${argsArray[ARGS_INDEX_SCALE_IN_ON_BRIGHTNESS_SCALE]}"
                            )
                        }
                    } else {
                        SLog.e(TAG, "[invoke], method $METHOD_ON_BRIGHTNESS_SCALE, args size is ${argsArray.size}, not valid.")
                    }
                } ?: also {
                    SLog.e(TAG, "[invoke], method $METHOD_ON_BRIGHTNESS_SCALE args are null.")
                }
            }
        }

        return null
    }

    /**
     * 提亮功能相关的状态发生变化的监听
     */
    fun interface IBrightnessScaleListener {
        /**
         * 提亮功能相关的状态发生变化的监听，如是否有提亮，提亮时背光的倍数
         * 目前提亮相关功能有Local HDR提亮和杜比视频的提亮。
         *
         * @param toggle 屏幕是否有被提亮。
         * @param scale 如果屏幕有被提亮，屏幕提亮后的亮度相比于基准亮度的scale值。
         */
        fun onBrightnessScale(toggle: Int, scale: Float)
    }

    companion object {
        private const val TAG = "BrightnessScaleListenerWrapper"
        private const val CLASS_NAME_BRIGHTNESS_SCALE_LISTENER = "com.oplus.view.IBrightnessScaleListener"

        /**
         * 定义[CLASS_NAME_BRIGHTNESS_SCALE_LISTENER]中方法[METHOD_ON_BRIGHTNESS_SCALE]相关的参数信息，目前该方法在framework定义如下
         * ```
         * public void onBrightnessScale(long timestampNanos, long physicalDisplayId, int toggle, float scale, boolean scaleApplyImmediately)
         * ```
         */
        private const val METHOD_ON_BRIGHTNESS_SCALE = "onBrightnessScale"

        // 方法[METHOD_ON_BRIGHTNESS_SCALE] 参数的最小个数
        private const val ARGS_COUNT_ON_BRIGHTNESS_SCALE = 5

        // 方法[METHOD_ON_BRIGHTNESS_SCALE] 参数toggle在参数列表中的index值
        private const val ARGS_INDEX_TOGGLE_IN_ON_BRIGHTNESS_SCALE = 2

        // 方法[METHOD_ON_BRIGHTNESS_SCALE] 参数scale在参数列表中的index值
        private const val ARGS_INDEX_SCALE_IN_ON_BRIGHTNESS_SCALE = 3

        /**
         * [CLASS_NAME_BRIGHTNESS_SCALE_LISTENER]的class对象
         */
        val clazzIBrightnessScaleListener by lazy {
            kotlin.runCatching {
                Class.forName(CLASS_NAME_BRIGHTNESS_SCALE_LISTENER)
            }.onFailure {
                SLog.e(TAG, "Class DisplayEventReceiver init fail with message ${it.message}")
            }.getOrNull()
        }

        /**
         * 创建类[CLASS_NAME_BRIGHTNESS_SCALE_LISTENER]的动态代理，创建后的实例，需要注册到[DisplayEventReceiverWrapper.setBrightnessScaleListener]中
         * @param listener IBrightnessScaleListener
         * @return Any?
         */
        @JvmStatic
        fun createBrightnessScaleListenerInstance(listener: IBrightnessScaleListener): Any? {
            return clazzIBrightnessScaleListener?.let { clazz ->
                val invocationHandler = BrightnessScaleListenerWrapper(listener)
                Proxy.newProxyInstance(clazz.classLoader, arrayOf(clazz), invocationHandler)
            } ?: let {
                SLog.e(TAG, "[createBrightnessScaleListenerInstance], class IBrightnessScaleListener is null")
                null
            }
        }
    }
}