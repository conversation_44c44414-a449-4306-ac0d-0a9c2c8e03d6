/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - ConfigurationWrapper.kt
 * Description: OplusBaseConfiguration 的封装类，用于防呆
 * Version: 1.0
 * Date: 2022/11/19
 * Author: chenzengxin@Apps.Gallery3D
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                   <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * chenzengxin@Apps.Gallery3D 2022/11/19     1.0              create
 **************************************************************************************************/
package com.oplus.gallery.addon.content.res

import android.content.res.Configuration
import android.content.res.OplusBaseConfiguration
import com.oplus.compat.content.res.ConfigurationNative
import com.oplus.gallery.addon.utils.SLog
import com.oplus.gallery.addon.utils.VersionHelper
import com.oplus.util.OplusTypeCastingHelper

object ConfigurationWrapper {

    private const val TAG = "ConfigurationWrapper"
    private const val DEFAULT_FLIP_FONT = -1

    @JvmStatic
    fun getFlipFont(configuration: Configuration?): Int {
        return runCatching {
            if (VersionHelper.isShouldUseOSDKApi()) {
                val oplusBaseConfiguration = OplusTypeCastingHelper.typeCasting(OplusBaseConfiguration::class.java, configuration)
                val extraConfiguration = oplusBaseConfiguration.oplusExtraConfiguration
                extraConfiguration.mFlipFont
            } else {
                configuration?.let {
                    ConfigurationNative.getFlipFont(it)
                } ?: DEFAULT_FLIP_FONT
            }
        }.onFailure {
            SLog.e(TAG, "getFlipFont, e:", it)
        }.getOrDefault(DEFAULT_FLIP_FONT)
    }
}