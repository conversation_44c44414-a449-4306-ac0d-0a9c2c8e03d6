/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - GalleryOsenseResEventClient.kt
 ** Description : 查杀的client
 ** Version: 1.0
 ** Date:  2024/03/28
 ** Author: <EMAIL>
 ** TAG: init
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** v-maqi<PERSON>@oppo.com		       2024/03/28    1.0		  init
 *********************************************************************************/
package com.oplus.gallery.addon.osense

import android.os.Bundle
import com.oplus.gallery.addon.utils.OSVersionUtils
import com.oplus.gallery.addon.utils.SLog
import com.oplus.osense.OsenseResEventClient
import com.oplus.osense.eventinfo.EventConfig
import com.oplus.osense.eventinfo.EventType
import com.oplus.osense.eventinfo.OsenseConfig
import java.util.Arrays

/**
 * 通过系统的addon sdk 来监听相册被杀死的情况
 * 死亡回调事件的接入：https://odocs.myoas.com/docs/WlArzB7Jj6Fw2aA2
 * 从14.1.0版本之后才能正常调用，低于这个版本调用无法监听回调
 */
class GalleryOsenseResEventClient {
    private val osenseClient by lazy {
        OsenseResEventClient.getInstance()
    }
    private var osenseEventCallback: GalleryOsenseEventCallback? = null

    /**
     * 注册死亡回调事件
     * @param callback 相册进程死亡时的回调
     */
    fun registerEventCallback(callback: ((reason: String) -> Unit)? = null) {
        kotlin.runCatching {
            if (OSVersionUtils.oplusOsVersion < OSVersionUtils.OPLUS_OS_14_1_0) {
                SLog.d(TAG, "registerEventCallback the OplusOS version is low OPLUS_OS_14_1_0")
                return
            }
            osenseEventCallback = GalleryOsenseEventCallback(callback)
            var ret = -1
            val eventConfig = EventConfig()
            val extra = Bundle()
            val clearTypes = ArrayList(mutableListOf(MEMORY_GUARD2, KS_CPULIMIT_CPUHIGHLOAD))
            extra.putStringArrayList(CLEARTYPES, clearTypes)
            val osenseEvents = HashSet<OsenseConfig>().apply {
                add(OsenseConfig(EventType.EVENT_RES_TERMINATE, extra))
            }
            eventConfig.setOsenseConfigSet(osenseEvents)
            ret = osenseClient.registerEventCallback(osenseEventCallback, eventConfig)
            SLog.d(
                TAG, "resgisterEventCallback ret: " + ret + " eventConfig: " + eventConfig.toString()
                        + " mEventSet: " + Arrays.toString(eventConfig.eventSet.toTypedArray())
            )
        }.onFailure {
            SLog.e(TAG, "resgisterEventCallback exception: $it")
        }
    }

    /**
     * 取消死亡回调事件
     */
    fun unregisterEventCallback() {
        kotlin.runCatching {
            if (OSVersionUtils.oplusOsVersion < OSVersionUtils.OPLUS_OS_14_1_0) {
                SLog.d(TAG, "unregisterEventCallback the OplusOS version is low OPLUS_OS_14_1_0")
                return
            }
            var ret = -1
            osenseEventCallback?.let {
                ret = osenseClient.unregisterEventCallback(it)
                SLog.d(TAG, "unregisterEventCallback ret: $ret")
            }
        }.onFailure {
            SLog.e(TAG, "unregisterEventCallback exception: $it")
        }
    }

    companion object {
        private const val TAG = "GalleryOsenseResEventClient"

        //低内存查杀
        private const val MEMORY_GUARD2 = "memory_guard2"

        //高负载查杀
        private const val KS_CPULIMIT_CPUHIGHLOAD = "ks_cpulimit_cpuhighload"
        private const val CLEARTYPES = "clearTypes"
    }
}