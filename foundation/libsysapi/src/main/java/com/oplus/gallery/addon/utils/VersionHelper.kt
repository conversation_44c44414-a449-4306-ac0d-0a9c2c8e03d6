/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - Utils.kt
 * Description: 版本工具类，由于无法依赖 libutils 模块
 * Version: 1.0
 * Date: 2022/11/19
 * Author: chenzengxin@Apps.Gallery3D
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                   <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * chenzengxin@Apps.Gallery3D 2022/11/19     1.0              create
 **************************************************************************************************/
package com.oplus.gallery.addon.utils

import android.os.Build
import com.oplus.gallery.addon.os.BuildWrapper
import com.oplus.os.OplusBuild

object VersionHelper {
    private const val TAG = "VersionHelper"
    private const val INVALID_VERSION = -1

    /**
     * version变量调用频繁，整体初始化耗时300us，使用缓存
     */
    val isAtLeastAndroidT: Boolean by lazy {
        Build.VERSION.SDK_INT > Build.VERSION_CODES.S_V2
    }
    private val oplusSdkVersion: Int by lazy {
        if (isAtLeastAndroidT) {
            BuildWrapper.getOplusSdkVersion()
        } else {
            INVALID_VERSION
        }
    }
    private val oplusSubSdkVersion: Int by lazy {
        if (isAtLeastAndroidT) {
            BuildWrapper.getOplusSubSdkVersion()
        } else {
            INVALID_VERSION
        }
    }

    /**
     * 是否大于等于 android R 版本
     */
    @androidx.annotation.ChecksSdkIntAtLeast(api = Build.VERSION_CODES.R)
    fun isAtLeastAndroidR(): Boolean {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.R
    }

    /**
     * 是否大于等于 android U 版本
     */
    fun isAtLeastAndroidU(): Boolean {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE
    }

    /**
     * 是否等于 android T 版本
     */
    fun isAndroidT(): Boolean {
        return Build.VERSION.SDK_INT == Build.VERSION_CODES.TIRAMISU
    }


    /**
     * 是否等于 android U 版本
     */
    fun isAndroidU(): Boolean {
        return Build.VERSION.SDK_INT == Build.VERSION_CODES.UPSIDE_DOWN_CAKE
    }

    /**
     *  判断是否应该使用 osdk 的接口，如果不经过判断直接使用 osdk 接口，在低版本中找不到接口，会 crash 。
     *  必须要知道自己使用的接口，是在什么版本上加入的 osdk ，然后做出判断，在xx版本以上才能使用。
     * （被后缀为Wrapper的类封装起来的接口有try catch）
     *
     * osdk 版本号为 version.subversion 的形式，比如 26.1
     * 26 为 ColorOS api版本
     * 11 为 osdk 的迭代版本
     * 详情看 https://noah.myoas.com/micro-app/sdk/sdkdocs-docsdetails-sdkdocsbrowse?id=764049891840104481
     * &sdkSource=6&sdkName=CompatCheckUtil
     *
     *  @return 是否应该使用 OSDK
     */
    fun isShouldUseOSDKApi(apiVersion: Int = INVALID_VERSION, subApiVersion: Int = INVALID_VERSION): Boolean {
        // 小于T，不使用 osdk
        if (!isAtLeastAndroidT) {
            return false
        }

        /* 如果 osdk 相关版本号非法，此时直接使用 android T 版本来判断是否应该使用 osdk
        osdk 里的接口基本都会使用 @since apiVersion.subApiVersion 来标注是什么版本加入的 osdk
        如果接口没有定义 apiVersion 和 subApiVersion ，此时通过 isAtLeastAndroidT 来判断 */
        if (apiVersion == INVALID_VERSION
            || subApiVersion == INVALID_VERSION
            || oplusSdkVersion == INVALID_VERSION
            || oplusSubSdkVersion == INVALID_VERSION
        ) {
            return isAtLeastAndroidT
        }

        /*
        大于T，当前系统版本是否大于 osdk 接口定义的版本，由于版本号是整数，需要这样判断：
        如 26.10， (apiVersion==26 && subApiVersion>=10) || apiVersion>27
         */
        return ((oplusSdkVersion == apiVersion) && (oplusSubSdkVersion >= subApiVersion))
                || (oplusSdkVersion > apiVersion)
    }

    /**
     * 是否大于等于 os15 版本
     */
    fun isAtLeastOs15(): Boolean {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
            return false
        }
        return OplusBuild.VERSION.SDK_VERSION >= OplusBuild.OsdkVersionCodes.OS_15_0_0
    }
}