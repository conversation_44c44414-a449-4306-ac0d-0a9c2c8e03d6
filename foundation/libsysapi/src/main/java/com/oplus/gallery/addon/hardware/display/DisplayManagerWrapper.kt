/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - DisplayManagerWrapper.kt
 * Description: oplus DisplayManager 的封装类，用于防呆
 * Version: 1.0
 * Date: 2022/11/19
 * Author: chenzengxin@Apps.Gallery3D
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                   <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * chenzengxin@Apps.Gallery3D 2022/11/19     1.0              create
 **************************************************************************************************/
package com.oplus.gallery.addon.hardware.display

import android.annotation.TargetApi
import android.content.Context
import android.hardware.display.DisplayManager
import android.hardware.display.OplusosDisplayManager
import com.oplus.gallery.addon.utils.SLog
import com.oplus.compat.hardware.display.DisplayManagerNative as CompatDisplayManagerNative
import com.oplus.gallery.addon.utils.VersionHelper
import com.oplusx.sysapi.hardware.display.DisplayManagerNative
import com.oplus.wrapper.hardware.display.DisplayManager as OplusDisplayManager

object DisplayManagerWrapper {
    const val TAG = "DisplayManagerWrapper"

    /**
     * Temporarily sets the auto brightness adjustment factor.
     * adjustment The [adjustment] factor from -1.0 to 1.0.
     *
     * 1：当os版本大于等于15时，调用addon的下沉接口
     * 2：如果os版本小于15，使用原有的接口。
     * Requires the {@link android.Manifest.permission#CONTROL_DISPLAY_BRIGHTNESS} permission.
     * CompatDisplayManagerNative#setTemporaryAutoBrightnessAdjustment 的注释上说需要这个权限，
     * 但是1.相册没有该权限也可以 2.sysapi下的这个接口不需要该权限。
     */
    @JvmStatic
    fun setTemporaryAutoBrightnessAdjustment(adjustment: Float) {
        runCatching {
            if (VersionHelper.isAtLeastOs15()) {
                OplusosDisplayManager().setTemporaryAutoBrightnessAdjustment(adjustment)
            } else if (VersionHelper.isShouldUseOSDKApi()) {
                DisplayManagerNative.setTemporaryAutoBrightnessAdjustment(adjustment)
            } else {
                CompatDisplayManagerNative.setTemporaryAutoBrightnessAdjustment(adjustment)
            }
        }.onFailure {
            SLog.e(TAG, "setTemporaryAutoBrightnessAdjustment, e:", it)
        }
    }

    /**
     *  Get the state of the currently active display.
     *
     *  @return One of: [android.hardware.display.WifiDisplayStatus.DISPLAY_STATE_NOT_CONNECTED]=0,
     *  [android.hardware.display.WifiDisplayStatus.DISPLAY_STATE_CONNECTING]=1,
     *  or [android.hardware.display.WifiDisplayStatus.DISPLAY_STATE_CONNECTED]=2.
     *
     *  CompatDisplayManagerNative.getActiveDisplayStatus() 限制在 target api 30 以上才能使用
     *
     *  @since 27.11
     */
    @Suppress("MagicNumber")
    @TargetApi(30)
    @JvmStatic
    fun getActiveDisplayStatus(context: Context): Int? {
        return runCatching {
            if (VersionHelper.isShouldUseOSDKApi(27, 11)) {
                (context.getSystemService(Context.DISPLAY_SERVICE) as? DisplayManager)?.let { originDisplayManager ->
                    OplusDisplayManager(originDisplayManager).wifiDisplayStatus?.activeDisplayState
                }
            } else if (VersionHelper.isAtLeastAndroidR()) {
                CompatDisplayManagerNative.getActiveDisplayStatus()
            } else {
                // R以下不支持此功能
                null
            }
        }.onFailure {
            SLog.e(TAG, "getActiveDisplayStatus, e:", it)
        }.getOrNull()
    }
}