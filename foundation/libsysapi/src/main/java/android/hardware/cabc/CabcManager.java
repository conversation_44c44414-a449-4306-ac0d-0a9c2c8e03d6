/*
 * Copyright (C) 2010 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

 /*
add by <PERSON><PERSON><PERSON><PERSON><PERSON>  to offer cabc interface for application 2013-09-27
 */
package android.hardware.cabc;

import android.util.Log;

/**
 * 针对不同版本、不同手机，避免运行时出现类找不到的异常，将该 Class 打包进相册 apk。
 *
 * - api-adapter 的 addOn 里面都是空实现，会直接抛异常；并且仅仅是编译期使用，不会打包进 apk
 * - 如果手机 framework 包含该类，因为双亲委托机制，会直接使用系统的类运行
 * - 如果手机 framework 不包含该类，则使用打包进相册的本 Class 运行，不会抛出异常
 * - 抛出异常会 dump 堆栈，会消耗性能
 */
public class CabcManager {
    private static final boolean DEBUG = true;
    private static final String TAG = "CabcManager";

    public static final int OFF_MODE = 0;
    public static final int UI_MODE = 1;
    public static final int PIC_MODE = 2;
    public static final int VIDEO_MODE = 3;

    private static volatile CabcManager sCabcManagerInstance = null;

    private CabcManager() {
    }

    public static CabcManager getCabcManagerInstance() {
        if (null == sCabcManagerInstance) {
            synchronized (CabcManager.class){
                if (null == sCabcManagerInstance) {
                    sCabcManagerInstance = new CabcManager();
                }
            }
        }
        return sCabcManagerInstance;
    }

    public void setMode(int mode) {
        if (DEBUG) {
            Log.d(TAG, "setMode======mode = " + mode);
        }
    }

    public int getMode() {
        return 3;
    }

    public void closeCabc() {
        if (DEBUG) {
            Log.d(TAG, "closeCabc========");
        }
    }

    public void openCabc() {
        if (DEBUG) {
            Log.d(TAG, "openCabc========");
        }
    }
}