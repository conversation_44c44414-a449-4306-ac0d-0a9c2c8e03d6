plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-kapt'
//    id 'com.oplus.sdk.compat.check'
}

apply from: "${rootDir}/gradle/libCommon.gradle"

// 这部分一定要在android的前面
ext {
    mavenDescription = "相册系统接口库"
    mavenGroupId = mavenGroupName
}

android {
    compileSdkVersion prop_compileSdkVersion
    buildToolsVersion prop_buildToolsVersion
    resourcePrefix project.name + "_"
    namespace 'com.oplus.gallery.foundation.sysapi'

    defaultConfig {
        minSdkVersion prop_minSdkVersion
        targetSdkVersion prop_targetSdkVersion

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    //忽略，olint扫描项:代码混淆风险
    lintOptions {
        disable  "MinifyEnabled"
    }
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])
    implementation "androidx.annotation:annotation:$androidxAnnotationVersion"
    implementation "com.oplus.support:api-adapter-compat:${prop_supportSdkVersion}"
    // 老的 addon sdk，是最全的.
    implementation "com.heytap.support:addon-adapter:${addonAdapterVersion}"
    // 新的 addon sdk，后续新接口会加到这里，接口兼容旧版本，但是R以下的去品牌化接口，要用 api adapter 兼容
    compileOnly "com.oplus.sdk:addon:${osdkVersion}"
    // 新库，后续新提权接口会加到这里，老的 addon sdk 里的提权接口也移动到这里
    //noinspection OJetPackIncompatibleError
    implementation "com.oplus.appplatform:sysapi:${sysApiSdkVersion}"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:$kotlinxCoroutinesCore"
}