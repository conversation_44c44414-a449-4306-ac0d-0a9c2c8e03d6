/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File		: - ${FILE_NAME}
 ** Description	: build this module
 ** Version		: 1.0
 ** Date		: 2019/7/13
 ** Author		: <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>     <data>     <version>  <desc>
 **  <EMAIL>   2019/7/13  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.foundation.networkaccess.utils;

public class RtnCode {

    public interface Base {
        int CODE_MUSIC = 0;
        int CODE_TEMPLATE = 1 << 5;
        int CODE_FILTER = CODE_TEMPLATE * 2;
        int CODE_TRANSITION = CODE_TEMPLATE * 3;
    }

    public interface Music {
        int SUCCESS = Base.CODE_MUSIC; //0
        int AT_INTERVALS = Base.CODE_MUSIC + 1; //1

    }

    public interface Template {
        int SUCCESS = Base.CODE_TEMPLATE; //32
        int AT_INTERVALS = Base.CODE_TEMPLATE + 1; //33
    }
}