/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File        : - TextRequestParam.java
 ** Description :
 ** Version     : 1.0
 ** Date        : 2019/6/11
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                 <data>      <version>  <desc>
 **  <EMAIL>  2019/6/11  1.0        build this module
 ***********************************************************************/

package com.oplus.gallery.foundation.networkaccess.param;

import com.oplus.gallery.foundation.networkaccess.body.RequestBodyImpl;
import com.oplus.gallery.foundation.networkaccess.other.MimeType;

import okhttp3.RequestBody;

public class TextRequestParam extends BaseOkhttpRequestParam<String> {

    public TextRequestParam(String param) {
        super(param);
    }

    @Override
    public RequestBody buildParam() {
        return new RequestBodyImpl(RequestBody.create(MimeType.TEXT.getMediaType(), getParam()),
                mUploadListener);
    }
}
