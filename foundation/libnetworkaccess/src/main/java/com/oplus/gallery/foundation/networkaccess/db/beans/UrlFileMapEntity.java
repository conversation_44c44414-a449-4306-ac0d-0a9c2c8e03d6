/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File        : - UrlFileMapEntity.java
 ** Description :
 ** Version     : 1.0
 ** Date        : 2019/7/2
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                 <data>      <version>  <desc>
 **  <EMAIL>  2019/7/2  1.0        build this module
 ***********************************************************************/

package com.oplus.gallery.foundation.networkaccess.db.beans;


import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.oplus.gallery.foundation.networkaccess.db.Consts;


@Entity(tableName = Consts.TB_URL_FILE_MAP)
public class UrlFileMapEntity {
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "_id")
    private int mId;

    @NonNull
    @ColumnInfo(name = "url")
    private String mUrl;

    @NonNull
    @ColumnInfo(name = "filePath")
    private String mFilePath;
    @NonNull
    @ColumnInfo(name = "mContentLength")
    private long mContentLength;

    public int getId() {
        return mId;
    }

    public void setId(int mId) {
        this.mId = mId;
    }

    public String getUrl() {
        return mUrl;
    }

    public void setUrl(String url) {
        mUrl = url;
    }

    public String getFilePath() {
        return mFilePath;
    }

    public void setFilePath(String filePath) {
        mFilePath = filePath;
    }

    public long getContentLength() {
        return mContentLength;
    }

    public void setContentLength(long contentLength) {
        this.mContentLength = contentLength;
    }
}
