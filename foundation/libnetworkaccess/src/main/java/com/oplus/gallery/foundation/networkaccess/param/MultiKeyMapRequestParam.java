/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File        : - MultiKeyMapRequestParam.java
 ** Description :
 ** Version     : 1.0
 ** Date        : 2019/6/11
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                 <data>      <version>  <desc>
 **  <EMAIL>  2019/6/11  1.0        build this module
 ***********************************************************************/

package com.oplus.gallery.foundation.networkaccess.param;

import com.oplus.gallery.foundation.networkaccess.body.RequestBodyImpl;
import com.oplus.gallery.foundation.networkaccess.callback.ProgressListener;
import com.oplus.gallery.standard_lib.file.File;
import com.oplus.gallery.foundation.util.debug.GLog;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;

import okhttp3.MultipartBody;
import okhttp3.RequestBody;

public class MultiKeyMapRequestParam extends BaseOkhttpRequestParam<Map<String, Object>> {
    private static final String TAG = "MultiKeyMapRequestParam";
    public MultiKeyMapRequestParam(Map<String, Object> param) {
        this(param, null);
    }

    public MultiKeyMapRequestParam(Map<String, Object> baseOkhttpRequestParams,
                                   ProgressListener uploadListener) {
        super(baseOkhttpRequestParams, uploadListener);
    }

    @Override
    public RequestBody buildParam() {
        MultipartBody.Builder builder = new MultipartBody.Builder();
        for (Map.Entry<String, Object> entry : getParam().entrySet()) {
            if (entry.getValue() instanceof File) {
                try {
                    builder.addFormDataPart(entry.getKey(), URLEncoder.encode(((File) entry.getValue()).getName(),
                            StandardCharsets.UTF_8.name()), new FileRequestParam((File) entry.getValue()).buildParam());
                } catch (UnsupportedEncodingException e) {
                    GLog.e(TAG, "buildParam", e);
                }
            } else {
                builder.addFormDataPart(entry.getKey(), String.valueOf(entry.getValue()));
            }
        }
        return new RequestBodyImpl(builder.build(), mUploadListener);
    }
}
