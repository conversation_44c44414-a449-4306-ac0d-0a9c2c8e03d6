/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File        : - RandomAccessFileResponseConvert.java
 ** Description :
 ** Version     : 1.0
 ** Date        : 2019/7/2
 ** Author      : huca<PERSON><PERSON>@Apps.VideoEditor
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                 <data>      <version>  <desc>
 **  <EMAIL>  2019/7/2  1.0        build this module
 ***********************************************************************/

package com.oplus.gallery.foundation.networkaccess.convert;


import android.content.Context;

import com.oplus.gallery.foundation.networkaccess.base.CheckCancelCallback;
import com.oplus.gallery.foundation.networkaccess.body.ResponseBodyImpl;
import com.oplus.gallery.foundation.networkaccess.callback.ProgressListener;
import com.oplus.gallery.foundation.networkaccess.db.DownloadDbClient;
import com.oplus.gallery.foundation.networkaccess.db.beans.DownloadInfoEntity;
import com.oplus.gallery.standard_lib.file.File;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.standard_lib.util.io.IOUtils;

import java.io.IOException;
import java.io.RandomAccessFile;

import okhttp3.Headers;
import okhttp3.Response;
import okio.BufferedSource;

public class RandomAccessFileResponseConvert extends OkhttpResponseConvert<Void> {
    private static final String TAG = "RandomAccessFileResponseConvert";

    private final Context mContext;
    private long mStartPos;
    private ProgressListener mDownloadListener;
    private DownloadInfoEntity mDownloadInfoEntity;
    private CheckCancelCallback mCheckCancelCallback;

    public RandomAccessFileResponseConvert(Context context, long startPos, DownloadInfoEntity downloadInfoEntity, ProgressListener downloadListener,
                                           CheckCancelCallback checkCancelCallback) {
        mStartPos = startPos;
        mDownloadListener = downloadListener;
        mDownloadInfoEntity = downloadInfoEntity;
        mCheckCancelCallback = checkCancelCallback;
        mContext = context.getApplicationContext();
    }

    @Override
    public Void convert(Response response) throws IOException {
        Headers headers = response.headers();
        GLog.d(TAG, "convert : headers:" + headers.toString());
        if (response.body() != null) {
            toFile(new ResponseBodyImpl(response.body(), mDownloadListener).source(), new File(mDownloadInfoEntity.getFilePath()),
                    mStartPos);
        }
        return null;
    }

    private void toFile(BufferedSource source, File file, long startPos) throws IOException {
        RandomAccessFile accessFile = null;
        try {
            accessFile = new RandomAccessFile(file.getFile(), "rwd");
            accessFile.seek(startPos);
            byte[] buffer = new byte[1024 * 8];
            int readSize = 0;
            while (((readSize = source.read(buffer)) > 0) && ((mCheckCancelCallback == null) || (!mCheckCancelCallback.checkCanceled()))) {
                accessFile.write(buffer, 0, readSize);

                writeToDb(readSize);
            }
        } finally {
            IOUtils.closeQuietly(accessFile);
        }
    }

    private void writeToDb(long currentReadSize) {
        mDownloadInfoEntity.setDownloadSize(mDownloadInfoEntity.getDownloadSize() + currentReadSize);
        if (mDownloadInfoEntity.getDownloadSize() == (mDownloadInfoEntity.getEndPos() - mDownloadInfoEntity.getStartPos() + 1)) {
            mDownloadInfoEntity.setFinish(true);
        }
        //TODO
        DownloadDbClient.getInstance(mContext).getDownloadDb().downloadInfoDao().update(mDownloadInfoEntity);
    }
}
