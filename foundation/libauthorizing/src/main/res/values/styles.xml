<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="StatementDialogContentStyle">
        <item name="android:textSize">@dimen/normal_statement_dialog_content_text_size</item>
        <item name="android:textColor">?attr/couiColorSecondNeutral</item>
        <item name="fontFamily">sans-serif-regular</item>
    </style>

    <style name="StatementDialogNegativeStyle">
        <item name="android:textSize">@dimen/coui_full_page_statement_text_size</item>
        <item name="android:textColor">?attr/couiColorPrimaryTextOnPopup</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>

    <style name="DefaultBottomSheetDialog" parent="Theme.MaterialComponents.Light.Dialog">
        <item name="android:background">@null</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:navigationBarColor">?attr/couiColorSurface</item>
        <item name="panelDragViewIcon">@drawable/coui_panel_drag_view</item>
        <item name="panelDragViewTintColor">@color/coui_panel_drag_view_color</item>
        <item name="panelBackground">@drawable/coui_panel_bg_without_shadow</item>
        <item name="panelBackgroundTintColor">?attr/couiColorSurface</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowElevation">0dp</item>
        <item name="couiHandleViewHasPressAnim">true</item>
    </style>

    <style name="COUIAlertDialogCustomScrollViewStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:fadingEdgeLength">@dimen/center_dialog_scroll_fading_edge_length</item>
        <item name="android:overScrollMode">ifContentScrolls</item>
        <item name="android:requiresFadingEdge">vertical</item>
        <item name="android:scrollbarStyle">insideOverlay</item>
        <item name="android:scrollbarThumbVertical">@drawable/coui_scrollbar_handle_vertical</item>
        <item name="android:scrollbars">none</item>
        <item name="android:forceDarkAllowed">false</item>
    </style>

    <style name="COUIAlertDialogCustomScrollViewStyleWithButton">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">0dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:fadingEdgeLength">@dimen/center_dialog_scroll_fading_edge_length</item>
        <item name="android:overScrollMode">ifContentScrolls</item>
        <item name="android:requiresFadingEdge">vertical</item>
        <item name="android:scrollbarStyle">insideOverlay</item>
        <item name="android:scrollbarThumbVertical">@drawable/coui_scrollbar_handle_vertical</item>
        <item name="android:scrollbars">none</item>
        <item name="android:forceDarkAllowed">false</item>
    </style>

    <style name="Animation.COUI.Dialog.NoEnterAnimation">
        <item name="android:windowEnterAnimation">@null</item>
        <item name="android:windowExitAnimation">@anim/coui_bottom_dialog_exit</item>
    </style>
</resources>