/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - BaseHdrTransform.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/11/01
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery		2024/11/01		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.oplus.gallery.foundation.hdrtransform.impl

import com.oplus.gallery.foundation.hdrtransform.data.InitParam
import com.oplus.gallery.foundation.hdrtransform.data.TransformParam
import com.oplus.ocs.camera.hdrtransform.HdrTransformConstants
import com.oplus.ocs.camera.hdrtransform.HdrTransformInitParameter
import com.oplus.ocs.camera.hdrtransform.HdrTransformProcessParameter

abstract class BaseHdrTransform : IHdrTransform {
    /**
     * 渲染模式，可选
     * - [HdrTransformConstants.RENDER_MODE_CPU]
     * - [HdrTransformConstants.RENDER_MODE_GPU]
     */
    protected abstract val renderMode: Int

    /**
     * 处理模式，可选：
     * - [HdrTransformConstants.TYPE_TEXTURE]
     * - [HdrTransformConstants.TYPE_BITMAP]
     */
    protected abstract val processType: Int

    /**
     * 将[InitParam]参数转换成[HdrTransformInitParameter]
     */
    protected fun InitParam.toHdrTransformInitParameter(): HdrTransformInitParameter {
        return HdrTransformInitParameter().apply {
            renderMode = <EMAIL>
            tmcMode = <EMAIL>
            // 初始化不需要这个值，随便设置即可
            cameraMode = <EMAIL>
            // 目前相册只有下变换
            transformMode = HdrTransformConstants.TRANSFORM_HLG_TO_SRGB
            setProcessFlag(<EMAIL>)
        }
    }

    /**
     * 应用下变化数据。从[transformParam]获取输入和输出的图片/纹理，转换到[HdrTransformProcessParameter]中
     *
     * @param transformParam 下变换执行参数
     */
    protected abstract fun HdrTransformProcessParameter.applyData(transformParam: TransformParam)

    /**
     * 应用下变化参数。从[transformParam]获取参数，转换到[HdrTransformProcessParameter]中
     *
     * @param transformParam 下变换执行参数
     */
    protected fun HdrTransformProcessParameter.applyParams(transformParam: TransformParam) {
        processType = <EMAIL>

        cameraMode = transformParam.cameraMode
        luxIndex = transformParam.luxIndex
        faceLumaRatio = transformParam.faceLumaRatio
        faceNum = transformParam.faceNum
        zoomFactor = transformParam.zoomFactor

        hlgGammaArray = transformParam.hlgGammaArray
        srgbGammaArray = transformParam.srgbGammaArray

        ccmData = transformParam.ccmData
        transformParam.sharpenSigma?.also { sharpenSigma = it }
        transformParam.sharpenRadius?.also { sharpenRadius = it }
        transformParam.imageCeoff?.also { imageCoefficients = it }
    }

    companion object {

        /**
         * HLG Gamma下的最大亮度
         */
        internal const val HLG_MAX_LUMEN = 1000f

        /**
         * sRGB Gamma下的最大亮度
         */
        internal const val SRGB_MAX_LUMEN = 203f
    }
}