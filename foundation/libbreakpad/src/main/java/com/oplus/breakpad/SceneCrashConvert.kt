/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SceneCrashConvert.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2021/05/27
 ** Author: WUDANYANG
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** WUDANYANG  2021/05/27		1.0		BREAKPAD
 *********************************************************************************/
package com.oplus.breakpad

import android.database.Cursor
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.dbaccess.convert.IConvert
import com.oplus.gallery.standard_lib.util.io.IOUtils

class SceneCrashConvert : IConvert<Cursor, List<SceneCrashInfo>?> {
    override fun convert(cursor: Cursor?): List<SceneCrashInfo>? {
        if ((cursor == null) || (cursor.count <= 0)) {
            return null
        } else {
            val crashes: ArrayList<SceneCrashInfo> = arrayListOf()
            val sceneIndex = cursor.getColumnIndex(GalleryStore.SceneCrashColumns.SCENE)
            val carriedMsqIndex = cursor.getColumnIndex(GalleryStore.SceneCrashColumns.CARRIED_MSG)
            val crashTimeIndex = cursor.getColumnIndex(GalleryStore.SceneCrashColumns.CRASH_TIME)
            val backtraceIndex = cursor.getColumnIndex(GalleryStore.SceneCrashColumns.BACKTRACE)
            val strerrorIndex = cursor.getColumnIndex(GalleryStore.SceneCrashColumns.STRERROR)
            val siSignIndex = cursor.getColumnIndex(GalleryStore.SceneCrashColumns.SI_SIGNO)
            val siCodeIndex = cursor.getColumnIndex(GalleryStore.SceneCrashColumns.SI_CODE)
            val siErrnoIndex = cursor.getColumnIndex(GalleryStore.SceneCrashColumns.SI_ERRNO)
            val siBandIndex = cursor.getColumnIndex(GalleryStore.SceneCrashColumns.SI_BAND)
            val siFdIndex = cursor.getColumnIndex(GalleryStore.SceneCrashColumns.SI_FD)
            val siOverrunIndex = cursor.getColumnIndex(GalleryStore.SceneCrashColumns.SI_OVERRUN)
            val siPidIndex = cursor.getColumnIndex(GalleryStore.SceneCrashColumns.SI_PID)
            val siUidIndex = cursor.getColumnIndex(GalleryStore.SceneCrashColumns.SI_UID)
            val sitIdIndex = cursor.getColumnIndex(GalleryStore.SceneCrashColumns.SI_TID)
            val siStatusIndex = cursor.getColumnIndex(GalleryStore.SceneCrashColumns.SI_STATUS)
            while (cursor.moveToNext()) {
                val crash = SceneCrashInfo()
                crash.scene = cursor.getString(sceneIndex)
                crash.carriedMsg = cursor.getString(carriedMsqIndex)
                crash.crashTime = cursor.getLong(crashTimeIndex)
                crash.backtrace = cursor.getString(backtraceIndex)
                crash.strerror = cursor.getString(strerrorIndex)
                crash.si_signo = cursor.getInt(siSignIndex)
                crash.si_code = cursor.getInt(siCodeIndex)
                crash.si_errno = cursor.getInt(siErrnoIndex)
                crash.si_band = cursor.getLong(siBandIndex)
                crash.si_fd = cursor.getInt(siFdIndex)
                crash.si_overrun = cursor.getInt(siOverrunIndex)
                crash.si_pid = cursor.getInt(siPidIndex)
                crash.si_uid = cursor.getInt(siUidIndex)
                crash.si_tid = cursor.getInt(sitIdIndex)
                crash.si_status = cursor.getInt(siStatusIndex)
                crashes.add(crash)
            }
            IOUtils.closeQuietly(cursor)
            return crashes
        }
    }
}