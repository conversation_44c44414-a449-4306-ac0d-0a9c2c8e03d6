/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ServiceContentsInfo.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/1/5
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  dustin.shu      2022/1/5      1.0        create
 ***********************************************************************/
package com.oplus.gallery.questionnaire.data.entity

import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import com.oplus.gallery.questionnaire.utils.Log

data class ServiceContentsInfo(
    @SerializedName("content")
    val content: String,

    @SerializedName("serviceId")
    val serviceId: Int,

    @SerializedName("version")
    val version: Int
) {

    fun mapperToServiceInfoParams(): ServiceInfoParams {
        return ServiceInfoParams(serviceId.toString(), version)
    }

    data class Content(
        val attributes: String,
        val desc: Map<String, String>,
        val operatePositions: List<OperatePosition>,
        val picUrl: String
    )

    @Suppress("TooGenericExceptionCaught")
    fun mapperContent(): Content? {
        if (content.isNotEmpty()) {
            return try {
                val contentType = object : TypeToken<Content>() {}.type
                Gson().fromJson(content, contentType)
            } catch (e: Exception) {
                Log.e("mapperContent has error -> $e")
                null
            }
        }
        return null
    }

    data class ServiceInfoParams(
        val serviceId: String = "",
        val version: Int = 0
    )
}