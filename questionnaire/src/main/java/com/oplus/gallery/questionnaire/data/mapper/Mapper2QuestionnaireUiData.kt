/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : Mapper2QuestionnaireUiData.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/1/5
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  dustin.shu      2022/1/5      1.0        create
 ***********************************************************************/
package com.oplus.gallery.questionnaire.data.mapper

import com.oplus.gallery.questionnaire.data.entity.Questionnaire
import com.oplus.gallery.questionnaire.data.entity.QuestionnaireUiDataWithServiceId

class Mapper2QuestionnaireUiData :
    Mapper<List<Questionnaire>, List<QuestionnaireUiDataWithServiceId>> {

    override fun map(input: List<Questionnaire>): List<QuestionnaireUiDataWithServiceId> {
        val entities = ArrayList<QuestionnaireUiDataWithServiceId>()
        input.forEach { questionnaire ->
            val serviceId = questionnaire.serviceId
            QuestionnaireCardContentParser.parse(questionnaire.content)?.let {
                entities.add(QuestionnaireUiDataWithServiceId(serviceId, it))
            }
        }
        return entities
    }
}