/*********************************************************************************
 ** Copyright (C), 2022-2032, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - QuestionnaireDM.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/04/19
 ** Author: <EMAIL>
 ** TAG: OPLUS_TRACK
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2022/04/19		1.0		INIT
 *********************************************************************************/

package com.oplus.gallery.questionnaire

import android.app.Activity
import android.content.Context
import androidx.annotation.IdRes
import androidx.lifecycle.LifecycleOwner
import com.oplus.gallery.business_lib.api.IQuestionnaireDM
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.router_lib.annotations.Component

@Component(interfaceName = "com.oplus.gallery.business_lib.api.IQuestionnaireDM")
class QuestionnaireDM : IQuestionnaireDM {
    override fun injectQuestionnaire(
        activity: Activity,
        lifecycleOwner: LifecycleOwner,
        @IdRes targetViewStub: Int,
        idsBean: IQuestionnaireDM.QuestionnaireIdsBean,
        isPermitted: Boolean
    ) {
        QuestionnaireApi.injectQuestionnaire(
            activity,
            lifecycleOwner,
            targetViewStub,
            idsBean,
            isPermitted
        )
    }

    override fun canShowQuestionnaireCard(context: Context): Boolean {
        return GTrace.trace("canShowQuestionnaireCard") {
            QuestionnaireApi.canShowQuestionnaireCard(context)
        }
    }
}