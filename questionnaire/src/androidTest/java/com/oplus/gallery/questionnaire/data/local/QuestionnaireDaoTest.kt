/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : QuestionnaireDaoTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2021/12/31
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2021/12/31       1.0      create
 ***********************************************************************/
package com.oplus.gallery.questionnaire.data.local

import android.content.Context
import androidx.room.Room
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.oplus.gallery.questionnaire.data.entity.Questionnaire
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking
import org.hamcrest.MatcherAssert
import org.hamcrest.Matchers.equalTo
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import java.io.IOException

@RunWith(AndroidJUnit4::class)
class QuestionnaireDaoTest {

    private lateinit var db: AppDatabase
    private lateinit var questionnaireDao: QuestionnaireDao

    @Before
    fun setUp() {
        val context = ApplicationProvider.getApplicationContext<Context>()
        db = Room.inMemoryDatabaseBuilder(context, AppDatabase::class.java).build()
        questionnaireDao = db.questionnaireDao()
    }

    @After
    @Throws(IOException::class)
    fun closeDb() {
        db.close()
    }

    @Test
    @Throws(Exception::class)
    fun testInsertQuestionnaireList() {
        val questionnaire = Questionnaire(
            1, "1", "1", 1, 1, "content"
        )
        val questionnaireList = listOf(questionnaire)
        questionnaireDao.insertQuestionnaireList(questionnaireList)
        val result = questionnaireDao.getQuestionnaireNeedToShow()
        MatcherAssert.assertThat(result[0], equalTo(questionnaire))
    }

    @Test
    @Throws(Exception::class)
    fun testGetQuestionnaireNeedToShow() {
        val questionnaireSort1 = Questionnaire(
            1, "1", "1", 1, 1, "content"
        )
        val questionnaireSort2 = Questionnaire(
            1, "1", "1", 1, 2, "content"
        )
        val questionnaireSort1LargeTimeStamp = Questionnaire(
            1, "1", "12312", 1, 1, "content"
        )
        val questionnaireList = listOf(questionnaireSort2, questionnaireSort1, questionnaireSort1LargeTimeStamp)
        questionnaireDao.insertQuestionnaireList(questionnaireList)
        val result = questionnaireDao.getQuestionnaireNeedToShow()
        MatcherAssert.assertThat(result[0], equalTo(questionnaireSort1LargeTimeStamp))
    }

    @Test
    @Throws(Exception::class)
    fun testGetQuestionnaireNeedToShowFlow() {
        val questionnaireSort1 = Questionnaire(
            1, "1", "1", 1, 1, "content"
        )
        val questionnaireSort2 = Questionnaire(
            1, "1", "1", 1, 2, "content"
        )
        val questionnaireSort1LargeTimeStamp = Questionnaire(
            1, "1", "12312", 1, 1, "content"
        )
        val questionnaireList = listOf(questionnaireSort2, questionnaireSort1, questionnaireSort1LargeTimeStamp)
        questionnaireDao.insertQuestionnaireList(questionnaireList)
        val result = questionnaireDao.getQuestionnaireNeedToShowFlow()
        runBlocking {
            MatcherAssert.assertThat(result.first()[0], equalTo(questionnaireSort1LargeTimeStamp))
        }
    }

    @Test
    @Throws(Exception::class)
    fun testDeleteQuestionnaireByServiceId() {
        val questionnaireSort1 = Questionnaire(
            1, "1", "1", 1, 1, "content"
        )
        val questionnaireSort2 = Questionnaire(
            2, "1", "1", 1, 2, "content"
        )
        val questionnaireSort1LargeTimeStamp = Questionnaire(
            1, "1", "12312", 1, 1, "content"
        )
        val questionnaireList = listOf(questionnaireSort2, questionnaireSort1, questionnaireSort1LargeTimeStamp)
        questionnaireDao.insertQuestionnaireList(questionnaireList)
        questionnaireDao.deleteQuestionnaireByServiceId(1)
        val result = questionnaireDao.getQuestionnaireNeedToShow()
        MatcherAssert.assertThat(result[0], equalTo(questionnaireSort2))
        questionnaireDao.deleteQuestionnaireByServiceId(2)
        val result2 = questionnaireDao.getQuestionnaireNeedToShow()
        MatcherAssert.assertThat(result2, equalTo(emptyList()))
    }

    @Test
    @Throws(Exception::class)
    fun testDeleteQuestionnaireNotInServiceIdList() {
        val questionnaireSort1 = Questionnaire(
            1, "1", "1", 1, 1, "content"
        )
        val questionnaireSort2 = Questionnaire(
            2, "1", "1", 1, 2, "content"
        )
        val questionnaireSort1LargeTimeStamp = Questionnaire(
            3, "1", "12312", 1, 1, "content"
        )
        val questionnaireList = listOf(questionnaireSort2, questionnaireSort1, questionnaireSort1LargeTimeStamp)
        questionnaireDao.insertQuestionnaireList(questionnaireList)
        questionnaireDao.deleteQuestionnaireNotInServiceIdList(listOf(3))
        val result = questionnaireDao.getQuestionnaireNeedToShow()
        MatcherAssert.assertThat(result[0], equalTo(questionnaireSort1LargeTimeStamp))
    }

    @Test
    @Throws(Exception::class)
    fun testDeleteAllQuestionnaire() {
        val questionnaireSort1 = Questionnaire(
            1, "1", "1", 1, 1, "content"
        )
        val questionnaireSort2 = Questionnaire(
            2, "1", "1", 1, 2, "content"
        )
        val questionnaireSort1LargeTimeStamp = Questionnaire(
            1, "1", "12312", 1, 1, "content"
        )
        val questionnaireList = listOf(questionnaireSort2, questionnaireSort1, questionnaireSort1LargeTimeStamp)
        questionnaireDao.insertQuestionnaireList(questionnaireList)
        questionnaireDao.deleteAllQuestionnaire()
        val result = questionnaireDao.getQuestionnaireNeedToShow()
        MatcherAssert.assertThat(result, equalTo(emptyList()))
    }
}