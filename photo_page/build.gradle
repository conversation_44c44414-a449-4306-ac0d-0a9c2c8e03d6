// 这部分一定要在android的前面
ext {
    mavenDescription = "相册oppo大图页面库"
    isDfm = localAppBundle.toBoolean()
}

apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'

apply from: "${rootDir}/gradle/launch.gradle"
apply from: "${rootDir}/gradle/pageCommon.gradle"
apply from: "${rootDir}/gradle/dfmCommon.gradle"

android {
    compileSdkVersion prop_compileSdkVersion
    buildToolsVersion prop_buildToolsVersion
    resourcePrefix project.name + "_"
    namespace 'com.oplus.gallery.photo_page'

    defaultConfig {
        minSdkVersion prop_minSdkVersion
        targetSdkVersion prop_targetSdkVersion
        versionCode 1
        versionName "1.0.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        debug {
            matchingFallbacks = ['release', 'debug']
            minifyEnabled false
        }
        release {
            matchingFallbacks = ['release']
            minifyEnabled false
        }
    }
    //忽略，olint扫描项:代码混淆风险
    lintOptions {
        disable  "MinifyEnabled"
    }

    kotlinOptions {
        freeCompilerArgs += [
                "-Xuse-experimental=kotlin.ExperimentalUnsignedTypes",
                "-XXLanguage:+InlineClasses"
        ]
    }
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])
    if (isBundleFun() && localAppBundle.toBoolean()) {
        implementationProject(":app")
    }


    kapt "com.oplus.gallery.router_lib:compiler:$routerCompilerVersion"
    implementation "com.oplus.gallery.router_lib:annotations:$routerAnnotationsVersion"

    implementation("com.oplus.appability:appabilitylib:$abilityapiLibVersion")

    // 相册大图页控件
    implementation "com.oplus.gallery:photopager:${photopagerLibVersion}"

    //ocs display
    implementation ("com.oplus.ocs:display:${ocsDisplayVersion}") {
        exclude group: 'com.oplus.sdk', module: 'addon'
    }
    //ocs base
    implementation "com.oplus.ocs:base:${ocsBaseVersion}"

    implementationProject(':framework:business_lib')
    implementationProject(':basebiz')
    implementationProject(':foundation:libgstartup')
    implementationProject(':foundation:libarch')
    implementationProject(':foundation:libbreakpad')
    implementationProject(':foundation:libcache')
    implementationProject(':foundation:libcodec')
    implementationProject(':foundation:libdbaccess')
    implementationProject(':foundation:libexif')
    implementationProject(':foundation:libfileaccess')
    implementationProject(':foundation:libopengl')
    implementationProject(':foundation:librouter')
    implementationProject(':foundation:libsecurity')
    implementationProject(':foundation:libtracing')
    implementationProject(':foundation:libui')
    implementationProject(':foundation:libuikit')
    implementationProject(':foundation:libutil')
    implementationProject(':foundation:libcvimageprocess')
    implementationProject(':foundation:libutiltmp')
    implementationProject(':foundation:libsysapi')
    implementationProject(':foundation:libgallerypaging')
    implementationProject(':foundation:libhdrtransform')
    implementationProject(':framework')
    implementationProject(':framework:abilityapi')
    implementationProject(':foundation:libtaskscheduling')
    //Marked by zhangwenming 将data部分及相关依赖临时迁移到framework层而添加，后面整改时，需要去掉。
    implementationProject(':framework:dataabilitytmp')
    //Marked by zhangwenming 因ResoucingAbility未完全将旧的引用去掉，因此会依赖部分定义及类，未来需要去掉
    implementationProject(':framework:resourcingability')
    implementationProject(':framework:authorizingability')
    implementationProject(':foundation:libauthorizing')
    implementationProject(':framework:searchability')
//    implementationProject(':framework:scanability2')
    // liftandshift-SDK(相册内部)
    implementationProject(':foundation:libliftandshift')
    // 抠图动效SDK(Web与图形引擎组) 内外销通用
    implementation("com.oplusos.vfxsdk:CutoutEffect:$lnsVfxSdkVersion")
    // super text
    implementation("com.oplus.smartsdk.supertext:ostatic:${superTextVersion}") {
        exclude group: 'com.coui.support', module: 'coui-support-appcompat'
        exclude group: 'com.oplus.aiunit.open', module: 'nlp'

        // MarkBy TangHui：exclude COUISupport 旧版本结构的所有子包
        exclude group: 'com.oplus.appcompat', module: 'base'
        exclude group: 'com.oplus.appcompat', module: 'controls'
        exclude group: 'com.oplus.appcompat', module: 'lists'
        exclude group: 'com.oplus.appcompat', module: 'bars'
        exclude group: 'com.oplus.appcompat', module: 'component'
        exclude group: 'com.oplus.appcompat', module: 'panel'
        exclude group: 'com.oplus.appcompat', module: 'responsiveui'
        exclude group: 'com.oplus.appcompat', module: 'nearx'
    }
    // cloudkit 核心同步库
    compileOnly "com.heytap.cloudkit.libsync:libsync:$cloudkitSdkVersion"
    // cloudkit 公共库
    compileOnly "com.heytap.cloudkit.libcommon:libcommon:$cloudkitSdkVersion"

    // media3:在线播放器，谷歌云需求引入
    implementation "androidx.media3:media3-session:${media3SessionVersion}"

    // olivePhoto显示控件
    implementation "com.oplus.gallery:olive-viewplayer:$oliveViewplayerVersion"
    // olive 解码能力，临时存放，要下沉了 Marked by tlyao
    implementation "com.oplus.gallery:olive-decoder:$oliveDecoderVersion"
    // 小布识屏服务接口
    implementation project(path: ':foundation:libcolordirect')

    // AIUnit插件下载模型所需
    implementation "com.oplus.aiunit.open:download:$aiUnitDownloadVersion"

    // RenderThread动画依赖系统集成的osdk
    compileOnly "com.oplus.sdk:addon:${osdkVersion}"

    implementationProject(':foundation:libopengl')

    implementation ("com.oplus.TBLPlayer:CommonPlayer-processor:${TBLPlayerVersion}") {
        exclude group: 'com.squareup.okhttp3'
        // 在线播放模块 start, tbl在线模块相册用不到，解除依赖，减小apk大小
        exclude group: 'com.oplus.tbl.exo', module: 'exoplayer-dash'
        exclude group: 'com.oplus.tbl.exo', module: 'exoplayer-hls'
        exclude group: 'com.oplus.tbl.exo', module: 'exoplayer-smoothstreaming'
        exclude group: 'com.oplus.tbl.exo', module: 'extension-rtmp'
        // 在线播放模块 end
    }
    implementation("javolution:javolution:$javolutionVersion")
}