/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : CShotActivity.kt
 ** Description : 连拍页Activity
 ** Version     : 1.0
 ** Date        : 2022/08/25
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2022/08/25    1.0      create module
 *********************************************************************************/
package com.oplus.gallery.cshot_page.ui

import android.app.Activity
import android.app.KeyguardManager
import android.content.Context
import android.content.pm.ActivityInfo
import android.graphics.Color
import android.os.Bundle
import android.view.Display
import androidx.core.view.WindowInsetsControllerCompat
import com.oplus.gallery.basebiz.constants.IntentConstant
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_FROM_CAMERA
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_MEDIA_FROM
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.helper.start
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.foundation.ui.systembar.ActivitySystemBarStyle
import com.oplus.gallery.foundation.ui.systembar.ImmersiveActivitySystemBarStyle
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.ext.getValidScreenOrientationOrDefault
import com.oplus.gallery.foundation.util.ext.isOnDefaultDisplay
import com.oplus.gallery.foundation.util.systemcore.IntentUtils
import com.oplus.gallery.foundation.util.systemcore.PowerManagerUtils
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.router_lib.annotations.RouterNormal

/**
 * 连拍的activity。
 *
 * 单独一个activity的原因是，默认的activity横竖屏不会重建视图，
 * 但连拍需要重建。
 */
@RouterNormal(RouterConstants.RouterName.CSHOT_ACTIVITY)
@ExperimentalUnsignedTypes
class CShotActivity : BaseActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setupScreenOrientationIfNeeded()
        setContentView()
        setUpLockMode()
        supportFragmentManager.start(
            resId = com.oplus.gallery.basebiz.R.id.base_fragment_container,
            fragmentClass = CShotFragment::class.java,
            data = intent.extras,
            localStack = this,
            addToBackStack = true
        )
    }

    override fun onStart() {
        super.onStart()
        tryFinish()
    }

    override fun finish() {
        super.finish()
        // 处理大图进入连拍页闪黑问题，临时和设计协商加入。原始大图进入连拍页无动画，需后续和设计讨论动画
        overridePendingTransition(
            R.anim.picture3d_photopage_to_cshotpage_silent,
            R.anim.picture3d_photopage_to_cshotpage_bottom_out
        )
    }

    override fun onStop() {
        super.onStop()
        //  处理连拍页显示在锁屏之上时，关屏则finish页面，使得亮屏后回到锁屏页面
        if (isFromCamera && isDisplayOnLock && PowerManagerUtils.isInteractive().not()) {
            GLog.d(TAG, "screen off in keyguard, finish.")
            finish()
        }
    }

    override fun getSystemBarStyle(): ActivitySystemBarStyle = object : ImmersiveActivitySystemBarStyle(this) {
        override fun onInit() {
            super.onInit()
            setNaviBarColor(Color.TRANSPARENT)
            setSystemBarBehavior(WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE)
            hideStatusBar()
        }
    }

    private fun setUpLockMode() {
        val intent = intent
        isFromCamera = KEY_FROM_CAMERA.equals(IntentUtils.getStringExtra(intent, KEY_MEDIA_FROM), ignoreCase = true)
        val keyguardManager = getSystemService(Context.KEYGUARD_SERVICE) as? KeyguardManager
        isDisplayOnLock = keyguardManager?.isKeyguardLocked ?: false
        GLog.d(TAG, "isFromCamera $isFromCamera, isDisplayOnLock $isDisplayOnLock")
        if (isFromCamera && isDisplayOnLock) {
            setShowWhenLocked(true)
        }
    }

    private fun setupScreenOrientationIfNeeded() {
        val expectedScreenOrientation: Int = IntentUtils.getIntExtra(
            intent,
            IntentConstant.ViewGalleryConstant.KEY_INIT_SCREEN_ORIENTATION,
            ActivityInfo.SCREEN_ORIENTATION_USER
        ).getValidScreenOrientationOrDefault()

        GLog.d(TAG) {
            "[setupScreenOrientationIfNeeded] requestedOrientation = $requestedOrientation , " +
                    "expectedScreenOrientation = $expectedScreenOrientation"
        }

        if (requestedOrientation == expectedScreenOrientation) {
            return
        }

        requestedOrientation = expectedScreenOrientation
    }

    /**
     * 如果当前连拍页面不在默认[Display]上显示，如在外屏显示
     * 则退出连拍页面
     */
    private fun tryFinish() {
        if (isShowedOnDefaultDisplay(this).not()) {
            GLog.d(TAG) { "[tryFinish] need finish when cshot page show on not default display" }
            finish()
        }
    }

    /**
     *
     * 当前 [activity] 是否展示在默认 [Display.DEFAULT_DISPLAY] 上
     * 分为两种场景:
     * 场景一：当前不是多[android.view.Display] 设备
     * 场景二：当前是多 [android.view.Display] 设备 且 当前显示在默认显示设备
     * 条件1：当前是多 [android.view.Display] 设备
     * @see [ConfigID.Common.SystemInfo.IS_MULTI_DISPLA]
     *
     * 条件2：当前显示在默认显示设备
     * @see [isOnDefaultDisplay]
     *
     * @param activity
     */
    private fun isShowedOnDefaultDisplay(activity: Activity): Boolean {
        val isMultiDisplayDevice = ConfigAbilityWrapper.getBoolean(ConfigID.Common.SystemInfo.IS_MULTI_DISPLAY)
        val isOnDefaultDisplay = activity.isOnDefaultDisplay
        return isMultiDisplayDevice.not() || isOnDefaultDisplay
    }
}