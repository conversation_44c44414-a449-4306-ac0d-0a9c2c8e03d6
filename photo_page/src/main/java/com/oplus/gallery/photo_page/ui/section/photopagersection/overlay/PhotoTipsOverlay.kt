/*********************************************************************************
 ** Copyright (C), 2022-2032, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoTipsOverlay.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/08/25
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>               2022/08/25  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.ui.section.photopagersection.overlay

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.graphics.Matrix
import android.view.View
import android.view.View.ALPHA
import android.view.View.SCALE_X
import android.view.View.SCALE_Y
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams
import com.coui.appcompat.animation.COUIEaseInterpolator
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.oplus.anim.EffectiveAnimationView
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photopager.PhotoSlotOverlay

internal class PhotoTipsOverlay(
    private val containerView: View
) : PhotoSlotOverlay {

    private val animationView: EffectiveAnimationView = containerView.findViewById(R.id.av_overlay_loading)
    private var animatorSet: AnimatorSet? = null

    /**
     * 开始加载 loading ,即淡入动画以及转圈动画
     */
    fun showLoadingTips() {
        startLoading()
        startShowAnim()
    }

    /**
     * 开始隐藏 loading，即淡出动画，淡出动画结束后暂停转圈动画
     */
    fun hideLoadingTips(onHideAnimCompleted: () -> Unit) {
        startHideAnim {
            onHideAnimCompleted.invoke()
            pauseLoading()
        }
    }

    /**
     * 当前 overlay 是否显示
     */
    fun isShowing(): Boolean {
        return containerView.visibility == View.VISIBLE
    }

    /**
     * 开始 loading 转圈
     */
    private fun startLoading() {
        if (COUIDarkModeUtil.isNightMode(animationView.context)) {
            animationView.setAnimation(ANIM_LOADING_JSON_NIGHT)
        } else {
            animationView.setAnimation(ANIM_LOADING_JSON)
        }
        animationView.playAnimation()
    }

    /**
     * 暂停 loading 转圈
     */
    private fun pauseLoading() {
        animationView.pauseAnimation()
    }

    /**
     * 开始进入动画，提供的默认进入动画，如果要使用其他动画，可以在类里或者外部新增。
     */
    private fun startShowAnim() {
        val alphaAnimator = ObjectAnimator.ofFloat(animationView, ALPHA, ANIM_SHOW_ALPHA_START, ANIM_SHOW_ALPHA_END)
        val scaleXAnimator = ObjectAnimator.ofFloat(animationView, SCALE_X, ANIM_SHOW_SCALE_START, ANIM_SHOW_SCALE_END)
        val scaleYAnimator = ObjectAnimator.ofFloat(animationView, SCALE_Y, ANIM_SHOW_SCALE_START, ANIM_SHOW_SCALE_END)
        alphaAnimator.interpolator = ANIM_SHOW_ALPHA_INTERPOLATOR
        scaleXAnimator.interpolator = ANIM_SHOW_SCALE_X_INTERPOLATOR
        scaleYAnimator.interpolator = ANIM_SHOW_SCALE_Y_INTERPOLATOR
        AnimatorSet().apply {
            animatorSet = this
            play(alphaAnimator).with(scaleXAnimator).with(scaleYAnimator)
            duration = ANIM_SHOW_DURATION
            start()
        }
    }

    /**
     * 开始退出动画，提供的默认退出动画，如果要使用其他动画，可以在类里或者外部新增。
     */
    private fun startHideAnim(onHideAnimCompleted: (() -> Unit)? = null) {
        // 极限情况考虑，刚调用 show 动画，就调用 hide 动画， 暂停掉 show 动画
        animatorSet?.end()
        ObjectAnimator.ofFloat(animationView, ALPHA, ANIM_HIDE_ALPHA_START, ANIM_HIDE_ALPHA_END).apply {
            duration = ANIM_HIDE_DURATION
            interpolator = ANIM_HIDE_ALPHA_INTERPOLATOR
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    onHideAnimCompleted?.invoke()
                }
            })
            start()
        }
    }

    override fun attach(parent: ViewGroup) {
        ensureParentDetached()
        parent.addView(containerView, LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT))
    }

    override fun detach(parent: ViewGroup) {
        ensureParentDetached()
    }

    override fun hide() {
        containerView.visibility = View.GONE
    }

    override fun show() {
        containerView.visibility = View.VISIBLE
    }

    override fun onSlotTransformChanged(slotTransform: Matrix) = Unit

    override fun onInsetChanged(left: Int, top: Int, right: Int, bottom: Int) = Unit

    private fun ensureParentDetached() {
        (containerView.parent as? ViewGroup)?.removeView(containerView)
    }

    internal companion object {

        private const val ANIM_SHOW_DURATION = 300L
        private const val ANIM_SHOW_ALPHA_START = 0f
        private const val ANIM_SHOW_ALPHA_END = 1f
        private const val ANIM_SHOW_SCALE_START = 0.93f
        private const val ANIM_SHOW_SCALE_END = 1f
        private const val ANIM_HIDE_DURATION = 200L
        private const val ANIM_HIDE_ALPHA_START = 1f
        private const val ANIM_HIDE_ALPHA_END = 0f
        private const val ANIM_LOADING_JSON = "anim_view_loading.json"
        private const val ANIM_LOADING_JSON_NIGHT = "anim_view_loading_night.json"

        private val ANIM_SHOW_ALPHA_INTERPOLATOR = COUIEaseInterpolator()
        private val ANIM_SHOW_SCALE_X_INTERPOLATOR = COUIEaseInterpolator()
        private val ANIM_SHOW_SCALE_Y_INTERPOLATOR = COUIEaseInterpolator()
        private val ANIM_HIDE_ALPHA_INTERPOLATOR = COUIEaseInterpolator()
    }
}