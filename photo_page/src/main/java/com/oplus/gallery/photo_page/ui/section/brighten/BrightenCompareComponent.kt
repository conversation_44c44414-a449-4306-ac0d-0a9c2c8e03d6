/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - BrightenCompareComponent.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2023/02/25
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2023/02/25    1.0                create
 ******************************************************************************/

package com.oplus.gallery.photo_page.ui.section.brighten

import android.animation.ObjectAnimator
import android.content.res.ColorStateList
import android.view.MotionEvent
import android.view.View
import android.view.ViewStub
import android.widget.ImageView
import androidx.core.animation.addListener
import androidx.core.graphics.ColorUtils
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import com.coui.appcompat.animation.COUIEaseInterpolator
import com.coui.appcompat.uiutil.ShadowUtils
import com.oplus.gallery.foundation.arch.sectionpage.ISectionPage
import com.oplus.gallery.foundation.ui.helper.FeedbackAnimatorHelper
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.ext.collect
import com.oplus.gallery.foundation.util.ext.updateMarginRelative
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.ui.DynamicVisibility
import com.oplus.gallery.photo_page.ui.PhotoFragment
import com.oplus.gallery.photo_page.ui.isVisible
import com.oplus.gallery.photo_page.ui.section.thumblinesection.PhotoAnimationConfig
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.dataloading.diffedFocusViewDataWhenPresentAndIdle
import com.oplus.gallery.photo_page.viewmodel.dataloading.focusItemViewData
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.defaultFabCenterToTargetDistanceValue
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.thumbLineHeightValue
import com.oplus.gallery.standard_lib.app.AppConstants
import com.oplus.gallery.tools.addShadowLV2
import com.oplus.gallery.tools.setCircularOutline
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map

/**
 * 提亮效果对比组件
 */
internal class BrightenCompareComponent(
    private val sectionPage: ISectionPage<PhotoFragment, PhotoViewModel>,
    private val onItemTouchEvent: (Int) -> Unit
) : LifecycleOwner {

    /**
     * 是否启用缩图轴
     */
    private val isThumbLineEnabled: Boolean
        get() = (sectionPage.pageViewModel.inputArguments.features.value?.isThumbLineEnabled == true)

    /**
     * 缩图轴是否显示
     */
    private val isThumbLineVisible: Boolean
        get() = sectionPage.pageViewModel.pageManagement.isThumbLineVisible.value

    /**
     * 是否支持ThumbnailSeekBar（显示视频多帧背景的seekbar）
     */
    private val isPlaybackPreviewEnabled: Boolean
        get() = (sectionPage.pageViewModel.inputArguments.features.value?.isPlaybackPreviewEnabled == true)

    /**
     * 缩图轴高度
     */
    private val thumbLineHeight: Int
        get() = sectionPage.pageViewModel.pageManagement.thumbLineHeightValue

    /**
     * 默认底部margin
     */
    private val defaultBottomMargin: Int
        get() = sectionPage.pageViewModel.pageManagement.defaultFabCenterToTargetDistanceValue - (brightenCompareViewHeight / 2)

    private var brightenCompareView: View? = null
    private var animationHelper: FeedbackAnimatorHelper? = null

    private val brightenCompareViewDefaultHeight by lazy {
        brightenCompareView?.context?.resources?.getDimensionPixelSize(R.dimen.photopage_pro_xdr_height)
    }
    private val brightenCompareViewHeight: Int
        get() = brightenCompareView?.run {
            height.takeIf { it != 0 } ?: brightenCompareViewDefaultHeight
        } ?: 0

    private val compareTouchListener = object : View.OnTouchListener {
        override fun onTouch(view: View, event: MotionEvent): Boolean {
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    brightenCompareView?.isPressed = true
                    animationHelper?.executeFeedbackAnimator(true)
                }

                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    brightenCompareView?.isPressed = false
                    animationHelper?.executeFeedbackAnimator(false)
                }

                else -> {}
            }
            event.action.let(onItemTouchEvent)
            return true
        }
    }

    /**
     * diffedFocus流。下发时机：
     * 1. 当图片 <-> 视频切换时才会下发
     * 2. 当入场动画结束 & 大图滑动idle时才下发
     */
    private val diffedFocusMediaTypeTimingFlow by lazy {
        sectionPage.pageViewModel.dataLoading.diffedFocusViewDataWhenPresentAndIdle.distinctUntilChanged { _, diffed ->
            // 只关心mimeType的变化
            diffed?.newItem?.mediaType == diffed?.oldItem?.mediaType
        }
    }

    /**
     * 出现、隐藏动画的动画器。
     * marked by caiconghu 理论上此处应该使用一个物理动画器还原spring动画效果。
     * 但是由于spring动画器会导致动画时间增长，进而导致出帧增多、功耗量增加。
     * 因此和动效沟通后，此处改为原动画。
     */
    private val componentFadeAnimator by lazy {
        ObjectAnimator.ofFloat(ALPHA_HIDE, ALPHA_SHOW).apply {
            interpolator = FADE_ANIM_INTERPOLATOR
            duration = FADE_ANIM_DURATION
        }
    }

    /**
     * 当前proXDR对比按钮是否已经 or 正在显示。用于去重避免冗余动画。
     */
    private var isComponentShowing = false

    /**
     * 初始化当前组件，需要与[releaseComponent]成对调用，在页面加载时初始化，在页面销毁时release。
     */
    fun initComponent(view: View) {
        view.findViewById<ViewStub>(R.id.vs_proxdr).inflate()?.let {
            brightenCompareView = it
            hideCompareView()
            it.setOnTouchListener(compareTouchListener)
            animationHelper = FeedbackAnimatorHelper(it, FeedbackAnimatorHelper.CARD_PRESS_FEEDBACK)
            applyThemeToView()
        }

        subscribeLiveDataFromViewModel()
    }

    fun onAppUiStateChanged(config: AppUiResponder.AppUiConfig) {
        if (config.windowWidth.isChanged()) {
            brightenCompareView?.post {
                brightenCompareView?.let {
                    adaptScreen(it)
                }
            }
        }
    }

    private fun adaptScreen(compareView: View) {
        val context = compareView.context
        val marginStart = if (ResourceUtils.isRTL(compareView.context)) {
            sectionPage.pageInstance.rightNaviBarHeight()
        } else {
            sectionPage.pageInstance.leftNaviBarHeight()
        } + compareView.context.resources.getDimensionPixelSize(R.dimen.photopage_pro_xdr_margin_start)

        val isVideo = sectionPage.pageViewModel.dataLoading.focusItemViewData?.isVideo == true
        val extBottomPadding = when {
            // 缩图轴高度
            isThumbLineEnabled && isThumbLineVisible -> thumbLineHeight
            // 视频播放时的ThumbnailSeekBar高度
            isVideo && isPlaybackPreviewEnabled -> context.resources.getDimensionPixelSize(R.dimen.photopage_playback_thumbnail_seekbar_height)
            // 视频播放时的默认SeekBar高度
            isVideo -> context.resources.getDimensionPixelSize(R.dimen.photopage_playback_normal_seekbar_height)
            else -> 0
        }

        // 避免导航栏和其他控件挡住推荐功能区
        compareView.updateMarginRelative(start = marginStart, bottom = defaultBottomMargin + extBottomPadding)
        GLog.d(TAG) { "[adaptScreen], start:$marginStart, defaultBottomMargin:$defaultBottomMargin, extBottomPadding:$extBottomPadding" }
    }

    /**
     * release当前组件的资源。需要在页面销毁时调用，避免产生泄漏。
     */
    fun releaseComponent() = Unit

    private fun subscribeLiveDataFromViewModel() {
        sectionPage.pageViewModel.brighten.hdrImageCompareViewAvailable.observe(this) { available ->
            updateBrightenCompareVisibility(if (available) DynamicVisibility.SHOW else DynamicVisibility.HIDE)
        }

        diffedFocusMediaTypeTimingFlow.collect(this@BrightenCompareComponent) {
            GTrace.trace({ "$TAG.diffedFocusViewData" }) {
                brightenCompareView?.let {
                    adaptScreen(it)
                }
            }
        }
        sectionPage.pageViewModel.pageManagement.thumbLineHeight.observe(this) {
            brightenCompareView?.let {
                adaptScreen(it)
            }
        }

        sectionPage.pageViewModel.pageManagement.pageTheme.map { it.floatingButtonTheme }.collect(this) {
            applyThemeToView()
        }

        sectionPage.pageViewModel.pageManagement.isThumbLineVisible.collect(this) {
            // 进入详情模式时无需进行变更
            if (sectionPage.pageViewModel.details.isDetailModeByTransition()) return@collect
            brightenCompareView?.let(::adaptScreen)
        }

        sectionPage.pageViewModel.pageManagement.shouldAnimateWhenRemoved.observe(this) {
            if (it) {
                updateBrightenCompareVisibility(DynamicVisibility.HIDE)
            }
        }
    }

    private fun applyThemeToView() {
        val theme = sectionPage.pageViewModel.pageManagement.pageTheme.value.floatingButtonTheme
        brightenCompareView?.apply {
            val backgroundColor = context.getColor(theme.background)
            setBackgroundColor(backgroundColor)
            setCircularOutline()

            ShadowUtils.clearShadow(this)
            val shadow = context.getColor(theme.shadowColor)
            addShadowLV2(shadow)

            (this as? ImageView)?.apply {
                val contentColor = ColorUtils.setAlphaComponent(context.getColor(theme.contentColor), AppConstants.Number.NUMBER_0xFF)
                imageTintList = ColorStateList.valueOf(contentColor)
            }
        }
    }

    private fun updateBrightenCompareVisibility(dynamicVisibility: DynamicVisibility) {
        GLog.d(TAG) {
            "updateBrightenCompareVisibility: dynamicVisibility = ${dynamicVisibility.name}"
        }
        var internalDynamicVisibility = dynamicVisibility
        /**
         * 此处与显示框架有一部分耦合
         * 目前若大图处于浮窗中，则不会提亮。但是[DisplayEventReceiverManager]中上报的事件，如果浮窗底部有提亮区域，显示框架无法将该场景区分出来上报，
         * 因此这里在相册做一下判断。未来若显示框架支持浮窗提亮后，可以采用提升Local HDR版本号的方式兼容。
         */
        brightenCompareView?.clearAnimation()

        if (internalDynamicVisibility.isVisible()
            && isFloatingWindowMode().not()
            && sectionPage.pageViewModel.details.isDetailModeByTransition().not()
            && (sectionPage.pageViewModel.brighten.hdrImageCompareViewAvailable.value == true)
        ) {
            val shouldAnimateWhenRemoved = sectionPage.pageViewModel.pageManagement.shouldAnimateWhenRemoved.value == true
            if (shouldAnimateWhenRemoved) {
                internalDynamicVisibility = DynamicVisibility.SHOW
            }
            if (internalDynamicVisibility == DynamicVisibility.SHOW_IMMEDIATELY) {
                brightenCompareView?.alpha = ALPHA_SHOW
                isComponentShowing = true
                showCompareView()
            } else {
                val fadeInWithSpring = {
                    startFadeIn(
                        onStart = { showCompareView() }
                    )
                }
                if (shouldAnimateWhenRemoved) {
                    brightenCompareView?.postDelayed({
                        fadeInWithSpring.invoke()
                    }, REMOVED_DELAY_ALPHA_SHOW_TIME)
                } else {
                    fadeInWithSpring.invoke()
                }
            }
        } else {
            //执行删除动画之前功能区就不可见 ,不支持消失动画
            if (brightenCompareView?.isShown == false) {
                internalDynamicVisibility = DynamicVisibility.HIDE_IMMEDIATELY
                GLog.d(TAG) {
                    "updateBrightenCompareVisibility: brightenCompareView isShown = false, unsupported HIDE animation."
                }
            } else if (sectionPage.pageViewModel.details.isDetailModeByTransition()) {
                //进入详情模式下，不支持消失动画
                internalDynamicVisibility = DynamicVisibility.HIDE_IMMEDIATELY
                GLog.d(TAG) {
                    "updateBrightenCompareVisibility: brightenCompareView in detail Model, unsupported HIDE animation."
                }
            }

            if (internalDynamicVisibility == DynamicVisibility.HIDE_IMMEDIATELY) {
                hideCompareView()
                isComponentShowing = false
                brightenCompareView?.alpha = ALPHA_HIDE
            } else {
                startFadeOut(
                    onEnd = { hideCompareView() }
                )
            }
        }
    }

    /**
     * 对比按钮淡入
     *
     * marked by caiconghu 为了减少动画时长带来的功耗增量，此处将继续采用原生动画。
     * 原效果：spring动画，bounce=0, response=0.3
     *
     * @param onStart 动画开始回调
     * @param onEnd 动画结束回调
     *
     */
    private fun startFadeIn(
        onStart: () -> Unit = {},
        onEnd: () -> Unit = {}
    ) {
        val view = brightenCompareView ?: return

        // 已经显示了，无需重复淡入
        if (isComponentShowing) {
            onStart()
            onEnd()
            return
        }

        componentFadeAnimator.apply {
            // 原动画效果先全部取消
            removeAllListeners()
            removeAllUpdateListeners()
            cancel()

            addUpdateListener {
                val v = it.animatedValue as Float
                view.alpha = v
            }

            addListener(
                onStart = {
                    onStart()
                    isComponentShowing = true
                    view.alpha = ALPHA_HIDE
                },

                onEnd = {
                    view.alpha = ALPHA_SHOW
                    onEnd()
                }
            )

            start()
        }
    }

    /**
     * 对比按钮淡淡出
     *
     * marked by caiconghu 为了减少动画时长带来的功耗增量，此处将继续采用原生动画。
     * 原效果：spring动画，bounce=0, response=0.2
     *
     * @param onStart 动画开始回调
     * @param onEnd 动画结束回调
     *
     */
    private fun startFadeOut(
        onStart: () -> Unit = {},
        onEnd: () -> Unit = {}
    ) {
        val view = brightenCompareView ?: return

        // 已经隐藏了，无需重复淡出
        if (isComponentShowing.not()) {
            onStart()
            onEnd()
            return
        }

        componentFadeAnimator.apply {
            // 原动画效果先全部取消
            removeAllListeners()
            removeAllUpdateListeners()
            cancel()

            addUpdateListener {
                val v = ALPHA_SHOW - (it.animatedValue as Float)
                view.alpha = v
            }

            addListener(
                onStart = {
                    onStart()
                    isComponentShowing = false
                    view.alpha = ALPHA_SHOW
                },

                onEnd = {
                    view.alpha = ALPHA_HIDE
                    onEnd()
                }
            )

            start()
        }
    }

    private fun showCompareView() {
        brightenCompareView?.visibility = View.VISIBLE
    }

    private fun hideCompareView() {
        brightenCompareView?.visibility = View.INVISIBLE
    }

    private fun isFloatingWindowMode(): Boolean = sectionPage.pageInstance.isFloatingWindowMode()

    override val lifecycle: Lifecycle
        get() = sectionPage.pageLifecycle

    companion object {
        private const val TAG = "BrightenCompareComponent"

        private const val ALPHA_SHOW = 1F
        private const val ALPHA_HIDE = 0F

        /**
         * 延时alpha显示compareView的时间
         */
        private const val REMOVED_DELAY_ALPHA_SHOW_TIME = PhotoAnimationConfig.PHOTO_REMOVED_DELAY_ALPHA_SHOW_TIME

        private const val FADE_ANIM_DURATION = 180L

        private val FADE_ANIM_INTERPOLATOR = COUIEaseInterpolator()
    }
}