/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MenuActionView.kt
 ** Description:
 **     {DESCRIPTION}
 **
 ** Version: 1.0
 ** Date: 2025-05-09
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>      <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>         2025/05/09  1.0          OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.photo_page.widget.menu

import android.content.Context
import android.graphics.drawable.Drawable
import android.view.Gravity
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.view.setPadding
import com.oplus.gallery.basebiz.widget.AnimatedReplacementView
import com.oplus.gallery.foundation.util.display.ScreenUtils.toPx
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.tools.setCircularOutline

/**
 * 顶部菜单栏的 ActionView，
 * 在变更图标时会做显示/隐藏动画。
 */
internal class MenuActionView(context: Context) : AnimatedReplacementView(
    context,
    showAnimationParam = AnimationParam(SHOW_ANIMATION_BOUNCE, SHOW_ANIMATION_RESPONSE),
    hideAnimationParam = AnimationParam(HIDE_ANIMATION_BOUNCE, HIDE_ANIMATION_RESPONSE),
) {
    /**
     * 当前绑定的 menuItem 的 itemId，如果没有绑定的 menuItem 则 null
     */
    val currentMenuItemId: Int? get() = menuItem?.itemId

    /**
     * 新设置的 icon 是否需要置灰显示
     * 用于 updateIcon 中判断 alpha 值，其中如果 icon 未变也会对当前 icon 设置 alpha。
     * @see updateIcon
     */
    var shouldGrayDisplay: Boolean = false

    private var menuItem: MenuItem? = null

    private var iconMenuItemId: Int? = null

    private var iconKey: Any? = null

    private var isHalfImmersive: Boolean = false

    private var currentTargetAlpha: Float = ALPHA_ENABLED

    /**
     * 将此 view 与 menuItem 进行绑定
     */
    fun bindTo(menuItem: MenuItem) {
        unbind()
        menuItem.actionView = this
        this.menuItem = menuItem
    }

    /**
     * 将此 view 与已绑定的 menuItem 解绑
     */
    fun unbind() {
        menuItem?.actionView = null
        menuItem = null
    }

    /**
     * 移除图标
     *
     * @param onEnd 结束后的回调
     */
    fun removeIcon(onEnd: (() -> Unit)?) {
        super.removeContent(true, onEnd)
        iconMenuItemId = null
        iconKey = null
    }

    /**
     * 更新当显示的图标，如果 menuItem.id 变更，则会做切换动画。
     *
     * @param menuItem 当前的 menuItem
     * @param iconKey 图标的 key：resId
     * @param icon 图标
     * @param isHalfImmersive 是否是半沉浸模式
     * @param forceAnimate 必须执行动画（删除时）
     * @param animationDelayTime 动画延迟时间
     * @param shouldIconChangeWithAnim icon 更换是否需要动画
     * @param onIconShown icon 显示动画完成后的回调
     */
    fun updateIcon(
        menuItem: MenuItem,
        iconKey: Any,
        icon: Drawable?,
        isHalfImmersive: Boolean,
        forceAnimate: Boolean,
        forceSkipAnimate: Boolean = false,
        animationDelayTime: Long,
        shouldIconChangeWithAnim: Boolean = false,
        onIconShown: () -> Unit,
    ) {
        updateIcon(
            menuItem = menuItem,
            iconKey = iconKey,
            isHalfImmersive = isHalfImmersive,
            shouldIconChangeWithAnim = shouldIconChangeWithAnim,
            onIconShown = onIconShown,
            onCreateIconView = {
                AppCompatImageView(context).apply {
                    setImageDrawable(icon)
                }
            },
            forceAnimate = forceAnimate, animationDelayTime = animationDelayTime,
            forceSkipAnimate = forceSkipAnimate
        )
    }

    /**
     * 更新当显示的图标，如果 menuItem.id 变更，则会做切换动画。
     *
     * @param menuItem 当前的 menuItem
     * @param iconKey 图标的 key, 如：动画 resId
     * @param icon 图标 view
     * @param isHalfImmersive 是否是半沉浸模式
     * @param shouldIconChangeWithAnim icon 更换是否需要动画
     * @param onIconShown icon 显示动画完成后的回调
     */
    fun updateIcon(
        menuItem: MenuItem,
        iconKey: Any,
        icon: View,
        isHalfImmersive: Boolean,
        forceSkipAnimate: Boolean = false,
        shouldIconChangeWithAnim: Boolean = false,
        onIconShown: () -> Unit,
    ) {
        updateIcon(
            menuItem = menuItem,
            iconKey = iconKey,
            isHalfImmersive = isHalfImmersive,
            forceSkipAnimate = forceSkipAnimate,
            shouldIconChangeWithAnim = shouldIconChangeWithAnim,
            onIconShown = onIconShown,
            onCreateIconView = {
                (icon.parent as? ViewGroup)?.removeView(icon)
                icon
            })
    }

    /**
     * 更新当显示的图标，如果 menuItem.id 变更，则会做切换动画。
     *
     * @param menuItem 当前的 menuItem
     * @param iconKey 图标的 key
     * @param isHalfImmersive 是否是半沉浸模式
     * @param shouldIconChangeWithAnim icon 更换是否需要动画
     * @param onIconShown icon 显示动画完成后的回调
     * @param onCreateIconView 用于获取图标 view
     * @param forceAnimate 必须执行动画（删除时）
     * @param animationDelayTime 动画延迟时间
     */
    private fun updateIcon(
        menuItem: MenuItem,
        iconKey: Any,
        isHalfImmersive: Boolean,
        shouldIconChangeWithAnim: Boolean,
        onIconShown: () -> Unit,
        onCreateIconView: () -> View,
        forceAnimate: Boolean = false,
        forceSkipAnimate: Boolean = false,
        animationDelayTime: Long = 0L,
    ) {
        if (menuItem != this.menuItem) {
            bindTo(menuItem)
        }

        val targetAlpha = getAlphaValueByEnableState(isEnabled && !shouldGrayDisplay)

        // 没有变则无需更新
        val hasIcon = (currentContent != null)
        val isSameWithPre = (iconMenuItemId == menuItem.itemId)
                && (this.iconKey == iconKey)
                && (this.isHalfImmersive == isHalfImmersive)
                && (currentTargetAlpha == targetAlpha)
        if (hasIcon && isSameWithPre) {
            onIconShown.invoke()
            return
        }

        val shouldAnimateOnIconChanged = shouldIconChangeWithAnim && this.iconKey != iconKey
        // 只有在id变化时，或者删除时，需要做动画 (切换 focus 时，当前图标变更为其他图标),或者开启icon切换动画并且icon发成切换时
        val shouldShowAnimation = iconMenuItemId != menuItem.itemId || forceAnimate || shouldAnimateOnIconChanged

        // 创建新的图标 view
        val newChild = if (isHalfImmersive) {
            createHalfImmersiveContainer(onCreateIconView())
        } else {
            createNormalContainer(onCreateIconView())
        }

        super.replaceContent(
            child = newChild,
            withAnimation = forceSkipAnimate.not() && shouldShowAnimation,
            showAlphaValue = targetAlpha,
            onContentShown = onIconShown,
            showAnimDelayTime = animationDelayTime
        )

        this.iconMenuItemId = menuItem.itemId
        this.iconKey = iconKey
        this.isHalfImmersive = isHalfImmersive
        currentTargetAlpha = targetAlpha

        contentDescription = menuItem.contentDescription?.takeIf { it.isNotBlank() } ?: menuItem.title
    }

    /**
     * 创建普通图标的容器
     */
    private fun createNormalContainer(content: View): View {
        return FrameLayout(context).apply {
            layoutDirection = if (ResourceUtils.isRTL(context)) View.LAYOUT_DIRECTION_RTL else View.LAYOUT_DIRECTION_LTR
            layoutParams = LayoutParams(NORMAL_ICON_VIEW_SIZE.toPx, NORMAL_ICON_VIEW_SIZE.toPx).apply {
                gravity = Gravity.CENTER
            }
            setPadding(NORMAL_ICON_PADDING.toPx)
            addView(content)
        }
    }

    /**
     * 创建半沉浸式图标的容器
     */
    private fun createHalfImmersiveContainer(content: View): View {
        val newIconView = FrameLayout(context).apply {
            layoutDirection = if (ResourceUtils.isRTL(context)) View.LAYOUT_DIRECTION_RTL else View.LAYOUT_DIRECTION_LTR
            layoutParams = LayoutParams(IMMERSIVE_ICON_VIEW_SIZE.toPx, IMMERSIVE_ICON_VIEW_SIZE.toPx).apply {
                gravity = Gravity.CENTER
            }
            setPadding(IMMERSIVE_ICON_VIEW_PADDING.toPx)
            addView(content)

            // MarkedBy linkailong 质感需重新适配
            setBackgroundColor(context.getColor(R.color.photopage_menu_half_immersive_icon_bg))
            setCircularOutline()
        }

        return FrameLayout(context).apply {
            setPadding(IMMERSIVE_ICON_CONTAINER_PADDING.toPx)
            addView(newIconView)
        }
    }

    private fun getAlphaValueByEnableState(isEnable: Boolean): Float = if (isEnable) ALPHA_ENABLED else ALPHA_DISABLED

    companion object {
        private const val TAG = "MenuActionView"

        /**
         * 普通模式 icon view的大小, dp
         */
        private const val NORMAL_ICON_VIEW_SIZE = 48

        /**
         * 普通模式 icon 容器的 padding, dp
         */
        private const val NORMAL_ICON_PADDING = 12

        /**
         * 半沉浸模式导航键 view 的大小, dp
         */
        private const val IMMERSIVE_ICON_VIEW_SIZE = 30

        /**
         * 半沉浸模式 icon view 的 padding, dp
         */
        private const val IMMERSIVE_ICON_VIEW_PADDING = 6

        /**
         * 半沉浸模式 icon 容器的 padding, dp
         */
        private const val IMMERSIVE_ICON_CONTAINER_PADDING = 6

        /**
         * 显示动画的弹性
         */
        private const val SHOW_ANIMATION_BOUNCE = 0f

        /**
         * 显示动画的 response
         */
        private const val SHOW_ANIMATION_RESPONSE = 0.3f

        /**
         * 隐藏动画的弹性
         */
        private const val HIDE_ANIMATION_BOUNCE = 0f

        /**
         * 隐藏动画的 response
         */
        private const val HIDE_ANIMATION_RESPONSE = 0.25f

        /**
         * 按钮不可用时的Alpha值
         */
        private const val ALPHA_DISABLED = 0.3f

        /**
         * 按钮可用时的Alpha值
         */
        private const val ALPHA_ENABLED = 1.0f
    }
}