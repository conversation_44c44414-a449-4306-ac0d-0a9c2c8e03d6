/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoMenuDetailActionRule.kt
 ** Description : 大图菜单详细信息操作
 ** Version     : 1.0
 ** Date        : 2022/03/18
 ** Author      : <PERSON>@Apps.Gallery
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON>@Apps.Gallery              2022/03/18  1.0         create file
 *********************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules

import androidx.annotation.IdRes
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.business_lib.menuoperation.MenuAction
import com.oplus.gallery.business_lib.menuoperation.MenuActionGetter.details
import com.oplus.gallery.business_lib.menuoperation.MenuOperationManager
import com.oplus.gallery.business_lib.menuoperation.base.MenuResult
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant.Value.MENU_ITEM_DETAIL
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.dataloading.DiffedPhotoItemViewData
import com.oplus.gallery.photo_page.viewmodel.dataloading.PhotoItemViewData

/**
 * 大图菜单详细信息操作
 */
internal class PhotoMenuDetailActionRule(
    @IdRes ruleAction: Int,
    viewModel: PhotoViewModel,
    private val activity: BaseActivity
) : PhotoMenuActionRule(TAG, ruleAction, viewModel) {

    /**
     * 菜单执行结果的句柄，用于取消菜单执行任务
     */
    private var menuResult: MenuResult? = null

    /**
     * 菜单事件完成回调
     */
    private var onDone: (() -> Unit)? = null

    /**
     * 菜单执行回调的锁，避免多线程问题。
     */
    private val actionLock = Any()

    override fun onFocusViewDataChanged(diffedFocusViewData: DiffedPhotoItemViewData): Unit =
        synchronized(actionLock) {
            menuResult?.let {
                // 如果 menuResult 不为空，说明正在显示详情页
                if (diffedFocusViewData.isContentChanged) {
                    // 如果是图片发生改变，隐藏详情页
                    it.cancel()
                    onDone?.invoke()
                } else if (diffedFocusViewData.isFileMoved()) {
                    // 如果图片没有改变，但文件被移动了，重新show详情页
                    it.cancel()
                    diffedFocusViewData.newItem?.let { itemViewData ->
                        val mediaItem = viewModel.dataLoading.getMediaItem(itemViewData)
                        if (mediaItem != null) {
                            showDetailPage(mediaItem)
                        }
                    }
                } else Unit
            }
        }

    override fun execute(viewData: PhotoItemViewData, mediaItem: MediaItem, onDone: () -> Unit) {
        // 埋点： [MENU_ITEM_DETAIL]菜单项被点击
        trackMenuClick(MENU_ITEM_DETAIL)
        synchronized(actionLock) { this.onDone = onDone }
        // 展示详情页
        showDetailPage(mediaItem)
    }

    override fun isOperationSupportedForQuick(): Boolean {
        return false
    }

    /**
     * 展示详情页面
     */
    private fun showDetailPage(mediaItem: MediaItem) {
        menuResult = MenuOperationManager.doAction(
            action = MenuAction.DETAILS,
            paramMap = details.builder
                .setActivity(activity)
                .setMediaItem(mediaItem)
                .setDetailsOperationRegister {}
                .setTrackCallerEntry(trackCaller)
                .build(),
            onCompleted = { _, _ ->
                viewModel.details.changeDetailsMode(enter = viewModel.details.isInDetailsMode.value != true)
                synchronized(actionLock) {
                    onDone?.invoke()
                    menuResult = null
                    onDone = null
                }
            }
        )
    }

    /**
     * 文件被移动。
     * 场景： 重命名或者移动到其它文件夹。
     */
    private fun DiffedPhotoItemViewData.isFileMoved(): Boolean {
        return (oldItem?.filePath != newItem?.filePath)
    }


    companion object {
        private const val TAG = "PhotoMenuDetailActionRule"
    }
}