/*********************************************************************************
 ** Copyright (C), 2022-2032, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PlaybackThumbnailUIController.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/03/09
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>               2023/03/09  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.ui.section.playback.uicontroller

import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.lifecycle.Observer
import com.coui.appcompat.animation.COUIEaseInterpolator
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.ext.fadeIn
import com.oplus.gallery.foundation.util.ext.fadeOut
import com.oplus.gallery.foundation.util.ext.isRtl
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.ui.section.playback.PhotoPlaybackSection.IPlaybackControl
import com.oplus.gallery.photo_page.ui.theme.PhotoPageTheme
import com.oplus.gallery.photo_page.viewmodel.playbackcontrol.PlaybackInfo
import com.oplus.gallery.photo_page.widget.seekbar.ISeekBar
import com.oplus.gallery.photo_page.widget.seekbar.ISliceRetrieverFactory
import com.oplus.gallery.photo_page.widget.seekbar.ThumbnailSeekBar

/**
 * 大图播控子模块，大图定制化的基础 UI 控制器，继承 [PlaybackDefaultUIController] 特性
 *
 * 样式：[R.layout.photo_section_playback_thumbnail]
 *
 * 扩展特性：
 *  - Seek：[ISeekBar] 为自定义的带有缩图显示的 SeekBar 样式
 *  - 缩图获取：使用 [viewModel] 间接获取 [ISliceRetrieverFactory] 得到
 *
 *  markedBy linkailong, 此样式暂时没有被用到，暂时保留
 */
internal class PlaybackThumbnailUIController : PlaybackDefaultUIController() {

    private var sliceRetrieverFactoryObserver: Observer<ISliceRetrieverFactory?>? = null

    override val layoutId: Int = R.layout.photo_section_playback_thumbnail

    override fun onCreateViewHolder(container: ViewGroup, playbackRoot: View): PlaybackDefaultViewHolder {
        return PlaybackThumbnailViewHolder(container, playbackRoot)
    }

    override fun onBindViewHolder(playbackVH: PlaybackDefaultViewHolder) {
        super.onBindViewHolder(playbackVH)

        if (playbackVH !is PlaybackThumbnailViewHolder) {
            GLog.e(TAG) { "[onBindViewHolder] ignore, not PlaybackThumbnailViewHolder" }
            return
        }

        playbackVH.sbPlaybackProgress.onSeekBarChangeListener = OnThumbnailPlaybackSeekBarChangeListener(playbackVH, playbackControl)

        val thumbnailSeekBar = playbackVH.sbPlaybackProgress as ThumbnailSeekBar
        sliceRetrieverFactoryObserver = Observer<ISliceRetrieverFactory?>(thumbnailSeekBar::sliceRetrieverFactory::set)
        sliceRetrieverFactoryObserver?.let(viewModel.playback.sliceRetrieverFactory::observeForever)
    }

    override fun onUnbindViewHolder() {
        sliceRetrieverFactoryObserver?.let(viewModel.playback.sliceRetrieverFactory::removeObserver)
        sliceRetrieverFactoryObserver = null
    }

    override fun onUpdateTimeTipsStatus(playbackVH: PlaybackDefaultViewHolder, playbackTimeMsg: PlaybackTimeMsg) {
        if (playbackVH !is PlaybackThumbnailViewHolder) {
            GLog.e(TAG) { "[onUpdateTimeTipsStatus] ignore, not PlaybackThumbnailViewHolder" }
            return
        }
        val (progressMsg, durationMsg) = playbackTimeMsg
        val timeText = if (playbackVH.tvThumbnailPlaybackMessage.isRtl) "$durationMsg / $progressMsg" else "$progressMsg / $durationMsg"
        playbackVH.tvThumbnailPlaybackMessage.text = timeText
    }

    override fun onUpdatePlaybackInfo(playbackVH: PlaybackDefaultViewHolder, playbackInfo: PlaybackInfo?, lastPlaybackInfo: PlaybackInfo?) {
        if (playbackVH !is PlaybackThumbnailViewHolder) {
            GLog.e(TAG) { "[onUpdatePlaybackInfo] ignore, not PlaybackThumbnailViewHolder" }
            return
        }
        invalidateThumbnailSeekbarIfNeeded(playbackVH, playbackInfo, lastPlaybackInfo)
    }

    override fun onThemeUpdated(theme: PhotoPageTheme) {}

    private fun invalidateThumbnailSeekbarIfNeeded(
        playbackVH: PlaybackThumbnailViewHolder,
        playbackInfo: PlaybackInfo?,
        lastPlaybackInfo: PlaybackInfo?
    ) {
        val playerToken = playbackInfo?.playerToken
        val lastPlayerToken = lastPlaybackInfo?.playerToken
        val isPlayerChanged = playerToken != lastPlayerToken
        val shouldForceInvalidate = playbackInfo?.shouldSliceForceInvalidate == true
        if (isPlayerChanged || shouldForceInvalidate) {
            GLog.d(TAG) {
                "[invalidateThumbnailSeekbarIfNeeded] isPlayerChanged=$isPlayerChanged, shouldForceInvalidate=$shouldForceInvalidate"
            }
            playbackVH.sbPlaybackProgress.invalidateForce()
        }
    }

    /**
     * 因为泛型原因，此处不能使用 private 修饰
     */
    internal class PlaybackThumbnailViewHolder(
        container: ViewGroup,
        playbackRoot: View
    ) : PlaybackDefaultViewHolder(container, playbackRoot) {

        override val sbPlaybackProgress: ThumbnailSeekBar
            get() = super.sbPlaybackProgress as ThumbnailSeekBar

        /**
         * 缩图播控条中的文字进度展示（展示）
         */
        val tvThumbnailPlaybackMessage: TextView by bind(R.id.tv_thumbnail_playback_message)
    }

    private inner class OnThumbnailPlaybackSeekBarChangeListener(
        private val playbackVH: PlaybackThumbnailViewHolder,
        playbackControl: IPlaybackControl
    ) : OnPlaybackSeekBarChangeListener(playbackControl) {

        override fun onStartTrackingTouch(seekBar: ISeekBar) {
            super.onStartTrackingTouch(seekBar)
            playbackVH.tvThumbnailPlaybackMessage.fadeIn(DEFAULT_ANIM_DURATION, defaultInterpolator)
        }

        override fun onStopTrackingTouch(seekBar: ISeekBar) {
            super.onStopTrackingTouch(seekBar)
            playbackVH.tvThumbnailPlaybackMessage.fadeOut(DEFAULT_ANIM_DURATION, defaultInterpolator)
        }
    }

    private companion object {
        private const val TAG = "PlaybackThumbnailUIController"

        /**
         * View动画的默认时长，300毫秒。
         */
        private const val DEFAULT_ANIM_DURATION = 300L

        /**
         * 播控视图做动画时默认使用的插值器
         */
        private val defaultInterpolator = COUIEaseInterpolator()
    }
}