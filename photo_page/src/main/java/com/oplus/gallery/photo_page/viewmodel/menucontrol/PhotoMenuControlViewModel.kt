/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoMenuControlViewModel.kt
 ** Description : 大图页面菜单数据管理
 ** Version     : 1.0
 ** Date        : 2022/02/17
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><EMAIL>              2022/02/17  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.menucontrol

import android.content.Intent
import android.graphics.Rect
import android.os.Bundle
import android.os.Looper
import android.provider.MediaStore.Files.FileColumns.MEDIA_TYPE_VIDEO
import android.view.Display
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.viewModelScope
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_DISPLAY_SET_COVER_MENU
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.helper.restrict.RestrictButtonConfigUtils
import com.oplus.gallery.business.aiunitdownload.groupphoto.GroupPhotoModelUpdater
import com.oplus.gallery.business.open.PhotoPageOpenAbilityCaller
import com.oplus.gallery.business_lib.aiunitdownload.AIUnitFeatureConfigHelper
import com.oplus.gallery.business_lib.aiunitdownload.ModelUpdateListener
import com.oplus.gallery.business_lib.gifsynthesis.GifSynthesisSpecificationChecker.checkCShotItemMatchGifRule
import com.oplus.gallery.business_lib.helper.PhotoDataHelper.isPreviewType
import com.oplus.gallery.business_lib.menuoperation.helper.OpenInSystemPlayerHelper
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.UriImage
import com.oplus.gallery.business_lib.model.data.base.item.isFastCShotQuickImage
import com.oplus.gallery.business_lib.model.data.base.item.isGroupPhoto
import com.oplus.gallery.business_lib.model.data.base.item.isHdrVideoOlive
import com.oplus.gallery.business_lib.model.data.base.item.isOlivePhoto
import com.oplus.gallery.business_lib.model.data.base.item.isOnlinePlayableVideo
import com.oplus.gallery.business_lib.model.data.base.item.isSupportCasting
import com.oplus.gallery.business_lib.model.data.base.item.isSupportDownloadOriginal
import com.oplus.gallery.business_lib.model.data.base.item.isSupportPassAddToGoogleWallet
import com.oplus.gallery.business_lib.model.data.base.item.isTemporaryCacheItem
import com.oplus.gallery.business_lib.model.data.base.item.isVideo
import com.oplus.gallery.business_lib.model.data.base.source.DataManager
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.CameraMode.FLAG_AI_ID
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_CSHOT
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_ENHANCE_TEXT
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_HEIF
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_PORTRAIT_BLUR
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_AI_ID
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_CLOUD_DOWNLOAD
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_CONVERT_PDF
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_CSHOT
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_DELETE
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_DLNA
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_EDIT
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_ENHANCE_TEXT
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_EXPORT_OLIVE
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_EXPORT_VIDEO
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_FAVORITES
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_FREE_FACE
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_GOOGLE_PASS_SCAN
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_GROUP_PHOTO
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_INFO
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_OLIVE_PHOTO
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_OPEN_IN_SYSTEM_PLAYER
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_PORTRAIT_BLUR
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_PREVIEW_CHECK
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_RENAME_FILE
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_SAVE_TO_GIF
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_SELF_SPLIT
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_SETAS_CONTACT
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_SETAS_WALLPAPER
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_SET_COVER
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_TRANSFORM_HEIF_TO_JPEG
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_TRANSFORM_TO_SDR
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_VIDEO_GET_FRAME
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_CAMERA
import com.oplus.gallery.business_lib.model.data.base.utils.ImageTypeUtils
import com.oplus.gallery.business_lib.model.data.base.utils.MediaItemUtils.isItemSupportFormat
import com.oplus.gallery.business_lib.model.data.base.utils.VideoTypeUtils
import com.oplus.gallery.business_lib.model.data.local.utils.LocalMediaDataHelper
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE
import com.oplus.gallery.foundation.database.util.DatabaseUtils
import com.oplus.gallery.foundation.ui.split.SplitOperationUsability
import com.oplus.gallery.foundation.util.datastructure.LongFlags
import com.oplus.gallery.foundation.util.datastructure.toFlags
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.collectNotNull
import com.oplus.gallery.foundation.util.ext.setOrPostValue
import com.oplus.gallery.foundation.util.ext.updateValueIfChanged
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils
import com.oplus.gallery.foundation.util.systemcore.KeyguardManagerUtils
import com.oplus.gallery.foundation.util.systemcore.TemperatureUtil
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING
import com.oplus.gallery.framework.abilities.config.IConfigListener
import com.oplus.gallery.framework.abilities.config.ISettingsAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.GroupPhoto.FIRST_USING_GROUP_PHOTO
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.FIRST_USING_RESTRICT_WATERMARK_TAG
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_DEVICE_SUPPORT_AI_ELIMINATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_ENABLE_MENU_INFLATE_POSTPONE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_CSHOT_CONTINUATION
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_GIF_SYNTHESIS
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_HASSEL_WATERMARK
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_OLIVE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_PHOTO_EDITOR_WATERMARK
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_PORTRAIT_BLUR_BEFORE_OS16
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_PRIVACY_WATERMARK
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_QUICK_PHOTO_DELETE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_RAW_EDIT
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_SPRING_FESTIVAL_WATERMARK
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_SUPER_TEXT_V2
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_VIDEO_WALLPAPER
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Pages.PhotoPage.DialogWindow.AI_ELIMINATE_HAS_USED
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_PRODUCT_LIGHT
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REALME_BRAND
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REGION_CN
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_SUPPORT_DOLBY_DECODE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_SUPPORT_DOLBY_ENCODE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_SUPPORT_HLG_ENCODE
import com.oplus.gallery.framework.abilities.groupphoto.IGroupPhotoAbility
import com.oplus.gallery.framework.abilities.moduleaccess.IModuleAccessAbility
import com.oplus.gallery.framework.abilities.moduleaccess.PhotoPageModuleOperations
import com.oplus.gallery.framework.abilities.moduleaccess.PhotoPageModuleTopics
import com.oplus.gallery.framework.abilities.open.OpenAbilityConstant
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.ui.section.photomenu.MenuIcon
import com.oplus.gallery.photo_page.ui.section.photomenu.PhotoMenuIconDrawableManager
import com.oplus.gallery.photo_page.ui.theme.MenuDecorationTheme
import com.oplus.gallery.photo_page.viewmodel.PhotoSubViewModel
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.dataloading.PhotoDataLoadingViewModel.Companion.INVALID_INDEX
import com.oplus.gallery.photo_page.viewmodel.dataloading.PhotoItemViewData
import com.oplus.gallery.photo_page.viewmodel.dataloading.diffedFocusViewDataDistinctUi
import com.oplus.gallery.photo_page.viewmodel.dataloading.focusItemViewData
import com.oplus.gallery.photo_page.viewmodel.dataloading.photoViewDataSet
import com.oplus.gallery.photo_page.viewmodel.details.PhotoDetailsConstants
import com.oplus.gallery.photo_page.viewmodel.funcRecommend.FuncDetectInfo
import com.oplus.gallery.photo_page.viewmodel.funcRecommend.FuncItem
import com.oplus.gallery.photo_page.viewmodel.funcRecommend.PhotoFuncViewModel.Companion.filterNonNeedShowInDetailFuncItems
import com.oplus.gallery.photo_page.viewmodel.funcRecommend.Status
import com.oplus.gallery.photo_page.viewmodel.inputarguments.PhotoInputArgumentsViewModel
import com.oplus.gallery.photo_page.viewmodel.inputarguments.PhotoInputArgumentsViewModel.DataSource.DataSet
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoFavoritesStateCache
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PageOperationState
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PageOperationState.DisableMenuAll
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PageOperationState.SuppressMenuCaptureVideoFrame
import com.oplus.gallery.standard_lib.app.CPU
import com.oplus.gallery.standard_lib.codec.player.AVController
import com.oplus.gallery.standard_lib.util.network.NetworkMonitor
import com.oplus.gallery.standard_lib.util.network.NetworkMonitor.ConnectType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

/**
 * 大图页面菜单数据管理
 */
@Suppress("LargeClass")
class PhotoMenuControlViewModel(
    override val pageViewModel: PhotoViewModel
) : PhotoSubViewModel(pageViewModel) {

    private var playerState: AVController.LoadingState? = null

    /**
     * [ISettingsAbility]配置能力的对象，避免频繁创建
     */
    private val settingsAbility: ISettingsAbility?
        get() = run {
            if (_settingsAbility == null) {
                _settingsAbility = initSettingsAbility()
            }
            _settingsAbility
        }
    private var _settingsAbility: ISettingsAbility? = null

    /**
     * [IModuleAccessAbility]配置能力的对象，避免频繁创建
     */
    private val moduleAccessAbility: IModuleAccessAbility?
        get() = run {
            if (_moduleAccessAbility == null) {
                _moduleAccessAbility = initModuleAccessAbility()
            }
            _moduleAccessAbility
        }
    private var _moduleAccessAbility: IModuleAccessAbility? = null

    /**
     * [IGroupPhotoAbility]配置能力的对象，避免频繁创建
     */
    private val groupPhotoAbility: IGroupPhotoAbility?
        get() = run {
            if (_groupPhotoAbility == null) {
                _groupPhotoAbility = initGroupPhotoAbility()
            }
            _groupPhotoAbility
        }
    private var _groupPhotoAbility: IGroupPhotoAbility? = null

    /**
     * 提供菜单项属性信息的查阅
     */
    private val menuInformation by lazy { PhotoMenuItemInfoTable() }

    /**
     * 正在执行操作的菜单项
     */
    private val executingActions = mutableSetOf<Int>()

    /**
     * 负责执行菜单项对应的功能
     */
    private var menuExecutor: PhotoMenuExecutor? = null

    /**
     * 当前的菜单预设配置
     */
    private var menuPresent: PhotoMenuPresent = PhotoMenuPresent.None

    /**
     * 当前菜单支持的操作项
     */
    private val supportedOperations: LongFlags = LongFlags(0)

    /**
     * 菜单数据更新任务，此任务需要保持唯一，因此做此记录以便任务去重
     */
    private var menuDataUpdatingJob: Job? = null

    /**
     * 需要被抑制的菜单action集合
     */
    private val suppressMenuIconArray by lazy {
        intArrayOf(
            R.id.action_group_photo, // 合影优化
            R.id.action_olive_photo, // olive 播放
            R.id.action_portrait_blur, // 人像
            R.id.action_aiidphoto, // 证件照
            R.id.action_cshot, // 连拍
            R.id.action_enhancetext  // 超级文本1.0
        )
    }

    /**
     * 需要检测网络状态的action数组
     */
    private val needCheckNetworkStatusActions by lazy {
        intArrayOf(
            R.id.action_image_quality_enhance, // AI画质增强
            R.id.action_ai_repair_deblur,
            R.id.action_ai_repair_dereflection,
            R.id.action_google_pass_scan,
            R.id.action_ai_best_take,
            R.id.action_ai_lighting, //AI补光
        )
    }

    /**
     * 抑制菜单显示
     * 抑制：true
     * 不抑制：false
     */
    private var suppressMenuIconShow: Boolean = false

    /**
     * 系统播放器是否可用
     * true：可用
     * false：不可用
     */
    private var isSystemPlayerAvailable: Boolean = false

    /////////////////// 以下为对外输出状态 //////////////////////

    /**
     * 收藏状态缓存。
     */
    // Marked by Johnny 2022/6/15 for 李程里 这个为什么要公开？为了访问效率？
    val photoFavoriteStateCache: PhotoFavoritesStateCache by lazy { PhotoFavoritesStateCache() }

    /**
     * 底部菜单
     */
    val photoMenu: StateFlow<MenuState> get() = _photoMenu.asStateFlow()
    private val _photoMenu = MutableStateFlow<MenuState>(MenuState())

    /**
     * 气泡弹窗
     */
    val bubbleWindow: LiveData<BubbleWindowState> get() = _bubbleWindow
    private val _bubbleWindow = MutableLiveData<BubbleWindowState>()

    /**
     * 大图顶部标题
     */
    val photoTitle: StateFlow<PhotoMenuTitle> get() = _photoTitle
    private val _photoTitle = MutableStateFlow(
        PhotoMenuTitle(
            navigationIcon = MenuIcon.NULL,
            mediaKey = EMPTY_STRING,
            title = EMPTY_STRING,
            subTitle = EMPTY_STRING
        )
    )

    /**
     * 最近一次点击的菜单项
     */
    val latestMenuItemClickId: LiveData<Int> get() = _latestMenuItemClickId
    private val _latestMenuItemClickId = MutableLiveData<Int>()

    /**
     * 当前是否有菜单项正在执行
     */
    val menuActionExecuting: LiveData<Boolean> get() = _menuActionExecuting
    private val _menuActionExecuting = MutableLiveData<Boolean>()

    /**
     * 交互要求 大图页面有显性提示的菜单项被点击（如移动到图集底部通知，复制到图集进度条）时，需要影藏quick加载动画
     *
     * 此监听用于quick图场景下，右下角加载圈圈的显示，如果菜单项被点击，则不需要再去显示quick图的加载状态了
     */
    internal val menuActionIsClick: LiveData<Boolean> get() = _menuActionIsClick
    private val _menuActionIsClick: MutableLiveData<Boolean> = MutableLiveData(false)

    /**
     * 当前需要清除的tips,Marked by ruimin,后面做Tips逻辑整合统一整改掉
     */
    val clearTips: LiveData<Int> get() = _clearTips
    private val _clearTips = MutableLiveData<Int>()

    /**
     * 需要清除的水印角标
     */
    internal val clearWatermarkCorner: LiveData<Boolean> get() = _clearWatermarkCorner
    private val _clearWatermarkCorner = MutableLiveData<Boolean>()

    /**
     * 当前展示的水印角标Id
     */
    private var currentWatermarkCornerId: String? = null

    /**
     * 是否支持HLG编码
     */
    private val supportHlgTransform by lazy {
        ConfigAbilityWrapper.getBoolean(IS_SUPPORT_HLG_ENCODE)
    }

    /**
     * 是否支持杜比编解码
     */
    private val supportDolbyTransform by lazy {
        ConfigAbilityWrapper.let {
            it.getBoolean(IS_SUPPORT_DOLBY_DECODE) && it.getBoolean(IS_SUPPORT_DOLBY_ENCODE)
        }
    }

    /**
     * 是否支持GIF合成
     */
    private val supportGifSynthesis by lazy {
        ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_GIF_SYNTHESIS)
    }

    /**
     * 是否支持合影优化
     */
    internal val supportGroupPhoto: Boolean by lazy {
        var result = false
        groupPhotoAbility?.let { groupPhotoAbility ->
            result = (groupPhotoAbility.isSupportGroupPhoto())
        }
        result
    }

    /**
     * 大图底部菜单栏显示与隐藏
     */
    internal val bottomMenuVisibility: LiveData<Boolean> get() = _bottomMenuVisibility
    private val _bottomMenuVisibility: MutableLiveData<Boolean> = MutableLiveData()

    /**
     * 菜单的主题,
     *
     * 如使用方依赖于 [PhotoPageManagementViewModel.photoDecorationState] 的监听时，使用此 flow 会有时机问题，导致值不准确，
     * 请直接使用 [PhotoMenuSection.shouldUseHalfImmersiveMode] 判断替代。
     *
     * Pair(顶部菜单主题、底部菜单主题)
     */
    internal val menuTheme: StateFlow<Pair<MenuDecorationTheme, MenuDecorationTheme>> get() = _menuTheme
    private val _menuTheme: MutableStateFlow<Pair<MenuDecorationTheme, MenuDecorationTheme>> by lazy {
        val defaultTheme = pageViewModel.pageManagement.pageTheme.value.menuDecorationTheme
        MutableStateFlow(Pair(defaultTheme, defaultTheme))
    }

    /**
     * 是否需要推迟进入大图时菜单组件的加载
     * marked by caiconghu 某些项目上进大图动画受到子线程菜单加载的影响导致掉帧，需要菜单错峰加载
     */
    internal val enablePostponeMenuInflate: Boolean by lazy {
        ConfigAbilityWrapper.getBoolean(FEATURE_IS_ENABLE_MENU_INFLATE_POSTPONE)
    }

    /**
     * 大图时菜单组件如果需要被推迟加载，则加载方根据此信号判断当前能否加载菜单
     * see [enablePostponeMenuInflate]
     */
    internal var couldInflateMenuIfPostponed: Boolean = false
        private set

    /**
     * 更新菜单用的聚合 Flow，
     * photoMenuDataEmitter 用于发射数据，订阅需使用 photoMenuData
     * photoMenuData 用于收集最新的菜单数据，会做去重和筛选不合法更新
     */
    private val photoMenuDataEmitter: MutableStateFlow<MenuDependentData> = MutableStateFlow(MenuDependentData())
    private val photoMenuData: Flow<MenuDependentData> = photoMenuDataEmitter
        .filter {
            when {
                it.focusSlot == INVALID_INDEX -> {
                    GLog.d(TAG, LogFlag.DL) { "[updateMenuFlow] focusSlot is not valid" }
                    return@filter false
                }
            }
            return@filter true
        }


    /**
     * 更新标题用的聚合 Flow，
     * 此 Flow 作为调用 updatePhotoTitle 的唯一入口
     */
    private val updateTitleFlow: MutableStateFlow<TitleDependentData> = MutableStateFlow(TitleDependentData())

    /**
     * 大图底部菜单栏高度
     */
    internal val bottomMenuHeight: LiveData<Int> get() = _bottomMenuHeight
    private val _bottomMenuHeight: MutableLiveData<Int> = MutableLiveData()

    /**
     * 大图顶部菜单栏高度
     */
    internal val topMenuHeight: LiveData<Int> get() = _topMenuHeight
    private val _topMenuHeight: MutableLiveData<Int> = MutableLiveData()

    /**
     * 当前currentFuncDetectInfo，用来判断是否需要更新
     */
    private var currentFuncDetectInfo: FuncDetectInfo? = null

    /**
     * 是否可以点击顶部菜单按键
     * 在动效期间未false，未开始动效和动效结束为true
     */
    val canClickTopMenu: LiveData<Boolean> get() = _canClickTopMenu
    private val _canClickTopMenu = MutableLiveData<Boolean>()

    /**
     * 是否可以点击底部菜单按键
     * 在动效期间未false，未开始动效和动效结束为true
     */
    val canClickBottomMenu: LiveData<Boolean> get() = _canClickBottomMenu
    private val _canClickBottomMenu = MutableLiveData<Boolean>()

    /**
     * 支持阻断的菜单项
     */
    private val suppressibleMenuItems by lazy {
        intArrayOf(
            R.id.action_details
        )
    }

    /////////////////// 以下为外部输入事件 //////////////////////

    /**
     * 在需要的时候抑制菜单图标显示，此方法会刷新菜单项
     *
     * 当前此抑制只对以下图标生效：
     * 1.人像编辑按钮
     * 2.olive播放按钮
     *
     * @param isSuppress 是否需要抑制 ，默认值为true需要抑制图标显示
     *
     */
    fun suppressMenuIconShow(isSuppress: Boolean = true) {
        suppressMenuIconShow = isSuppress
        GLog.d(TAG, LogFlag.DL) { "[suppressMenuIconShow] isSuppress = $isSuppress" }
        photoMenuDataEmitter.tryEmit(photoMenuDataEmitter.value.copy(suppressMenuIconShow = suppressMenuIconShow))
    }

    /**
     * 更新大图底部菜单栏显示与隐藏
     */
    fun notifyBottomMenuVisibility(isShow: Boolean) {
        _bottomMenuVisibility.setOrPostValue(isShow)
    }

    /**
     * 分发menuActionIsClick 菜单点击的结果
     */
    fun setMenuActionIsClick(b: Boolean) {
        _menuActionIsClick.setOrPostValue(b)
    }

    /**
     * 外部网络变更时调用，参数同 NetworkMonitor.NetworkListener
     */
    internal fun notifyNetworkChanged(connectType: ConnectType, isValidated: Boolean) {
        GLog.d(TAG, LogFlag.DL) { "[onNetworkChanged] emit, connectType: $connectType, isValidated: $isValidated" }
        photoMenuDataEmitter.tryEmit(
            photoMenuDataEmitter.value.copy(networkConnectType = connectType, networkIsValidated = isValidated)
        )
    }

    /**
     * 更新 GooglePassScan 的最新状态，
     * 目前仅 GooglePassScan 可用时才会调进来
     */
    internal fun notifyGooglePassScanState(state: FuncItem.State) {
        GLog.d(TAG, LogFlag.DL) { "[updateGooglePassScanState] emit new state: $state" }
        photoMenuDataEmitter.tryEmit(
            photoMenuDataEmitter.value.copy(googlePassScanState = state)
        )
    }

    /**
     * 通知菜单规则已经创建，因为规则内部需要持有Fragment，故通过外部传入。
     */
    fun setupMenuExecutor(executor: PhotoMenuExecutor) {
        menuExecutor = executor
    }

    /**
     * 通知菜单项被点击了
     * @param action 菜单项
     * @param isLongClicked 是否长按事件
     * @param extras 点击菜单的附带参数
     */
    fun notifyMenuItemClicked(action: Int, isLongClicked: Boolean = false, extras: Bundle? = null) {
        GLog.i(TAG, LogFlag.DL, "[notifyMenuItemClicked] action: $action")

        if (executingActions.contains(action)) {
            GLog.i(TAG, LogFlag.DL, "[notifyMenuItemClicked] repetition action, ignore!")
            return
        }
        if (needCheckNetworkStatusActions.contains(action) && NetworkMonitor.isNetworkConnected().not()) {
            pageViewModel.pageManagement.showShortToast(
                pageViewModel.context.getString(com.oplus.gallery.basebiz.R.string.common_network_disconnected)
            )
            return
        }
        _latestMenuItemClickId.value = action
        menuExecutor?.let { executor ->
            // STEP1 禁用action对应的菜单项
            executingActions.add(action)
            _menuActionExecuting.updateValueIfChanged(executingActions.isNotEmpty())
            photoMenuDataEmitter.tryEmit(photoMenuDataEmitter.value.copy(executingActionSum = executingActions.sum()))

            // STEP2 执行菜单项操作
            executor.doAction(action, isLongClicked, extras) { onDoneAction ->
                // STEP3 恢复action对应菜单项的可用性
                executingActions.remove(onDoneAction)
                _menuActionExecuting.updateValueIfChanged(executingActions.isNotEmpty())
                photoMenuDataEmitter.tryEmit(photoMenuDataEmitter.value.copy(executingActionSum = executingActions.sum()))
            }
        }
    }

    /**
     * 清除指定的texRes的Tips
     */
    fun clearTextTips(textRes: Int) {
        GLog.i(TAG, LogFlag.DL, "[clearTextTips] action: $textRes")
        _clearTips.setOrPostValue(textRes)
    }

    /**
     * 在菜单执行一些离开菜单的逻辑并收到结果后，通过此方法分发回到原来的菜单处理结果。
     */
    fun notifyMenuHandleResult(requestCode: Int, resultCode: Int, intent: Intent?) {
        GLog.i(TAG, LogFlag.DL, "[notifyMenuHandleResult] action: $requestCode")
        menuExecutor?.handleResult(requestCode, resultCode, intent)
    }

    /**
     * 请求弹出气泡弹窗
     * @param action 气泡弹窗的行为：点击气泡弹窗后会将输入的action回传到指定的回调
     */
    fun toastBubble(action: BubbleAction) {
        _bubbleWindow.postValue(
            BubbleWindowState(
                eventId = System.currentTimeMillis(), // 拓展：后期有需要可优化为UUID
                eventTime = System.currentTimeMillis(),
                eventAction = action
            )
        )
    }

    /**
     * 更新大图底部菜单高度
     * @param height 高度
     */
    fun updateBottomMenuHeight(height: Int) {
        _bottomMenuHeight.setOrPostValue(height)
    }

    /**
     * 更新大图顶部菜单栏高度
     * @param height 高度
     */
    fun updateTopMenuHeight(height: Int) {
        _topMenuHeight.setOrPostValue(height)
    }

    /**
     * 根据菜单actionId来查询配置图标menuIcon
     * @param actionId 菜单actionId
     */
    fun queryMenuIconWithActionId(actionId: Int): MenuIcon? {
        return menuInformation.queryMenuIconWithActionId(actionId)
    }



    /////////////////// 以下为内部私有方法 //////////////////////

    override fun onCreate() {
        super.onCreate()
        subscribeSelfFlow()
        subscribeStatesFromViewModel()
        subscribeStatesFromConfigAbility()
        if (executingActions.isNotEmpty()) {
            executingActions.forEach { action ->
                GLog.w(TAG, LogFlag.DL, "execute rule action = $action")
            }
            executingActions.clear()
        }
        menuExecutor?.onCreate()
        registerModuleCallbacks()
    }

    override fun onStart() {
        super.onStart()
        menuExecutor?.onStart()
        GroupPhotoModelUpdater.listener = createGroupPhotoModelUpdateListener()
        unregisterModuleCallbacks()
        registerModuleCallbacks()
    }

    override fun onResume() {
        super.onResume()
        launch {
            val isSystemPlayerAvailableCurrent = OpenInSystemPlayerHelper.isSystemPlayerAvailable()
            if (isSystemPlayerAvailableCurrent != isSystemPlayerAvailable) {
                isSystemPlayerAvailable = isSystemPlayerAvailableCurrent
                photoMenuDataEmitter.tryEmit(photoMenuDataEmitter.value.copy(isSystemPlayerAvailable = isSystemPlayerAvailableCurrent))
            }
        }
        /**
         * 尝试再次注册一遍跨模块调用。内部会有去重，无需担心重复注册。
         *
         * 问题场景：在虚拟图集进编辑，返回大图后会拉起新的大图页，新大图页退出后，旧大图页回到resume状态。
         * 这导致原本应该存在的回调被另一个大图页注销，且没有新的回调注册进来，其他模块会调用失败。
         * 因此，在resume阶段再尝试注册一次，确保这个回调在resume时是存在的。
         */
        registerModuleCallbacks()
        // 通知隐藏水印角标
        notifyHideWatermarkCorner()
    }

    override fun onStop() {
        menuExecutor?.onStop()
        unregisterModuleCallbacks()
        super.onStop()
    }

    override fun onDestroy() {
        menuExecutor?.onDestroy()
        menuExecutor = null
        GroupPhotoModelUpdater.listener = null
        unsubscribeStatesFromConfigAbility()
        unsubscribeStatesFromViewModel()
        //关闭 ability
        closeAbilities()
        super.onDestroy()
    }

    /**
     * 关闭Ability
     */
    private fun closeAbilities() {
        _settingsAbility?.close()
        _moduleAccessAbility?.close()
        _groupPhotoAbility?.close()
        _settingsAbility = null
        _moduleAccessAbility = null
        _groupPhotoAbility = null
    }

    /**
     * 订阅此类内部自身的 Flow
     */
    private fun subscribeSelfFlow() {
        launch {
            var previousMenuDependentData: MenuDependentData? = null
            // 订阅 photoMenuData 并调用菜单更新
            photoMenuData
                .collect {
                    GLog.d(TAG, LogFlag.DL) { "[subscribeSelfFlow] updateMenuFlow collect: $it" }
                    updateMenuData(it, previousMenuDependentData)
                    previousMenuDependentData = it
                }
        }

        launch {
            updateTitleFlow.collect {
                GLog.d(TAG, LogFlag.DL) { "[subscribeSelfFlow] updateTitleFlow collect: $it" }
                updatePhotoTitle(it)
            }
        }
    }

    @Suppress("LongMethod")
    private fun subscribeStatesFromViewModel() {
        launch(Dispatchers.Main.immediate) {
            pageViewModel.dataLoading.diffedFocusViewDataDistinctUi.collectNotNull {
                GTrace.trace({ "$TAG.focusSlotViewData" }) {
                    val photoItemViewData = pageViewModel.dataLoading.diffedFocusViewData.value?.newItem ?: return@trace
                    GLog.d(TAG, LogFlag.DL) { "[subscribeStatesFromViewModel] focus viewData: $photoItemViewData" }
                    photoMenuDataEmitter.tryEmit(photoMenuDataEmitter.value.copy(photoItemViewData = photoItemViewData))
                }
            }
        }
        pageViewModel.inputArguments.dataSource.observeForever { dataSource ->
            GLog.d(TAG, LogFlag.DL) {
                """
                [subscribeStatesFromViewModel] except focus: ${dataSource.focus}, is data set: ${dataSource.isDataSet},
                                               model type: ${dataSource.modelType}, item path: ${dataSource.itemPath}, set path: ${dataSource.setPath},
                                               should reverse data order: ${dataSource.shouldReverseDataOrder}, data set: ${dataSource.whichDataSet}
            """.trimIndent()
            }
        }
        pageViewModel.inputArguments.menu.observeForever { menu ->
            GLog.d(TAG, LogFlag.DL) { "[subscribeStatesFromViewModel] menuPresent=${menu.present}" }
            // 菜单预设
            menuPresent = menu.present

            // 通知菜单刷新
            photoMenuDataEmitter.tryEmit(photoMenuDataEmitter.value.copy(menuPresent = menuPresent))
        }

        pageViewModel.cloudSync.isDownloadable.observeForever {
            GLog.d(TAG, LogFlag.DL) { "[subscribeStatesFromViewModel] allowDownloadOriginal=$it" }
            photoMenuDataEmitter.tryEmit(photoMenuDataEmitter.value.copy(isDownloadable = it))
        }

        pageViewModel.preview.selectedCount.observeForever {
            GLog.d(TAG, LogFlag.DL) { "[subscribeStatesFromViewModel] selectionCount=$it" }
            updateTitleFlow.tryEmit(
                updateTitleFlow.value.copy(selectedCount = it)
            )
        }

        launch(Dispatchers.CPU) {
            // 用 flow 异步收集,避免 LiveData 需要等待主线程调度
            pageViewModel.details.currentPhotoInfo.collect {
                GLog.d(TAG, LogFlag.DL) { "[subscribeStatesFromViewModel] currentPhotoInfo=$it" }
                it ?: return@collect
                val date = it.details.getString(PhotoDetailsConstants.Key.DATE)
                val time = it.details.getString(PhotoDetailsConstants.Key.TIME)
                val formatDataTime = it.details.getString(PhotoDetailsConstants.Key.FORMAT_DATE_TIME)
                val location = it.details.getString(PhotoDetailsConstants.Key.LOCATION)

                updateTitleFlow.tryEmit(
                    updateTitleFlow.value.copy(
                        photoKey = it.normalizedPath,
                        detailsDate = date,
                        detailsTime = time,
                        detailsFormatDateTime = formatDataTime,
                        detailsLocation = location,
                    )
                )
            }
        }

        pageViewModel.inputArguments.title.observeForever {
            GLog.d(TAG, LogFlag.DL) { "[subscribeStatesFromViewModel] title=$it" }
            updateTitleFlow.tryEmit(updateTitleFlow.value.copy(navigationIcon = it.navigationIcon, titleContent = it.titleContent))
        }

        pageViewModel.outputChannels.castingManager.isCasting.observeForever { isCasting ->
            GLog.d(TAG, LogFlag.DL) { "[subscribeStatesFromViewModel] isCasting=$isCasting" }
            // 投屏状态时截帧菜单不可点击
            photoMenuDataEmitter.tryEmit(photoMenuDataEmitter.value.copy(isCasting = isCasting))
        }

        pageViewModel.outputChannels.lensManager.isLensEnabled.observeForever {
            if (pageViewModel.outputChannels.lensManager.isLensFuncAvailable.not()) return@observeForever
            GLog.d(TAG, LogFlag.DL) { "[subscribeStatesFromViewModel] isLensEnabled=$isLensEnabled" }
            // 若当前图片支持lens操作则通知菜单栏更新，使得菜单栏显示镜头图标
            photoMenuDataEmitter.tryEmit(photoMenuDataEmitter.value.copy(isLensEnabled = it))
        }

        pageViewModel.outputChannels.castingManager.isCastingEnable.observeForever {
            GLog.d(TAG, LogFlag.DL) { "[subscribeStatesFromViewModel] isCastingEnable=$it" }
            // 投屏能力变更时，需要去更新菜单项，控制“投屏播放”是否要显示
            photoMenuDataEmitter.tryEmit(photoMenuDataEmitter.value.copy(isCastingEnable = it))
        }

        pageViewModel.olive.oliveEnableStatus.observeForever {
            //oliveEnable状态变更时，需要去更新olive图标
            photoMenuDataEmitter.tryEmit(photoMenuDataEmitter.value.copy(isOliveEnable = it))
        }

        pageViewModel.pageManagement.isSupportSelfSplitting.observeForever(isSupportSelfSplittingObserver)

        pageViewModel.pageManagement.pageOperationState.observeForever {
            GLog.d(TAG, LogFlag.DL) { "[subscribeStatesFromViewModel] pageOperationState changed: $it" }
            photoMenuDataEmitter.tryEmit(photoMenuDataEmitter.value.copy(pageOperationState = it))
        }

        pageViewModel.playback.playbackInfo.observeForever { playerInfo ->
            pageViewModel.dataLoading.focusItemViewData?.takeIf {
                val shouldUpdateMenu = (playerState != playerInfo?.playerState) && it.isOnlinePlayableVideo
                playerState = playerInfo?.playerState
                shouldUpdateMenu
            }?.let {
                GLog.d(TAG, LogFlag.DL) { "[subscribeStatesFromViewModel] updateMenu $playerInfo" }
                // 谷歌云视频，只有播放器已准备好时,才能启用截帧 playbackInfo.
                photoMenuDataEmitter.tryEmit(
                    photoMenuDataEmitter.value.copy(
                        playerState = playerState,
                    )
                )
            }
        }

        pageViewModel.photoFunc.funcDetectInfo.observeForever { functionInfo ->
            //加载完毕智能推荐，更新menu对应状态
            if ((functionInfo.status != Status.END) || functionInfo == currentFuncDetectInfo) {
                GLog.d(TAG, LogFlag.DL) {
                    "[subscribeStatesFromViewModel] funcDetectInfo not Changed or status not End  "
                }
                return@observeForever
            }
            currentFuncDetectInfo = functionInfo
            photoMenuDataEmitter.tryEmit(photoMenuDataEmitter.value.anonymousUpdate())
        }

        pageViewModel.details.isInDetailsMode.observeForever { isInDetailsMode ->
            //详情页展示状态改变
            GLog.d(TAG, LogFlag.DL) { "[subscribeStatesFromViewModel] isInDetailsMode changed to $isInDetailsMode " }
            photoMenuDataEmitter.tryEmit(photoMenuDataEmitter.value.anonymousUpdate())
        }
    }

    private val configListener = IConfigListener {
        GLog.d(TAG, LogFlag.DL) { "[subscribeStatesFromViewModel] configListener, config=$it" }
        photoMenuDataEmitter.tryEmit(photoMenuDataEmitter.value.anonymousUpdate())
    }

    /**
     * 相册自分屏Observer,自分屏系统接口需要传入Activity,所以系统那边会持有相册Activity
     * 因此,自分屏相关的Observer需要手动移除监听,否则会内存泄露。
     */
    private val isSupportSelfSplittingObserver: Observer<SplitOperationUsability?> by lazy {
        Observer<SplitOperationUsability?> { isSupportSelfSplitting ->
            GLog.d(TAG, LogFlag.DL) { "[subscribeStatesFromViewModel] isSupportSelfSplitting changed: $isSupportSelfSplitting" }
            photoMenuDataEmitter.tryEmit(
                photoMenuDataEmitter.value.copy(isAllowSplitAsNewInstance = isSupportSelfSplitting?.allowSplitAsNewInstance)
            )
        }
    }

    private fun subscribeStatesFromConfigAbility() {
        ConfigAbilityWrapper.apply {
            registerListener(AI_ELIMINATE_HAS_USED, configListener)
            registerListener(FIRST_USING_RESTRICT_WATERMARK_TAG, configListener)
        }
    }

    private fun unsubscribeStatesFromConfigAbility() {
        ConfigAbilityWrapper.apply {
            unregisterListener(AI_ELIMINATE_HAS_USED, configListener)
            unregisterListener(FIRST_USING_RESTRICT_WATERMARK_TAG, configListener)
        }
    }

    private fun unsubscribeStatesFromViewModel() {
        /**
         * 如果LiveData.observe时传了Activity作为LifeCycleOwner，这样便会导致Fragment销毁后,
         * Activity仍然持有此Fragment关联的ViewModel中的LiveData引用.导致内存泄露。
         * 因此在界面销毁后，需要手动removeObserver
         */
        pageViewModel.pageManagement.isSupportSelfSplitting.removeObserver(isSupportSelfSplittingObserver)
    }

    /**
     * 注册回调到能力层，给其他模块使用。
     * 添加目的：开放能力模块需要跨模块调用大图逻辑来进入编辑。
     */
    private fun registerModuleCallbacks() {
        moduleAccessAbility?.let { ability ->
            ability.registerCallback(PhotoPageModuleTopics.PHOTO_STATE_AND_CALLBACK_FUNCTION.name) {
                when (val operation = it?.getString(OpenAbilityConstant.OPERATION_NAME)) {
                    PhotoPageModuleOperations.GO_TO_AIGC.name -> goToAIGC(it)

                    PhotoPageModuleOperations.GET_CURRENT_SHOW_PHOTOURI.name -> getCurrentShowPhotoUri()

                    PhotoPageModuleOperations.GET_PHOTO_SHOW_STATE.name -> getPhotoShowState()

                    else -> {
                        GLog.w(TAG, LogFlag.DL, "[registerModuleCallbacks] no matched operation for [$operation], null return be rtn.")
                        null
                    }
                }
            }
            GLog.d(TAG, LogFlag.DL) { "[registerModuleCallbacks] callback <PHOTO_STATE_AND_CALLBACK_FUNCTION> has registered." }
        }
    }

    private fun getCurrentShowPhotoUri(): Bundle? {
        val diffed = pageViewModel.dataLoading.diffedFocusViewData.value
        val resultBundle = diffed?.newItem?.let { newItem ->
            newItem.contentUri?.let { currentUri ->
                return Bundle().apply {
                    GLog.d(TAG, LogFlag.DL, "registerModuleCallbacks uri:  $currentUri")
                    putString(
                        OpenAbilityConstant.PHOTO_URI, currentUri.toString()
                    )
                }
            }
        }
        return resultBundle
    }

    private fun getPhotoShowState(): Bundle {
        return Bundle().apply {
            putInt(OpenAbilityConstant.SHOW_STATE, OpenAbilityConstant.IS_SHOWING)
        }
    }

    private fun goToAIGC(it: Bundle): Bundle? {
        // 检查大图页是否在顶层且已经稳定 有限等待+轮询，防止binder线程进来，大图页相关数据或动态加载View没加完成
        val stateLong = System.currentTimeMillis()
        if (Looper.myLooper() != Looper.getMainLooper()) {
            while (System.currentTimeMillis() - stateLong < WAITING_TIME) {
                Thread.sleep(POLLING_DURATION)
                if (pageViewModel.pageManagement.isPhotoPageStable()) {
                    val method = it.getString(OpenAbilityConstant.METHOD_NAME)
                    return PhotoPageOpenAbilityCaller.matchMethod(method, pageViewModel)
                }
            }
        }
        return Bundle().apply {
            putInt(
                OpenAbilityConstant.KEY_BREENO_INVOKED_RESULT,
                OpenAbilityConstant.COMMON_PHOTO_PAGE_NOT_AVAILABLE
            )
        }
    }

    /**
     * 注销跨模块回调
     */
    private fun unregisterModuleCallbacks() {
        moduleAccessAbility?.unregisterCallback(PhotoPageModuleTopics.PHOTO_STATE_AND_CALLBACK_FUNCTION.name)
    }

    ///////////////////// 以下为内部定义 ////////////////////////

    /**
     * 更新大图标题。规则如下：
     * 1. 如果外部指的的有Title信息，使用指定的
     * 2. 如果没有指定，则大图自行获取，获取规则如下：
     * ```
     *   ________________________________
     *  | 有无位置信息 | 标题  |   副标题：  |
     *  |============|======|============|
     *  | 有位置信息   | 位置  | 日期 + 时间 |
     *  | 无位置信息   | 时间  |    日期    |
     *  |-------------------------------|
     * ```
     */
    private fun updatePhotoTitle(titleDependentData: TitleDependentData) {
        if (titleDependentData.navigationIcon == MenuIcon.NULL) return
        /**
         * 如果当前[activity]不在默认[Display.DEFAULT_DISPLAY]上显示，只显示返回箭头
         */
        if (isShowedOnDefaultDisplay.not()) {
            PhotoMenuTitle(
                navigationIcon = titleDependentData.navigationIcon,
                mediaKey = EMPTY_STRING,
                title = EMPTY_STRING,
                subTitle = EMPTY_STRING
            ).let { _photoTitle.tryEmit(it) }
            return
        }

        val key = titleDependentData.photoKey
        if (key == EMPTY_STRING) {
            PhotoMenuTitle(
                navigationIcon = titleDependentData.navigationIcon,
                mediaKey = EMPTY_STRING,
                title = EMPTY_STRING,
                subTitle = EMPTY_STRING
            ).let { _photoTitle.tryEmit(it) }
            return
        }
        if (isPreviewType(pageViewModel.inputArguments.features.value?.photoType)) {
            PhotoMenuTitle(
                navigationIcon = titleDependentData.navigationIcon,
                mediaKey = key,
                title = pageViewModel.preview.getPreviewTitle(),
                subTitle = EMPTY_STRING
            ).let { _photoTitle.tryEmit(it) }
            return
        }
        titleDependentData.titleContent?.let { titleContent ->
            // 使用指定的Title信息
            PhotoMenuTitle(
                navigationIcon = titleDependentData.navigationIcon,
                mediaKey = key,
                title = titleContent,
                subTitle = EMPTY_STRING
            ).let { _photoTitle.tryEmit(it) }
        } ?: let {
            // 不使用指定的Title信息，则主动获取
            val date = titleDependentData.detailsDate ?: EMPTY_STRING
            val time = titleDependentData.detailsTime ?: EMPTY_STRING
            val formatDataTime = titleDependentData.detailsFormatDateTime ?: EMPTY_STRING
            val location = titleDependentData.detailsLocation ?: EMPTY_STRING

            // 是否存在位置信息
            val isExistLocation = location.isNotEmpty()
            PhotoMenuTitle(
                navigationIcon = titleDependentData.navigationIcon,
                mediaKey = key,
                title = if (isExistLocation) location else date,
                subTitle = if (isExistLocation) formatDataTime else time
            ).let { _photoTitle.tryEmit(it) }
        }
    }

    /**
     * 获取指定 [slot] 对应的菜单状态
     */
    private fun updateMenuData(newState: MenuDependentData, oldState: MenuDependentData?) {
        /*
        1. 获取[slot]对应的[MediaItem]
        2. 根据以下线索计算当前的菜单预设：
        - [pageViewModel.inputArguments.dataSource.isDataSet]
        - [pageViewModel.inputArguments.invokeFrom]
        - [pageViewModel.focusMediaItem]
        3. 输出菜单项预设、额外启用的菜单项、额外关闭的菜单项
        */

        // 当 isDownloadable 变更时，当前 isDownloadable 不为 true 则不更新。
        if ((oldState != null) && (newState.isDownloadable != oldState.isDownloadable) && (newState.isDownloadable != true)) {
            GLog.d(TAG, LogFlag.DL) { "[updateMenuData] isDownloadable is changed and is not true" }
            return
        }

        val slot = newState.focusSlot
        val isInvalidSlot = (slot == INVALID_INDEX)

        /**
         * Marked by huangmaowei 在PhotoItemViewData的needUpdateUI修改经纬度判断后，newState.focusSlot就不会更新，获取的是旧slot
         * 所以通过position的方式获取是不可靠的，场景：在视频中截取帧（前一个是图片数据），最后获取的是图片slotViewData，导致截取帧的icon不显示
         * 遗留问题：newState.photoItemViewData能否直接使用，数据集合变化，旧数据也可以在这个方法使用？
         */
        val slotViewData = newState.photoItemViewData?.id?.let { id ->
            pageViewModel.dataLoading.photoViewDataSet?.get(id)
        }

        when {
            isInvalidSlot -> return

            (slotViewData?.id == null) -> return

            else -> {
                menuDataUpdatingJob?.cancel()
                menuDataUpdatingJob = launch(Dispatchers.Default) {
                    GTrace.traceBegin("PhotoMenuControlViewModel.updateMenuData")
                    val menuState = (DataManager.getMediaObject(Path.fromString(slotViewData.id)) as? MediaItem)?.let { mediaItem ->
                        if (mediaItem is UriImage) {
                            MenuState()
                        } else {
                            if (isActive && (mediaItem is LocalMediaItem) && mediaItem.isLoaded.not()) {
                                /**
                                 * 如果是LocalMediaItem, 且未加载的话，尝试从数据库加载。
                                 * 是一个兜底行为，以保证菜单能够正常显示，方法内部会再次去重，以保证不会重复加载。
                                 */
                                LocalMediaDataHelper.getLocalMediaItem(mediaItem.path)
                            }
                            MenuState(
                                isMenuItemDataValid = true,
                                isBottomMenuVisible = false,
                                topMenuResId = menuPresent.topMenuResId,
                                bottomMenuResId = menuPresent.bottomMenuResId,
                                menuItemStates = if (isShowedOnDefaultDisplay) {
                                    figureMenuItemInfo(mediaItem)
                                } else {
                                    figureConciseMenuItemInfo(mediaItem)
                                }
                            )
                        }
                    } ?: MenuState(
                        topMenuResId = menuPresent.topMenuResId,
                        bottomMenuResId = menuPresent.bottomMenuResId,
                        isMenuItemDataValid = false
                    )

                    if (isActive) {
                        if (_photoMenu.value != menuState) {
                            GLog.i(TAG, LogFlag.DL) {
                                "[notifyMenuItemClicked] post photoMenu: $menuState"
                            }
                        }
                        _photoMenu.emit(menuState)
                    }
                    GTrace.traceEnd()
                }
            }
        }
    }

    /**
     * 返回相册简版大图的菜单项集合
     */
    private fun figureConciseMenuItemInfo(mediaItem: MediaItem): Map<Int, MenuItemState> {
        return mutableMapOf<Int, MenuItemState>().also { resultSet ->
            queryConciseMenuItemByMediaItem(mediaItem, resultSet)
            removeMenuIfSuppressed(resultSet)
        }
    }

    private fun figureMenuItemInfo(mediaItem: MediaItem): Map<Int, MenuItemState> {
        return mutableMapOf<Int, MenuItemState>().also { resultSet ->
            queryMenuItemsSupportedByMediaItem(mediaItem, resultSet)
            removeMenuIfSuppressed(resultSet)
            queryMenuItemSupportedByGallery(resultSet)
        }
    }

    /**
     * 抑制图标显示期间，需要从resultSet删除对应的action
     */
    private fun removeMenuIfSuppressed(resultSet: MutableMap<Int, MenuItemState>) {
        if (suppressMenuIconShow.not()) {
            return
        }

        suppressMenuIconArray.forEach { key ->
            resultSet.remove(key)
        }
    }

    /**
     * 获取相册简版大图的菜单项。
     * 比如:外屏连拍
     */
    private fun queryConciseMenuItemByMediaItem(mediaItem: MediaItem, resultSet: MutableMap<Int, MenuItemState>) {

        fun createMenuItemStateFor(action: Int): MenuItemState = MenuItemState(
            id = action,
            isVisible = true,
            isEnabled = true,
            isCheckable = queryCheckableStateFor(action),
            isChecked = queryCheckedStateFor(action),
            isGrayDisplay = queryCheckedStateFor(action),
            tips = PhotoMenuItemTips.ItemTipsNone,
            extraTouchArea = Rect(), // 尚未支持，使用默认值EmptyRect,
            menuIcon = queryMenuIconFor(action)
        )

        // case 1: 连拍
        val supportCShot = fun() {
            shouldSupportCShot(mediaItem) {
                createMenuItemStateFor(
                    R.id.action_cshot
                ).let { menuItemState ->
                    resultSet[menuItemState.id] = menuItemState
                }
            }
        }

        // case 2: 删除
        val supportRecycle = fun() {
            if (mediaItem.isTemporaryCacheItem().not()) {
                createMenuItemStateFor(
                    R.id.action_recycle
                ).let { menuItemState ->
                    resultSet[menuItemState.id] = menuItemState
                }
            }
        }

        supportRecycle()

        /**
         * 如果当前版本支持外屏连拍接续功能，外屏则显示连拍菜单
         */
        if (isSupportCshotContinuation) {
            supportCShot()
        }
    }

    /**
     * 获取由相册本身场景支持的菜单项。
     * 比如是否能跳转相册主页、处于投屏模式时顶部菜单显示投屏按钮等。
     */
    private fun queryMenuItemSupportedByGallery(resultSet: MutableMap<Int, MenuItemState>) {

        fun createMenuItemStateFor(action: Int): MenuItemState = MenuItemState(
            id = action,
            isVisible = querySupportedStateFor(action),
            isEnabled = queryEnabledStateFor(action),
            isCheckable = queryCheckableStateFor(action),
            isChecked = queryCheckedStateFor(action),
            isGrayDisplay = queryCheckedStateFor(action),
            tips = PhotoMenuItemTips.ItemTipsNone,
            extraTouchArea = Rect(), // 尚未支持，使用默认值EmptyRect
            menuIcon = queryMenuIconFor(action),
        )

        // case 1: 支持从大图跳转到相册主页（时间轴）
        val supportLaunchGallery = fun() {
            /*
            当前预设菜单为[PhotoMenuPresent.VIEW_GALLERY]时
            应当支持跳转到相册主页
            */
            if ((menuPresent == PhotoMenuPresent.ViewGallery) || (menuPresent == PhotoMenuPresent.ViewWidgetContent)) {
                createMenuItemStateFor(
                    R.id.action_gallery
                ).let { menuItemState ->
                    resultSet[menuItemState.id] = menuItemState
                }
            }
        }

        // case 2: 菜单应显示投屏按钮
        val supportProjection = fun() {
            val isCastingNow = pageViewModel.outputChannels.castingManager.isCasting.value ?: false
            if (isCastingNow) {
                createMenuItemStateFor(
                    R.id.action_screencast
                ).let { menuItemState ->
                    resultSet[menuItemState.id] = menuItemState
                }
            }
        }

        supportLaunchGallery()
        supportProjection()

        // 后续依赖于相册场景的菜单判断，放在这里
    }

    /**
     * 获取由MediaItem支持的菜单项
     */
    private fun queryMenuItemsSupportedByMediaItem(mediaItem: MediaItem, resultSet: MutableMap<Int, MenuItemState>) {
        /*
        1. 菜单项 = (MediaItem支持的菜单项 + inputArguments.menu支持的菜单项 + 交互中请求可见的菜单项)
        - (MediaItem不支持的菜单项 + inputArguments.menu不支持的菜单项 + 交互中请求不可见的菜单项)
        2. 菜单项 = 菜单项 * 可选状态过滤
        */
        val presentSupportedActions = pageViewModel.inputArguments.menu.value?.enabledActions.toFlags()
        val presentUnsupportedActions = pageViewModel.inputArguments.menu.value?.disabledActions.toFlags()

        val supportedActions = mediaItem.calcSupportedActions() + presentSupportedActions
        val unsupportedActions = mediaItem.calcUnsupportedActions() + presentUnsupportedActions

        return (supportedActions - unsupportedActions).let { enableFlags ->
            // 备份到[PhotoMenuControlViewModel.supportedActions]
            supportedActions.removeAllFlags()
            supportedActions.addFlags(enableFlags.flags)
            // MediaItem支持的菜单项
            enableFlags.forEach { flag ->
                menuInformation.queryMenuItemInfoWithSupportFlags(flag) { supportOperation, menuItemInfo ->
                    MenuItemState(
                        id = menuItemInfo.action,
                        isVisible = querySupportedStateFor(menuItemInfo.action),
                        isEnabled = queryEnabledStateFor(menuItemInfo.action, mediaItem),
                        isGrayDisplay = queryGrayDisplayStateFor(menuItemInfo.action),
                        isCheckable = menuItemInfo.isCheckable,
                        isChecked = menuItemInfo.isCheckable && queryCheckedStateFor(mediaItem, supportOperation),
                        tips = queryTipsStateFor(mediaItem, menuItemInfo, supportOperation),
                        extraTouchArea = Rect(), // 尚未支持，使用默认值EmptyRect
                        menuIcon = queryMenuIconFor(menuItemInfo.action, supportOperation, mediaItem, menuItemInfo)
                    ).let { menuItemState ->
                        resultSet[menuItemInfo.action] = menuItemState
                    }
                }
            }
        }
    }

    /**
     * 菜单项在当前场景下是否支持。
     */
    private fun querySupportedStateFor(
        action: Int
    ): Boolean {
        return menuPresent.menuItemLimits.contains(action)
    }

    /**
     * 菜单项在当前场景下是否启用。
     * -从相机相册进入 分享和编辑大图未加载完成时 不可点击 待大图加载完成后进行可点击操作
     * -视频投屏时，需要禁用截帧，编辑，更多菜单项
     */
    private fun queryEnabledStateFor(
        action: Int,
        mediaItem: MediaItem? = null
    ): Boolean = isDisableMenuAll.not() && executingActions.contains(action).not() && when (action) {
        R.id.action_edit,
        R.id.action_more -> isCastingVideo.not()

        R.id.action_getvideoframe -> {
            // 谷歌云视频时，需要播放器渲染第一帧后才能截帧
            isCastingVideo.not() &&
                    isCaptureVideoFrameSuppressed.not() &&
                    (mediaItem?.isOnlinePlayableVideo() != true || pageViewModel.playback.isPlayerPrepared())
        }

        R.id.action_group_photo -> GroupPhotoModelUpdater.isUpdating.not()

        R.id.action_olive_photo -> pageViewModel.details.isInDetailsMode.value != true

        else -> true
    }

    /**
     * 菜单项是否置灰显示,只是图标置灰显示，但会响应用户点击
     */
    private fun queryGrayDisplayStateFor(action: Int): Boolean = when (action) {
        R.id.action_olive_photo -> (pageViewModel.olive.oliveEnableStatus.value as Boolean).not()

        else -> false
    }

    /**
     * 当前MediaItem是否有智能推荐
     * 并判断传入 mediaItem对应的智能推荐列表是否为空
     * @param mediaPah 图像信息的path
     */
    private fun isHasRecommended(mediaPah: String): Boolean {
        val funcDetectInfo = currentFuncDetectInfo
        if (funcDetectInfo == null) {
            GLog.d(TAG, LogFlag.DL, "[isHasRecommended], funcDetectInfo isNull")
            return false
        }

        if (funcDetectInfo.funcItems.isEmpty()) {
            GLog.d(TAG, LogFlag.DL, "[isHasRecommended], funcItems isEmpty")
            return false
        }

        if (mediaPah != funcDetectInfo.mediaPath) {
            GLog.w(TAG, LogFlag.DL) { "[isHasRecommended], path not same " }
            return false
        }

        return filterNonNeedShowInDetailFuncItems(funcDetectInfo.funcItems).isNotEmpty()
    }

    private fun createGroupPhotoModelUpdateListener(): ModelUpdateListener {
        return object : ModelUpdateListener {
            // 安装成功需要刷新菜单
            override fun onInstallSuccess() {
                GLog.d(TAG, LogFlag.DL) { "<createGroupPhotoModelUpdateListener> onInstallSuccess notifyUpdateMenu!!!" }
                photoMenuDataEmitter.tryEmit(photoMenuDataEmitter.value.anonymousUpdate())
            }

            // 安装失败需要刷新菜单
            override fun onInstallFail(code: Int) {
                GLog.w(TAG, LogFlag.DL) { "<createGroupPhotoModelUpdateListener> onInstallFail notifyUpdateMenu!!!" }
                photoMenuDataEmitter.tryEmit(photoMenuDataEmitter.value.anonymousUpdate())
            }

            override fun onDownloading(bytesRead: Long, contentLength: Long) = Unit

            override fun onDownloadSuccess(version: Int) = Unit

            override fun onDownloadFail(code: Int) {
                GLog.w(TAG, LogFlag.DL) { "<createGroupPhotoModelUpdateListener> onDownloadFail notifyUpdateMenu!!!" }
                photoMenuDataEmitter.tryEmit(photoMenuDataEmitter.value.anonymousUpdate())
            }

            override fun onDownloadCancel() {
                GLog.w(TAG, LogFlag.DL) { "<createGroupPhotoModelUpdateListener> onDownloadCancel notifyUpdateMenu!!!" }
                photoMenuDataEmitter.tryEmit(photoMenuDataEmitter.value.anonymousUpdate())
            }
        }
    }

    @Suppress("FunctionOnlyReturningConstant", "UnusedPrivateMember")
    private fun queryCheckableStateFor(
        action: Int
    ): Boolean = false

    @Suppress("FunctionOnlyReturningConstant", "UnusedPrivateMember")
    // Marked by Johnny 2022/6/17 for 舒茧
    private fun queryCheckedStateFor(
        action: Int
    ): Boolean = false

    private fun queryCheckedStateFor(
        mediaItem: MediaItem,
        supportOperation: Long
    ): Boolean = when (supportOperation) {
        // case 1: 对action_favorite做处理
        OPERATION_SUPPORT_FAVORITES -> photoFavoriteStateCache.isFavorite(mediaItem)
        // case 2: 对action_preview做处理
        OPERATION_SUPPORT_PREVIEW_CHECK -> pageViewModel.preview.isItemChecked(mediaItem)
        /*
        case [other]: xxxxxxx
        有其他业务需要，在此后继续添加
        */
        else -> false
    }

    private fun queryCheckedIconResFor(
        mediaItem: MediaItem,
        supportOperation: Long,
        actionId: Int
    ): MenuIcon {
        // case 1: 对action_favorite做处理
        if (supportOperation == OPERATION_SUPPORT_FAVORITES) {
            return if (photoFavoriteStateCache.isFavorite(mediaItem)) {
                PhotoMenuIconDrawableManager.getMenuIconForState(actionId, PhotoMenuIconDrawableManager.STATE_SELECT)
            } else {
                PhotoMenuIconDrawableManager.getMenuIconForState(actionId, PhotoMenuIconDrawableManager.STATE_NORMAL)
            }
        }
        if (supportOperation == OPERATION_SUPPORT_PREVIEW_CHECK) {
            return if (pageViewModel.preview.isItemChecked(mediaItem)) {
                PhotoMenuIconDrawableManager.getMenuIconForState(actionId, PhotoMenuIconDrawableManager.STATE_SELECT)
            } else {
                PhotoMenuIconDrawableManager.getMenuIconForState(actionId, PhotoMenuIconDrawableManager.STATE_NORMAL)
            }
        }
        if (supportOperation == OPERATION_SUPPORT_INFO) {
            return if (pageViewModel.details.isInDetailsMode.value == true) {
                PhotoMenuIconDrawableManager.getMenuIconForState(actionId, PhotoMenuIconDrawableManager.STATE_SELECT)
            } else {
                PhotoMenuIconDrawableManager.getMenuIconForState(actionId, PhotoMenuIconDrawableManager.STATE_NORMAL)
            }
        }

        /*
        case [other]: xxxxxxx
        有其他业务需要，在此后继续添加
        */
        return MenuIcon.NULL
    }

    /**
     * 1. 从 menuItemInfo 中获取 menuIcon
     * 2. 或没有在 PhotoMenuDefinition 中预定义 menuItem 的 action，会在此创建 MenuIcon。
     */
    private fun queryMenuIconFor(
        actionId: Int,
        supportOperation: Long? = null,
        mediaItem: MediaItem? = null,
        menuItemInfo: MenuItemInfo? = null,
    ): MenuIcon {
        // case 1: action_gallery 没在 PhotoMenuDefinition 中定义，在 xml 中单独存在，所以额外提供 MenuIcon
        if (actionId == R.id.action_gallery) {
            return MenuIcon.Static(
                darkIcon = R.drawable.photopage_ic_menu_gallery_selector,
                lightIcon = R.drawable.photopage_ic_menu_gallery_selector_light,
            )
        }

        if (actionId == R.id.action_screencast) {
            return MenuIcon.Static(
                darkIcon = R.drawable.photopage_ic_menu_screencast_selector,
                lightIcon = R.drawable.photopage_ic_menu_screencast_selector_light,
            )
        }

        if (mediaItem == null) return menuItemInfo?.menuIcon ?: MenuIcon.NULL

        // case 2: 对 action_favorite 做处理
        if (supportOperation == OPERATION_SUPPORT_FAVORITES) {
            return if (photoFavoriteStateCache.isFavorite(mediaItem)) {
                PhotoMenuIconDrawableManager.getMenuIconForState(actionId, PhotoMenuIconDrawableManager.STATE_SELECT)
            } else {
                PhotoMenuIconDrawableManager.getMenuIconForState(actionId, PhotoMenuIconDrawableManager.STATE_NORMAL)
            }
        }

        // case 3: 对 action_preview_check 做处理
        if (supportOperation == OPERATION_SUPPORT_PREVIEW_CHECK) {
            return if (pageViewModel.preview.isItemChecked(mediaItem)) {
                PhotoMenuIconDrawableManager.getMenuIconForState(actionId, PhotoMenuIconDrawableManager.STATE_SELECT)
            } else {
                PhotoMenuIconDrawableManager.getMenuIconForState(actionId, PhotoMenuIconDrawableManager.STATE_NORMAL)
            }
        }

        // case 4: 对 action_details 做处理
        if (supportOperation == OPERATION_SUPPORT_INFO) {
            val isInDetailsMode = pageViewModel.details.isInDetailsMode.value == true
            val isHasRecommend = isHasRecommended(mediaItem.path.toString())
            // 在详情模式下，则显示选中状态，否则普通状态
            var state = if (isInDetailsMode) PhotoMenuIconDrawableManager.STATE_SELECT else PhotoMenuIconDrawableManager.STATE_NORMAL
            if (isHasRecommend) {
                // 有智能推荐则加上相关的 flag
                state = state or PhotoMenuIconDrawableManager.STATE_FLAG_RECOMMEND
            }
            return PhotoMenuIconDrawableManager.getMenuIconForState(actionId, state)
        }

        return menuItemInfo?.menuIcon ?: MenuIcon.NULL
    }

    /**
     * 根据传入的媒体项、菜单项信息、支持的操作和所有支持的操作，查询并返回图片菜单项的提示状态。
     *
     * 小红点已下架---下架时间2025/07
     *
     * @param mediaItem 媒体项对象
     * @param menuItemInfo 菜单项信息对象
     * @param supportOperation 当前支持的操作
     * @return 图片菜单项的提示状态
     */
    private fun queryTipsStateFor(
        mediaItem: MediaItem,
        menuItemInfo: MenuItemInfo,
        supportOperation: Long
    ): PhotoMenuItemTips {
        //case 1: 显示图片提示
        if (R.id.action_edit == menuItemInfo.action && (mediaItem.mediaType == MEDIA_TYPE_IMAGE)) {
            val iconTips = queryIconTipsStateFor()
            if (iconTips != null) {
                return iconTips
            }
        }

        // case 2: 没有提示
        if (menuItemInfo.shouldShowNewTip.not()) {
            return PhotoMenuItemTips.ItemTipsNone
        }

        // case 3: 显示文本提示
        val textTips = queryTextTipsStateFor(supportOperation)
        if (textTips != null) {
            return textTips
        }

        // 默认没有提示
        return PhotoMenuItemTips.ItemTipsNone
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun queryTextTipsStateFor(
        supportOperation: Long
    ): PhotoMenuItemTips.ItemTipsText? {
        /**
         * 需求说明：
         * - 可能会有多个菜单需要文本提示，但只允许同时出现一个菜单文本提示，
         *   因此，需要给每个提示一个优先级，展示处按照优先级来展示。
         * - 此处优先级设定： 给定一个 `order` ,数字小的优先展示。
         *
         * - 推荐处理方式：
         * ```
         * withOrder { order ->
         *
         *  // content
         *
         * } ?:  withOrder { order ->
         *
         *  // content
         *
         * }
         * ```
         * 先声明的先被展示。
         */
        var innerOrder = 0
        val withOrder =
            fun(block: (order: Int) -> PhotoMenuItemTips.ItemTipsText?): PhotoMenuItemTips.ItemTipsText? {
                return block(innerOrder++)
            }

        return withOrder { order -> // 合影优化
            if ((supportOperation == OPERATION_SUPPORT_GROUP_PHOTO) &&
                isFirstUsingFunction(FIRST_USING_GROUP_PHOTO)
            ) {
                PhotoMenuItemTips.ItemTipsText(
                    order = order,
                    textRes = R.string.photopage_group_photo_guide_entrance_tint,
                    onShow = { hasTippedUserGroupPhoto() }
                )
            } else null
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun queryIconTipsStateFor(): PhotoMenuItemTips.ItemIconTips? {
        return if (isSupportWatermarkMaster()) {
            val pair = RestrictButtonConfigUtils.getButtonCornerMarkAndCornerId(pageViewModel.context) ?: return null
            // 控制每次进入只生成一次角标，避免多个系列同时上线的情况下，旋转屏幕后，隐藏后又显示其他系列的角标
            if ((currentWatermarkCornerId != null) && (pair.first != currentWatermarkCornerId)) return null
            currentWatermarkCornerId = pair.first
            PhotoMenuItemTips.ItemIconTips(IconTipType.RESTRICT_WATERMARK, pair.second)
        } else null
    }

    private fun hasTippedUserGroupPhoto() {
        // Marked by caiconghu: 同上，临时放到ISettingAbility
        settingsAbility?.markGroupPhotoUsed()
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun isFirstUsingFunction(function: String): Boolean {
        return ConfigAbilityWrapper.getBoolean(function, defValue = true, expDefValue = false)
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal val dataSource: PhotoInputArgumentsViewModel.DataSource?
        get() = pageViewModel.inputArguments.dataSource.value

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal val isPageDisplayedAboveKeyguardPage: Boolean?
        get() = pageViewModel.pageManagement.isPageDisplayedAboveKeyguardPage.value

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal val isLensEnabled: Boolean?
        get() = (pageViewModel.outputChannels.lensManager.isLensFuncAvailable)
                && (pageViewModel.outputChannels.lensManager.isLensEnabled.value == true)

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal val allowSplitAsNewInstance: Boolean?
        get() = pageViewModel.pageManagement.isSupportSelfSplitting.value?.allowSplitAsNewInstance

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal val isCasting: Boolean?
        get() = pageViewModel.outputChannels.castingManager.isCasting.value

    private val isCastingVideo: Boolean
        get() = pageViewModel.outputChannels.castingManager.isVideoCasting.value ?: false

    private val isContentHighQuality: Boolean
        get() = pageViewModel.dataLoading.focusItemViewData?.contentQuality == PhotoItemViewData.ContentQuality.HIGH

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal val isCaptureVideoFrameSuppressed: Boolean
        get() = pageViewModel.pageManagement.pageOperationState.value?.contains(SuppressMenuCaptureVideoFrame) ?: false

    private val isDisableMenuAll: Boolean
        get() = pageViewModel.pageManagement.pageOperationState.value?.contains(DisableMenuAll) ?: false

    /**
     * 当前 [activity] 是否显示在默认 [Display.DEFAULT_DISPLAY] 上
     */
    private val isShowedOnDefaultDisplay: Boolean
        get() = pageViewModel.inputArguments.features.value?.isShowedOnDefaultDisplay ?: true

    /**
     * 当前版本是否支持外屏连拍接续功能
     */
    private val isSupportCshotContinuation: Boolean
        get() = ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_CSHOT_CONTINUATION)

    /**
     * 获取MediaItem支持的菜单项
     */
    @Suppress("LongMethod")
    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun MediaItem.calcSupportedActions(): LongFlags =
        LongFlags(this.supportedOperations).also { flags ->
            /**
             * case 1: 当前MediaItem支持投屏 -> add OPERATION_SUPPORT_DLNA flag
             * case 2: 当前系统相机支持Heif拍摄，且当前MediaItem是Heif -> add OPERATION_SUPPORT_TRANSFORM_FORMAT flag
             * case 3: 当前系统支持HDR转码特性且当前MediaItem是HDR(HLG) 或者 当前MediaItem 是 5.1 声道视频
             *          -> add OPERATION_SUPPORT_TRANSFORM_HLG_TO_SDR flag
             * case 4: 当前MediaItem不是连拍 -> add OPERATION_SUPPORT_RENAME_FILE flag
             * case 5: 当前MediaItem是FaceItem -> add OPERATION_SUPPORT_FREE_FACE flag
             * case 6: 当前是连拍图片，且支持连拍归档 -> add OPERATION_SUPPORT_CSHOT
             *         如果支持连拍，判断是否支持GIF合成
             * case 7: 当前是超级文本图片 -> add OPERATION_SUPPORT_HYPER_TEXT
             * case 8: 当前是视频，且不是锁屏状态 -> add OPERATION_SUPPORT_VIDEO_GET_FRAME
             * case 9: 当前图片支持AI证件照 -> add OPERATION_SUPPORT_AI_ID
             * case 10: 当前图片支持人像景深 -> add OPERATION_SUPPORT_PORTRAIT_BLUR
             * case 11: 当前图片支持合影优化 -> add OPERATION_SUPPORT_GROUP_PHOTO
             * case 13: 当前图片支持olive播放 -> add OPERATION_SUPPORT_OLIVE_PHOTO
             * case 14: 当前图片为实况照片时支持另存为视频 -> add OPERATION_SUPPORT_EXPORT_VIDEO
             * case 15: 当前图片支持图片矫正 -> add OPERATION_SUPPORT_PIC_CORRECTION
             * case 16: 当前图片是否支持设置为封面 -> add OPERATION_SUPPORT_SET_COVER
             * case 17: 当前大图非视频 -> add OPERATION_SUPPORT_CONVERT_PDF
             * case 19: 当前大图是视频且支持视频播放 -> add OPERATION_SUPPORT_SYSTEM_PLAYER
             * case 20: 当前系统支持视频壁纸 -> add OPERATION_SUPPORT_SETAS_WALLPAPER
             */
            flags.addFlags(OPERATION_SUPPORT_DELETE)

            // CASE 1
            shouldSupportCasting(this) {
                flags.addFlags(OPERATION_SUPPORT_DLNA)
            }

            // CASE 2
            shouldSupportHeif2Jpeg(this) {
                flags.addFlags(OPERATION_SUPPORT_TRANSFORM_HEIF_TO_JPEG)
            }

            // CASE 3
            shouldSupportHdr2Sdr(this) {
                flags.addFlags(OPERATION_SUPPORT_TRANSFORM_TO_SDR)
            }

            // CASE 4
            shouldSupportFileRenaming(this) {
                flags.addFlags(OPERATION_SUPPORT_RENAME_FILE)
            }

            // CASE 5
            shouldSupportFaceFreeing {
                flags.addFlags(OPERATION_SUPPORT_FREE_FACE)
            }

            // CASE 6
            shouldSupportCShot(this) {
                flags.addFlags(OPERATION_SUPPORT_CSHOT)
                //支持GIF合成才会添加Action
                shouldSupportShowGifOperation(this) {
                    flags.addFlags(OPERATION_SUPPORT_SAVE_TO_GIF)
                }
            }

            // CASE 7
            shouldSupportEnhanceText(this) {
                flags.addFlags(OPERATION_SUPPORT_ENHANCE_TEXT)
            }

            // CASE 8
            shouldSupportGetVideoFrame(this) {
                flags.addFlags(OPERATION_SUPPORT_VIDEO_GET_FRAME)
            }

            // CASE 9
            shouldSupportAiID(this) {
                flags.addFlags(OPERATION_SUPPORT_AI_ID)
            }

            //CASE 10
            shouldSupportPortraitBlur(this) {
                flags.addFlags(OPERATION_SUPPORT_PORTRAIT_BLUR)
            }

            // CASE 11
            shouldSupportGroupPhoto(this) {
                flags.addFlags(OPERATION_SUPPORT_GROUP_PHOTO)
            }

            //CASE 12
            shouldSupportCloudDownload(this) {
                flags.addFlags(OPERATION_SUPPORT_CLOUD_DOWNLOAD)
            }

            //CASE 13
            shouldSupportOlivePhoto(this) {
                flags.addFlags(OPERATION_SUPPORT_OLIVE_PHOTO)
                if (supportGifSynthesis && (isPageDisplayedAboveKeyguardPage?.not() != false)) {
                    flags.addFlags(OPERATION_SUPPORT_SAVE_TO_GIF)
                }
            }

            //CASE 14
            shouldSupportExportVideo(this) {
                flags.addFlags(OPERATION_SUPPORT_EXPORT_VIDEO)
            }

            // CASE 16
            shouldSupportSetCover {
                flags.addFlags(OPERATION_SUPPORT_SET_COVER)
            }

            //CASE 17
            shouldSupportPicToPdf(this) {
                flags.addFlags(OPERATION_SUPPORT_CONVERT_PDF)
            }

            // CASE 18
            shouldSupportGooglePassScan(this) {
                flags.addFlags(OPERATION_SUPPORT_GOOGLE_PASS_SCAN)
            }

            // CASE 19
            shouldOpenInSupportSystemPlayer(this) {
                flags.addFlags(OPERATION_SUPPORT_OPEN_IN_SYSTEM_PLAYER)
            }

            //CASE 20
            shouldSupportVideoWallpaper(this) {
                flags.addFlags(OPERATION_SUPPORT_SETAS_WALLPAPER)
            }

            //CASE 21
            shouldSupportExportOlive(this) {
                flags.addFlags(OPERATION_SUPPORT_EXPORT_OLIVE)
            }
        }

    private fun shouldSupportSetCover(doIfSupports: () -> Unit) {
        val isDisplaySetCoverMenu =
            pageViewModel.inputArguments.dataSource.value?.extra?.getBoolean(KEY_DISPLAY_SET_COVER_MENU, true) ?: true
        if (isDisplaySetCoverMenu.not()) return
        if (pageViewModel.dataLoading.isCoverSupported()) {
            doIfSupports()
        }
    }

    private fun shouldSupportOlivePhoto(mediaItem: MediaItem, doIfSupports: () -> Unit) {
        if (mediaItem.isOlivePhoto() && ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_OLIVE)) {
            doIfSupports()
        }
    }

    private fun shouldSupportExportVideo(mediaItem: MediaItem, doIfSupports: () -> Unit) {
        val isSupportOlive = ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_OLIVE)
        if (isSupportOlive && mediaItem.isOlivePhoto()) {
            doIfSupports()
        }
    }

    private fun shouldSupportCloudDownload(mediaItem: MediaItem, doIfSupports: () -> Unit) {
        val isCasting = pageViewModel.outputChannels.castingManager.isCasting.value ?: false
        val isDownloadable = pageViewModel.cloudSync.isDownloadable.value == true
        if (isCasting || isDownloadable.not()) {
            // 当前属于投屏状态  或者 当前不可下载  则不直接return，将不会显示云下载图标
            return
        }
        if (mediaItem.isSupportDownloadOriginal().not()) {
            // google云不支持回收站数据的原图下载
            return
        }
        if (mediaItem.isVideo) {
            // 视频不会进入大图自动下载，所以不管是什么网络状态，只要是没有已下载就都要显示云下载的图标
            doIfSupports()
        } else {
            val isNetWorkError = NetworkMonitor.isNetworkValidated().not()
            val isMobileValidated = NetworkMonitor.isMobileValidated() && NetworkMonitor.isWifiConnected().not()
            // 当前可下载，且不是投屏状态时，网络异常状况（没有一种网络可用），移动数据连通wifi未连接支持主动原图下载
            if (isNetWorkError || isMobileValidated) {
                doIfSupports()
            }
        }
    }

    /**
     * 是否支持系统播放器
     */
    private fun shouldOpenInSupportSystemPlayer(mediaItem: MediaItem, doIfSupports: () -> Unit) {
        // 如果不是内销版，则不显示“在系统播放器中打开”的入口
        if (!ConfigAbilityWrapper.getBoolean(IS_REGION_CN)) {
            return
        }
        if (mediaItem.mediaType != MEDIA_TYPE_VIDEO) {
            return
        }
        if (!OpenInSystemPlayerHelper.isSystemPlayerEntrySupportCloud()) {
            return
        }
        // 如果播放器中的meta-data数据不支持倍速播放，则直接返回，不显示“在系统播放器中打开”的入口
        if (!isSystemPlayerAvailable) {
            GLog.d(TAG, LogFlag.DL) { "shouldOpenInSupportSystemPlayer, is not support double speed." }
            return
        }
        doIfSupports()
    }

    /**
     * 是否支持导出实况
     */
    private fun shouldSupportExportOlive(mediaItem: MediaItem, doIfSupports: () -> Unit) {
        val supportExportOlive = ConfigAbilityWrapper.getBoolean(ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_VIDEO_EXTRACT_OLIVE)
        if (mediaItem.isVideo && supportExportOlive) {
            doIfSupports()
        }
    }

    private fun shouldSupportPicToPdf(mediaItem: MediaItem, doIfSupports: () -> Unit) {
        if (mediaItem.mediaType == MEDIA_TYPE_VIDEO) {
            return
        }
        doIfSupports()
    }

    /**
     * 更多菜单列表中"添加到Google Wallet"逻辑
     */
    private fun shouldSupportGooglePassScan(mediaItem: MediaItem, doIfSupports: () -> Unit) {
        if (mediaItem.mediaType != MEDIA_TYPE_IMAGE) {
            GLog.d(TAG, LogFlag.DL) { "[shouldSupportGooglePassScan] false: is not image" }
            return
        }
        if (mediaItem.isSupportPassAddToGoogleWallet()) {
            GLog.d(TAG, LogFlag.DL) { "[shouldSupportGooglePassScan] isSupportPassAddToGoogleWallet:true" }
            doIfSupports()
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun shouldSupportCasting(mediaItem: MediaItem, doIfSupports: () -> Unit) {
        if (ConfigAbilityWrapper.getBoolean(IS_REALME_BRAND) && ConfigAbilityWrapper.getBoolean(IS_PRODUCT_LIGHT)) {
            GLog.i(TAG, LogFlag.DL, "[shouldSupportCasting] skip ,cause by realme light os disable casting")
            return
        }
        val isKeyguardLocked = KeyguardManagerUtils.isKeyguardLocked()
        GLog.d(TAG, LogFlag.DL, "[shouldSupportCasting] isKeyguardLocked=$isKeyguardLocked")
        val isCastingEnabled = if (isKeyguardLocked) false else pageViewModel.outputChannels.castingManager.isCastingEnable.value ?: false
        if (isCastingEnabled.not()) {
            GLog.i(TAG, LogFlag.DL, "[shouldSupportCasting] skip ,cause by disable casting")
            return
        }
        if (mediaItem.isSupportCasting().not()) {
            GLog.i(TAG, LogFlag.DL, "[shouldSupportCasting] skip ,cause by media not support casting")
            return
        }
        doIfSupports()
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun shouldSupportHeif2Jpeg(mediaItem: MediaItem, doIfSupports: () -> Unit) {
        val isHeifItem = (mediaItem.getSupportFormat(FORMAT_HEIF) == FORMAT_HEIF)
        val isCameraSupportHeifOutput = FeatureUtils.isSupportCameraHeif
        if (isHeifItem && isCameraSupportHeifOutput) {
            doIfSupports()
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun shouldSupportHdr2Sdr(mediaItem: MediaItem, doIfSupports: () -> Unit) {
        val isHlgItemSupport = VideoTypeUtils.isHlgVideo(mediaItem) && supportHlgTransform
        val isDolbyItemSupport = VideoTypeUtils.isDolbyVideo(mediaItem) && supportDolbyTransform
        val isSpatialItem = VideoTypeUtils.isSpatialAudio(mediaItem)
        if (isHlgItemSupport || isDolbyItemSupport || isSpatialItem || mediaItem.isHdrVideoOlive()) {
            doIfSupports()
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun shouldSupportFileRenaming(mediaItem: MediaItem, doIfSupports: () -> Unit) {
        val isCShotItem = (mediaItem.getSupportFormat(FORMAT_CSHOT) == FORMAT_CSHOT)
        if (isCShotItem.not()) {
            doIfSupports()
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun shouldSupportFaceFreeing(doIfSupports: () -> Unit) {
        dataSource?.let { dataSource ->
            if ((dataSource.whichDataSet == DataSet.PERSON)
                || (dataSource.whichDataSet == DataSet.PET)
                || (dataSource.whichDataSet == DataSet.PERSON_PET_GROUP)
            ) {
                doIfSupports()
            }
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun shouldSupportCShot(mediaItem: MediaItem, doIfSupports: () -> Unit) {
        if ((mediaItem is LocalMediaItem) && DatabaseUtils.isCShotIdValid(mediaItem.cShotID)) {
            doIfSupports.invoke()
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun shouldSupportEnhanceText(mediaItem: MediaItem, doIfSupports: () -> Unit) {
        // 先判断format是否支持，这个内存判断，耗时短
        if (isItemSupportFormat(mediaItem, FORMAT_ENHANCE_TEXT).not()) {
            return
        }
        // 再判断是否支持超级文本2.0，因为超级文本1.0和2.0是互斥的，只有在不支持2.0的时候才支持1.0
        val isSupportSuperTextV2 = ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_SUPER_TEXT_V2)
        if (isSupportSuperTextV2.not()) {
            doIfSupports.invoke()
        }
    }

    /**
     * 显示“另存为 GIF”的条件
     * 前提条件：mediaItem是连拍类型的图片
     * 1.机器支持Gif合成;
     * 2.不是从锁屏进入大图;
     * 3.连拍的数量大于1张;
     * */
    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun shouldSupportShowGifOperation(mediaItem: MediaItem, supports: () -> Unit) {
        if (supportGifSynthesis && (isPageDisplayedAboveKeyguardPage?.not() != false)) {
            checkCShotItemMatchGifRule(
                scope = pageViewModel.viewModelScope,
                cShotId = mediaItem.cShotID,
                onFail = { }
            ) {
                supports.invoke()
            }
        }
    }

    internal fun shouldSupportPortraitBlur(mediaItem: MediaItem, doIfSupports: () -> Unit) {

        // 先判断format是否支持，这个内存判断，耗时短
        /**
         * MimeTypeUtils.isHeifOrHeic(mediaItem.mimeType)
         * 规避方案 mark by ********
         * 经产品同意，暂时屏蔽人像景深入口，等#8816059适配过后再取消
         * 这个临时方案
         */
        if ((isItemSupportFormat(mediaItem, FORMAT_PORTRAIT_BLUR).not()) || (MimeTypeUtils.isHeifOrHeic(mediaItem.mimeType))) {
            return
        }
        // 再判断ability的接口
        val isSupportPortraitBlur = ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_PORTRAIT_BLUR_BEFORE_OS16)
        GLog.i(TAG, LogFlag.DL, "shouldSupportPortraitBlur: $isSupportPortraitBlur")
        if (isSupportPortraitBlur) {
            doIfSupports.invoke()
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun shouldSupportGetVideoFrame(mediaItem: MediaItem, doIfSupports: () -> Unit) {
        isPageDisplayedAboveKeyguardPage?.let { isLocked ->
            if (isLocked.not() && (mediaItem.mediaType == MEDIA_TYPE_VIDEO)) {
                doIfSupports.invoke()
            }
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun shouldSupportAiID(mediaItem: MediaItem, doIfSupports: () -> Unit) {
        val isSupportAiIDPhoto = ConfigAbilityWrapper.getBoolean(ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_AI_ID_PHOTO)
        if ((mediaItem.tagFlags and FLAG_AI_ID != 0L) && isSupportAiIDPhoto) {
            doIfSupports.invoke()
        }
    }

    private fun shouldSupportGroupPhoto(mediaItem: MediaItem, doIfSupports: () -> LongFlags) {
        if (GProperty.DEBUG_GROUP_PHOTO == GProperty.FEATURE_FORCE_ENABLE) {
            GLog.d(TAG, LogFlag.DL, "debug mode,show group photo entrance")
            doIfSupports.invoke()
            return
        }
        if (TemperatureUtil.isTemperatureAllow(MAX_TEMPERATURE_ALLOWED_FOR_GP).not()) {
            GLog.w(TAG, LogFlag.DL, "[shouldSupportGroupPhoto] phone temperature is too high, not support GroupPhoto!")
            return
        }
        // 查询当前是否支持最佳表情
        val isBestTakeSupport = ConfigAbilityWrapper.getBoolean(FeatureSwitch.FEATURE_IS_SUPPORT_AI_BEST_TAKE)
        if (supportGroupPhoto && groupPhotoAbility?.isItemSupportGroupPhoto(
                mediaItem.mimeType,
                mediaItem.isGroupPhoto(),
                mediaItem.filePath,
                isBestTakeSupport
            ) == true
        ) {
            pageViewModel.launch {
                AIUnitFeatureConfigHelper.writeAIGroupPhotoDownloadConfig(pageViewModel.context)
            }
            doIfSupports.invoke()
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun shouldSupportVideoWallpaper(mediaItem: MediaItem, doIfSupports: () -> Unit) {
        if (!mediaItem.isVideo) return

        if (ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_VIDEO_WALLPAPER, true)) {
            doIfSupports()
        }
    }

    /**
     * 获取MediaItem不支持的菜单项
     */
    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun MediaItem.calcUnsupportedActions(): LongFlags =
        LongFlags().also { notSupportFlags ->
            /**
             * case 1: 当前MediaItem的状态为快拍缩图 ->
             *         - 支持quick图删除：remove OPERATION_SUPPORT_SHARE | OPERATION_SUPPORT_EDIT | OPERATION_SUPPORT_MORE flags
             *         - 不支持quick图删除：remove OPERATION_SUPPORT_SHARE | OPERATION_SUPPORT_EDIT
             *                                   OPERATION_SUPPORT_DELETE | OPERATION_SUPPORT_MORE flags
             *         - 无影连拍导出的quick图也不支持分享/编辑/更多等常用菜单
             * case 2: 当前MediaItem是VIDEO或者CAMERA -> remove OPERATION_SUPPORT_SETAS_CONTACT
             * case 3: 当前MediaItem 不满足 isHlgVideo 或者不是5.1声道视频-> remove OPERATION_SUPPORT_TRANSFORM_HLG_TO_SDR
             * case 4: 当前MediaItem 不是 Heif || 相机不支持 Heif 输出 -> remove OPERATION_SUPPORT_TRANSFORM_FORMAT
             * case 6: 当MediaItem  是Raw，但不支持Raw编辑 -> remove OPERATION_SUPPORT_EDIT
             */

            // case 1: quick图删除功能目前在 os 14.0 及以上，若不支持quick图删除，此状态下应该禁用大图”删除“功能
            shouldNotSupportIfTemporaryCacheItem(this) {
                if (ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_QUICK_PHOTO_DELETE).not()) {
                    notSupportFlags.addFlags(OPERATION_SUPPORT_DELETE)
                }
            }

            // case 2: 当前MediaItem是VIDEO或者CAMERA，不支持设置为联系人
            shouldNotSupportSetAsWallpaperOrContact(this) {
                notSupportFlags.addFlags(
                    OPERATION_SUPPORT_SETAS_CONTACT
                )
            }

            // case 3: 当前MediaItem 不满足 isHlgVideo 或 isDolbyVideo，不支持Hdr转Slg
            shouldNotSupportHdr2Slg(this) {
                notSupportFlags.addFlags(OPERATION_SUPPORT_TRANSFORM_TO_SDR)
            }

            // case 4: 当前MediaItem 不是 Heif || 相机不支持 Heif 输出，不支持heif转jpeg
            shouldNotSupportHeif2Jpeg(this) {
                notSupportFlags.addFlags(OPERATION_SUPPORT_TRANSFORM_HEIF_TO_JPEG)
            }

            // case 5: 当前不支持分裂新页面
            shouldNotSupportSelfSplitting {
                notSupportFlags.addFlags(OPERATION_SUPPORT_SELF_SPLIT)
            }

            // case 6: 当前是Raw，但不支持Raw编辑
            shouldNotSupportRawEdit(this) {
                notSupportFlags.addFlags(OPERATION_SUPPORT_EDIT)
            }
        }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun shouldNotSupportIfTemporaryCacheItem(mediaItem: MediaItem, doIfNotSupports: () -> Unit) {
        if (mediaItem.isTemporaryCacheItem() || mediaItem.isFastCShotQuickImage()) {
            doIfNotSupports()
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun shouldNotSupportSetAsWallpaperOrContact(mediaItem: MediaItem, doIfNotSupports: () -> Unit) {
        when (mediaItem.mediaType) {
            MEDIA_TYPE_VIDEO, MEDIA_TYPE_SUPPORT_CAMERA -> doIfNotSupports()
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun shouldNotSupportHdr2Slg(mediaItem: MediaItem, doIfNotSupports: () -> Unit) {
        if (VideoTypeUtils.isHlgVideo(mediaItem).not()
            && VideoTypeUtils.isDolbyVideo(mediaItem).not()
            && VideoTypeUtils.isSpatialAudio(mediaItem).not()
            && mediaItem.isHdrVideoOlive().not()
        ) {
            doIfNotSupports()
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun shouldNotSupportHeif2Jpeg(mediaItem: MediaItem, doIfNotSupports: () -> Unit) {
        val isHeifItem = (mediaItem.getSupportFormat(FORMAT_HEIF) == FORMAT_HEIF)
        val isCameraSupportHeifOutput = FeatureUtils.isSupportCameraHeif
        if (isHeifItem.not() || isCameraSupportHeifOutput.not()) {
            doIfNotSupports()
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun shouldNotSupportSelfSplitting(doIfNotSupports: () -> Unit) {
        val allowSplitAsNewInstance = allowSplitAsNewInstance ?: false
        val isCasting = isCasting ?: false

        // 设备不允许自分屏或者正在投屏状态，不展示自分屏按钮
        if (allowSplitAsNewInstance.not() || isCasting) {
            doIfNotSupports()
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun shouldNotSupportRawEdit(mediaItem: MediaItem, doIfNotSupports: () -> Unit) {
        val isRaw = ImageTypeUtils.isRawFilePath(mediaItem.path)
        val isSupportRawEdit = ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_RAW_EDIT)
        // 是Raw 但是不支持Raw编辑
        if (isRaw && isSupportRawEdit.not()) {
            doIfNotSupports()
        }
    }

    /**
     * 当菜单已经被推迟加载了后，在合适的时机重新允许菜单加载
     */
    internal fun permitMenuInflateAfterPostponed() {
        GLog.d(TAG, LogFlag.DL) { "[permitMenuInflateAfterPostponed] menu could Inflate now! " }
        couldInflateMenuIfPostponed = true
        photoMenuDataEmitter.tryEmit(photoMenuDataEmitter.value.anonymousUpdate())      // 主动触发一次菜单数据刷新，会引发菜单UI加载
    }

    internal fun notifyMenuThemeUpdate(topMenuTheme: MenuDecorationTheme, bottomMenuTheme: MenuDecorationTheme) {
        _menuTheme.tryEmit(Pair(topMenuTheme, bottomMenuTheme))
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    fun isSupportHasselblad(): Boolean = ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_HASSEL_WATERMARK)

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    fun isSupportPhotoEditorWatermark(): Boolean = ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_PHOTO_EDITOR_WATERMARK)

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    fun isSupportSpringFestivalWatermark(): Boolean = ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_SPRING_FESTIVAL_WATERMARK)

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    fun isSupportPrivacyWatermark(): Boolean = ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_PRIVACY_WATERMARK)

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    fun isSupportAiEliminate(): Boolean = ConfigAbilityWrapper.getBoolean(FEATURE_IS_DEVICE_SUPPORT_AI_ELIMINATE)

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    fun isFirstUseAIEliminate(): Boolean = ConfigAbilityWrapper.getBoolean(AI_ELIMINATE_HAS_USED)

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    fun isSupportWatermarkMaster(): Boolean = ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_PHOTO_EDITOR_WATERMARK)

    /**
     * 菜单当前编辑按钮是否为 置灰状态
     */
    fun actionEditorEnable(): Boolean = photoMenu.value?.menuItemStates?.get(R.id.action_edit)?.isEnabled ?: false

    /**
     * 菜单项当前是否 显示
     */
    fun menuVisible(): Boolean = menuPresent != PhotoMenuPresent.None

    /**
     * 创建[ISettingsAbility]
     */
    private fun initSettingsAbility(): ISettingsAbility? {
        return pageViewModel.context.getAppAbility<ISettingsAbility>().apply {
            if (this == null) {
                GLog.i(TAG, LogFlag.DL) { "[initSettingsAbility]init settingsAbility failed , settingsAbility is null" }
            }
        }
    }

    /**
     * 创建[IModuleAccessAbility]
     */
    private fun initModuleAccessAbility(): IModuleAccessAbility? {
        return pageViewModel.context.getAppAbility<IModuleAccessAbility>().apply {
            if (this == null) {
                GLog.i(TAG, LogFlag.DL) { "[initModuleAccessAbility]init moduleAccessAbility failed , moduleAccessAbility is null" }
            }
        }
    }

    /**
     * 创建[IGroupPhotoAbility]
     */
    private fun initGroupPhotoAbility(): IGroupPhotoAbility? {
        return pageViewModel.context.getAppAbility<IGroupPhotoAbility>().apply {
            if (this == null) {
                GLog.i(TAG, LogFlag.DL) { "[initGroupPhotoAbility]init groupPhotoAbility failed , groupPhotoAbility is null" }
            }
        }
    }

    /**
     * 通知隐藏水印角标
     */
    private fun notifyHideWatermarkCorner() {
        val cornerId = currentWatermarkCornerId ?: return
        launch {
            if (RestrictButtonConfigUtils.checkCurrentCornerIsNeedHide(cornerId)) {
                _clearWatermarkCorner.setOrPostValue(true)
            }
        }
    }

    /**
     * 菜单是否支持详情按键
     */
    fun isSupportDetailAbility(): Boolean {
        return pageViewModel.inputArguments.menuPresent.value?.isSupportDetail ?: false
    }

    /**
     * 修改顶部菜单栏是否可以点击
     */
    fun changeCanClickTopMenu(canClick: Boolean) {
        _canClickTopMenu.setOrPostValue(canClick)
    }

    /**
     * 修改底部菜单栏是否可以点击
     */
    fun changeCanClickBottomMenu(canClick: Boolean) {
        _canClickBottomMenu.setOrPostValue(canClick)
    }

    /**
     * 是否是支持可阻断的菜单项
     * @param itemId 菜单项 id
     * @return true 支持，false 不支持
     */
    fun shouldSuppressedItems(itemId: Int): Boolean {
        return suppressibleMenuItems.contains(itemId)
    }

    companion object {
        private const val TAG = "PhotoMenuControlViewModel"

        /**
         * 允许执行合影优化的最高温度
         * 超过 47° 不支持合影优化
         */
        private const val MAX_TEMPERATURE_ALLOWED_FOR_GP = 47f
        private const val WAITING_TIME = 500L
        private const val POLLING_DURATION = 10L
    }
}

/**
 * 菜单更新所依赖的所有数据，
 * 通过更新此数据类来触发菜单更新，
 * 如图片变更，图片信息变更，业务属性/状态变更。
 */
private data class MenuDependentData(
    /*
     * 通过 LiveData 更新的参数
     */

    /**
     * 当前 focus 的 PhotoItemViewData
     */
    val photoItemViewData: PhotoItemViewData? = null,

    /**
     * 菜单预设
     */
    val menuPresent: PhotoMenuPresent? = null,

    /**
     * 云相关，是否可下载
     */
    val isDownloadable: Boolean? = null,

    /**
     * 系统播放器是否可用，与视屏APP和媒体播放器相关
     */
    val isSystemPlayerAvailable: Boolean? = null,

    /**
     * 是否在投屏中，与投屏按钮、云下载相关
     */
    val isCasting: Boolean? = null,

    /**
     * Lens 是否可用
     */
    val isLensEnabled: Boolean? = null,

    /**
     * 是否可投屏，DLNA 相关
     */
    val isCastingEnable: Boolean? = null,

    /**
     * 是否olive状态开启
     */
    val isOliveEnable: Boolean? = null,

    /**
     * 页面操作状态
     */
    val pageOperationState: PageOperationState? = null,

    /**
     * 播放状态
     */
    val playerState: AVController.LoadingState? = null,

    /**
     * 自分屏相关
     */
    val isAllowSplitAsNewInstance: Boolean? = null,

    /*
     * 通过内外部主动更新的参数
     */

    /**
     * 抑制图标显示
     */
    val suppressMenuIconShow: Boolean? = null,

    /**
     * 当前正在执行的 Action，用 id 的和来记录
     */
    val executingActionSum: Int? = null,

    /**
     * 网络类型，与云下载相关
     */
    val networkConnectType: ConnectType? = null,

    /**
     * 网络状态，与云下载相关
     */
    val networkIsValidated: Boolean? = null,

    /**
     * Google Pass Scan 的状态
     */
    val googlePassScanState: FuncItem.State? = null,

    /**
     * 匿名更新，
     * 无法定义具体值的情况下，更新这个整数来确保不被去重。
     * 由 anonymousUpdate() 控制。
     */
    private val anonChanges: Int = 0,
) {
    /**
     * 当前 focusSlot
     */
    val focusSlot: Int
        get() = photoItemViewData?.position ?: INVALID_INDEX

    /**
     * 无法定义具体值的情况下需要更新时可以调用此方法来生成新实例。
     * 如由配置变更时触发，
     */
    @Deprecated("方法设置不合理，后续会删除此方法，并细化变更项加入MenuDependentData中")
    fun anonymousUpdate(): MenuDependentData = copy(anonChanges = (anonChanges + 1) and 7)
}

/**
 * 标题更新所依赖的全部数据，
 * 如图片变更，图片信息变更，标题变更，预览选择数变更。
 */
private data class TitleDependentData(
    /**
     * [inputArguments.title] 的导航图标资源
     */
    val navigationIcon: MenuIcon = MenuIcon.NULL,
    /**
     * [inputArguments.title] 的 titleContent，
     * 不为空时会用于创建 PhotoMenuTitle。
     */
    val titleContent: String? = null,
    /**
     * 多选预览时的数量，
     * 用于多选预览时创建 PhotoMenuTitle。
     */
    val selectedCount: Int = 0,
    /**
     * 标题对应的 Media 的 key，取自 PhotoInfo.normalizedPath，
     * 创建 PhotoMenuTitle 的必要参数。
     */
    val photoKey: String = EMPTY_STRING,
    /**
     * PhotoInfo.details 内的 Date，
     * 用于 titleContent 为空时创建 PhotoMenuTitle。
     */
    val detailsDate: String? = null,
    /**
     * PhotoInfo.details 内的 Time，
     * 用于 titleContent 为空时创建 PhotoMenuTitle。
     */
    val detailsTime: String? = null,
    /**
     * PhotoInfo.details 内的 FormatDateTime，
     * 用于 titleContent 为空时创建 PhotoMenuTitle。
     */
    val detailsFormatDateTime: String? = null,
    /**
     * PhotoInfo.details 内的 Location，
     * 用于 titleContent 为空时创建 PhotoMenuTitle。
     */
    val detailsLocation: String? = null,
) {
    /**
     * 重写以去除敏感信息（navIconResId, titleContent, detailsLocation）
     *
     * 用于日志打印
     *
     * @return 脱敏后的当前信息
     */
    override fun toString(): String = "TitleDependentData(" +
            "selectedCount: $selectedCount, " +
            "photoKey: $photoKey, " +
            "detailsDate: $detailsDate, " +
            "detailsTime: $detailsTime, " +
            "detailsFormatDateTime: $detailsFormatDateTime" +
            ")"
}