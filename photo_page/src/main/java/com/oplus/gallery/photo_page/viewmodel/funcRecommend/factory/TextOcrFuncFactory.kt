/********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - TextOcrFuncFactory
 ** Description:文本功能项的工厂
 ** Version: 1.0
 ** Date: 2024-05-08
 ** Author: zhongxuechang@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** zhongxuechang@Apps.Gallery3D    2024-05-08     1.0
 ********************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.funcRecommend.factory

import android.content.Context
import android.graphics.Bitmap
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.isWaterMarkerPhoto
import com.oplus.gallery.business_lib.model.data.base.utils.Constants
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_ENHANCE_TEXT
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_TEXT
import com.oplus.gallery.business_lib.model.data.base.utils.ImageTypeUtils
import com.oplus.gallery.business_lib.model.data.base.utils.MediaItemUtils
import com.oplus.gallery.business_lib.supertext.SuperTextAbility
import com.oplus.gallery.business_lib.supertext.data.AiUnitOcrResultWrap
import com.oplus.gallery.business_lib.supertext.data.SuperTextImageInfo
import com.oplus.gallery.foundation.cache.memorycache.BitmapPools
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.SuperText.GET_SUPER_TEXT_V3_JUMP_OCR_SCANNER_SUPPORTED
import com.oplus.gallery.framework.abilities.search.ocrembedding.IOcrEmbeddingAbility
import com.oplus.gallery.framework.abilities.search.ocrembedding.OcrEmbeddingCode
import com.oplus.gallery.framework.abilities.search.ocrembedding.OcrEmbeddingScanProviderHelper
import com.oplus.gallery.framework.abilities.watermark.IWatermarkAbility
import com.oplus.gallery.framework.abilities.watermark.IWatermarkMasterAbility
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.photo_page.viewmodel.funcRecommend.FuncItem
import com.oplus.gallery.photo_page.viewmodel.funcRecommend.FuncItem.State.SUPPORTED
import com.oplus.gallery.photo_page.viewmodel.funcRecommend.FuncItem.State.UNDETECTED
import com.oplus.gallery.photo_page.viewmodel.funcRecommend.FuncItem.State.UNSUPPORTED
import com.oplus.gallery.photo_page.viewmodel.track.producttracker.tracker.IntelliFuncTrackDataHelper
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.file.Dir
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

internal class TextOcrFuncFactory(private val context: Context) : AbsFuncFactory() {

    /** 是否支持超级文本2.0，第一次需要跨进程调用AiUnit，放在主线程容易引起ANR，需放子线程加载 */
    private val isSupportSuperTextV2Func: Boolean by lazy {
        ConfigAbilityWrapper.getBoolean(ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_SUPER_TEXT_V2)
    }

    /** 是否支持屏幕翻译功能 */
    private val isSupportTranslateFunc: Boolean by lazy {
        ConfigAbilityWrapper.getBoolean(ConfigID.Business.Editor.ScreenTranslate.GET_TRANSLATE_SUPPORTED)
    }

    private val superTextAbility by lazy {
        SuperTextAbility(context)
    }

    private var isSpecialSuperTextFormat = false

    override fun getFromCache(mediaItem: MediaItem): List<FuncItem> {
        return when {
            isSupportSuperTextV2Func.not() || isEditableFormat(mediaItem).not() || mediaItem.isSupportSuperText().not() -> getFuncList(UNSUPPORTED)

            mediaItem.getSupportFormat(FORMAT_TEXT) == FORMAT_TEXT -> {
                // 检测有没有OCR结果，有则支持文本OCR
                val ocrResult = superTextAbility.getOcrResultFromCache(SuperTextImageInfo(mediaItem))
                GLog.d(TAG, LogFlag.DL) { "getFromCache, isEditable:${ocrResult?.isEditable}, containsText:${ocrResult?.containsText()}" }
                getFuncList(
                    when (ocrResult?.isEditable) {
                        true -> SUPPORTED
                        false -> UNSUPPORTED
                        else -> UNDETECTED
                    }, ocrResult
                )
            }

            mediaItem.isFormatScanned(FORMAT_TEXT) -> {
                // 没有文本内容，则文本相关的功能都不支持
                GLog.d(TAG, LogFlag.DL) { "isSupport not text, mediaId:${mediaItem.mediaId}" }
                getFuncList(UNSUPPORTED)
            }

            else -> {
                GLog.d(TAG, LogFlag.DL) { "isSupport not scanned, mediaId:${mediaItem.mediaId}" }
                getFuncList(UNDETECTED)
            }
        }
    }

    override fun onCreateFuncTask(mediaItem: MediaItem, bitmap: Bitmap): AbsFuncTask {
        isSpecialSuperTextFormat = isSpecialSuperTextFormat(mediaItem)
        return TextOcrFuncTask(mediaItem, bitmap)
    }

    /**
     * 获取功能支持列表
     * [FuncItem.TextTranslate]，不支持翻译功能时为不支持，可编辑时为支持
     * [FuncItem.LongPressTextOcr]，有文本时为支持
     * [FuncItem.OcrScanner]，GET_SUPER_TEXT_V3_JUMP_OCR_SCANNER_SUPPORTED 为true 时是增强格式为支持,可编辑时为支持
     * [FuncItem.TextOcr]，可编辑时为支持
     * */
    private fun getFuncList(state: FuncItem.State, ocrResult: AiUnitOcrResultWrap? = null): List<FuncItem> {
        return listOf(
            FuncItem.TextTranslate(if (isSupportTranslateFunc) state else UNSUPPORTED),
            FuncItem.LongPressTextOcr(if ((state == UNSUPPORTED) && (ocrResult?.containsText() == true)) SUPPORTED else state, ocrResult),
            FuncItem.TextOcr(state),
            if (ConfigAbilityWrapper.getBoolean(GET_SUPER_TEXT_V3_JUMP_OCR_SCANNER_SUPPORTED)) {
                FuncItem.OcrScanner(if (isSpecialSuperTextFormat) SUPPORTED else state)
            } else {
                FuncItem.OcrScanner(UNSUPPORTED)
            }
        )
    }

    private fun MediaItem.isSupportSuperText(): Boolean {
        if (ConditionHelper.isCommonImage(this).not()) return false
        if (ImageTypeUtils.isRawFilePath(path)) {
            GLog.d(TAG, LogFlag.DL) { "isSupportSuperText raw file not supported" }
            return false
        }
        if (!SuperTextAbility.isSizeSupported(this)) {
            GLog.d(TAG, LogFlag.DL, "isSupportSuperText size not supported")
            return false
        }
        return true
    }

    /**
     * 是否为支持编辑的格式，如果不支持编辑，则不显示超级文本2.0入口
     *
     * 目前10bit和人像景深不支持超级文本编辑，超级文本编辑保存后会使前两者的内容丢失
     * 会先判断数据是否解析完成，即tagFlags是否已经解析出来，否则10bit和人像景深格式的判断是不准确的
     * 保险起见，所有没有解析完成的图片都不可进入超级文本编辑。
     */
    private fun isEditableFormat(mediaItem: MediaItem) =
        ((mediaItem.syncStatus == LocalColumns.SYNC_STATUS_DEFAULT)
                && mediaItem.isYuvFormat.not()
                && MediaItemUtils.isItemSupportFormat(mediaItem, Constants.IMediaItemSupportFormat.FORMAT_PORTRAIT_BLUR).not()).also {
            GLog.d(TAG, LogFlag.DL) { "isEditableFormat, isEditableFormat : $it" }
        }

    /**
     * 判断是否为来源于相机和扫一扫app的特殊文本照片
     * 1.包含超级文本flag
     * 2.在Pictures/OcrScanner目录下的图片
     */
    private fun isSpecialSuperTextFormat(mediaItem: MediaItem): Boolean {
        val isSupport = when {
            ConfigAbilityWrapper.getBoolean(GET_SUPER_TEXT_V3_JUMP_OCR_SCANNER_SUPPORTED).not() -> false
            mediaItem.getSupportFormat(FORMAT_ENHANCE_TEXT) == FORMAT_ENHANCE_TEXT -> true
            else -> {
                val directory = Dir.OCRSCANNER.internalPath ?: return false
                val mediaObject = mediaItem.path?.getObject()
                (mediaItem.mediaType == LocalColumns.MEDIA_TYPE_IMAGE
                        && mediaObject is LocalMediaItem
                        && mediaObject.filePath?.startsWith(directory, false) == true)
            }
        }
        GLog.d(TAG, LogFlag.DL) { "isSupportEnhanceText, state:$isSupport" }
        return isSupport
    }

    private inner class TextOcrFuncTask(
        private val mediaItem: MediaItem,
        private val bitmap: Bitmap
    ) : AbsFuncTask() {
        override val tag: String = "TextOcrFuncTask"

        override val funcItems: List<FuncItem> = getFuncList(UNDETECTED)

        override fun onDetect(): List<FuncItem> {
            val startTime = System.currentTimeMillis()
            val ocrBitmap = BitmapUtils.rotateBitmap(bitmap, mediaItem.rotation, false)
            val hasText = isTextImage(ocrBitmap)
            val detectTime = GLog.getTime(startTime)

            val ocrResult: AiUnitOcrResultWrap? = if (hasText) {
                val scale = SuperTextAbility.getScaleFactor(
                    mediaItem.width,
                    mediaItem.height,
                    ocrBitmap.width,
                    ocrBitmap.height
                )
                superTextAbility.ocrBitmapSync(ocrBitmap, SuperTextImageInfo(mediaItem), scale)
            } else null

            val ocrText = ocrResult?.allText?.trim()
            if (ocrText != null) {
                doSingleOcrEmbedding(ocrText)
            }

            if (ocrBitmap != bitmap) {
                BitmapPools.recycle(ocrBitmap)
            }
            val scanTotalTime = System.currentTimeMillis() - startTime
            IntelliFuncTrackDataHelper.recordIntelliFuncScanCount(IntelliFuncTrackDataHelper.ScanType.TEXT_OCR)
            IntelliFuncTrackDataHelper.recordIntelliFuncScanTime(IntelliFuncTrackDataHelper.ScanType.TEXT_OCR, scanTotalTime)

            GLog.d(tag, LogFlag.DL) {
                "onDetect. total:$scanTotalTime, detectTime:$detectTime, hasText:$hasText, editable:${ocrResult?.isEditable}"
            }
            return getFuncList(if (ocrResult?.isEditable == true) SUPPORTED else UNSUPPORTED, ocrResult)
        }

        /**
         * 判定是否为带文本图片，主要操作为去除水印后进行预分类扫描
         */
        private fun isTextImage(srcBitmap: Bitmap): Boolean {
            var detectBitmap = srcBitmap
            if (mediaItem.isWaterMarkerPhoto()) {
                val watermarkMasterAbility = context.getAppAbility<IWatermarkMasterAbility>()
                watermarkMasterAbility?.newWatermarkFileOperator(mediaItem.contentUri)?.use { operator ->
                    if (operator.readWatermarkInfo().isAiMasterWatermark()
                    ) {
                        watermarkMasterAbility.use { ability ->
                            detectBitmap =
                                ability.removeAllWatermark(
                                    srcBitmap,
                                    mediaItem.contentUri,
                                    mediaItem.width,
                                    mediaItem.height,
                                    mediaItem.rotation
                                )
                        }
                    } else {
                        context.getAppAbility<IWatermarkAbility>()?.use { ability ->
                            detectBitmap =
                                ability.removeAllWatermark(
                                    srcBitmap,
                                    mediaItem.contentUri,
                                    mediaItem.width,
                                    mediaItem.height,
                                    mediaItem.rotation
                                )
                        }
                    }
                } ?: run {
                    context.getAppAbility<IWatermarkAbility>()?.use { ability ->
                        detectBitmap =
                            ability.removeAllWatermark(
                                srcBitmap,
                                mediaItem.contentUri,
                                mediaItem.width,
                                mediaItem.height,
                                mediaItem.rotation
                            )
                    }
                }
            }
            val result = superTextAbility.detectImageText(SuperTextImageInfo(mediaItem), detectBitmap)
            if (detectBitmap != srcBitmap) {
                BitmapPools.recycle(detectBitmap)
            }
            return result
        }

        /**
         * 单个大图OCR语义扫描
         * 当有ocr内容时，调用方法去判断是否需要做 ocr embedding
         */
        private fun doSingleOcrEmbedding(ocrText: String) {
            context.getAppAbility<IOcrEmbeddingAbility>()?.use {
                try {
                    if (it.isAllowEmbedding(ocrText).not()) {
                        GLog.d(TAG, LogFlag.DL) { "doSingleOcrEmbedding, ocr text is not allowed embedding, return" }
                        return
                    }

                    if (it.isDownloading) {
                        GLog.e(TAG, LogFlag.DL) { "doSingleOcrEmbedding, OcrEmbeddingAbility is downloading, return" }
                        return
                    }

                    if (it.init().not()) {
                        GLog.e(TAG, LogFlag.DL) { "doSingleOcrEmbedding, init ocr embedding engine fail, return" }
                        return
                    }

                    val versionMode = it.currentModelVersion
                    val where = "${GalleryStore.ScanOcrEmbedding.MEDIA_ID} = ?"
                    val whereArgs: Array<String> = arrayOf("${mediaItem.mediaId}")
                    var ocrEmbeddingInfo = OcrEmbeddingScanProviderHelper.getOcrEmbeddingInfoFromDB(where, whereArgs)
                    GLog.d(tag, LogFlag.DL) { "doSingleOcrEmbedding ocrEmbedding scanState: ${ocrEmbeddingInfo?.mScanState}, " +
                            "ocrEmbedding versionModel: ${ocrEmbeddingInfo?.versionModelPath}, versionMode:$versionMode" }
                    if (ocrEmbeddingInfo == null) {
                        // 如果ocrEmbedding 数据为 null 或 empty 重新整合一个 OcrEmbeddingImageInfo 然后向量化，并将结果插入Ocr_Embedding表
                        ocrEmbeddingInfo = OcrEmbeddingScanProviderHelper.getBaseOcrEmbeddingInfoFromDB(where, whereArgs, mediaItem.filePath)
                        if (ocrEmbeddingInfo != null) {
                            val result = it.processImageText(ocrEmbeddingInfo.mGalleryId, ocrText)
                            GLog.d(tag, LogFlag.DL) { "doSingleOcrEmbedding processImageText result: $result, " +
                                    "image mediaId: ${ocrEmbeddingInfo.mMediaId}" }
                            if (result) {
                                ocrEmbeddingInfo.mScanState = OcrEmbeddingCode.CODE_SUCCESS
                            } else {
                                ocrEmbeddingInfo.mScanState = OcrEmbeddingCode.CODE_SDK_ERROR
                            }
                            OcrEmbeddingScanProviderHelper.insertOcrEmbeddingScanData(listOf(ocrEmbeddingInfo), versionMode)
                        }
                    } else if ((ocrEmbeddingInfo.mScanState != OcrEmbeddingCode.CODE_SUCCESS)
                        || (ocrEmbeddingInfo.versionModelPath != versionMode)) {
                        // 如果模型更新了或 SCAN_STATE != OcrEmbeddingCode.CODE_SUCCESS 重新进行embedding向量化并更新Ocr_Embedding表
                        val result = it.processImageText(ocrEmbeddingInfo.mGalleryId, ocrText)
                        GLog.d(tag, LogFlag.DL) { "doSingleOcrEmbedding processImageText result: $result, " +
                                "image mediaId: ${ocrEmbeddingInfo.mMediaId}" }
                        if (result) {
                            ocrEmbeddingInfo.mScanState = OcrEmbeddingCode.CODE_SUCCESS
                        } else {
                            ocrEmbeddingInfo.mScanState = OcrEmbeddingCode.CODE_SDK_ERROR
                        }
                        OcrEmbeddingScanProviderHelper.updateOcrEmbeddingScanData(listOf(ocrEmbeddingInfo), versionMode)
                    }
                } finally {
                    it.release()
                }
            }
        }
    }

    override fun release() {
        AppScope.launch(Dispatchers.IO) {
            superTextAbility.release()
        }
    }

    companion object {
        private const val TAG = "TextOcrFuncFactory"
    }
}