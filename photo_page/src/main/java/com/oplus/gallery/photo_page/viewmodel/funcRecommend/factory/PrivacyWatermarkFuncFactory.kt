/********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PrivacyWatermarkFuncFactory
 ** Description:隐私水印功能项的工厂
 ** Version: 1.0
 ** Date: 2024-05-08
 ** Author: zhongxuechang@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** zhongxuechang@Apps.Gallery3D    2024-05-08     1.0
 ********************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.funcRecommend.factory

import android.graphics.Bitmap
import android.os.SystemClock
import com.oplus.gallery.basebiz.constants.IntentConstant
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business_lib.cardcase.utils.CardCaseUtils
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.utils.ImageTypeUtils
import com.oplus.gallery.business_lib.model.data.label.LabelSearchEngine
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.watermark.IPrivacyWatermarkAbility
import com.oplus.gallery.framework.abilities.watermark.IWatermarkAbility
import com.oplus.gallery.framework.abilities.watermark.file.PrivacyWatermarkEditableState
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.funcRecommend.FuncItem
import com.oplus.gallery.photo_page.viewmodel.funcRecommend.FuncItem.PrivacyWatermark
import com.oplus.gallery.photo_page.viewmodel.funcRecommend.FuncItem.State.SUPPORTED
import com.oplus.gallery.photo_page.viewmodel.funcRecommend.FuncItem.State.UNDETECTED
import com.oplus.gallery.photo_page.viewmodel.funcRecommend.FuncItem.State.UNSUPPORTED
import com.oplus.gallery.standard_lib.app.AppScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

internal class PrivacyWatermarkFuncFactory(private val pageViewModel: PhotoViewModel) : AbsFuncFactory() {

    private val isSupportFunc = ConfigAbilityWrapper.getBoolean(ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_PRIVACY_WATERMARK)
    private val ability: IPrivacyWatermarkAbility? = pageViewModel.context.getAppAbility<IWatermarkAbility>()?.newPrivacyWatermarkAbility()

    override fun getFromCache(mediaItem: MediaItem): List<FuncItem> {
        if (isSupportFunc.not() || mediaItem.isSupport().not()) {
            return listOf(PrivacyWatermark(UNSUPPORTED))
        }

        val result = when (ability?.getPrivacyWatermarkEditableState(mediaItem)) {
            PrivacyWatermarkEditableState.UNKNOWN -> PrivacyWatermark(UNDETECTED)
            PrivacyWatermarkEditableState.EDITABLE -> PrivacyWatermark(SUPPORTED)
            else -> PrivacyWatermark(UNSUPPORTED)
        }

        // 从相机进入大图时，跳过扫描
        if ((result.state == UNDETECTED) && isFromCamera()) {
            result.state = UNSUPPORTED
            GLog.d(TAG, LogFlag.DL) { "getFromCache. Skip privacy watermark detect while from camera. id=${mediaItem.mediaId}" }
        }

        return listOf(result)
    }

    override fun onCreateFuncTask(mediaItem: MediaItem, bitmap: Bitmap): AbsFuncTask {
        return PrivacyWatermarkTask(mediaItem)
    }

    private fun MediaItem.isSupport(): Boolean {
        if (ConditionHelper.isCommonImage(this).not()) return false
        if (ImageTypeUtils.isRawFilePath(path)) {
            GLog.d(TAG, LogFlag.DL) { "isSupport raw file not supported" }
            return false
        }
        if (isDrm) {
            GLog.d(TAG, LogFlag.DL) { "isSupport, does not support privacy watermark editing because mediaItem is Drm" }
            return false
        }
        return true
    }

    /**
     * 是否从相机进入大图
     */
    private fun isFromCamera(): Boolean {
        return pageViewModel.inputArguments.features.value?.chainFrom == IntentConstant.ViewGalleryConstant.VALUE_CHAIN_FROM_CAMERA
    }

    private inner class PrivacyWatermarkTask(
        private val mediaItem: MediaItem
    ) : AbsFuncTask() {
        override val tag: String = "PrivacyWatermarkTask"

        override val funcItems: List<FuncItem> = listOf(PrivacyWatermark(UNDETECTED))

        override fun onDetect(): List<FuncItem> {
            val time = SystemClock.elapsedRealtime()
            var cardCaseSceneIdSet = CardCaseUtils.getAllCardCaseSceneIdSet()
            if (cardCaseSceneIdSet.isEmpty()) {
                // 如果随身卡包标签集为空，就触发加载词典，loadDictionary是synchronized方法，会等待结果
                LabelSearchEngine.getInstance().loadDictionary(pageViewModel.context, false)
                cardCaseSceneIdSet = CardCaseUtils.getAllCardCaseSceneIdSet()
            }
            val result = listOf(
                if (ability?.detectPrivacyWatermarkEditableState(mediaItem, cardCaseSceneIdSet) == PrivacyWatermarkEditableState.EDITABLE) {
                    PrivacyWatermark(SUPPORTED)
                } else {
                    PrivacyWatermark(UNSUPPORTED)
                }
            )
            GLog.d(tag, LogFlag.DL) { "onDetect cost:${SystemClock.elapsedRealtime() - time}" }
            return result
        }
    }

    override fun release() {
        AppScope.launch(Dispatchers.IO) {
            ability?.close()
        }
    }

    companion object {
        private const val TAG = "PrivacyWatermarkFuncFactory"
    }
}