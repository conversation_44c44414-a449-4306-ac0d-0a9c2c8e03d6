/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoInputArgumentsViewModel.kt
 ** Description : 大图页面输入参数管理器
 ** Version     : 1.0
 ** Date        : 2022/02/17
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2022/02/17  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.inputarguments

import android.annotation.SuppressLint
import android.app.Activity
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
import android.content.res.Resources
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.Rect
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import android.net.Uri
import android.os.Bundle
import android.os.IBinder
import android.util.Size
import android.view.Display
import android.view.Window
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.oplus.gallery.basebiz.constants.IntentConstant.AlbumConstant
import com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_EDIT_SKILL
import com.oplus.gallery.basebiz.constants.IntentConstant.LabelConstant
import com.oplus.gallery.basebiz.constants.IntentConstant.PicturePageConstant
import com.oplus.gallery.basebiz.constants.IntentConstant.PicturePageConstant.KEY_CURRENT_PAGE
import com.oplus.gallery.basebiz.constants.IntentConstant.PicturePageConstant.KEY_FROM_SELF_SPLIT
import com.oplus.gallery.basebiz.constants.IntentConstant.PicturePageConstant.KEY_SHOULD_DECODE_MICRO_PREVIEW_SHOT
import com.oplus.gallery.basebiz.constants.IntentConstant.PicturePageConstant.KEY_THUMB_SIZE_TYPE
import com.oplus.gallery.basebiz.constants.IntentConstant.SearchConstant.KEY_JUMP_SOURCE
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_CAMERA_SHOULD_WAIT_FOR_CAMERA_ANIMATION
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_CAMERA_TRANSITION_TYPE
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_CHAIN_FROM
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_ENABLE_PLAYBACK_PREVIEW
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_ENABLE_THUMBLINE_PREVIEW
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_FROM_CAMERA
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_FROM_FILE_MANAGER
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_FROM_FILE_MANAGER_RECENT
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_FROM_WIDGET_CARD
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_HAS_PRE_TRANSITION
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_INIT_SCREEN_ORIENTATION
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_IS_QUICK_ALREADY_ROTATED
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_MEDIA_ITEM_PATH
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_MEDIA_MENU_FLAG
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_MEDIA_SAVE_PATH
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_MEDIA_SET_PATH
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_NOT_DISPLAY_CSHOT_BTN
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_PHOTO_TYPE
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_PHOTO_TYPE_DEFAULT
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_SHOULD_WAIT_FOR_TRANSITION_FINISH
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.VALUE_CHAIN_FROM_CAMERA
import com.oplus.gallery.basebiz.constants.IntentConstant.WidgetConstant.KEY_WIDGET_MODE
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.helper.SlotOverlayHelper.SUPPORT_CROP_RECT_KEY
import com.oplus.gallery.basebiz.helper.SlotOverlayHelper.SUPPORT_THUMB_ROTATION
import com.oplus.gallery.basebiz.transition.InvokeFrom
import com.oplus.gallery.basebiz.transition.PhotoTransitionMakers
import com.oplus.gallery.basebiz.transition.Transition
import com.oplus.gallery.business.drawable.getBitmap
import com.oplus.gallery.business_lib.helper.PhotoDataHelper.isDragOutEnable
import com.oplus.gallery.business_lib.helper.PhotoDataHelper.isPreviewType
import com.oplus.gallery.business_lib.helper.PhotoDataHelper.isRotateEnable
import com.oplus.gallery.business_lib.helper.PhotoDataHelper.isSelectMultiType
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.getThumbnailRotation
import com.oplus.gallery.business_lib.model.data.base.item.toOriginalItem
import com.oplus.gallery.business_lib.model.data.base.source.DataManager
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants.Local
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants.Mtp
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants.Person
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants.PersonPet
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants.Pet
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants.Recycle
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants.Shared
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants.Uri.PATH_ALBUM_URI
import com.oplus.gallery.business_lib.model.data.base.utils.Constants
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_COPY
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_CUSTOM_REMOVE_WIDGET_LIST
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_EXPORT_VIDEO
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_INFO
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_MORE
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_MOVE
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_NONE
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_PREVIEW_CHECK
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_PREVIEW_SELECT
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_RECOMMENDED_REMOVE_WIDGET_LIST
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_REMOVE_LABEL
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_RENAME_FILE
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_SAFE_BOX
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_SAVE
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_SETAS_CONTACT
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_SETAS_WALLPAPER
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_SHARE
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_SLIDESHOW
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_TRANSFORM_HEIF_TO_JPEG
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_TRANSFORM_TO_SDR
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.SpecifiedAttributes.KEY_REQUIRE_SPECIFIED_ATTRS
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.SpecifiedAttributes.SPECIFIED_ATTR_FAST_CAPTURE
import com.oplus.gallery.business_lib.model.data.base.utils.MediaItemUtils
import com.oplus.gallery.business_lib.model.data.fastcapture.FastCaptureUtils
import com.oplus.gallery.business_lib.model.data.local.utils.LocalMediaDataHelper
import com.oplus.gallery.business_lib.model.selection.SelectionData
import com.oplus.gallery.business_lib.transition.InternalPageTransitionMaker
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.database.store.GalleryStore.WidgetSetColumns.MODE_CUSTOM
import com.oplus.gallery.foundation.database.store.GalleryStore.WidgetSetColumns.MODE_RECOMMENDED
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_RECOMMEND_PAGE_VALUE
import com.oplus.gallery.foundation.tracing.constant.LaunchExitPopupConstant.Value.ALBUM_MAIN_SET_PAGE
import com.oplus.gallery.foundation.tracing.constant.LaunchExitPopupConstant.Value.CUR_PAGE_LABEL_ALBUM
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.addDebugTextWithGain
import com.oplus.gallery.foundation.util.ext.getValidScreenOrientationOrDefault
import com.oplus.gallery.foundation.util.ext.hasGainmapCompat
import com.oplus.gallery.foundation.util.ext.isOnDefaultDisplay
import com.oplus.gallery.foundation.util.ext.setOrPostValue
import com.oplus.gallery.foundation.util.math.MathUtils
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils
import com.oplus.gallery.foundation.util.systemcore.IntentUtils
import com.oplus.gallery.foundation.util.systemcore.IntentUtils.NAVIGATE_UP_PACKAGE
import com.oplus.gallery.foundation.util.systemcore.IntentUtils.NAVIGATE_UP_TITLE_ID
import com.oplus.gallery.foundation.util.systemcore.IntentUtils.NAVIGATE_UP_TITLE_TEXT
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING
import com.oplus.gallery.framework.abilities.caching.CacheOperation
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_HDR
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_PHOTO_THUMB_LINE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_SAFE_BOX_ALBUM
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REGION_CN
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_SUPPORT_BRIGHTEN_UNIFORMITY
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_SUPPORT_BRIGHTNESS_ENHANCE
import com.oplus.gallery.framework.abilities.data.DataRepository
import com.oplus.gallery.framework.abilities.data.DataRepository.LocalAlbumModelGetter
import com.oplus.gallery.framework.abilities.data.ShareSession
import com.oplus.gallery.framework.abilities.ipc.IIPCAbility
import com.oplus.gallery.framework.abilities.resourcing.IResourcingAbility
import com.oplus.gallery.framework.abilities.resourcing.crop.CropParams
import com.oplus.gallery.framework.abilities.resourcing.key.ResourceKey
import com.oplus.gallery.framework.abilities.resourcing.key.ResourceKeyFactory
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceSaveOptions
import com.oplus.gallery.framework.abilities.resourcing.options.SourceOperation
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.ui.section.photomenu.MenuIcon
import com.oplus.gallery.photo_page.viewmodel.PhotoSubViewModel
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.contentloading.PhotoContentLoadingViewModel.SpecialThumbnailLoadRequester
import com.oplus.gallery.photo_page.viewmodel.dataloading.focusItemViewData
import com.oplus.gallery.photo_page.viewmodel.inputarguments.exit.PhotoExitLaunchRuleMaker
import com.oplus.gallery.photo_page.viewmodel.inputarguments.exit.rule.IPhotoLaunchPageRule
import com.oplus.gallery.photo_page.viewmodel.inputarguments.transition.DefaultPageTransitionMaker
import com.oplus.gallery.photo_page.viewmodel.inputarguments.transition.OplusCameraTransitionMaker
import com.oplus.gallery.photo_page.viewmodel.menucontrol.PhotoMenuPresent
import com.oplus.gallery.standard_lib.app.AppConstants
import com.oplus.gallery.standard_lib.app.AtFrontOfMainQueueDispatcher
import com.oplus.gallery.standard_lib.graphics.drawable.ThumbnailDrawable
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.system.measureTimeMillis

/**
 * 大图页面输入参数管理器
 */
internal class PhotoInputArgumentsViewModel(
    override val pageViewModel: PhotoViewModel,
) : PhotoSubViewModel(pageViewModel) {

    /**
     * [IResourcingAbility]配置能力的对象，避免频繁创建
     */
    private val resourcingAbility: IResourcingAbility? by lazy {
        pageViewModel.context.getAppAbility<IResourcingAbility>().apply {
            if (this == null) {
                GLog.i(TAG, LogFlag.DL) { "[resourcingAbility]init resourcingAbility failed , resourcingAbility is null" }
            }
        }
    }

    /////////////////// 以下为对外输出状态 //////////////////////

    /**
     * 调用来源
     */
    val invokeFrom: LiveData<InvokeFrom> get() = _invokeFrom
    private val _invokeFrom = MutableLiveData<InvokeFrom>()

    /**
     * 数据源
     */
    val dataSource: LiveData<DataSource> get() = _dataSource
    private val _dataSource = MutableLiveData<DataSource>()

    /**
     * 预览信息（目前仅有快拍），与大图预览的功能不同
     */
    val previewShot: LiveData<PreviewShot> get() = _previewShot
    private val _previewShot = MutableLiveData<PreviewShot>()

    /**
     * 指定的标题栏配置
     */
    val title: LiveData<Title> get() = _title
    private val _title = MutableLiveData<Title>()

    /**
     * 指定的菜单配置
     */
    val menu: LiveData<Menu> get() = _menu
    private val _menu = MutableLiveData<Menu>()

    /**
     * 指定的页面转场动画
     */
    val transition: LiveData<Transition> get() = _transition
    private val _transition = MutableLiveData<Transition>()

    /**
     * 指定的输出所需的信息
     */
    val outputTo: LiveData<OutputTo> get() = _outputTo
    private val _outputTo = MutableLiveData<OutputTo>()

    /**
     * 当前大图页是否是自分裂实例。
     */
    val isSelfSplittingInstance: LiveData<Boolean> get() = _isSelfSplittingInstance
    private val _isSelfSplittingInstance = MutableLiveData<Boolean>()

    /**
     * 大图退出时的行为定义
     */
    internal val exit: LiveData<Exit> get() = _exit
    private val _exit = MutableLiveData<Exit>()

    /**
     * 相册内部功能
     */
    val features: LiveData<Features> get() = _features
    private val _features = MutableLiveData<Features>()

    /**
     * 最近一次使用 [collect] 时，传入的 [Intent] 对象副本
     */
    private var inputIntent: Intent = emptyIntent()
        get() = Intent(field)

    /**
     * 最近一次使用 [collect] 时，传入的 [Intent] 对象的action
     */
    internal val inputIntentAction: String?
        get() = inputIntent.action

    /**
     * 最近一次使用 [collect] 时，传入的 [Bundle] 对象副本
     */
    internal var inputData: Bundle = emptyBundle()
        get() = Bundle(field)

    val menuPresent: LiveData<PhotoMenuPresent> get() = _menuPresent
    private val _menuPresent = MutableLiveData<PhotoMenuPresent>()

    /**
     * 相机intent传递过来的transitionType
     * mark: 临时方案，没有合适的地方存放该代码，因此临时放这里
     * 从intent中获取相机传递过来的 transitionType = 1 为强制加载quick动画
     * 该值只能用一次，用完后就要清除该值，否则会重复加数据。
     */
    internal val transitionType: LiveData<Int> get() = _transitionType
    private val _transitionType = MutableLiveData<Int>()

    /**
     * view display
     */
    internal val display: LiveData<Display> get() = _display
    private val _display = MutableLiveData<Display>()

    /**
     * activity window
     */
    internal val window: LiveData<Window> get() = _window
    private val _window = MutableLiveData<Window>()

    /////////////////// 以下为内部使用的属性 //////////////////////

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal val isKeyguardLocked: Boolean
        get() = pageViewModel.pageManagement.isPageDisplayedAboveKeyguardPage.value ?: false

    /////////////////// 以下为外部输入事件 //////////////////////

    /**
     * 通知期望跳转到指定位置，主要适用于同图集（本图集能查找到该 item）的情况，比如编辑、转码图片、视频
     *
     * *注意：*
     *
     * - 不能全部为无效值：[focusHint]、[itemPath]、[itemUri] 至少有一个有效，否则本次调用被忽略
     * - 生效优先级：[focusHint] > [itemPath] > [itemUri]
     *
     * @param focusHint 期望跳转到的位置，默认值为 [INVALID_INDEX]
     * @param itemPath 目标媒体文件路径，形式为 [Path.toString]
     * @param itemUri 目标媒体文件 [Uri] 路径，一般配合 [Intent.getData] 和 [Intent.getType] 使用
     * @param itemType 目标媒体文件 mime 类型，一般配合 [Intent.getData] 和 [Intent.getType] 使用
     * Marked by chenzengxin这个流程和 [updateSlotByNewDataSource] 是一样的，后续要把这块统一起来
     */
    fun notifyChangeFocusHint(
        focusHint: Int = INVALID_INDEX,
        itemPath: String? = null,
        itemUri: Uri? = null,
        itemType: String? = null
    ) {
        if ((focusHint < 0) && itemPath.isNullOrEmpty() && (itemUri == null)) {
            GLog.w(TAG) { "[notifyChangeFocusHint] At least one of focusHint、itemPath、itemUri is valid, check code" }
            return
        }

        val newIntent = inputIntent.apply {
            setDataAndType(itemUri, itemType)
        }
        val newData = inputData.apply {
            remove(PicturePageConstant.KEY_PLAYBACK_POSITION)
            putInt(PicturePageConstant.KEY_INDEX_HINT, focusHint)
            putString(ViewGalleryConstant.KEY_MEDIA_ITEM_PATH, itemPath)
        }

        inputIntent = newIntent
        inputData = newData

        val dataSource = collectForDataSource(newIntent, newData)

        // 预加载并存入 _previewShot ,方便大图页显示 Mask,需要在设置 _dataSource 前调用,因为大图的Mask需要用到此处的PreviewShot
        collectForPreviewShot(newIntent, dataSource, null)?.let(_previewShot::postValue)

        _dataSource.setOrPostValue(dataSource)
    }

    /**
     * 收集输入的参数，并转换为大图内部可识别的输入参数格式
     * @param intent 指定需要传递给大图页的intent
     * @param bundle 指定需要传递给大图页的bundle
     */
    @Suppress("LongMethod")
    fun collect(activity: Activity, intent: Intent = emptyIntent, bundle: Bundle = emptyBundle) {
        inputIntent = intent
        inputData = bundle
        /**
         * 此处调用一下contentLoading，是让其在主线程中先做初始化，否则在子线程执行到collectForQuickInfo方法会使用到contentLoading
         * 第一次使用会回调到contentLoading的onBind方法，执行observeForever会报在子线程注册监听异常
         */
        pageViewModel.contentLoading

        GLog.i(TAG, LogFlag.DL) { "[collect] intent=$intent, bundle=$bundle" }

        val feature = collectForFeatures(activity, intent, bundle).also { feature ->
            GLog.i(TAG, LogFlag.DL) { "[collect] feature=$feature" }
            _features.setOrPostValue(feature)
        }

        GLog.d(TAG, LogFlag.DL) { "[collect] display=${activity.display}, window=${activity.window}" }
        _display.setOrPostValue(activity.display)
        _window.setOrPostValue(activity.window)

        collectTransitionType().also { transitionType ->
            GLog.d(TAG, LogFlag.DL) { "[collect] transitionType=$transitionType" }
            _transitionType.setOrPostValue(transitionType)
        }

        collectForTitle(bundle).also { title ->
            GLog.d(TAG, LogFlag.DL) { "[collect] title=$title" }
            _title.setOrPostValue(title)
        }

        collectPhotoMenuPresent(bundle, feature.isShowedOnDefaultDisplay).also {
            GLog.d(TAG, LogFlag.DL) { "[collect] photoMenuPresent=$it" }
            _menuPresent.setOrPostValue(it)
        }

        val invokeFrom = collectForInvokeFrom(intent, bundle).also { invokeFrom ->
            GLog.d(TAG, LogFlag.DL) { "[collect] invokeFrom=$invokeFrom" }
            _invokeFrom.setOrPostValue(invokeFrom)
        }

        // 依赖于 invokeFrom
        collectForTransition(intent, bundle, invokeFrom).also { transition ->
            GLog.d(TAG, LogFlag.DL) { "[collect] transition=$transition" }
            _transition.setOrPostValue(transition)
        }

        val dataSource = collectForDataSource(intent, bundle).also { dataSource ->
            GLog.d(TAG, LogFlag.DL) { "[collect] dataSource=$dataSource" }
            _dataSource.setOrPostValue(dataSource)
        }

        launch { // Marked 内部可做并发优化
            GTrace.traceBegin("inputArgument.collect")

            val isOnlyUseCache = shouldOnlyUseCacheToCollectPreviewShot(bundle)
            // 依赖于 dataSource
            collectForPreviewShot(intent, dataSource, bundle, onlyUseCache = isOnlyUseCache)?.also { previewShot ->
                /**
                 * 在子线程中通知出去，会在大图首帧之后的协程消息中执行启动render动画，实际动画开始会在第二帧之后正式开始
                 * 首帧与第二帧之间有多个协程消息耗时较长，动画实际会被延后很多才正式开始
                 * 当前将大图启动动画，提前到Fragment被拉起后的首帧之前的协程消息启动，并在首帧就能正式开始
                 */
                launch(Dispatchers.AtFrontOfMainQueueDispatcher) {
                    GLog.i(TAG, LogFlag.DL) { "[collect] previewShot=$previewShot" }
                    _previewShot.setOrPostValue(previewShot)
                }
            }

            // 依赖于 feature
            collectForMenu(bundle, feature).also { menu ->
                GLog.d(TAG, LogFlag.DL) { "[collect] menu=$menu" }
                _menu.setOrPostValue(menu)
            }

            // 依赖于 dataSource
            collectForExit(dataSource, feature).also { exit ->
                GLog.d(TAG, LogFlag.DL) { "[collect] exit=$exit" }
                _exit.setOrPostValue(exit)
            }

            collectForOutputTo(bundle).also { outputTo ->
                GLog.d(TAG, LogFlag.DL) { "[collect] outputTo=$outputTo" }
                _outputTo.setOrPostValue(outputTo)
            }

            collectForSelfSplitting(bundle).also { isSelfSplittingInstance ->
                GLog.d(TAG, LogFlag.DL) { "[collect] isSelfSplittingInstance=$isSelfSplittingInstance" }
                _isSelfSplittingInstance.setOrPostValue(isSelfSplittingInstance)
            }
            GTrace.traceEnd()
        }
    }

    /**
     * 导出页面输入参数，并且将页面当前关键状态也给导出到 [outBundle]
     *
     * @param outBundle 导出页面输入参数的载体，导出的数据会存到该 [outBundle] 中
     */
    fun exportCurrentInputArgumentsStatus(
        intent: Intent? = null,
        outBundle: Bundle? = null
    ) {
        intent ?: outBundle ?: let {
            GLog.w(TAG) { "[exportCurrentInputArgumentsStatus] ignore, both intent and bundle are null!" }
            return
        }

        val focusPlaybackInfo = pageViewModel.playback.playbackInfo.value
        val focusPlaybackPosition =
            focusPlaybackInfo?.let { (it.duration * it.positionInPercent).toLong() } ?: INVALID_PLAYBACK_POSITION
        val focusViewData = pageViewModel.dataLoading.focusItemViewData
        val focusHint = focusViewData?.position ?: PhotoInputArgumentsViewModel.INVALID_INDEX
        val focusId = focusViewData?.id
        val focusUri = focusId?.let(Path::fromString)?.let(DataManager::getContentUri)
        val focusType = focusViewData?.mimeType

        intent?.apply {
            setDataAndType(focusUri, focusType)
        }

        outBundle?.apply {
            putAll(inputData)
            putString(KEY_MEDIA_ITEM_PATH, focusId ?: EMPTY_STRING)
            putInt(PicturePageConstant.KEY_INDEX_HINT, focusHint)
            putLong(PicturePageConstant.KEY_PLAYBACK_POSITION, focusPlaybackPosition)
        }
    }

    /**
     * 判断大图创建PreviewShot时用于判断是否仅使用磁盘缓存和内存缓存的flag
     *
     * 编辑回大图是不能只用缓存的，需要重新解码来创建PreviewShot
     *
     * 后续如果有其他场景进大图创建PreviewShot需要解码时，在这里修改
     *
     * 其他场景默认为true
     * */
    private fun shouldOnlyUseCacheToCollectPreviewShot(bundle: Bundle): Boolean {
        val isNeedDecodeMicroPreviewShot = (bundle.getBoolean(KEY_SHOULD_DECODE_MICRO_PREVIEW_SHOT)).also {
            GLog.d(TAG) { "[shouldOnlyUseCacheToCollectPreviewShot] isNeedDecodeMicroPreviewShot=$it" }
        }
        return isNeedDecodeMicroPreviewShot.not()
    }

    /**
     * 判断传入的新数据源和原来的数据源是否一致，将判断结果给到外部传入的 action，执行外部期望的更新逻辑。
     * @param intent 新数据源的载体，intent.data 是uri，intent.type 是mimeType
     * @param isUseNewDataSource 判断是否需要使用新的数据源
     * @param updateSlotAction 外部传入的更新逻辑，方法内部提供了以下参数：
     * 1.是否新数据源：isSameDataSource
     * 2.新数据源：targetItemPath 和 targetAlbumPath
     * Marked by chenzengxin后续替代 [notifyChangeFocusHint] 接口
     */
    fun updateSlotByNewDataSource(
        intent: Intent,
        isUseNewDataSource: Boolean? = null,
        updateSlotAction: ((data: Intent, isDifferenceDataSource: Boolean, targetItemPath: Path, targetAlbumPath: Path) -> Unit)
    ) {
        val uri = intent.data
        if (uri == null) {
            GLog.d(TAG, "updateSlotByNewDataSource, uri is null, return")
            return
        }
        // 获取原始图集 path
        val originSetPath = dataSource.value?.setPath
        if (originSetPath.isNullOrEmpty()) {
            GLog.d(TAG, "updateSlotByNewDataSource, originSetPath is null, return")
            return
        }
        val sourceAlbumPath = Path.fromString(originSetPath)

        val editSkill = intent.getStringExtra(KEY_EDIT_SKILL)

        // 获取目标图片 path
        /*
        首先从intent中获取，内部跳转回来都会有的，并且mediaItem已经缓存好了
         */
        val itemPathStr = intent.getStringExtra(KEY_MEDIA_ITEM_PATH)
        if (itemPathStr != null) {
            val itemPath = Path.fromString(itemPathStr)
            val targetAlbumPath = DataManager.getDefaultSetOf(itemPath)
            if (targetAlbumPath == null) {
                GLog.d(TAG, "updateSlotByNewDataSource, targetAlbumPath is null!")
                return
            }
            GLog.d(TAG) { "updateNewSlotForResult, sourceAlbumPath=$sourceAlbumPath, targetAlbumPath=$targetAlbumPath" }
            updateSlotAction.invoke(
                intent,
                DataSourceComparator().isUseNewDataSource(sourceAlbumPath, targetAlbumPath, editSkill),
                itemPath,
                targetAlbumPath
            )
            return
        }
        /*
        intent没有获取到，通过uri获取，会查询数据库
         */
        launch {
            GLog.d(TAG, "updateSlotByNewDataSource, get path by uri")
            val itemPath = DataManager.findPathByUri(uri, intent.type)
            if (itemPath == null) {
                GLog.d(TAG, "updateSlotByNewDataSource, path is null!")
                return@launch
            }
            val targetAlbumPath = DataManager.getDefaultSetOf(itemPath)
            if (targetAlbumPath == null) {
                GLog.d(TAG, "updateSlotByNewDataSource, targetAlbumPath is null!")
                return@launch
            }
            GLog.d(TAG) { "updateNewSlotForResult, sourceAlbumPath=$sourceAlbumPath, targetAlbumPath=$targetAlbumPath" }
            withContext(Dispatchers.Main) {
                updateSlotAction.invoke(
                    intent,
                    isUseNewDataSource ?: DataSourceComparator().isUseNewDataSource(sourceAlbumPath, targetAlbumPath, editSkill),
                    itemPath,
                    targetAlbumPath
                )
            }
        }
    }

    /**
     * 重置TransitionType
     * 由于该type是存在相机的intent中，因此使用后必须清除掉，否则相机进入相册后左右滑动会重复判断。
     */
    internal fun resetTransitionType() {
        if (_transitionType.value != 0) {
            GLog.d(TAG, "resetTransitionType")
            _transitionType.setOrPostValue(0)
            inputIntent.removeExtra(KEY_CAMERA_TRANSITION_TYPE)
        }
    }

    /////////////////// 以下为内部逻辑 //////////////////////

    /**
     * 不同启动场景配置一个[PhotoTransitionMaker]，
     * 由[PhotoTransitionMakers.choose]来选择转场动画制作器，
     * 由[PhotoTransitionMaker.make]来制作转场动画
     */
    private val transitionMakers: PhotoTransitionMakers by lazy {
        val defaultTransitionMaker = DefaultPageTransitionMaker(pageViewModel.viewModelApplication)
        PhotoTransitionMakers(defaultTransitionMaker).apply {
            registerTransitionMaker(OplusCameraTransitionMaker())
            registerTransitionMaker(InternalPageTransitionMaker(pageViewModel.viewModelApplication))
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    fun collectForInvokeFrom(intent: Intent, bundle: Bundle): InvokeFrom = InvokeFrom(
        callerPackage = findInvokePackage(),
        parentPage = findInvokeParentPage(intent, bundle),
        fromExternal = bundle.getBoolean(ViewGalleryConstant.KEY_EXTERNAL),
        fromSelfSplit = bundle.getBoolean(KEY_FROM_SELF_SPLIT, false)
    )

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    fun collectForDataSource(intent: Intent, bundle: Bundle): DataSource {
        val itemPath = parseItemPath(intent, bundle)
        val itemSetPath = bundle.getString(KEY_MEDIA_SET_PATH, EMPTY_STRING)
        val modelType = parseModelType(itemPath, itemSetPath, bundle)

        val indexHint = bundle.getInt(PicturePageConstant.KEY_INDEX_HINT, INVALID_INDEX)
        val sharedId = bundle.getString(PicturePageConstant.KEY_SHARED_ID) ?: EMPTY_STRING
        val playbackPosition = bundle.getLong(PicturePageConstant.KEY_PLAYBACK_POSITION, INVALID_PLAYBACK_POSITION)
        val shouldReverseDataOrder = shouldReverseDataOrder(bundle)
        val selectionDataId = bundle.getInt(ViewGalleryConstant.KEY_SELECTION_DATA_ID, SelectionData.INVALID_ID)

        val countLimit = bundle.getInt(ViewGalleryConstant.KEY_MAX_COUNT_LIMIT, Int.MAX_VALUE)
        val sizeLimit = bundle.getLong(ViewGalleryConstant.KEY_MAX_SIZE_LIMIT, Long.MAX_VALUE)
        val singleImageSizeLimit = bundle.getLong(ViewGalleryConstant.KEY_SINGLE_IMAGE_MAX_SIZE_LIMIT, Long.MAX_VALUE)
        val singleVideoSizeLimit = bundle.getLong(ViewGalleryConstant.KEY_SINGLE_VIDEO_MAX_SIZE_LIMIT, Long.MAX_VALUE)
        val selectionLimitConfig = SelectionLimitConfig(countLimit, sizeLimit, singleImageSizeLimit, singleVideoSizeLimit)
        return DataSource(
            modelType = modelType,
            setPath = itemSetPath,
            itemPath = itemPath,
            focus = indexHint,
            shareId = sharedId,
            playbackPosition = playbackPosition,
            shouldReverseDataOrder = shouldReverseDataOrder,
            selectionDataId = selectionDataId,
            selectionLimitConfig = selectionLimitConfig,
            extra = Bundle(bundle)
        )
    }

    private fun parseItemPath(intent: Intent, bundle: Bundle): String {
        // 1. 先从 bundle 数据集查询是否有 ViewGalleryConstant.KEY_MEDIA_ITEM_PATH，有则直接使用
        bundle.getString(ViewGalleryConstant.KEY_MEDIA_ITEM_PATH)
            ?.takeIf { it.isNotEmpty() }
            ?.let { return it }

        // 2. 如果 bundle 数据集没有指定路径，则尝试从 Intent.data 找到 uri，然后根据 uri 找到 itemPath
        val itemUri = intent.data ?: return EMPTY_STRING
        return DataManager.findPathByUri(itemUri, intent.type)?.toString() ?: EMPTY_STRING
    }

    private fun parseModelType(itemPath: String, itemSetPath: String, bundle: Bundle): String {
        // 1. 先从 bundle 数据集查询是否有 ViewGalleryConstant.KEY_MEDIA_MODEL_TYPE，有则直接使用
        bundle.getString(ViewGalleryConstant.KEY_MEDIA_MODEL_TYPE)
            ?.takeIf { modelType -> modelType.isNotEmpty() }
            ?.let { modelType -> return modelType }

        // 2. 如果 bundle 数据集没有指定 modelType，则需要自行推断
        val albumPath = if (itemSetPath.isNotEmpty()) {
            // 2.1 如果 itemSetPath 存在，使用 itemSetPath 得到 AlbumPath
            Path.fromString(itemSetPath)
        } else {
            // 2.2 如果 itemSetPath 不存在，则先使用 itemPath 得到媒体文件 Path，然后再从文件 Path 得到默认 AlbumPath
            DataManager.getDefaultSetOf(Path.fromString(itemPath))
        }

        // 3. 最后从 albumPath 里面推导出来 modelType，如果推导不出来，使用默认的 LocalAlbumModelGetter.TYPE_LOCAL_ALBUM 作为兜底
        return albumPath
            ?.let(DataManager::getMediaSet)
            ?.let(DataRepository::getModelType)
            ?.takeIf { modelType -> modelType.isNotEmpty() }
            ?: LocalAlbumModelGetter.TYPE_LOCAL_ALBUM
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun shouldReverseDataOrder(bundle: Bundle): Boolean {
        /*
         * Marked by zhangjisong 外界应该传入单独的、明确的值告诉大图需不需要配合外部正倒序，
         * 而不应该告诉一部分值，让大图内部自行推导，这属于把其他外部业务耦合起来
         * 目前做兼容处理，同旧大图逻辑，后续再整改
         * PositiveOrder 什么命名！！！
         *
         * 根据旧大图注释和逻辑转换为如下代码：
         * 1、如果不支持，数据源不需要反序显示
         * 2、目前人物详情是这个样式（不支持view层正倒序）
         */
        val mediaSetPath = bundle.getString(ViewGalleryConstant.KEY_MEDIA_SET_PATH, EMPTY_STRING)
        val isInvokeFromCamera = mediaSetPath.isCameraAlbum
        return when {
            mediaSetPath.isNullOrBlank() -> false
            isInvokeFromCamera -> pageViewModel.isPositiveOrderFromSetting()
            else -> false
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    fun collectForTitle(bundle: Bundle): Title = Title(
        resources = pageViewModel.viewModelApplication.resources,
        usePhotoPageToolbar = bundle.getBoolean(AlbumConstant.KEY_HIDE_INTERNAL_TOOLBAR, false).not(),
        navigationIcon = findNavigationRes(bundle),
        titleId = findTitleId(bundle),
        titleText = bundle.getString(NAVIGATE_UP_TITLE_TEXT, null),
        albumName = bundle.getString(LabelConstant.KEY_LABEL_NAME, EMPTY_STRING),
        useLightStatusBar = false
    )

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    fun collectForMenu(
        bundle: Bundle,
        feature: Features
    ): Menu = collectPhotoMenuPresent(bundle, feature.isShowedOnDefaultDisplay).let { present ->
        Menu(
            present = present,
            enabledActions = findExtraEnableActionsFor(bundle),
            disabledActions = findExtraDisableActionsFor(bundle, feature),
        )
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    fun collectForTransition(
        intent: Intent,
        bundle: Bundle,
        invokeFrom: InvokeFrom
    ): Transition = transitionMakers.choose(intent, bundle, invokeFrom).make(intent, bundle)

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    fun collectForOutputTo(bundle: Bundle): OutputTo = OutputTo(
        savePath = Uri.Builder().fragment(bundle.getString(KEY_MEDIA_SAVE_PATH, EMPTY_STRING)).build()
    )

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun collectForExit(dataSource: DataSource, feature: Features): Exit = Exit(
        isFinishSelf = findIsFinishSelf(dataSource, feature),
        launchRule = PhotoExitLaunchRuleMaker().makeExitLaunchRule(dataSource)
    )

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun collectForSelfSplitting(bundle: Bundle): Boolean {
        return bundle.getBoolean(KEY_FROM_SELF_SPLIT, false)
    }

    @Suppress("LongMethod")
    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    fun collectForFeatures(context: Context, intent: Intent, bundle: Bundle): Features {
        val setPath = bundle.getString(KEY_MEDIA_SET_PATH, EMPTY_STRING)
        val isInvokeFromCamera = setPath.isCameraAlbum

        var chainFrom: String? = null
        if (isInvokeFromCamera) {
            chainFrom = VALUE_CHAIN_FROM_CAMERA
        } else {
            chainFrom = bundle.getString(KEY_CHAIN_FROM, null)
        }
        val isInvokeFromKeyguard = setPath.isKeyguardCameraAlbum

        // 显示亮度相关配置
        val isBrightnessEnhanceEnabled = isSupportBrightnessEnhance()
        val brightnessMode = if (isBrightnessEnhanceEnabled) BrightnessMode.NORMAL else findBrightnessMode(setPath)
        val keepBrightnessWhenDestroy = if (isBrightnessEnhanceEnabled) false else isInvokeFromCamera

        // 屏幕转向相关配置，大图默认值使用 SCREEN_ORIENTATION_UNSPECIFIED
        val screenOrientation = bundle.getInt(KEY_INIT_SCREEN_ORIENTATION, SCREEN_ORIENTATION_UNSPECIFIED).getValidScreenOrientationOrDefault()

        // 相机进相册一体化 UI 动画相关配置
        val integrationUIGestureToken = IntentUtils.getBinderExtra(intent, KEY_INTEGRATION_UI_GESTURE_TOKEN)
        val hasIntegrationUITransition = integrationUIGestureToken != null
        val multiDisplayCooperationComponentName =
            if (hasIntegrationUITransition) IIPCAbility.ICallCamera.Companion.Other.CAMERA_INTEGRATION_UI_COOPERATION_COMPONENT else null

        // 相机进相册无缝动画相关配置
        val isSuggestingSeamlessTransition =
            (IntentUtils.getParcelableExtra<Rect>(intent, KEY_THUMBNAIL_RECT) != null) && isInvokeFromCamera
        val isSuggestingForSeamlessTransitionFinish =
            IntentUtils.getBooleanExtra(intent, KEY_CAMERA_SHOULD_WAIT_FOR_CAMERA_ANIMATION, false)
                    || IntentUtils.getBooleanExtra(intent, KEY_SHOULD_WAIT_FOR_TRANSITION_FINISH, false)
        val hasSeamlessTransition = hasIntegrationUITransition.not() && isSuggestingSeamlessTransition
        val shouldWaitForSeamlessTransitionFinish = hasSeamlessTransition && isSuggestingForSeamlessTransitionFinish

        // 其他杂项
        val isExternalAppDoEntranceAnimation = hasSeamlessTransition || hasIntegrationUITransition
        val isSwipeForbiddenWhenNotRender = hasSeamlessTransition || hasIntegrationUITransition
        val isHDRAnimationEnabled = hasSeamlessTransition || hasIntegrationUITransition
        val shouldWaitRenderReadyForEnterAnimation = hasSeamlessTransition.not() && hasIntegrationUITransition.not()

        /**
         * Activity 窗口是否透明相关配置
         * 无缝动画和一体化UI依赖相机传递此参数，支持无缝动画和一体化UI时此参数为false需要找相机确认
         */
        val isTransparentWindowEnabled = IntentUtils.getBooleanExtra(intent, KEY_IS_ACTIVITY_TRANSPARENT, false)

        val photoType = bundle.getInt(KEY_PHOTO_TYPE, KEY_PHOTO_TYPE_DEFAULT)
        val isPreview = isPreviewType(photoType)
        val isSupportDragOut = isDragOutEnable(bundle)
        val isSupportRotate = isRotateEnable(bundle)
        val shouldSetTaskDescriptionBgBlack =
            (IntentUtils.getIntExtra(intent, KEY_SHOULD_SET_TASK_DESCRIPTION, 0) == VALUE_SET_TASK_DESCRIPTION_BLACK_BACKGROUND)

        val isQuickAlreadyRotated = IntentUtils.getBooleanExtra(intent, KEY_IS_QUICK_ALREADY_ROTATED, true)

        val isInvokeFromExternal = bundle.getBoolean(ViewGalleryConstant.KEY_EXTERNAL)

        return Features(
            isSupportSlideShowPresent = IS_SLIDESHOW_SUPPORTED,
            isPlaybackPreviewEnabled = bundle.getBoolean(KEY_ENABLE_PLAYBACK_PREVIEW, false),
            isThumbLineEnabled = bundle.getBoolean(KEY_ENABLE_THUMBLINE_PREVIEW, true)
                    // mark@zhangwenming 临时方案，解决页面没有选缩图轴切片，但是isThumbLineEnabled为true导致启动后过了几秒黑图的bug，后续有更好的方案就替换这里
                    && isPreview.not()
                    && isSupportThumbLineInternal(context),
            isShowedOnDefaultDisplay = isShowedOnDefaultDisplay(context),
            isSupport10BitImagePresent = FeatureUtils.isSupport10Bit,
            isSupportHdrVideoPresent = ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_HDR),
            isSupportLensPresent = ConfigAbilityWrapper.getBoolean(IS_REGION_CN).not() && isInvokeFromCamera.not(),
            brightnessMode = brightnessMode,
            shouldKeepBrightnessWhenDestroy = keepBrightnessWhenDestroy,
            hasSeamlessTransition = hasSeamlessTransition,
            shouldWaitForSeamlessTransitionFinish = shouldWaitForSeamlessTransitionFinish,
            hasIntegrationUITransition = hasIntegrationUITransition,
            multiDisplayCooperationComponentName = multiDisplayCooperationComponentName,
            integrationUIGestureToken = integrationUIGestureToken,
            isTransparentWindowEnabled = isTransparentWindowEnabled,
            isDefaultImmersiveStyle = IntentUtils.getBooleanExtra(intent, KEY_IS_DEFAULT_IMMERSIVE_STYLE, false),
            shouldUnmute = isInvokeFromCamera,
            isSupportSafeBoxAlbum = ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_SAFE_BOX_ALBUM),
            screenOrientation = screenOrientation,
            isBrightnessEnhanceSupported = isBrightnessEnhanceEnabled,
            isSupportBrightenUniformity = isSupportBrightenUniformity(),
            shouldWaitRenderReadyForEnterAnimation = shouldWaitRenderReadyForEnterAnimation,
            isSwipeForbiddenWhenNotRender = isSwipeForbiddenWhenNotRender,
            isHDRAnimationEnabled = isHDRAnimationEnabled,
            isInvokeFromCameraForEdit = isInvokeFromCamera,
            isInvokeFromKeyguardCamera = isInvokeFromKeyguard,
            chainFrom = chainFrom,
            photoType = photoType,
            isSupportDragOut = isSupportDragOut,
            isSupportRotate = isSupportRotate,
            shouldSetTaskDescriptionBgBlack = shouldSetTaskDescriptionBgBlack,
            isExternalAppDoEntranceAnimation = isExternalAppDoEntranceAnimation,
            isQuickAlreadyRotated = isQuickAlreadyRotated,
            isInvokeFromExternal = isInvokeFromExternal,
            hasPreTransition = bundle.getBoolean(KEY_HAS_PRE_TRANSITION, false)
        )
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun findBrightnessMode(itemSetPath: String): BrightnessMode {
        return when {
            itemSetPath.isCameraAlbum -> BrightnessMode.HIGH
            else -> BrightnessMode.NORMAL
        }
    }

    private fun collectTransitionType(): Int {
        val transitionType = inputIntent.getIntExtra(KEY_CAMERA_TRANSITION_TYPE, 0)
        GLog.d(TAG, LogFlag.DL) { "collectTransitionType transitionType = $transitionType" }
        return transitionType
    }

    /**
     * 是否支持单独做提亮策略
     * 相机进入相册时，某些项目上需要相册单独做提亮策略，不需要保持跟相机预览界面一样的亮度显示
     */
    private fun isSupportBrightnessEnhance(): Boolean {
        return ConfigAbilityWrapper.getBoolean(IS_SUPPORT_BRIGHTNESS_ENHANCE).also { isEnabled ->
            GLog.d(TAG, LogFlag.DL) { "isSupportBrightnessEnhance: $isEnabled" }
        }
    }

    /**
     * 是否支持 进入相册亮度一致性
     * eg.相机进入相册时，如果支持，则只要不离开相册，相册内所有界面都应该保持与相机亮度一致
     */
    private fun isSupportBrightenUniformity(): Boolean {
        return ConfigAbilityWrapper.getBoolean(IS_SUPPORT_BRIGHTEN_UNIFORMITY).also { isEnabled ->
            GLog.d(TAG, LogFlag.DL) { "isSupportBrightenUniformity: $isEnabled" }
        }
    }

    /**
     *
     * 相册内部业务认定：是否可以显示缩图轴
     *
     * 条件1：当前设备是否支持
     * @see [ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_PHOTO_THUMB_LINE]  相关 isSupportPhotoThumbLine
     *
     * 条件2：当前显示屏为默认显示设备
     * @see [isOnDefaultDisplay]
     *
     * @param context 必须是activity的
     */
    private fun isSupportThumbLineInternal(context: Context): Boolean {
        val isDeviceSupportThumbLine = ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_PHOTO_THUMB_LINE)
        return isDeviceSupportThumbLine && context.isOnDefaultDisplay
    }

    /**
     *
     * 当前 [activity] 是否展示在默认 [Display.DEFAULT_DISPLAY] 上
     * 分为两种场景:
     * 场景一：当前不是多[android.view.Display] 设备
     * 场景二：当前是多 [android.view.Display] 设备 且 当前显示在默认显示设备
     * 条件1：当前是多 [android.view.Display] 设备
     * @see [ConfigID.Common.SystemInfo.IS_MULTI_DISPLA]
     *
     * 条件2：当前显示在默认显示设备
     * @see [isOnDefaultDisplay]
     *
     * @param context 必须是activity的
     */
    fun isShowedOnDefaultDisplay(context: Context): Boolean {
        val isMultiDisplayDevice = ConfigAbilityWrapper.getBoolean(ConfigID.Common.SystemInfo.IS_MULTI_DISPLAY)
        val isOnDefaultDisplay = context.isOnDefaultDisplay
        return isMultiDisplayDevice.not() || isOnDefaultDisplay
    }

    @Suppress("LongMethod")
    private fun collectForPreviewShot(intent: Intent, dataSource: DataSource, bundle: Bundle?, onlyUseCache: Boolean = false): PreviewShot? {
        val itemPath = dataSource.itemPath
        if (itemPath.isEmpty()) {
            GLog.w(TAG) { "[collectForPreviewShot] itemPath is empty, return null" }
            return null
        }

        val mediaPath = Path.fromString(itemPath)
        var mediaItem = (DataManager.getMediaObject(mediaPath) as? MediaItem)?.toOriginalItem()
        if ((mediaItem is LocalMediaItem) && !mediaItem.isLoaded) {
            mediaItem = LocalMediaDataHelper.getLocalMediaItem(mediaItem.path)
        }
        val mediaResKey = mediaItem?.let(ResourceKeyFactory::createResourceKey) ?: return null
        var saveTime: Long = 0
        var loadTime: Long = 0
        var drawableFromResource: ThumbnailDrawable? = null
        var inThumbnailType = ThumbnailSizeUtils.getFullThumbnailKey()
        var isHdrPhoto = false

        resourcingAbility.let {
            val isFromCamera = (KEY_FROM_CAMERA == invokeFrom.value?.parentPage)
            val saveOptions = ResourceSaveOptions(
                inCacheOperation = CacheOperation.WriteMemCache,
                inCropParams = CropParams.noCrop()
            ).apply {
                if (isFromCamera) {
                    thumbnailList = listOf(getThumbnailLongSizeMaxSize())
                }
            }
            val bitmap: Bitmap?
            saveTime = measureTimeMillis {
                // 尝试保存 Intent 里面的缩图，并且只保存到内存缓存中，具体逻辑交给 ResourcingAbility 去做
                bitmap = it?.saveThumbnail(mediaResKey, intent, saveOptions)
            }
            // 如果是相机进相册收集quick图较长边大小、mediaId等信息
            if ((bitmap != null) && isFromCamera) {
                val targetLongSize = getQuickFullThumbnailLongSize(bitmap)
                inThumbnailType = ThumbnailSizeUtils.getFullThumbnailKey(targetLongSize)
                collectForQuickInfo(targetLongSize, mediaItem)
            }
            loadTime = measureTimeMillis {
                drawableFromResource = loadPreviewShotThumbnail(it, mediaItem, mediaResKey, inThumbnailType, onlyUseCache, bundle)
                isHdrPhoto = drawableFromResource?.getBitmap()?.hasGainmapCompat() ?: false
            }
        }

        GLog.d(TAG, LogFlag.DL) {
            "[collectForPreviewShot] drawable=$drawableFromResource, saveTime=${saveTime}ms, loadTime=${loadTime}ms"
        }
        val previewShotDrawable = drawableFromResource ?: ColorDrawable(Color.TRANSPARENT).apply {
            bounds.set(0, 0, mediaItem.width, mediaItem.height)
            GLog.e(TAG, LogFlag.DL) { "[collectForPreviewShot] drawable is null, replace with ColorDrawable!" }
        }

        // 如果缩图的宽高信息无效，会导致动画rect参数异常。此处需要确保宽高信息正确。
        val previewShotSize = if ((previewShotDrawable.intrinsicWidth <= 0) || (previewShotDrawable.intrinsicHeight <= 0)) {
            if (AppConstants.Degree.isVertical(mediaItem.getThumbnailRotation())) {
                // 被90 or 270旋转过，需要倒置宽高
                Size(mediaItem.height, mediaItem.width)
            } else {
                Size(mediaItem.width, mediaItem.height)
            }
        } else {
            Size(previewShotDrawable.intrinsicWidth, previewShotDrawable.intrinsicHeight)
        }

        val isFastCapture: Boolean = FastCaptureUtils.isFastCaptureFile(mediaItem)
        val mediaAttrs = Bundle()
            .apply { putInt(KEY_REQUIRE_SPECIFIED_ATTRS, SPECIFIED_ATTR_FAST_CAPTURE) }
            .let(mediaItem::getSpecifiedAttributes)
        mediaAttrs.putInt(SUPPORT_THUMB_ROTATION, mediaItem.getThumbnailRotation())
        mediaItem.cropRectSet?.let { mediaAttrs.putString(SUPPORT_CROP_RECT_KEY, it) }

        return PreviewShot(
            mediaId = mediaItem.mediaId,
            size = previewShotSize,
            itemPath = itemPath,
            mimeType = mediaItem.mimeType ?: EMPTY_STRING,
            thumbnail = previewShotDrawable,
            isHdrPhoto = isHdrPhoto,
            isFastCapture = isFastCapture,
            extra = mediaAttrs
        )
    }

    /**
     * 先尝试获取大缩图 (TYPE_FULL_THUMBNAIL)，获取不到再获取小缩图 (TYPE_MICRO_THUMBNAIL)
     * @param onlyUseCache 是否以只读缓存的方式获取缩图
     */
    private fun loadPreviewShotThumbnail(
        resourcingAbility: IResourcingAbility?,
        mediaItem: MediaItem,
        mediaResKey: ResourceKey,
        thumbnailType: Int,
        onlyUseCache: Boolean,
        bundle: Bundle?,
    ): ThumbnailDrawable? {
        val loadOptions = ResourceGetOptions(
            inThumbnailType = thumbnailType,
            inCacheOperation = CacheOperation.ReadAllCache,
            inCropParams = CropParams.noCrop(),
        ).apply {
            // 当外部指定只读缓存或图片为超大图时，则设置ReadNone，只读缓存不解原图。
            inSourceOperation = if (onlyUseCache || MediaItemUtils.isLargeItem(mediaItem)) {
                SourceOperation.ReadNone
            } else {
                SourceOperation.ReadLocal   // 会先尝试读缓存（因为设置了inCacheOperation），缓存miss则会解原图
            }
        }

        GLog.d(TAG, LogFlag.DL) { "[loadPreviewShotThumbnail] previewShot load start, onlyUseCache=$onlyUseCache" }
        var requestedBitmap = GTrace.trace("LoadPreviewFullThumbnail") {
            resourcingAbility?.requestBitmap(mediaResKey, loadOptions)?.result
        }
        if (requestedBitmap == null) {
            GLog.d(TAG, LogFlag.DL) { "[loadPreviewShotThumbnail] get full thumbnail failed, try to get micro thumbnail." }
            loadOptions.apply {
                // 大图缓存拿不到，micro thumbnail如果缓存拿不到需要解码原图
                inSourceOperation = SourceOperation.ReadLocal
                // 如 bundle 中有传入缓存的 inThumbnailType，则直接使用，否则用默认实现
                inThumbnailType = bundle?.run {
                    getInt(KEY_THUMB_SIZE_TYPE, ThumbnailSizeUtils.getMicroThumbnailKey())
                } ?: ThumbnailSizeUtils.getMicroThumbnailKey()
                if (onlyUseCache.not()) {
                    // 没有小缩图缓存的情况下，如果没有允许低分辨率，heif图片请求小缩图解码耗时很长，所以需要允许低分辨率，减少耗时
                    minWidth = SMALL_IMAGE_SIZE
                    minHeight = SMALL_IMAGE_SIZE
                }
            }
            requestedBitmap = GTrace.trace("LoadPreviewMicroThumbnail") {
                resourcingAbility?.requestBitmap(mediaResKey, loadOptions)?.result
            }
        }
        requestedBitmap?.also {
            GLog.d(TAG, LogFlag.DL) { "[loadPreviewShotThumbnail] previewShot load success." }
        } ?: run {
            GLog.w(TAG, LogFlag.DL) { "[loadPreviewShotThumbnail] previewShot load failed." }
        }
        if (GProperty.DEBUG_PHOTO_PAGE_BITMAP_DEBUG_INFO) {
            requestedBitmap?.addDebugTextWithGain("PreviewSlot", MathUtils.ONE)
        }
        return requestedBitmap?.let { ThumbnailDrawable(it, mediaItem.getThumbnailRotation()) }
    }

    /**
     * 如果是从相机跳转相册，收集相机传过来的quick图信息
     *
     * @param targetLongSize 从intent中加载到的quick图较长边大小
     * @param mediaItem 相机进相册查看图片对应的mediaItem对象
     */
    private fun collectForQuickInfo(targetLongSize: Int, mediaItem: MediaItem) {
        GLog.d(TAG) {
            "[collectForQuickInfo] mediaId: ${mediaItem.mediaId}, targetLongSize: $targetLongSize"
        }
        pageViewModel.contentLoading.updateSpecialThumbnailLoadRequester(SpecialThumbnailLoadRequester(mediaItem.mediaId, targetLongSize))
    }

    data class PreviewShot(
        val mediaId: Int,
        val size: Size,
        val itemPath: String,
        val mimeType: String,
        val thumbnail: Drawable,
        val isHdrPhoto: Boolean,
        val isFastCapture: Boolean,
        val extra: Bundle
    )

    /**
     * 从[intent]和[bundle]查询出调用者的包名或者相册定义的虚拟调用者。
     * FIXME [1. 有什么用？用于埋点？]
     * FIXME [2. 是不是可以用“以什么意图进入相册”来取代？]
     */
    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    fun findInvokePackage(): String = EMPTY_STRING

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    fun findInvokeParentPage(intent: Intent, bundle: Bundle): String {
        /**
         * 增加intent.getStringExtra(KEY_CURRENT_PAGE)?: bundle.getString(ViewGalleryConstant.KEY_MEDIA_FROM)
         * 完善进入大图来源的埋点数据，包括图集、搜索、发现等场景
         */
        val fromPage = intent.getStringExtra(ViewGalleryConstant.KEY_MEDIA_FROM)
            ?: intent.getStringExtra(NAVIGATE_UP_PACKAGE) ?: intent.getStringExtra(KEY_CURRENT_PAGE)
            ?: bundle.getString(ViewGalleryConstant.KEY_MEDIA_FROM)
        GLog.d(TAG, LogFlag.DL) { "[findInvokeparentPage] fromPage=$fromPage" }
        return when {
            fromPage == ALBUM_MAIN_SET_PAGE -> ALBUM_MAIN_SET_PAGE
            fromPage == KEY_FROM_FILE_MANAGER -> KEY_FROM_FILE_MANAGER
            fromPage == KEY_FROM_FILE_MANAGER_RECENT -> KEY_FROM_FILE_MANAGER
            fromPage == KEY_FROM_WIDGET_CARD -> KEY_FROM_WIDGET_CARD
            fromPage == CUR_PAGE_LABEL_ALBUM -> CUR_PAGE_LABEL_ALBUM
            intent.hasExtra(KEY_JUMP_SOURCE) -> ALBUMS_ACTION_RECOMMEND_PAGE_VALUE
            else -> bundle.getString(KEY_CURRENT_PAGE, EMPTY_STRING)
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    fun findTitleId(bundle: Bundle): Int =
        (bundle[NAVIGATE_UP_TITLE_ID]
                // ?: bundle[NAVIGATE_MIDDLE_TITLE_ID] Marked by Charlie 字段找不到
                ).let {
                (it as? Int) ?: Resources.ID_NULL
            }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    fun findNavigationRes(bundle: Bundle): MenuIcon {
        val isFromSelfSplit = bundle.getBoolean(KEY_FROM_SELF_SPLIT, false)

        return when {
            // case 1: 来自自分屏时，返回按钮是个 X 。
            isFromSelfSplit -> MenuIcon.Static(com.oplus.gallery.basebiz.R.drawable.common_ic_cancel, R.drawable.photopage_ic_menu_cancel_light)

            // 默认使用 <- 箭头。
            else -> MenuIcon.Static(R.drawable.photopage_ic_menu_back_selector, R.drawable.photopage_ic_menu_back_selector_light)
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun collectPhotoMenuPresent(bundle: Bundle, isShowedOnDefaultDisplay: Boolean): PhotoMenuPresent {
        val itemSetPath = bundle.getString(KEY_MEDIA_SET_PATH, EMPTY_STRING)
        val isFileManagerRecentDataSet = bundle.getBoolean(KEY_FROM_FILE_MANAGER_RECENT, false)
        val isFromCshotFolder = bundle.getBoolean(KEY_NOT_DISPLAY_CSHOT_BTN, false)
        val canBeSave = (bundle.getInt(KEY_MEDIA_MENU_FLAG) and OPERATION_SUPPORT_SAVE.toInt()) != 0
        val canBeShare = (bundle.getInt(KEY_MEDIA_MENU_FLAG) and OPERATION_SUPPORT_SHARE.toInt()) != 0
        val isFormCamera = itemSetPath.isCameraAlbum
        val isPreview = isPreviewType(bundle)
        val isMulti = isSelectMultiType(bundle)
        return when {
            // 注意这里的顺序，如果先满足了，那么就会先返回，所以理论上来说存在menu的判断优先级
            isPreview && isMulti -> PhotoMenuPresent.ViewForPreviewCheck
            isPreview && isMulti.not() -> PhotoMenuPresent.ViewForPreviewSelect
            /**
             * 如果当前 [activity] 不在默认 [Display.DEFAULT_DISPLAY] 上显示
             * 返回外屏简版大图预设菜单
             */
            isFormCamera && isShowedOnDefaultDisplay.not() -> PhotoMenuPresent.ViewForConcisePhoto
            isFormCamera -> PhotoMenuPresent.ViewGallery
            itemSetPath.startsWith(PATH_ALBUM_URI.toString()) -> PhotoMenuPresent.None
            itemSetPath.startsWith(Recycle.PATH_ALBUM_ALL_ANY.toString()) -> PhotoMenuPresent.ViewRecycleContent
            itemSetPath.startsWith(Local.PATH_ALBUM_WIDGET_ANY.toString()) -> PhotoMenuPresent.ViewWidgetContent
            itemSetPath.startsWith(Shared.PATH_ALBUM_ALL_ANY.toString()) -> PhotoMenuPresent.ViewSharedAlbumContent
            itemSetPath.startsWith(Mtp.PATH_ALBUM_ALL_ANY.toString()) -> PhotoMenuPresent.ViewForImport
            isFileManagerRecentDataSet -> PhotoMenuPresent.ViewForOperation
            isFromCshotFolder -> PhotoMenuPresent.ViewCshotFolderContent
            itemSetPath.isEmpty() && (canBeSave || canBeShare) -> PhotoMenuPresent.ViewForSaveAndShare
            else -> PhotoMenuPresent.ViewContent
        }
    }


    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun findExtraEnableActionsFor(bundle: Bundle): LongArray {
        val mediaSetPath = bundle.getString(KEY_MEDIA_SET_PATH) ?: EMPTY_STRING
        val isInvokeFromKeyguardCamera = mediaSetPath.isKeyguardCameraAlbum
        val isInvokeFromMap = mediaSetPath.startsWith(Local.PATH_ALBUM_MAP_ADDRESS.toString())
        val isInvokeFromLabel = bundle.getBoolean(LabelConstant.KEY_FROM_LABEL)
        val isInvokeFromWidgetService = mediaSetPath.startsWith(Local.PATH_ALBUM_WIDGET_ANY.toString())
        val isInvokeFromMMS = (bundle.getInt(KEY_MEDIA_MENU_FLAG).toLong() != 0L) // 来自彩信
        val widgetMode = bundle.getInt(KEY_WIDGET_MODE)

        /**
         * 判断保存和分享功能是否打开
         */
        val enableSaveOrShare = fun(supports: MutableSet<Long>) {
            bundle.getInt(KEY_MEDIA_MENU_FLAG).toLong().let { extraMenuFlags ->
                if ((extraMenuFlags and OPERATION_SUPPORT_SHARE) != 0L) {
                    supports.add(OPERATION_SUPPORT_SHARE)
                }
                if ((extraMenuFlags and OPERATION_SUPPORT_SAVE) != 0L) {
                    supports.add(OPERATION_SUPPORT_SAVE)
                }
            }
        }
        val isPreview = isPreviewType(bundle)
        val isSelectMulti = isSelectMultiType(bundle)

        /**
         * 支持自分屏。
         * 条件： 来自相册内部页面 && 不是自分屏启动的大图页面
         */
        val shouldEnableSelfSplit = fun(block: () -> Unit) {
            val isInvokeFromSelfSplit = bundle.getBoolean(KEY_FROM_SELF_SPLIT, false)
            val isInvokeFromExternal = bundle.getBoolean(ViewGalleryConstant.KEY_EXTERNAL)
            if (isInvokeFromExternal.not() && isInvokeFromSelfSplit.not()) {
                block()
            }
        }

        return mutableSetOf(OPERATION_SUPPORT_NONE).also { enableActions ->
            when {
                isPreview && isSelectMulti -> enableActions.add(OPERATION_SUPPORT_PREVIEW_CHECK)

                isPreview && isSelectMulti.not() -> enableActions.add(OPERATION_SUPPORT_PREVIEW_SELECT)

                isInvokeFromKeyguardCamera -> {
                    enableActions.add(OPERATION_SUPPORT_INFO)
                    enableActions.add(OPERATION_SUPPORT_MORE)
                    enableActions.add(getOperationType(isInvokeFromWidgetService, widgetMode))
                }

                isInvokeFromMap -> {
                    enableActions.add(OPERATION_SUPPORT_MOVE)
                    enableActions.add(OPERATION_SUPPORT_COPY)
                    enableActions.add(OPERATION_SUPPORT_SAFE_BOX)
                    enableActions.add(OPERATION_SUPPORT_INFO)
                    enableActions.add(OPERATION_SUPPORT_MORE)
                    enableActions.add(getOperationType(isInvokeFromWidgetService, widgetMode))
                }

                isInvokeFromLabel -> {
                    enableActions.add(OPERATION_SUPPORT_MOVE)
                    enableActions.add(OPERATION_SUPPORT_COPY)
                    enableActions.add(OPERATION_SUPPORT_SAFE_BOX)
                    enableActions.add(OPERATION_SUPPORT_INFO)
                    enableActions.add(OPERATION_SUPPORT_MORE)
                    enableActions.add(OPERATION_SUPPORT_REMOVE_LABEL)
                    enableActions.add(getOperationType(isInvokeFromWidgetService, widgetMode))
                }

                isInvokeFromMMS -> enableSaveOrShare(enableActions)

                else -> {
                    enableActions.add(OPERATION_SUPPORT_MOVE)
                    enableActions.add(OPERATION_SUPPORT_COPY)
                    enableActions.add(OPERATION_SUPPORT_SAFE_BOX)
                    enableActions.add(OPERATION_SUPPORT_INFO)
                    enableActions.add(OPERATION_SUPPORT_MORE)
                    enableActions.add(getOperationType(isInvokeFromWidgetService, widgetMode))
                }
            }

            shouldEnableSelfSplit {
                enableActions.add(Constants.IMediaSupportOperations.OPERATION_SUPPORT_SELF_SPLIT)
            }
        }.toLongArray()
    }

    private fun getOperationType(isInvokeFromWidgetService: Boolean, widgetMode: Int): Long {
        return if (isInvokeFromWidgetService) {
            when (widgetMode) {
                MODE_CUSTOM -> OPERATION_SUPPORT_CUSTOM_REMOVE_WIDGET_LIST
                MODE_RECOMMENDED -> OPERATION_SUPPORT_RECOMMENDED_REMOVE_WIDGET_LIST
                else -> OPERATION_SUPPORT_NONE
            }
        } else {
            OPERATION_SUPPORT_NONE
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun findExtraDisableActionsFor(bundle: Bundle, feature: Features): LongArray {
        val mediaSetPath = bundle.getString(KEY_MEDIA_SET_PATH) ?: EMPTY_STRING
        val isInvokeFromKeyguardCamera = mediaSetPath.isKeyguardCameraAlbum
        val isInvokeFromMap = mediaSetPath.startsWith(Local.PATH_ALBUM_MAP_ADDRESS.toString())
        val isSupportSafeBoxAlbum = feature.isSupportSafeBoxAlbum
        val shouldDisableSlidingShow = fun(block: () -> Unit) {
            if (feature.isSupportSlideShowPresent.not()) {
                block()
            }
        }

        return mutableSetOf(OPERATION_SUPPORT_NONE).also { disableActions ->
            when {
                isInvokeFromKeyguardCamera -> {
                    disableActions.add(OPERATION_SUPPORT_MOVE)
                    disableActions.add(OPERATION_SUPPORT_COPY)
                    disableActions.add(OPERATION_SUPPORT_SETAS_WALLPAPER)
                    disableActions.add(OPERATION_SUPPORT_SETAS_CONTACT)
                    disableActions.add(OPERATION_SUPPORT_SAFE_BOX)
                    disableActions.add(OPERATION_SUPPORT_TRANSFORM_HEIF_TO_JPEG)
                    disableActions.add(OPERATION_SUPPORT_TRANSFORM_TO_SDR)
                    disableActions.add(OPERATION_SUPPORT_RENAME_FILE)
                    disableActions.add(OPERATION_SUPPORT_EXPORT_VIDEO)
                }

                isInvokeFromMap -> {
                    disableActions.add(OPERATION_SUPPORT_MOVE)
                    disableActions.add(OPERATION_SUPPORT_COPY)
                    disableActions.add(OPERATION_SUPPORT_SAFE_BOX)
                }

                isSupportSafeBoxAlbum.not() -> disableActions.add(OPERATION_SUPPORT_SAFE_BOX)

                else -> {
                    /*
                    do noting
                    */
                }
            }

            shouldDisableSlidingShow {
                disableActions.add(OPERATION_SUPPORT_SLIDESHOW)
            }
        }.toLongArray()
    }

    override fun onCleared() {
        super.onCleared()
        transitionMakers.clear()
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    fun findIsFinishSelf(dataSource: DataSource, feature: Features): Boolean {
        // 外部启动大图时，明确告知需要在退出时finishActivity，就需要结束自身
        val isForceFinishActivity = transition.value?.isForceFinishActivity ?: false
        val hasIntegrationUITransition = feature.hasIntegrationUITransition

        // case XX: 其他情况定义在此处

        return isForceFinishActivity && hasIntegrationUITransition.not()
    }

    /**
     * 获取相机进相册查看照片时缩图最大边长
     */
    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    fun getThumbnailLongSizeMaxSize(): Int {
        return THUMBNAIL_LONG_SIZE_MAX_LENGTH
    }

    /**
     * 获取bitmap较长边与Quick缩图加载时限制最大值中的较小者
     * @param bitmap 需要比较边长的图片
     */
    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    fun getQuickFullThumbnailLongSize(bitmap: Bitmap): Int {
        return getQuickFullThumbnailLongSize(bitmap.width.coerceAtLeast(bitmap.height))
    }

    /**
     * 获取传入targetSize与Quick缩图加载时限制最大值中的较小者
     * @param targetSize 图片较长边长大小
     */
    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    fun getQuickFullThumbnailLongSize(targetSize: Int): Int {
        return THUMBNAIL_LONG_SIZE_MAX_LENGTH.coerceAtMost(targetSize)
    }

    /////////////////////// 以下为内部类定义 ////////////////////////

    /**
     * 数据源
     */
    data class DataSource(
        /**
         * 数据源Model的类型。如果为空则“ TODO ”
         */
        val modelType: String = EMPTY_STRING,

        /**
         * 数据集的[com.oplus.gallery.business_lib.model.data.base.Path]。
         * 使用时应使用[com.oplus.gallery.business_lib.model.data.base.Path.toString()]将Path转为String类型
         */
        val setPath: String = EMPTY_STRING,

        /**
         * 需要显示为焦点数据的[com.oplus.gallery.business_lib.model.data.base.Path]。
         * 使用时应使用[com.oplus.gallery.business_lib.model.data.base.Path.toString()]将Path转为String类型
         */
        val itemPath: String = EMPTY_STRING,

        /**
         * 需要显示的焦点页，如果已经指定了[itemPath]，则优先以[itemPath]来确定大图
         * 显示的焦点页，如果[itemPath]无法确定出焦点的数据，则以[focus]来确定焦点页。
         * 默认为0。
         */
        val focus: Int = INVALID_INDEX,

        /**
         * 共享数据的 ID
         *
         * 通过该 ID 可找到前序页面传入的共享数据,刚进入大图时,直接使用该共享数据,为加快大图的数据加载速度
         * @see ShareSession
         */
        val shareId: String = EMPTY_STRING,

        /**
         * 页面跳转时，进入的时候直接进行播放的位置。
         *
         * 默认值是 [INVALID_PLAYBACK_POSITION]，代表无效，不播放
         */
        val playbackPosition: Long = INVALID_PLAYBACK_POSITION,

        /**
         * 是否启用反序显示输入的数据
         * > 注意：*是否支持反序，目前人物图集详情页不支持反序*
         */
        val shouldReverseDataOrder: Boolean = false,

        /**
         * 选择数据集的id，可以根据这个id创建选择的数据集合createSelectionData
         */
        val selectionDataId: Int = SelectionData.INVALID_ID,

        /**
         * 选择限制配置，包含数量，总大小，单个大小等
         */
        val selectionLimitConfig: SelectionLimitConfig? = null,

        /**
         * 定制化的更多数据，需要大图透传到下游组件的数据，但大图完全不关心得数据，可以放到这里。
         *
         * > 注意：*除了完全不可避免、历史遗留问题外，外部不允许使用该变量*
         */
        val extra: Bundle = Bundle()

    ) {
        /**
         * 数据源是否为一个集合：
         *  - [true]则说明输入的数据源为一个集合，大图可以在整个集合中浏览
         *  - [false]则说明数据源为一个单一数据
         */
        val isDataSet: Boolean = setPath.isNotEmpty()

        /**
         * 数据源来自于什么图集
         */
        val whichDataSet: DataSet = when {
            setPath.isCameraAlbum -> DataSet.CAMERA
            setPath.startsWith(Person.PATH_ALBUM_PERSON_ANY.toString()) -> DataSet.PERSON
            setPath.startsWith(Recycle.PATH_ALBUM_ALL_ANY.toString()) -> DataSet.RECYCLE
            setPath.startsWith(Local.PATH_ALBUM_WIDGET_ANY.toString()) -> DataSet.WIDGET
            setPath.startsWith(Local.PATH_ALBUM_MAP_ADDRESS.toString()) -> DataSet.MAP
            setPath.startsWith(Shared.PATH_ALBUM_ALL_ANY.toString()) -> DataSet.SHARED_ALBUM
            setPath.startsWith(Pet.PATH_ALBUM_PET_ANY.toString()) -> DataSet.PET
            setPath.startsWith(PersonPet.PATH_ALBUM_PERSON_PET_GROUP_ANY.toString()) -> DataSet.PERSON_PET_GROUP
            else -> DataSet.OTHER
        }

        /**
         * 数据源的类型，仅在[PhotoInputArgumentsViewModel.DataSource]中使用
         */
        enum class DataSet {
            CAMERA, PERSON, RECYCLE, MAP, WIDGET, OTHER, SHARED_ALBUM, PET, PERSON_PET_GROUP
        }
    }

    /**
     * 标题栏配置
     */
    data class Title(
        private val resources: Resources,
        /**
         * 是否使用大图页的Toolbar，如果为false，大图页不会显示Toolbar，
         * 此时页面的标题栏将由承载大图页的父Fragment或Activity指定
         */
        val usePhotoPageToolbar: Boolean = false,

        /**
         * 标题处导航图标资源
         */
        val navigationIcon: MenuIcon = MenuIcon.NULL,

        /**
         * 大图页标题栏的标题文本资源。
         */
        private val titleId: Int = Resources.ID_NULL,

        /**
         * 大图页标题栏的标题文本。
         * 如果[titleId]不为[Resources.ID_NULL]，则优先使用[titleId]
         */
        private val titleText: String? = null,

        /**
         * 进入大图的图集名字
         * 场景：智能场景进入大图时，会携带图集的名字，用于移除图片时的提示语。
         */
        val albumName: String = EMPTY_STRING,

        /**
         * 建议使用亮色主题状态栏，此时状态栏背景会使用接近#FFFFFF的高亮主题颜色
         * > 注意：*原始字段：statusBarTint*
         */
        val useLightStatusBar: Boolean = false
    ) {
        /**
         * 大图页标题栏。
         * - 不为空：显示为大图标题
         * - 为空：  大图执行加载标题（地点、时间等）
         * (关于变量命名，Kt的checkstyle规范，当为其他类型时，不能与类名相同)
         */
        val titleContent: String?
            get() = when {
                (titleId != Resources.ID_NULL) -> kotlin.runCatching { resources.getString(titleId) }.getOrDefault(titleText)
                else -> titleText
            }
    }

    /**
     * 菜单配置
     */
    data class Menu(
        /**
         * 预设的菜单项
         * *备注：似乎是多余的，菜单项会因为MediaItem的具体属性而改变*
         */
        val present: PhotoMenuPresent = PhotoMenuPresent.None,

        /**
         * 除预设中的菜单项外，还需要启用的菜单项
         */
        // Marked by Johnny 2022/6/15 for 李程里，这里的意思其实是support，改名为supportedActions
        val enabledActions: LongArray = longArrayOf(),

        /**
         * 在预设的菜单项中需要禁用的菜单项
         */
        // Marked by Johnny 2022/6/15 for 李程里，这里的意思其实是support，改名为unsupportedActions
        val disabledActions: LongArray = longArrayOf(),

        /**
         * 需要大图页菜单提供的功能点，可选功能有：
         *  -
         */
        val requestFeatures: LongArray = longArrayOf()
    ) {
        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (javaClass != other?.javaClass) return false

            other as Menu

            if (present != other.present) return false
            if (!enabledActions.contentEquals(other.enabledActions)) return false
            if (!disabledActions.contentEquals(other.disabledActions)) return false

            return true
        }

        override fun hashCode(): Int {
            var result = present.hashCode()
            result = 31 * result + enabledActions.contentHashCode()
            result = 31 * result + disabledActions.contentHashCode()
            return result
        }
    }

    /**
     * 大图功能项
     *
     * 这个主要是给VM使用，会比section更晚执行到
     */
    data class Features(
        /**
         * 是否支持幻灯片播放图片
         */
        val isSupportSlideShowPresent: Boolean = false,

        /**
         * 是否启用视频播控时的进度预览
         * 若为 false，大图页不启用视频播控进度预览，使用常规进度条
         *
         * markedBy linkailong 此值目前没有作用，与设计沟通大图目前都使用常规进度条
         */
        val isPlaybackPreviewEnabled: Boolean = false,

        /**
         * 是否支持10bit图片的呈现
         */
        val isSupport10BitImagePresent: Boolean = false,

        /**
         * 是否支持HDR视频的呈现
         */
        val isSupportHdrVideoPresent: Boolean = false,

        /**
         * 是否需要启用非静音状态
         * 产品需求: 相机进相册需要保存非静音
         */
        val shouldUnmute: Boolean = false,

        /**
         * 是否需要开启自动播放,默认是[null]，使用用户配置项
         * - true: 指定自动播放
         * - false： 指定不自动播放
         * - null： 不指定，大图内部获取用户设置。
         *
         * Marked: 目前外部没有业务指定该值，始终为 [null]
         */
        val shouldAutoPlay: Boolean? = null,

        /**
         * 是否需要启用Google Lens
         * 产品需求: 进入大图 是否显示Lens图标
         * - 不是中国地区
         * - 不是从相机进入
         */
        val isSupportLensPresent: Boolean = false,

        /**
         * 大图亮度模式
         */
        val brightnessMode: BrightnessMode = BrightnessMode.NORMAL,

        /**
         * 退出时是否需要保持当前屏幕亮度
         */
        val shouldKeepBrightnessWhenDestroy: Boolean = false,

        /**
         * 是否有无缝衔接过渡动作 .
         * 注意：目前跟相机强相关，因为目前Feature并不会清理（input内的入参是复制的），导致activity内的入参不会变化。
         * 如果要解耦，需要增加完整的入参清理流程。相关bug：#5653777
         */
        val hasSeamlessTransition: Boolean = false,

        /**
         * 是否需要等待无缝动画结束。
         * 相机进入相册的无缝动画由相机处理。由于性能需求，相机会提前启动相册，提前加载资源。
         * 所以相册需要等待相机过渡动画结束后，才显示相册，否则会出现跳闪问题。
         * 该值与AndroidManifest.xml内的 <meta-data android:name="isSupportEarlyStart" android:value="true" />是匹配的
         * 兼容性：
         * 无缝动画旧版本不会传递该值，所以无需等待，即准备好资源后，直接显示即可
         * 非无缝动画旧版本不会传递该值，走原有的相册动画，无变化。
         */
        val shouldWaitForSeamlessTransitionFinish: Boolean = false,

        /**
         * 是否有一体化 UI 动画，与 [hasSeamlessTransition] 互斥
         */
        val hasIntegrationUITransition: Boolean = false,

        /**
         * 一体化 UI 动画的触摸手势代理 token，可以通过该 token 将相册大图触摸手势转让给此 [integrationUIGestureToken]
         */
        val integrationUIGestureToken: IBinder? = null,

        val multiDisplayCooperationComponentName: ComponentName? = null,

        /**
         * 是否启用窗口透明的能力
         *
         * @see android.app.Activity.setTranslucent
         */
        val isTransparentWindowEnabled: Boolean = false,

        /**
         * 是否默认进入沉浸式样式
         */
        val isDefaultImmersiveStyle: Boolean = false,

        /**
         * 是否支持私密相册
         */
        @SuppressLint("SupportAnnotationUsage")
        @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
        val isSupportSafeBoxAlbum: Boolean = true,

        /**
         * 页面的初始化Screen orientation
         * 可取值为:[android.content.pm.ActivityInfo.ScreenOrientation]中定义的常量
         */
        val screenOrientation: Int? = null,

        /**
         * 是否支持单独做提亮策略
         */
        val isBrightnessEnhanceSupported: Boolean = false,

        /**
         * 是否支持 进入相册亮度一致性
         */
        val isSupportBrightenUniformity: Boolean = false,

        /**
         * 是否启用大图缩图轴：显示true , 不显示 false
         * - 1.当前机型支持显示缩图轴，详见[isSupportThumbLineInternal]
         * - 2.外部输入参数需要显示缩图轴，默认值true
         */
        val isThumbLineEnabled: Boolean = true,

        /**
         * 当前 [activity] 是否显示在默认 [Display.DEFAULT_DISPLAY] 上
         * - 1.当前是多 [android.view.Display] 设备
         * - 2.当前显示在默认显示设备
         * 详见[isShowedOnDefaultDisplay]
         */
        val isShowedOnDefaultDisplay: Boolean = true,

        /**
         * 进入动画时是否需要等待图片缩图、高清图之一渲染就绪，默认为 true。
         *
         * 如果为 true，则只有图片缩图、高清图之一渲染就绪后，进入动画才会允许执行动画完成回调。
         *
         * 目前主要用于相机进相册的无缝动画和一体化 UI 动画场景。
         */
        val shouldWaitRenderReadyForEnterAnimation: Boolean = true,

        /**
         * 当大图焦点位置缩图、高清图都没有渲染就绪时，是否禁止滑动大图切页，默认是 false。
         *
         * 目前主要用于相机进相册的无缝动画和一体化 UI 动画场景。
         */
        val isSwipeForbiddenWhenNotRender: Boolean = false,

        /**
         * 是否允许执行 HDR 提亮动画
         *
         * 目前主要用于相机进相册的无缝动画和一体化 UI 动画场景。
         */
        val isHDRAnimationEnabled: Boolean = true,

        /**
         * 是否直接从相机进入相册大图，再进入编辑
         */
        val isInvokeFromCameraForEdit: Boolean = false,

        /**
         * 是否从锁屏拍照再进入相册大图
         */
        val isInvokeFromKeyguardCamera: Boolean = false,

        /**
         * 来自哪条进相册的链路
         */
        val chainFrom: String? = null,

        /**
         * 大图页面的类型，用来总体区分页面的功能，当前的取值有：
         * [com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_PHOTO_TYPE_DEFAULT]
         * 默认值，0，表示大图的常用功能，各种编辑，效果等
         * [com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_PHOTO_TYPE_PREVIEW]
         * 大图预览，1，表示在选择模式下，点击图片以预览的形式查看，只有简单的效果展示和选中的功能
         */
        val photoType: Int = KEY_PHOTO_TYPE_DEFAULT,

        /**
         * 是否支持长按拖拽到其他应用，比如传送坞、便签等
         */
        val isSupportDragOut: Boolean = true,

        /**
         * 是否支持旋转手势
         */
        val isSupportRotate: Boolean = true,

        /**
         * 是否需要将Activit.TaskDescription设置背景色为默认黑色
         * 该参数由相机指定，规则大致是不支持一体化UI的OS14.1及以后得机器，都会配置为true。
         */
        val shouldSetTaskDescriptionBgBlack: Boolean = false,

        /**
         * 是否为外部做的入场动画，比如无缝动画版本或相机相册一体化版本是相机做的动画
         */
        val isExternalAppDoEntranceAnimation: Boolean = false,

        /**
         * 判定当前相机给定的quick图是否已经做过角度旋转了
         * false：表示quick图没有叠加任何角度，与final图直接解码出来的方向是一致的
         * true: 表示quick图已经叠加了角度。
         */
        val isQuickAlreadyRotated: Boolean = false,

        /**
         * 是否为外部进入大图页
         */
        val isInvokeFromExternal: Boolean = false,

        /**
         * 是否使用的Pre Transition动画
         */
        val hasPreTransition: Boolean = false
    )

    /**
     * 页面输出
     */
    data class OutputTo(
        /**
         * 保存图片时的存储位置。
         */
        val savePath: Uri
    )

    /**
     * 大图退出时的行为
     */
    internal data class Exit(
        /**
         * 退出时是否Finish自身（Activity）
         */
        val isFinishSelf: Boolean,
        /**
         * 退出大图时启动其它页面的规则。
         */
        val launchRule: IPhotoLaunchPageRule
    )

    /**
     * 选择限制条件集合
     * 包含总数量，总大小，单个大小等
     */
    internal data class SelectionLimitConfig(
        /**
         * 选择item时的最大数量限制
         */
        val selectCountLimit: Int = Int.MAX_VALUE,

        /**
         * 选择item时的总文件大小限制
         */
        val selectSizeLimit: Long = Long.MAX_VALUE,

        /**
         * 选择单个图片时的文件大小限制
         */
        val selectSingleImageSizeLimit: Long = Long.MAX_VALUE,

        /**
         * 选择单个视频时的文件大小限制
         */
        val selectSingleVideoSizeLimit: Long = Long.MAX_VALUE
    )

    /**
     * 关闭Ability
     */
    private fun closeAbility() {
        resourcingAbility?.close()
    }

    override fun onDestroy() {
        super.onDestroy()
        //关闭 Ability
        closeAbility()
    }

    companion object {
        private const val TAG = "PhotoInputArgumentsViewModel"

        private val PATH_EXT_LOCK_CAMERA_ALBUM = Local.PATH_ALBUM_EXT_LOCK_CAMERA.toString()
        private val PATH_EXT_UNLOCK_CAMERA_ALBUM = Local.PATH_ALBUM_EXT_UNLOCK_CAMERA.toString()

        /**
         * 相机进相册时使用的缩图最大边长
         * 相机传给相册quick图，正常不带画框水印大小为960x1280，带画框/哈苏水印大小是960x1417
         * 相机进相册预览图片时，加载预览图缩图为了跟相机无缝动画使用预览图画质保持一致，设置较长边最大值为1417
         * 如果相机传给相册quick图最长边超过1417，则会以1417为基准进行缩放
         */
        private const val THUMBNAIL_LONG_SIZE_MAX_LENGTH = 1417

        /**
         * 相机进入大图时，是否启动沉浸式界面
         * 目前此参数为相机所用
         */
        private const val KEY_IS_DEFAULT_IMMERSIVE_STYLE = "isDefaultImmersiveMode"

        /**
         * 一体化 UI 动画的触摸手势代理 token 的 key 值，目前此参数为相机所用
         * 此参数在 FastCaptureManager 里也有定义，如修改需同步修改
         * @see Features.integrationUIGestureToken
         */
        internal const val KEY_INTEGRATION_UI_GESTURE_TOKEN = "com.oplus.camera.inputtoken"

        /**
         * 指定大图反回相机动画中，缩略图的坐标。目前此参数为相机所用
         *
         * @see [Transition.remoteSourceBounds]
         */
        internal const val KEY_THUMBNAIL_RECT = "ThumbnailRect"

        /**
         * 启动大图时，是否期望大图的窗口 [android.app.Activity] 为透明背景
         */
        internal const val KEY_IS_ACTIVITY_TRANSPARENT = ViewGalleryConstant.KEY_IS_ACTIVITY_TRANSPARENT

        /**
         * 启动大图时，期望大图将TaskDescription的背景设置为黑色
         */
        internal const val KEY_SHOULD_SET_TASK_DESCRIPTION = "need_set_task_description"
        internal const val VALUE_SET_TASK_DESCRIPTION_BLACK_BACKGROUND = 1

        internal const val INVALID_PLAYBACK_POSITION: Long = -1

        internal const val INVALID_INDEX: Int = -1

        /**
         * heif图片没有小缩图缓存的情况下，请求小缩图解码耗时很长，
         * 允许低分辨率 (minWidth/minHeight = 100)，则会尝试从exif中加载缩图，可有效减少耗时
         */
        private const val SMALL_IMAGE_SIZE = 100

        /**
         * 内部使用的空[Intent]
         */
        internal val emptyIntent = Intent()

        /**
         * 内部使用的空[Bundle]
         */
        internal val emptyBundle = Bundle()

        /**
         * 大图是否支持幻灯片播放。支持时会有对应菜单项。
         */
        private val IS_SLIDESHOW_SUPPORTED = GProperty.DEBUG_PHOTO_SLIDESHOW

        private val String.isCameraAlbum: Boolean
            get() = isKeyguardCameraAlbum || startsWith(PATH_EXT_UNLOCK_CAMERA_ALBUM)

        private val String.isKeyguardCameraAlbum: Boolean
            get() = startsWith(PATH_EXT_LOCK_CAMERA_ALBUM)
    }

    /**
     * 亮度模式
     * HIGH：保持与相机一致
     * NORMAL：正常亮度模式
     */
    enum class BrightnessMode {
        NORMAL, HIGH
    }
}

/**
 * 空[Intent]类型参数，可使用此函数返回的对象做空[Intent]判断。
 * 切勿自行```new Intent()```来作为空[Intent]使用，会无法进行空判断。
 */
fun emptyIntent(): Intent = PhotoInputArgumentsViewModel.emptyIntent

/**
 * 空[Bundle]类型参数，可使用此函数返回的对象做空[Bundle]判断。
 * 切勿自行```new Bundle()```来作为空[Bundle]使用，会无法进行空判断。
 */
fun emptyBundle(): Bundle = PhotoInputArgumentsViewModel.emptyBundle
