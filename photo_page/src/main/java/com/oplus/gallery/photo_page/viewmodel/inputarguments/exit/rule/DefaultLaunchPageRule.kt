/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : DefaultLaunchPageRule.kt
 ** Description : 大图页退出时默认的跳转规则
 ** Version     : 1.0
 ** Date        : 2022/06/15
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2022/06/15  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.inputarguments.exit.rule

import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.inputarguments.exit.FragmentLaunchArguments

/**
 * 大图退出时使用的默认的跳转规则。
 */
internal class DefaultLaunchPageRule : IPhotoLaunchPageRule {

    override fun isShouldLaunchPage(
        viewModel: PhotoViewModel
    ): Boolean = false

    override fun getLaunchArguments(
        viewModel: PhotoViewModel
    ): FragmentLaunchArguments = FragmentLaunchArguments.EMPTY
}