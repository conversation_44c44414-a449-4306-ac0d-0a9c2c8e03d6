/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : IPhotoExitLaunchRule.kt
 ** Description : 大图页退出规则
 ** Version     : 1.0
 ** Date        : 2022/06/15
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2022/06/15  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.inputarguments.exit.rule

import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.inputarguments.exit.PhotoLaunchArguments

/**
 * 退出大图页时，启动新页面的的规则。
 * - 退出时启动其它页面的模式
 * - 退出前需要的行为
 * - 退出后需要的行为
 */
internal interface IPhotoLaunchPageRule {

    /**
     * 是否启动其它页面。
     */
    fun isShouldLaunchPage(viewModel: PhotoViewModel): Boolean

    /**
     * 获取启动页面使用的参数
     */
    fun getLaunchArguments(viewModel: PhotoViewModel): PhotoLaunchArguments
}


