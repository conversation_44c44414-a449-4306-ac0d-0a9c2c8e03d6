/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoDetailsCardViewManager.kt
 ** Description : 大图内容详情信息卡片展示管理类
 ** Version     : 1.0
 ** Date        : 2025/5/22
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>                  2025/5/22       1.0     create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.ui.section.details

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.res.Configuration
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.UiThread
import androidx.core.content.ContextCompat
import androidx.core.view.isGone
import androidx.core.view.isNotEmpty
import androidx.core.view.isVisible
import androidx.lifecycle.coroutineScope
import com.oplus.gallery.basebiz.constants.IntentConstant
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.foundation.arch.sectionpage.ISectionPage
import com.oplus.gallery.foundation.uikit.broadcast.bus.BroadcastDispatcher
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.runOnUiThread
import com.oplus.gallery.foundation.util.ext.safeStartActivity
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.foundation.util.systemcore.KeyguardManagerUtils
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.ui.PhotoFragment
import com.oplus.gallery.photo_page.ui.section.details.collaborator.contract.DetailPanelContentContract.Companion.ENTRANCE_ATTACH
import com.oplus.gallery.photo_page.ui.section.details.collaborator.contract.DetailPanelContentContract.Companion.ENTRANCE_DETATCH
import com.oplus.gallery.photo_page.ui.section.details.collaborator.contract.DetailPanelContentContract.Companion.ENTRANCE_ON_FOCUS_CHANGE
import com.oplus.gallery.photo_page.ui.section.details.widget.PanelDetailsView
import com.oplus.gallery.photo_page.ui.section.details.widget.PhotoDetailsAlbumSetCardView
import com.oplus.gallery.photo_page.ui.section.details.widget.PhotoDetailsBaseCardView
import com.oplus.gallery.photo_page.ui.section.details.widget.PhotoDetailsDocumentCardView
import com.oplus.gallery.photo_page.ui.section.details.widget.PhotoDetailsImageParamCardView
import com.oplus.gallery.photo_page.ui.section.details.widget.PhotoDetailsLensCardView
import com.oplus.gallery.photo_page.ui.section.details.widget.PhotoDetailsTimeCardView
import com.oplus.gallery.photo_page.ui.section.details.widget.PhotoDetailsVideoParamCardView
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.dataloading.focusSlot
import com.oplus.gallery.photo_page.viewmodel.details.PhotoDetails
import com.oplus.gallery.photo_page.viewmodel.details.PhotoDetailsConstants
import com.oplus.gallery.router_lib.Starter
import com.oplus.gallery.router_lib.meta.PostCard
import okio.IOException
import java.nio.file.Files
import java.nio.file.Paths
import kotlin.math.abs

/**
 * 大图内容详情信息卡片展示管理类
 */
internal class PhotoDetailsCardViewManager(private val sectionPage: ISectionPage<PhotoFragment, PhotoViewModel>) {
    /**
     * 基础信息卡
     */
    private var baseInfoCardView: PhotoDetailsTimeCardView? = null

    /**
     * 拍摄参数卡
     */
    private var exifInfoCardView: PhotoDetailsLensCardView? = null

    /**
     * 拍摄iso等信息卡
     */
    private var exifIsoCardView: FrameLayout? = null

    /**
     * 拍摄iso等信息卡--图片参数
     */
    private var exifImageCardView: PhotoDetailsImageParamCardView? = null

    /**
     * 拍摄iso等信息卡--视频参数
     */
    private var exifVideoCardView: PhotoDetailsVideoParamCardView? = null

    /**
     * sd存储卡
     */
    private var storageCardView: PhotoDetailsDocumentCardView? = null

    /**
     * 图集上方的横线
     */
    private var storageDiverLine: PanelDetailsView? = null

    /**
     * 图集
     */
    private var albumSetCardView: PhotoDetailsAlbumSetCardView? = null

    /**
     * 上次更新页面时的主题色
     */
    private var lastUiMode: Int? = null

    /**
     * 详情数据
     */
    private var photoDetails: PhotoDetails? = null

    /**
     * 监听解锁锁屏BroadcastReceiver
     */
    private var unlockReceiver: BroadcastReceiver? = null

    /**
     * 上次监听解锁锁屏后的操作类型
     */
    private var receiverType: String? = null

    /**
     * 记录上一次当前在地图的window中的状态
     */
    private var lastInMapWindow: Boolean = false

    /**
     * 是否锁屏拍照，相机进入相册大图
     */
    private val isInvokeFromKeyguardCamera
        get() = sectionPage.pageViewModel.inputArguments.features.value?.isInvokeFromKeyguardCamera == true

    /**
     * 初始化view
     */
    internal fun initViewObjects(view: View) {
        //基础信息
        baseInfoCardView = view.findViewById(R.id.base_info)
        //拍摄参数
        exifInfoCardView = view.findViewById(R.id.exif_info)
        //拍摄iso等信息，图片
        exifIsoCardView = view.findViewById(R.id.exif_iso)
        //拍摄iso等信息，图片
        exifImageCardView = view.findViewById(R.id.exif_image)
        //拍摄iso等信息，视频
        exifVideoCardView = view.findViewById(R.id.exif_video)
        //sd存储
        storageCardView = view.findViewById(R.id.storage)
        storageCardView?.onCardListener = object : PhotoDetailsBaseCardView.CardClickListener {
            override fun onClick() {
                // 1. 是否从锁屏相机进入相册
                if (!isInvokeFromKeyguardCamera) {
                    // 1.1 未锁屏情况下直接跳转到文管
                    jumpToFileManager()
                    return
                }

                // 2. 检查锁屏状态
                if (!KeyguardManagerUtils.isKeyguardLocked()) {
                    // 2.1 未锁屏情况下直接跳转到文管
                    jumpToFileManager()
                    return
                }

                // 3.1 锁屏状态下触发解锁流程，先注册监听
                registerUnlockReceiver()
                // 3.2 设置当前广播类型
                receiverType = JUMP_TO_FILE_MANAGER_RECEIVER_TYPE
                // 3.3 开启锁屏页面
                KeyguardManagerUtils.startScreenUnlockView()
            }
        }
        //图集
        albumSetCardView = view.findViewById(R.id.album_set)
        albumSetCardView?.onCardListener = object : PhotoDetailsBaseCardView.CardClickListener {
            override fun onClick() {
                // 1. 是否从锁屏相机进入相册
                if (!isInvokeFromKeyguardCamera) {
                    // 1.1 未锁屏情况下直接跳转到文管
                    startCommonAlbum()
                    return
                }

                // 2. 检查锁屏状态
                if (!KeyguardManagerUtils.isKeyguardLocked()) {
                    // 2.1 未锁屏情况下直接跳转到图集
                    startCommonAlbum()
                    return
                }

                // 3.1 锁屏状态下触发解锁流程，先注册监听
                registerUnlockReceiver()
                // 3.2 设置当前广播类型
                receiverType = START_ALBUM_SET_RECEIVER_TYPE
                // 3.3 开启锁屏页面
                KeyguardManagerUtils.startScreenUnlockView()
            }
        }
        storageDiverLine = view.findViewById(R.id.storage_diver_line)
        //注册所在fragment的lifecycle
        baseInfoCardView?.registerLifecycle(sectionPage.pageLifecycle)
        adapterViewObjects(view.context)
    }

    /**
     * 更新view
     * 由于可能存在在异步线程内获取完数据就回调数据刷新的方法，因此需要runOnUiThread保证页面在主线程刷新
     * 以下刷新动作中，不能有耗时操作，仅将数据按要求展示
     */
    @UiThread
    internal fun updateViewObjects(photoDetails: PhotoDetails) {
        this.photoDetails = photoDetails
        //基础信息
        baseInfoCardView?.updateData(photoDetails)
        //获取面板结构配置
        val panelStructure = getPanelStructure(sectionPage.pageInstance.getCurrentAppUiConfig())
        //如果是双列结构就不展示分割线，非双列结构展示分割线
        baseInfoCardView?.setViewLineVisible(panelStructure != PanelStructure.VERTICAL_SLIDE_UP_DOUBLE)
        //拍摄参数
        exifInfoCardView?.updateData(photoDetails)
        //展示图片参数
        exifImageCardView?.isVisible = !photoDetails.isVideo()
        //展示视频参数
        exifVideoCardView?.isVisible = photoDetails.isVideo()
        //获取详情字段
        val viewData = photoDetails.photoDetailsViewData
        if (!photoDetails.isVideo()) {
            //截图图片
            val isScreenShot = viewData.imageModel == PhotoDetailsConstants.PhotoDetailsModelKeys.FORMAT_SCREENSHOT_IMAGE
            //转存图片
            val noCameraInfo = viewData.shootingInfo == EMPTY_STRING
            //整体展示
            exifIsoCardView?.isGone = isScreenShot || noCameraInfo
            //拍摄iso等信息，图片
            exifImageCardView?.updateData(photoDetails)
        } else {
            //整体展示
            exifIsoCardView?.isVisible = true
            //拍摄iso等信息，视频
            exifVideoCardView?.updateData(photoDetails)
        }
        //文件存储路径
        storageCardView?.isVisible = isPathFileExist(viewData.pathValue)
        storageCardView?.updateData(photoDetails)
        /**  //图集与上方的横线展示逻辑 @MarkBy伍子俊：第二轮交付时展示
        storageDiverLine?.isGone = viewData.fromAlbumBundle == null
        albumSetCardView?.isGone = viewData.fromAlbumBundle == null
         */

        //图集一轮交付时隐藏
        storageDiverLine?.isGone = true
        albumSetCardView?.isGone = true
        albumSetCardView?.updateData(photoDetails)
    }


    internal fun checkMapWindow(
        slotGetter: () -> Int,
        sectionPageGetter: () -> ISectionPage<PhotoFragment, PhotoViewModel>,
        entrance: String
    ) {
        val inwindow = checkInOrOutWindow(slotGetter, sectionPageGetter, entrance)
        val windowModeChanged = inwindow != lastInMapWindow
        GLog.d(TAG, LogFlag.DL, "checkMapWindow entrance $entrance inwindow $inwindow, lastInWindow $lastInMapWindow")
        if (windowModeChanged) {
            lastInMapWindow = inwindow
        }
        if (entrance == ENTRANCE_ATTACH || entrance == ENTRANCE_ON_FOCUS_CHANGE) {
            if (windowModeChanged) {
                processInOrOutMapWindow(inwindow)
            }
        } else if (entrance == ENTRANCE_DETATCH) {
            releaseMapView()
        }
    }


    private fun processInOrOutMapWindow(inwindow: Boolean) {
        if (inwindow) {
            refreshMapView()
        } else {
            releaseMapView()
        }
    }

    private fun checkInOrOutWindow(
        slotGetter: () -> Int,
        sectionPageGetter: () -> ISectionPage<PhotoFragment, PhotoViewModel>,
        entrance: String
    ): Boolean {
        val currentSlot = slotGetter.invoke()
        val currentFocus = sectionPageGetter.invoke().pageViewModel.dataLoading.focusSlot
        val offset = currentSlot - currentFocus
        val absOffset = abs(offset)
        val newResult = absOffset <= MAP_WINDOW_SIZE
        GLog.d(
            TAG, LogFlag.DL, "checkInOrOutWindow $entrance currentFocus $currentFocus, slot $currentSlot, offset $offset, " +
                    ", nowInWindow $newResult"
        )
        return newResult
    }


    /**
     * 刷新绑定地图
     */
    internal fun refreshMapView() {
        val pageScope = sectionPage.pageLifecycle.coroutineScope
        runOnUiThread(pageScope) {
            GLog.d(TAG, LogFlag.DL, "refreshMapView baseInfoCardView $baseInfoCardView")
            baseInfoCardView?.refreshMapContainer()
        }
    }


    /**
     * 释放地图
     */
    internal fun releaseMapView() {
        val pageScope = sectionPage.pageLifecycle.coroutineScope
        runOnUiThread(pageScope) {
            GLog.d(TAG, LogFlag.DL, "releasMapView baseInfoCardView $baseInfoCardView")
            baseInfoCardView?.releaseMapView()
        }
    }


    /**
     * 刷新部分明暗模式view的资源
     */
    internal fun refreshViewObjects() {
        //如果上次刷新时的主题色与当前主题色不一致，则刷新所有cardView的特殊资源设置
        val currentUiMode = sectionPage.pageViewModel.pageManagement.uiMode.value
        if (lastUiMode != currentUiMode) {
            //设置最近一次刷新的主题色
            lastUiMode = currentUiMode
            //刷新所有cardView的资源
            baseInfoCardView?.refreshUI()
            sectionPage.pageContext?.let { exifIsoCardView?.background = ContextCompat.getDrawable(it, R.drawable.photo_page_bg_exif_iso) }
            exifImageCardView?.refreshUI()
            exifVideoCardView?.refreshUI()
            albumSetCardView?.refreshUI()
        }
    }

    /**
     * 刷新所有CardView的RTL的布局展示
     */
    internal fun adapterViewObjects(context: Context? = null) {
        //获取config，并刷新所有cardView的RTL适配
        val config = (context ?: sectionPage.pageContext)?.resources?.configuration ?: return

        //baseInfoCardView只有一个云端icon,该icon不需要镜像，因此取消镜像
        adapterCardViewRTL(baseInfoCardView, config, isAdapterImageView = false)
        adapterCardViewRTL(exifInfoCardView, config)
        adapterCardViewRTL(exifImageCardView, config)
        adapterCardViewRTL(exifVideoCardView, config)
        adapterCardViewRTL(storageCardView, config)
        adapterCardViewRTL(albumSetCardView, config)
    }

    /**
     * 刷新RTL的布局
     * @param viewGroup 要适配RTL的ViewGroup
     * @param config 当前config环境
     * @param isAdapterImageView 是否需要适配ImageView
     */
    private fun adapterCardViewRTL(viewGroup: ViewGroup?, config: Configuration, isAdapterImageView: Boolean = true) {
        //遍历，按需刷新资源
        if ((viewGroup?.childCount != null) && (viewGroup.isNotEmpty())) {
            //遍历，按需刷新资源
            for (i in 0 until viewGroup.childCount) {
                when (val view = viewGroup.getChildAt(i)) {
                    //TextView设置
                    is TextView -> adapterTextViewRTL(view, config)

                    //ImageView设置
                    is ImageView -> {
                        if (isAdapterImageView) {
                            adapterImageViewRTL(view, config)
                        }
                    }
                }
            }
        }
    }

    /**
     * @param imageView 要适配RTL的imageView
     * @param config 当前config环境
     */
    private fun adapterImageViewRTL(imageView: ImageView?, config: Configuration) {
        //若当前处于RTL情况
        imageView?.scaleX = if (config.layoutDirection == View.LAYOUT_DIRECTION_RTL) {
            -1f
        } else {
            1f
        }
    }

    /**
     * @param textView 要适配RTL的textView
     * @param config 当前config环境
     */
    private fun adapterTextViewRTL(textView: TextView?, config: Configuration) {
        //若当前处于RTL情况
        if (config.layoutDirection == View.LAYOUT_DIRECTION_RTL) {
            textView?.textDirection = View.TEXT_DIRECTION_ANY_RTL
        } else {
            textView?.textDirection = View.TEXT_DIRECTION_FIRST_STRONG_LTR
        }
    }

    /**
     * 只要加载数据就会运行
     * 存储文件路径是否可以访问
     * 当当前图片是quick图或者quick视频的时候， File(path).exists()为false，不展示跳转入口，等到final图加载完后再允许跳转
     */
    private fun isPathFileExist(filePath: String?): Boolean {
        //sd存储，当当前图片是quick图或者quick视频的时候， File(path).exists()为false，不展示跳转入口，等到final图加载完后再允许跳转
        return if (!filePath.isNullOrEmpty()) {
            try {
                //路径是否存在
                Files.exists(Paths.get(filePath))
            } catch (e: IOException) {
                false
            }
        } else {
            false
        }
    }

    /**
     * 根据文件路径跳转到文件管理器
     */
    private fun jumpToFileManager() {
        val viewData = photoDetails?.photoDetailsViewData
        if (isPathFileExist(viewData?.pathValue)) {
            val path = viewData?.pathValue ?: EMPTY_STRING
            val intent = Intent().also {
                //跳转action
                it.setAction(ACTION_FILE_MANAGER_BROWSER_FILE)
                //跳转路径
                it.putExtra(EXTRA_FILE_MANAGER_BROWSER_FILE, path)
                //是否直接返回
                it.putExtra(EXTRA_FILE_MANAGER_FROM_DETAIL, true)

                // 需要让文管返回大图时关闭activity上的alpha动画，防止多个图层同时alpha导致的显示效果异常
                it.putExtra(EXTRA_FILE_MANAGER_DISABLE_EXIT_ALPHA, true)
            }
            sectionPage.pageContext?.safeStartActivity(intent)
            sectionPage.pageViewModel.pageManagement.notifyShouldSetOpaqueOnPause(true)
            overrideTransitionWhenJumpToOtherPage()
        }
    }

    /**
     * 跳转图集Activity以避免重复AlbumFragment存在
     */
    private fun startCommonAlbum() {
        val albumBundle = photoDetails?.photoDetailsViewData?.fromAlbumBundle
        //进行跳转
        if (albumBundle != null) {
            //跳转到以新的Activity形式打开图集
            Starter.ActivityStarter(
                startContext = sectionPage.pageInstance,
                bundle = albumBundle,
                postCard = PostCard(RouterConstants.RouterName.VIEW_GALLERY_ACTIVITY),
                //跳转到图集的Action
                onIntentCreated = fun(intent: Intent) {
                    intent.action = IntentConstant.ViewGalleryConstant.ACTION_INNER_ALBUM_SET
                    intent.type = MimeTypeUtils.MIME_TYPE_ANY
                }
            ).start()

            overrideTransitionWhenJumpToOtherPage()
        }
    }

    /**
     * 跳转其他activity时，指定转场动画。
     */
    private fun overrideTransitionWhenJumpToOtherPage() {
        // 需要禁用掉进入时的alpha效果，防止大图的多图层同时做alpha导致的显示效果异常
        sectionPage.pageInstance.activity?.overridePendingTransition(
            com.support.appcompat.R.anim.coui_open_slide_enter,
            com.oplus.gallery.basebiz.R.anim.business_lib_open_slide_exit_no_alpha
        )
    }

    private fun getUnlockReceiver(): BroadcastReceiver {
        return object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                val action = intent?.action
                if ((Intent.ACTION_USER_PRESENT == action)) {
                    //解锁后刷新数据
                    when (receiverType) {
                        //跳转到文管
                        JUMP_TO_FILE_MANAGER_RECEIVER_TYPE -> jumpToFileManager()
                        //跳转到图集
                        START_ALBUM_SET_RECEIVER_TYPE -> startCommonAlbum()
                    }
                    unregisterUnlockReceiver()
                }
            }
        }
    }

    /**
     * 注册锁屏监听
     */
    private fun registerUnlockReceiver() {
        //如果未注册，才进行注册
        if (unlockReceiver == null) {
            // 注册解锁监听
            unlockReceiver = getUnlockReceiver()
            IntentFilter().apply {
                addAction(Intent.ACTION_USER_PRESENT)
                sectionPage.pageContext?.let { context ->
                    BroadcastDispatcher.registerReceiver(context, unlockReceiver, this)
                }
            }
        }
    }

    /**
     * 注销Receiver
     */
    private fun unregisterUnlockReceiver() {
        sectionPage.pageContext?.let { context ->
            unlockReceiver?.let { receiver -> BroadcastDispatcher.unregisterReceiver(context, receiver) }
        }
        unlockReceiver = null
    }

    /**
     * 需要在onDestroy和onFocusChange中执行，避免内存泄漏
     */
    internal fun release() {
        unregisterUnlockReceiver()
    }

    companion object {
        private const val TAG = "PhotoDetailsCardViewManager"

        //跳转文管用action
        private const val ACTION_FILE_MANAGER_BROWSER_FILE = "oplus.intent.action.filemanager.BROWSER_FILE"

        //跳转文管用key
        private const val EXTRA_FILE_MANAGER_BROWSER_FILE = "CurrentDir"

        //跳转文管是否直接返回用key
        private const val EXTRA_FILE_MANAGER_FROM_DETAIL = "fromDetail"

        // 跳转文管key：返回大图时是否需要关闭activity默认的alpha动画
        private const val EXTRA_FILE_MANAGER_DISABLE_EXIT_ALPHA = "disable_exit_alpha_animation"

        // 跳转文管的广播接收器类型
        private const val JUMP_TO_FILE_MANAGER_RECEIVER_TYPE = "JUMP_TO_FILE_MANAGER_RECEIVER_TYPE"

        // 跳转图集的广播接收器类型
        private const val START_ALBUM_SET_RECEIVER_TYPE = "START_ALBUM_SET_RECEIVER_TYPE"


        const val MAP_WINDOW_SIZE = 1
    }
}