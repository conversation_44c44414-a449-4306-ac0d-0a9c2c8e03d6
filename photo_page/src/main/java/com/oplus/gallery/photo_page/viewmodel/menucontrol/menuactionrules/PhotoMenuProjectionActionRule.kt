/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoMenuProjectionActionRule.kt
 ** Description : 大图页菜单投屏操作规则
 ** Version     : 1.0
 ** Date        : 2022/03/18
 ** Author      : <PERSON>@Apps.Gallery
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON>@Apps.Gallery              2022/03/18  1.0         create file
 *********************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules

import android.app.Activity
import android.content.Intent
import androidx.annotation.IdRes
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.business_lib.menuoperation.MenuAction
import com.oplus.gallery.business_lib.menuoperation.MenuActionGetter
import com.oplus.gallery.business_lib.menuoperation.MenuOperationManager
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant.Value.CLICK_DLNA
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant.Value.MENU_ITEM_DLNA
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.version.ApiLevelUtil
import com.oplus.gallery.framework.abilities.cast.ICastAbility
import com.oplus.gallery.framework.abilities.cast.ICastService
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.ui.section.castingsection.PhotoCastingDeviceListDialog
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.dataloading.PhotoItemViewData

/**
 * 大图页菜单投屏操作规则
 */
internal class PhotoMenuProjectionActionRule(
    @IdRes private val ruleAction: Int,
    viewModel: PhotoViewModel,
    private val activity: BaseActivity,
    ruleResponseCodes: IntArray
) : PhotoMenuActionRule(TAG, ruleAction, viewModel, ruleResponseCodes) {

    /**
     * [ICastAbility]配置能力的对象，避免频繁创建
     */
    private val castAbility: ICastAbility? by lazy {
        activity.getAppAbility<ICastAbility>().apply {
            if (this == null) {
                GLog.i(TAG, LogFlag.DL) { "[castAbility]init castAbility failed , castAbility is null" }
            }
        }
    }

    override fun execute(viewData: PhotoItemViewData, mediaItem: MediaItem, onDone: () -> Unit) {
        // 埋点： 投屏菜单项被点击。
        when (ruleAction) {
            com.oplus.gallery.basebiz.R.id.action_dlna -> trackPictureClick(CLICK_DLNA)
            R.id.action_projection -> trackMenuClick(MENU_ITEM_DLNA)
        }

        doProjection(onDone)
    }

    override fun handleResult(requestCode: Int, resultCode: Int, intent: Intent?) {
        if (resultCode == Activity.RESULT_OK) {
            viewModel.outputChannels.castingManager.notifyCastingDeviceResult(requestCode, intent)
        }
    }

    private fun doProjection(onDone: () -> Unit) {
        MenuOperationManager.doAction(
            action = MenuAction.PROJECTION,
            paramMap = MenuActionGetter.projection.builder
                .setActivity(activity)
                .setRequestCode(ruleAction)
                .setTrackCallerEntry(trackCaller)
                .build(),
            onCompleted = { _, _ ->
                castAbility?.let {
                    it.castChannel?.apply {
                        activity.intent.putExtra(ICastService.INTENT_KEY_REQUEST_CODE, ruleAction)
                        if (ApiLevelUtil.isAtLeastAndroidT()) {
                            PhotoCastingDeviceListDialog(activity).show()
                        } else {
                            startCastDevicePage(activity)
                        }
                    }
                }
                onDone()
            }
        )
    }

    override fun onDestroy() {
        super.onDestroy()
        //关闭 ability
        closeAbility()
    }

    /**
     * 关闭Ability
     */
    private fun closeAbility() {
        castAbility?.close()
    }

    companion object {
        private const val TAG = "PhotoMenuProjectionActionRule"
    }
}