/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : IntelliFuncContract.kt
 ** Description : Overlay联动之智能推荐联动部分
 ** Version     : 1.0
 ** Date        : 2025/6/8
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>          2025/6/8    1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.photo_page.ui.section.details.collaborator.contract

import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.photo_page.ui.section.details.collaborator.IOverlayContract
import com.oplus.gallery.photo_page.ui.section.intelliRecommend.PhotoIntelliFuncComponent

/**
 * 智能推荐联动部分
 * @param intelliFuncComponentGetter 智能推荐组件获取器.用户获取组件，联动时传递或者控制必要属性
 */
internal class IntelliFuncContract(private val intelliFuncComponentGetter: () -> PhotoIntelliFuncComponent?) : IOverlayContract {

    /**
     * 智能推荐区域的根组件
     */
    private val intelliComponent: PhotoIntelliFuncComponent?
        get() = intelliFuncComponentGetter.invoke()

    override fun onTransitionValueUpdate(transitionPercent: Float, transitionPosition: Float) {
        intelliComponent?.updateTransitionPosition(transitionPercent)
    }

    override fun onWindowInsetsChanged() {
        intelliComponent?.onDetailsOverlayUiStateChanged()
    }

    override fun onLayoutChanged() {
        intelliComponent?.onDetailsOverlayUiStateChanged()
    }

    override fun onAttach() {
        //刷新缓存池里的暗亮色模式
        intelliComponent?.refreshUiStateChanged()
    }

    override fun onAppUiStateChanged(config: AppUiResponder.AppUiConfig) {
        // 暗亮色刷新内容
        intelliComponent?.refreshUiStateChanged()
    }

    override fun toString(): String = "$TAG@${hashCode()}"

    companion object {
        private const val TAG = "IntelliFuncContract"
    }
}