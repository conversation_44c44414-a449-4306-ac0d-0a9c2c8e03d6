/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - PictureTrackTemplate.kt
 * Description: 大图界面模板基类
 * Version: 1.0
 * Date: 2020/12/28
 * Author: huang.linpeng@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * huang.linpeng@Apps.Gallery3D     2020/12/28     1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.track.producttracker.template

import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.foundation.tracing.TrackEntry
import com.oplus.gallery.foundation.tracing.template.DcsSingleTrackTemplate

abstract class PictureTrackTemplate(private val pictureTrackEntry: PictureTrackEntry, protected val value: String?) :
    DcsSingleTrackTemplate() {

    /**
     * 1、进图大图的上一个界面title
     * 2、三方APP进来为空
     */
    protected val backTitle
        get() = pictureTrackEntry.trackBackTitle


    /**
     * 1、埋点的mediaItem数据
     */
    protected val mediaItem
        get() = pictureTrackEntry.trackMediaItem

    /**
     * 1、来源应用包名
     */
    protected val callPage
        get() = pictureTrackEntry.trackCallPage

    /**
     * 当前大图实例的窗口状态
     * - 正常
     * - 自分屏
     * - 后续可能有： 浮窗、正常分屏等等
     */
    protected val trackWindowState
        get() = pictureTrackEntry.trackWindowState

    final override fun pack(trackId: String, type: String, event: String, entry: MutableMap<String, Any?>): TrackEntry {
        return super.pack(trackId, type, event, entry).apply {
            mediaItem?.apply {
                pack(trackItems, this)
            }
        }
    }

    /**
     * 1、子类实现该方法
     */
    protected abstract fun pack(trackItems: MutableMap<String, Any?>, mediaItem: MediaItem)

    data class PictureTrackEntry(
        val trackBackTitle: String?,
        val trackMediaItem: MediaItem?,
        val trackCallPage: String?,
        val trackWindowState: String
    )
}