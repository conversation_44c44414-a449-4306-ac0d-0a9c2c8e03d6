/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoMenuShareActionRule.kt
 ** Description : 大图菜单分享操作规则
 ** Version     : 1.0
 ** Date        : 2022/03/18
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON>@Apps.Gallery              2022/03/18  1.0         create file
 *********************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules

import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import androidx.annotation.IdRes
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.lifecycleScope
import com.oplus.gallery.basebiz.constants.IntentConstant
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_CHAIN_FROM
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.basebiz.uikit.fragment.BaseFragment
import com.oplus.gallery.business_lib.helper.ShareHelper
import com.oplus.gallery.business_lib.menuoperation.ShareAction
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.UriImage
import com.oplus.gallery.business_lib.model.data.base.item.toOriginalItem
import com.oplus.gallery.business_lib.model.data.base.utils.VideoTypeUtils
import com.oplus.gallery.business_lib.template.editor.MeicamEngineLimiter
import com.oplus.gallery.foundation.fileaccess.FileConstants
import com.oplus.gallery.foundation.fileaccess.GalleryFileProvider
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant.Value.MENU_ITEM_SEND
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_USER_CUSTOM_GALLERY_SHARE
import com.oplus.gallery.framework.abilities.data.DataRepository.UriModelGetter.Companion.TYPE_URI_ALBUM
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.dataloading.PhotoItemViewData
import com.oplus.gallery.photo_page.viewmodel.dataloading.focusItemViewData
import com.oplus.gallery.photo_page.viewmodel.menucontrol.save.ISaveActionListener
import com.oplus.gallery.photo_page.viewmodel.menucontrol.save.PhotoSaveImageHelper
import com.oplus.gallery.photo_page.viewmodel.playbackcontrol.PlaybackInfo
import com.oplus.gallery.router_lib.Starter
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.standard_lib.scheduler.session.WorkerSession
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import kotlinx.coroutines.CoroutineStart
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference

/**
 * 大图菜单分享操作规则
 */
internal class PhotoMenuShareActionRule(
    @IdRes ruleAction: Int,
    viewModel: PhotoViewModel,
    private val fragment: BaseFragment,
    private val activity: BaseActivity,
    private val session: WorkerSession
) : PhotoMenuActionRule(TAG, ruleAction, viewModel) {

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal val shouldReverseDataOrder: Boolean
        get() = viewModel.inputArguments.dataSource.value?.shouldReverseDataOrder ?: false

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal val playbackInfo: PlaybackInfo?
        get() = viewModel.playback.playbackInfo.value

    private val uriSet: Array<String>?
        get() = viewModel.inputArguments.dataSource.value?.extra?.getStringArray(ViewGalleryConstant.KEY_URI_ARRAY)

    override fun execute(viewData: PhotoItemViewData, mediaItem: MediaItem, onDone: () -> Unit) {
        // 埋点： 分享菜单项被点击
        trackMenuClick(MENU_ITEM_SEND) { track ->
            val fromPage = viewModel.inputArguments.invokeFrom.value?.parentPage ?: EMPTY_STRING
            if (viewModel.track.isFromPickedPages(fromPage)) {
                val filePath = viewModel.dataLoading.focusItemViewData?.filePath ?: EMPTY_STRING
                val viewType = viewModel.inputArguments.dataSource.value?.extra?.getString(IntentConstant.PicturePageConstant.KEY_VIEW_TYPE)
                    ?: EMPTY_STRING
                val crop = viewModel.inputArguments.dataSource.value?.extra?.getString(IntentConstant.PicturePageConstant.KEY_CROP) ?: EMPTY_STRING
                viewModel.track.getPictureTrackEnterEventMapFromPickedPage(fromPage, filePath, viewType, crop).forEach {
                    track.putProperty(it.key, it.value)
                }
            }
            track.save()
        }

        // 1. 条件判断
        if (modelType.isEmpty() || viewData.id.isEmpty()) {
            GLog.e(TAG, "[doShareAction] Cannot share because the ModelType or MediaId of source is missing")
            onDone()
            return
        }

        //隐藏详情页
        if (viewModel.details.isInDetailsMode.value == true) {
            viewModel.details.changeDetailsMode(enter = false)
        }
        doShareAction(viewData, onDone, mediaItem)
    }

    override fun isOperationSupportedForQuick(): Boolean {
        return false
    }

    private fun doShareAction(
        viewData: PhotoItemViewData,
        onDone: () -> Unit,
        mediaItem: MediaItem
    ) {
        if (MeicamEngineLimiter.getInstance().allowCreateNewEngine().not()) {
            ToastUtil.showShortToast(R.string.photopage_menu_can_not_create_multiple_editor_and_export)
            GLog.d(TAG, LogFlag.DL, "[doShareAction] have exist a video engine, can't enter. ignore!")
            onDone()
            return
        }

        // 2. 执行分享操作
        GLog.d(TAG, "doShareAction id = ${viewData.id}, modelType = $modelType")
        when {
            !isSupportUserCustomGalleryShare() -> {
                ToastUtil.showShortToast(com.oplus.gallery.basebiz.R.string.base_feature_is_disabled_tip)
                onDone()
                return
            }
            // 政企模式不执行分享
            FeatureUtils.isContainerUser -> {
                ToastUtil.showLongToast(R.string.picture3d_function_not_available_in_current_mode)
                onDone()
                return
            }

            // 旧版本慢动作
            isOldSlowMotionVideo(mediaItem) -> shareOldSlowMotionVideo(mediaItem, onDone)

            // UriImage执行单张图片分享
            (mediaItem is UriImage) -> shareUriImage(mediaItem, onDone)

            // 正常分享流程
            else -> shareSinglePhoto(viewData, mediaItem, onDone)
        }
    }

    /**
     * 分享多张图片，且分享页会展示对应的Model中的全部图片
     */
    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun shareSinglePhoto(viewData: PhotoItemViewData, mediaItem: MediaItem, onDone: () -> Unit) {
        val uriSet = when (modelType) {
            TYPE_URI_ALBUM -> {
                uriSet ?: let {
                    GLog.e(TAG) {
                        "[shareMultiplePhoto] modelType is TYPE_URI_ALBUM," +
                            " but no ${ViewGalleryConstant.KEY_URI_ARRAY} value, check code!!!"
                    }
                    return
                }
            }
            else -> uriSet
        }

        val bundle = Bundle().apply {
            putString(ShareAction.KEY_VIEWDATA_ID, mediaSetPath)
            putString(ShareAction.KEY_MODEL_TYPE, modelType)
            putBoolean(ShareAction.KEY_ORDER_REVERT, shouldReverseDataOrder)
            putBoolean(ShareAction.KEY_FROM_PHOTO_PAGE, true)
            // 相机相册亮度一致性
            putString(KEY_CHAIN_FROM, viewModel.inputArguments.features.value?.chainFrom)
            uriSet?.let { putStringArray(ViewGalleryConstant.KEY_URI_ARRAY, it) }
        }
        val selectionPath = mediaItem.toOriginalItem()?.path ?: let {
            GLog.e(TAG) { "[shareMultiplePhoto] selectionPath is null. skip." }
            return
        }
        val callback = SafeShareCallback(activity, viewModel, onDone)
        ShareHelper.showShareDialog(
            activity = activity,
            bundle = bundle,
            focusPath = Path.fromString(viewData.id),
            focusPosition = viewData.position,
            selectionPath = selectionPath,
            updateShowCallback = callback::handle,
            trackCallerEntry = trackCaller,
        )
    }

    private class SafeShareCallback(
        private val activity: BaseActivity,
        private val viewModel: PhotoViewModel,
        private val onDone: () -> Unit
    ) {
        private val safeActivity = WeakReference(activity)
        private val safeViewModel = WeakReference(viewModel)
        fun handle(state: Int) {
            // 会在 dismiss 时回调
            if (state == ShareHelper.STATE_SHARE_BY_SYSTEM_SHARE) {
                /**
                 * 如果启动了系统的分享页，我们无法得知何时分享完成，
                 * 在此处依赖相册[safeActivity]的生命周期进行处理。
                 * 当 [Activity.onResume]时，再恢复分享页可用。
                 */
                safeActivity.get()?.lifecycleScope?.let { scopeIt ->
                    scopeIt.launch(Dispatchers.Main) {
                        /**
                         * 回调时，系统分享页也许还没有拉起，如果立即监听声明周期，此时还在resume状态，会立即执行逻辑。
                         * 因此延时 300ms 执行，给系统分享页启动的时间。
                         */
                        launch(start = CoroutineStart.LAZY) {
                            scopeIt.launchWhenResumed {
                                // 不做任何事，只是要这个时机去调用 invokeOnCompletion
                            }.invokeOnCompletion {
                                /**
                                 *  任务执行完成，或者任务被取消，都会走到这里。
                                 *
                                 *  任务被取消：
                                 *  在执行分享的过程中，用户切换暗色模式，会引起activity的销毁重建，
                                 *  在 "onDestroy" 的时候，任务会被取消，此时也应该执行 onDone,使分享可用。
                                 */
                                onDone()
                                safeViewModel.get()?.pageManagement?.notifyTransferToInternalSharePage() ?: GLog.e(
                                    TAG,
                                    LogFlag.DL
                                ) { "[SafeShareCallback:handle] viewModel Has been recycled by GC, weak reference returned." }
                            }
                        }.apply {
                            delay(DELAY_TIME_WHEN_SHARE_BY_SYSTEM)
                            start()
                        }
                    }
                } ?: GLog.e(TAG, LogFlag.DL) { "[SafeShareCallback:handle] Activity Has been recycled by GC, weak reference returned." }
            } else {
                onDone()
            }
        }
    }

    /**
     * 分享旧版本慢动作视频，旧逻辑单独适配。
     */
    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun shareOldSlowMotionVideo(mediaItem: MediaItem, onDone: () -> Unit) {
        runCatching {
            Starter.ActivityStarter(
                startContext = activity,
                bundle = Bundle(),
                postCard = PostCard(RouterConstants.RouterName.VIDEO_EDITOR_SEND_ACTIVITY),
                onIntentCreated = { intent ->
                    intent.setDataAndType(
                        mediaItem.contentUri,
                        MimeTypeUtils.MIME_TYPE_VIDEO_ANY
                    )
                }
            ).start()
        }.onFailure { e ->
            GLog.d(TAG, "doShareAction : startActivity error", e)
        }
        onDone()
    }

    /**
     * 分享UriImage，分享页只会展示单张图片
     */
    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun shareUriImage(
        mediaItem: MediaItem,
        onDone: () -> Unit
    ) {
        // UriImage执行分享之前需要先保存到缓存。
        val saveActionListener = object : ISaveActionListener {
            override fun onSaveSuccess(saveFilePath: String?) {
                onDone()
                //TODO JinPeng 2021/10/23 是否需要转到主线程去操作·
                if (saveFilePath.isNullOrEmpty()) {
                    GLog.w(TAG, "shareUriImage, onSaveSuccess file path is empty!")
                    ToastUtil.showShortToast(com.oplus.gallery.basebiz.R.string.common_saving_failure)
                    return
                }
                val saveFile = File(saveFilePath)
                if (!saveFile.exists()) {
                    GLog.w(TAG, "shareUriImage, onSaveSuccess file not exist!")
                    ToastUtil.showShortToast(com.oplus.gallery.basebiz.R.string.common_saving_failure)
                    return
                }

                if ((mediaItem.mediaType == FileConstants.MediaType.MEDIA_TYPE_IMAGE)
                    || (mediaItem.mediaType == FileConstants.MediaType.MEDIA_TYPE_VIDEO)
                ) {
                    val intent = Intent(Intent.ACTION_SEND)
                    intent.type = mediaItem.mimeType
                    val uri = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                        GalleryFileProvider.fromFile(activity, saveFile)
                    } else {
                        Uri.fromFile(saveFile.file)
                    }
                    intent.putExtra(Intent.EXTRA_STREAM, uri)
                    activity.startActivity(
                        Intent.createChooser(intent, activity.getString(com.oplus.gallery.basebiz.R.string.picture3d_share))
                    )
                } else {
                    GLog.w(TAG, "shareUriImage, onSaveSuccess, type = ${mediaItem.mediaType} not support!")
                }
            }

            override fun onSaveFail(errorCode: Int) {
                //TODO JinPeng 2021/10/23 是否需要转到主线程去操作
                ToastUtil.showShortToast(com.oplus.gallery.basebiz.R.string.common_saving_failure)
                onDone()
            }
        }
        createPhotoSaveImageHelper(saveActionListener).performSave(
            session = session,
            item = mediaItem,
            dirPath = (activity.cacheDir.absolutePath + PhotoSaveImageHelper.SHARE_ITEM_CACHE_DIR),
            isNotifyMediaScannerFile = false
        )
    }

    /**
     * 创建一个 [PhotoSaveImageHelper]
     */
    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun createPhotoSaveImageHelper(saveActionListener: ISaveActionListener) =
        PhotoSaveImageHelper(saveActionListener)

    /**
     * 是旧版本的慢动作视频
     */
    private fun isOldSlowMotionVideo(mediaItem: MediaItem) =
        ((mediaItem.mediaType == FileConstants.MediaType.MEDIA_TYPE_VIDEO)
            && VideoTypeUtils.isOldSlowMotion(mediaItem.name))

    private fun isSupportUserCustomGalleryShare() = ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_USER_CUSTOM_GALLERY_SHARE)

    companion object {
        private const val TAG = "PhotoMenuShareActionRule"
        private const val DELAY_TIME_WHEN_SHARE_BY_SYSTEM = 300L
    }
}
