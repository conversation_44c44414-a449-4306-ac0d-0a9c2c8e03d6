/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoDetailsTimeCard.kt
 ** Description : 大图内容详情信息时间UI展示块
 ** Version     : 1.0
 ** Date        : 2025/4/11
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>                  2025/4/11       1.0     create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.ui.section.details.widget

import android.app.Activity
import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import com.oplus.gallery.basebiz.mappage.MapPageManager
import com.oplus.gallery.basebiz.permission.NetworkPermissionManager
import com.oplus.gallery.business_lib.helper.MapPageJumper
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.map.IMapAbility
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.ui.section.intelliRecommend.AutoEndMarqueeTextView
import com.oplus.gallery.photo_page.viewmodel.details.PhotoDetails
import com.oplus.gallery.standard_lib.app.AppConstants

/**
 * 大图内容详情信息时间UI展示块
 */
internal class PhotoDetailsTimeCardView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    defStyleRes: Int = 0
) : PhotoDetailsBaseCardView(context, attrs, defStyleAttr, defStyleRes) {

    /**
     * 日期
     */
    private var tvDate: TextView? = null

    /**
     * 云备份图标
     */
    private var ivCloud: ImageView? = null

    /**
     * 文件名全名
     */
    private var tvFullName: TextView? = null

    /**
     * 调整按钮
     */
    private var tvAdjust: AutoEndMarqueeTextView? = null

    /**
     * 地址
     */
    private var tvAddress: TextView? = null

    /**
     * 下横线
     */
    private var vLine: View? = null

    /**
     * 地图容器
     */
    private var mapContainer: MapContainerLayout? = null

    /**
     * 地图和上面的imageView整体的容器
     */
    private var mapLayout: FrameLayout? = null

    /**
     * 地图处理类
     */
    private var mapPageManager = MapPageManager()

    /**
     * 地图跳转类
     */
    private var mapPageJumper = MapPageJumper()

    private var registeredLifecycle: Lifecycle? = null

    /**
     * 地图功能是否授权
     */
    private val mapAuthorized: Boolean by lazy {
        MapPageJumper.Companion.checkMapAgreed(this.context)
    }

    /**
     * 当前这个view绑定的地点数据
     */
    private var attachedLocation: DoubleArray? = null


    /**
     * 地图是否可以显示在详情页外部入口
     */
    private val canShowMap: Boolean by lazy {
        context.getAppAbility<IMapAbility>().use {
            it?.canShowInDetailOuter() == true
        }
    }

    /**
     * 地图需要监听所在页面的lifecycle
     */
    private var lifecycleObserver: LifecycleEventObserver = LifecycleEventObserver { source, event ->
        GLog.d(TAG, LogFlag.DL, "onStateChanged source $source, event: $event, this $this")
        when (event) {
            Lifecycle.Event.ON_RESUME -> processOnResume()
            Lifecycle.Event.ON_PAUSE -> processOnPause()
            Lifecycle.Event.ON_DESTROY -> {
                processOnDestroy()
                unregisterLifecycle()
            }

            else -> {}
        }
    }

    /**
     * 当前界面所展示的数据内容
     */
    private var currentPhotoDetails: PhotoDetails? = null

    /**
     * 初始化view
     */
    override fun initView() {
        super.initView()
        //日期
        tvDate = view.findViewById(R.id.tv_date)
        //云备份icon
        ivCloud = view.findViewById(R.id.iv_cloud)
        //文件名
        tvFullName = view.findViewById(R.id.tv_full_name)
        //调整按钮
        tvAdjust = view.findViewById(R.id.tv_adjust)
        //设置轮播两次
        tvAdjust?.setMarqueeRepeatAndStart(AppConstants.Number.NUMBER_2)
        //地址
        tvAddress = view.findViewById(R.id.tv_address)
        //下横线
        vLine = view.findViewById(R.id.v_line)
        //地图容器
        mapContainer = view.findViewById(R.id.detail_map_container)
        //地图和前景ImageView的整体父容器
        mapLayout = view.findViewById(R.id.map_layout)
    }

    /**
     * 初始化事件
     * Mark 占位，用于展示调整弹窗
     */
    override fun initEvent() {
        super.initEvent()
        //展示调整弹窗
        tvAdjust?.setOnClickListener {
            showAdjustWindow()
        }
    }

    /**
     * 设置layoutId
     */
    override fun getLayoutId(): Int {
        return R.layout.photo_details_time_card
    }

    /**
     * 更新数据
     *  MarkedBy@all 地图未展示
     */
    override fun updateData(photoDetails: PhotoDetails) {
        this.photoDetails = photoDetails

        if (isViewInitialized.not()) return

        val oldData = currentPhotoDetails
        val dataChanged = (oldData == null) || (oldData.getMediaId() != photoDetails.getMediaId())

        currentPhotoDetails = photoDetails

        //获取详情字段
        val viewData = photoDetails.photoDetailsViewData
        //日期
        tvDate?.text = viewData.date
        //是否展示云备份icon
        ivCloud?.isVisible = viewData.isCloudGlobal
        //展示文件名
        tvFullName?.text = viewData.fullName
        //展示地址
        tvAddress?.text = viewData.address ?: ContextCompat.getString(context, R.string.photopage_details_no_location_information)
        val locationChanged = checkLocationChanged(viewData.latLong, attachedLocation)
        val isInFocusWindow = photoDetails.isCurrentInFocusWindow()
        if ((dataChanged || locationChanged) && isInFocusWindow) {
            //绑定处理地图, 为了避免多次调用，只有在绑定数据变动，或下载原图时地点变动时才处理地图的初始化逻辑
            bindMapViewContainer(photoDetails)
        } else {
            GLog.d(TAG, LogFlag.DL, "updateData mediaItem equal, not to config map Data $this")
        }
        attachedLocation = viewData.latLong
    }

    internal fun setViewLineVisible(visible: Boolean) {
        vLine?.isVisible = visible
    }

    fun refreshMapContainer() {
        GLog.d(TAG, "refreshMapContainer photoDetails $photoDetails")
        photoDetails?.let {
            bindMapViewContainer(it)
        }
    }

    fun releaseMapView() {
        GLog.d(TAG, "releaseMapView photoDetails $photoDetails")
        mapPageManager.onDestroy()
        mapContainer?.removeAllViews()
        mapContainer?.isVisible = false
    }


    /**
     * 这里加入这个判断是判定大图原图下载的场景，原图下载前后updateData中传入的photoDetails是都是同一个对象，无法通过前后对象比对完成场景区分
     * 这就需要对比View中的地点数据和传入的photoDetails数据做地点比对（下载前oneLocaton=null，下载后oneLocation！=null）
     */
    private fun checkLocationChanged(oneLocation: DoubleArray?, twoLocation: DoubleArray?): Boolean {
        val result = if ((oneLocation == null) && (twoLocation == null)) {
            false
        } else if ((oneLocation != null) && (twoLocation == null)) {
            true
        } else if (oneLocation == null) {
            true
        } else {
            val latitudeNotEqual = oneLocation[0] != twoLocation?.get(0)
            val longitudeNotEqual = oneLocation[1] != twoLocation?.get(1)
            latitudeNotEqual || longitudeNotEqual
        }
        GLog.d(TAG, LogFlag.DL, "checkLocationChanged result: $result")
        return result
    }

    override fun onDetachedFromWindow() {
        GLog.d(TAG, LogFlag.DL, "onDetachedFromWindow this $this")
        super.onDetachedFromWindow()
    }

    override fun onAttachedToWindow() {
        GLog.d(TAG, LogFlag.DL, "onAttachedToWindow this $this")
        super.onAttachedToWindow()
    }

    private fun bindMapViewContainer(photoDetails: PhotoDetails) {
        val viewData = photoDetails.photoDetailsViewData
        /** 大图详情页是否需要展示地图有以下条件：
         1.该图片具有经纬度信息
         2.用户已经授权了使用地图权限
         3.内销机的MapComFrame是否满足1.1.17及以上版本
         4.相册设置中开启了联网权限
         */
        val needShowMap = (viewData.latLong != null) && mapAuthorized && canShowMap && NetworkPermissionManager.isUseOpenNetwork
        val mediaPath = photoDetails.getMediaPath()
        GLog.d(TAG, LogFlag.DL, "bindMapViewContainer latLong ${viewData.latLong},  needShowMap $needShowMap, mediaPath: " +
                "$mediaPath, mediaId: ${photoDetails.getMediaId()} , this: $this")
        mapLayout?.isVisible = needShowMap
        vLine?.isGone = needShowMap
        mapContainer?.setOnClickListener {
            GLog.d(TAG, LogFlag.DL, "bindMapViewContainer onClick mapContainer")
            processClickMap(mediaPath)
        }
        if (needShowMap) {
            mapContainer?.isVisible = true
            //触发mapView初始化
            val context = this.context
            mapPageManager.initMapSdk(context)
            mapContainer?.let {
                /* 清楚所有之前的子View，mapContainer是复用的，这里如果不remove内部的子View，左右滑动时可能导致复用的Container中添加了多个MapView，
                 * 导致最上层的MapView显示和预期的MapView显示的不是同一个图片和位置,这里initDetailOuter中执行了remove地图View方法，这里不用执行了，注释掉
                 *
                 * MarkedBy 檀路遥：@黄远望
                 * initDetailOuter 这个耗时在主线程，会导致详情出现时贼卡
                 * 地图业务，单个拎出来，这里支持异步产生View？业务需要的时候再择机addView？
                 * 目前地图业务这里改不动
                 */
                mapPageManager.initDetailOuter(context, it, mediaPath.toString(), null)
            }
        } else {
            /*清楚所有之前的子View，mapContainer是复用的，这里如果不remove内部的子View，左右滑动时可能导致复用的Container中添加了多个MapView，
            导致最上层的MapView显示和预期的MapView显示的不是同一个图片和位置*/
            mapContainer?.removeAllViews()
            mapContainer?.isVisible = false
        }
    }

    fun registerLifecycle(lifecycle: Lifecycle) {
        GLog.d(TAG, LogFlag.DL, "registerLifecycle lifecycle $lifecycle, this: $this")
        registeredLifecycle = lifecycle
        lifecycle.addObserver(lifecycleObserver)
    }

    private fun unregisterLifecycle() {
        GLog.d(TAG, LogFlag.DL, "unregisterLifecycle lifecycle $registeredLifecycle, this: $this")
        registeredLifecycle?.removeObserver(lifecycleObserver)
        registeredLifecycle = null
    }

    private fun processOnResume() {
        mapPageManager.onResume()
    }

    private fun processOnPause() {
        mapPageManager.onPause()
    }

    private fun processOnDestroy() {
        mapPageManager.onDestroy()
    }

    private fun processClickMap(mediaItemPath: Path) {
        val activity = context as Activity
        mapPageJumper.enterMapLocationAlbumActivity(activity, mediaItemPath, mapPageManager.getDefaultZoomForMap())
    }

    /**
     * 明暗模式更新UI
     */
    override fun refreshUI() {
        super.refreshUI()
        //调整按钮
        tvAdjust?.setTextColor(ContextCompat.getColor(context, R.color.photopage_details_adjust_blue))
        tvAdjust?.setBackgroundDrawable(ContextCompat.getDrawable(context, R.drawable.photopage_bg_photo_details_adjust_gray))
    }

    /**
     * 展示调整弹窗
     * MarkedBy@all 暂未实现，先按方案占个方法坑
     */
    private fun showAdjustWindow() = Unit

    override fun toString(): String = "$TAG@${hashCode()}"

    companion object {
        private const val TAG = "PhotoDetailsTimeCardView"
    }
}