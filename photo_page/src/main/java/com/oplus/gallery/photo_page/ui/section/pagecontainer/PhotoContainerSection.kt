/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoPageSectionImpl.kt
 ** Description : 大图页面 - 部分页面切片
 ** Version     : 1.0
 ** Date        : 2022/02/17
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>@Apps.Gallery              2022/02/17  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.ui.section.pagecontainer

import android.app.ActivityManager
import android.content.Intent
import android.content.pm.ActivityInfo
import android.content.pm.ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
import android.content.res.Configuration
import android.content.res.Configuration.ORIENTATION_LANDSCAPE
import android.database.ContentObserver
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.Rect
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import android.os.Build.VERSION
import android.os.Build.VERSION_CODES
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Size
import android.view.Surface
import android.view.View
import android.view.ViewGroup
import androidx.core.graphics.ColorUtils
import androidx.core.view.WindowInsetsControllerCompat
import androidx.core.view.doOnNextLayout
import androidx.core.view.doOnPreDraw
import androidx.core.view.isEmpty
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.oplus.gallery.basebiz.constants.IntentConstant
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.VALUE_CHAIN_FROM_CAMERA
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.helper.SlotOverlayHelper.SUPPORT_IS_YUV_FORMAT
import com.oplus.gallery.basebiz.transition.BackgroundColorTransition
import com.oplus.gallery.basebiz.transition.PhotoBackgroundDrawable
import com.oplus.gallery.basebiz.transition.PhotoPageTransitionManager
import com.oplus.gallery.basebiz.transition.PhotoSlotSizeCalculator
import com.oplus.gallery.basebiz.transition.PreviewTransition
import com.oplus.gallery.basebiz.transition.TransitionAnimStrategy
import com.oplus.gallery.basebiz.transition.TransitionPreviewData
import com.oplus.gallery.basebiz.transition.widget.PhotoClipBoundTransitionView
import com.oplus.gallery.business_lib.transition.TransitionHelper
import com.oplus.gallery.foundation.arch.sectionpage.ISectionPage
import com.oplus.gallery.foundation.cache.memorycache.BitmapPools
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant
import com.oplus.gallery.foundation.ui.animator.WindowInsetsFadeInAnimator
import com.oplus.gallery.foundation.ui.animator.WindowInsetsFadeOutAnimator
import com.oplus.gallery.foundation.ui.widget.BoundsType
import com.oplus.gallery.foundation.uikit.broadcast.bus.HomeRecentListener
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.foundation.util.ext.collect
import com.oplus.gallery.foundation.util.ext.getOrLog
import com.oplus.gallery.foundation.util.ext.getValidScreenOrientationOrDefault
import com.oplus.gallery.foundation.util.ext.isVisibleAndOpaque
import com.oplus.gallery.foundation.util.graphic.ImageUtils
import com.oplus.gallery.foundation.util.multiprocess.ITransBitmap
import com.oplus.gallery.foundation.util.systemcore.IntentUtils
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING
import com.oplus.gallery.foundation.util.version.ApiLevelUtil
import com.oplus.gallery.framework.abilities.colormanagement.IColorManagementAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_LOW_PERFORMANCE_DEVICE
import com.oplus.gallery.framework.abilities.hardware.IHardwareAbility
import com.oplus.gallery.framework.abilities.hardware.IScreen
import com.oplus.gallery.framework.abilities.hardware.IScreen.ClearBrightnessMode
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.framework.app.withAbility
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.ui.AbstractPhotoSection
import com.oplus.gallery.photo_page.ui.PhotoFragment
import com.oplus.gallery.photo_page.ui.section.brighten.PhotoBrightenSectionCompatLHDR
import com.oplus.gallery.photo_page.ui.section.details.PanelStructure
import com.oplus.gallery.photo_page.ui.section.details.collaborator.contract.PanelContractEffectState
import com.oplus.gallery.photo_page.ui.section.details.getPanelStructure
import com.oplus.gallery.photo_page.ui.section.photomenu.PhotoMenuSection
import com.oplus.gallery.photo_page.ui.section.photopagersection.PhotoPagerSection
import com.oplus.gallery.photo_page.ui.theme.MenuDecorationTheme
import com.oplus.gallery.photo_page.ui.transition.BrightenPreviewTransitionCompatLHDR
import com.oplus.gallery.photo_page.ui.transition.adapter.PhotoPageTransitionAdapter
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.dataloading.DiffedPhotoItemViewData
import com.oplus.gallery.photo_page.viewmodel.dataloading.PhotoItemViewData
import com.oplus.gallery.photo_page.viewmodel.dataloading.focusItemViewData
import com.oplus.gallery.photo_page.viewmodel.dataloading.focusSlot
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PhotoDecorationState
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PhotoDecorationState.DecorationAnimator
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PhotoPageManagementViewModel.FirstFrameRenderingStatus
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PhotoPageManagementViewModel.PageTransitionState
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PhotoPageManagementViewModel.PageTransitionState.NEW
import com.oplus.gallery.photo_page.viewmodel.track.producttracker.tracker.IVideoPlayProductTracker
import com.oplus.gallery.photopager.PhotoPager
import com.oplus.gallery.photopager.PhotoSlot
import com.oplus.gallery.photopager.animationcontrol.createAnimationPropertyProvider
import com.oplus.gallery.photopager.viewpager.widget.ViewPager
import com.oplus.gallery.standard_lib.app.SINGLE_UN_BUSY
import com.oplus.gallery.tools.any
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.launch
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.math.roundToInt

/**
 * 大图页页面部分页面切片。
 * 管控逻辑：
 * - 进入、退出大图的动画
 * - 大图状态栏、导航栏可见性。
 * - 大图装饰器的出现与消失动画
 */
internal open class PhotoContainerSection(
    sectionPage: ISectionPage<PhotoFragment, PhotoViewModel>
) : AbstractPhotoSection(sectionPage) {
    /**
     * 过渡动画完成后，需要回到的目标位置的 viewDataId（如编辑回到大图时）
     */
    private var targetViewDataId: String? = null

    /**
     * 大图页面的容器控件
     */
    private lateinit var pageContainer: View

    private lateinit var photoPager: PhotoPager

    /**
     * 大图过渡动画控件；图片
     */
    private lateinit var transitionView: PhotoClipBoundTransitionView

    /**
     * 底部装饰器View。
     * - 底部菜单。
     * - 视频播控。
     */
    private lateinit var bottomDecorationView: View

    /**
     * 顶部装饰器View。
     * - 顶部菜单。
     */
    private lateinit var topDecorationView: View

    /**
     * 半沉浸模式的菜单背景 view - 顶部
     */
    private var halfImmersiveTopMenuBgView: View? = null

    /**
     * 半沉浸模式的菜单背景 view - 底部
     */
    private var halfImmersiveBottomMenuBgView: View? = null

    /**
     * 是否已发送相册提亮通知
     */
    private var hadNotifyScreenBrightness = false

    /**
     * 是否页面退出
     */
    private var isPageTransitionExit = false

    /**
     * 是否页面进入
     */
    private var isPageTransitionEnter = false

    private val pagerSection: PhotoPagerSection? get() = sectionPage.pageInstance.requireSection()

    private val menuSection: PhotoMenuSection? get() = sectionPage.pageInstance.requireSection()

    private var enterThumbnail: Bitmap? = null

    /**
     * [IColorManagementAbility]配置能力的对象，避免频繁创建
     */
    private var colorManagementAbility: IColorManagementAbility? = null

    /**
     * 存储view及对应的动画用于及时取消
     */
    private val viewTransitionAnimations: MutableMap<View, PhotoAlphaSpringAnimation> = mutableMapOf()

    /**
     * 设置window透明的Job
     */
    private var translucentSettingJob: Job? = null

    /**
     * 当前activity是否为透明状态
     */
    private val isCurrentlyTranslucent: AtomicBoolean = AtomicBoolean(false)

    /**
     * 获取大图页的约束矩形，即大图页的显示范围。
     * TODO 待调整为由[PhotoPager]控件提供
     */
    val onGetPageConstraintRect = {
        // isInitialized 不能放入内联函数中
        if (::pageContainer.isInitialized.not()) {
            Rect()
        } else {
            val visibleRect = Rect()
            if (::photoPager.isInitialized) {
                visibleRect.set(photoPager.currentSlotVisibleRect)
            } else {
                pageContainer.getGlobalVisibleRect(visibleRect)
            }
            visibleRect
        }
    }

    /**
     * 获取大图页的当前slot的内容显示大小
     * TODO 初始进入大图，由于获取时机的不确定性，可能导致获取到的区域不准确
     */
    val currentSlotContentRect: Rect = Rect()
        get() = field.apply(::loadCurrentSlotVisibleRect)

    /**
     * 大图过渡动画执行器
     *
     * TODO Cocoonshu 这不是最佳方案：
     * 1. 等待focusThumbnail: LiveData回调
     * 2. focusThumbnail如果准备好了，则说明focusViewData也准备好了
     * 3. 此时获取pagerContainer的visibleRect作为约束框，focusViewData中获取缩图尺寸
     *    利用GifAvatarZoomOverrider来计算缩图的targetRect
     * 4. 开始缩图动画
     *
     * - *GifAvatarZoomOverrider应该改名为ContentZoomOverrider，以cover所有需要修改Zoom参数的管理器
     *    不仅仅针对Gif使用*
     *
     * 更优的方案：
     *   - 抽离一个页面动画SDK
     *   - 这个SDK支持的特性：
     *     1. 支持一个过渡缩略图从起始界面过渡到目标界面
     *        - 此动画支持直接设置缩图View来让SDK施加动画
     *        - 也可以直接监听缩图动画属性的变化自行施加到需要做动画的组件上
     *     2. 支持起始界面退出的进度
     *        - 此值为一个[1.0, 0.0]的进度，可使用它来实现起始页面的透明度变化等动画
     *     3. 支持目标界面进入的进度
     *        - 此值为一个[0.0, 1.0]的进度，可使用它来实现目标页面的透明度变化等动画
     *     4. 支持动画启动的条件等待
     *        - 可以为动画设置一个等待条件及等待时间，启动动画后，
     *          - 如果在等待时间内没有满足等待条件，则在等待时间结束时动画启动
     *          - 如果在等待时间内满足了等待条件，则立刻启动动画
     */
    private val transitionManager by lazy {
        // 确定过渡动画采用的动画策略：NORMAL or RENDER。V以上默认RENDER。
        val isFromOther = sectionPage.pageViewModel.inputArguments.invokeFrom.value?.fromExternal == true
        val animStrategy = TransitionHelper.getStrategyByConfig(isFromOther)
        GLog.i(TAG, LogFlag.DL) { "[transitionManager] animationStrategy = ${animStrategy.name} isFromOther = $isFromOther" }

        val previewTransition = PreviewTransition(transitionView, animStrategy).apply {
            if (sectionPage.pageInstance.requireSection<PhotoBrightenSectionCompatLHDR>() != null) {
                // 创建进入和退出大图页时，Local HDR效果的动效处理器，仅支持LHDR不支持UHDR的版本适用
                val transition = BrightenPreviewTransitionCompatLHDR(
                    transitionView, viewModel, this@PhotoContainerSection
                )
                previewPosture = transition.viewPosture
            }
        }
        PhotoPageTransitionManager(
            pageInstance = sectionPage.pageInstance,
            previewTransition = previewTransition,
            backgroundColorTransition = BackgroundColorTransition(pageContainer, animStrategy),
            onGetConstraintRect = onGetPageConstraintRect,
            PhotoPageTransitionAdapter(sectionPage = sectionPage).apply {
                setOnGetConstraintRect(onGetPageConstraintRect)
                setPreviewTransition(previewTransition)
            }
        )
    }

    /**
     * 亮度一致性操作器
     */
    private val brightenUniformityOperator: IScreen.IBrightenUniformityOperator?
        get() = context?.withAbility<IHardwareAbility, IScreen.IBrightenUniformityOperator?> { hardwareAbility ->
            hardwareAbility?.screen?.getBrightenUniformityOperator()
        }

    /**
     * home按键监听
     */
    private val homeListener: HomeRecentListener by lazy {
        HomeRecentListener(arrayOf(HomeRecentListener.REASON_HOME_KEY)) {
            recoverDefaultScreenBrightnessIfNeeded(true)
        }
    }

    /**
     * 底部显示导航动画
     */
    private val naviBarShowAnimator: WindowInsetsFadeInAnimator? by lazy {
        WindowInsetsFadeInAnimator()
    }

    /**
     * 底部隐藏导航动画
     */
    private val naviBarHideAnimator: WindowInsetsFadeOutAnimator? by lazy {
        WindowInsetsFadeOutAnimator()
    }

    /**
     * 是否已经通知过第一帧被渲染过了
     *
     * 注意：这是一个 UI 的过程变量，如果要把该变量放到 vm，一定要切换暗色模式验证下效果，避免切换暗色后，状态不对
     */
    private var isSeamlessTransitionHided: Boolean = false
    private var isIntegrationUITransitionHided: Boolean = false

    /**
     * 记录上一次的 Configuration.uiMode
     * @see onConfigurationChanged
     */
    private var lastUiMode: Int? = null

    /**
     * 当遮罩层显示一段时间后，超时依然未消失，则执行此Runnable
     */
    private val disappearTransitionViewRunnable: Runnable by lazy {
        Runnable {
            GLog.d(TAG) { "[disappearTransitionViewRunnable] show TransitionView timeout, need to disappear" }
            doDisappearTransitionView()
        }
    }

    /**
     * diffedFocus流：只关心SUPPORT_IS_YUV_FORMAT变化的情况
     */
    private val diffedFocusYuvFormatDistinctFlow by lazy {
        viewModel.dataLoading.diffedFocusViewData.distinctUntilChanged { _, diffed ->
            // 只关心SUPPORT_IS_YUV_FORMAT变化的情况
            diffed?.isCertainSupportAbilitiesChanged(SUPPORT_IS_YUV_FORMAT) != true
        }
    }

    /**
     * diffedFocus流：只关心targetViewDataId和focusId能对的上的情况
     */
    private val diffedFocusTargetIdFilterFlow by lazy {
        viewModel.dataLoading.diffedFocusViewData.filter {
            // 只关心targetViewDataId和focusId能对的上的情况
            (targetViewDataId != null) && (targetViewDataId == it?.newItem?.id)
        }
    }

    /**
     * 是否为低端机
     */
    private val isLowPerformanceDevice: Boolean by lazy {
        ConfigAbilityWrapper.getBoolean(FEATURE_IS_LOW_PERFORMANCE_DEVICE)
    }

    /**
     * 自动旋转开关状态监听
     *
     * 引入原因：
     * 非一体化UI的版本，在相机设置“照片查看方向”为“跟随系统设置”时，requestedOrientation是SCREEN_ORIENTATION_PORTRAIT，
     * 进入相册后，打开自动旋转开关，相册不会跟随系统旋转，此问题在相册端进行兼容修改，相机保证好一体化版本的即可
     */
    private val autoRotateModeObserver: ContentObserver by lazy {
        object : ContentObserver(Handler(Looper.getMainLooper())) {
            override fun onChange(selfChange: Boolean) {
                notifyAutoRotateModeChanged()
            }
        }
    }

    /**
     * PhotoPager 的 Item 动画器是否被禁用，
     * 用于解决 Pager 在插入新 item 时切换 focus，会闪 focus 的前一张大尺寸图的问题。
     */
    private var isPagerItemAnimatorDisabled: Boolean = false

    /**
     * 操作 PhotoPager Item Animator 相关方法的锁，
     * 范围：只用于禁用和恢复 Item Animator
     */
    private val pagerItemAnimatorLock: Any = Object()

    /**
     * 用于在 Pager 滑动结束后恢复 ItemAnimator 的回调
     * 仅用于 [disablePhotoPagerItemAnimator] 和 [enablePhotoPagerItemAnimator] 两个方法，
     *
     * 经自测 onPageScrollStateChanged 在此场景下不会回调，需使用 onPageScrolled 做判断。
     * 所以注册回调时需先调用 pageScrollFinishCallback.resetCallbackState() 重置状态.
     */
    private val pageScrollFinishCallback = object : ViewPager.OnPageChangeCallback() {
        private var isFirstSkipped: Boolean = false

        override fun onPageScrollStateChanged(state: Int) = Unit

        override fun onPageSelected(position: Int) = Unit

        override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
            GLog.d(TAG, LogFlag.DL) {
                "[pageScrollFinishCallback] onPageScrolled, " +
                        "position: $position, positionOffset: $positionOffset, ifFirstSkipped: $isFirstSkipped"
            }
            // 滑动中则跳过
            if (positionOffset != 0f) return

            // 回大图时会先跳到当前位置再跳到目标位置，所以跳过第一次 onPageScrolled。
            if (isFirstSkipped.not()) {
                isFirstSkipped = true
            } else {
                GLog.d(TAG, LogFlag.DL) { "[pageScrollFinishCallback] enable ItemAnimator" }
                enablePhotoPagerItemAnimator()
                isFirstSkipped = false
            }
        }

        fun resetCallbackState() {
            isFirstSkipped = false
        }
    }

    /**
     * 当滑动状态的回调超时/未执行成功时，由此 Runnable 来恢复 ItemAnimator
     * 仅用于 [disablePhotoPagerItemAnimator] 和 [enablePhotoPagerItemAnimator] 两个方法
     */
    private val restorePagerItemAnimatorFallback: Runnable by lazy {
        Runnable {
            GLog.d(TAG, LogFlag.DL) { "[restorePagerItemAnimatorFallback] enable ItemAnimator" }
            enablePhotoPagerItemAnimator()
        }
    }

    /**
     * 控制详情页展开和收缩，展开时，隐藏其他组件，收缩时，显示其他组件
     */
    private val detailViewModeControl by lazy {
        DetailViewModeControl()
    }

    override fun onCreate() {
        super.onCreate()
        subscribeLiveDataFromViewModel()
        sectionPage.pageInstance.activity?.let { activity ->
            sectionPage.pageInstance.getCurrentAppUiConfig().let {
                viewModel.pageManagement.notifyPageSizeChanged(it.windowWidth.current, it.windowHeight.current)
            }
            viewModel.outputChannels.notifyCreateOutputChannel()
            viewModel.pageManagement.notifyPrepareSelfSplittingSupporting(activity)
            viewModel.reactiveFold.observeFoldingFeatureFlow(activity)
        }
    }

    override fun onViewCreated(view: View) {
        super.onViewCreated(view)
        colorManagementAbility = viewModel.context.getAppAbility<IColorManagementAbility>()
        initView(view)
    }

    private fun initView(view: View) {
        pageContainer = view
        transitionView = view.findViewById(R.id.transition_view)
        photoPager = view.findViewById(R.id.photo_view)
        bottomDecorationView = view.findViewById(R.id.bottom_decoration)
        topDecorationView = view.findViewById(R.id.tool_bar_container)

        halfImmersiveTopMenuBgView = pageContainer.findViewById<View>(R.id.top_half_immersive_bg).also {
            applyHalfImmersiveBackground(it)
        }
        halfImmersiveBottomMenuBgView = bottomDecorationView.findViewById<View>(R.id.bottom_half_immersive_bg).also {
            applyHalfImmersiveBackground(it)
        }
    }

    private fun applyHalfImmersiveBackground(view: View) {
        view.background = GradientDrawable(
            GradientDrawable.Orientation.TOP_BOTTOM,
            intArrayOf(
                Color.BLACK,
                ColorUtils.setAlphaComponent(Color.BLACK, ALPHA_PERCENT_60),
                ColorUtils.setAlphaComponent(Color.BLACK, ALPHA_PERCENT_30),
                Color.TRANSPARENT,
            )
        )
    }

    override fun onStart() {
        super.onStart()
        sectionPage.pageInstance.activity?.let { activity ->
            viewModel.outputChannels.notifyStartOutputChannel(activity)
            homeListener.register(activity)
        }
        notifyScreenBrightness()
        // 记录发送提亮flag的行为
        PhotoContainerBrightnessManager.recordEnableBrightness(this)
    }

    override fun onResume() {
        super.onResume()
        setupSystemBar()
        sectionPage.pageInstance.activity?.let { activity ->
            viewModel.outputChannels.notifyResumeOutputChannel(activity)
        }
        notifyScreenBrightenUniformity()
        sectionPage.pageViewModel.pageManagement.notifyShouldSetOpaqueOnPause(false)
        if ((isCurrentlyTranslucent.get().not())
            && (viewModel.inputArguments.features.value?.isTransparentWindowEnabled == true)
        ) {
            setWindowTransparent(isWindowTransparent = true, delayTime = WINDOW_TRANSITION_ANIMATION_DELAY_MS)
        }
    }

    override fun onPause() {
        super.onPause()
        GLog.d(TAG) { "onPause" }
        sectionPage.pageInstance.activity?.let { activity ->
            viewModel.outputChannels.notifyPauseOutputChannel(activity)
        }
        transitionManager.pause()
        recoverScreenBrightenUniformityIfNeed()
        if ((isCurrentlyTranslucent.get())
            && (viewModel.inputArguments.features.value?.isTransparentWindowEnabled == true)
            && (viewModel.details.isInDetailsMode.value == true)
            && (viewModel.pageManagement.shouldSetOpaqueOnPause)
        ) {
            setWindowTransparent(isWindowTransparent = false)
        }
    }

    override fun onStop() {
        super.onStop()
        sectionPage.pageInstance.activity?.let { activity ->
            viewModel.outputChannels.notifyStopOutputChannel(activity)
            homeListener.unregister(activity)
        }
        // 当PhotoContainerBrightnessManager记录有发送多个提亮flag的行为时，不需要发送退亮的flag，避免出现闪屏的问题
        if (PhotoContainerBrightnessManager.isSingleSectionMode()) {
            recoverDefaultScreenBrightnessIfNeeded()
        } else if (viewModel.pageManagement.isUsedCameraBrightness) {
            GLog.d(TAG, "[onStop], [recoverDefaultScreenBrightnessIfNeeded] it is invoked from camera!")
            recoverDefaultScreenBrightnessIfNeeded()
        } else {
            GLog.d(TAG, "[onStop], PhotoContainerSectionMode is MULTIPLE!")
        }
        // 记录发送退亮flag的行为
        PhotoContainerBrightnessManager.recordDisableBrightness(this)
    }

    private fun notifyScreenBrightness() {
        // 如果支持进入相册亮度一致性，则无需在大图页控制屏幕亮度
        if (viewModel.inputArguments.features.value?.isSupportBrightenUniformity == true) {
            GLog.d(TAG) { "[notifyScreenBrightness] isSupportBrightenUniformity=true, no need control brightness in photo page" }
            return
        }
        GLog.d(TAG) {
            "[notifyScreenBrightness] isBrightnessEnhanceSupported:" +
                    " ${viewModel.inputArguments.features.value?.isBrightnessEnhanceSupported}"
        }
        /**
         * 1.项目提亮策略feature隔离
         * 2.在分屏或者浮窗模式下，不需要做相册提亮
         */
        if ((viewModel.inputArguments.features.value?.isBrightnessEnhanceSupported == true) && isMultiWindowOrFloatingWindow()) {
            GLog.d(TAG) { "[notifyScreenBrightness] is isMultiWindowOrFloatingWindow" }
            return
        }

        sectionPage.pageInstance.activity?.window?.let {
            viewModel.pageManagement.notifyScreenBrightnessControlGained(it)
            hadNotifyScreenBrightness = true
        }
    }

    private fun recoverDefaultScreenBrightnessIfNeeded(isKeyHome: Boolean = false) {
        // 如果支持进入相册亮度一致性，则无需在大图页控制屏幕亮度
        if (viewModel.inputArguments.features.value?.isSupportBrightenUniformity == true) {
            GLog.d(TAG) {
                "[recoverDefaultScreenBrightnessIfNeeded] isSupportBrightenUniformity=true, no need control brightness in photo page"
            }
            return
        }
        val isTransferToInternalPage = viewModel.pageManagement.currentPage.value?.isInternalPage() == true
        val isFinishing = sectionPage.pageInstance.activity?.isFinishing == true
        val shouldKeepBrightnessWhenDestroy = viewModel.inputArguments.features.value?.shouldKeepBrightnessWhenDestroy == true

        GLog.d(TAG) {
            "recoverDefaultScreenBrightnessIfNeeded:finishing = $isFinishing,InternalPage=$isTransferToInternalPage," +
                    "keep=$shouldKeepBrightnessWhenDestroy,isKeyHome=$isKeyHome"
        }
        /**
         * 非销毁状态、大图跳转到相册内部页、非从相机进入、home退出，需要取消提亮。其他则不做处理。
         * 此处用来实现：大图进编辑、桌面、最近人物以及进相册内部页等需要取消提亮
         *            大图返回相机时，不做处理，保持亮度与相机一致(除支持相册单独提亮的项目，大图返回相机时，取消相册提亮)
         */
        if (isFinishing.not() || isTransferToInternalPage || shouldKeepBrightnessWhenDestroy.not() || isKeyHome) {
            viewModel.pageManagement.notifyClearBrightnessMode(ClearBrightnessMode.RESET)
            hadNotifyScreenBrightness = false
        } else {
            /**
             * 返回相机，如不取消提亮，则不会清除BrightnessModeSwitcher
             * 下次再进相册，BrightnessModeSwitche不为null，则会先取消上次亮度，再进行提亮
             * 而取消提亮和提亮的时序不可控，会偶现先提亮，再取消提亮，导致屏幕亮度未提升
             * 故此场景下clear BrightnessModeSwitcher
             */
            viewModel.pageManagement.notifyClearBrightnessMode(ClearBrightnessMode.CLEAR)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        switch10bitColorMode(false)
        sectionPage.pageInstance.activity?.let { activity ->
            viewModel.outputChannels.notifyDestroyOutputChannel(activity)
            viewModel.inputArguments.features.value?.let {
                if ((it.chainFrom == VALUE_CHAIN_FROM_CAMERA) && it.hasIntegrationUITransition.not()) {
                    activity.contentResolver.unregisterContentObserver(autoRotateModeObserver)
                }
            }
        }
        colorManagementAbility?.close()
        colorManagementAbility = null
        viewTransitionAnimations.clear()
    }

    /**
     * 该方法会调用[viewModel.pageManagement]对象更新值的方法，即下方[viewModel.pageManagement]notify的方法
     * 如果需要在其他Section中的onAppUiStateChanged中使用[viewModel.pageManagement]的值需要关注在[SectionSelector]中其他Section与[PhotoContainerSection]加载的顺序
     * 不然会取到错误的值，因为[PhotoContainerSection]的加载顺序在其他Section之后，导致在其他Section中使用[viewModel.pageManagement]的值时，还未初始化或则是旧的值
     */
    override fun onAppUiStateChanged(config: AppUiResponder.AppUiConfig) {
        super.onAppUiStateChanged(config)
        /*
         * 因为 AppUiConfig 只存储了横竖屏信息，而没有旋转角度，
         * 在此处只是利用了这个回调时机，去获取旋转角度。
         * 李主任 有没有一个AppCompat类来兼容获取各版本的rotation
         */
        viewModel.pageManagement.notifyUiRotationChanged(
            rotation = context?.let { ctx ->
                ctx.display?.rotation ?: Surface.ROTATION_0
            } ?: Surface.ROTATION_0
        )

        viewModel.pageManagement.notifyPageSizeChanged(
            config.windowWidth.current, config.windowHeight.current
        )
        updateBrightnessStateIfNeed()
        setupSystemBar()

        // 横屏时不显示底下的移轴模糊背景
        if (config.orientation.current == ORIENTATION_LANDSCAPE) {
            halfImmersiveBottomMenuBgView?.isVisible = false
        }
        onInDetailsModeChanged(viewModel.details.transitionPanelEffectState.value)
    }

    override fun onConfigurationChanged(configuration: Configuration) {
        super.onConfigurationChanged(configuration)

        // 系统亮暗色模式变更时通知大图主题变更
        val newUiModeNight = configuration.uiMode and Configuration.UI_MODE_NIGHT_MASK
        val lastUiModeNight = lastUiMode?.let { it and Configuration.UI_MODE_NIGHT_MASK }
        if (lastUiModeNight != newUiModeNight) {
            viewModel.pageManagement.notifyPhotoPageThemeUpdate()
        }

        lastUiMode = configuration.uiMode
    }

    /**
     * 订阅ViewModel中的数据
     */
    @OptIn(ExperimentalUnsignedTypes::class)
    @Suppress("LongMethod")
    private fun subscribeLiveDataFromViewModel() {
        // 视图可见性
        viewModel.pageManagement.transitionViewVisibility.observe(this) { needVisible ->
            changeTransitionViewVisibility(needVisible)
        }

        // pre Transition视图可见性
        viewModel.pageManagement.preTransitionViewVisibility.observe(this) { needVisible ->
            changePreTransitionViewVisibility(needVisible)
        }

        // 适配PixelFormat图片的显示效果
        diffedFocusYuvFormatDistinctFlow.collect(this@PhotoContainerSection) {
            val viewData = viewModel.dataLoading.diffedFocusViewData.value?.newItem
            val is10BitImage = viewData?.supportedAbilities?.getBoolean(SUPPORT_IS_YUV_FORMAT) ?: false
            switch10bitColorMode(is10BitImage)
        }

        // 检查是否需要隐藏遮罩层
        diffedFocusTargetIdFilterFlow.collect(this@PhotoContainerSection) {
            GTrace.trace({ "$TAG.focusSlotViewData.hide" }) {
                tryDisappearTransitionView(isPageIdle = true)
            }
        }

        // 外部传入的动画信息
        viewModel.pageManagement.enterThumbnailPositionTransition.observe(this) {
            launchEnterTransitionIfReady()
        }

        // 外部传入的缩图信息
        viewModel.pageManagement.transitionPreviewData.observe(this) {
            launchEnterTransitionIfReady()
        }

        // 退出动画信息
        viewModel.pageManagement.exitTransition.observe(this) {
            transitionManager.onExitPageEvent()
        }

        // 页面背景透明度
        viewModel.pageManagement.photoBackgroundAlpha.observe(this) {
            val backgroundDrawable = pageContainer.background
            when {
                (backgroundDrawable is PhotoBackgroundDrawable) &&
                        (backgroundDrawable.animStrategy == TransitionAnimStrategy.RENDER) -> {
                    // 如果大图背景跑的是Render动画，backgroundDrawable本身透明。需要修改的是drawable上renderNode的透明度。
                    backgroundDrawable.setForegroundAlpha(it)
                }

                else -> {
                    // 主线程动画，renderNode透明，backgroundDrawable带颜色不透明。需要修改的是drawable的透明度。
                    pageContainer.background?.alpha = (it * ALPHA_MAX_VALUE).roundToInt()
                }
            }
        }

        viewModel.pageManagement.photoDecorationState.observe(this) { decorationState ->
            when (decorationState) {
                is PhotoDecorationState.Show -> {
                    setupSystemBar()
                    showDecoration(decorationState)
                }

                is PhotoDecorationState.Hide -> {
                    if (decorationState.shouldHideNaviBar) {
                        hideSystemBar()
                    }
                    hideDecoration(decorationState)
                }
            }

            updateImmersiveBgVisibility()
        }

        viewModel.inputArguments.features.observe(this) { features ->
            notifyScreenBrightness()
            if (features.isTransparentWindowEnabled) {
                setPageBackgroundDrawable(ColorDrawable(Color.TRANSPARENT), true)
            }

            if (features.shouldSetTaskDescriptionBgBlack) {
                setPageTaskDescriptionBackgroundBlack()
            }

            //相册内部跳转无需获取requestedOrientation，requestedOrientation是一个binder耗时调用
            val isFromOther = viewModel.inputArguments.inputData.getBoolean(IntentConstant.ViewGalleryConstant.KEY_EXTERNAL)
            if (isFromOther) {
                val expectedScreenOrientation: Int? = features.screenOrientation?.getValidScreenOrientationOrDefault()
                val requestedScreenOrientation: Int? = sectionPage.pageInstance.activity?.requestedOrientation
                GLog.d(TAG) {
                    "[setupScreenOrientationIfNeeded] requestedOrientation = $requestedScreenOrientation , " +
                            "expectedScreenOrientation = $expectedScreenOrientation"
                }
                if (requestedScreenOrientation != expectedScreenOrientation) {
                    expectedScreenOrientation?.let {
                        sectionPage.pageInstance.activity?.requestedOrientation = it
                    }
                }
            }

            // 非一体化UI的版本，注册自动旋转开关监听，让大图可以跟随系统旋转。
            if ((features.chainFrom == VALUE_CHAIN_FROM_CAMERA) && !features.hasIntegrationUITransition) {
                sectionPage.pageInstance.activity?.contentResolver?.registerContentObserver(
                    Settings.System.getUriFor(AUTO_ROTATE_SWITCH), false, autoRotateModeObserver
                )
            }
        }

        viewModel.pageManagement.firstFrameRenderingStatus.observe(this) {
            tryToHideIntegrationUITransition()
            tryToHideSeamlessTransition()
        }
        viewModel.pageManagement.photoPagerVisibility.observe(this) {
            tryToHideIntegrationUITransition()
            tryToHideSeamlessTransition()
        }
        viewModel.pageManagement.isSeamlessTransitionFinish.observe(this) {
            tryToHideSeamlessTransition()
        }

        viewModel.pageManagement.photoPageTransitionState.observe(this) {
            isPageTransitionExit = (it == PageTransitionState.EXITING) || (it == PageTransitionState.DESTROYED)
            isPageTransitionEnter = (it == PageTransitionState.NEW) || (it == PageTransitionState.ENTERING)
            val isPagePresenting = (it == PageTransitionState.ENTERING) || (it == PageTransitionState.PRESENTED)
            val isDefaultImmersiveStyle = viewModel.inputArguments.features.value?.isDefaultImmersiveStyle == true
            if (isPagePresenting && isDefaultImmersiveStyle) {
                // 默认是沉浸式效果
                viewModel.pageManagement.changeImmersionInteractive(true)
            }
        }

        viewModel.playback.playbackInfo.observe(this) {
            if (it == null) return@observe
            updateImmersiveBgVisibility()
        }

        lifecycleScope.launch {
            viewModel.menuControl.menuTheme.collect {
                updateImmersiveBgVisibility()
            }
        }

        viewModel.pageManagement.pageTheme.collect(this@PhotoContainerSection) {
            setupSystemBar()
        }

        observeDetailViewChange()
    }

    /**
     * 是否处于详情页模式变更
     * @param panelContractEffectState panel滑动状态
     */
    private fun onInDetailsModeChanged(panelContractEffectState: PanelContractEffectState? = null) {
        if (panelContractEffectState == null) {
            return
        }
        val isUnderImmersionInteractive = viewModel.pageManagement.isUnderImmersionInteractive.value ?: false
        val panelStructure = getPanelStructure(sectionPage.pageInstance.getCurrentAppUiConfig())
        // 1.竖屏
        val verticalStructure = panelStructure == PanelStructure.VERTICAL_SLIDE_UP_SINGLE
                || panelStructure == PanelStructure.VERTICAL_SLIDE_UP_DOUBLE
        // 2.平板中大屏(是平板，但不是中大屏，如分屏模式下，宽度不足则表现和竖屏手机一致)
        val isTabletLargeScreen = sectionPage.pageInstance.isTablet && isMiddleAndLargeScreen()
        // 3.折叠展开中大屏(满足三个条件才是折叠展开的中大屏，isLargeScreen为展开，isMiddleAndLargeScreen为尺寸判断中大屏)
        val isFoldLargeScreen = sectionPage.pageInstance.isFold && sectionPage.pageInstance.isLargeScreen() && isMiddleAndLargeScreen()
        // 4.夜间(深色)模式
        val isNightMode = context?.let { COUIDarkModeUtil.isNightMode(it) } ?: false
        // 5.是否有虚拟导航栏
        val hasVirtualKey = sectionPage.pageInstance.hasVirtualKey()
        val detailsModeInfo = DetailsModeInfo(
            isUnderImmersionInteractive = isUnderImmersionInteractive,
            verticalStructure = verticalStructure,
            isTabletLargeScreen = isTabletLargeScreen,
            isFoldLargeScreen = isFoldLargeScreen,
            isNightMode = isNightMode,
            hasVirtualKey = hasVirtualKey,
            panelContractEffectState = panelContractEffectState
        )
        detailViewModeControl.onDetailsModeChange(detailsModeInfo)
    }

    /**
     * 订阅详情页中控件状态变化
     */
    private fun observeDetailViewChange() {
        detailViewModeControl.detailsViewStateList.observe(this) { detailViewStateList ->
            detailViewStateList?.forEach { detailViewState ->
                val shouldShow = detailViewState.shouldShow
                when (detailViewState) {
                    is DetailsViewState.StatusBar -> changeStatusBarVisibility(shouldShow, detailViewState.isLightStatusBarAppearance)

                    is DetailsViewState.TopDecorationView -> changeTopDecorationViewVisibility(shouldShow)

                    is DetailsViewState.BottomDecorationView -> changeBottomDecorationViewVisibility(shouldShow)

                    is DetailsViewState.BottomDecorationChildView -> changeBottomDecorationChildViewVisibility(detailViewState, shouldShow)

                    is DetailsViewState.NaviBar -> changeNaviBarVisibility(shouldShow)

                    else -> Unit
                }
            }
        }
        //订阅panel滑动状态
        viewModel.details.transitionPanelEffectState.observe(this) { state ->
            onInDetailsModeChanged(state)
            viewModel.pageManagement.notifyPhotoPageThemeUpdate()
        }
    }

    /**
     * 状态栏显示或隐藏
     * @param shouldShow 是否显示
     * @param isLightStatusBarAppearance 状态栏字体颜色,true-亮色状态-字体黑色显示 false-暗色状态-字体白色显示
     */
    private fun changeStatusBarVisibility(shouldShow: Boolean, isLightStatusBarAppearance: Boolean) {
        if (shouldShow) {
            systemBarController.showStatusBar()
        } else {
            // 隐藏状态栏
            systemBarController.hideStatusBar()
        }
        systemBarController.setStatusBarAppearance(isLight = isLightStatusBarAppearance)
        systemBarController.setSystemBarBehavior(WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE)
    }

    /**
     * 顶部装饰器显示或隐藏
     * @param shouldShow 是否显示
     */
    private fun changeTopDecorationViewVisibility(shouldShow: Boolean) {
        if (canDecorationViewAnimation(topDecorationView, shouldShow).not()) {
            return
        }
        val onStart = { viewModel.menuControl.changeCanClickTopMenu(false) }
        val onEnd = { viewModel.menuControl.changeCanClickTopMenu(true) }
        if (shouldShow) {
            val showDecorationState = PhotoDecorationState.Show(DecorationAnimator(onStart = onStart, onEnd = onEnd))
            topDecorationView.show(
                param = showDecorationState.animator.defaultParam,
                onStart = showDecorationState.animator.onStart,
                onEnd = showDecorationState.animator.onEnd
            )
        } else {
            val hideDecorationState = PhotoDecorationState.Hide(DecorationAnimator(onStart = onStart, onEnd = onEnd), true)
            topDecorationView.hide(
                param = hideDecorationState.animator.defaultParam,
                onStart = hideDecorationState.animator.onStart,
                onEnd = hideDecorationState.animator.onEnd
            )
        }
    }

    /**
     * 底部装饰器显示或隐藏
     * @param shouldShow 是否显示
     */
    private fun changeBottomDecorationViewVisibility(shouldShow: Boolean) {
        if (canDecorationViewAnimation(bottomDecorationView, shouldShow).not()) {
            return
        }
        val showDecorationState = PhotoDecorationState.Show.IMMERSIVE
        val hideDecorationState = PhotoDecorationState.Hide.IMMERSIVE

        if (shouldShow) {
            bottomDecorationView.show(
                param = showDecorationState.animator.defaultParam,
                onStart = {
                    showDecorationState.animator.onStart.invoke()
                    viewModel.menuControl.changeCanClickBottomMenu(true)
                },
                onEnd = showDecorationState.animator.onEnd
            )
        } else {
            bottomDecorationView.hide(
                param = hideDecorationState.animator.defaultParam,
                onStart = {
                    hideDecorationState.animator.onStart.invoke()
                    viewModel.menuControl.changeCanClickBottomMenu(false)
                },
                onEnd = {
                    hideDecorationState.animator.onEnd.invoke()
                    viewModel.menuControl.changeCanClickBottomMenu(true)
                }
            )
        }
    }

    /**
     * 底部装饰器中的子view显示或隐藏
     * @param detailsViewState 底部装饰器中的子view状态
     */
    private fun changeBottomDecorationChildViewVisibility(
        detailsViewState: DetailsViewState.BottomDecorationChildView,
        show: Boolean
    ) {
        val bottomDecorationViewGroup = (bottomDecorationView as? ViewGroup) ?: return
        val otherShow = detailsViewState.childShouldShow
        val excludeViewList = detailsViewState.excludeViewList
        (0 until bottomDecorationViewGroup.childCount)
            .map { bottomDecorationViewGroup.getChildAt(it) }
            .filter { !excludeViewList.contains(it.id) }
            .forEach { child ->
                /**
                 * R.id.bottom_bar_container为底部的菜单栏（分享，收藏，编辑，详情），与缩图轴或其他控件情况不同
                 * 底部菜单栏和其他控件显示隐藏关系不是绑定的，例如，当进入详情时，除了底部菜单栏，其他控件都要隐藏
                 */
                val shouldShow = if (child.id == R.id.bottom_bar_container) show else otherShow
                if (canDecorationViewAnimation(child, shouldShow).not()) {
                    return
                }
                if (shouldShow) {
                    if (detailViewModeControl.detailsVisibleDecorationViews.contains(child)) {
                        val showDecorationState = PhotoDecorationState.Show.IMMERSIVE
                        child.show(
                            showDecorationState.animator.defaultParam,
                            showDecorationState.animator.onStart,
                            showDecorationState.animator.onEnd
                        )
                    }
                } else {
                    detailViewModeControl.detailsVisibleDecorationViews.add(child)
                    val hideDecorationState = PhotoDecorationState.Hide.IMMERSIVE
                    child.hide(
                        param = hideDecorationState.animator.defaultParam,
                        onStart = hideDecorationState.animator.onStart,
                        onEnd = hideDecorationState.animator.onEnd
                    )
                }
            }
    }

    private fun notifyAutoRotateModeChanged() {
        sectionPage.pageInstance.activity?.let { currentActivity ->
            GLog.d(TAG, "[notifyAutoRotateModeChanged] auto rotate changed. requestedOrientation = $SCREEN_ORIENTATION_UNSPECIFIED")
            currentActivity.requestedOrientation = SCREEN_ORIENTATION_UNSPECIFIED
        }
    }

    /**
     * 注册监听 [RenderingStatus] 的渲染监听器 [RenderingStatusListener]
     * 当监听到渲染完成，就尝试去掉遮罩层
     */
    private val photoRenderingStatusListener = object : PhotoSlot.RenderingStatusListener {
        override fun onRenderingStatusChanged(
            position: Int,
            viewData: Any?,
            oldRenderingStatus: PhotoSlot.RenderingStatus,
            newRenderingStatus: PhotoSlot.RenderingStatus
        ) {
            tryDisappearTransitionView()
        }
    }

    /**
     * 注册监听 [ScrollState] 的渲染监听器 [OnPageChangeCallback]
     * 当监听到滚动完成，就尝试去掉遮罩层
     */
    private val pageChangeCallback = object : ViewPager.OnPageChangeCallback() {
        override fun onPageScrollStateChanged(state: Int) {
            tryDisappearTransitionView(state == ViewPager.SCROLL_STATE_IDLE)
        }

        override fun onPageSelected(position: Int) = Unit

        override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) = Unit
    }

    /**
     * 检查是否需要隐藏遮罩层，需满足以下条件：
     * 1. 当前大图焦点，已经切换到指定的目标位置
     * 2. 当前大图图片已显示完成
     * 3. 如果大图页无缩图轴显示，则还需判断pageScrollState是否已经IDLE了。因为无缩图轴时，大图就有切图效果，需要切图效果也执行完成。
     */
    private fun tryDisappearTransitionView(isPageIdle: Boolean = false) {
        val isTargetFocused = targetViewDataId != null && targetViewDataId == viewModel.dataLoading.focusItemViewData?.id
        if (isTargetFocused.not()) {
            GLog.d(TAG) { "[tryDisappearTransitionView] target is not focused, No need to disappear" }
            return
        }

        val focusSlot = viewModel.dataLoading.focusSlot
        val renderingStatus: PhotoSlot.RenderingStatus = pagerSection?.getRenderingStatus(focusSlot) ?: PhotoSlot.RenderingStatus.NoReady
        val isContentShowed = (PhotoSlot.RenderingStatus.ContentReady in renderingStatus)
        if (isContentShowed.not()) {
            GLog.d(TAG) { "[tryDisappearTransitionView] current status is not ready, No need to disappear" }
            return
        }

        val isThumbLineEnabled = viewModel.inputArguments.features.value?.isThumbLineEnabled ?: false
        if (isThumbLineEnabled.not() && isPageIdle.not()) {
            GLog.d(TAG) { "[tryDisappearTransitionView] current page state is not IDLE, No need to disappear" }
            return
        }

        doDisappearTransitionView()
    }

    /**
     * 隐藏遮罩层
     */
    private fun doDisappearTransitionView() {
        GLog.d(TAG) { "[doDisappearTransitionView] do disappear TransitionView start" }
        transitionView.removeCallbacks(disappearTransitionViewRunnable)
        pagerSection?.removeRenderingStatusListener(photoRenderingStatusListener)
        pagerSection?.unregisterOnPageChangeCallback(pageChangeCallback)
        viewModel.pageManagement.changeImmersionInteractive(false)
        changeTransitionViewVisibility(false)
        if (enterThumbnail != null) {
            BitmapPools.recycle(enterThumbnail)
            enterThumbnail = null
        }
    }

    /**
     * 设置transition可见性
     */
    private fun changeTransitionViewVisibility(needVisible: Boolean) {
        GLog.i(TAG) { "[changeTransitionViewVisibility] needVisible:$needVisible" }
        transitionView.isVisible = needVisible
        if (needVisible.not()) {
            transitionView.setImageBitmap(null)
            transitionView.setBackgroundColor(Color.TRANSPARENT)
            targetViewDataId = null
        }
    }

    /**
     * 半沉浸装饰器模式下显示背景，其他模式下不显示，
     * 以及控制顶部菜单是否开启质感模糊（半沉浸时开启，但视频播放时不开启）
     */
    @OptIn(ExperimentalUnsignedTypes::class)
    private fun updateImmersiveBgVisibility() {
        if ((halfImmersiveTopMenuBgView == null) && (halfImmersiveBottomMenuBgView == null)) return

        val state = sectionPage.pageViewModel.pageManagement.photoDecorationState.value?.getOrLog(TAG, "photoDecorationState") ?: return
        val animator = state.animator
        val showDecor = state.shouldShow
        val theme = sectionPage.pageViewModel.menuControl.menuTheme.value

        // 底栏
        halfImmersiveBottomMenuBgView?.let {
            val photoMenuSection = sectionPage.pageInstance.requireSection<PhotoMenuSection>() ?: return
            val isBottomInHalfImmersiveTheme = theme.second == MenuDecorationTheme.HalfImmersive
            val targetVisible = showDecor && isBottomInHalfImmersiveTheme && photoMenuSection.couldShowBottomMenu()
            if (targetVisible == it.isVisible) return@let
            if (targetVisible) {
                it.show(animator.defaultParam)
            } else {
                it.hide(animator.halfImmersiveHideParam, finalVisibility = View.GONE)
            }
        }

        // 顶栏
        halfImmersiveTopMenuBgView?.let {
            val isTopInHalfImmersiveTheme = theme.first == MenuDecorationTheme.HalfImmersive
            val targetVisible = showDecor && isTopInHalfImmersiveTheme
            if (targetVisible == it.isVisible) return@let
            if (targetVisible) {
                it.show(animator.defaultParam)
            } else if (it.isVisible) {
                it.hide(animator.halfImmersiveHideParam, finalVisibility = View.GONE)
            }
        }
    }

    /**
     * 设置pre transition的可见性
     */
    private fun changePreTransitionViewVisibility(needVisible: Boolean) {
        GLog.i(TAG) { "[changePreTransitionViewVisibility] needVisible:$needVisible" }
        notifyPreTransitionHided()
    }

    /**
     * pre transition已经隐藏，通知更新大图container背景和animStrategy
     */
    private fun notifyPreTransitionHided() {
        viewModel.pageManagement.pageBackgroundTransition.value?.let { transition ->
            val endColor = transition.animations.last()
            if (pageContainer.background !is PhotoBackgroundDrawable) {
                pageContainer.background = PhotoBackgroundDrawable(Color.BLACK) // 不是则替换
            }
            val backgroundDrawable = pageContainer.background as PhotoBackgroundDrawable
            backgroundDrawable.setForegroundAlpha(endColor.alpha())
            backgroundDrawable.animStrategy = TransitionAnimStrategy.RENDER
        }
    }

    override fun onResponsePageResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onResponsePageResult(requestCode, resultCode, data)
        loadTransitionInfo(requestCode, data)
    }

    /**
     * 进入大图，加载过渡动画相关信息
     */
    private fun loadTransitionInfo(requestCode: Int, data: Intent?) {
        enterThumbnail = runCatching {
            IntentUtils.getBinderExtra(data, IntentConstant.PicturePageConstant.KEY_TRANSITION_THUMBNAIL)
                ?.let(ITransBitmap.Stub::asInterface)
                ?.bitmap
        }.onFailure {
            GLog.w(TAG) { "[onResponsePageResult] get enterThumbnail onFailure" }
        }.getOrDefault(null)
        targetViewDataId = data?.getStringExtra(IntentConstant.ViewGalleryConstant.KEY_MEDIA_ITEM_PATH)
        // 考虑到从其他页面进入，默认给true，不显示mask
        val isCancelFromEdit = data?.getBooleanExtra(IntentConstant.EditablePhotoConstant.KEY_IS_CANCEL_FROM_EDIT, true)
        GLog.i(TAG) {
            "[onResponsePageResult] hasThumbnail=${enterThumbnail != null}," +
                    "targetId:$targetViewDataId, isCancelFromEdit:$isCancelFromEdit"
        }
        if ((enterThumbnail != null) && (targetViewDataId != null) && isCancelFromEdit != true) {
            disablePhotoPagerItemAnimator()
            pagerSection?.registerOnPageChangeCallback(pageChangeCallback)
            changeTransitionViewVisibility(true)
            val contentVisibleRect: Rect = data?.getParcelableExtra(IntentConstant.ViewGalleryConstant.KEY_MEDIA_ITEM_RECT) ?: Rect()
            transitionView.apply {
                boundsType = BoundsType.SPECIAL
                setSourceBound(Rect(0, 0, 0, 0))
                setImageBitmap(enterThumbnail)
                setDestinationBound(contentVisibleRect)
                setBackgroundResource(com.oplus.gallery.basebiz.R.color.base_black)
            }
            /**
             * 增加超时保护，如果超时还未隐藏遮罩层，则强制隐藏
             * 背景：
             * 在低端机上，由于图片数据量比较多（3w+），清除数据缓存，编辑图片返回大图时，由于数据库查询慢
             * 导致1秒遮罩消失后，大图数据还没更新完成，出现了切图和闪黑的现象
             *
             * 方案：
             * 根据手机性能情况，设置遮罩超时消失时长
             */
            val delayTime = if (isLowPerformanceDevice) {
                DELAY_DISAPPEAR_TRANSITION_VIEW_1300
            } else {
                DELAY_DISAPPEAR_TRANSITION_VIEW_1000
            }
            transitionView.postDelayed(disappearTransitionViewRunnable, delayTime)
            // 这里注册监听就立马会有回调，调用到tryDisappearTransitionView，将enterThumbnail置空，导致闪黑
            pagerSection?.addRenderingStatusListener(photoRenderingStatusListener)
        } else {
            // 从编辑页未修改返回，退出沉浸模式
            viewModel.pageManagement.changeImmersionInteractive(false)
            if (enterThumbnail != null) {
                BitmapPools.recycle(enterThumbnail)
                enterThumbnail = null
            }
        }
    }

    /**
     * 禁用 PhotoPager 的 ItemAnimator，会设置在滑动完成后恢复的回调。
     * 如已禁用则无效
     *
     * 用于解决 Pager 在插入新 item 时切换 focus，会闪 focus 的前一张大尺寸图的问题。
     */
    private fun disablePhotoPagerItemAnimator() {
        synchronized(pagerItemAnimatorLock) {
            if (isPagerItemAnimatorDisabled || (pagerSection == null)) return
            GLog.d(TAG, LogFlag.DL) { "[disablePhotoPagerItemAnimator] disabling" }
            isPagerItemAnimatorDisabled = true
            photoPager.disableItemAnimator()

            // 设置滑动状态回调
            pagerSection?.unregisterOnPageChangeCallback(pageScrollFinishCallback)
            pagerSection?.registerOnPageChangeCallback(pageScrollFinishCallback.apply { resetCallbackState() })

            // 设置超时回调
            photoPager.removeCallbacks(restorePagerItemAnimatorFallback)
            photoPager.postDelayed(restorePagerItemAnimatorFallback, DELAY_RESTORE_PAGER_ITEM_ANIMATOR)
        }
    }

    /**
     * 恢复 PhotoPager 的 ItemAnimator，并移除所有回调。
     * 如当前未禁用则无效
     */
    private fun enablePhotoPagerItemAnimator() {
        synchronized(pagerItemAnimatorLock) {
            if (!isPagerItemAnimatorDisabled) return
            GLog.d(TAG, LogFlag.DL) { "[enablePhotoPagerItemAnimator] restoring" }
            isPagerItemAnimatorDisabled = false
            photoPager.enableItemAnimator()

            // 移除回调
            pagerSection?.unregisterOnPageChangeCallback(pageScrollFinishCallback)
            photoPager.removeCallbacks(restorePagerItemAnimatorFallback)
        }
    }

    /**
     * 一体化 UI 过渡动画：根据接收到的第一帧渲染信息，尝试更新如下内容：
     *
     * - 页面背景（纯黑色），当窗口是透明的，用于遮盖住大图下面的内容
     * - 窗口是否透明（[android.app.Activity.setTranslucent]），
     * 由变量影响 [com.oplus.gallery.photo_page.viewmodel.inputarguments.PhotoInputArgumentsViewModel.Features.hasSeamlessTransition]
     */
    private fun tryToHideIntegrationUITransition() {
        if (isIntegrationUITransitionHided) {
            return
        }

        // 没有相机“一体化 UI 过渡动画”，不做处理
        val hasIntegrationUITransition = viewModel.inputArguments.features.value?.hasIntegrationUITransition ?: false
        if (hasIntegrationUITransition.not()) {
            GLog.d(TAG) { "[tryToHideIntegrationUITransition] hasIntegrationUITransition is false" }
            isIntegrationUITransitionHided = true
            return
        }

        // 无渲染状态，不做处理
        val renderingStatus = viewModel.pageManagement.firstFrameRenderingStatus.value ?: let {
            GLog.w(TAG, "[tryToHideIntegrationUITransition] renderingStatus is null")
            return
        }

        val isPhotoPagerVisible = photoPager.isVisible || (viewModel.pageManagement.photoPagerVisibility.value == true)
        val isFirstFrameRendered = renderingStatus.isContentReady || renderingStatus.isThumbnailReady
        val shouldHideIntegrationUITransition = isFirstFrameRendered && isPhotoPagerVisible

        GLog.d(TAG) {
            "[tryToHideIntegrationUITransition]" +
                    "shouldHideIntegrationUITransition=$shouldHideIntegrationUITransition," +
                    "isPhotoPagerVisible=$isPhotoPagerVisible," +
                    "isFirstFrameRendered=$isFirstFrameRendered, " +
                    "renderingStatus=$renderingStatus"
        }

        if (shouldHideIntegrationUITransition) {
            photoPager.doOnPreDraw {
                setPageBackgroundDrawable(ColorDrawable(Color.BLACK))
            }
            isIntegrationUITransitionHided = true
        }
    }

    /**
     * 无缝衔接过渡动画：根据接收到的第一帧渲染信息，尝试更新如下内容：
     *
     * - 页面背景（纯黑色），当窗口是透明的，用于遮盖住大图下面的内容
     * - 窗口是否透明（[android.app.Activity.setTranslucent]），
     * 由变量影响 [com.oplus.gallery.photo_page.viewmodel.inputarguments.PhotoInputArgumentsViewModel.Features.hasSeamlessTransition]
     * - 无缝衔接 Transition View
     *
     * firstFrameRenderingStatus和photoPagerVisibility/photoPager可见性共同决定隐藏相机TransitionView（相机进相册时）的时机。
     * 否则会出现以下问题：
     * 如果firstFrameRenderingStatus状态满足，但是photoPager不可见，
     * 则会因为隐藏相机TransitionView时设置相册大图Fragment的容器View:pageContainer背景色为黑色，
     * 导致用户看见的是黑色，当photoPager可见时才显示内容，形成了闪黑的bug现象
     * 解决方案：
     * 如果firstFrameRenderingStatus状态满足，但是photoPager不可见，此时不要隐藏相机TransitionView，等到photoPager可见时再去隐藏。
     * 如果photoPager可见，则在firstFrameRenderingStatus状态满足时，直接隐藏相机TransitionView。
     */
    private fun tryToHideSeamlessTransition(firstFrameRenderingStatus: FirstFrameRenderingStatus? = null) {
        if (isSeamlessTransitionHided) {
            return
        }

        // 没有相机“无缝衔接过渡动作”，不做处理
        val hasSeamlessTransition = viewModel.inputArguments.features.value?.hasSeamlessTransition ?: false
        if (hasSeamlessTransition.not()) {
            GLog.d(TAG) { "[tryToHideSeamlessTransition] hasSeamlessTransition is false" }
            isSeamlessTransitionHided = true
            return
        }

        val isSupportPreviewRotate = ConfigAbilityWrapper
            .getBoolean(ConfigID.Business.FeatureSwitch.FEATURE_IS_CAMERA_SUPPORT_PREVIEW_ROTATE)
        // 可能下拉方式已经调用了[notifyTryingHideSeamlessTransition]，此时不需要再隐藏了无缝动画，避免下拉设置黑色背景导致闪黑
        isSeamlessTransitionHided = viewModel.pageManagement.isSeamlessTransitionHided.value ?: false
        if (isSeamlessTransitionHided && isSupportPreviewRotate) {
            GLog.d(TAG, LogFlag.DL) { "[tryToHideSeamlessTransition] isSeamlessTransitionAlreadyHided" }
            return
        }

        // 无渲染状态，不做处理
        val renderingStatus = firstFrameRenderingStatus
            ?: viewModel.pageManagement.firstFrameRenderingStatus.value
            ?: let {
                GLog.w(TAG, "[setupFirstFrameRenderingStatusIfNeeded] renderingStatus is null")
                return
            }

        val isPhotoPagerVisible = photoPager.isVisible || (viewModel.pageManagement.photoPagerVisibility.value == true)
        val isFrameRenderError = renderingStatus.isError
        val isFirstFrameRendered = renderingStatus.isContentReady || renderingStatus.isThumbnailReady
        val isSeamlessTransitionFinish = viewModel.pageManagement.isSeamlessTransitionFinish.value == true
        GLog.d(TAG) {
            "[tryToHideSeamlessTransition] isPhotoPagerVisible=$isPhotoPagerVisible " +
                    "isFirstFrameRendered=$isFirstFrameRendered isFrameRenderError=$isFrameRenderError " +
                    "isSeamlessTransitionFinish=$isSeamlessTransitionFinish isSupportPreviewRotate=$isSupportPreviewRotate"
        }
        /**
         * 1.只要渲染状态是ERROR,则不管是否渲染过，是否PhotoPager可见，都应该隐藏相机TransitionViw.
         * 2.即使第一帧已经渲染，但是如果PhotoPager不可见，则不允许继续执行，
         * 否则会造成闪黑（Fragment容器View背景色为黑色，但是内容PhotoPager不可见），等待PhotoPager可见后再次驱动此处隐藏相机TransitionViw.
         */
        val shouldHideSeamlessTransition =
            (isFirstFrameRendered && isPhotoPagerVisible && isSeamlessTransitionFinish) || isFrameRenderError
        if (shouldHideSeamlessTransition) {
            GLog.d(TAG) { "[tryToHideSeamlessTransition] go, renderingStatus=$renderingStatus" }

            /**
             * 如果有无缝切换动画（相机进入相册的无缝动画，查看下述场景），当第一帧显示出来后，为避免显示需要及时将背景设为黑色，
             * 同时通知做动画的一方（相机）及时隐藏 Transition View。否则，第一帧显示出来后，下拉、滑动会透出来下面的 Transition View
             *
             * 注意：通知做动画的一方（相机）隐藏 Transition View 时，一定要等一帧（等 Page 背景设为黑色），否则，可能会有其他闪烁问题
             *
             * 补充：
             *  上面就算等一帧，也会存在闪烁的问题，原因如下
             *  - 相册调用[setPageBackgroundDrawable]将背景设置为黑色，但RendererThread在渲染时会有概率被卡主，导致并没有真正将
             *    黑色背景Draw出来。
             *  - 这个的卡顿主要是相机进相册时，会存在帧率切换，该场景下会导致SF卡主，从而导致RendererThread绘制时卡主。
             *  - 目前暂无较好的方案监听RendererThread真正渲染完成，先通过延时[HIDE_SEAMLESS_TRANSITION_DELAY_TIME]降低概率。
             *
             * 场景：
             *
             * - 相机进入相册大图，相机会做进入大图的放大动画，做完之后定在那里不消失，然后启动大图
             * - 大图启动中，大图的 Activity 没有动画，并且是透明的
             * - 当大图启动完成并显示图片之后，就可以通知相机隐藏这个无缝切换的 Transition View
             */
            photoPager.doOnPreDraw {
                GTrace.traceBegin("setPageBackgroundDrawable_BLACK")
                /**
                 * bug 7286833，如果相册第一帧还没有渲染出来，就收到了back事件，相册直接退出，渲染状态为ERROR，此时设置背景为黑色，
                 * 隐藏Transition View，存在闪黑的情况，此场景增加判断只有在渲染状态不为ERROR或不是来自相机时才设置背景为黑色。
                 * 目前只有在收到back事件并且第一帧还未渲染时会设置FirstFrameRenderingStatus.ERROR。
                 */
                val isFromCameraChain = viewModel.inputArguments.features.value?.chainFrom == VALUE_CHAIN_FROM_CAMERA
                GLog.d(TAG, LogFlag.DL) { "[tryToHideSeamlessTransition] isFromCameraChain: $isFromCameraChain" }
                if (isFrameRenderError.not() || isFromCameraChain.not()) {
                    setPageBackgroundDrawable(ColorDrawable(Color.BLACK))
                }
                GTrace.traceEnd()
                val hidingDelayTime = when {
                    isFrameRenderError -> 0L
                    isSupportPreviewRotate.not() && ConfigAbilityWrapper.getBoolean(
                        ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_DELAY_HIDE_SEAMLESS_TRANSITION_BY_MODEL_100,
                        defValue = false
                    ) -> HIDE_SEAMLESS_TRANSITION_DELAY_TIME_100

                    isSupportPreviewRotate && ConfigAbilityWrapper.getBoolean(
                        ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_DELAY_HIDE_SEAMLESS_TRANSITION_BY_MODEL_1500,
                        defValue = false
                    ) -> HIDE_SEAMLESS_TRANSITION_DELAY_TIME_1500

                    else -> HIDE_SEAMLESS_TRANSITION_DELAY_TIME_50
                }
                photoPager.postOnAnimationDelayed({
                    viewModel.pageManagement.notifyTryingHideSeamlessTransition()
                    isSeamlessTransitionHided = true
                }, hidingDelayTime)
            }
        }
    }

    private fun switch10bitColorMode(switchOn: Boolean) {
        sectionPage.pageInstance.window?.let {
            colorManagementAbility?.switch10bitColorMode(it, switchOn)
        }
    }

    private fun launchEnterTransitionIfReady() {
        // 如果进入大图过渡动画入参还未准备好，则先搁置、等待
        viewModel.pageManagement.enterThumbnailPositionTransition.value ?: let {
            GLog.d(TAG) { "[launchEnterTransitionIfReady] waiting, not ready of enterThumbnailPositionTransition" }
            return
        }

        val transitionPreviewData = viewModel.pageManagement.transitionPreviewData.value ?: let {
            GLog.d(TAG) { "[launchEnterTransitionIfReady] waiting, not ready of transitionPreviewData" }
            return
        }

        val thumbnail = transitionPreviewData.drawable ?: let {
            GLog.d(TAG) { "[launchEnterTransitionIfReady] waiting, not ready of thumbnail" }
            return
        }

        if (thumbnail is ColorDrawable) {
            GLog.d(TAG) { "[launchEnterTransitionIfReady] thumbnail is a ColorDrawable! EnterAnimation might not appear!" }
        }

        //入场动画被提前到点击缩略图，则大图自身无需再启动入场动画
        if ((viewModel.inputArguments.features.value?.hasPreTransition == true)
            && (viewModel.inputArguments.invokeFrom.value?.fromSelfSplit == false)
        ) {
            GLog.d(TAG) { "[launchEnterTransitionIfReady] hasPreTransition return it." }
            return
        }

        val displaySize = Size(pageContainer.width, pageContainer.height).also {
            if ((pageContainer.width <= 0) || (pageContainer.height <= 0)) {
                pageContainer.doOnNextLayout {
                    GLog.d(TAG) { "launchEnterTransitionIfReady:pageContainer.doOnNextLayout" }
                    //下一次layout后进行尝试，避免无缝动画时，启动太慢导致闪屏
                    launchEnterTransitionIfReady()
                }
                return
            }
        }

        GLog.d(TAG) { "[launchEnterTransitionIfReady] emit event: transitionPreviewData=$transitionPreviewData" }
        transitionManager.onEnterPageEvent(thumbnail) {
            createEnterPageTransitionRegion(transitionPreviewData, displaySize)
        }
    }

    private fun createEnterPageTransitionRegion(
        transitionPreviewData: TransitionPreviewData,
        displaySize: Size,
    ): Rect {
        val propertyProvider = createAnimationPropertyProvider(
            contentSize = transitionPreviewData.size,
            displaySize = displaySize
        )

        val targetRect = propertyProvider.let { provider ->
            PhotoSlotSizeCalculator().overrideSlotAnimationPropertiesIfNeed(transitionPreviewData, provider)

            val centerX = pageContainer.width / 2
            val centerY = pageContainer.height / 2
            val scale = provider.minZoomScale?.invoke() ?: ImageUtils.scaleImage(
                transitionPreviewData.size.width.toFloat(), transitionPreviewData.size.height.toFloat(),
                pageContainer.width.toFloat(), pageContainer.height.toFloat(),
                ImageUtils.SCALE_MODE_INSIDE
            )

            val halfWidth = (transitionPreviewData.size.width * scale / 2).toInt()
            val halfHeight = (transitionPreviewData.size.height * scale / 2).toInt()
            Rect(
                centerX - halfWidth,
                centerY - halfHeight,
                centerX + halfWidth,
                centerY + halfHeight
            )
        }

        return targetRect
    }

    override fun onInterceptBackPressed(): Boolean {
        if ((viewModel.inputArguments.features.value?.hasSeamlessTransition == true) &&
            (viewModel.pageManagement.isSeamlessTransitionFinish.value != true)
        ) {
            // 包含无缝动画 && 无缝动画还未结束，禁止返回(动画过程中返回会出现闪：结束动画的起始位置和相机无缝动画当前位置未对齐)
            GLog.w(TAG) { "[onInterceptBackPressed] camera seamless has not finish, skip onBackPressed" }
            return true
        }
        return super.onInterceptBackPressed()
    }

    override fun onBackPressed(isBackHandled: Boolean): Boolean {
        if (isBackHandled) {
            GLog.e(TAG) { "[onBackPressed] skip, isBackHandled=true" }
            return super.onBackPressed(isBackHandled)
        }

        val isPreTransitionEntering = sectionPage.pageViewModel.pageManagement.isPreTransitionEntering()
        if (isPreTransitionEntering) {
            GLog.d(TAG, LogFlag.DL, "[onBackPressed] isPreTransitionEntering..")
            sectionPage.pageViewModel.pageManagement.notifyPreTransitionDoExitWhenEnterTransition()
            sectionPage.pageInstance.exitExecutor.exit()
            return true
        }

        val isSeamlessTransitionHided = viewModel.pageManagement.isSeamlessTransitionHided.value ?: false
        if (isSeamlessTransitionHided.not()) {
            // 这里不要阻塞返回按键，如果图片始终无法加载好，则返回键会永久失灵；此处主动隐藏相机 TransitionView，至多闪一下黑
            GLog.e(TAG) { "[onBackPressed] force hiding SeamlessTransition" }
            tryToHideSeamlessTransition(FirstFrameRenderingStatus.ERROR)
        }

        sectionPage.pageViewModel.pageManagement.notifyFinishPageRequested()

        requestFinishPage(
            transitionType = PhotoPageTransitionManager.TransitionType.AUTO_TRANSITION,
            transitionStartPosition = currentSlotContentRect,
            trackActionData = PictureTrackConstant.Value.VIDEO_PLAYER_FINISH_BACK_PRESSED,
            exitTransitionFromType = PhotoPageTransitionManager.ExitTransitionFromType.TYPE_BACK_KEY
        )

        return true
    }

    private fun loadCurrentSlotVisibleRect(outRect: Rect) {
        if (::photoPager.isInitialized.not()) {
            outRect.set(onGetPageConstraintRect())
        } else {
            outRect.set(photoPager.currentSlotVisibleRect)
        }
        //currentSlotContentRect为0时，返回会有动画异常
        if (outRect.isEmpty) {
            pageContainer.getGlobalVisibleRect(outRect)
            GLog.d(TAG) { "[loadCurrentSlotVisibleRect] currentSlotVisibleRect is empty,use container visible rect($outRect)" }
        }
    }

    /**
     * 设置页面背景
     *
     * @param drawable 设置窗口背景
     * @param isWindowTransparent 设置窗口（Activity）是否透明，仅 [VERSION.SDK_INT] >= [VERSION_CODES.R] 时生效
     */
    private fun setPageBackgroundDrawable(drawable: Drawable? = null, isWindowTransparent: Boolean? = null) {
        pageContainer.background = drawable
        isWindowTransparent?.let {
            setWindowTransparent(it)
        }
    }

    /**
     * 设置窗口透明
     * @param isWindowTransparent 设置窗口（Activity）是否透明
     * @param delayTime 设置透明状态前的延迟时间，单位毫秒，默认为0，即立即设置透明状态
     */
    private fun setWindowTransparent(isWindowTransparent: Boolean, delayTime: Long? = null) {
        translucentSettingJob?.cancel()
        translucentSettingJob = lifecycleScope.launch(Dispatchers.SINGLE_UN_BUSY) {
            delayTime?.let {
                delay(it)
            }
            // 原子更新状态并获取之前的状态
            val previousState = isCurrentlyTranslucent.getAndSet(isWindowTransparent)
            // 状态未变化则不执行实际操作
            if (previousState == isWindowTransparent) {
                return@launch
            }
            GTrace.trace({ "$TAG.setTranslucent" }) {
                sectionPage.pageInstance.activity?.setTranslucent(isWindowTransparent)
            }
        }
    }

    /**
     * 根据外部指定，配置当前的taskDescription
     */
    private fun setPageTaskDescriptionBackgroundBlack() {
        if (ApiLevelUtil.isAtLeastAndroidU()) {
            val taskDescription = ActivityManager.TaskDescription.Builder().apply {
                setBackgroundColor(Color.BLACK)
            }.build()
            sectionPage.pageInstance.activity?.setTaskDescription(taskDescription)
        }
    }

    /**
     * 请求指定转场动画并退出页面
     * @param transitionType 指定过渡动画类型
     * @param transitionStartPosition 指定要复写动画起始位置的Rect，随后退场动画将从指定位置开始
     * @param transitionEndPosition 指定要复写动画结束位置的Rect，随后退场动画将从指定位置截止
     * @param exitTransitionFromType 退出动画的类型  返回键还是下拉返回按键
     * Marked by zhangjisong to lichengli 记得改造 trackActionData，
     * 所有位置以 VIDEO_PLAYER 开头会很奇怪，检查下使用 [requestFinishPage] 的位置
     */
    fun requestFinishPage(
        transitionType: PhotoPageTransitionManager.TransitionType,
        transitionStartPosition: Rect? = null,
        transitionEndPosition: Rect? = null,
        trackActionData: String = EMPTY_STRING,
        exitTransitionFromType: PhotoPageTransitionManager.ExitTransitionFromType
    ) {
        GLog.d(TAG) {
            "[requestFinishPage] " +
                    "transitionType=$transitionType, " +
                    "exitTransitionFromType=$exitTransitionFromType, " +
                    "transitionPosition=[$transitionStartPosition -> $transitionEndPosition]"
        }

        // 埋点： 下拉退出大图
        viewModel.track.trackPlayBackEvent(
            action = IVideoPlayProductTracker.ACTION_FINISH,
            actionData = trackActionData
        )

        transitionManager.requestExitTransitionAndFinishPage(
            transitionType = transitionType,
            transitionStartPosition = transitionStartPosition,
            transitionEndPosition = transitionEndPosition,
            exitTransitionFromType = exitTransitionFromType
        )
    }

    @OptIn(ExperimentalUnsignedTypes::class)
    private fun hideDecoration(decorationState: PhotoDecorationState.Hide) {
        val theme = viewModel.menuControl.menuTheme.value
        val topParam = when {
            // 对半沉浸隐藏动画做特殊处理
            theme.first == MenuDecorationTheme.HalfImmersive -> decorationState.animator.halfImmersiveHideParam
            else -> decorationState.animator.defaultParam
        }
        val bottomParam = when {
            // 对半沉浸隐藏动画做特殊处理
            theme.second == MenuDecorationTheme.HalfImmersive -> decorationState.animator.halfImmersiveHideParam
            else -> decorationState.animator.defaultParam
        }

        topDecorationView.hide(
            topParam,
            onStart = { viewModel.menuControl.changeCanClickTopMenu(false) },
            onEnd = { viewModel.menuControl.changeCanClickTopMenu(true) }
        )
        bottomDecorationView.hide(
            param = bottomParam,
            onStart = {
                decorationState.animator.onStart.invoke()
                viewModel.menuControl.changeCanClickBottomMenu(false)
            },
            onEnd = {
                decorationState.animator.onEnd.invoke()
                viewModel.menuControl.changeCanClickBottomMenu(true)
            },
            onUpdate = {
                if (isPageTransitionExit || (viewModel.pageManagement.isInSlideDown.value == true)) {
                    menuSection?.updateMenuThemeWhenAnimate(null)
                }
            }
        )
    }

    @OptIn(ExperimentalUnsignedTypes::class)
    private fun showDecoration(decorationState: PhotoDecorationState.Show) {
        //在详情页展开场景下，不处理顶部菜单栏；例如，人像景深调节后，会进入非沉浸式，瞬间上滑出详情，待非沉浸式回调后导致顶部菜单栏出现
        if (viewModel.details.isDetailModeByTransition().not()) {
            if ((topDecorationView as? ViewGroup)?.isEmpty() == true) {
                // 顶栏还未添加进容器，需待添加后再动画
                menuSection?.doOnTopMenuPrepared {
                    val transitionState = viewModel.pageManagement.photoPageTransitionState.value ?: NEW
                    if (transitionState <= PageTransitionState.PRESENTED) {
                        GTrace.trace("$TAG.showDecoration.top.topMenuPrepared") {
                            topDecorationView.show(
                                param = decorationState.animator.defaultParam,
                                onStart = {
                                    viewModel.menuControl.changeCanClickTopMenu(true)
                                }
                            )
                        }
                    }
                }
            } else {
                GTrace.trace("$TAG.showDecoration.top") {
                    topDecorationView.show(
                        param = decorationState.animator.defaultParam,
                        onStart = {
                            viewModel.menuControl.changeCanClickTopMenu(true)
                        }
                    )
                }
            }
        }

        GTrace.trace("$TAG.showDecoration.bottom") {
            bottomDecorationView.show(
                param = decorationState.animator.defaultParam,
                onStart = {
                    decorationState.animator.onStart.invoke()
                    viewModel.menuControl.changeCanClickBottomMenu(true)
                },
                onEnd = {
                    decorationState.animator.onEnd()
                    if (viewModel.menuControl.menuTheme.value.any { it.background == Color.TRANSPARENT }) {
                        // 结束显示动画时，强制取消透明
                        menuSection?.updateMenuThemeWhenAnimate(false)
                    }
                },
                onUpdate = {
                    if (viewModel.pageManagement.isInSlideDown.value == true) {
                        menuSection?.updateMenuThemeWhenAnimate(null)
                    }
                }
            )
        }
    }

    /**
     * 判断窗口是否处于分屏或者浮窗模式
     */
    private fun isMultiWindowOrFloatingWindow(): Boolean {
        return (sectionPage.pageInstance.isInMultiWindow() || sectionPage.pageInstance.isFloatingWindowMode())
    }

    /**
     * 分屏/浮窗模式提亮、退亮逻辑和从分屏/浮窗返回相册大图需重新提亮
     */
    private fun updateBrightnessStateIfNeed() {
        // 如果支持进入相册亮度一致性，则无需在大图页控制屏幕亮度
        if (viewModel.inputArguments.features.value?.isSupportBrightenUniformity == true) {
            GLog.d(TAG) { "[updateBrightnessStateIfNeed] isSupportBrightenUniformity=true, no need control brightness in photo page" }
            return
        }
        /**
         * 项目提亮策略feature隔离
         */
        if (viewModel.inputArguments.features.value?.isBrightnessEnhanceSupported == false) {
            GLog.d(TAG) { "[updateBrightnessStateIfNeed] isBrightnessEnhanceSupported is false" }
            return
        }

        val isInMultiWindowMode = sectionPage.pageInstance.isInMultiWindow()
        val isInFloatingWindowMode = sectionPage.pageInstance.isFloatingWindowMode()
        GLog.d(TAG) {
            "[updateBrightnessStateIfNeed] isInMultiWindowMode: $isInMultiWindowMode" +
                    " ,isInFloatingWindowMode: $isInFloatingWindowMode" +
                    " ,hadNotifyScreenBrightness: $hadNotifyScreenBrightness"
        }

        /**
         * 1.如果在进入分屏和浮窗之前，已经给背光系统发过进入flag，则需要发退亮flag
         * 2.从分屏和浮窗返回到相册大图，需要发进入flag
         */
        when {
            (isInMultiWindowMode || isInFloatingWindowMode) -> {
                if (hadNotifyScreenBrightness) {
                    GLog.d(TAG) { "[updateBrightnessStateIfNeed] ClearBrightnessMode.RESET" }
                    viewModel.pageManagement.notifyClearBrightnessMode(ClearBrightnessMode.RESET)
                    hadNotifyScreenBrightness = false
                }
            }

            (hadNotifyScreenBrightness.not()) -> {
                GLog.d(TAG) { "[updateBrightnessStateIfNeed] send screen brightness notify" }
                notifyScreenBrightness()
            }
        }
    }

    /**
     * 亮度一致性 恢复系统亮度
     */
    private fun recoverScreenBrightenUniformityIfNeed() {
        /**
         * 1.进编辑或移动到页面的时候，遮盖当前页面，不恢复系统亮度，保持提亮
         * 2.销毁状态、大图跳转到相册内部页、非从相机进入、home退出，需要取消提亮。其他则不做处理。
         * 此处用来实现：桌面、最近人物以及进相册内部页等需要取消提亮
         *            大图返回相机时，不做处理，保持亮度与相机一致(除支持相册单独提亮的项目，大图返回相机时，取消相册提亮)
         * 3.回到相机时，只清除标志，保持亮度，使用ClearBrightnessMode.CLEAR
         */
        val isFromCameraChain: Boolean = viewModel.inputArguments.features.value?.chainFrom == VALUE_CHAIN_FROM_CAMERA
        val isTransferToEditorPage = viewModel.pageManagement.currentPage.value?.isEditorPage() == true
        val isTransferToMoveToPage = viewModel.pageManagement.currentPage.value?.isMoveToPage() == true
        val isTransferToSynthesisGifPage = viewModel.pageManagement.currentPage.value?.isSynthesisGifPage() == true
        val isTransferToCShotPage = viewModel.pageManagement.currentPage.value?.isCShotPage() == true
        val isTransferToInternalSharePage = viewModel.pageManagement.currentPage.value?.isInternalSharePage() == true
        val shouldIgnore = isTransferToEditorPage || isTransferToMoveToPage ||
                isTransferToSynthesisGifPage || isTransferToCShotPage || isTransferToInternalSharePage
        val shouldKeep = fun(): Boolean {
            return (isFromCameraChain && shouldIgnore).also {
                GLog.d(TAG, LogFlag.DL) {
                    "[recoverScreenBrightenUniformityIfNeed] shouldKeep=$it  shouldIgnore=$shouldIgnore"
                }
            }
        }
        val shouldDoNothing = fun(): Boolean {
            return shouldIgnore.also {
                GLog.d(TAG, LogFlag.DL) {
                    "[recoverScreenBrightenUniformityIfNeed] shouldDoNothing=$it shouldIgnore=$shouldIgnore"
                }
            }
        }
        val shouldReset = fun(): Boolean {
            val isFinishing = sectionPage.pageInstance.activity?.isFinishing == true
            val isTransferToInternalPage = viewModel.pageManagement.currentPage.value?.isInternalPage() == true
            val shouldKeepBrightnessWhenDestroy =
                viewModel.inputArguments.features.value?.shouldKeepBrightnessWhenDestroy == true

            return (isFinishing.not() || isTransferToInternalPage || shouldKeepBrightnessWhenDestroy.not()).also {
                GLog.d(TAG, LogFlag.DL) {
                    "[recoverScreenBrightenUniformityIfNeed] shouldReset=$it isTransferToInternalPage=$isTransferToInternalPage " +
                            "isFinishing=$isFinishing shouldKeepBrightnessWhenDestroy=$shouldKeepBrightnessWhenDestroy"
                }
            }
        }
        brightenUniformityOperator?.detachBrightenUniformity(
            detachOperate = when {
                shouldKeep() -> IScreen.IBrightenUniformityOperator.DetachOperate.KEEP
                shouldDoNothing() -> IScreen.IBrightenUniformityOperator.DetachOperate.DO_NOTHING
                shouldReset() -> IScreen.IBrightenUniformityOperator.DetachOperate.RESET
                else -> IScreen.IBrightenUniformityOperator.DetachOperate.CLEAR
            }
        )
    }

    /**
     * 通知屏幕亮度一致性开启
     */
    private fun notifyScreenBrightenUniformity() {
        val window = sectionPage.pageInstance.activity?.window ?: let {
            GLog.w(TAG, LogFlag.DL) { "[notifyScreenBrightenUniformity] skip, since window is null." }
            return
        }
        val isChainFromCamera = viewModel.inputArguments.features.value?.chainFrom == VALUE_CHAIN_FROM_CAMERA
        GLog.d(TAG, LogFlag.DL) { "[notifyScreenBrightenUniformity] shouldEnable=isChainFromCamera=$isChainFromCamera" }
        brightenUniformityOperator?.attachBrightenUniformity(window, shouldEnable = isChainFromCamera)
    }

    /**
     * 适配导航栏和状态栏
     */
    private fun setupSystemBar() {
        sectionPage.pageInstance.activity?.let { activity ->
            /**
             * 安卓标准的使用windowLayoutInDisplayCutoutMode适配刘海屏
             * 和全屏的效果一样，还会有一些兼容性问题（比如标题栏），我们继续使用全屏方案
             */
            activity.window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or
                    View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
            activity.window.setUiOptions(ActivityInfo.UIOPTION_SPLIT_ACTION_BAR_WHEN_NARROW)

            /*
             * 保证状态栏和导航栏的样式：
             * - 黑色字：当前主题是亮色且非半沉浸式，
             * - 白色字：其他情况
             */
            val isMenuShowing = viewModel.pageManagement.photoDecorationState.value is PhotoDecorationState.Show
            val shouldUseHalfImmersive = menuSection?.shouldUseHalfImmersiveMode()
            val isLightTheme = viewModel.pageManagement.pageTheme.value.statusBarTheme.isLight

            val shouldTopUseHalfImmersive = shouldUseHalfImmersive?.first ?: false
            val isInDetail = viewModel.details.isDetailModeByTransition()

            /**
             * Marked by huangmaowei 状态栏颜色
             * 详情模式下，直接使用pageTheme的statusBarTheme值，系统浅色模式就是黑字isLight=true，系统深色模式就是白字isLight=false，不需要别的参数判断；
             * 当大图预览支持白底，修改pageTheme配置就可以；
             * 非详情模式下，涉及 菜单是否展示，是否沉浸式，当前主题中状态栏的颜色 的判断
             */
            val topUseLightStatusBar = if (isInDetail) {
                isLightTheme
            } else {
                isLightTheme && isMenuShowing && !shouldTopUseHalfImmersive
            }
            systemBarController.setStatusBarColor(Color.TRANSPARENT)
            systemBarController.setStatusBarAppearance(isLight = topUseLightStatusBar)

            val shouldBottomUseHalfImmersive = shouldUseHalfImmersive?.second ?: false
            val bottomUseLightStatusBar = isLightTheme && isMenuShowing && !shouldBottomUseHalfImmersive
            systemBarController.setNaviBarColor(Color.TRANSPARENT)
            systemBarController.setNaviBarAppearance(isLight = bottomUseLightStatusBar)

            // 显示或者隐藏 系统导航栏
            val decorationState = viewModel.pageManagement.photoDecorationState.value
            when {
                // 正在浏览详细页时，隐藏SystemBar，显示NaviBar
                isInDetail -> {
                    // 中大屏布局下保持SystemBar的显示
                    if (isMiddleAndLargeScreen()) showSystemBarIfCould() else hideSystemBar()
                    systemBarController.animateNaviBar(naviBarShowAnimator)
                }

                decorationState is PhotoDecorationState.Show -> showSystemBarIfCould()

                decorationState is PhotoDecorationState.Hide -> {
                    if (decorationState.shouldHideNaviBar) {
                        hideSystemBar()
                    } else {
                        GLog.d(TAG, LogFlag.DL) { "[setupSystemBar] no need to hideNaviBar, cause decoration=$decorationState." }
                    }
                }
            }
        }
    }

    private fun showSystemBarIfCould() {
        if (sectionPage.pageInstance.couldShowSystemBar()) {
            systemBarController.showStatusBar()
            systemBarController.animateNaviBar(naviBarShowAnimator)
        } else {
            /**
             * 高度小于360dp的情况下状态栏和导航栏的显示逻辑如下：
             * 竖屏:
             *     - 状态栏: 隐藏
             *     - 导航栏: 隐藏
             * 横屏:
             *     - 状态栏： 隐藏。
             *     - 导航栏： 如果有虚拟导航键，则显示，无虚拟导航键，隐藏。
             */
            systemBarController.hideStatusBar()
            changeNaviBarVisibility(sectionPage.pageInstance.isLandscape() && sectionPage.pageInstance.hasVirtualKey())
        }
        systemBarController.setSystemBarBehavior(WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE)
    }

    /**
     * 底部导航栏显示或隐藏
     * @param show true 显示，false 隐藏
     */
    private fun changeNaviBarVisibility(show: Boolean) {
        if (show) {
            // 展示导航栏
            systemBarController.animateNaviBar(naviBarShowAnimator)
        } else {
            // 隐藏导航栏
            systemBarController.animateNaviBar(naviBarHideAnimator)
        }
    }

    private fun hideSystemBar() {
        systemBarController.hideStatusBar()
        if (isPageTransitionExit.not()) {
            systemBarController.animateNaviBar(naviBarHideAnimator)
        }
        systemBarController.setSystemBarBehavior(WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE)
    }

    /**
     * 判断是否是中大屏：平板 or 大折叠屏展开态
     */
    private fun isMiddleAndLargeScreen(): Boolean {
        return ScreenUtils.isMiddleAndLargeScreen(sectionPage.pageInstance.requireContext())
    }

    /**
     * 判断能否执行顶部菜单栏，底部菜单栏动效
     * @param view 执行动效的view
     * @param shouldShow 是否显示
     * @return true 可以执行，false 不可以执行（显示状态一致且没有正在执行动效）
     */
    private fun canDecorationViewAnimation(view: View, shouldShow: Boolean): Boolean {
        val isVisible = view.isVisibleAndOpaque()
        val animation = viewTransitionAnimations[view]
        val showing = animation?.isShowing ?: false
        val hiding = animation?.isHiding ?: false
        val animating = showing || hiding
        return (shouldShow == isVisible && animating.not()).not()
    }

    /**
     * [PhotoItemViewData] 的扩展方法 : old和new相比，指定的[PhotoItemViewData.supportedAbilities]是否发生了变化
     */
    private fun DiffedPhotoItemViewData.isCertainSupportAbilitiesChanged(key: String): Boolean {
        if (oldItem == null && newItem == null) return false    // 都为null则视为无变化
        if (oldItem == null || newItem == null) return true     // 一个null一个非null则视为有变化

        return newItem.supportedAbilities[key] != oldItem.supportedAbilities[key]
    }

    private fun View.hide(
        param: PhotoDecorationState.SpringAnimationParam,
        finalVisibility: Int = View.INVISIBLE,
        onStart: (() -> Unit)? = null,
        onEnd: (() -> Unit)? = null,
        onUpdate: (() -> Unit)? = null
    ) {
        //开始动效前，先取消原先动效，再执行新动效
        viewTransitionAnimations[this]?.cancel()
        val animation = viewTransitionAnimations.getOrPut(this) {
            PhotoAlphaSpringAnimation(this)
        }
        animation.hide(
            bounce = param.bounce,
            response = param.response,
            onStart = onStart,
            onEnd = {
                visibility = finalVisibility
                onEnd?.invoke()
                viewTransitionAnimations.remove(this)
            },
            onUpdate = {
                onUpdate?.invoke()
            }
        )
    }

    private fun View.show(
        param: PhotoDecorationState.SpringAnimationParam,
        onStart: (() -> Unit)? = null,
        onEnd: (() -> Unit)? = null,
        onUpdate: (() -> Unit)? = null
    ) {
        //开始动效前，先取消原先动效，再执行新动效
        viewTransitionAnimations[this]?.cancel()

        // 原先动效可能取消会把当前view不可见, 因此要在取消后可见
        visibility = View.VISIBLE
        val animation = viewTransitionAnimations.getOrPut(this) {
            PhotoAlphaSpringAnimation(this)
        }
        animation.show(
            param.bounce,
            param.response,
            onStart,
            onEnd = {
                visibility = View.VISIBLE
                onEnd?.invoke()
                viewTransitionAnimations.remove(this)
            },
            onUpdate = {
                onUpdate?.invoke()
            })
    }

    private companion object {

        private const val TAG = "PhotoContainerSection"
        private const val DELAY_DISAPPEAR_TRANSITION_VIEW_1000 = 1000L
        private const val DELAY_DISAPPEAR_TRANSITION_VIEW_1300 = 1300L
        private const val ALPHA_MAX_VALUE = 255
        private const val DELAY_RESTORE_PAGER_ITEM_ANIMATOR = 1000L

        /**
         * 该延时的作用如下：
         * - 相册调用[setPageBackgroundDrawable]将背景设置为黑色，但RendererThread在渲染时会有概率被卡主，导致并没有真正将
         *   黑色背景Draw出来。
         * - 这个的卡顿主要是相机进相册时，会存在帧率切换，该场景下会导致SF卡主，从而导致RendererThread绘制时卡主。
         * - 目前暂无较好的方案监听RendererThread真正渲染完成，先通过延时[HIDE_SEAMLESS_TRANSITION_DELAY_TIME]降低概率。
         *
         * 注意，这个参数不能设置过大，否则可能会影响相机的O测。
         */
        private const val HIDE_SEAMLESS_TRANSITION_DELAY_TIME_50 = 50L
        private const val HIDE_SEAMLESS_TRANSITION_DELAY_TIME_100 = 100L
        private const val HIDE_SEAMLESS_TRANSITION_DELAY_TIME_1500 = 1500L

        private const val AUTO_ROTATE_SWITCH = "accelerometer_rotation"
        private const val AUTO_ROTATE_SWITCH_ON = 1
        private const val AUTO_ROTATE_SWITCH_OFF = 0

        /**
         * 色值：60% Alpha
         */
        private const val ALPHA_PERCENT_60 = 0x99

        /**
         * 色值：30% Alpha
         */
        private const val ALPHA_PERCENT_30 = 0x4D

        /**
         * 这个延迟是预估值，当前没有办法精确的获取到窗口转场结束，当onResume执行时窗口的转场动画还没有结束，所以立即设置activity的透明依旧会导致展示出相机的页面
         * 所以这里需要再延迟100ms。
         */
        private const val WINDOW_TRANSITION_ANIMATION_DELAY_MS = 100L
    }
}