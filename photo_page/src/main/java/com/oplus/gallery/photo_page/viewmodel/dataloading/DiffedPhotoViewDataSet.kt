/*********************************************************************************
 ** Copyright (C), 2022-2032, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : DiffedViewDataSet.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/02/20
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>               2024/02/20  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.dataloading

import androidx.recyclerview.widget.RecyclerView.Adapter

/**
 * 数据类 : [PhotoViewDataSet] 的变更记录
 *
 * - 请务必保持 [diffResult] [oldVersion] [newDataSet] 三者的匹配
 *
 * @see PhotoViewDataSet
 */
internal data class DiffedPhotoViewDataSet constructor(
    /**
     * 旧版本数据的版本号
     *
     * - [oldVersion] 是计算 [diffResult] 使用的旧数据 [PhotoViewDataSet] 的版本号
     *
     * 注意:
     * - [diffResult] 是 [oldVersion] 和 [newDataSet] 的版本的数据变化结果
     * - 给 [oldVersion] 赋值时,一定要用计算 [diffResult] 的版本号,而不是正在显示的版本号
     */
    val oldVersion: Long = INVALID_VERSION,

    /**
     * 新旧数据的 Diff
     * - 是 [oldVersion] 和 [newDataSet] 的版本的数据的 Diff
     * - 使用 [diffResult] 前,需要校验当前显示的版本号,是否与 [oldVersion] 相同,仅相同时才可使用 [diffResult] 结果
     */
    val diffResult: PhotoDiffResult?,

    /**
     * 新的 [PhotoViewDataSet]
     */
    val newDataSet: PhotoViewDataSet,
) {

    /**
     * 数据是否全量刷新
     *
     * - 若数据是全量刷新,则应该调用 [Adapter.notifyDataSetChanged] 进行全量刷新
     * - 若数据不是全量刷新,则应调用 [dispatchUpdatesTo] 进行 Diff 分发
     */
    val isFullUpdate: Boolean
        get() = (oldVersion == INVALID_VERSION) && (diffResult?.isFullUpdate != false)

    /**
     * 数据是否增量刷新
     *
     * - 若数据是增量刷新,则应调用 [dispatchUpdatesTo] 进行 Diff 分发
     * - 若数据不是增量刷新,则应该调用 [Adapter.notifyDataSetChanged] 进行全量刷新
     */
    val isIncrementalUpdate: Boolean
        get() = isFullUpdate.not()

    /**
     * 获取 [slot] 位置的 [PhotoItemViewData]
     *
     * 仅 [PhotoViewDataSet.dataRange] 范围内的下标可通过该方法获取有效数据
     *
     * @see PhotoViewDataSet.get
     */
    operator fun get(slot: Int): PhotoItemViewData? = newDataSet[slot]

    /**
     * 解析 [diffResult] ,分发至 [callback]
     * - [dispatchUpdatesTo] 前,需要校验当前显示的版本号,是否与 [oldVersion] 相同,仅相同时才可使用 [dispatchUpdatesTo] 进行分发
     */
    fun dispatchUpdatesTo(callback: ListUpdateCallback) {
        diffResult?.dispatchUpdatesTo(callback)
    }

    companion object {
        /**
         * 非法的数据版本号
         */
        private const val INVALID_VERSION = -1L
    }
}

/**
 * [PhotoViewDataSet] 的对比结果
 */
internal data class PhotoDiffResult constructor(

    /**
     * 计算 Diff 事件的旧版本
     */
    val oldVersion: Long = INVALID_VERSION,

    /**
     * 计算 Diff 事件的新版本
     */
    val newVersion: Long,

    /**
     * 对比结果的 Diff 事件
     */
    private val diffEvents: List<DiffEvent> = emptyList()
) {
    /**
     * 数据是否全量刷新
     *
     * - 若数据是全量刷新,则应该调用 [Adapter.notifyDataSetChanged] 进行全量刷新
     * - 若数据不是全量刷新,则应调用 [dispatchUpdatesTo] 进行 Diff 分发
     */
    val isFullUpdate: Boolean
        get() = diffEvents.isEmpty()

    /**
     * 数据是否增量刷新
     *
     * - 若数据是增量刷新,则应调用 [dispatchUpdatesTo] 进行 Diff 分发
     * - 若数据不是增量刷新,则应该调用 [Adapter.notifyDataSetChanged] 进行全量刷新
     */
    val isIncrementalUpdate: Boolean
        get() = isFullUpdate.not()

    /**
     * [PhotoDiffResult] 是否已经被消费
     *
     * [PhotoDiffResult] 应只生效一次,消费前应判断 [PhotoDiffResult] 是否已经被消费,若已经消费过了,则不应再次进行消费
     */
    var isConsumed: Boolean = false
        private set

    /**
     * 消费 [PhotoDiffResult]
     *
     * [PhotoDiffResult] 消费后,应调用此方法,标记该此数据已被消费,放在数据被多次消费
     */
    fun markConsumed() {
        isConsumed = true
    }

    /**
     * 解析 [diffEvents] ,分发至 [callback]
     */
    fun dispatchUpdatesTo(callback: ListUpdateCallback) {
        diffEvents.forEach { diffEvent ->
            when (diffEvent) {
                is DiffEvent.InsertEvent -> callback.onInserted(start = diffEvent.start, count = diffEvent.count)
                is DiffEvent.RemoveEvent -> callback.onRemoved(start = diffEvent.start, count = diffEvent.count)
                is DiffEvent.MoveEvent -> callback.onMoved(from = diffEvent.from, to = diffEvent.to)
                is DiffEvent.ChangeEvent -> callback.onChanged(start = diffEvent.start, count = diffEvent.count)
            }
        }
    }

    companion object {
        /**
         * 非法的版本号
         */
        private const val INVALID_VERSION = -1L
    }
}

/**
 * Diff 事件
 */
internal sealed class DiffEvent {
    data class InsertEvent(val start: Int, val count: Int) : DiffEvent()
    data class RemoveEvent(val start: Int, val count: Int) : DiffEvent()
    data class MoveEvent(val from: Int, val to: Int) : DiffEvent()
    data class ChangeEvent(val start: Int, val count: Int) : DiffEvent()
}

/**
 * Diff 回调
 */
internal interface ListUpdateCallback {
    fun onInserted(start: Int, count: Int)
    fun onRemoved(start: Int, count: Int)
    fun onMoved(from: Int, to: Int)
    fun onChanged(start: Int, count: Int)
}