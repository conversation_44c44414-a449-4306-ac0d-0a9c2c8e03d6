/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PhotoTagGroup.kt
 ** Description:
 **     {DESCRIPTION}
 **
 ** Version: 1.0
 ** Date: 2025-07-28
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>      <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>         2025/07/28  1.0          OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.photo_page.widget.infotag

import android.annotation.SuppressLint
import android.content.Context
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.core.view.children
import androidx.core.view.isNotEmpty
import com.oplus.gallery.foundation.util.display.ScreenUtils.toPx
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.ui.theme.PhotoTagTheme

/**
 * 用于装角标的透明容器，支持单个或多个角标。
 * Alpha 动画时会将 alpha 值分发到 PhotoTagView 上。
 *
 * @param tagViews 子 PhotoTagView 集合
 */
@SuppressLint("ViewConstructor")
internal class PhotoTagGroup(
    context: Context,
    tagViews: Iterable<PhotoTagView>
) : LinearLayout(context) {

    init {
        tagViews.forEach(::addPhotoTagView)
    }

    /**
     * 获取的 tag 为子 view (PhotoTagView) tag 的集合，其 tag 为显示的 content
     */
    override fun getTag(): List<Any?> {
        return children.map { it.tag }.toList()
    }

    override fun setAlpha(alpha: Float) {
        // 此 View 为纯容器，向下分发 alpha 变更（质感适配）
        children.forEach { it.alpha = alpha }
    }

    /**
     * 设置 PhotoTag 的主题，会透传到所有子 view 中
     */
    fun applyTheme(theme: PhotoTagTheme) {
        children.forEach {
            (it as? PhotoTagView)?.theme = theme
        }
    }

    /**
     * 添加一个 PhotoTag，每个 PhotoTag 间会间隔 [ITEMS_MARGIN_SPACE] dp 的间距
     */
    private fun addPhotoTagView(view: PhotoTagView) {
        val layoutParams = (view.layoutParams as? MarginLayoutParams) ?: LayoutParams(
            ViewGroup.LayoutParams.WRAP_CONTENT,
            context.resources.getDimensionPixelSize(R.dimen.photopage_detail_tag_height)
        )
        view.layoutParams = layoutParams.apply {
            if (isNotEmpty()) {
                marginStart = ITEMS_MARGIN_SPACE.toPx
            }
        }

        addView(view)
    }

    companion object {
        /**
         * PhotoTag 间的间隔，单位 dp
         */
        private const val ITEMS_MARGIN_SPACE = 8
    }
}