/*********************************************************************************
 ** Copyright (C), 2022-2032, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : FocusHintDiff.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/02/20
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>               2024/02/20  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.dataloading

/**
 * 数据类 : [PhotoFocusHint] 的变更记录
 */
internal data class DiffedPhotoFocusHint(
    /**
     * 上次使用的 [PhotoFocusHint]
     */
    val oldPhotoFocusHint: PhotoFocusHint? = null,

    /**
     * 本次更新的 [PhotoFocusHint]
     */
    val newPhotoFocusHint: PhotoFocusHint? = null
)