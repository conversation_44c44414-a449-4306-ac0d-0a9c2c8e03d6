/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ThumbnailSeekBar.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/03/29
 ** Author: zhangjisong
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>            <version>        <desc>
 ** ------------------------------------------------------------------------------
 ** zhangjisong                     2022/03/29        1.0
 *********************************************************************************/
package com.oplus.gallery.photo_page.widget.seekbar

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.Resources
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Path
import android.graphics.Rect
import android.util.AttributeSet
import android.util.Size
import android.view.MotionEvent
import android.view.View
import android.view.animation.Interpolator
import androidx.annotation.MainThread
import androidx.dynamicanimation.animation.FloatValueHolder
import com.coui.appcompat.animation.dynamicanimation.COUIDynamicAnimation
import com.coui.appcompat.animation.dynamicanimation.COUISpringAnimation
import com.coui.appcompat.animation.dynamicanimation.COUISpringForce
import com.oplus.animation.DynamicAnimation
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.widget.seekbar.ISeekBar.Companion.max
import com.oplus.gallery.photo_page.widget.seekbar.ISeekBar.Companion.min
import com.oplus.gallery.photo_page.widget.seekbar.ISeekBar.Companion.progress
import com.oplus.gallery.photo_page.widget.seekbar.ThumbnailSeekBarLoader.BitmapData
import com.oplus.gallery.photo_page.widget.seekbar.ThumbnailSeekBarLoader.BitmapSpec
import com.oplus.gallery.photo_page.widget.seekbar.ThumbnailSeekBarLoader.OnBitmapDataLoadedListener
import com.oplus.gallery.photo_page.widget.seekbar.ThumbnailSeekBarLoader.PanelSpecMode
import kotlin.math.absoluteValue
import kotlin.math.max
import kotlin.math.min

/**
 * 带有缩略图的 SeekBar 控件，适用于视频预览进度条等场景。
 *
 * 使用方式和 API 和 [android.widget.SeekBar] 保持一致，如有必要，建议和 [NormalSeekBar] 配合 [ISeekBar] 接口使用，
 * [NormalSeekBar] 也实现了 [ISeekBar] 接口，使用方可以利用多态特性，使用 [ISeekBar] 接口无差别控制 [NormalSeekBar] 和 [ThumbnailSeekBar] 控件。
 *
 *
 * 布局示例：
 * 字符画太难画了，请查看设计图片：docs/assets/seekbar/缩图SeekBar-布局示例.jpg
 *
 *
 * 主要接口：
 *
 * - [sliceGap]：设置或获取缩图小分片之间的间隔
 * - [sliceMaxCount]：设置或获取缩图小分片的最大数量
 * - [sliceWidth]：设置或获取缩图小分片的宽度
 * - [sliceHeight]：设置或获取缩图小分片的高度
 * - [max]：设置或获取 [progress] 进度最大值，计算进度百分比时会使用
 * - [min]：设置或获取 [progress] 进度最小值，计算进度百分比时会使用
 * - [progress]：设置或获取 [progress] 进度当前值，计算进度百分比时会使用
 * - [thumbResourceId]：设置或获取拖拽把手资源 Id
 * - [thumbTopOffset]：拖拽把手基于 [getPaddingTop] 位置的顶部偏移量
 * - [thumbBottomOffset]：拖拽把手基于 [getPaddingBottom] 位置的底部偏移量
 * - [sliceRetrieverFactory]：设置或获取生产缩图小分片的工厂
 * - [onSeekBarChangeListener]：设置或获取监听 [ISeekBar] 动作监听器，触摸 [ISeekBar] 或者调整 [progress] 时使用
 * - [invalidateForce]：强制刷新进度缩图，会重新走获取缩图小分片，然后绘制的流程
 * - [measureMode]：控件的宽高测量模式
 * - [isUserInputEnabled]：是否响应用户输入，即用户触摸事件，此优先级低于外界设置的 [setOnTouchListener]
 * - [isSeekable]：是否可以拖动进度条
 * - [isThumbVisible]：拖拽把手是否可见
 * - [isSeamlessAnimationEnabled]：是否无缝更新 SeekBar 内部缩图背景，默认值为 false
 *
 *
 * xml 属性配置：
 * - [R.styleable.ThumbnailSeekBar_min]：[progress] 最小值 [min]，计算进度百分比时会使用
 * - [R.styleable.ThumbnailSeekBar_max]：[progress] 最大值 [max]，计算进度百分比时会使用
 * - [R.styleable.ThumbnailSeekBar_progress]：当前 [progress] 值，计算进度百分比时会使用
 * - [R.styleable.ThumbnailSeekBar_sliceGap]：缩图小分片之间的间隔 [sliceGap]
 * - [R.styleable.ThumbnailSeekBar_sliceMaxCount]：缩图小分片的最大数量 [sliceMaxCount]
 * - [R.styleable.ThumbnailSeekBar_sliceWidth]：缩图小分片的宽度 [sliceWidth]
 * - [R.styleable.ThumbnailSeekBar_sliceHeight]：缩图小分片的高度 [sliceHeight]
 * - [R.styleable.ThumbnailSeekBar_thumb]：拖拽把手的资源 Id
 * - [R.styleable.ThumbnailSeekBar_thumbTopOffset]：拖拽把手基于 [getPaddingTop] 位置的顶部偏移量
 * - [R.styleable.ThumbnailSeekBar_thumbBottomOffset]：拖拽把手基于 [getPaddingBottom] 位置的底部偏移量
 * - [R.styleable.ThumbnailSeekBar_measureMode]：控件的宽高测量模式 Id
 * - [R.styleable.ThumbnailSeekBar_isUserInputEnabled]：是否响应用户输入，即用户触摸事件，此优先级低于外界设置的 [setOnTouchListener]
 * - [R.styleable.ThumbnailSeekBar_isSeekable]：是否可以拖动进度条
 * - [R.styleable.ThumbnailSeekBar_isThumbVisible]：拖拽把手是否可见
 * - [R.styleable.ThumbnailSeekBar_isSeamlessAnimationEnabled]：是否无缝更新 SeekBar 内部缩图背景，默认值为 false
 *
 *
 * xml 使用属性：
 *
 * ```xml
 * <com.oplus.gallery.photo_page.widget.seekbar.ThumbnailSeekBar
 *     xmlns:android="http://schemas.android.com/apk/res/android"
 *     xmlns:app="http://schemas.android.com/apk/res-auto"
 *     android:id="@+id/sb_playback_progress"
 *     android:layout_width="match_parent"
 *     android:layout_height="match_parent"
 *     android:paddingVertical="5dp"
 *     app:min="0"
 *     app:max="10000"
 *     app:progress="500"
 *     app:sliceGap="3dp"
 *     app:sliceMaxCount="15"
 *     app:sliceWidth="40dp"
 *     app:sliceHeight="40dp"
 *     app:measureMode="viewSize"
 *     app:isUserInputEnabled="true"
 *     app:isSeekable="true"
 *     app:thumbTopOffset="-3dp"
 *     app:thumbBottomOffset="-3dp"
 *     app:thumb="@drawable/picture_video_player_indicator" />
 * ```
 *
 * @see R.styleable.ThumbnailSeekBar
 * @see R.styleable.ThumbnailSeekBar_min
 * @see R.styleable.ThumbnailSeekBar_max
 * @see R.styleable.ThumbnailSeekBar_progress
 * @see R.styleable.ThumbnailSeekBar_sliceGap
 * @see R.styleable.ThumbnailSeekBar_sliceMaxCount
 * @see R.styleable.ThumbnailSeekBar_sliceWidth
 * @see R.styleable.ThumbnailSeekBar_sliceHeight
 * @see R.styleable.ThumbnailSeekBar_thumb
 * @see R.styleable.ThumbnailSeekBar_thumbTopOffset
 * @see R.styleable.ThumbnailSeekBar_thumbBottomOffset
 * @see R.styleable.ThumbnailSeekBar_measureMode
 * @see R.styleable.ThumbnailSeekBar_isUserInputEnabled
 * @see R.styleable.ThumbnailSeekBar_isSeekable
 * @see R.styleable.ThumbnailSeekBar_isThumbVisible
 * @see R.styleable.ThumbnailSeekBar_isSeamlessAnimationEnabled
 *
 * @see ISeekBar
 * @see NormalSeekBar
 * @see android.widget.SeekBar
 */
open class ThumbnailSeekBar @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    defStyleRes: Int = 0,
    /**
     * 是否需要解析StyledAttributes，并重新对[ThumbnailSeekBar]的参数初始化。
     * 如果指定为false，则使用[ThumbnailSeekBar]内部默认值。
     *
     * 具体参数 @see [R.styleable.ThumbnailSeekBar]
     */
    shouldInitByStyleAttr: Boolean = true
) : View(context, attrs, defStyleAttr, defStyleRes), ISeekBar {

    /**
     * 是否无缝更新 SeekBar 内部缩图背景，默认值为 false
     *
     * 当有内容并绘制之后，如果缩图规格（sliceXXX 相关属性）发生变更，会触发重新创建缩图 Bitmap，
     * 一个空内容的 Bitmap 会通知出来绘制，等再次加载后才会有新的 Bitmap 通知绘制。
     * 按照上面流程导致界面上会闪烁：图-无图-图。为解决该问题，故加上无缝切换参数
     */
    var isSeamlessAnimationEnabled: Boolean = false
        @MainThread
        set(value) {
            if (field == value) {
                return
            }
            isBitmapSpecDirty = true
            field = value
            requestLayout()
        }

    /**
     * 设置或获取缩图小分片之间的间隔
     */
    var sliceGap: Int = 0
        @MainThread
        set(value) {
            if (field == value) {
                return
            }
            isBitmapSpecDirty = true
            field = value
            requestLayout()
        }


    /**
     * 设置或获取缩图小分片的最大数量
     */
    var sliceMaxCount: Int = 0
        @MainThread
        set(value) {
            if (field == value) {
                return
            }
            isBitmapSpecDirty = true
            field = value
            requestLayout()
        }


    /**
     * 设置或获取缩图小分片的宽度
     */
    var sliceWidth: Int = 0
        @MainThread
        set(value) {
            if (field == value) {
                return
            }
            isBitmapSpecDirty = true
            field = value
            requestLayout()
        }


    /**
     * 设置或获取缩图小分片的高度
     */
    var sliceHeight: Int = 0
        @MainThread
        set(value) {
            if (field == value) {
                return
            }
            isBitmapSpecDirty = true
            field = value
            requestLayout()
        }


    /**
     * 设置或获取拖拽把手资源 Id，与 [thumbContent] 互斥，当设置此值时，会自动将 [thumbContent] 设置为 null
     *
     * 因为要和 [thumbContent] 互斥，要更新 [thumbContent]，因此需要用 [isUpdatingThumbContent] 进行守卫，防止死循环
     */
    var thumbResourceId: Int = Resources.ID_NULL
        @MainThread
        set(value) {
            if (field == value) {
                return
            }
            if (isUpdatingThumbContent) {
                field = value
                return
            }
            isUpdatingThumbContent = true
            thumbContent = null
            field = value
            setupSeekbarResources()
            invalidate()
            isUpdatingThumbContent = false
        }

    /**
     * 设置或获取拖拽把手 [Bitmap]，与 [thumbResourceId] 互斥，当设置此值时，会自动将 [thumbResourceId] 设置为 [Resources.ID_NULL]
     *
     * 因为要和 [thumbResourceId] 互斥，要更新 [thumbResourceId]，因此需要用 [isUpdatingThumbContent] 进行守卫，防止死循环
     */
    var thumbContent: Bitmap? = null
        @MainThread
        set(value) {
            if (field == value) {
                return
            }
            if (isUpdatingThumbContent) {
                field = value
                return
            }
            isUpdatingThumbContent = true
            thumbResourceId = Resources.ID_NULL
            field = value
            setupSeekbarResources()
            invalidate()
            isUpdatingThumbContent = false
        }

    /**
     * 拖拽把手是否显示
     */
    var isThumbVisible: Boolean = true
        @MainThread
        set(value) {
            if (field == value) {
                return
            }
            field = value
            if (isAttachedToWindow.not()) {
                thumbPaint.alpha = if (value) ALPHA_MAX else 0
                return
            }
            if (isSeamlessAnimationEnabled) {
                startThumbAnimator()
            } else {
                invalidate()
            }
        }

    /**
     * 时间线动效结束监听，成员变量防止重复addEndListener
     */
    private val onThumbAnimationEndListener by lazy {
        COUIDynamicAnimation.OnAnimationEndListener { animation, canceled, value, velocity ->
            //动效结束后重置动效数值,不影响alpha和动效执行；沉浸式场景下，滑动切换视频，将alpha=0后，切换回来没执行动效导致时间线不可见
            thumbAnimationValue.value = ALPHA_DEFAULT
            if (value == ALPHA_HIDE) {
                //隐藏后重置进度，防止下次再次显示时，进度条从原进度闪跳到0
                internalProgress = 0
            }
            invalidate()
        }
    }

    private var animEndListener: COUIDynamicAnimation.OnAnimationEndListener? = null

    private fun startThumbAnimator() {
        val currentAlpha = (thumbPaint.alpha / ALPHA_MAX).toFloat()
        val targetAlpha = if (isThumbVisible) ALPHA_SHOW else ALPHA_HIDE
        if (currentAlpha == targetAlpha) {
            invalidate()
            return
        }
        thumbAnimation.cancel()
        thumbAnimation.apply {
            spring.finalPosition = targetAlpha
            setStartValue(currentAlpha)
            addEndListener(onThumbAnimationEndListener)
            invalidate()
            start()
        }
    }

    /**
     * 拖拽把手基于 [getPaddingTop] 位置的顶部偏移量
     */
    var thumbTopOffset: Int = 0
        @MainThread
        set(value) {
            if (field == value) {
                return
            }
            field = value
            invalidate()
        }


    /**
     * 拖拽把手基于 [getPaddingBottom] 位置的底部偏移量
     */
    var thumbBottomOffset: Int = 0
        @MainThread
        set(value) {
            if (field == value) {
                return
            }
            field = value
            invalidate()
        }

    /**
     * 设置或获取生产缩图小分片的工厂
     *
     * 设置不同的值，行为不同：
     *
     * - 和现有的值相同：忽略设置
     * - 其他或者 null：更新内部值，且刷新界面
     */
    var sliceRetrieverFactory: ISliceRetrieverFactory?
        @MainThread
        set(value) {
            if (value === thumbnailSeekBarLoader.sliceRetrieverFactory) {
                return
            }
            if (value === ISliceRetrieverFactory.EmptySliceRetrieverFactory) {
                thumbnailSeekBarLoader.sliceRetrieverFactory = value
                recycleSeekbarResources(isSeamlessAnimationEnabled.not())
                return
            }
            val shouldReloadSpec = thumbnailSeekBarLoader.sliceRetrieverFactory === ISliceRetrieverFactory.EmptySliceRetrieverFactory
            thumbnailSeekBarLoader.sliceRetrieverFactory = value
            invalidateForce(shouldReloadSpec)
        }
        get() {
            return thumbnailSeekBarLoader.sliceRetrieverFactory
        }

    /**
     * sliceRetriever 插值器。
     *
     * 内部使用 [ISliceRetriever.loadSlice] 取帧时，会使用该 [Interpolator] 影响参数 fraction，间接影响取帧的位置
     */
    var sliceRetrieverInterpolator: Interpolator
        @MainThread
        set(value) {
            thumbnailSeekBarLoader.sliceRetrieverInterpolator = value
            invalidate()
        }
        get() {
            return thumbnailSeekBarLoader.sliceRetrieverInterpolator
        }


    /**
     * [ThumbnailSeekBar] 控件的宽高测量模式
     *
     * - [MeasureMode.VIEW_SIZE]：
     *     - 在 [android.view.View] 原生测量逻辑下，根据内容大小向下调整，支持 padding 设置
     *     - 确保正好能完整显示缩图内容，不会裁剪
     *     - 缩图的显示数量由 [sliceGap]、[sliceWidth]、[sliceMaxCount] 共同决定
     *     - 宽度计算：(measuredWidth - paddingHorizontal - [sliceGap] - ([measuredWidth] + [sliceGap]) % ([sliceWidth] + [sliceGap])).coerceAtLeast(0)
     *     - 高度计算：measuredHeight
     * - [MeasureMode.SLICE_SIZE]：
     *     - 根据内容大小进行测量宽度，支持 padding 设置
     *     - 宽度计算：(paddingHorizontal + [sliceMaxCount] * ([sliceWidth] + [sliceGap]) - [sliceGap]).coerceAtLeast(0)
     *     - 高度计算：[sliceHeight]
     *
     * @see MeasureMode
     */
    var measureMode: MeasureMode = MeasureMode.VIEW_SIZE
        @MainThread
        set(value) {
            if (field == value) {
                return
            }
            isBitmapSpecDirty = true
            field = value
            requestLayout()
        }


    /**
     * 是否响应用户输入，即用户触摸事件，此优先级低于外界设置的 [setOnTouchListener]
     *
     * - true：启用用户触摸事件，默认为 true
     * - false: 禁用用户触摸事件
     */
    var isUserInputEnabled: Boolean = true


    /**
     * 是否可以拖动进度条
     *
     * - true：响应拖动进度条事件，默认为 true
     * - false: 禁止拖动进度条
     */
    var isSeekable: Boolean = true


    /**
     * 设置或获取监听 [ISeekBar] 动作监听器，触摸 [ISeekBar] 或者调整 [progress] 时使用
     */
    override var onSeekBarChangeListener: ISeekBar.OnSeekBarChangeListener? = null

    private val onBitmapLoadedListener: OnBitmapDataLoadedListener by lazy { OnBitmapDataLoadedListenerImpl() }

    private val progressPaint: Paint by lazy { Paint().apply { isAntiAlias = true } }
    private var progressAnimationValue = FloatValueHolder(ALPHA_SHOW)
    private val progressAnimation: COUISpringAnimation by lazy {
        COUISpringAnimation(progressAnimationValue).apply {
            spring = COUISpringForce().apply {
                setBounce(BOUNCE)
                setResponse(ALPHA_RESPONSE)
                finalPosition = ALPHA_SHOW
            }
            minimumVisibleChange = DynamicAnimation.MIN_VISIBLE_CHANGE_ALPHA
            setStartValue(ALPHA_SHOW)
        }
    }

    private val expiredProgressPaint: Paint by lazy { Paint().apply { isAntiAlias = true } }
    private var expiredProgressAnimationValue = FloatValueHolder(ALPHA_HIDE)
    private val expiredProgressAnimation: COUISpringAnimation by lazy {
        COUISpringAnimation(expiredProgressAnimationValue).apply {
            spring = COUISpringForce().apply {
                setBounce(BOUNCE)
                setResponse(ALPHA_RESPONSE)
                finalPosition = ALPHA_HIDE
            }
            minimumVisibleChange = DynamicAnimation.MIN_VISIBLE_CHANGE_ALPHA
            setStartValue(ALPHA_SHOW)
        }
    }

    private val thumbnailSeekBarLoader: ThumbnailSeekBarLoader by lazy { ThumbnailSeekBarLoader() }
    private val progressSampleRegion: Rect = Rect()
    private val progressDrawingRegion: Rect = Rect()
    private val expiredProgressSampleRegion: Rect = Rect()
    private val expiredProgressDrawingRegion: Rect = Rect()
    private var progressBitmap: Bitmap? = null
    private var expiredProgressBitmap: Bitmap? = null
    private val progressBitmapRadiusPath = Path()
    private val expiredProgressBitmapRadiusPath = Path()


    private val thumbSampleRegion: Rect = Rect()
    private val thumbDrawingRegion: Rect = Rect()
    private var thumbBitmapByDecoding: Bitmap? = null
    private val drawingThumbBitmap: Bitmap? get() = thumbContent ?: thumbBitmapByDecoding

    private val thumbPaint: Paint = Paint().apply { isAntiAlias = true }
    private var thumbAnimationValue = FloatValueHolder(ALPHA_DEFAULT)
    private val thumbAnimation: COUISpringAnimation by lazy {
        COUISpringAnimation(thumbAnimationValue).apply {
            spring = COUISpringForce().apply {
                setBounce(BOUNCE)
                setResponse(ALPHA_RESPONSE)
            }
            minimumVisibleChange = DynamicAnimation.MIN_VISIBLE_CHANGE_ALPHA
        }
    }

    private var internalProgress: Int = 0
    private var internalMin: Int = 0
    private var internalMax: Int = 0

    private val drawingRect: Rect = Rect()

    /**
     * 视频缩图轴背景圆角是3dp
     */
    private val itemDrawableRadius: Float by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_item_drawable_radius)
    }

    /**
     * 记录当前view的宽
     */
    private var viewWidth: Int = 0

    /**
     * 记录当前view的高
     */
    private var viewHeight: Int = 0

    /**
     * 1. 因加载图片时，需要知道view的size，所以加载图片流程放在了onLayout里
     * 2. 缩图轴实现后，其滑动时onLayout会频繁的被调用，从而频繁的加载图片
     * 故：需要过滤掉无用的图片加载，使用isBitmapSpecDirty编辑值
     */
    private var isBitmapSpecDirty: Boolean = true

    private var isUpdatingThumbContent: Boolean = false

    private var isDragging: Boolean = false
    private var isTouching: Boolean = false

    private val isBitmapSpecReady: Boolean
        get() = (sliceWidth > 0)
                && (sliceHeight > 0)
                && (sliceMaxCount > 0)
                && (contentWidth > 0)
                && (contentHeight > 0)

    /**
     * 拖拽把手顶部的坐标
     */
    private var thumbTop: Int = 0

    /**
     * 拖拽把手底部的坐标
     */
    private var thumbBottom: Int = 0

    /**
     * 拖拽把手Bitmap的宽度
     */
    private var thumbBitmapWidth: Int = 0

    /**
     * 拖拽把手Bitmap的高度
     */
    private var thumbBitmapHeight: Int = 0

    init {
        if (shouldInitByStyleAttr) {
            context.obtainStyledAttributes(attrs, R.styleable.ThumbnailSeekBar, defStyleAttr, defStyleRes).apply {
                val defaultSliceWidth = R.dimen.foundation_thumbnail_seekbar_slice_width.dimensionPixelSize
                val defaultSliceHeight = R.dimen.foundation_thumbnail_seekbar_slice_height.dimensionPixelSize
                max = getInteger(R.styleable.ThumbnailSeekBar_max, PROGRESS_MAX)
                min = getInteger(R.styleable.ThumbnailSeekBar_min, PROGRESS_MIN)
                progress = getInteger(R.styleable.ThumbnailSeekBar_progress, PROGRESS_DEFAULT)
                sliceGap = getDimensionPixelSize(R.styleable.ThumbnailSeekBar_sliceGap, SLICE_GAP_DEFAULT)
                sliceMaxCount = getInteger(R.styleable.ThumbnailSeekBar_sliceMaxCount, SLICE_MAX_COUNT_DEFAULT)
                sliceWidth = getDimensionPixelSize(R.styleable.ThumbnailSeekBar_sliceWidth, defaultSliceWidth)
                sliceHeight = getDimensionPixelSize(R.styleable.ThumbnailSeekBar_sliceHeight, defaultSliceHeight)
                thumbResourceId = getResourceId(R.styleable.ThumbnailSeekBar_thumb, R.drawable.foundation_thumbnail_seekbar_indicator)
                thumbTopOffset = getDimensionPixelSize(R.styleable.ThumbnailSeekBar_thumbTopOffset, thumbTopOffset)
                thumbBottomOffset = getDimensionPixelSize(R.styleable.ThumbnailSeekBar_thumbBottomOffset, thumbBottomOffset)
                measureMode = getInt(R.styleable.ThumbnailSeekBar_measureMode, MeasureMode.VIEW_SIZE.ordinal).let(MeasureMode::fromValue)
                isSeekable = getBoolean(R.styleable.ThumbnailSeekBar_isSeekable, isSeekable)
                isUserInputEnabled = getBoolean(R.styleable.ThumbnailSeekBar_isUserInputEnabled, isUserInputEnabled)
                isSeamlessAnimationEnabled = getBoolean(R.styleable.ThumbnailSeekBar_isSeamlessAnimationEnabled, isSeamlessAnimationEnabled)
                recycle()
            }
        }
    }

    override fun getProgress(): Int = internalProgress

    @MainThread
    override fun setProgress(value: Int, isFromUser: Boolean) {
        if (internalProgress == value) {
            return
        }
        internalProgress = value
        invalidate()
        notifyProgressChanged(isFromUser)
    }

    override fun getMin(): Int = internalMin

    @MainThread
    override fun setMin(value: Int) {
        if (internalMin == value) {
            return
        }
        internalMin = value
        invalidate()
    }

    override fun getMax(): Int = internalMax

    @MainThread
    override fun setMax(value: Int) {
        if (internalMax == value) {
            return
        }
        internalMax = value
        invalidate()
    }


    /**
     * 强制刷新进度缩图，会重新走获取缩图小分片，然后绘制的流程
     */
    @MainThread
    fun invalidateForce(shouldReloadSpec: Boolean = false) {
        if (isAttachedToWindow) {
            if (shouldReloadSpec) {
                resetSeekbarResources()
            } else {
                thumbnailSeekBarLoader.invalidate()
            }
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        isBitmapSpecDirty = true
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        recycleSeekbarResources()
    }

    override fun draw(canvas: Canvas) {
        GTrace.traceBegin("${TAG}draw")
        background?.let {
            getDrawingRect(drawingRect)
            if (it.bounds != drawingRect) {
                it.bounds = drawingRect
            }
        }
        super.draw(canvas)
        GTrace.traceEnd()
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)

        if ((sliceWidth <= 0) || (sliceHeight <= 0)) {
            return
        }

        val paddingHorizontal = paddingStart + paddingEnd
        val paddingVertical = paddingTop + paddingBottom
        val sliceBlockWidth = sliceWidth + sliceGap
        val sliceCount = when (measureMode) {
            MeasureMode.VIEW_SIZE -> ((measuredWidth - paddingHorizontal + sliceGap) / sliceBlockWidth).coerceAtMost(sliceMaxCount)
            else -> sliceMaxCount
        }

        val width = (paddingHorizontal + sliceCount * sliceBlockWidth - sliceGap).coerceAtLeast(0)
        val height = when (measureMode) {
            MeasureMode.VIEW_SIZE -> measuredHeight
            else -> (sliceHeight + paddingVertical)
        }.coerceAtLeast(0)

        setMeasuredDimension(width, height)
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)

        // 在 VIEW_SIZE 模式下，只要尺寸变更，就需要将 isBitmapSpecDirty 置位 true，保证能正常刷新内部 Bitmap
        if (measureMode == MeasureMode.VIEW_SIZE) {
            isBitmapSpecDirty = true
        }

        // 原本以下部分代码放在onDraw中，会频繁被触发，现在针对有些不怎变化的值提取到此处做缓存
        calculateProgressDrawingRegions()
        calculateThumbSampleRegion()
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)

        if (isBitmapSpecDirty && isBitmapSpecReady) {
            /**
             * 因为resetSeekbarResources 会重新加载图片，缩图轴滑动时又频繁的layout，
             * 这里只需要根据特定时机更新图片已减少无用layout触发的图片刷新
             */
            resetSeekbarResources()
        }
    }


    private fun resetSeekbarResources() {
        /*
         * 重置刷新新图片时，需要先回收旧的 bitmap 缓存
         * 然后重新设置尺寸规格进行加载
         */
        recycleSeekbarResources(isSeamlessAnimationEnabled.not())
        setupSeekbarResources()
        setupProgressBitmapSpec()
        // 解决在不支持缩图轴的高通平台的设备上，视频大图播放时，没有进度条的问题
        calculateProgressDrawingRegions()
        calculateThumbSampleRegion()
    }


    private fun recycleSeekbarResources(shouldRecycleProgress: Boolean = true) {
        thumbnailSeekBarLoader.onBitmapLoadedListener = null
        thumbnailSeekBarLoader.release()

        if (shouldRecycleProgress) {
            thumbDrawingRegion.setEmpty()
            thumbSampleRegion.setEmpty()
            progressDrawingRegion.setEmpty()
            progressSampleRegion.setEmpty()
            if (isSeamlessAnimationEnabled) {
                // 当无缝动画启用时，为了动画过渡，bitmap 不会自动回收，需要本控件自行处理回收
                progressBitmap?.let {
                    ThumbnailSeekBarLoader.recycleBitmap(it, "recycleSeekbarResources(true)")
                }
            }
            progressBitmap = null
        }

        thumbBitmapByDecoding?.recycle()
        thumbBitmapByDecoding = null
    }


    private fun setupProgressBitmapSpec() {
        if ((GProperty.PROPERTY_OTEST_RUNNING) || (GProperty.PROPERTY_MONKEY_RUNNING)) {
            GLog.e(TAG, LogFlag.DL) {
                "[setupProgressBitmapSpec] update thumbnailSeekBarLoader.bitmapSpec measureMode=$measureMode"
            }
        }
        thumbnailSeekBarLoader.bitmapSpec = BitmapSpec(
            shouldAutoRecycle = isSeamlessAnimationEnabled.not(),
            isRtl = isRtl,
            constraintSize = Size(width - paddingStart - paddingEnd, height - paddingTop - paddingBottom),
            sliceGap = sliceGap,
            sliceSize = Size(sliceWidth, sliceHeight),
            sliceMaxCount = sliceMaxCount,
            panelMeasureMode = when (measureMode) {
                MeasureMode.VIEW_SIZE -> PanelSpecMode.AUTO_SIZE
                MeasureMode.SLICE_SIZE -> PanelSpecMode.SLICE_SIZE
            }
        )
        isBitmapSpecDirty = thumbnailSeekBarLoader.bitmapSpec.isEmpty
    }

    private fun setupSeekbarResources() {
        thumbBitmapByDecoding?.recycle()
        thumbBitmapByDecoding = if (thumbResourceId == Resources.ID_NULL) null else BitmapFactory.decodeResource(resources, thumbResourceId)
        thumbnailSeekBarLoader.onBitmapLoadedListener = onBitmapLoadedListener
    }


    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        GTrace.traceBegin("${TAG}onDraw")
        // 计算是有先后顺序的，不要随意调整
        calculateExpiredProgressDrawingRegions()
        calculateThumbDrawingRegions()
        resetRadiusPathIfNeed()

        // 绘制是有先后顺序的，不要随意调整
        tryDrawProgress(canvas)
        tryDrawExpiredProgress(canvas)
        tryDrawThumb(canvas)
        GTrace.traceEnd()
    }

    /**
     * 当view的宽高改变时，重新设置圆角路径属性
     */
    private fun resetRadiusPathIfNeed() {
        if (viewWidth == width && viewHeight == height) {
            // 宽高没有变化时，直接return
            return
        }

        viewWidth = width
        viewHeight = height
        progressBitmapRadiusPath.reset()
        progressBitmapRadiusPath.addRoundRect(
            0f,
            0f,
            width.toFloat(),
            height.toFloat(),
            itemDrawableRadius,
            itemDrawableRadius,
            Path.Direction.CW
        )
        expiredProgressBitmapRadiusPath.reset()
        expiredProgressBitmapRadiusPath.addRoundRect(
            0f,
            0f,
            width.toFloat(),
            height.toFloat(),
            itemDrawableRadius,
            itemDrawableRadius,
            Path.Direction.CW
        )
    }

    /**
     * 计算thumbBar背景绘制区域
     */
    private fun calculateExpiredProgressDrawingRegions() {
        val progressDrawingRegion = progressDrawingRegion

        if (progressDrawingRegion.isEmpty) {
            expiredProgressDrawingRegion.setEmpty()
            expiredProgressSampleRegion.setEmpty()
            return
        }

        val expiredProgressBitmap = expiredProgressBitmap
        if (expiredProgressBitmap?.isRecycled != false) {
            expiredProgressDrawingRegion.setEmpty()
            expiredProgressSampleRegion.setEmpty()
            return
        }

        val bitmapWidth = expiredProgressBitmap.width
        val bitmapHeight = expiredProgressBitmap.height

        if (min(bitmapWidth, bitmapHeight) <= 0) {
            expiredProgressDrawingRegion.setEmpty()
            expiredProgressSampleRegion.setEmpty()
            return
        }

        val expiredProgressDrawingHeight = progressDrawingRegion.height()
        val expiredProgressDrawingWidth = (expiredProgressDrawingHeight.toDouble() / bitmapHeight * bitmapWidth).toInt()

        expiredProgressSampleRegion.set(0, 0, bitmapWidth, bitmapHeight)
        expiredProgressDrawingRegion.set(
            progressDrawingRegion.left,
            progressDrawingRegion.top,
            progressDrawingRegion.left + expiredProgressDrawingWidth,
            progressDrawingRegion.top + expiredProgressDrawingHeight
        )
    }

    private fun calculateProgressDrawingRegions() {
        val panelSize = thumbnailSeekBarLoader.bitmapSpec.panelSpec.panelSize
        val progressPanelWidth = panelSize.width
        val progressPanelHeight = panelSize.height
        val contentWidth = contentWidth
        val contentHeight = contentHeight

        if ((contentWidth == 0)
            || (contentHeight == 0)
            || (progressPanelWidth == 0)
            || (progressPanelHeight == 0)
        ) {
            progressDrawingRegion.setEmpty()
            progressSampleRegion.setEmpty()
            return
        }

        val progressDrawingWidth: Int
        val progressDrawingHeight: Int
        val progressExtraOffsetX: Int
        val progressExtraOffsetY: Int

        if (measureMode == MeasureMode.SLICE_SIZE) {
            /**
             * 当 Seekbar 的宽高改变时，内容不变，可见区域减小 即：内容被裁剪
             */
            progressDrawingWidth = progressPanelWidth
            progressDrawingHeight = progressPanelHeight
            progressExtraOffsetX = 0
            progressExtraOffsetY = (contentHeight - progressPanelHeight) / 2
        } else {
            progressDrawingWidth = min(contentWidth, progressPanelWidth)
            progressDrawingHeight = min(contentHeight, progressPanelHeight)
            progressExtraOffsetX = (contentWidth - progressDrawingWidth) / 2
            progressExtraOffsetY = (contentHeight - progressDrawingHeight) / 2
        }

        val progressStart = paddingStart + progressExtraOffsetX
        val progressTop = paddingTop + progressExtraOffsetY
        val progressEnd = progressStart + progressDrawingWidth
        val progressBottom = progressTop + progressDrawingHeight

        /** View可视Rect */
        progressDrawingRegion.set(progressStart, progressTop, progressEnd, progressBottom)
        /** 绘制内容Rect */
        progressSampleRegion.set(0, 0, progressPanelWidth, progressPanelHeight)

        if (GProperty.DEBUG_THUMB_NAIL_SEEKBAR) {
            GLog.d(TAG, LogFlag.DL, "[calculateProgressDrawingRegions] drawingRegion: $progressDrawingRegion, sampleRegion: $progressSampleRegion")
        }
    }

    private fun calculateThumbSampleRegion() {
        // 初始化可缓存的变量
        val thumbDrawingHeight = contentHeight - thumbTopOffset - thumbBottomOffset
        thumbTop = paddingTop + thumbTopOffset
        thumbBottom = thumbTop + thumbDrawingHeight
        thumbBitmapWidth = drawingThumbBitmap?.width ?: 0
        thumbBitmapHeight = drawingThumbBitmap?.height ?: 0

        if ((contentWidth == 0)
            || (contentHeight == 0)
            || (thumbBitmapWidth == 0)
            || (thumbBitmapHeight == 0)
        ) {
            thumbSampleRegion.setEmpty()
            return
        }

        /** 绘制内容Rect */
        thumbSampleRegion.set(0, 0, thumbBitmapWidth, thumbBitmapHeight)

        if (GProperty.DEBUG_THUMB_NAIL_SEEKBAR) {
            GLog.d(TAG, LogFlag.DL, "[calculateThumbSampleRegion] sampleRegion: $thumbSampleRegion")
        }
    }

    /**
     * 计算thumbBar 手柄绘制区域
     */
    @Suppress("ComplexCondition")
    private fun calculateThumbDrawingRegions() {
        val contentWidth = contentWidth
        val contentHeight = contentHeight
        val progressPanelWidth = thumbnailSeekBarLoader.bitmapSpec.panelSpec.panelSize.width

        if ((contentWidth == 0)
            || (contentHeight == 0)
            || (thumbBitmapWidth == 0)
            || (thumbBitmapHeight == 0)
            || (progressPanelWidth == 0)
        ) {
            thumbDrawingRegion.setEmpty()
            thumbSampleRegion.setEmpty()
            return
        }
        val thumbDrawingHeight = contentHeight - thumbTopOffset - thumbBottomOffset
        val thumbDrawingWidth = (thumbBitmapWidth * (thumbDrawingHeight.toFloat() / thumbBitmapHeight)).toInt()
        val progressPercent = ((internalProgress - internalMin).toFloat() / (internalMax - internalMin)).coerceIn(0f, 1f)

        val progressDrawingWidth: Int
        val thumbDrawingCenterX: Int
        if (measureMode == MeasureMode.SLICE_SIZE) {
            /**
             * SLICE_SIZE 模式下以Seekbar contentWidth 为把手最大位置
             */
            progressDrawingWidth = contentWidth
            thumbDrawingCenterX = if (isRtl) {
                width - paddingStart - progressPercent * contentWidth
            } else {
                paddingStart + progressPercent * contentWidth
            }.toInt()
        } else {
            progressDrawingWidth = min(contentWidth, progressPanelWidth)
            val progressDrawingOffsetX = (contentWidth - progressDrawingWidth) / 2
            thumbDrawingCenterX = if (isRtl) {
                width - paddingStart - progressDrawingOffsetX - progressPercent * progressPanelWidth
            } else {
                paddingStart + progressDrawingOffsetX + progressPercent * progressPanelWidth
            }.toInt()
        }

        val thumbHalfDrawingWidth = thumbDrawingWidth / 2
        val thumbStartMax = (progressDrawingWidth - thumbHalfDrawingWidth).coerceAtLeast(0)
        val thumbStart = (thumbDrawingCenterX - thumbHalfDrawingWidth).coerceIn(-thumbHalfDrawingWidth, thumbStartMax)
        val thumbEnd = thumbStart + thumbDrawingWidth

        /** View可视Rect */
        thumbDrawingRegion.set(thumbStart, thumbTop, thumbEnd, thumbBottom)

        if (GProperty.DEBUG_THUMB_NAIL_SEEKBAR) {
            GLog.d(TAG, LogFlag.DL, "[calculateThumbDrawingRegions] drawingRegion: $thumbDrawingRegion ")
        }
    }

    private fun tryDrawProgress(canvas: Canvas) {
        val progressBitmap = progressBitmap ?: return
        if (progressSampleRegion.isEmpty || progressDrawingRegion.isEmpty) {
            return
        }
        if (progressBitmap.isRecycled) {
            return
        }

        progressPaint.alpha = (ALPHA_MAX * progressAnimationValue.value).toInt()

        canvas.save()
        canvas.clipPath(progressBitmapRadiusPath)
        canvas.drawBitmap(progressBitmap, progressSampleRegion, progressDrawingRegion, progressPaint)
        canvas.restore()

        if (progressAnimation.isRunning) {
            invalidate()
        }
    }

    private fun tryDrawExpiredProgress(canvas: Canvas) {
        val expiredProgressBitmap = expiredProgressBitmap ?: return
        if (progressSampleRegion.isEmpty || progressDrawingRegion.isEmpty) {
            return
        }
        if (expiredProgressBitmap.isRecycled) {
            return
        }
        if (expiredProgressAnimation.isRunning.not()) {
            return
        }
        expiredProgressPaint.alpha = (ALPHA_MAX * expiredProgressAnimationValue.value).toInt()

        canvas.save()
        canvas.clipPath(expiredProgressBitmapRadiusPath)
        canvas.drawBitmap(expiredProgressBitmap, expiredProgressSampleRegion, expiredProgressDrawingRegion, expiredProgressPaint)
        canvas.restore()

        if (expiredProgressAnimation.isRunning) {
            invalidate()
        }
    }

    private fun tryDrawThumb(canvas: Canvas) {
        if (thumbAnimation.isRunning.not() && isThumbVisible.not()) return
        val thumbBitmap = drawingThumbBitmap ?: return
        if (thumbSampleRegion.isEmpty || thumbDrawingRegion.isEmpty) {
            return
        }
        if (thumbBitmap.isRecycled) {
            return
        }
        thumbPaint.alpha = (ALPHA_MAX * thumbAnimationValue.value).toInt()
        canvas.drawBitmap(thumbBitmap, thumbSampleRegion, thumbDrawingRegion, thumbPaint)

        if (thumbAnimation.isRunning) {
            invalidate()
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent): Boolean {
        return when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                // 开始触摸，状态重置，优先 Seek 事件，否则 View 原生逻辑事件
                isDragging = isUserInputEnabled && isSeekable
                isTouching = isUserInputEnabled && isDragging.not()
                dispatchEvent(event, isDragging, isTouching)
            }

            MotionEvent.ACTION_MOVE -> {
                // 根据 ACTION_DOWN 时设定的状态进行分发，避免中途改变 isUserInputEnabled、isSeekable 导致链路逻辑中断和状态异常
                dispatchEvent(event, isDragging, isTouching)
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                dispatchEvent(event, isDragging, isTouching).apply {
                    // 事件收尾，重置状态
                    isDragging = false
                    isTouching = false
                }
            }

            else -> false
        }
    }

    private fun dispatchEvent(event: MotionEvent, isDragEvent: Boolean, isTouchEvent: Boolean): Boolean {
        return when {
            isDragEvent -> dispatchDragEvent(event)
            isTouchEvent -> super.onTouchEvent(event)
            else -> false
        }
    }

    private fun dispatchDragEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                notifyStartTrackingTouch()
                updateProgressByTouch(event.x.toInt())
            }

            MotionEvent.ACTION_MOVE -> updateProgressByTouch(event.x.toInt())
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> notifyStopTrackingTouch()
        }
        return true
    }

    private fun updateProgressByTouch(touchX: Int) {
        if (progressDrawingRegion.isEmpty) {
            return
        }

        val constraintTouchX = touchX.coerceIn(progressDrawingRegion.left, progressDrawingRegion.right)
        val constrainEnd = if (isRtl) progressDrawingRegion.right else progressDrawingRegion.left
        val progressPercent = (constraintTouchX - constrainEnd).absoluteValue.toFloat() / progressDrawingRegion.width()
        val progress = (internalMin + progressPercent * (internalMax - internalMin)).toInt()
        if (progress == internalProgress) {
            return
        }
        internalProgress = progress
        invalidate()
        notifyProgressChanged(true)
    }


    private fun notifyProgressChanged(fromUser: Boolean) {
        onSeekBarChangeListener?.onProgressChanged(this, internalProgress, fromUser)
    }

    private fun notifyStartTrackingTouch() {
        GLog.d(TAG, LogFlag.DL, "[notifyStartTrackingTouch]")
        onSeekBarChangeListener?.onStartTrackingTouch(this)
    }

    private fun notifyStopTrackingTouch() {
        GLog.d(TAG, LogFlag.DL, "[notifyStopTrackingTouch]")
        onSeekBarChangeListener?.onStopTrackingTouch(this)
    }


    private val Int.dimensionPixelSize: Int
        get() = resources.getDimensionPixelSize(this)

    private val View.isRtl: Boolean
        get() = layoutDirection == LAYOUT_DIRECTION_RTL

    private val contentWidth: Int
        get() = width - paddingStart - paddingEnd

    private val contentHeight: Int
        get() = height - paddingTop - paddingBottom


    /**
     * [ThumbnailSeekBar] 控件的宽高测量模式
     *
     * - [MeasureMode.VIEW_SIZE]：在 [android.view.View] 原生测量逻辑下，根据内容大小向下调整，支持 padding 设置
     * - [MeasureMode.SLICE_SIZE]：根据内容大小进行测量宽度，支持 padding 设置
     *
     * @see ThumbnailSeekBar.measureMode
     */
    enum class MeasureMode {
        VIEW_SIZE,
        SLICE_SIZE;

        internal companion object {
            fun fromValue(value: Int): MeasureMode = when (value) {
                SLICE_SIZE.ordinal -> SLICE_SIZE
                else -> VIEW_SIZE
            }
        }
    }

    private inner class OnBitmapDataLoadedListenerImpl : OnBitmapDataLoadedListener {
        override fun onBitmapDataLoaded(bitmapData: BitmapData, from: String) {
            val (bitmap, _, hasContent) = bitmapData
            if ((GProperty.PROPERTY_OTEST_RUNNING) || (GProperty.PROPERTY_MONKEY_RUNNING)) {
                GLog.e(TAG, LogFlag.DL) {
                    "[onBitmapDataLoaded] from=$from, bitmap hashCode=${bitmap.hashCode()}"
                }
            }
            if (hasContent || isSeamlessAnimationEnabled.not()) {
                when {
                    (progressBitmap == null) -> {
                        // 首次获取到 bitmap
                        if (isSeamlessAnimationEnabled) {
                            // 无缝动画启用，则启动动画
                            startProgressAnimator()
                        }
                    }

                    (progressBitmap != bitmap) -> {
                        if ((GProperty.PROPERTY_OTEST_RUNNING) || (GProperty.PROPERTY_MONKEY_RUNNING)) {
                            GLog.e(TAG, LogFlag.DL) {
                                "[onBitmapDataLoaded] from=$from, not same bitmap " +
                                        "bitmap.hashCode=${bitmap.hashCode()}, progressBitmap.hashCode=${progressBitmap?.hashCode()}"
                            }
                        }
                        // 新的 bitmap 和已经存在的不一致
                        if (isSeamlessAnimationEnabled) {
                            // 无缝动画启用，则启动动画
                            expiredProgressBitmap = progressBitmap
                            expiredProgressBitmap?.let(::startExpiredProgressAnimator) // 会回收progressBitmap
                            progressBitmap = null
                        } else {
                            // 无缝动画未启用，则直接则直接置空 bitmap，回收操作由 Loader 自行处理
                            expiredProgressBitmap = null
                            progressBitmap = null
                        }
                    }
                }
                // 直接赋值最新的，并刷新
                progressBitmap = bitmap // 这里将bitmapCache.bitmapData.bitmap
                if ((GProperty.PROPERTY_OTEST_RUNNING) || (GProperty.PROPERTY_MONKEY_RUNNING)) {
                    GLog.e(TAG, LogFlag.DL) {
                        "[onBitmapDataLoaded] from=$from, set bitmap to progressBitmap, " +
                                "bitmap.hashCode=${bitmap.hashCode()}, progressBitmap.hashCode=${progressBitmap?.hashCode()}"
                    }
                }
                invalidate()
            }
        }

        private fun startProgressAnimator() {
            progressAnimation.cancel()
            progressAnimation.start()
        }

        private fun startExpiredProgressAnimator(bitmap: Bitmap) {
            expiredProgressAnimation.cancel()
            expiredProgressAnimation.removeEndListener(animEndListener)
            animEndListener = COUIDynamicAnimation.OnAnimationEndListener { _, _, _, _ ->
                if (expiredProgressBitmap === bitmap) {
                    expiredProgressBitmap = null
                }
                ThumbnailSeekBarLoader.recycleBitmap(bitmap, "startExpiredProgressAnimator")
            }
            expiredProgressAnimation.addEndListener(animEndListener)
            expiredProgressAnimation.start()
        }
    }

    private companion object {
        private const val TAG = "ThumbnailSeekBar"

        private const val PROGRESS_MIN = 0
        private const val PROGRESS_MAX = 100
        private const val PROGRESS_DEFAULT = 0
        private const val SLICE_GAP_DEFAULT = 0
        private const val SLICE_MAX_COUNT_DEFAULT = Int.MAX_VALUE

        private const val ALPHA_MAX = 255
        private const val ALPHA_SHOW = 1F
        private const val ALPHA_HIDE = 0F
        private const val ALPHA_DEFAULT = ALPHA_SHOW

        private const val BOUNCE = 0F
        private const val ALPHA_RESPONSE = 0.3F
    }
}
