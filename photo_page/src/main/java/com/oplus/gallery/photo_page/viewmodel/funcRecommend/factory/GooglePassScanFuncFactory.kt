/***********************************************************************************
 *  Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 *  All rights reserved.
 *
 *  File: - GooglePassScanFuncFactory.kt
 *  Description: 登机牌扫描任务Factory
 *
 *  Version: 1.0
 *  Date: 2024/09/03
 *  Author: lizhenya
 *  TAG:
 *  ------------------------------- Revision History: ----------------------------
 *  <author>                        <date>       <version>       <desc>
 *  ------------------------------------------------------------------------------
 *   lizhenya                     2024/09/03       1.0        build this module
 **********************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.funcRecommend.factory

import android.content.Context
import android.graphics.Bitmap
import android.os.SystemClock
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.GooglePassScanConst.MASK_SUPPORT
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.GooglePassScanConst.SHIFT_GOOGLE_PASS_SCAN
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.GooglePassScanConst.STATUS_SCANNED
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.GooglePassScanConst.STATUS_SCANNED_CLICKED
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.GooglePassScanConst.STATUS_SCANNED_UNSUPPORTED
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.scan.googlepassscan.IGooglePassScanAbility
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.photo_page.viewmodel.funcRecommend.FuncItem
import com.oplus.gallery.standard_lib.app.AppScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 登机牌扫描任务Factory
 */
internal class GooglePassScanFuncFactory(private val context: Context) : AbsFuncFactory() {

    /**
     * 是否支持机票扫描添加到Google Wallet
     */
    private val isSupportGooglePassScanFunc: Boolean by lazy {
        ConfigAbilityWrapper.getBoolean(ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_GOOGLE_PASS_SCAN, false)
    }

    private val googlePassScanAbility: IGooglePassScanAbility? by lazy {
        context.getAppAbility()
    }

    override fun getFromCache(mediaItem: MediaItem): List<FuncItem> {
        return listOf(mediaItem.queryGooglePassScanState())
    }

    override fun onCreateFuncTask(mediaItem: MediaItem, bitmap: Bitmap): AbsFuncTask {
        return GooglePassScanFuncTask(mediaItem, bitmap)
    }

    override fun release() {
        AppScope.launch(Dispatchers.IO) {
            googlePassScanAbility?.close()
        }
    }

    /**
     * 检查设备与MediaItem对Google Pass Scan的支持状态
     */
    private fun MediaItem.queryGooglePassScanState(): FuncItem.GooglePassScan {
        if (ConditionHelper.isCommonImage(this).not()) {
            GLog.d(TAG, LogFlag.DL) { "[queryGooglePassScanState] this image is not support" }
            return FuncItem.GooglePassScan(FuncItem.State.UNSUPPORTED)
        }
        if (isSupportGooglePassScanFunc.not()) {
            GLog.d(TAG, LogFlag.DL) { "[queryGooglePassScanState] device is not support" }
            return FuncItem.GooglePassScan(FuncItem.State.UNSUPPORTED)
        }

        return when ((this.extTagFlagsAsLong shr SHIFT_GOOGLE_PASS_SCAN) and MASK_SUPPORT) {
            STATUS_SCANNED_CLICKED,
            STATUS_SCANNED_UNSUPPORTED -> FuncItem.GooglePassScan(FuncItem.State.UNSUPPORTED)
            STATUS_SCANNED -> FuncItem.GooglePassScan(FuncItem.State.SUPPORTED)

            else -> FuncItem.GooglePassScan(FuncItem.State.UNDETECTED)
        }.also {
            GLog.d(TAG, LogFlag.DL) { "[queryGooglePassScanState] mediaItem state:$it" }
        }
    }

    /**
     * 登机牌扫描任务
     */
    private inner class GooglePassScanFuncTask(
        private val mediaItem: MediaItem,
        private val bitmap: Bitmap
    ) : AbsFuncTask() {
        override val tag: String = "GooglePassScanFuncTask"

        override val funcItems: List<FuncItem> = listOf(FuncItem.GooglePassScan(FuncItem.State.UNDETECTED))

        override fun onDetect(): List<FuncItem> {
            GLog.d(tag, LogFlag.DL) { "onDetect ." }
            val time = SystemClock.elapsedRealtime()
            googlePassScanAbility?.let { ability ->
                val detectResult = ability.detect(mediaItem, bitmap) ?: return emptyList()
                val funItem = if (detectResult.pendingIntent != null) {
                    FuncItem.GooglePassScan(FuncItem.State.SUPPORTED)
                } else {
                    FuncItem.GooglePassScan(FuncItem.State.UNSUPPORTED)
                }
                GLog.d(tag, LogFlag.DL) { "onDetect, real time detect cost:${SystemClock.elapsedRealtime() - time}" }
                return listOf(funItem)
            } ?: let {
                return emptyList()
            }
        }
    }

    companion object {
        private const val TAG = "GooglePassScanFuncFactory"
    }
}