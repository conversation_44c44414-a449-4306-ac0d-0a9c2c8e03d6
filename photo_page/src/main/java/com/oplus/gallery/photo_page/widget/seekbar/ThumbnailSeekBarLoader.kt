/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ThumbnailSeekBarLoader.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/03/29
 ** Author: zhangjisong
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>            <version>        <desc>
 ** ------------------------------------------------------------------------------
 ** zhangjisong                     2022/03/29        1.0
 *********************************************************************************/
package com.oplus.gallery.photo_page.widget.seekbar

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Rect
import android.util.Size
import android.view.animation.Interpolator
import android.view.animation.LinearInterpolator
import com.oplus.gallery.foundation.cache.memorycache.BitmapPools
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.LogFlag
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import java.util.concurrent.atomic.AtomicLong
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock
import kotlin.math.min
import kotlin.system.measureTimeMillis

/**
 * 缩图小分片获取器，用于获取 [ThumbnailSeekBar] 进度缩图的小分片
 */
interface ISliceRetriever {

    /**
     * 加载缩图小分片 Bitmap，可以返回 null
     *
     * @param fraction 获取缩图小分片的百分比位置，如果是视频，那么应该取 `视频总时间 * fraction` 位置的缩图
     * @param referSize 缩图小分片的参考大小，期望返回的 Bitmap 是 [referSize] 大小，仅仅是建议，如果不是这么大，内部会裁剪处理
     */
    fun loadSlice(fraction: Float, referSize: Size): Bitmap? = null

    /**
     * 释放缩图小分片获取器，后续不再使用，建议在该函数内释放资源
     */
    fun release(): Unit = Unit

    /**
     * [ISliceRetriever] 的空实现，加载 slice 时直接返回 null
     */
    object EmptySliceRetriever : ISliceRetriever
}


/**
 * 提供缩图小分片获取器的工厂
 *
 * 有可能 ISliceRetriever 使用不同方式实现，可以在该工厂内部实现创建不同的 [ISliceRetriever],
 * 并且 [ISliceRetriever] 是使用完即可释放，需要在刷新缩图时动态创建 [ISliceRetriever] 实例
 */
fun interface ISliceRetrieverFactory {

    /**
     * 创建一个 [ISliceRetriever] 实例对象
     */
    fun create(): ISliceRetriever

    /**
     * ISliceRetrieverFactory 的空实现，直接返回一个 [ISliceRetriever.EmptySliceRetriever]
     *
     * @see ISliceRetriever.EmptySliceRetriever
     */
    object EmptySliceRetrieverFactory : ISliceRetrieverFactory {
        override fun create(): ISliceRetriever = ISliceRetriever.EmptySliceRetriever
    }
}

/**
 * 缩图加工处理器，仅限 [ThumbnailSeekBar] 内部使用
 */
internal class ThumbnailSeekBarLoader {

    internal var sliceRetrieverInterpolator: Interpolator = LinearInterpolator()

    internal var sliceRetrieverFactory: ISliceRetrieverFactory? = null

    internal var bitmapSpec: BitmapSpec = BitmapSpec()
        set(value) {
            // 如果 bitmap 规格变更或者 bitmapCache 不存在，则尝试初始化一下
            val shouldInitBitmap = (field != value) || (bitmapCache == null)
            field = value

            if (shouldInitBitmap) {
                initBitmap()
                doInvalidBitmap("updateBitmapSpec")
            }
        }


    internal var onBitmapLoadedListener: OnBitmapDataLoadedListener? = null
        set(value) {
            if (field == value) {
                return
            }
            field = value
            bitmapCache?.bitmapData?.let { value?.onBitmapDataLoaded(it, "setBitmapLoader") }
        }

    // 避免预览图撕裂使用
    private var bitmapCache: BitmapCache? = null
    private var loadingIdCreator: AtomicLong = AtomicLong(0)
    private var loadingJob: Job? = null
    private var isBitmapErased = true

    internal fun invalidate() {
        doInvalidBitmap("invalidate")
    }

    internal fun release() {
        cancelExpiredJob()
        recycleBitmapCache()
    }

    private fun initBitmap() {
        cancelExpiredJob()
        recycleBitmapCache()

        if (bitmapSpec.panelSpec.isEmpty) {
            GLog.d(TAG, "[initBitmap] bitmapSpec: $bitmapSpec, empty")
            return
        }

        val panelSize = bitmapSpec.panelSpec.panelSize
        val panelBitmap = allocateBitmap(panelSize)
        val panelCanvas = Canvas(panelBitmap)
        val bitmapData = BitmapData(panelBitmap, bitmapSpec, false)
        if ((GProperty.PROPERTY_OTEST_RUNNING) || (GProperty.PROPERTY_MONKEY_RUNNING)) {
            GLog.e(TAG, LogFlag.DL) { "[initBitmap] create bitmap cache, panelBitmap.hashCode=${panelBitmap.hashCode()}" }
        }
        bitmapCache = BitmapCache(panelBitmap, panelCanvas, bitmapData)
        isBitmapErased = true

        GLog.d(TAG, "[initBitmap] bitmapSpec: $bitmapSpec, panelSpec: ${bitmapSpec.panelSpec}")

        performInvalidateBitmap(bitmapCache, false, "initBitmap")
    }

    private fun recycleBitmapCache() {
        bitmapCache?.recycle()
        bitmapCache = null
    }

    private fun cancelExpiredJob() {
        loadingJob?.cancel()
        loadingJob = null
    }

    private fun doInvalidBitmap(from: String = "default") {
        val loadingId = loadingIdCreator.incrementAndGet()

        cancelExpiredJob()
        eraseBitmap()

        val bitmapCache = bitmapCache ?: let {
            GLog.d(TAG, LogFlag.DL) { "[doInvalidBitmap] from=$from bitmapCache is null, skip" }
            return
        }

        val sliceRetrieverFactory = sliceRetrieverFactory ?: let {
            GLog.d(TAG, LogFlag.DL) { "[doInvalidBitmap] from=$from sliceRetrieverFactory is null, skip" }
            return
        }

        if (bitmapCache.bitmapData.bitmapSpec.panelSpec.isEmpty) {
            GLog.d(TAG, LogFlag.DL) { "[doInvalidBitmap] from=$from panelSpec: ${bitmapSpec.panelSpec}, empty" }
            return
        }

        if (bitmapCache.bitmapData.bitmapSpec.panelSpec.sliceCount <= 0) {
            GLog.w(TAG, LogFlag.DL) { "[doInvalidBitmap] from=$from sliceCount is not a positive number, skip" }
            return
        }

        GLog.d(TAG, LogFlag.DL) { "[doInvalidBitmap] from=$from scheduling" }
        loadingJob = GlobalScope.launch {
            sliceRetrieverFactory.create().let { sliceRetriever ->
                runCatching { doLoadAndDrawSlices(bitmapCache, loadingId, sliceRetriever) }
                sliceRetriever.release()
            }
        }
    }

    private fun eraseBitmap() {
        val bitmapCache = bitmapCache ?: return

        if (!isBitmapErased) {
            isBitmapErased = true
            if ((GProperty.PROPERTY_OTEST_RUNNING) || (GProperty.PROPERTY_MONKEY_RUNNING)) {
                GLog.w(TAG) { "[eraseBitmap] hasCode=${bitmapCache.bitmap.hashCode()}" }
            }
            bitmapCache.bitmap.eraseColor(Color.TRANSPARENT)
        }

        performInvalidateBitmap(bitmapCache, false, "eraseBitmap")
    }

    private fun doLoadAndDrawSlices(bitmapCache: BitmapCache, loadingId: Long, sliceRetriever: ISliceRetriever) {
        GLog.i(TAG) { "[doLoadAndDrawSlices] start: loadingId=$loadingId, bitmapSpec=${bitmapCache.bitmapData.bitmapSpec}" }
        val timeSpent = measureTimeMillis {
            val panelSpec = bitmapCache.bitmapData.bitmapSpec.panelSpec
            val realSliceSize = panelSpec.sliceSize

            val sliceCount = panelSpec.sliceCount
            (0 until sliceCount)
                .asSequence()
                .map { it.toFloat() / sliceCount }
                .map { sliceRetrieverInterpolator.getInterpolation(it).coerceIn(0f, 1f) }
                .map { if (loadingId.isLoadIdExpired) null else sliceRetriever.loadSlice(it, realSliceSize) }
                .mapIndexedNotNull { index, bitmap ->
                    bitmap?.let {
                        if ((GProperty.PROPERTY_OTEST_RUNNING) || (GProperty.PROPERTY_MONKEY_RUNNING)) {
                            GLog.e(TAG, LogFlag.DL) {
                                "[doLoadAndDrawSlices] currentHashCode=${bitmap.hashCode()}, cacheHashCode=${bitmapCache.bitmap.hashCode()}"
                            }
                        }
                        SliceRecord(loadingId, index, it, bitmapCache)
                    }
                }
                .forEach(::doDrawBitmap)
        }
        GLog.i(TAG) {
            "[doLoadAndDrawSlices] end:   loadingId=$loadingId, timeSpent=$timeSpent ms, bitmapSpec=${bitmapCache.bitmapData.bitmapSpec}"
        }
    }

    private fun doDrawBitmap(sliceRecord: SliceRecord) {
        val rawBitmap = sliceRecord.rawBitmap

        if (sliceRecord.loadingId.isLoadIdExpired) {
            recycleBitmap(rawBitmap, "isLoadIdExpired")
            return
        }

        if (rawBitmap.size.isEmpty) {
            recycleBitmap(rawBitmap, "emptySize")
            return
        }

        val bitmapCache = sliceRecord.bitmapCache
        val bitmapSpec = bitmapCache.bitmapData.bitmapSpec
        val panelSpec = bitmapSpec.panelSpec

        val sampleRegion = calculateSliceSampleRegion(panelSpec, rawBitmap.size)
        val drawingRegions = calculatePanelDrawingRegion(panelSpec, bitmapSpec.isRtl, sliceRecord.index)

        GLog.d(TAG, "[doDrawBitmap] sampleRegion: $sampleRegion, drawingRegions: $drawingRegions, isRtl: ${bitmapSpec.isRtl}")

        isBitmapErased = false
        performDrawSlice(bitmapCache, rawBitmap, sampleRegion, drawingRegions)
        performInvalidateBitmap(bitmapCache, true, "doDrawBitmap")

        recycleBitmap(rawBitmap, "doDrawBitmap_finish")
    }

    private fun calculateSliceSampleRegion(panelSpec: PanelSpec, bitmapSize: Size): Rect {
        val result = Rect()

        val sliceSize = panelSpec.sliceSize
        val bitmapRatio = bitmapSize.width.toDouble() / bitmapSize.height
        val constraintRatio = sliceSize.width.toDouble() / sliceSize.height

        /**
         * CenterCrop方式
         */
        if (bitmapRatio < constraintRatio) {
            /*
             *                     ┌┐
             * bitmapSize:         ││
             *                     └┘
             *                 ┌────────┐
             * constraintSize: │        │
             *                 └────────┘
             */
            val sampleHeight = (bitmapSize.width / constraintRatio).toInt()
            val sampleTop = (bitmapSize.height - sampleHeight) / 2
            val sampleBottom = sampleTop + sampleHeight
            result.set(0, sampleTop, bitmapSize.width, sampleBottom)
        } else {
            /*
             *                 ┌────────┐
             * bitmapSize:     │        │
             *                 └────────┘
             *                     ┌┐
             * constraintSize:     ││
             *                     └┘
             */
            val sampleWidth = (bitmapSize.height * constraintRatio).toInt()
            val sampleStart = (bitmapSize.width - sampleWidth) / 2
            val sampleEnd = sampleStart + sampleWidth
            result.set(sampleStart, 0, sampleEnd, bitmapSize.height)
        }

        return result
    }

    private fun calculatePanelDrawingRegion(panelSpec: PanelSpec, isRtl: Boolean, sliceIndex: Int): List<Rect> {
        val sliceGap = panelSpec.sliceGap
        val sliceSize = panelSpec.sliceSize
        val panelSize = panelSpec.panelSize
        val panelIndent = if (isRtl) (panelSize.width - sliceSize.width) else 0
        val panelOffsetDirection = if (isRtl) -1 else 1
        /*
         *           ┌───┐
         * slice:    │ + │
         *           └───┘
         *          ┆┌───┬───┬───┬───┬───┬───┐┆
         * panel:   ┆│ x │ x │   │   │   │   │┆
         *          ┆└───┴───┴───┴───┴───┴───┘┆
         *          ┆        ┌───┬───┬───┬───┐┆
         * drawing: ┆        │ + │ + │ + │ + │┆
         *          ┆        └───┴───┴───┴───┘┆
         */
        return (sliceIndex until panelSpec.sliceCount)
            .map { sliceOffsetIndex ->
                val dstRectStart = panelIndent + sliceOffsetIndex * (sliceSize.width + sliceGap) * panelOffsetDirection
                val dstRectEnd = dstRectStart + sliceSize.width
                Rect().apply { set(dstRectStart, 0, dstRectEnd, sliceSize.height) }
            }
    }

    private fun performDrawSlice(bitmapCache: BitmapCache, rawBitmap: Bitmap, sampleRegion: Rect, drawingRegions: List<Rect>) {
        GLog.d(TAG, "performDrawSlice: with lock")
        bitmapCache.lock.withLock {
            if (bitmapCache.released) {
                GLog.w(TAG, "[performDrawSlice] bitmapCache had released")
                return
            }
            drawingRegions.forEach { drawingRegion ->
                bitmapCache.canvas.drawBitmap(rawBitmap, sampleRegion, drawingRegion, null)
            }
        }
        GLog.d(TAG, "performDrawSlice: unlocked")
    }

    private fun performInvalidateBitmap(bitmapCache: BitmapCache?, hasContent: Boolean, from: String = "default") {
        val bitmapData = bitmapCache?.bitmapData?.copy(hasContent = hasContent) ?: return
        bitmapData.bitmap.prepareToDraw()
        if ((GProperty.PROPERTY_OTEST_RUNNING) || (GProperty.PROPERTY_MONKEY_RUNNING)) {
            GLog.e(TAG, LogFlag.DL) {
                "[performInvalidateBitmap] from=$from, " +
                        "copyBitmap hashCode=${bitmapData.bitmap.hashCode()}, " +
                        "srcHashCode=${bitmapCache.bitmapData.bitmap.hashCode()}"
            }
        }
        GlobalScope.launch(Dispatchers.Main) { onBitmapLoadedListener?.onBitmapDataLoaded(bitmapData, "performInvalidateBitmap($from)") }
    }

    private val Long.isLoadIdExpired: Boolean
        get() = loadingIdCreator.get() != this

    private data class BitmapCache(
        val bitmap: Bitmap,
        val canvas: Canvas,
        val bitmapData: BitmapData
    ) {
        var released = false
        val lock = ReentrantLock()
        fun recycle() {
            // 因为 bitmap 是给到 ThumbnailSeekBar 了，控制权不在 Loader 内，不要主动回收，避免异常
            lock.withLock {
                released = true
                if (bitmapData.bitmapSpec.shouldAutoRecycle) {
                    recycleBitmap(bitmap, "shouldAutoRecycle")
                }
                canvas.setBitmap(null)
            }
        }
    }

    private data class SliceRecord(
        val loadingId: Long,
        val index: Int,
        val rawBitmap: Bitmap,
        val bitmapCache: BitmapCache,
    )

    internal data class PanelSpec(
        val sliceCount: Int = 0,
        val sliceGap: Int = 0,
        val sliceSize: Size = Size(0, 0),
        val panelSize: Size = Size(0, 0)
    ) {
        val isEmpty: Boolean
            get() = (sliceCount <= 0)
                    || sliceSize.isEmpty
                    || panelSize.isEmpty
    }

    internal data class BitmapSpec(
        val shouldAutoRecycle: Boolean = true,
        val isRtl: Boolean = false,
        val constraintSize: Size = Size(0, 0),
        val sliceGap: Int = 0,
        val sliceSize: Size = Size(0, 0),
        val sliceMaxCount: Int = THUMB_MAX_COUNT,
        val panelMeasureMode: PanelSpecMode = PanelSpecMode.AUTO_SIZE
    ) {

        val isEmpty: Boolean by lazy {
            constraintSize.isEmpty
                    || sliceSize.isEmpty
                    || (sliceMaxCount <= 0)
        }

        val panelSpec: PanelSpec by lazy { calculatePanelSpec(this) }

        private fun calculatePanelSpec(bitmapSpec: BitmapSpec): PanelSpec {
            if (bitmapSpec.isEmpty) {
                return PanelSpec()
            }
            return when (bitmapSpec.panelMeasureMode) {
                PanelSpecMode.AUTO_SIZE -> calculatePanelSpecAutoSizeMode(bitmapSpec)
                PanelSpecMode.SLICE_SIZE -> calculatePanelSpecSliceSizeMode(bitmapSpec)
            }
        }

        /**
         * PanelSpecMode.AUTO_SIZE 模式下会挤压内容
         */
        private fun calculatePanelSpecAutoSizeMode(bitmapSpec: BitmapSpec): PanelSpec {
            val constraintWidth = bitmapSpec.constraintSize.width
            val constraintHeight = bitmapSpec.constraintSize.height
            val sliceGap = bitmapSpec.sliceGap
            val sliceWidth = bitmapSpec.sliceSize.width
            val sliceHeight = min(constraintHeight, bitmapSpec.sliceSize.height)
            val sliceCount = min(bitmapSpec.sliceMaxCount, (constraintWidth + sliceGap) / (sliceWidth + sliceGap))
            val panelWidth = sliceCount * (sliceWidth + sliceGap) - sliceGap
            val panelHeight = min(constraintHeight, bitmapSpec.sliceSize.height)
            return PanelSpec(
                sliceCount = sliceCount,
                sliceGap = sliceGap,
                sliceSize = Size(sliceWidth, sliceHeight),
                panelSize = Size(panelWidth, panelHeight)
            )
        }

        /**
         * SLICE_SIZE 模式下内容不挤压
         */
        private fun calculatePanelSpecSliceSizeMode(bitmapSpec: BitmapSpec): PanelSpec {
            val sliceGap = bitmapSpec.sliceGap
            val sliceWidth = bitmapSpec.sliceSize.width
            val sliceHeight = bitmapSpec.sliceSize.height
            val sliceCount = bitmapSpec.sliceMaxCount
            val panelWidth = sliceCount * (sliceWidth + sliceGap) - sliceGap
            val panelHeight = bitmapSpec.sliceSize.height

            return PanelSpec(
                sliceCount = sliceCount,
                sliceGap = sliceGap,
                sliceSize = Size(sliceWidth, sliceHeight),
                panelSize = Size(panelWidth, panelHeight)
            )
        }

        private companion object {
            private const val THUMB_MAX_COUNT = 15
        }
    }

    /**
     *  Panel 背景填充模式
     *
     * 备注：constraintXXX sliceGap 等具体释义看这里: photo_page/docs/assets/seekbar/缩图SeekBar-布局示例.jpg
     */
    internal enum class PanelSpecMode {
        /**
         * AUTO_SIZE
         *
         * 根据内容大小向下调整，支持 padding 设置
         * 确保正好能完整显示缩图内容，不会裁剪，但是会压缩内容 ：以将内容全部显示在可视区域为目的
         * 缩图的显示数量由 [SeekBar.sliceGap]、[SeekBar.sliceWidth]、[SeekBar.sliceMaxCount] 共同决定
         * 宽度计算：min(SeekBar.sliceMaxCount, (constraintWidth + SeekBar.sliceGap) / (SeekBar.sliceWidth + SeekBar.sliceGap))
         * 高度计算：min(constraintHeight, SeekBar.sliceHeight)
         */
        AUTO_SIZE,

        /**
         * SLICE_SIZE
         *
         * 根据内容大小进行测量宽度
         * 宽度计算： SeekBar.sliceMaxCount * (SeekBar.sliceWidth + SeekBar.sliceGap) - SeekBar.sliceGap
         * 高度计算： SeekBar.sliceHeight
         */
        SLICE_SIZE;
    }

    internal data class BitmapData(
        val bitmap: Bitmap,
        val bitmapSpec: BitmapSpec,
        val hasContent: Boolean
    )

    internal fun interface OnBitmapDataLoadedListener {

        fun onBitmapDataLoaded(bitmapData: BitmapData, from: String)
    }

    companion object {
        private const val TAG = "ThumbnailSeekBarLoader"

        private val Size.isEmpty: Boolean
            get() = (width <= 0) || (height <= 0)

        private val Bitmap.size: Size
            get() = Size(this.width, this.height)

        private fun allocateBitmap(size: Size): Bitmap {
            val bitmap = BitmapPools.getBitmap(size.width, size.height, Bitmap.Config.ARGB_8888)
            bitmap?.eraseColor(Color.TRANSPARENT)
            return bitmap ?: Bitmap.createBitmap(size.width, size.height, Bitmap.Config.ARGB_8888)
        }

        /**
         * 回收 [Bitmap]，内部会将 [Bitmap] 复用
         */
        internal fun recycleBitmap(bitmap: Bitmap, reason: String = "default") {
            if ((GProperty.PROPERTY_OTEST_RUNNING) || (GProperty.PROPERTY_MONKEY_RUNNING)) {
                GLog.e(TAG, LogFlag.DL) { "[recycleBitmap] reason=$reason,  hashCode=${bitmap.hashCode()}" }
                (Exception()).printStackTrace()
            }
            BitmapPools.recycle(bitmap)
        }
    }
}