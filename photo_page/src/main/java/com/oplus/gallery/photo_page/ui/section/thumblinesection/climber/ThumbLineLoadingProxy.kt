/*********************************************************************************
 ** Copyright (C), 2022-2032, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : ThumbLineLoadingProxy.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/03/30
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>               2023/03/30  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.ui.section.thumblinesection.climber

import android.graphics.drawable.Drawable
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import com.oplus.gallery.business.contentloading.ContentFactory.Companion.LOAD_TYPE_MICRO_THUMBNAIL
import com.oplus.gallery.business.contentloading.SizeType
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.photo_page.ui.section.thumblinesection.climber.ThumbLineClimberStateMachine.IThumbLineLoadingProxy
import com.oplus.gallery.photo_page.ui.section.thumblinesection.climber.ThumbLineClimberStateMachine.ReleaseContentStateReply
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.dataloading.PhotoItemViewData
import com.oplus.gallery.photo_page.viewmodel.dataloading.photoViewDataSet
import com.oplus.gallery.photo_page.viewmodel.dataloading.totalCount
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PhotoPageManagementViewModel
import com.oplus.gallery.photopager.strategy.Slottable
import com.oplus.gallery.standard_lib.app.UI
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentHashMap

/**
 * 缩图轴内容加载代理，内部有加载限流、加载策略控制
 */
internal class ThumbLineLoadingProxy(
    lifecycleOwner: LifecycleOwner?,
    private val viewModel: PhotoViewModel,
    private val isSlotVisibleToUser: (Int) -> Boolean
) : IThumbLineLoadingProxy {

    /**
     * 数据请求池
     */
    private val bindDataRequests = ConcurrentHashMap<ThumbLineSlot, DataRequester>()

    /**
     * 执行 [updateBindDataRequests] 时，针对不可见的slot，是否需要延迟执行。
     */
    private var shouldDelayBindInvisibleSlot: Boolean = true

    init {
        lifecycleOwner?.let {
            viewModel.dataLoading.diffedPhotoViewDataSet.observe(it) {
                GTrace.trace({ "$TAG.diffedPhotoViewDataSet" }) {
                    updateBindDataRequests()
                }
            }
            viewModel.pageManagement.photoPageTransitionState.observe(it) { state ->
                if (state == PhotoPageManagementViewModel.PageTransitionState.PRESENTED) {
                    // 拦截刚进入大图页时的 updateBindDataRequests() 操作一段时间,避开刚进入大图页时的繁忙阶段
                    shouldDelayBindInvisibleSlot = false
                    // 主动调用一次 updateBindDataRequests(),避免前面拦截掉了无法执行
                    updateBindDataRequests()
                }
            }
            viewModel.pageManagement.isUnderImmersionInteractive.observe(it) { isUnderImmersionInteractive ->
                if (!isUnderImmersionInteractive) {
                    // 主动调用一次 updateBindDataRequests(),避免前面拦截掉了无法执行
                    updateBindDataRequests()
                }
            }
            viewModel.pageManagement.firstFrameRenderingStatus.observe(it) { status ->
                if (status.isContentReady || status.isThumbnailReady) {
                    // 主动调用一次 updateBindDataRequests(),避免前面拦截掉了无法执行
                    updateBindDataRequests()
                }
            }
        }
    }

    override fun unloadContent(stateReply: ReleaseContentStateReply) {
        stateReply.thumbLineSlot.viewData?.let { viewData ->
            if (shouldReleaseCache(stateReply)) {
                viewModel.contentLoading.releaseLoadedContent(viewData = viewData, sizeType = SizeType.MicroThumb(LOAD_TYPE_MICRO_THUMBNAIL))
                stateReply.thumbLineSlot.content = null
            } else {
                GLog.d(
                    TAG, LogFlag.DL, "[unloadContent], position:${stateReply.thumbLineSlot.slot}," +
                            " data:${stateReply.thumbLineSlot.viewData} should not be release."
                )
            }
        } ?: let {
            GLog.w(TAG, LogFlag.DL) { "[unloadContent] viewData is null for position ${stateReply.thumbLineSlot.slot}" }
        }
    }

    override fun loadContent(thumbLineSlot: ThumbLineSlot, callback: (content: Drawable?) -> Unit) {
        thumbLineSlot.viewData?.let { viewData ->
            viewModel.contentLoading.startLoadContent(viewData = viewData, SizeType.MicroThumb(LOAD_TYPE_MICRO_THUMBNAIL)) {
                // 如果数据变化，这里不绘制，但是之前显示的图片会被释放，导致缩略图半黑或者其他异常，参考大图，不拦截，判断是这个图片即可
                thumbLineSlot.content = if (viewData.id == thumbLineSlot.viewData?.id) {
                    it
                } else {
                    GLog.d(TAG, LogFlag.DL) {
                        "[loadContent] content:${it.hashCode()}, but viewData:$viewData, slotViewData:${thumbLineSlot.viewData}, not equals"
                    }
                    null
                }
                callback.invoke(thumbLineSlot.content)
            }
        } ?: let {
            GLog.w(TAG, LogFlag.DL) { "[loadContent] viewData is null for position ${thumbLineSlot.slot}" }
        }
    }

    override fun bindData(thumbLineSlot: ThumbLineSlot, bindDataReply: (Boolean) -> Unit) {
        val position = thumbLineSlot.slot
        if (position !in (0 until viewModel.dataLoading.totalCount)) {
            GLog.e(TAG, LogFlag.DL) { "bindData error, position is $position not in range:[0:${viewModel.dataLoading.totalCount}" }
            bindDataReply.invoke(false)
            return
        }

        if (shouldDelayBindData(position) || !isNeedLoadData()) {
            addBindRequest(thumbLineSlot, bindDataReply)
            return
        }

        val viewData = viewModel.dataLoading.photoViewDataSet?.get(position) ?: let {
            addBindRequest(thumbLineSlot, bindDataReply)
            return
        }

        thumbLineSlot.viewData = viewData
        bindDataReply.invoke(true)
    }

    override fun unbindData(thumbLineSlot: ThumbLineSlot) {
        thumbLineSlot.viewData = null
        bindDataRequests.remove(thumbLineSlot)
    }

    private fun shouldReleaseCache(stateReply: ReleaseContentStateReply): Boolean {
        // 如果需要强制释放资源，那就释放吧。
        if (stateReply.forceReleaseContent) {
            return true
        }

        // 1. 判断生命周期,是否需要
        if (viewModel.pageLifecycle?.currentState?.isAtLeast(Lifecycle.State.CREATED) != true) {
            return true
        }

        // 2. visibleRange 为空，释放
        val dataRange = viewModel.dataLoading.photoViewDataSet?.dataRange ?: return true

        // 3.不在 visibleRange 范围内，释放
        if (dataRange.isEmpty() || (stateReply.thumbLineSlot.slot !in dataRange)) return true

        // 6.判断数据 id 状态。如果加载缩图失败，则只会有高清图，重新加载中要求加载好之后再替换，否则会有闪黑现象
        stateReply.thumbLineSlot.viewData?.id?.let { oldId ->
            if (oldId != viewModel.dataLoading.photoViewDataSet?.get(stateReply.thumbLineSlot.slot)?.id) {
                return true
            }

            return false
        }
        return true
    }

    private fun addBindRequest(thumbLineSlot: ThumbLineSlot, bindDataReply: (Boolean) -> Unit) {
        val requester = DataRequester(thumbLineSlot) { itemViewData ->
            thumbLineSlot.viewData = itemViewData
            bindDataReply.invoke(itemViewData != null)
        }

        bindDataRequests.put(thumbLineSlot, requester)
    }

    private fun updateBindDataRequests() {
        if (!isNeedLoadData()) {
            return
        }
        bindDataRequests.forEach { (key, requester) ->
            val position = requester.thumbLineSlot.slot
            if (shouldDelayBindData(position)) {
                return@forEach
            }

            bindDataRequests.remove(key)
            viewModel.launch(Dispatchers.UI) {
                GTrace.trace({ "$TAG.updateBindDataRequests.$position" }) {
                    val totalCount = viewModel.dataLoading.totalCount
                    if (position !in (0 until totalCount)) {
                        requester.dataBindCallback(null)
                        GLog.i(TAG, LogFlag.DL, "[updateBindDataRequests] current position $position is not in range[0:$totalCount")
                        return@trace
                    }

                    viewModel.dataLoading.photoViewDataSet?.get(position)?.let {
                        // 如果这个绑定数据的请求已经被处理了，则从队列中移除
                        requester.dataBindCallback(it)
                        return@trace
                    }

                    /**
                     * 当前position有效，但是在[diffedViewDataSet]中无法找到，说明[diffedViewDataSet]还未填充完，
                     * 则需要重新放回[bindDataRequests]，等待下一次轮询
                     */
                    GLog.e(TAG, LogFlag.DL) { "[updateBindDataRequests] can't find data for position $position, totalCount:$totalCount, will retry" }
                    bindDataRequests.put(key, requester)
                }
            }
        }
    }

    /**
     * 在刚进入大图时，如入场动画阶段，此时缩图轴会加载很多的资源和数据，可能导致主线程卡顿。因此做如下优化
     *
     * 1. [canUpdateBindDataRequests]为false，目前入场动画结束后才为true，则只允许处于屏幕真实可见区域的slot加载，其他不可见的，则延时加载。
     * 2. 待[canUpdateBindDataRequests]为true时，则不再拦截。
     *
     * @param slotId Int
     * @return Boolean
     */
    private fun shouldDelayBindData(slotId: Int): Boolean {
        /**
         * 如果不需要延迟加载不可见的Slot，则直接绑定数据即可。
         */
        if (shouldDelayBindInvisibleSlot.not()) {
            return false
        }

        /**
         * 如果需要绑定的slot id为 [NO_SLOT]，说明状态机还未准备好，此时还无法绑定数据，则不绑定数据。
         */
        if (slotId == Slottable.NO_SLOT) {
            return true
        }

        /**
         * 如果当前slot对用户还不可见，则需要延迟加载。
         */
        if (isSlotVisibleToUser(slotId).not()) {
            return true
        }

        return false
    }

    /**
     * 相机拍照进相册时，缩略图轴的加载比较快，大图还未加载出来，就开始解码缩略图轴的任务了。
     * 这样大图来了之后，任务被占满了，只能等到缩图轴小缩图解码完成之后再排队。因此做如下优化
     *
     * 1. [isNeedLoadData]为false，目前在非沉浸式并且大图第一帧渲染之后才为true，则延时加载缩图轴数据。
     * 2. 待[isNeedLoadData]为true时，则不再拦截。
     *
     * @return Boolean
     */
    private fun isNeedLoadData(): Boolean {
        /**
         * 无渲染状态，延后绑定数据。
         */
        val renderingStatus = viewModel.pageManagement.firstFrameRenderingStatus.value ?: let {
            return false
        }
        val isFirstFrameRendered = renderingStatus.isContentReady || renderingStatus.isThumbnailReady
        val isUnderImmersionInteractive = viewModel.pageManagement.isUnderImmersionInteractive.value == true
        /**
         * 如果当前是沉浸式，且相册大图第一帧还未渲染，则延后绑定数据。
         */
        if (isUnderImmersionInteractive && !isFirstFrameRendered) {
            return false
        }

        return true
    }

    private data class DataRequester(
        val thumbLineSlot: ThumbLineSlot,
        val dataBindCallback: (PhotoItemViewData?) -> Unit
    )

    private companion object {
        private const val TAG = "ThumbLineLoadingProxy"
    }
}