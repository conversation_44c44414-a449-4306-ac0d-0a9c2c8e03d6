/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : SlideUpEvent.kt
 ** Description : slot滑动的事件
 ** Version     : 1.0
 ** Date        : 2025/4/4
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2025/4/4      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.photo_page.ui.section.photopagersection

import android.view.MotionEvent

/**
 * slot上滑的事件
 */
sealed class SlideUpEvent {
    /**
     * 上滑开始
     */
    object SlideUpBegin : SlideUpEvent()

    /**
     * 上滑中
     */
    data class SlidingUp(
        /**
         * 上滑事件中，开始的 [MotionEvent]
         */
        val eventStart: MotionEvent,

        /**
         * 上滑事件中，当前的 [MotionEvent]
         */
        val eventEnd: MotionEvent
    ) : SlideUpEvent()

    /**
     * 上滑结束
     */
    object SlideUpEnd : SlideUpEvent()
}