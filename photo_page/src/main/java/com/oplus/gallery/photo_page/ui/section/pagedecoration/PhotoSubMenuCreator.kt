/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoSubMenuCreator.kt
 ** Description : 大图菜单项的子菜单生成器
 ** Version     : 1.0
 ** Date        : 2022/03/18
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON>@Apps.Gallery              2022/03/18  1.0         create file
 *********************************************************************************/
package com.oplus.gallery.photo_page.ui.section.pagedecoration

import android.content.res.Resources
import android.graphics.drawable.Drawable
import com.coui.appcompat.poplist.PopupListItem
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_CONVERT_PDF
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_COPY
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_CUSTOM_REMOVE_WIDGET_LIST
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_DLNA
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_EXPORT_OLIVE
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_EXPORT_VIDEO
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_FREE_FACE
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_GOOGLE_PASS_SCAN
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_MOVE
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_OPEN_IN_SYSTEM_PLAYER
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_RECOMMENDED_REMOVE_WIDGET_LIST
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_REMOVE_LABEL
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_RENAME_FILE
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_SAFE_BOX
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_SAVE_TO_GIF
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_SETAS_CONTACT
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_SETAS_WALLPAPER
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_SET_COVER
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_SLIDESHOW
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_TRANSFORM_HEIF_TO_JPEG
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_TRANSFORM_TO_SDR
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.ui.section.pagedecoration.PhotoSubMenuCreator.PopupListWindowItem.Companion.HIDE_TIPS
import com.oplus.gallery.photo_page.ui.section.pagedecoration.PhotoSubMenuCreator.PopupListWindowItem.Companion.SHOW_TIPS_WITH_RED_DOT
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.inputarguments.PhotoInputArgumentsViewModel
import com.oplus.gallery.photo_page.viewmodel.menucontrol.MenuState
import com.oplus.gallery.photo_page.viewmodel.menucontrol.PhotoMenuItemTips
import com.oplus.gallery.standard_lib.ui.bottomnavigation.BottomNavigationView

/**
 * 针对大图适配的子菜单生成器，
 * 如果后续菜单换用其他控件，应修改此生成器来适配新的菜单控件
 */
internal class PhotoSubMenuCreator(
    /**
     * 更多菜单依附的父菜单项
     */
    private val parentMenuItem: Int = Resources.ID_NULL,
) {

    /**
     * 根据[MenuState]创建[BottomNavigationView]可用的子菜单项
     */
    fun createSubMenu(presentMenuItems: Array<SubMenuItem>, menuState: MenuState): SubMenu = SubMenu(
        parentMenuItem = parentMenuItem,
        menuItems = mutableListOf<SubMenuItem>().also { outMenuItems ->
            presentMenuItems.forEach { subMenuItem ->
                if (menuState.menuItemStates.containsKey(subMenuItem.action)) {
                    menuState.menuItemStates[subMenuItem.action]?.let { menuItemState ->
                        /**
                         * 获取是否显示红点提示, 目前更多菜单只支持红点提示。
                         * 后续若更多菜单有文字提示需求，参照顶部或底部菜单的提示实现方式。
                         */
                        subMenuItem.hasTips = (menuItemState.tips is PhotoMenuItemTips.ItemTipsNone).not()
                    }
                    outMenuItems.add(subMenuItem)
                }
            }
        }
    )

    /**
     * 大图菜单“更多”可显示的的全部菜单项。
     */
    @Suppress("LongMethod")
    fun createAllMoreMenuItems(viewModel: PhotoViewModel? = null) = arrayOf(
        // 导出实况
        SubMenuItem(
            R.id.action_export_olive,
            OPERATION_SUPPORT_EXPORT_OLIVE,
            R.string.photopage_export_olive,
            Resources.ID_NULL
        ),
        // 登机牌添加到Google Wallet
        SubMenuItem(
            R.id.action_google_pass_scan,
            OPERATION_SUPPORT_GOOGLE_PASS_SCAN,
            com.oplus.gallery.basebiz.R.string.title_google_pass_scan,
            Resources.ID_NULL
        ),
        SubMenuItem(
            R.id.action_set_cover,
            OPERATION_SUPPORT_SET_COVER,
            R.string.photopage_popup_setas_album_cover,
            Resources.ID_NULL
        ),
        SubMenuItem(
            R.id.action_slideshow,
            OPERATION_SUPPORT_SLIDESHOW,
            R.string.picture3d_slideshow,
            Resources.ID_NULL
        ),
        SubMenuItem(
            R.id.action_custom_remove_from_widget_list,
            OPERATION_SUPPORT_CUSTOM_REMOVE_WIDGET_LIST,
            Resources.ID_NULL,
            com.oplus.gallery.basebiz.R.drawable.base_free_from_group_selector
        ),
        SubMenuItem(
            R.id.action_recommended_remove_from_widget_list,
            OPERATION_SUPPORT_RECOMMENDED_REMOVE_WIDGET_LIST,
            Resources.ID_NULL,
            com.oplus.gallery.basebiz.R.drawable.base_free_from_group_selector
        ),
        SubMenuItem(
            com.oplus.gallery.basebiz.R.id.action_remove_from_label,
            OPERATION_SUPPORT_REMOVE_LABEL,
            com.oplus.gallery.basebiz.R.string.base_remove_from_label,
            com.oplus.gallery.basebiz.R.drawable.base_ic_remove_selector
        ),
        //0:另存为视频
        SubMenuItem(
            R.id.action_export_video,
            OPERATION_SUPPORT_EXPORT_VIDEO,
            com.oplus.gallery.basebiz.R.string.base_export_video,
            Resources.ID_NULL
        ),
        //另存为 GIF
        SubMenuItem(
            R.id.action_save_to_gif,
            OPERATION_SUPPORT_SAVE_TO_GIF,
            com.oplus.gallery.basebiz.R.string.gif_synthesis_item_save_name,
            Resources.ID_NULL
        ),
        // 不是此人
        SubMenuItem(
            com.oplus.gallery.basebiz.R.id.action_free_face_from_group,
            OPERATION_SUPPORT_FREE_FACE,
            when (viewModel?.menuControl?.dataSource?.whichDataSet) {
                PhotoInputArgumentsViewModel.DataSource.DataSet.PERSON -> com.oplus.gallery.basebiz.R.string.base_free_from_group
                PhotoInputArgumentsViewModel.DataSource.DataSet.PET -> com.oplus.gallery.basebiz.R.string.not_they
                PhotoInputArgumentsViewModel.DataSource.DataSet.PERSON_PET_GROUP -> com.oplus.gallery.basebiz.R.string.not_them
                else -> com.oplus.gallery.basebiz.R.string.base_free_from_group
            },
            com.oplus.gallery.basebiz.R.drawable.base_free_from_group_selector
        ),
        //移动到图集
        SubMenuItem(
            com.oplus.gallery.basebiz.R.id.action_move_to,
            OPERATION_SUPPORT_MOVE,
            com.oplus.gallery.basebiz.R.string.base_move_to_gallery,
            com.oplus.gallery.basebiz.R.drawable.base_ic_move_to_selector
        ),
        //复制到图集
        SubMenuItem(
            R.id.action_copy_to,
            OPERATION_SUPPORT_COPY,
            com.oplus.gallery.basebiz.R.string.base_copy_to,
            Resources.ID_NULL
        ),
        //重命名
        SubMenuItem(
            R.id.action_rename_file,
            OPERATION_SUPPORT_RENAME_FILE,
            com.oplus.gallery.basebiz.R.string.base_rename_photo,
            Resources.ID_NULL
        ),
        //转PDF
        SubMenuItem(
            R.id.action_convert_photo_to_pdf,
            OPERATION_SUPPORT_CONVERT_PDF,
            com.oplus.gallery.basebiz.R.string.base_convert_photo_to_pdf_photopage,
            Resources.ID_NULL
        ),
        //设为壁纸
        SubMenuItem(
            R.id.action_setas_wallpaper,
            OPERATION_SUPPORT_SETAS_WALLPAPER,
            R.string.picture3d_popup_setas_wallpaper,
            R.drawable.picture3d_setas_wallpaper_selector
        ),
        //设为联系人头像
        SubMenuItem(
            R.id.action_setas_contact,
            OPERATION_SUPPORT_SETAS_CONTACT,
            R.string.picture3d_popup_setas_contact,
            R.drawable.picture3d_setas_contact_selector
        ),
        //设为私密
        SubMenuItem(
            com.oplus.gallery.basebiz.R.id.action_encrypt,
            OPERATION_SUPPORT_SAFE_BOX,
            com.oplus.gallery.basebiz.R.string.base_safe_encryption_nemu,
            R.drawable.photopage_ic_menu_safe_box_selector
        ),
        //转为兼容格式
        SubMenuItem(
            R.id.action_transform_to_sdr,
            OPERATION_SUPPORT_TRANSFORM_TO_SDR,
            com.oplus.gallery.basebiz.R.string.base_transform_to_compatible_formats,
            Resources.ID_NULL
        ),
        SubMenuItem(
            R.id.action_transform_heif_to_jpeg,
            OPERATION_SUPPORT_TRANSFORM_HEIF_TO_JPEG,
            com.oplus.gallery.basebiz.R.string.base_transform_to_compatible_formats,
            Resources.ID_NULL
        ),
        //系统播放器播放
        SubMenuItem(
            R.id.action_open_in_system_player,
            OPERATION_SUPPORT_OPEN_IN_SYSTEM_PLAYER,
            com.oplus.gallery.basebiz.R.string.base_open_in_system_player,
            Resources.ID_NULL
        ),
        //投屏播放
        SubMenuItem(
            com.oplus.gallery.basebiz.R.id.action_dlna,
            OPERATION_SUPPORT_DLNA,
            com.oplus.gallery.basebiz.R.string.base_projection_play,
            Resources.ID_NULL
        ),
    )

    /**
     * 获取更多菜单MenuItem对应的title的资源id
     */
    fun makeMoreMenuItemTitleId(
        viewModel: PhotoViewModel,
        subMenuItem: SubMenuItem
    ): Int {
        // case 1: 来自桌面卡片，获取对应的title资源id
        val widgetState = viewModel.widgetManagement.widgetState.value
        if ((subMenuItem.supportOperations == OPERATION_SUPPORT_CUSTOM_REMOVE_WIDGET_LIST
                    || subMenuItem.supportOperations == OPERATION_SUPPORT_RECOMMENDED_REMOVE_WIDGET_LIST)
            && (widgetState != null)) {
            return widgetState.removeFromWidgetListResId
        }

        // 其它获取title资源id的操作放在此处

        // 默认返回SubMenuItem中保存的资源ID
        return subMenuItem.titleResId
    }

    data class SubMenu(
        val parentMenuItem: Int,
        val menuItems: List<SubMenuItem>
    )

    data class SubMenuItem(
        var action: Int = Resources.ID_NULL,
        var supportOperations: Long = 0L,
        var titleResId: Int = Resources.ID_NULL,
        var iconResId: Int = Resources.ID_NULL,
        var groupId: Int = Resources.ID_NULL,
        var hasTips: Boolean = false
    )

    /**
     * PopupListWindow 展示数据使用的Item。
     * 此处仅是将 POJO 转换为了 kotlin 的 data class，便于使用。
     */
    data class PopupListWindowItem(
        /**
         * 标题文本
         */
        private val itemTitle: String,
        /**
         * 图标id
         */
        private val itemIconId: Drawable? = null,
        /**
         * 菜单项是否可用
         */
        private val isItemEnable: Boolean,
        /**
         * 是否可选中。
         * - true 显示选择框
         * - false 不显示选择框
         */
        private val checkable: Boolean = false,
        /**
         * 红点提示。
         * - [HIDE_TIPS] `-1` : 不显示提示。
         * - [SHOW_TIPS_WITH_RED_DOT] `0`: 显示一个红点进行提示。
         * - 大于`0`的数字： 显示红色数字进行提示。
         */
        private val tipsCount: Int = HIDE_TIPS,
        /**
         * 分组ID
          */
        private val mGroupId: Int = HIDE_TIPS
    ) : PopupListItem(itemIconId, itemTitle, checkable, false, tipsCount, isItemEnable, null, null, null, -1, mGroupId) {

        init {
            isCheckable = checkable
            this.hintType = MENU_HINT_TYPE_RED_DOT
            this.redDotAmount = tipsCount
        }

        companion object {
            const val SHOW_TIPS_WITH_RED_DOT = 0
            const val HIDE_TIPS = -1
        }
    }
}