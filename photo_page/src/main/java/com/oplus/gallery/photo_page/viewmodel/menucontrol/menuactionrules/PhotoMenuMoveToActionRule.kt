/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoMenuMoveToActionRule.kt
 ** Description : 大图页菜单移动到操作规则
 ** Version     : 1.0
 ** Date        : 2022/03/18
 ** Author      : <PERSON>@Apps.Gallery
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON>@Apps.Gallery              2022/03/18  1.0         create file
 *********************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules

import androidx.annotation.IdRes
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.basebiz.uikit.fragment.BaseFragment
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.business_lib.fileprocessor.FileProcessScene
import com.oplus.gallery.business_lib.menuoperation.MenuAction
import com.oplus.gallery.business_lib.menuoperation.MenuActionGetter
import com.oplus.gallery.business_lib.menuoperation.MenuOperationManager
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.toOriginalItem
import com.oplus.gallery.business_lib.model.data.base.source.DataManager
import com.oplus.gallery.business_lib.model.data.utils.TypeFilterUtils
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant.Value.MENU_ITEM_APPEND_TO
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.dataloading.PhotoItemViewData

/**
 * 大图页菜单移动到操作规则
 */
internal class PhotoMenuMoveToActionRule(
    @IdRes ruleAction: Int,
    viewModel: PhotoViewModel,
    private val fragment: BaseFragment,
    private val activity: BaseActivity
) : PhotoMenuActionRule(TAG, ruleAction, viewModel) {

    override fun execute(viewData: PhotoItemViewData, mediaItem: MediaItem, onDone: () -> Unit) {
        // 埋点： 移动菜单项被点击。
        trackMenuClick(MENU_ITEM_APPEND_TO) {
            it.save()
        }
        viewModel.menuControl.setMenuActionIsClick(true)
        if (ApiDmManager.getCloudSyncDM().isGoogleChannel() == true) {
            viewModel.cloudSync.triggerDownloadOriginal(activity = activity,
                scene = FileProcessScene.DOWNLOAD_ORIGIN_SCENE_MOVE_TO,
                onDownloadOriginalFailAction = { _, _ -> onDone() },
                onDownloadOriginalSuccessAction = { moveTo(viewData, mediaItem, onDone) })
        } else {
            moveTo(viewData, mediaItem, onDone)
            viewModel.menuControl.setMenuActionIsClick(false)
        }
    }

    private fun moveTo(viewData: PhotoItemViewData, mediaItem: MediaItem, onDone: () -> Unit) {
        // 从viewData.id获取的path是PersonPath，移动操作需要localPath才能执行
        val itemPath = mediaItem.toOriginalItem()?.path ?: let {
            GLog.d(TAG, "[execute], itemPath is null. skip. id=${viewData.id}, path=${mediaItem.path}")
            onDone()
            return
        }
        val paths = ArrayList<Path>().apply {
            add(itemPath)
        }
        val itemType = TypeFilterUtils.getMediaTypeByPathString(paths)
        val setPathStr: String = if (mediaSetPath.isEmpty()) {
            // 当从文件管理器-最近进来会走
            DataManager.getDefaultSetOf(itemPath)?.toString() ?: TextUtil.EMPTY_STRING
        } else mediaSetPath
        GLog.d(TAG, "[execute], id = ${viewData.id}, setPathStr=$setPathStr")
        viewModel.pageManagement.notifyTransferToMoveToPage(true)
        MenuOperationManager.doAction(
            action = MenuAction.MOVE_TO,
            paramMap = MenuActionGetter.moveTo.builder
                .setPaths(paths)
                .setFragment(fragment)
                .setAlbumSetPath(setPathStr)
                .setItemsMediaType(itemType)
                .setIsStartActivity(true)
                .setTrackCallerEntry(trackCaller)
                .setLifecycle(fragment.lifecycle)
                .build(),
            onCompleted = { _, _ ->
                onDone()
                viewModel.pageManagement.notifyTransferToMoveToPage(false)
            }
        )
    }

    companion object {
        private const val TAG = "PhotoMenuMoveToActionRule"
    }
}