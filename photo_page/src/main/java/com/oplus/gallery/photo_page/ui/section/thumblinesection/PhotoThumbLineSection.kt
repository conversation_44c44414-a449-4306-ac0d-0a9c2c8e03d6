/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoThumbLineSection.kt
 ** Description : 大图页面 - 缩略图轴切片
 ** Version     : 1.0
 ** Date        : 2023/03/02
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/03/02  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.ui.section.thumblinesection

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams
import android.view.ViewStub
import android.widget.ImageView
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.doOnLayout
import androidx.core.view.isEmpty
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.core.view.updatePadding
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import com.oplus.gallery.basebiz.transition.PhotoSlotSizeCalculator
import com.oplus.gallery.basebiz.transition.TransitionPreviewData
import com.oplus.gallery.business.contentloading.ContentFactory.Companion.LOAD_TYPE_PREVIEW_THUMBNAIL
import com.oplus.gallery.business.contentloading.SizeType
import com.oplus.gallery.foundation.arch.sectionpage.ISectionPage
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.cpu.PerformanceLevelUtils
import com.oplus.gallery.foundation.util.cpu.PerformanceLevelUtils.PerformanceLevel.LOW
import com.oplus.gallery.foundation.util.cpu.PerformanceLevelUtils.PerformanceLevel.LOWER
import com.oplus.gallery.foundation.util.cpu.PerformanceLevelUtils.PerformanceLevel.MIDDLE
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.foundation.util.ext.collect
import com.oplus.gallery.foundation.util.ext.collectNotNull
import com.oplus.gallery.foundation.util.ext.isVisibleAndOpaque
import com.oplus.gallery.foundation.util.math.MathUtil.equalWithinTolerance
import com.oplus.gallery.photo_page.BuildConfig
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.ui.AbstractPhotoSection
import com.oplus.gallery.photo_page.ui.PhotoFragment
import com.oplus.gallery.photo_page.ui.section.pagecontainer.PhotoAlphaSpringAnimation
import com.oplus.gallery.photo_page.ui.section.photomenu.PhotoMenuSection
import com.oplus.gallery.photo_page.ui.section.photopagersection.PhotoPagerSection
import com.oplus.gallery.photo_page.ui.section.photopagersection.PhotoPagerSection.ISlotBatchEventCallback
import com.oplus.gallery.photo_page.ui.section.playback.PhotoPlaybackSection
import com.oplus.gallery.photo_page.ui.section.thumblinesection.PhotoCoordinateLayoutInfoManager.ContentType
import com.oplus.gallery.photo_page.ui.section.thumblinesection.PhotoCoordinateLayoutInfoManager.PresentationType
import com.oplus.gallery.photo_page.ui.section.thumblinesection.PhotoCoordinateLayoutInfoManager.PresentationType.Detail
import com.oplus.gallery.photo_page.ui.section.thumblinesection.PhotoCoordinateLayoutInfoManager.PresentationType.Preview
import com.oplus.gallery.photo_page.ui.section.thumblinesection.PhotoCoordinateLayoutInfoManager.PresentationType.Thumbnail
import com.oplus.gallery.photo_page.ui.section.thumblinesection.climber.ThumbLineClimberStateMachine
import com.oplus.gallery.photo_page.ui.section.thumblinesection.coordinate.PhotoPagerContract
import com.oplus.gallery.photo_page.ui.section.thumblinesection.coordinate.PhotoThumbLineCoordinator
import com.oplus.gallery.photo_page.ui.section.thumblinesection.coordinate.PhotoThumbLineCoordinator.Companion.CONTRACT_ID_PHOTO_PAGER
import com.oplus.gallery.photo_page.ui.section.thumblinesection.coordinate.PhotoThumbLineCoordinator.Companion.CONTRACT_ID_PHOTO_PREVIEW
import com.oplus.gallery.photo_page.ui.section.thumblinesection.coordinate.PhotoThumbLineCoordinator.Companion.CONTRACT_ID_PLAYBACK
import com.oplus.gallery.photo_page.ui.section.thumblinesection.coordinate.PhotoThumbLineCoordinator.Companion.CONTRACT_ID_THUMB_LINE
import com.oplus.gallery.photo_page.ui.section.thumblinesection.coordinate.ThumbLineContract
import com.oplus.gallery.photo_page.ui.section.thumblinesection.playback.PlaybackContract
import com.oplus.gallery.photo_page.ui.section.thumblinesection.playback.PlaybackContract.IThumbLinePlaybackCollaborator
import com.oplus.gallery.photo_page.ui.section.thumblinesection.playback.PlaybackThumbLineProxySeekbar
import com.oplus.gallery.photo_page.ui.section.thumblinesection.playback.PlaybackThumbLineSizeSpecCalculator
import com.oplus.gallery.photo_page.ui.section.thumblinesection.playback.PlaybackThumbLineUIController
import com.oplus.gallery.photo_page.ui.section.thumblinesection.preview.PreviewMaskContract
import com.oplus.gallery.photo_page.ui.section.thumblinesection.preview.PreviewMaskContract.IThumbLinePreviewMaskCollaborator
import com.oplus.gallery.photo_page.ui.theme.MenuDecorationTheme
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.dataloading.DiffedPhotoFocusHint
import com.oplus.gallery.photo_page.viewmodel.dataloading.DiffedPhotoItemViewData
import com.oplus.gallery.photo_page.viewmodel.dataloading.DiffedPhotoViewDataSet
import com.oplus.gallery.photo_page.viewmodel.dataloading.PhotoDataLoadingViewModel
import com.oplus.gallery.photo_page.viewmodel.dataloading.PhotoItemViewData
import com.oplus.gallery.photo_page.viewmodel.dataloading.diffedFocusViewDataWhenPresentAndIdle
import com.oplus.gallery.photo_page.viewmodel.dataloading.focusSlot
import com.oplus.gallery.photo_page.viewmodel.dataloading.isGif
import com.oplus.gallery.photo_page.viewmodel.dataloading.photoViewDataSet
import com.oplus.gallery.photo_page.viewmodel.details.DetailsPanelTransitionStatus
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PageOperationState
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PhotoDecorationState
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PhotoPageManagementViewModel.PageTransitionState.DESTROYED
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PhotoPageManagementViewModel.PageTransitionState.EXITING
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.from
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.thumbLineHeightValue
import com.oplus.gallery.photo_page.viewmodel.playbackcontrol.PhotoPlaybackViewModel.PlaybackUiStyle.THUMB_LINE
import com.oplus.gallery.photo_page.viewmodel.playbackcontrol.PlaybackInfo
import com.oplus.gallery.photo_page.widget.recyclerview.ThumbLineRecyclerView
import com.oplus.gallery.photo_page.widget.seekbar.ISeekBar
import com.oplus.gallery.photo_page.widget.seekbar.ISliceRetrieverFactory
import com.oplus.gallery.photopager.PhotoSlot.RenderingStatus
import com.oplus.gallery.photopager.PhotoSlot.RenderingStatusListener
import com.oplus.gallery.photopager.animationcontrol.createAnimationPropertyProvider
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch

/**
 * 大图页缩略图轴切片
 */
internal class PhotoThumbLineSection(
    sectionPage: ISectionPage<PhotoFragment, PhotoViewModel>
) : AbstractPhotoSection(sectionPage) {
    /**
     * @see PhotoPagerSection
     */
    private val pagerSection: PhotoPagerSection?
        get() = sectionPage.pageInstance.requireSection()

    /**
     * @see PhotoPlaybackSection
     */
    private val photoPlaybackSection: PhotoPlaybackSection?
        get() = sectionPage.pageInstance.requireSection()

    /**
     * @see PhotoMenuSection
     */
    @OptIn(ExperimentalUnsignedTypes::class)
    private val photoMenuSection: PhotoMenuSection?
        get() = sectionPage.pageInstance.requireSection()

    /**
     * 缩略图轴最多可见的 Item 数量
     */
    private val thumbLineMaxVisibleSize: Int
        get() {
            if (::layoutInfoManager.isInitialized.not()) {
                GLog.d(TAG, LogFlag.DL) { "[get thumbLineMaxVisibleSize] layoutInfoManager.isInitialized = false." }
                return INVALID_CONTROL_SLOT_SIZE
            }
            val widthPixels = context?.resources?.displayMetrics?.widthPixels ?: ScreenUtils.displayScreenWidth
            return (widthPixels / layoutInfoManager.getLayoutInfo(ContentType.Image).width).toInt() + 1
        }

    /**
     * @see ISeekBar
     */
    private lateinit var proxySeekbar: ISeekBar

    /**
     * @see PlaybackThumbLineSizeSpecCalculator
     */
    private lateinit var playbackSizeSpecCalculator: PlaybackThumbLineSizeSpecCalculator

    /**
     * 预览蒙版图片控件
     */
    private lateinit var ivThumbLinePreviewMask: ImageView

    /**
     * 缩略图轴
     */
    private lateinit var rvThumbLine: ThumbLineRecyclerView

    /**
     * [rvThumbLine] 使用的 LayoutManager
     */
    private lateinit var thumbLineLayoutManager: ThumbLineLayoutManager

    /**
     * Item 布局的 Manager ,用于获取联动的 [ThumbLineLayoutManager.ItemLayoutInfo] 等
     */
    private lateinit var layoutInfoManager: PhotoCoordinateLayoutInfoManager

    /**
     * 缩图轴 UI，包含控件及其相关辅助类
     */
    private lateinit var thumbLineUI: ThumbLineUI

    /**
     * 缩略图轴的滑动窗口,根据当前的页面状态,驱动每个 Item 的状态机,进行数据加载/资源加载等
     */
    private lateinit var thumbLineWindowLoadingStrategy: ThumbLineWindowLoadingStrategy

    /**
     * [rvThumbLine] 使用的 Adapter
     */
    private lateinit var thumbLineAdapter: ThumbLineAdapter

    /**
     * 负责缩略图轴与大图联动的协调者
     */
    private lateinit var photoThumbLineCoordinator: PhotoThumbLineCoordinator

    /**
     * 参与联动的大图部分
     */
    private lateinit var photoPagerContract: PhotoPagerContract

    /**
     * 参与联动的缩略图轴部分
     */
    private lateinit var thumbLineContract: ThumbLineContract

    /**
     * 参与联动的播控部分
     */
    private lateinit var playbackContract: PlaybackContract

    /**
     * 参与联动的预览蒙版部分
     */
    private lateinit var previewMaskContract: PreviewMaskContract

    /**
     * 监听大图数据变化的 callback
     */
    private val slotBatchEventCallback: SlotBatchEventCallback = SlotBatchEventCallback()

    /**
     * naviBar的色值，会设置到naviBarPlaceholder
     */
    private var naviBarColor: Int = Color.TRANSPARENT

    /**
     * naviBar占位符，高度为NaviBar的实时高度，在多图浏览场景，会同步后者的色值
     */
    private var naviBarPlaceholder: View? = null

    /**
     * 底部菜单栏
     */
    private var vBottomBar: View? = null

    /**
     * [PhotoDataLoadingViewModel.diffedPhotoFocusHint] 的监听回调
     */
    private val diffedPhotoFocusHintChangeObserver: Observer<DiffedPhotoFocusHint?> = Observer<DiffedPhotoFocusHint?> { diff ->
        diff ?: return@Observer

        val focusHint = diff.newPhotoFocusHint ?: let {
            return@Observer
        }

        GTrace.trace({ "$TAG.diffedPhotoFocusHint" }) {
            photoThumbLineCoordinator.coordinateState?.triggeredBy = null

            if (diff.oldPhotoFocusHint != null) {
                // 非第一次从外部进入的时候,通知 Focus hint 变化
                photoPagerContract.changeFocusHint(focusHint.index, focusHint.id)
            } else {
                // 第一次从外部进入的时候,通知 init position
                photoPagerContract.initPosition(focusHint.index)
            }
            pagerSection?.notifySlotFocusChanged(focusHint.index)
            thumbLineWindowLoadingStrategy.notifySlotFocusChanged(focusHint.index)
        }
    }

    /**
     * 缩图轴高度
     */
    private val thumbLineHeight: Int
        get() = sectionPage.pageViewModel.pageManagement.thumbLineHeightValue

    /**
     * 缩略图轴是否初始化过
     */
    private var isThumbLineInitialized = false

    /**
     * 缩图轴调试工具类
     */
    private val thumbLineDebugger: ThumbLineDebugger by lazy { ThumbLineDebugger() }

    private val focusRemovedListener: () -> Unit = {
        updateThumbLineVisibility(VISIBILITY_REASON_DELETE, true)
    }

    /**
     * 离开详情模式时大图的放大状态应该是怎么样的
     * 1. 进入详情模式中：lastIsFocusScaled（记录进入前的缩放）
     * 2. 正常退出详情模式中：false
     */
    private var shouldFocusScaledAfterExitDetails: Boolean? = null
    private var lastIsFocusScaled: Boolean? = null

    /**
     * rvThumbLine的透明度动效
     */
    private val thumbLineAlphaAnimation by lazy {
        PhotoAlphaSpringAnimation(rvThumbLine)
    }

    /**
     * 修改这里必测场景: 相机进相册切换暗色模式
     */
    override fun onViewCreated(view: View) {
        super.onViewCreated(view)

        checkSupportThumbLineStyle {
            if (isThumbLineInitialized) {
                if (BuildConfig.DEBUG) {
                    throw UnsupportedOperationException("thumbLine is instantiated repeatedly")
                } else {
                    GLog.w(TAG) { "[checkSupportThumbLineStyle] ignore, thumbLine is instantiated repeatedly , check onViewCreated returns" }
                }
                return@checkSupportThumbLineStyle
            }

            /** 初始化控件相关内容 */
            initViews(view)

            /**  订阅 VM 数据 */
            subscribeLiveDataFromViewModel()

            pagerSection?.registerFocusSlotRemovedListener(focusRemovedListener)

            isThumbLineInitialized = true

            if (BuildConfig.DEBUG) {
                thumbLineDebugger.attach(rvThumbLine)
            }
        }
    }

    override fun onBackPressed(isBackHandled: Boolean): Boolean {
        if (::rvThumbLine.isInitialized) {
            rvThumbLine.stopScroll()
        }
        return super.onBackPressed(isBackHandled)
    }

    override fun onViewDestroy() {
        super.onViewDestroy()
        if (isThumbLineInitialized) {

            // 反注册监听
            unRegisterListeners()

            // 取消订阅 VM 数据
            unSubscribeLiveDataFromViewModel()

            pagerSection?.unregisterFocusSlotRemovedListener(focusRemovedListener)

            // 停止联动
            unBindCoordinator()

            // 调试类做资源回收
            thumbLineDebugger.detach()

            // 释放引用
            thumbLineWindowLoadingStrategy.release()
        }
    }

    override fun onWindowInsetsChanged(windowInsets: WindowInsetsCompat) {
        super.onWindowInsetsChanged(windowInsets)
        GLog.d(TAG) { "[onWindowInsetsChanged]" }
        adaptThumbLineToNaviBar()
    }

    override fun onAppUiStateChanged(config: AppUiResponder.AppUiConfig) {
        super.onAppUiStateChanged(config)

        if (config.screenMode.isChanged() && ::rvThumbLine.isInitialized) {
            // 折叠屏切换
            (rvThumbLine.parent as? View)?.doOnLayout {
                updateThumbLineVisibility(VISIBILITY_REASON_SCREEN_MODE)
            }
        }

        if (config.windowWidth.isChanged().not()) {
            GLog.d(TAG, LogFlag.DL) { "[onAppUiStateChanged] windowWidth has not changed. return" }
            return
        }

        if (::thumbLineWindowLoadingStrategy.isInitialized.not()) {
            GLog.d(TAG, LogFlag.DL) { "[onAppUiStateChanged] thumbLineWindowLoadingStrategy is not initialized. return" }
            return
        }

        thumbLineWindowLoadingStrategy.changeWindowControlSlotSize(controlSlotSize = calculateControlSlotSize())

        /**
         * changeWindowControlSlotSize之后，给ViewHolder添加SlotLoadedCallback
         */
        rvThumbLine.post {
            thumbLineWindowLoadingStrategy.notifyAddSlotLoadedCallback()
        }
    }

    /**
     * 计算窗口/缩图缓存数量
     */
    private fun calculateControlSlotSize(): Int {
        val maxVisibleSize = thumbLineMaxVisibleSize
        if (maxVisibleSize < 0) {
            GLog.d(TAG, LogFlag.DL) { "[getControlSlotSize] maxVisibleSize < 0" }
            return INVALID_CONTROL_SLOT_SIZE
        }

        /**
         * 缩图轴预加载数据倍率，以thumbLineMaxVisibleSize为base值
         *
         * 规则：
         * 标准/最大系数: [DATA_RANGE_TIMES_NORMAL]
         * 内存等级为 MIDDLE（6G=<内存<=8G）时: [DATA_RANGE_TIMES_MIDDLE]
         * 内存等级为 LOW, LOWER （内存<=4G）时: [DATA_RANGE_TIMES_LOW]
         */
        val dataRangeTimes = when (PerformanceLevelUtils.ramLevel) {
            LOW, LOWER -> DATA_RANGE_TIMES_LOW
            MIDDLE -> DATA_RANGE_TIMES_MIDDLE
            else -> DATA_RANGE_TIMES_NORMAL
        }.also {
            GLog.d(TAG, LogFlag.DL) { "[getControlSlotSize] dataRangeTimes = $it" }
        }

        // 支持的预期数量(一屏的最大数量 * 放大倍率)
        val controlSlotTargetSize = (maxVisibleSize * dataRangeTimes).toInt()
        // 窗口最小缓存数量(可见屏幕数量 + 误差值)
        val controlSlotMinSize = maxVisibleSize + RECYCLERVIEW_VISIBLE_VH_COUNT_ERROR
        // 当预期数量小于最小数量时取controlSlotMinSize，必须保证缩图轴图片可以充满全屏
        return controlSlotTargetSize.coerceAtLeast(controlSlotMinSize)
    }

    /**
     * 校验是否支持缩图轴
     *
     * @param supportedCallback  只有在需要显示缩图轴的时候才会被回调
     *
     * 注意: 只有是缩图轴模式时，才需要处理此Section的所有业务逻辑
     */
    private fun checkSupportThumbLineStyle(supportedCallback: () -> Unit) {
        viewModel.playback.playbackUiStyle.observe(this) { uiStyle ->
            if (uiStyle != THUMB_LINE) {
                return@observe
            }
            supportedCallback.invoke()
        }
    }

    /**
     * 初始化 View
     */
    private fun initViews(view: View) {
        // 初始化预览盖板内容
        initPreviewMask(view)

        // 初始化播控相关内容
        initPlayback(view)

        // 初始化缩略图轴
        initThumbLine(view)

        // 初始化naviBar占位符和底部菜单栏
        initBottomBar(view)

        // 注册监听
        registerListeners()

        // 初始化协调器
        initCoordinator()

        // 开始联动
        bindCoordinator()
    }

    /**
     * 初始化naviBar占位符和底部菜单栏
     */
    private fun initBottomBar(view: View) {
        naviBarColor = view.context.getColor(R.color.photopage_thumbline_list_background)
        naviBarPlaceholder = bindView<View>(R.id.navibar_placeholder)
        vBottomBar = bindView<View>(com.oplus.supertext.ostatic.R.id.bottom_bar)
    }

    /**
     * 初始化预览盖板内容
     */
    private fun initPreviewMask(view: View) {
        ivThumbLinePreviewMask = view.findViewById(R.id.iv_thumb_line_preview_mask)
    }

    /**
     * 初始化播控相关内容：
     * - 创建 [proxySeekbar]，用于衔接缩图轴 [PhotoThumbLineSection] 和 [PhotoPlaybackSection] 进度条控制
     * - 创建 [PlaybackThumbLineUIController]，用于衔接缩图轴 [PhotoThumbLineSection] 和 [PhotoPlaybackSection] 播控控制
     * - 创建 [playbackSizeSpecCalculator]，用于计算播控 seekbar 缩图预览尺寸
     */
    private fun initPlayback(view: View) {
        proxySeekbar = PlaybackThumbLineProxySeekbar()
        photoPlaybackSection?.playbackUIController = PlaybackThumbLineUIController(proxySeekbar)
        playbackSizeSpecCalculator = PlaybackThumbLineSizeSpecCalculator(view.context)
    }

    /**
     * 初始化缩略图轴
     */
    @OptIn(ExperimentalUnsignedTypes::class)
    private fun initThumbLine(view: View) {
        pagerSection?.setFocusSlotDroveByPagerSelection(false)
        rvThumbLine = bindView<ViewStub>(R.id.vs_thumb_line)
            .inflate()
            .findViewById(R.id.rv_thumb_line)

        rvThumbLine.layoutParams.height = thumbLineHeight

        // 如果底栏需要显示，但是当前还没加载出来加到容器时，先隐藏，在底栏添加后再显示，避免缩图轴显示时还没有底栏导致位置跳变的问题。
        if ((photoMenuSection?.couldShowBottomMenu() == true) && (bindView<ViewGroup?>(R.id.bottom_bar_container)?.isEmpty() == true)) {
            rvThumbLine.visibility = View.INVISIBLE
            photoMenuSection?.doOnBottomMenuPrepared { updateThumbLineVisibility(VISIBILITY_REASON_INIT, false, false) }
        }

        adaptThumbLineToNaviBar()

        layoutInfoManager = PhotoCoordinateLayoutInfoManager(view.context, playbackSizeSpecCalculator)

        thumbLineWindowLoadingStrategy = ThumbLineWindowLoadingStrategy(
            windowControlSlotSize = calculateControlSlotSize(),
            strategyCollaborator = ThumbLineWindowLoadingStrategy.ThumbLineStrategyCollaborator(
                thumbLineMaxVisibleSize = thumbLineMaxVisibleSize,
                viewModel = <EMAIL>,
                lifecycleOwner = this@PhotoThumbLineSection,
                observer = object : ThumbLineWindowLoadingStrategy.ISlotObserver {
                    override fun onSlotCreated(position: Int, slotClimber: ThumbLineClimberStateMachine?) = Unit

                    override fun onAddSlotLoadedCallback(position: Int, slotClimber: ThumbLineClimberStateMachine?) {
                        val viewHolder = rvThumbLine.findViewHolderForAdapterPosition(position) as? ThumbLineAdapter.BaseVH
                        viewHolder?.onAddSlotLoadedCallback(position, slotClimber)
                    }

                    override fun onSlotDestroyed(position: Int) {
                        val viewHolder = rvThumbLine.findViewHolderForAdapterPosition(position) as? ThumbLineAdapter.BaseVH
                        viewHolder?.onSlotDestroyed(position)
                    }

                    override fun onSlotDetached(position: Int) {
                        val viewHolder = rvThumbLine.findViewHolderForAdapterPosition(position) as? ThumbLineAdapter.BaseVH
                        viewHolder?.onSlotDetached(position)
                    }

                    override fun onSlotAttached(position: Int) {
                        val viewHolder = rvThumbLine.findViewHolderForAdapterPosition(position) as? ThumbLineAdapter.BaseVH
                        viewHolder?.onSlotAttached(position)
                    }
                }
            )
        )

        thumbLineAdapter = ThumbLineAdapter(view.context, viewModel, thumbLineWindowLoadingStrategy, playbackSizeSpecCalculator)

        thumbLineLayoutManager = ThumbLineLayoutManager(
            context = view.context,
            reverseLayout = false,
            normalItemLayoutInfo = layoutInfoManager.getLayoutInfo(ContentType.Image),
            recyclerViewProxy = RecyclerLayoutProxy(rvThumbLine)
        )

        rvThumbLine.apply {
            layoutManager = thumbLineLayoutManager
            adapter = thumbLineAdapter
            itemAnimator = PhotoThumbLineAnimator(this, onAnimationEnd = {
                viewModel.pageManagement.setShouldDisableThumbLineAnimation(false)
                viewModel.pageManagement.setShouldAnimateWhenRemoved(false)
            })
            setBackgroundColor(context.getColor(sectionPage.pageViewModel.pageManagement.pageTheme.value.menuDecorationTheme.background))
        }

        thumbLineUI = ThumbLineUI(
            view = rvThumbLine,
            adapter = thumbLineAdapter,
            layoutManager = thumbLineLayoutManager,
            layoutInfoManager = layoutInfoManager
        )
    }

    /**
     * 动态控制缩图轴padding
     */
    private fun adaptThumbLineToNaviBar() {
        vBottomBar?.post {
            if ((viewModel.pageManagement.photoPageTransitionState.value?.isExiting() == true)
                || (lifecycle.currentState < Lifecycle.State.CREATED)
            ) {
                return@post
            }
            if (!::rvThumbLine.isInitialized) return@post
            /**
             * 设置缩图轴底部,左/右 padding
             * 1. 折叠屏展开，平板横屏，投屏时，底部菜单会隐藏掉，缩图轴会掉到屏幕底部，被虚拟导航栏遮挡
             * 2. 标准屏横屏，折叠屏小屏横屏等系统导航栏处于屏幕左右位置时，会遮挡缩图轴
             */
            val pageInstance = sectionPage.pageInstance
            val leftPadding = pageInstance.leftNaviBarHeight(false)
            val rightPadding = pageInstance.rightNaviBarHeight(false)
            pageInstance.systemBarController.setNaviBarColor(Color.TRANSPARENT)
            val backgroundColor = (naviBarPlaceholder?.background as? ColorDrawable)?.color
            if (backgroundColor != naviBarColor) {
                naviBarPlaceholder?.setBackgroundColor(naviBarColor)
            }
            /**
             * 如果修改去掉了updatePadding或者修改后layoutParam没有与padding保持同步变更，需要注意layoutParams的更新
             */
            rvThumbLine.updatePadding(left = leftPadding, right = rightPadding)
        }
    }

    /**
     * 初始化联动器
     */
    private fun initCoordinator() {
        // 大图轴联动部分
        photoPagerContract = PhotoPagerContract(
            id = CONTRACT_ID_PHOTO_PAGER,
            registerOnPageChangeCallback = { callback ->
                pagerSection?.registerOnPageChangeCallback(callback)
            },
            unregisterOnPageChangeCallback = { callback ->
                pagerSection?.unregisterOnPageChangeCallback(callback)
            }
        )

        // 缩略图轴联动部分
        thumbLineContract = ThumbLineContract(
            id = CONTRACT_ID_THUMB_LINE,
            thumbLineUI = thumbLineUI,
            thumbLineContractCollaborator = ThumbLineContractCollaborator(),
            motionCallback = MotionCallback()
        )

        // 播控联动部分
        playbackContract = PlaybackContract(
            id = CONTRACT_ID_PLAYBACK,
            thumbLineView = rvThumbLine,
            playbackCollaborator = ThumbLinePlaybackCollaborator()
        )

        // 预览蒙版部分
        previewMaskContract = PreviewMaskContract(
            id = CONTRACT_ID_PHOTO_PREVIEW,
            previewMaskView = ivThumbLinePreviewMask,
            thumbLinePreviewMaskCollaborator = ThumbLinePreviewMaskCollaborator()
        )

        // 构造联动者
        photoThumbLineCoordinator = PhotoThumbLineCoordinator(
            photoPagerContract,
            thumbLineContract,
            playbackContract,
            previewMaskContract
        )
    }

    /**
     * 开始联动
     */
    private fun bindCoordinator() {
        photoThumbLineCoordinator.bind(
            PhotoThumbLineCoordinator.PhotoCoordinatorState(null)
        )
    }

    /**
     * 停止联动
     */
    private fun unBindCoordinator() {
        photoThumbLineCoordinator.unbind()
    }

    /**
     * 注册监听
     */
    private fun registerListeners() {
        pagerSection?.registerSlotBatchEventListener(slotBatchEventCallback)
    }

    /**
     * 从 ViewModel 中注册 LiveData 的监听
     */
    private fun subscribeLiveDataFromViewModel() {

        lifecycleScope.launch {
            viewModel.menuControl.photoMenu.collectNotNull {
                adaptThumbLineToNaviBar()
            }
        }

        viewModel.menuControl.bottomMenuVisibility.observe(this) {
            adaptThumbLineToNaviBar()
        }

        viewModel.pageManagement.photoDecorationState.observe(this) {
            adaptThumbLineToNaviBar()
            if (it.shouldShow) {
                // HIDE 时无需变更可见性
                updateThumbLineVisibility(VISIBILITY_REASON_DECOR)
            }
        }

        viewModel.dataLoading.diffedPhotoFocusHint.observe(this, diffedPhotoFocusHintChangeObserver)

        viewModel.pageManagement.photoPageTransitionState.observe(this) { state ->
            when (state) {
                EXITING, DESTROYED -> {
                    ivThumbLinePreviewMask.visibility = View.INVISIBLE
                }

                else -> GLog.d(TAG, "[subscribeLiveDataFromViewModel] not care page transition state")
            }
        }

        viewModel.pageManagement.thumbLineHeight.observe(this) {
            // rvThumbLine 还未初始化则跳过，避免极限场景下出现异常
            if (::rvThumbLine.isInitialized.not()) return@observe

            rvThumbLine.updateLayoutParams<LayoutParams> {
                height = it
            }
            photoPagerContract.onThumbLineHeightChanged()
        }

        viewModel.menuControl.menuTheme.collect(this) {
            updateThumbLineBackground()
        }

        viewModel.pageManagement.pageTheme.map { it.menuDecorationTheme.background }.collect(this) {
            updateThumbLineBackground()
        }

        viewModel.dataLoading.diffedFocusViewDataWhenPresentAndIdle.collect(this) {
            // 图片变更时重新判断缩图是否需要显示，如滑动切图时重新显示缩图轴
            if (!rvThumbLine.isVisible) {
                updateThumbLineVisibility(VISIBILITY_REASON_DIFFED_PRESENT_IDLE)
            }
        }

        viewModel.pageManagement.appUiRotation.observe(this) {
            rvThumbLine.post {
                updateThumbLineVisibility(VISIBILITY_REASON_ROTATE)
            }
        }

        viewModel.details.transitionStatus.observe(this) {
            shouldFocusScaledAfterExitDetails = null
            when (it) {
                // 阻断场景，图片会回到进入时的状态
                DetailsPanelTransitionStatus.EnterStart -> shouldFocusScaledAfterExitDetails = lastIsFocusScaled
                // 正常退出详情时，会还原图片大小到未放大状态
                DetailsPanelTransitionStatus.ExitStart -> shouldFocusScaledAfterExitDetails = false
                else -> Unit
            }
        }

        viewModel.details.transitionPanelEffectState.observe(this) { panelEffectState ->
            // 进入详情模式时隐藏，退出详情模式时显示
            val isUnderImmersionInteractive = viewModel.pageManagement.isUnderImmersionInteractive.value ?: false
            updateThumbLineVisibility(reason = VISIBILITY_REASON_DETAILS_MODE, withAnim = isUnderImmersionInteractive.not())
        }

        viewModel.pageManagement.shouldDisableThumbLineAnimation.observe(this) { isDisable ->
            (rvThumbLine.itemAnimator as? PhotoThumbLineAnimator)?.apply {
                setSupportRemoveAnimation(isDisable.not())
            }
        }

        viewModel.menuControl.menuActionExecuting.observe(this) {
            // 当有菜单项正在执行时，缩图轴不可点击和滑动
            updateThumbLineUserInputEnabled()
        }

        viewModel.details.isInDetailsMode.observe(this) {
            // 当处于详情模式下时，缩图轴不可以点击和滑动
            updateThumbLineUserInputEnabled()
        }
    }

    /**
     * 从 ViewModel 中反注册 LiveData 的监听
     */
    private fun unSubscribeLiveDataFromViewModel() {
        viewModel.dataLoading.diffedPhotoFocusHint.removeObserver(diffedPhotoFocusHintChangeObserver)
    }

    /**
     * 修改缩图轴是否拦截用户输入事件
     */
    private fun updateThumbLineUserInputEnabled() {
        if (::rvThumbLine.isInitialized.not()) return

        // 当有菜单是否正在执行
        val isMenuExecuting: Boolean = viewModel.menuControl.menuActionExecuting.value ?: false

        // 是否处于详情模式下
        val isInDetailsMode: Boolean = viewModel.details.isInDetailsMode.value ?: false

        // 当处于详情模式或菜单正在处理点击时，任意一个场景满足，缩图轴均不支持点击或滑动
        rvThumbLine.isUserInputEnabled = (isMenuExecuting || isInDetailsMode).not()
    }

    /**
     * 反注册监听
     */
    private fun unRegisterListeners() {
        pagerSection?.unregisterSlotBatchEventListener(slotBatchEventCallback)
    }

    /**
     * 获取 [position] 位置的 [PhotoItemViewData]
     */
    private fun getViewData(position: Int): PhotoItemViewData? {
        return viewModel.dataLoading.photoViewDataSet?.get(position)
    }

    /**
     * 获取 [position] 位置的 [PlaybackInfo]
     */
    private fun getPlaybackInfo(position: Int): PlaybackInfo? {
        if (getViewData(position)?.isVideo != true) {
            return null
        }
        return viewModel.playback.playbackInfo.value
    }

    /**
     * 更新缩图轴是否显示，可见性判断依据：
     * 1. slot 未放大过
     * 2. 未打开详情页
     * 3. photoDecoration 可见
     *
     * @param isDeleting 在删除场景特殊处理，此时不判断是否缩放过，在删除动画前显示缩图轴并将focus交由缩图轴驱动，避免删除后显示错乱的问题
     * @param withAnim 显示或隐藏是否需要动效
     */
    private fun updateThumbLineVisibility(reason: String, isDeleting: Boolean = false, withAnim: Boolean = false) {
        //是否为图片尺寸放大场景
        val isScaled = isFocusScaled(isDeleting)
        //是否为详情场景
        val inDetailsMode = viewModel.details.isDetailModeByTransition()
        //是否为沉浸式场景
        val isUnderImmersionInteractive = viewModel.pageManagement.isUnderImmersionInteractive.value ?: false
        val isVisible = !isScaled && !inDetailsMode && !isUnderImmersionInteractive

        val updateSuccess = changeThumbLineVisibilityByAnimation(isVisible, withAnim)
        /* 缩图轴消失时焦点由大图驱动，出现时由缩图轴驱动，
        因为缩图轴不可见时 performInitPosition 的 thumbLineView.hasPendingAdapterUpdates() 会一直是true。 */
        if (updateSuccess) {
            pagerSection?.setFocusSlotDroveByPagerSelection(!isVisible)
        }

        viewModel.pageManagement.updateThumblineVisibility(isVisible)
        GLog.d(TAG, LogFlag.DL) {
            "[updateThumbLineVisibility] reason: $reason, isVisible: $isVisible, " +
                    "isScaled: $isScaled, inDetailsMode: $inDetailsMode"
        }
    }

    /**
     * 当前 focus 是否被放大过，
     * 1. 如果是删除场景，默认没放大过
     * 2. 优先使用 isFocusScaledBeforeEnterDetails（进入详情阻断场景）
     * 3. 否则使用 pagerSection?.isPhotoSlotScaled()
     */
    private fun isFocusScaled(isDeleting: Boolean = false): Boolean {
        if (isDeleting) return false

        val isScaled = shouldFocusScaledAfterExitDetails?.takeIf { viewModel.details.isInDetailsModeInclTransition.value == true }
            ?: pagerSection?.isPhotoSlotScaled()
            ?: false
        return isScaled.also {
            lastIsFocusScaled = it
        }
    }

    private fun updateThumbLineBackground() {
        val bottomThumbLineTheme = viewModel.menuControl.menuTheme.value.second
        val colorRes = viewModel.pageManagement.pageTheme.value.thumbLineBackground

        if (bottomThumbLineTheme == MenuDecorationTheme.LightTransparent || bottomThumbLineTheme == MenuDecorationTheme.DarkTransparent) {
            rvThumbLine.setBackgroundColor(Color.TRANSPARENT)
        } else {
            rvThumbLine.apply { setBackgroundColor(context.getColor(colorRes)) }
        }
    }

    /**
     * 缩图轴显示东西啊
     * @param isVisible true 显示, false 为不显示
     * @param withAnim true 需要动效，false 为不需要
     */
    private fun changeThumbLineVisibilityByAnimation(isVisible: Boolean, withAnim: Boolean): Boolean {
        if (this::rvThumbLine.isInitialized.not()) {
            return false
        }
        val hiding = thumbLineAlphaAnimation.isHiding
        val showing = thumbLineAlphaAnimation.isShowing
        val animation = hiding || showing
        if (rvThumbLine.isVisibleAndOpaque() == isVisible && animation.not()) {
            return false
        }
        when {
            withAnim && isVisible -> {
                val defaultParam = PhotoDecorationState.Show.IMMERSIVE.animator.defaultParam
                thumbLineAlphaAnimation.show(
                    defaultParam.bounce,
                    defaultParam.response,
                    {
                        rvThumbLine.visibility = View.VISIBLE
                    },
                    onEnd = {
                        rvThumbLine.visibility = View.VISIBLE
                    })
            }

            withAnim -> {
                val defaultParam = PhotoDecorationState.Hide.IMMERSIVE.animator.defaultParam
                thumbLineAlphaAnimation.hide(
                    defaultParam.bounce,
                    defaultParam.response,
                    onStart = {
                        rvThumbLine.visibility = View.VISIBLE
                    },
                    onEnd = {
                        rvThumbLine.visibility = View.INVISIBLE
                    })
            }

            else -> {
                //alpha设置是必须要的，存在动效结束后alpha=0.0f，再执行到此逻辑
                if (isVisible) {
                    rvThumbLine.alpha = THUMB_LINE_ALPHA_SHOW
                    rvThumbLine.visibility = View.VISIBLE
                } else {
                    rvThumbLine.alpha = THUMB_LINE_ALPHA_HIDE
                    rvThumbLine.visibility = View.INVISIBLE
                }
            }
        }
        return true
    }

    /**
     * 监听大图的数据变化
     *
     * 当大图有数据变化时,需要:
     * * 更新滑动窗口,驱动对应 Item 的状态机
     * * 通知[rvThumbLine]的 Adapter
     */
    private inner class SlotBatchEventCallback : ISlotBatchEventCallback {
        override fun slotBatchSetChanged() {
            GLog.d(TAG) { "[slotBatchSetChanged]" }

            //step 1 通知thumbLineWindow数据改变
            thumbLineWindowLoadingStrategy.notifyDataSetChanged()

            //step 2 通知thumbLineAdapter数据改变
            thumbLineAdapter.notifyDataSetChanged()

            //step 3 绑定thumbLineWindow数据回调的callback
            thumbLineWindowLoadingStrategy.notifyAddSlotLoadedCallback()
        }

        override fun slotBatchInserted(position: Int, count: Int) {
            GLog.d(TAG) { "[slotBatchInserted] position $position, count $count" }

            //step 1 通知thumbLineWindow数据改变
            thumbLineWindowLoadingStrategy.notifySlotsInserted(position, count)

            //step 2 通知thumbLineAdapter数据改变
            thumbLineAdapter.notifyItemRangeInserted(position, count)

            //step 3 绑定thumbLineWindow数据回调的callback
            thumbLineWindowLoadingStrategy.notifyAddSlotLoadedCallback()
        }

        override fun slotBatchRemoved(position: Int, count: Int) {
            GLog.d(TAG) { "slotBatchRemoved position $position, count $count" }

            //step 1 通知thumbLineWindow数据改变
            thumbLineWindowLoadingStrategy.notifySlotsRemoved(position, count)

            //step 2 通知thumbLineAdapter数据改变
            thumbLineAdapter.notifyItemRangeRemoved(position, count)

            //step 3 绑定thumbLineWindow数据回调的callback
            thumbLineWindowLoadingStrategy.notifyAddSlotLoadedCallback()
        }

        override fun slotBatchMoved(fromPosition: Int, toPosition: Int) {
            GLog.d(TAG) { "[slotBatchMoved] from  $fromPosition, to $toPosition" }

            //step 1 通知thumbLineWindow数据改变
            thumbLineWindowLoadingStrategy.notifySlotsMoved(fromPosition, toPosition)

            //step 2 通知thumbLineAdapter数据改变
            thumbLineAdapter.notifyItemMoved(fromPosition, toPosition)

            //step 3 绑定thumbLineWindow数据回调的callback
            thumbLineWindowLoadingStrategy.notifyAddSlotLoadedCallback()
        }

        override fun slotBatchInvalidate(position: Int, count: Int) {
            if (rvThumbLine.isComputingLayout) {
                GLog.w(TAG) { "[slotBatchInvalidate] perform slot batch invalidate later , cause thumb line is computing layout" }
                rvThumbLine.post {
                    performSlotBatchInvalidate(position, count)
                }
            } else {
                performSlotBatchInvalidate(position, count)
            }
        }

        override fun slotBatchEventFinished() {
            GLog.d(TAG) { "[slotBatchEventFinished]" }
            thumbLineWindowLoadingStrategy.notifySlotsUpdateEventsFinished()
        }

        private fun performSlotBatchInvalidate(position: Int, count: Int) {
            GLog.d(TAG) { "[performSlotBatchInvalidate] position $position, count $count" }

            //step 1 通知thumbLineWindow数据改变
            thumbLineWindowLoadingStrategy.notifySlotInvalidated(position, count)

            //step 2 通知thumbLineAdapter数据改变
            val firstVisibleItemPosition = thumbLineLayoutManager.findFirstVisibleItemPosition()
            val lastVisibleItemPosition = thumbLineLayoutManager.findLastVisibleItemPosition()
            val end = position + count - 1
            if ((position in firstVisibleItemPosition..lastVisibleItemPosition) || (end in firstVisibleItemPosition..lastVisibleItemPosition)) {
                thumbLineAdapter.notifyItemRangeChanged(position, count)
            }

            //step 3 绑定thumbLineWindow数据回调的callback
            thumbLineWindowLoadingStrategy.notifyAddSlotLoadedCallback()
        }
    }

    /** 注意：这里目前只是埋点，如果需要再添加其它业务逻辑，需要考虑以下场景
     *  - 共享图集场景下，大图焦点item是未下载的视频时，点击缩图轴对应item，不会打开二级展开态，但此Callback不受影响会被回调
     *  即：视频不是本地文件时，点击事件的Callback不受影响，但缩图轴不会响应二级展开态。
     */
    private inner class MotionCallback : PhotoThumbLineMotionCallback() {
        override fun onThumbLineClick(position: Int) {
            super.onThumbLineClick(position)
            viewModel.track.trackGestureClick(value = PictureTrackConstant.Value.GESTURE_CLICK_THUMBNAIL)
        }

        override fun onThumbLineDoubleClick(position: Int) {
            super.onThumbLineDoubleClick(position)
            viewModel.track.trackGestureClick(value = PictureTrackConstant.Value.GESTURE_CLICK_THUMBNAIL)
        }

        override fun onScrollStateChanged(state: Int) {
            super.onScrollStateChanged(state)
            if (state == RecyclerView.SCROLL_STATE_DRAGGING) {
                viewModel.track.trackGestureClick(value = PictureTrackConstant.Value.GESTURE_SLIDE_THUMBNAIL)
            }
        }
    }

    private inner class ThumbLineContractCollaborator : IThumbLineContractCollaborator {

        private val PlaybackInfo.isPlayedToStartOrEnd: Boolean
            get() {
                return equalWithinTolerance(positionInPercent, PLAYBACK_POSITION_MIN, PLAYBACK_POSITION_TOLERANCE)
                        || equalWithinTolerance(positionInPercent, PLAYBACK_POSITION_MAX, PLAYBACK_POSITION_TOLERANCE)
            }

        override val diffedPhotoViewDataSet: LiveData<DiffedPhotoViewDataSet>
            get() = viewModel.dataLoading.diffedPhotoViewDataSet

        override fun getPhotoItemViewData(position: Int): PhotoItemViewData? {
            return getViewData(position)
        }

        override fun shouldLockSelection(position: Int, presentationType: PresentationType): Boolean {
            return when (presentationType) {
                Thumbnail, Preview -> false
                Detail -> getPlaybackInfo(position)?.let { it.isPlaying || it.isPlayedToStartOrEnd.not() } ?: false
            }
        }

        override fun getRelativeOffsetPercent(position: Int, presentationType: PresentationType): Double {
            return when (presentationType) {
                Thumbnail, Preview -> 0.0
                Detail -> getPlaybackInfo(position)?.positionInPercent ?: 0.0
            }
        }

        override fun onCenterPositionChange(position: Int) {
            GLog.d(TAG) { "[onCenterPositionChange] position=$position" }
            thumbLineWindowLoadingStrategy.notifySlotFocusChanged(position)
            pagerSection?.setSelection(position)
            thumbLineWindowLoadingStrategy.notifyAddSlotLoadedCallback()
        }
    }

    /**
     * 预览蒙版协作器实现，一种适配器，用于将 [previewMaskContract] 的各种请求通过此类适配、转发给对应的 Section 或者 VM
     *
     * @see IThumbLinePreviewMaskCollaborator
     */
    private inner class ThumbLinePreviewMaskCollaborator : IThumbLinePreviewMaskCollaborator {
        private val photoSlotSizeCalculator: PhotoSlotSizeCalculator = PhotoSlotSizeCalculator()

        override val selectedPosition: Int
            get() = viewModel.dataLoading.focusSlot

        override val diffedFocusViewData: Flow<DiffedPhotoItemViewData?> get() = viewModel.dataLoading.diffedFocusViewData

        override fun requestFreezePhoto(operation: PageOperationState) {
            viewModel.pageManagement.requestPageOperation(
                pageOperationState = operation,
                operatorToken = OPERATOR_TOKEN_THUMB_LINE_PREVIEW_MASK
            )
        }

        override fun loadPreviewContent(position: Int, callback: (content: Drawable?) -> Unit) {
            val viewData = getViewData(position) ?: let {
                GLog.e(TAG) { "[loadPreviewContent] viewData not found, position=$position" }
                callback(null)
                return
            }
            viewModel.contentLoading.startLoadContent(
                viewData = viewData,
                SizeType.FullThumb(LOAD_TYPE_PREVIEW_THUMBNAIL),
                contentType = null,
                drawableCallback = callback
            )
        }

        override fun getFocusHintDrawable(position: Int, id: String): Drawable? {
            val previewShot = viewModel.inputArguments.previewShot.value ?: return null
            return if (previewShot.itemPath == id) previewShot.thumbnail else null
        }

        override fun unloadPreviewContent(position: Int, content: Drawable?) {
            val viewData = getViewData(position) ?: let {
                GLog.e(TAG) { "[unloadPreviewContent] viewData not found, position=$position" }
                return
            }

            viewModel.contentLoading.releaseLoadedContent(viewData, SizeType.FullThumb(LOAD_TYPE_PREVIEW_THUMBNAIL))
        }

        override fun getRenderingStatus(position: Int): RenderingStatus {
            return pagerSection?.getRenderingStatus(position) ?: RenderingStatus.NoReady
        }

        override fun registerRenderingStatusListener(listener: RenderingStatusListener) {
            pagerSection?.addRenderingStatusListener(listener)
        }

        override fun unregisterRenderingStatusListener(listener: RenderingStatusListener) {
            pagerSection?.removeRenderingStatusListener(listener)
        }

        override fun overridePreviewPresentationSpecIfNeed(position: Int, previewSpec: PreviewMaskContract.PreviewPresentationSpec) {
            val photoItemViewData = getViewData(position) ?: let {
                GLog.d(TAG) { "[overridePreviewMaskViewDataIfNeed] position : $position , viewData is null ,return." }
                return
            }

            /**
             * 当前业务只需要gif图特殊处理，其它return掉，减少不必要内存申请
             */
            if (photoItemViewData.isGif.not()) return

            val transitionData = TransitionPreviewData.from(viewData = photoItemViewData)
            val propertyProvider = createAnimationPropertyProvider(transitionData.size, previewSpec.displaySize)
            photoSlotSizeCalculator.overrideSlotAnimationPropertiesIfNeed(transitionData, propertyProvider)
            previewSpec.updateContentScale(propertyProvider.minZoomScale?.invoke() ?: Float.NaN)
        }
    }

    private inner class ThumbLinePlaybackCollaborator : IThumbLinePlaybackCollaborator {
        override val viewModel: PhotoViewModel
            get() = <EMAIL>

        override val proxySeekbar: ISeekBar
            get() = <EMAIL>

        override val diffedFocusViewDataSubject: Flow<DiffedPhotoItemViewData?>
            get() = viewModel.dataLoading.diffedFocusViewData

        override val playbackInfoSubject: LiveData<PlaybackInfo?>
            get() = viewModel.playback.playbackInfo

        override val playbackSizeCalculator: PlaybackThumbLineSizeSpecCalculator
            get() = <EMAIL>

        override fun getSliceRetrieverFactory(position: Int): ISliceRetrieverFactory? {
            return viewModel.playback.sliceRetrieverFactory.value
        }
    }

    private class RecyclerLayoutProxy(private val recyclerView: RecyclerView) : ThumbLineLayoutManager.IRecyclerViewProxy {
        override fun hasPendingAdapterUpdates(): Boolean = recyclerView.hasPendingAdapterUpdates()
        override fun findChildViewUnder(x: Float, y: Float): View? = recyclerView.findChildViewUnder(x, y)
        override fun getOverScrollX(): Int = recyclerView.scrollX
    }

    companion object {
        private const val TAG = "PhotoThumbLineSection"
        private const val OPERATOR_TOKEN_THUMB_LINE_PREVIEW_MASK = "operator_token_thumb_line_preview_mask"
        private const val PLAYBACK_POSITION_MIN = 0.0
        private const val PLAYBACK_POSITION_MAX = 1.0
        private const val PLAYBACK_POSITION_TOLERANCE = 0.001

        private const val DATA_RANGE_TIMES_LOW = 1.1F
        private const val DATA_RANGE_TIMES_MIDDLE = 1.3F
        private const val DATA_RANGE_TIMES_NORMAL = 1.5F
        private const val INVALID_CONTROL_SLOT_SIZE = -1
        private const val THUMB_LINE_ALPHA_SHOW = 1.0f
        private const val THUMB_LINE_ALPHA_HIDE = 0.0f

        /**
         * rvThumbLine 可见item误差范围
         *
         * 原因：自定义rvThumbLine/layoutManger 存在精度问题，可见item无法精准识别，误差有3个item（回收和显示不及时）
         * 导致onBindViewHolder后findViewHolderForAdapterPosition无法获取到对应VH
         *
         * 场景：滑动item时，新的VH进入可见区间onBindViewHolder后，触发窗口slot创建，当slot创建号后，绑定对应VH时无法取到，导致灰图
         *
         * 目的：限制窗口范围必须大于rvThumbLine最大范围 + 6
         */
        private const val RECYCLERVIEW_VISIBLE_VH_COUNT_ERROR = 3 * 2

        private const val VISIBILITY_REASON_DELETE = "delete"
        private const val VISIBILITY_REASON_DECOR = "decor_change"
        private const val VISIBILITY_REASON_DETAILS_MODE = "details_mode"
        private const val VISIBILITY_REASON_DIFFED_PRESENT_IDLE = "diffed_present_idle"
        private const val VISIBILITY_REASON_ROTATE = "rotate"
        private const val VISIBILITY_REASON_SCREEN_MODE = "screen_mode"
        private const val VISIBILITY_REASON_INIT = "init"
    }
}