/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoDetailsTimeCard.kt
 ** Description : 大图内容详情信息视频属性UI展示块
 ** Version     : 1.0
 ** Date        : 2025/4/11
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>                  2025/4/11       1.0     create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.ui.section.details.widget

import android.content.Context
import android.util.AttributeSet
import android.widget.TextView
import androidx.core.view.doOnPreDraw
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.viewmodel.details.PhotoDetails
import com.oplus.gallery.standard_lib.app.AppConstants

/**
 * 大图内容详情信息视频属性UI展示块
 */
internal class PhotoDetailsVideoParamCardView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    defStyleRes: Int = 0
) : PhotoDetailsBaseCardView(context, attrs, defStyleAttr, defStyleRes) {

    /**
     * 帧率
     */
    private var tvFps: TextView? = null

    /**
     * 时长
     */
    private var tvDuration: TextView? = null

    /**
     * 初始化view
     */
    override fun initView() {
        super.initView()
        if (isViewInitialized.not()) return
        //帧率
        tvFps = view.findViewById(R.id.tv_fps)
        //时长
        tvDuration = view.findViewById(R.id.tv_duration)

        //设置textView换行监听
        setTextViewObserver()
    }

    /**
     * 设置layoutId
     */
    override fun getLayoutId(): Int {
        return R.layout.photo_details_video_param_card
    }

    /**
     * 更新view
     */
    override fun updateData(photoDetails: PhotoDetails) {
        //获取展示信息
        this.photoDetails = photoDetails

        if (isViewInitialized.not()) return

        //获取详情字段
        val viewData = photoDetails.photoDetailsViewData
        //帧率
        tvFps?.text = viewData.fps
        //时长
        tvDuration?.text = viewData.duration
        //setTextViewObserver会在doOnLayout中重新赋值，会导致参数单位颜色失效，因此要在doOnPreDraw中设置单位颜色
        doOnPreDraw { setTextUnit() }
    }

    /**
     * 明暗模式更新UI
     */
    override fun refreshUI() {
        super.refreshUI()
        //设置参数单位颜色
        setTextUnit()
    }

    /**
     * 设置textView换行监听
     */
    private fun setTextViewObserver() {
        //帧率
        setTextMaxLineObserver(tvFps)
        //时长
        setTextMaxLineObserver(tvDuration)
    }

    /**
     * 设置单位颜色
     */
    private fun setTextUnit() {
        //设置FPS
        setTextUnitColor(
            tvFps,
            tvFps?.text?.length?.minus(AppConstants.Number.NUMBER_3) ?: AppConstants.Number.NUMBER_0,
            tvFps?.text?.length ?: AppConstants.Number.NUMBER_0
        )
    }

    override fun toString(): String = "$TAG@${hashCode()}"

    companion object {
        private const val TAG = "PhotoDetailsVideoParamCardView"
    }
}