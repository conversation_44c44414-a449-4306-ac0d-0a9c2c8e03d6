/*********************************************************************************
 ** Copyright (C), 2022-2032, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoReloadTask.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/10/10
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>               2022/10/10  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.dataloading

import android.os.Bundle
import androidx.recyclerview.widget.DiffUtil.Callback
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.utils.Constants
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.CameraMode.FLAG_SUPPORT_OLIVE
import com.oplus.gallery.business_lib.model.data.base.utils.MediaItemUtils.isTargetModeTagFlagChanged
import com.oplus.gallery.business_lib.ui.view.ItemViewData
import com.oplus.gallery.business_lib.viewmodel.AlbumReloadTask
import com.oplus.gallery.business_lib.viewmodel.base.BaseReloadInfo
import com.oplus.gallery.business_lib.viewmodel.base.ItemInfo
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING
import com.oplus.gallery.framework.abilities.data.model.BaseModel
import com.oplus.gallery.photo_page.viewmodel.dataloading.PhotoReloadTask.PhotoItemInfo

internal typealias MediaItemGetter = (Int) -> MediaItem?
/**
 * 大图专用 ReloadTask，与 [AlbumReloadTask] 对比，主要用于扩展 [Callback.areContentsTheSame]。
 *
 * 扩展对比：
 *
 * [AlbumReloadTask]：只对比 [PhotoItemInfo] 元素是否为同一个，不对比是否脏掉
 * [PhotoReloadTask]：对比 [PhotoItemInfo] 元素是否为同一个 + 对比 [PhotoItemInfo] 内容是否脏掉
 */
abstract class PhotoReloadTask(
    model: BaseModel<MediaItem>,
    needDiff: Boolean,
    spanCount: Int,
    reloadInfo: BaseReloadInfo<MediaItem, ItemViewData>,
    reloadCallback: ReloadCallback<MediaItem, ItemViewData>,
    private val viewDataCreator: (mediaItem: MediaItem, index: Int) -> ItemViewData
) : AlbumReloadTask(model, reloadInfo, needDiff, reloadCallback, spanCount) {
    override fun createViewData(mediaItem: MediaItem, index: Int): ItemViewData = viewDataCreator(mediaItem, index)

    override fun onCreateItemInfo(index: Int, item: MediaItem, itemVersion: Long, newVersion: Long): ItemInfo<MediaItem?> {
        return PhotoItemInfo(index, item, itemVersion, newVersion)
    }

    override fun onCreateEmptyItemInfo(index: Int): ItemInfo<MediaItem?> = EMPTY_ITEM_INFO

    override fun onCreateDiffCallback(
        oldList: List<ItemInfo<MediaItem?>>,
        newList: List<ItemInfo<MediaItem?>>
    ): Callback {
        return PhotoDiffCallback(oldList, newList)
    }

    /**
     * 大图专用 [Callback]，实现了 [areContentsTheSame]，对比内容是否脏掉
     */
    private class PhotoDiffCallback(
        oldList: List<ItemInfo<MediaItem?>>,
        newList: List<ItemInfo<MediaItem?>>
    ) : DiffCallback<MediaItem>(oldList, newList) {

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            val oldItem = oldList[oldItemPosition]
            val newItem = newList[newItemPosition]
            if ((oldItem !is PhotoItemInfo) || (newItem !is PhotoItemInfo)) {
                return super.areItemsTheSame(oldItemPosition, newItemPosition)
            }
            // 如果oldItem.item是null，则说明是默认的空对象，此时将diff改为change操作
            return (newItem.itemId == oldItem.itemId) || (oldItem.item == null)
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            val oldItem = oldList[oldItemPosition]
            val newItem = newList[newItemPosition]
            if ((oldItem !is PhotoItemInfo) || (newItem !is PhotoItemInfo)) {
                return super.areContentsTheSame(oldItemPosition, newItemPosition)
            }

            return newItem.isContentEquals(oldItem)
        }

        override fun getTag(): String = TAG

        companion object {
            private const val TAG = "PhotoDiffCallback"
        }
    }

    /**
     * 大图专用 [ItemInfo]，添加了 [itemId]/[filePath]/[fileSize]/[mediaRotation] 属性，主要用于能够对比内容是否脏掉
     */
    private class PhotoItemInfo(
        position: Int,
        item: MediaItem?,
        itemVersion: Long,
        setVersion: Long
    ) : ItemInfo<MediaItem?>(position, item, itemVersion, setVersion) {

        val itemId: String = item?.path?.toString() ?: EMPTY_STRING
        val mediaId: Int = item?.mediaId ?: 0
        val filePath: String = item?.filePath ?: EMPTY_STRING
        val fileSize: Long = item?.fileSize ?: 0L
        val mediaRotation: Int = item?.rotation ?: 0
        val isTenBit: Boolean = item?.isYuvFormat ?: false
        val isShareThumbnailDownloaded: Boolean? = getShareThumbnailDownloaded(item)
        val tagFlags: Int = item?.tagFlags ?: 0

        /**
         * 与 [other] 对比内容是否相同
         */
        fun isContentEquals(other: PhotoItemInfo?): Boolean {
            return (itemId == other?.itemId)
                    && (mediaId == other.mediaId)
                    && (filePath == other.filePath)
                    && (fileSize == other.fileSize)
                    && (mediaRotation == other.mediaRotation)
                    && (isTenBit == other.isTenBit)
                    && needUpdateByTagFlags(tagFlags, other.tagFlags).not()
                    && (isShareThumbnailDownloaded == other.isShareThumbnailDownloaded)
        }

        /**
         * 是否要因tagFlag变动而更新UI
         * - 1 Olive相关的变动了，需要更新
         */
        private fun needUpdateByTagFlags(tagFlags: Int, otherTagFlags: Int): Boolean {
            if (tagFlags == otherTagFlags) {
                return false
            }
            // 1 Olive相关的变动了，需要更新
            if (isTargetModeTagFlagChanged(tagFlags, otherTagFlags, FLAG_SUPPORT_OLIVE)) {
                return true
            }
            return false
        }

        private fun getShareThumbnailDownloaded(item: MediaItem?): Boolean? {
            return item?.getSpecifiedAttributes(Bundle())?.let { bundle ->
                if (bundle.containsKey(Constants.SpecifiedAttributes.KEY_SHARED_THUMBNAIL_DOWNLOADED)) {
                    bundle.getBoolean(Constants.SpecifiedAttributes.KEY_SHARED_THUMBNAIL_DOWNLOADED, false)
                } else {
                    null
                }
            }
        }
    }

    override fun getTag() = TAG

    companion object {
        private const val TAG = "PhotoReloadTask"
        private val EMPTY_ITEM_INFO: PhotoItemInfo = PhotoItemInfo(-1, null, -1, -1)
    }
}
