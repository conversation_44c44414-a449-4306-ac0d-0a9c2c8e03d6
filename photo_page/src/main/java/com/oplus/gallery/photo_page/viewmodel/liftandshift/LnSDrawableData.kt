/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : LnSDrawableData.kt
 ** Description : 抠图结果用于缓存的数据结构类
 ** Version     : 1.0
 ** Date        : 2023/07/13
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2023/07/13      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.liftandshift

import android.util.Size
import androidx.core.graphics.toRect
import androidx.core.graphics.toRectF
import com.oplus.gallery.foundation.liftandshift.data.LnSResultItem
import com.oplus.gallery.photo_page.viewmodel.liftandshift.LnSDrawableData.Companion.LNS_RESULT_CACHE_CODEC_VERSION

/**
 * 抠图结果用于缓存的数据结构类
 * @param version 数据结构存储 编解码规则版本， 当磁盘中的缓存与[LNS_RESULT_CACHE_CODEC_VERSION]不同时将舍弃磁盘的数据，重新抠图
 * @param baseResSize 基于哪个尺寸去抠图的，因为不同的尺寸会有不同的结果，所以缓存的也要对应
 * @param children 详细的抠图主体结果列表
 */
internal data class LnSDrawableData(
    val version: Int,
    val baseResSize: Size,
    val children: List<LnSBitmapDrawable>
) {
    companion object {
        /**
         * 编解码规则版本
         */
        const val LNS_RESULT_CACHE_CODEC_VERSION = 0
    }
}

/**
 * LnSBitmapDrawable转LnSResultItem
 * LnSResultItem是倾向于foundation&sdk的数据结构
 * LnSBitmapDrawable则是倾向于VM及业务层的数据结构
 *
 * @param baseResSize 基于哪个尺寸去抠图的
 */
internal fun LnSBitmapDrawable.toLnSResultItem(baseResSize: Size): LnSResultItem {
    return LnSResultItem(
        bitmap = bitmap,
        itemRectF = constraint.toRectF(),
        thumbImageSize = baseResSize,
        contourPointList = contourPointList
    )
}

/**
 * LnSResultItem转LnSBitmapDrawable
 * LnSResultItem是倾向于foundation&sdk的数据结构
 * LnSBitmapDrawable则是倾向于VM及业务层的数据结构
 */
internal fun LnSResultItem.toLnSBitmapDrawable(rotation: Int = 0): LnSBitmapDrawable {
    return LnSBitmapDrawable(
        bitmap = bitmap,
        constraint = itemRectF.toRect(),
        rotation = rotation,
        contourPointList = contourPointList
    )
}