/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoIsolationBottomMenu.kt
 ** Description : 大图页隔离层底部菜单
 ** Version     : 1.0
 ** Date        : 2022/03/01
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>@Apps.Gallery              2022/03/01  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.ui.isolatedlayer

import android.content.Context
import android.content.res.Resources
import android.view.ContextThemeWrapper
import android.view.MenuItem
import android.view.View
import android.widget.FrameLayout
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.isVisible
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.standard_lib.ui.bottomnavigation.BottomNavigationView
import com.oplus.gallery.standard_lib.ui.util.AsyncObj
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import kotlinx.coroutines.GlobalScope
import okio.withLock
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.locks.ReentrantLock

/**
 * 模拟的TemplateFragment菜单接口，供上层操作页面底部菜单使用
 */
interface PhotoBottomMenu {
    val bottomMenuPreloader: BottomMenuPreloader

    /**
     * 实现菜单内容适配的接口
     */
    var adapter: IPhotoBottomMenuAdapter?

    /**
     * 刷新底部菜单。
     * 此方法会重新通知[PhotoMenuSection.BottomMenu.onInflateBottomMenu]方法来获取菜单的定义，
     * 如果获取的菜单定义与当前菜单有差异，则会重新创建菜单。
     */
    fun invalidateMenu()

    /**
     * 显示底部菜单。
     * 如果此时菜单还未创建，则会触发菜单创建。
     */
    fun show()

    /**
     * 隐藏底部菜单
     */
    fun hide()
}

/**
 * 上层页面要使用此菜单所需要实现的接口
 */
interface IPhotoBottomMenuAdapter {
    /**
     * 底部菜单进行Layout变更，在此时应该尝试更新菜单的内外边距和菜单定位。
     */
    fun onBottomMenuLayout(bottomMenu: View)

    /**
     * 底部菜单创建后回调
     */
    fun onBottomMenuCreated(navigationView: BottomNavigationView)

    /**
     * 底部菜单需要被重建时回调，此时需要返回定义的菜
     * 单，以便它被填充到底部菜单中
     * @return 返回菜单定义的资源ID
     */
    fun onInflateBottomMenu(): Int

    /**
     * 底部菜单在填充完新的菜单项后回调，此时可以更新 BottomNavigationView 和更新充好的菜单项属性
     * @param menuView 菜单的实现类
     */
    fun onPrepareBottomMenu(menuView: BottomNavigationView)

    /**
     * 菜单销毁时被回调
     */
    fun onBottomMenuDestroyed()

    /**
     * 菜单项被点击时回调
     * @param item 回调的菜单项
     */
    fun onBottomMenuItemClicked(item: MenuItem)

    /**
     * 菜单项更新完成后的回调
     */
    fun onBottomMenuPrepared()
}

/**
 * 隔离层底部菜单实现
 *
 * 为上层（[PhotoFragment]）实现一套贴近于界面底部的菜单，上层仅需要提供此菜单
 * 的菜单项、主题、菜单项事件处理即可正常工作。
 */
open class PhotoIsolationBottomMenu(
    private val fragment: PhotoIsolationFragment,
    override var adapter: IPhotoBottomMenuAdapter? = null
) : PhotoBottomMenu {

    override val bottomMenuPreloader: BottomMenuPreloader = BottomMenuPreloader()

    private var bottomNavigationContainer: FrameLayout? = null
        get() {
            if (field == null) {
                field = fragment.view?.findViewById(R.id.bottom_bar_container)
            }
            return field
        }

    private val bottomNavigationViewAsync
        get() = getNavViewAsync()

    private var bottomMenuResId: Int = Resources.ID_NULL

    private val hasEnsureMenuView = AtomicBoolean(false)

    /**
     * 刷新底部菜单。
     * 此方法会重新通知[PhotoMenuSection.BottomMenu.onInflateBottomMenu]方法来获取菜单的定义，
     * 如果获取的菜单定义与当前菜单有差异，则会重新创建菜单。
     */
    override fun invalidateMenu() {
        adapter?.onInflateBottomMenu()?.let { requestedMenuResId ->
            bottomNavigationViewAsync.obj?.inflateMenuIfChanged(requestedMenuResId)
            recordBottomMenuResId(requestedMenuResId)
        }

        bottomNavigationViewAsync.obj?.let {
            adapter?.onPrepareBottomMenu(it)
        }

        ensureMenuView()
    }

    /**
     * 显示底部菜单。
     * 如果此时菜单还未创建，则会触发菜单创建。
     */
    override fun show() {
        bottomNavigationContainer?.isVisible = true
    }

    /**
     * 隐藏底部菜单
     */
    override fun hide() {
        //hide动画视觉效果不友好，已和交互设计师确认去掉动画
        bottomNavigationContainer?.isVisible = false
    }

    /**
     * 系统栏（状态栏、导航栏）更新，需要更新底部菜单栏的布局
     */
    fun onSystemBarChanged(windowInsets: WindowInsetsCompat) {
        GLog.d(TAG, LogFlag.DL) { "[onSystemBarChanged] windowInsets=$windowInsets" }
        bottomNavigationViewAsync.obj?.let {
            adapter?.onBottomMenuLayout(it)
        }
    }

    private fun getNavViewAsync(): AsyncObj<BottomNavigationView> {
        val context = fragment.context ?: ContextGetter.context
        val navViewAsyncObj = bottomMenuPreloader.getBottomNavAsyncObj(context, bottomMenuResId)
        GLog.d(TAG) { "[getNavViewAsync] use asyncObj : $navViewAsyncObj , resId :$bottomMenuResId" }
        return navViewAsyncObj
    }

    /**
     * 异步加载菜单view，加载完成后再将菜单挂到大图布局的container中。
     */
    private fun ensureMenuView() {
        if (hasEnsureMenuView.compareAndSet(false, true)) {
            GLog.i(TAG) { "[ensureMenuView] ensure menu , id : $bottomMenuResId" }
            bottomNavigationViewAsync.getIt {
                if (bottomNavigationContainer?.childCount != 0) {
                    return@getIt
                }
                adapter?.onBottomMenuLayout(it)
                bottomNavigationContainer?.addView(
                    it,
                    FrameLayout.LayoutParams.MATCH_PARENT,
                    FrameLayout.LayoutParams.WRAP_CONTENT
                )
                it.setOnNavigationItemSelectedListener { menuItem ->
                    adapter?.onBottomMenuItemClicked(menuItem)
                    true
                }
                it.dividerView.visibility = View.VISIBLE
                it.dividerView.setBackgroundColor(it.context.getColor(R.color.photopage_menu_diver_background_color))
                adapter?.run {
                    onBottomMenuCreated(it)

                    // 菜单加载完成后主动调用一次onPrepareBottomMenu刷新菜单
                    onPrepareBottomMenu(it)
                    // 通知菜单 prepare 完成
                    onBottomMenuPrepared()
                }
            }
        }
    }

    private fun recordBottomMenuResId(menuResId: Int) {
        if (bottomMenuResId == menuResId) return
        bottomMenuResId = menuResId
    }

    /**
     * 通知销毁资源
     */
    fun onDestroy() {
        adapter?.onBottomMenuDestroyed()
        bottomNavigationViewAsync.obj?.setOnNavigationItemSelectedListener(null)
        bottomNavigationViewAsync.destroy()
    }

    companion object {
        private const val TAG = "PhotoIsolationBottomMenu"
    }
}

/**
 * 底部菜单的预加载器
 */
class BottomMenuPreloader {

    private val lock = ReentrantLock()

    private var bottomNavAsyncObj: Pair<Int, AsyncObj<BottomNavigationView>>? = null

    /**
     * 预加载底部菜单
     */
    fun preload(context: Context, menuResId: Int): Unit = lock.withLock {
        // 获取 AsyncObj
        val asyncObj = getBottomNavAsyncObj(context, menuResId)
        // 预加载 AsyncObj
        asyncObj.getIt {}

        GLog.i(TAG) { "[preload] preload asyncObj : $asyncObj , menuResId : $menuResId" }
    }

    /**
     * 获取导航栏的 [AsyncObj]
     */
    fun getBottomNavAsyncObj(context: Context, menuResId: Int): AsyncObj<BottomNavigationView> = lock.withLock {
        // 定义一个内部方法:创建 AsyncObj
        fun createObj(context: Context, menuResId: Int): AsyncObj<BottomNavigationView> {
            val asyncObj = createBottomNavAsyncObj(context, menuResId)
            bottomNavAsyncObj = Pair(menuResId, asyncObj)
            return asyncObj
        }

        // 获取缓存
        val cache = bottomNavAsyncObj

        // 如果 menuResId 为空,表示暂时未赋值,优先返回预加载的 cache 值,若新创建,则新值只使用一次且不替换缓存
        if (menuResId == Resources.ID_NULL) {
            val currObj = cache?.second
            return@withLock if (currObj == null) {
                val asyncObj = createObj(context, menuResId)
                GLog.i(TAG) { "[getBottomNavAsyncObj]menuResId is null , create tmp obj without cache: $asyncObj" }
                asyncObj
            } else {
                GLog.i(TAG) { "[getBottomNavAsyncObj]menuResId is null , use curr obj : ${cache.second} , menuResId : ${cache.first}" }
                currObj
            }
        }

        // 没有缓存->创建对应的 obj 并触发预加载
        if (cache == null) {
            val asyncObj = createObj(context, menuResId)
            GLog.i(TAG) { "[getBottomNavAsyncObj]cache is null , create asyncObj : $asyncObj , menuResId : $menuResId" }
            return asyncObj
        }

        // 有缓存,但是缓存不符合->销毁缓存 & 创建对应的 obj
        if (cache.first != menuResId) {
            cache.second.destroy()
            val asyncObj = createObj(context, menuResId)
            GLog.i(TAG) {
                "[getBottomNavAsyncObj]cache not match , create asyncObj : $asyncObj , menuResId : $menuResId , " +
                        "destroy last : ${cache.second}"
            }
            return asyncObj
        }

        // 有缓存且符合->返回缓存
        GLog.i(TAG) { "[getBottomNavAsyncObj]cache match , use asyncObj : ${cache.second} , menuResId : $menuResId" }
        return cache.second
    }

    /**
     * 释放预加载的内容
     */
    fun release(): Unit = lock.withLock {
        GLog.e(TAG) { "[release]release bottom nav asyncObj : ${bottomNavAsyncObj?.second} , redId : ${bottomNavAsyncObj?.first}" }
        bottomNavAsyncObj?.second?.destroy()
        bottomNavAsyncObj = null
    }

    private fun createBottomNavAsyncObj(context: Context, menuResId: Int): AsyncObj<BottomNavigationView> {
        return AsyncObj(GlobalScope) {
            GTrace.trace("PhotoIsolationBottomMenu.viewAsync") {

                GTrace.traceBegin("$TAG.createBottomNavAsyncObj.initNavView")
                val navView = BottomNavigationView(ContextThemeWrapper(context, R.style.Photo_NavigationBar))
                GTrace.traceEnd()

                if (menuResId == Resources.ID_NULL) {
                    GLog.e(TAG) { "[createBottomNavAsyncObj] skip inflate menu , cause menu res id is null" }
                    return@trace navView
                }

                GTrace.traceBegin("$TAG.createBottomNavAsyncObj.inflateMenu")
                navView.inflateMenuIfChanged(menuResId)
                GTrace.traceEnd()

                navView
            }
        }
    }

    private companion object {
        private const val TAG = "BottomMenuPreloader"
    }
}