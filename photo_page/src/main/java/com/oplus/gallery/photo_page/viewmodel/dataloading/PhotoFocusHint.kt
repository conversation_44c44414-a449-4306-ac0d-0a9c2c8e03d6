/*********************************************************************************
 ** Copyright (C), 2022-2032, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : FocusHint.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/02/20
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>               2024/02/20  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.dataloading

import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.ui.view.ItemViewData

/**
 * 数据类 : 进入大图页面时，期望跳转元素的信息
 */
internal data class PhotoFocusHint(
    /**
     * 期望跳转元素的 itemPath
     *
     * @see ItemViewData.id
     * @see MediaItem.mPath
     */
    val id: String,

    /**
     * 期望跳转元素的位置
     *
     * @see ItemViewData.position
     */
    val index: Int
) {

    /**
     * [PhotoFocusHint] 是否已经被消费
     *
     * [PhotoFocusHint] 应只生效一次,消费前应判断 [PhotoFocusHint] 是否已经被消费,若已经消费过了,则不应再次进行消费
     */
    var isConsumed: Boolean = false
        private set

    /**
     * 消费 [PhotoFocusHint]
     *
     * [PhotoFocusHint] 消费后,应调用此方法,标记该此数据已被消费,放在数据被多次消费
     */
    fun markConsumed() {
        isConsumed = true
    }
}