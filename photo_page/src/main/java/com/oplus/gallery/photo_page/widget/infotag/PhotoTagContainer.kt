/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PhotoTagView.kt
 ** Description:
 **     {DESCRIPTION}
 **
 ** Version: 1.0
 ** Date: 2025-05-16
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>      <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>         2025/05/16  1.0          OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.photo_page.widget.infotag

import android.content.Context
import android.util.AttributeSet
import androidx.core.view.children
import com.oplus.gallery.basebiz.widget.AnimatedReplacementView
import com.oplus.gallery.photo_page.ui.theme.PhotoTagTheme

/**
 * 大图角标 view 的动画容器，
 * 通过 [replace] 方法设置新的角标 view 及做替换动画。
 * 通过 [removeContent] 来开始隐藏动画及移除 content。
 *
 * PhotoTagContainer 同一时间最多持有两个 PhotoTagGroup（替换动画时两个，正常显示时一个），
 * PhotoTagGroup 内可以有多个 PhotoTagView（在初始化时传入），
 * 替换、显示、隐藏动画基于 PhotoTagGroup 做整体的 Alpha 动画。
 */
internal class PhotoTagContainer @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AnimatedReplacementView(
    context, attrs, defStyleAttr,
    showAnimationParam = AnimationParam(ANIMATION_BOUNCE, SHOW_ANIMATION_RESPONSE),
    hideAnimationParam = AnimationParam(ANIMATION_BOUNCE, HIDE_ANIMATION_RESPONSE)
) {
    var theme: PhotoTagTheme = PhotoTagTheme.Dark
        set(value) {
            if (value == field) return
            field = value
            children.forEach {
                (it as? PhotoTagGroup)?.applyTheme(value)
            }
        }

    fun createPhotoTagGroup(tagContents: Iterable<PhotoTagView.TagContent>): PhotoTagGroup {
        val tagViews = tagContents.map { tagContent ->
            PhotoTagView(context).also {
                it.setContent(tagContent)
            }
        }
        return PhotoTagGroup(context, tagViews)
    }

    /**
     * 替换显示的角标组，会进行去重。
     * 替换时对整个当前显示的角标组和新的角标组做 alpha 动画。
     *
     * @param photoTagGroup 新的 PhotoTagView 集合
     * @param animationDelayTime 动画延迟时间
     */
    fun replace(photoTagGroup: PhotoTagGroup, animationDelayTime: Long) {
        if (currentContent?.tag == photoTagGroup.tag) return
        photoTagGroup.applyTheme(theme)
        replaceContent(child = photoTagGroup, withAnimation = true, showAnimDelayTime = animationDelayTime)
    }

    companion object {
        private const val TAG = "PhotoTagContainer"

        /**
         * 动画的弹性
         */
        private const val ANIMATION_BOUNCE = 0f

        /**
         * 显示动画的 response
         */
        private const val SHOW_ANIMATION_RESPONSE = 0.3f

        /**
         * 隐藏动画的 response
         */
        private const val HIDE_ANIMATION_RESPONSE = 0.25f
    }
}