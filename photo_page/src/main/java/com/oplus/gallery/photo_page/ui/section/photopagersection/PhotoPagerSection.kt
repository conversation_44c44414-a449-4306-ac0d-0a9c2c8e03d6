/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoPagerSectionImpl.kt
 ** Description : 大图页面 - 内容展示页面切片
 ** Version     : 1.0
 ** Date        : 2021/11/23
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>           2021/11/23  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.ui.section.photopagersection

import android.animation.ValueAnimator
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.Display
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.SurfaceView
import android.view.View
import android.view.animation.PathInterpolator
import androidx.annotation.UiThread
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.accessibility.AccessibilityNodeInfoCompat
import androidx.customview.widget.ExploreByTouchHelper
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.Observer
import androidx.lifecycle.coroutineScope
import androidx.lifecycle.viewModelScope
import com.coui.appcompat.animation.COUIPhysicalAnimationUtil.calcRealOverScrollDist
import com.oplus.gallery.addon.app.OplusJankWrapper
import com.oplus.gallery.addon.app.OplusJankWrapper.JANK_SCENE_DES_PHOTO_PAGER_SCROLL
import com.oplus.gallery.addon.app.OplusJankWrapper.JANK_SCENE_ID_PHOTO_PAGER_SCROLL
import com.oplus.gallery.addon.os.VibratorUtils
import com.oplus.gallery.addon.osense.CpuFrequencyManager
import com.oplus.gallery.basebiz.transition.PhotoPageTransitionManager
import com.oplus.gallery.business.renderer.IHomeRecentObserver
import com.oplus.gallery.business.renderer.IRendererLifecycleObserver
import com.oplus.gallery.business.renderer.brighten.IPageRenderPresentationVisibilityUpdater
import com.oplus.gallery.business_lib.api.ApiDmManager.getMainDM
import com.oplus.gallery.business_lib.helper.PhotoDataHelper.isPreviewType
import com.oplus.gallery.business_lib.menuoperation.MenuRequestCode
import com.oplus.gallery.business_lib.ui.PageTransitionAdapter
import com.oplus.gallery.foundation.arch.sectionpage.ISectionPage
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant.Value.CLICK_VIEW
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant.Value.GESTURE_DOUBLE_CLICK
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant.Value.GESTURE_DOWN_FINISH
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant.Value.GESTURE_SLIDE_PHOTO_PREVIEW
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant.Value.GESTURE_TWOFINGER
import com.oplus.gallery.foundation.uikit.broadcast.bus.HomeRecentListener
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.foundation.util.ext.collectNotNull
import com.oplus.gallery.foundation.util.ext.displayOrNull
import com.oplus.gallery.foundation.util.ext.getOrLog
import com.oplus.gallery.foundation.util.math.MathUtils.clamp
import com.oplus.gallery.foundation.util.systemcore.LowMemUtils
import com.oplus.gallery.framework.abilities.taskmanage.RealShowTimeInstrument
import com.oplus.gallery.framework.abilities.taskmanage.RealShowTimeInstrument.PHOTO_SHOW_TAG
import com.oplus.gallery.olivesdk.view.OliveView
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.ui.AbstractPhotoSection
import com.oplus.gallery.photo_page.ui.PhotoFragment
import com.oplus.gallery.photo_page.ui.PhotoSlotOverlayScratch.PhotoSlotOverlayRecord
import com.oplus.gallery.photo_page.ui.section.pagecontainer.PhotoContainerSection
import com.oplus.gallery.photo_page.ui.section.photopagersection.overlay.PhotoTipsOverlay
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.dataloading.DiffedPhotoFocusHint
import com.oplus.gallery.photo_page.viewmodel.dataloading.DiffedPhotoItemViewData
import com.oplus.gallery.photo_page.viewmodel.dataloading.DiffedPhotoViewDataSet
import com.oplus.gallery.photo_page.viewmodel.dataloading.ListUpdateCallback
import com.oplus.gallery.photo_page.viewmodel.dataloading.PhotoDataLoadingViewModel
import com.oplus.gallery.photo_page.viewmodel.dataloading.PhotoFocusHint
import com.oplus.gallery.photo_page.viewmodel.dataloading.focusHint
import com.oplus.gallery.photo_page.viewmodel.dataloading.focusItemViewData
import com.oplus.gallery.photo_page.viewmodel.dataloading.focusSlot
import com.oplus.gallery.photo_page.viewmodel.dataloading.photoViewDataSet
import com.oplus.gallery.photo_page.viewmodel.dataloading.totalCount
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PageOperationState
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PageOperationState.SuppressPhotoGesture
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PhotoPageManagementViewModel
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PhotoPageManagementViewModel.FirstFrameRenderingStatus
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PhotoPageManagementViewModel.FirstFrameRenderingStatus.NONE_READY
import com.oplus.gallery.photopager.PhotoMotionCallback
import com.oplus.gallery.photopager.PhotoPager
import com.oplus.gallery.photopager.PhotoPagerMotionCallback
import com.oplus.gallery.photopager.PhotoSlot
import com.oplus.gallery.photopager.PhotoSlot.RenderingStatus
import com.oplus.gallery.photopager.PhotoSlot.RenderingStatus.ContentReady
import com.oplus.gallery.photopager.PhotoSlot.RenderingStatus.ThumbnailReady
import com.oplus.gallery.photopager.PhotoSlotOverlay
import com.oplus.gallery.photopager.animationcontrol.AnimationControl
import com.oplus.gallery.photopager.animationcontrol.mathkernel.Matrix
import com.oplus.gallery.photopager.animationcontrol.mix
import com.oplus.gallery.photopager.animationcontrol.scrollerimpl.GestureScrollerAnimControl
import com.oplus.gallery.photopager.renderer.DrawableRenderer
import com.oplus.gallery.photopager.strategy.windowstrategy.WindowLoadingStrategy
import com.oplus.gallery.photopager.viewpager.widget.ViewPager
import com.oplus.gallery.standard_lib.app.SINGLE_UN_BUSY
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.FlowCollector
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.math.abs
import kotlin.math.absoluteValue
import kotlin.math.roundToInt

/**
 * 大图页内容展示页面切片
 *
 * Marked by zhangjisong，listener 太多了，需要搞个相关的 manager，用于分担管理 listener 的职责，不然一坨分布在这里，Section 职责太重
 */
internal class PhotoPagerSection(
    sectionPage: ISectionPage<PhotoFragment, PhotoViewModel>
) : AbstractPhotoSection(sectionPage) {

    /**
     * 此对象在切换暗色模式或者语言时不一会被初始化，
     * 在对外接口使用时需要判断有无初始化过
     */

    private val containerSection: PhotoContainerSection? get() = sectionPage.pageInstance.requireSection()

    private lateinit var photoPager: PhotoPager
    private lateinit var photoPageAdapter: PhotoPageAdapter

    private val diffCallback: PhotoListUpdateCallback = PhotoListUpdateCallback()

    private val slotBatchEventListeners = mutableSetOf<ISlotBatchEventCallback>()
    private val focusSlotRemovedListeners = mutableListOf<() -> Unit>()

    private var isFocusSlotDroveByPagerSelection: Boolean = true
    private val slotTouchEventListeners = mutableListOf<((slot: Int, event: MotionEvent) -> Boolean)>()
    private val focusSlotTransformUpdatedListeners = mutableListOf<(SlotTransformPose) -> Unit>()
    private val slotSlideDownListeners = mutableListOf<(SlideDownEvent) -> Boolean>()
    private val slotSlideUpListeners = mutableListOf<(SlideUpEvent) -> Boolean>()
    private val slotSingleClickListeners = mutableSetOf<(Int) -> Unit>()
    private val slotLongPressListeners = mutableListOf<((slot: Int) -> Boolean)>()
    private val uninterruptedSlotTouchEventListeners = mutableListOf<((slot: Int, event: MotionEvent) -> Unit)>()

    /**
     * 该监听器在触发后，会根据返回值从列表中移除
     */
    private val removableUninterruptedSlotTouchEventListeners = mutableListOf<((slot: Int, event: MotionEvent) -> Boolean)>()

    /**
     * [photoPager] 的期望焦点，当有时机时，会设置给 [photoPager]
     *
     * 大图被抑制期间，外部调用了 [setSelection]，因为无法完成布局，更新无效，需要延时设置 [photoPager] 的焦点，因此先暂存到这里
     *
     * 触发时机：[setSelection] || [updatePhotoPageOperationState] || [setFocusSlotDroveByPagerSelection]
     * 抑制条件：photoPager.isLayoutSuppressed || photoPager.isInLayout || photoPager.hasPendingAdapterUpdates
     *
     * @see setSelection
     */
    private var photoSelectionWhileSuppressed: Int = NO_SELECTION
    private var photoDelaySelectionTask: Runnable? = null

    /**
     * [photoPager]没有初始化时无法向外部透传[photoPager]有关的接口，需要使用此列表将监听器暂存起来
     */
    private var pendingPageChangeCallbacks = mutableListOf<ViewPager.OnPageChangeCallback>()

    private val diffedFocusViewDataChangeCollector = FlowCollector<DiffedPhotoItemViewData?> {
        GTrace.traceBegin("$TAG.focusSlotViewDataDiff")
        val diffed = viewModel.dataLoading.diffedFocusViewData.value
        diffed?.let { updateFocusSlotViewData(it) }
        GTrace.traceEnd()
    }

    private val diffedPhotoViewDataSetChangeObserver = Observer<DiffedPhotoViewDataSet?> { diff ->
        GTrace.trace({ "$TAG.diffedViewDataSet" }) {
            diff?.let { updatePhotoPagerDataSet(it) }
        }
    }

    private val diffedPhotoFocusHintChangeObserver = Observer<DiffedPhotoFocusHint?> { diff ->
        diff?.newPhotoFocusHint?.let {
            GTrace.trace({ "$TAG.diffedPhotoFocusHint" }) {
                updateSelectionIfDroveByPager(it)
            }
        }
    }

    private val exitPageObserver = Observer<Unit> {
        // 此处无需判断是否由编辑页进入，虚拟相册的处理在updatePhotoPagerDataSet中
        sectionPage.pageInstance.exitCurrentFragment()
    }

    private val toastShortObserver = Observer<String> {
        ToastUtil.showShortToast(it)
    }

    private val renderingStatusListener = PhotoSlot.RenderingStatusListener { slot, viewData, oldStatus, newStatus ->
        GLog.i(TAG, LogFlag.DL) {
            "[updateFirstFrameRenderedStatus] slot=$slot, currentFocus=$currentFocus, oldStatus=$oldStatus, newStatus=$newStatus"
        }

        sectionPage.pageViewModel.pageManagement.notifyRenderingStatusChange(slot, currentFocus, viewData, oldStatus, newStatus)

        if ((slot >= 0) && (slot == currentFocus)) {
            sectionPage.pageViewModel.pageManagement.photoPageDebugger.notifyRenderingStatusChange(newStatus)
            sectionPage.pageViewModel.pageManagement.photoPageDebugger.tracePictureCompletedIfNeeded(slot, oldStatus, newStatus)
            updateFirstFrameRenderingStatusIfNeeded()
            updateSpecialThumbnailLoadRequesterIfNeeded(slot, newStatus)
        }
    }

    /**
     * ContentRenderer状态监听器
     */
    private val contentRendererStatusListener = object : PhotoSlot.ContentRendererStatusListener {
        override fun onContentRendererStatusChange(
            slot: Int,
            viewData: Any?,
            isContentRendererValidBefore: Boolean,
            isContentRendererValidNow: Boolean
        ) {
            // 通过ContentRenderer的状态设置视频、Olive中SurfaceView的可见性
            val isPresented = viewModel.pageManagement.photoPageTransitionState.value?.isPresented() == true
            val isDoingTransition = viewModel.pageManagement.photoPageTransitionState.value?.isDoingTransition() == true
            val isScrollStateIdle = (lastScrollState == PhotoPager.SCROLL_STATE_IDLE)
            updatePresentationVisibilityIfNeeded(
                type = TriggerSceneType.CONTENT_RENDERER_STATE_CHANGE,
                slot = slot,
                contentRendererValid = isContentRendererValidNow,
                isPresented = isPresented,
                isDoingTransition = isDoingTransition,
                isScrollStateIdle = isScrollStateIdle
            )
        }
    }

    /**
     * Mark:改成request模式，不使用变量控制
     * 修改人:舒茧
     */
    private var negateNextImmersionChange = false

    private var isRequestingSwipeableFromOutter: Boolean = true
    private var isRequestingSwipeableFromPagerMotion: Boolean = true
    private var isRequestingSwipeableFromRenderingStatus: Boolean = false

    /**
     * 是否强制启用或禁用用户发起的滚动行为（滚动和抛滑手势）。
     * 用于控制ViewPager的滚动行为是否拦截
     * true:启用
     * false：禁用
     * null：还原为原逻辑的（这里是指通过isRequestingSwipeableFromOutter/PagerMotion/RenderingStatus等共同控制的）
     *
     * PhotoPager内部其实定义了该场景的手势，详见[GestureOperation.SlideUpForInfoOperation]中的定义。
     * 在GestureOperation的框架下，配置forbidSwipe = false即可实现该功能，无需在这里新增[forceUserInputEnable]方法
     *
     * 但是因这笔提交(http://gerrit.scm.adc.com:8080/#/c/37804996/)要解决的问题，上面的框架暂时还无法解决，因此还不能直接切换过去。
     * 考虑的方式是，新增一个SlidingUpOperation和SlidingDownOperation，在手势中识别出这两个操作，并配置forbidSwipe = true来实现
     *
     * 另外，针对场景"大图手势放大，退出沉浸式，点击详情菜单进入详情页，无法滑动"的问题，其根本原因是此时GestureStateMachine.currentOperation=NoneOperation
     * 其配置的forbidSwipe=true，进而导致[isRequestingSwipeableFromPagerMotion]被配置为了false。这个场景是Gallery仓库主动对大图做了Zoom操作
     * 考虑PhotoSlot对外暴露[notifyAutoZoom]的方法，最终将currentOperation=ZoomOperation
     */
    private var isForceUserInputEnabled: Boolean? = null

    private var isIntegrationUITransitionTransferringTouch: Boolean = false
    private val integrationUITransitionSlideDownListener: (SlideDownEvent) -> Boolean = ::handleSlideDownEventForIntegrationUITransition

    private val hasIntegrationUITransition: Boolean
        get() = viewModel.inputArguments.features.value?.hasIntegrationUITransition ?: false

    private val isSwipeForbiddenWhenNotRender: Boolean
        get() = viewModel.inputArguments.features.value?.isSwipeForbiddenWhenNotRender ?: false

    private val isFirstFrameRenderingReady: Boolean
        get() = viewModel.pageManagement.firstFrameRenderingStatus.value?.let { it != NONE_READY } ?: false

    private val isPagerInitialized: Boolean
        get() = ::photoPager.isInitialized

    private val isPagerScrolling: Boolean
        get() = photoPager.scrollState != PhotoPager.SCROLL_STATE_IDLE

    private val isPhotoGestureSuppressed: Boolean
        get() = viewModel.pageManagement.pageOperationState.value?.contains(SuppressPhotoGesture) ?: false

    override val slotOverlayRecords: List<PhotoSlotOverlayRecord> = listOf(
        PhotoSlotOverlayRecord(
            clazz = PhotoTipsOverlay::class.java,
            creator = ::createOverlay
        )
    )

    /**
     * 记录fragment需要延时退出的方式，在下次onResume的时候进行退出。
     *
     * 注意：此主要用于虚拟图集，仅1张图片时，进行图片编辑覆盖保存后，回到大图场景。
     * 此时大图有两个fragment，第二层的fragment没有数据时，先不能返回，否则会返回掉第一层的fragment，导致问题。
     * 等到第一层fragment返回后，第二层fragment在onResume的时候，再进行一次返回，退出空fragment
     */
    private var laterExitMode = EXIT_MODE_NONE

    /**
     * 是否从编辑页返回大图
     */
    private var isFromEditPage = false

    private var exitTask: Job? = null

    /**
     * 当前大图页面滑动状态
     */
    private var lastScrollState: Int = PhotoPager.SCROLL_STATE_IDLE


    /**
     * 判断 [PhotoDataLoadingViewModel.focusItemViewData] 是否与最新数据集里面的一致。
     *
     * true：一致，无需更新
     * false：不一致，说明 [PhotoDataLoadingViewModel.focusItemViewData] 内容已经滞后，需要找时机更新处理
     */
    private val isFocusViewDataExpired: Boolean
        get() = viewModel.dataLoading.focusItemViewData?.isContentEquals(viewModel.dataLoading.photoViewDataSet?.get(currentFocus)) != true

    /**
     * 可管控的Slot数量
     */
    val controlSlotSize = if (LowMemUtils.isRamLessThan4G()) CONTROL_SLOT_SIZE_IN_LOW_MEM else CONTROL_SLOT_SIZE

    /**
     * 监听PageTransitionState的状态，如果当前是退出状态，通知Renderer，即将要销毁
     */
    private val photoPageTransitionStateObserver = Observer<PhotoPageManagementViewModel.PageTransitionState> { pageTransitionState ->
        val isExiting = pageTransitionState.isExiting()
        if (isExiting) {
            GLog.d(TAG, LogFlag.DL) { "[photoPageTransitionStateObserver] surface will unbind " }
            notifyRendererPreRelease()
        }

        // 订阅进入大图动画状态，在进入大图动画结束后，才将视频、Olive中SurfaceView设置为可见
        val isPresented = (pageTransitionState == PhotoPageManagementViewModel.PageTransitionState.PRESENTED)
        updatePresentationVisibilityIfNeeded(
            type = TriggerSceneType.PAGE_TRANSITION_STATE_CHANGE,
            isPresented = isPresented
        )
    }

    /**
     * 当前 PhotoPagerSectionMode 的状态
     */
    private var currentSectionMode: PhotoPagerSectionMode = PhotoPagerSectionMode.SINGLE
        set(value) {
            field = value
            if ((value == PhotoPagerSectionMode.SINGLE) && viewModel.contentLoading.shouldSuppressLoadRenderer) {
                viewModel.contentLoading.updateSuppressRendererFlag(false)
                photoPageAdapter.notifyDataSetChanged()
            }
        }

    /**
     * photoPager滑动状态的回调。
     */
    private val photoPagerScrollCallback = object : ViewPager.OnPageChangeCallback() {
        override fun onPageScrollStateChanged(state: Int) {
            GTrace.trace({ "onPageScrollStateChanged-state:$state" }) {
                lastScrollState = state
                viewModel.pageManagement.updatePhotoPagerScrollState(state)
                if ((state == PhotoPager.SCROLL_STATE_IDLE) && isPreviewType(viewModel.inputArguments.features.value?.photoType)) {
                    viewModel.track.trackGestureClick(value = GESTURE_SLIDE_PHOTO_PREVIEW)
                }

                val isScrollStateIdle = (state == PhotoPager.SCROLL_STATE_IDLE)
                updatePresentationVisibilityIfNeeded(
                    type = TriggerSceneType.SCROLL_STATE_CHANGE,
                    isScrollStateIdle = isScrollStateIdle
                )
            }
            // 上报滑动卡顿监控
            when (state) {
                ViewPager.SCROLL_STATE_DRAGGING -> {
                    OplusJankWrapper.gfxScrollState(
                        viewModel.context,
                        JANK_SCENE_ID_PHOTO_PAGER_SCROLL,
                        JANK_SCENE_DES_PHOTO_PAGER_SCROLL,
                        OplusJankWrapper.ScrollState.Drag
                    )
                }

                ViewPager.SCROLL_STATE_IDLE -> {
                    OplusJankWrapper.gfxScrollState(
                        viewModel.context,
                        JANK_SCENE_ID_PHOTO_PAGER_SCROLL,
                        JANK_SCENE_DES_PHOTO_PAGER_SCROLL,
                        OplusJankWrapper.ScrollState.Idle
                    )
                }
            }
        }

        override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
            viewModel.pageManagement.slotScrolled = position
            viewModel.pageManagement.positionOffset = positionOffset
        }
    }

    /**
     * 是否处于详情模式
     */
    private val isInDetailsMode: Boolean
        get() = (viewModel.details.isInDetailsMode.value == true)

    /**
     *保存初始化时，双击图片的放大倍率(key:id value:倍率)
     * 使用id不使用slot的原因：增加和删除图片，会影响对应的slot，导致图片与倍率绑定的关系不准确，使用唯一id能解决
     */
    private var initialDoubleTapZoomScalePair: Pair<String, Float>? = null

    /**
     * 更新Video类型ContentRenderer的SurfaceView为显示状态
     * @param type 触发场景:ContentRender状态变化、进大图动画状态变化、左右滑动状态变化
     * @param slot 对应的图片slot位置
     * @param contentRendererValid ContentRenderer是否已经初始化
     * @param isPresented 进大图动画是否结束
     * @param isDoingTransition 页面是否在执行进入动画
     * @param isScrollStateIdle 左右滑动动画是否idle状态
     */
    private fun updatePresentationVisibilityIfNeeded(
        type: TriggerSceneType,
        slot: Int = -1,
        contentRendererValid: Boolean = false,
        isPresented: Boolean = false,
        isDoingTransition: Boolean = false,
        isScrollStateIdle: Boolean = false
    ) {
        // 更新presentation的可见性
        val updatePresentationVisibility = fun() {
            foreachSlotRenderer {
                // 如果是视频/Olive，且处于详情页面，则不要显示出来，否则会使得横向视频把2边的界面遮挡
                if ((((it.presentation is SurfaceView) || (it.presentation is OliveView)) && isInDetailsMode).not()) {
                    (it as? IPageRenderPresentationVisibilityUpdater)?.updatePresentationVisibility(View.VISIBLE)
                }
            }
        }
        when (type) {
            TriggerSceneType.CONTENT_RENDERER_STATE_CHANGE -> {
                //大图展开动画结束 && 左右滑动状态是idle && 对应slot的ContentRenderer加载完成
                val isPresentedAndIdleRenderValid = isPresented && contentRendererValid && isScrollStateIdle
                //大图展开动画过程中 && 对应slot的ContentRenderer加载完成
                val isDoingTransitionRenderValid = isDoingTransition && contentRendererValid
                if (isPresentedAndIdleRenderValid || isDoingTransitionRenderValid) {
                    updatePresentationVisibility()
                }
            }

            TriggerSceneType.PAGE_TRANSITION_STATE_CHANGE -> {
                if (isPresented) {
                    updatePresentationVisibility()
                }
            }

            TriggerSceneType.SCROLL_STATE_CHANGE -> {
                if (isScrollStateIdle) {
                    updatePresentationVisibility()
                }
            }
        }
    }

    override fun onRecentAndHomeKeyEvent(reason: String) {
        super.onRecentAndHomeKeyEvent(reason)
        GLog.d(TAG) { "[recentListener] Receive key event reason recentapps in $this" }
        if (reason.equals(HomeRecentListener.REASON_RECENT_APPS) || reason.equals(HomeRecentListener.REASON_HOME_KEY)) {
            foreachSlotRenderer {
                (it as? IHomeRecentObserver)?.notifyHomeRecentEvent(reason)
            }
        }
    }

    override fun onGalleryExit() {
        foreachSlotRenderer {
            (it as? IRendererLifecycleObserver)?.onGalleryExit()
        }
    }

    /**
     * 更新策略机的 focus
     */
    fun notifySlotFocusChanged(selection: Int) {
        photoPager.loadingStrategy.notifySlotFocusChanged(selection)
    }

    /**
     * 设置焦点是否由 [photoPager] 驱动，默认值为 true，由 [photoPager] 驱动
     *
     * @param shouldFocusSlotDroveByPagerSelection true: 焦点由 [photoPager] 驱动；false：焦点由外部通过 [setSelection] 驱动
     */
    fun setFocusSlotDroveByPagerSelection(shouldFocusSlotDroveByPagerSelection: Boolean) {
        if (isFocusSlotDroveByPagerSelection == shouldFocusSlotDroveByPagerSelection) {
            return
        }

        GLog.d(TAG) { "[setFocusSlotDroveByPagerSelection] isFocusSlotDroveByPagerSelection=$shouldFocusSlotDroveByPagerSelection" }
        isFocusSlotDroveByPagerSelection = shouldFocusSlotDroveByPagerSelection

        if (shouldFocusSlotDroveByPagerSelection) {
            if (isPagerInitialized.not()) {
                return
            }

            val photoSelection = photoSelectionWhileSuppressed
            if (photoSelection != NO_SELECTION) {
                photoSelectionWhileSuppressed = NO_SELECTION
                setSelection(photoSelection)
            }
        }
    }

    /**
     * 启用或禁用用户启动的滚动, 包括触摸输入（滚动和滑动手势）和可访问性输入
     * @param enable
     */
    fun setUserInputEnable(enable: Boolean, reason: String) {
        GLog.i(TAG) { "[setUserInputEnable] enable=$enable reason=$reason" }
        isRequestingSwipeableFromOutter = enable
        updatePagerSwipeableStatus()
    }

    /**
     * 强制启用或禁用用户启动的滚动, 包括触摸输入（滚动和滑动手势）和可访问性输入
     * @param enable  true:启用
     *                false：禁用
     *                null：还原为原逻辑的（这里是指通过isRequestingSwipeableFromOutter/PagerMotion/RenderingStatus等共同控制的）
     */
    fun forceUserInputEnable(enable: Boolean?, reason: String) {
        GLog.d(TAG, LogFlag.DL) { "<forceUserInputEnable> enable=$enable reason=$reason" }
        if (isForceUserInputEnabled == enable) {
            GLog.d(TAG, LogFlag.DL) { "<forceUserInputEnable> not changed" }
            return
        }
        isForceUserInputEnabled = enable
        updatePagerSwipeableStatus()
    }

    /**
     * 取消一次onNotifyPageClicked触发的沉浸式布局切换
     * 部分业务场景下，虽然tap了屏幕，但是不希望触发沉浸式布局切换（超级文本2.0，点击文字区域）
     * 此接口将取消这次沉浸式布局切换
     * @param negate 是否取消
     */
    fun negateNextImmersionChange(negate: Boolean) {
        negateNextImmersionChange = negate
    }

    /**
     * 获取焦点图片的PhotoSlot
     *
     * Marked by yaoweihe,后续改造，收紧相关属性为只读
     * 目前接口过于开放，直接暴露[PhotoSlot]的相关属性
     * @param index 索引
     * @return PhotoSlot
     */
    fun findPhotoSlotBySlot(index: Int): PhotoSlot? {
        return if (isPagerInitialized) {
            photoPager.findPhotoSlotBySlot(index)
        } else {
            GLog.w(TAG, "findPhotoSlotBySlot, can't find PhotoSlot because photoPager is not initialized")
            null
        }
    }

    /**
     * 获取大图slot窗口的管控数量
     */
    fun getWindowSlotSize() = if (isPagerInitialized) {
        photoPager.loadingStrategy.controlSlotSize
    } else {
        controlSlotSize
    }

    /**
     * 获取当前slot展示内容的view
     * @return View
     */
    fun getFocusPresentationView(): View? {
        return photoPager.findPhotoSlotBySlot(currentFocus)?.contentRenderer?.presentation
    }

    /**
     * 注册大图触摸事件的回调
     * @param listener 回调，输入当前slot和MotionEvent，返回是否拦截事件
     */
    fun registerSlotTouchEventListener(listener: ((slot: Int, event: MotionEvent) -> Boolean)) {
        synchronized(slotTouchEventListeners) {
            if (slotTouchEventListeners.contains(listener).not()) {
                slotTouchEventListeners.add(listener)
            }
        }
    }

    /**
     * 取消注册大图触摸事件的回调
     * @param listener 回调，输入当前slot和MotionEvent，返回是否拦截事件
     */
    fun unregisterSlotTouchEventListener(listener: ((slot: Int, event: MotionEvent) -> Boolean)) {
        synchronized(slotTouchEventListeners) {
            slotTouchEventListeners.remove(listener)
        }
    }

    /**
     * 注册不拦截大图触摸事件的监听
     * @param listener 监听，分发slot和MotionEvent
     */
    @UiThread
    fun registerUninterruptedTouchEventListener(listener: ((slot: Int, event: MotionEvent) -> Unit)) {
        if (uninterruptedSlotTouchEventListeners.contains(listener).not()) {
            uninterruptedSlotTouchEventListeners.add(listener)
        }
    }

    /**
     * 注册不拦截大图触摸事件的监听，且根据返回值移除监听
     * @param listener 监听，分发slot和MotionEvent；返回 true 则移除此监听，false 则保留
     */
    @UiThread
    fun registerRemovableUninterruptedTouchEventListener(listener: ((slot: Int, event: MotionEvent) -> Boolean)) {
        if (removableUninterruptedSlotTouchEventListeners.contains(listener).not()) {
            removableUninterruptedSlotTouchEventListeners.add(listener)
        }
    }

    /**
     * 注销不拦截大图触摸事件的监听
     * @param listener 监听，分发slot和MotionEvent
     */
    @UiThread
    fun unregisterUninterruptedTouchEventListener(listener: ((slot: Int, event: MotionEvent) -> Unit)) {
        uninterruptedSlotTouchEventListeners.remove(listener)
    }

    /**
     * 注销不拦截大图触摸事件的监听
     * @param listener 监听，分发slot和MotionEvent；返回 true 则移除此监听，false 则保留
     */
    @UiThread
    fun unregisterRemovableUninterruptedTouchEventListener(listener: ((slot: Int, event: MotionEvent) -> Boolean)) {
        removableUninterruptedSlotTouchEventListeners.remove(listener)
    }

    /**
     * 注册大图长按事件的监听
     * @param listener 回调，输入当前slot，返回是否拦截长按事件
     */
    @UiThread
    fun registerSlotLongPressListener(listener: ((slot: Int) -> Boolean)) {
        if (slotLongPressListeners.contains(listener).not()) {
            slotLongPressListeners.add(listener)
        }
    }

    /**
     * 注销大图长按事件的监听
     * @param listener 回调，输入当前slot，返回是否拦截长按事件
     */
    @UiThread
    fun unregisterSlotLongPressListener(listener: ((slot: Int) -> Boolean)) {
        slotLongPressListeners.remove(listener)
    }

    /**
     * 注册焦点图片变化监听器
     * @param callback 需要注册的监听器
     */
    fun registerOnPageChangeCallback(callback: ViewPager.OnPageChangeCallback) {
        if (isPagerInitialized) {
            photoPager.registerOnPageChangeCallback(callback)
        } else {
            synchronized(pendingPageChangeCallbacks) {
                if (pendingPageChangeCallbacks.contains(callback).not()) {
                    pendingPageChangeCallbacks.add(callback)
                }
            }
        }
    }

    /**
     * 取消注册焦点图片变化监听器
     * @param callback 需要反注册的监听器
     */
    fun unregisterOnPageChangeCallback(callback: ViewPager.OnPageChangeCallback) {
        if (isPagerInitialized) {
            photoPager.unregisterOnPageChangeCallback(callback)
        } else {
            synchronized(pendingPageChangeCallbacks) {
                pendingPageChangeCallbacks.remove(callback)
            }
        }
    }

    /**
     * 注册焦点图片姿态变换的监听器，回调焦点图片姿态变换后的参数对象[SlotTransformPose]
     * @param listener 监听器
     */
    fun registerFocusSlotTransformUpdatedListener(listener: ((SlotTransformPose) -> Unit)) {
        synchronized(focusSlotTransformUpdatedListeners) {
            if (focusSlotTransformUpdatedListeners.contains(listener).not()) {
                focusSlotTransformUpdatedListeners.add(listener)
            }
        }
    }

    /**
     * 取消注册焦点图片姿态变换的监听器
     * @param listener 监听器
     */
    fun unregisterFocusSlotTransformUpdatedListener(listener: ((SlotTransformPose) -> Unit)) {
        synchronized(focusSlotTransformUpdatedListeners) {
            focusSlotTransformUpdatedListeners.remove(listener)
        }
    }

    /**
     * 注册图片滑动的监听器
     * @param listener 监听器
     */
    fun registerSlotSlideDownListener(listener: ((SlideDownEvent) -> Boolean)) {
        synchronized(slotSlideDownListeners) {
            if (slotSlideDownListeners.contains(listener).not()) {
                slotSlideDownListeners.add(listener)
            }
        }
    }

    /**
     * 取消注册图片滑动的监听器
     * @param listener 监听器
     */
    fun unregisterSlotSlideDownListener(listener: ((SlideDownEvent) -> Boolean)) {
        synchronized(slotSlideDownListeners) {
            slotSlideDownListeners.remove(listener)
        }
    }

    /**
     * 注册图片上滑的监听器
     * @param listener 监听器
     */
    fun registerSlotSlideUpListener(listener: ((SlideUpEvent) -> Boolean)) {
        synchronized(slotSlideUpListeners) {
            if (slotSlideUpListeners.contains(listener).not()) {
                slotSlideUpListeners.add(listener)
            }
        }
    }

    /**
     * 取消注册图片上滑的监听器
     * @param listener 监听器
     */
    fun unregisterSlotSlideUpListener(listener: ((SlideUpEvent) -> Boolean)) {
        synchronized(slotSlideUpListeners) {
            slotSlideUpListeners.remove(listener)
        }
    }

    /**
     * 注册单击事件的监听器
     */
    fun registerSlotSingleClickListener(listener: (slot: Int) -> Unit) {
        synchronized(slotSingleClickListeners) {
            if (slotSingleClickListeners.contains(listener).not()) {
                slotSingleClickListeners.add(listener)
            }
        }
    }

    /**
     * 取消注册单击事件的监听器
     */
    fun unregisterSlotSingleClickListener(listener: (slot: Int) -> Unit) {
        synchronized(slotSingleClickListeners) {
            slotSingleClickListeners.remove(listener)
        }
    }

    /**
     * 注册slot变化的事件监听
     */
    fun registerSlotBatchEventListener(listener: ISlotBatchEventCallback) {
        synchronized(slotBatchEventListeners) {
            if (slotBatchEventListeners.contains(listener).not()) {
                slotBatchEventListeners.add(listener)
            }
        }
    }

    /**
     * 注册当前focus的slot移除的监听器
     */
    fun registerFocusSlotRemovedListener(listener: () -> Unit) {
        synchronized(focusSlotRemovedListeners) {
            if (focusSlotRemovedListeners.contains(listener).not()) {
                focusSlotRemovedListeners.add(listener)
            }
        }
    }

    /**
     * 取消注册当前focus的slot移除的监听器
     */
    fun unregisterFocusSlotRemovedListener(listener: () -> Unit) {
        synchronized(focusSlotRemovedListeners) {
            focusSlotRemovedListeners.remove(listener)
        }
    }

    /**
     * 请求给 [PhotoPager] 使用动画 [usingSmoothScroll] 跳转到指定 [position] 位置
     *
     * @see [PhotoPager.setSelection]
     * @return 返回是否请求成功，即内部是否会处理该跳转请求。false：[photoPager] 未初始化、位置不变、数据集为空、[position] 不在数据集范围内；true：处理跳转
     */
    fun setSelection(position: Int, usingSmoothScroll: Boolean = false): Boolean {
        if (isPagerInitialized.not()) {
            GLog.d(TAG) { "[setSelection] ignore, pager not initialized" }
            return false
        }

        if (position == photoPager.currentSelection) {
            clearPendingPhotoSelection()
            val isViewDataMatchToPagerSelection = (position == currentFocus) && isFocusViewDataExpired.not()
            if (isViewDataMatchToPagerSelection) {
                GLog.d(TAG) { "[setSelection] ignore, invalid or selected position($position)" }
                return false
            }
            GLog.d(TAG) { "[setSelection] position=$position, only sync viewData to match pager selection" }
            onRequestingPagerSelection(position)
            return true
        }

        val dataRange = 0 until viewModel.dataLoading.totalCount
        if (dataRange.isEmpty()) {
            GLog.d(TAG) { "[setSelection] ignore, dataRange is empty" }
            return false
        }

        if (position !in dataRange) {
            GLog.d(TAG) { "[setSelection] ignore, position($position) not in dataRange($dataRange)" }
            return false
        }

        if (photoPager.isLayoutSuppressed) {
            GLog.d(TAG) { "[setSelection] delay, suppressed, position=$position, scheduledSelection=$photoSelectionWhileSuppressed" }
            if (photoSelectionWhileSuppressed != position) {
                clearPendingPhotoSelection()
                photoSelectionWhileSuppressed = position
                onRequestingPagerSelection(position)
            }
            return true
        }

        if (photoPager.isInLayout || photoPager.hasPendingAdapterUpdates) {
            GLog.d(TAG) { "[setSelection] delay, in layout, position=$position, scheduledSelection=$photoSelectionWhileSuppressed" }
            if (photoSelectionWhileSuppressed != position) {
                clearPendingPhotoSelection()
                photoDelaySelectionTask = object : Runnable {
                    override fun run() {
                        val scheduledSelection = photoSelectionWhileSuppressed
                        if (photoDelaySelectionTask == this) {
                            photoDelaySelectionTask = null
                            photoSelectionWhileSuppressed = NO_SELECTION
                            setSelection(scheduledSelection, usingSmoothScroll)
                            onRequestingPagerSelection(position)
                        }
                    }
                }
                photoPager.rootView.postOnAnimation(photoDelaySelectionTask)
                photoSelectionWhileSuppressed = position
            }
            return true
        }

        GLog.d(TAG) { "[setSelection] position=$position, usingSmoothScroll=$usingSmoothScroll" }
        clearPendingPhotoSelection()
        GTrace.trace("$TAG.setSelection.$position") {
            photoPager.setSelection(position, usingSmoothScroll)
        }
        GTrace.trace("$TAG.onRequestingPagerSelection.$position") {
            onRequestingPagerSelection(position)
        }
        return true
    }

    private fun clearPendingPhotoSelection() {
        photoSelectionWhileSuppressed = NO_SELECTION
        photoDelaySelectionTask?.let(photoPager.rootView::removeCallbacks)
        photoDelaySelectionTask = null
    }

    /**
     * 取消注册slot变化的事件监听
     */
    fun unregisterSlotBatchEventListener(listener: ISlotBatchEventCallback) {
        synchronized(slotBatchEventListeners) {
            slotBatchEventListeners.remove(listener)
        }
    }

    /**
     * 判断大图是否放大过且放大超过阈值 [differThreshold]
     */
    internal fun isPhotoSlotScaled(slot: Int = currentFocus, differThreshold: Double = DEFAULT_SCALE_THRESHOLD): Boolean {
        val provider = findPhotoSlotBySlot(slot)?.animationPropertiesProvider
            .getOrLog(TAG, "[isPhotoSlotScaled] provider") ?: return false
        val control = provider.animationControl
        val snapbackPose = control.snapbackPose
        val finalPose = control.finalPose

        fun isDiffer(me: Float, other: Float) = (me - other).absoluteValue > differThreshold
        val isXZoomed = isDiffer(snapbackPose.scaleX, finalPose.scaleX) && (finalPose.scaleX > snapbackPose.scaleX)
        val isYZoomed = isDiffer(snapbackPose.scaleY, finalPose.scaleY) && (finalPose.scaleY > snapbackPose.scaleY)
        return (isXZoomed || isYZoomed).also {
            GLog.d(TAG, LogFlag.DL) {
                "[isPhotoSlotScaled] result: $it, snapback: (${snapbackPose.scaleX}, ${snapbackPose.scaleY}), " +
                        "final: (${finalPose.scaleX}, ${finalPose.scaleY})"
            }
        }
    }

    override fun onResume() {
        super.onResume()
        GLog.d(TAG) { "[onResume] laterExitMode: $laterExitMode" }
        if ((EXIT_MODE_CURRENT_FRAGMENT == laterExitMode) || (EXIT_MODE_EXECUTOR == laterExitMode)) {
            laterExitFragment()
        }

        currentSectionMode = PhotoPagerSectionModeManager.getPhotoPagerSectionMode(this)
    }

    /**
     * 用于延时退出当前空fragment
     * 场景：虚拟图集仅1张图片，且编辑图片后覆盖保存时，会有两个大图fragment，不可见那层是空的。
     * 需同时满足以下条件：
     * 1.当前fragment数据为空
     * 2.当前fragment未在前台显示
     */
    private fun laterExitFragment() {
        // 避免启动重复任务
        if ((exitTask != null) && (exitTask?.isCancelled == false)) {
            GLog.w(TAG, "[updateCache] exitTask is already running")
            return
        }
        exitTask = sectionPage.pageLifecycle.coroutineScope.launch(Dispatchers.Main) {
            GTrace.traceBegin("$TAG.laterExitFragment")
            when (laterExitMode) {
                EXIT_MODE_CURRENT_FRAGMENT -> sectionPage.pageInstance.exitCurrentFragment()
                EXIT_MODE_EXECUTOR -> sectionPage.pageInstance.exitExecutor.exit()
            }
            laterExitMode = EXIT_MODE_NONE
            exitTask = null
            GTrace.traceEnd()
        }
    }

    override fun onStop() {
        super.onStop()
        isFromEditPage = false
    }

    override fun onResponsePageResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onResponsePageResult(requestCode, resultCode, data)
        if (requestCode == MenuRequestCode.PHOTO_EDIT_DEFAULT && resultCode == Activity.RESULT_OK) {
            isFromEditPage = true
        }
    }

    override fun onViewCreated(view: View) {
        bindViews()
        initViews(view)
        bindPendingCallbacks()
        if (isPagerInitialized) {
            photoPager.registerOnPageChangeCallback(photoPagerScrollCallback)
        }
        PhotoPagerSectionModeManager.notifyPageSectionCreated(this)
    }

    override fun onWindowInsetsChanged(windowInsets: WindowInsetsCompat) {
        super.onWindowInsetsChanged(windowInsets)
        updateOverlayInset()
    }

    override fun onViewDestroy() {
        super.onViewDestroy()
    }

    override fun onDestroy() {
        super.onDestroy()
        teardownIntegrationUITransitionIfNeeded()
        unregisterSlotTouchEventListener(this::onSynergyTouch)
        viewModel.dataLoading.diffedPhotoViewDataSet.removeObserver(diffedPhotoViewDataSetChangeObserver)
        viewModel.pageManagement.photoPageTransitionState.removeObserver(photoPageTransitionStateObserver)
        viewModel.dataLoading.diffedPhotoFocusHint.removeObserver(diffedPhotoFocusHintChangeObserver)
        viewModel.pageManagement.exitPage.removeObserver(exitPageObserver)
        viewModel.pageManagement.toastShort.removeObserver(toastShortObserver)
        clearViews()
        if (isPagerInitialized) {
            photoPager.unregisterOnPageChangeCallback(photoPagerScrollCallback)
        }
        PhotoPagerSectionModeManager.notifyPageSectionDestroyed(this)
    }

    /**
     * 销毁或者重建时，清理旧的 View 和数据的绑定，避免回调的引用链含有旧的 View
     */
    private fun clearViews() {
        if (isPagerInitialized) {
            photoPager.adapter = null
            photoPager.removeRenderingStatusListener(renderingStatusListener)
            photoPager.removeContentRendererStatusListener(contentRendererStatusListener)
        }
        if (::photoPageAdapter.isInitialized) photoPageAdapter.onFocusChangedListener = null
    }

    private fun bindViews() {
        photoPager = bindView(R.id.photo_view)
    }

    private fun bindPendingCallbacks() {
        synchronized(pendingPageChangeCallbacks) {
            pendingPageChangeCallbacks.forEach {
                photoPager.registerOnPageChangeCallback(it)
            }
            pendingPageChangeCallbacks.clear()
        }
    }

    private fun initViews(view: View) {
        // 初始化 PhotoPager
        initPhotoPager(view)

        // 订阅 VM 数据
        subscribeLiveDataFromViewModel()

        // 尝试初始化一体化 UI 过渡动效
        setupIntegrationUITransitionIfNeeded()
    }

    override fun onBackPressed(isBackHandled: Boolean): Boolean {
        if (isPagerInitialized) {
            notifySlotPresentationRegionChanged("onBackPressed")
        }
        return super.onBackPressed(isBackHandled)
    }

    private fun notifySlotPresentationRegionChanged(entry: String) {
        val slot = currentFocus.takeIf { it >= 0 } ?: let {
            GLog.d(TAG) { "[$entry.notifySlotPresentationRegion] focus not ready" }
            return
        }
        val photoSlot = findPhotoSlotBySlot(slot) ?: let {
            GLog.w(TAG) { "[$entry.notifySlotPresentationRegion] photoSlot($slot) not found" }
            return
        }
        if (photoSlot.currentRenderingStatus == RenderingStatus.NoReady) {
            GLog.d(TAG) { "[$entry.notifySlotPresentationRegion] render not ready" }
            return
        }
        val slotAabb = photoSlot.animationPropertiesProvider.currentAabb
        if (slotAabb.isEmpty) {
            GLog.d(TAG) { "[$entry.notifySlotPresentationRegion] empty region" }
            return
        }

        // 此处仅返回或者下拉时调用，不是短时内高频调用场景，故没有设计为全局临时变量
        val slotLocation = IntArray(LOCATION_ARRAY_SIZE).apply(photoSlot::getRendererLocationInWindow)
        val slotVisibleRect = Rect().apply(photoSlot::getRendererVisibleRect)
        val slotPresentRegion = Rect()
        if (!isValid(slotAabb)) {
            GLog.d(TAG) { "[$entry.notifySlotPresentationRegion] invalid value" }
            return
        }
        slotPresentRegion.set(
            (slotAabb.left.roundToInt() + slotLocation[0]).coerceAtLeast(slotVisibleRect.left),
            (slotAabb.top.roundToInt() + slotLocation[1]).coerceAtLeast(slotVisibleRect.top),
            (slotAabb.right.roundToInt() + slotLocation[0]).coerceAtMost(slotVisibleRect.right),
            (slotAabb.bottom.roundToInt() + slotLocation[1]).coerceAtMost(slotVisibleRect.bottom)
        )

        GLog.d(TAG) {
            "[$entry.notifySlotPresentationRegion] " +
                    "slot=$slot, " +
                    "slotPresentRegion=$slotPresentRegion, " +
                    "slotAabb=$slotAabb, " +
                    "slotVisibleRect=$slotVisibleRect, " +
                    "slotLocation=${slotLocation.contentToString()}"
        }
        viewModel.pageManagement.notifySlotPresentRegionChanged(slotPresentRegion)
    }

    private fun isValid(rect: RectF): Boolean {
        return rect.left.isNaN().not() && rect.top.isNaN().not() && rect.right.isNaN().not() && rect.bottom.isNaN().not()
    }

    private fun initPhotoPager(view: View) {
        photoPageAdapter = PhotoPageAdapter(
            lifecycleOwner = this,
            viewModel = viewModel,
            slotOverlayScratch = sectionPage.pageInstance.slotOverlayScratch
        )
        photoPageAdapter.onFocusChangedListener = ::onPagerSelectionChanged
        photoPager.hostLifecycle = this
        photoPager.loadingStrategy = WindowLoadingStrategy(
            PhotoStrategyLookup(view.context, viewModel, photoPager::currentSelection),
            controlSlotSize = controlSlotSize
        )
        photoPager.offScreenPageLimit = OFFSCREEN_PAGE_LIMIT
        photoPager.adapter = photoPageAdapter
        registerSlotTouchEventListener(this::onSynergyTouch)
        photoPager.onTouchEventCallback = this::onSlotTouchEvent
        photoPager.onSlotTransformUpdated = this::fireFocusSlotTransformUpdatedEvent
        photoPager.pagerMotionCallback = doCreateGalleryPhotoMotionCallback()
        photoPager.addRenderingStatusListener(renderingStatusListener)
        photoPager.addContentRendererStatusListener(contentRendererStatusListener)
        photoPager.visibility = View.INVISIBLE
        updateOverlayInset()
        initAccessibilityTouchHelper()
        updatePagerSwipeableStatus()
    }

    private fun onSynergyTouch(slot: Int, event: MotionEvent): Boolean {
        return sectionPage.pageViewModel.pageManagement.synergyTouchInterceptor.onInterceptTouchEvent(event)
    }

    /**
     * 更新大图 Overlay的inset信息
     */
    private fun updateOverlayInset() {
        if (isPagerInitialized.not()) return
        sectionPage.pageInstance.let { photoFragment ->
            photoPager.setSlotOverlayInset(
                left = photoFragment.leftNaviBarHeight(ignoringVisibility = false),
                top = photoFragment.statusBarHeight(ignoringVisibility = false),
                right = photoFragment.rightNaviBarHeight(ignoringVisibility = false),
                bottom = photoFragment.bottomNaviBarHeight(ignoringVisibility = false)
            )
        }
    }

    private fun isSwipingPagerInterrupted(): Boolean {
        return isPhotoGestureSuppressed
                || isRequestingSwipeableFromOutter.not()
                || isRequestingSwipeableFromPagerMotion.not()
                || isRequestingSwipeableFromRenderingStatus.not()
    }

    /**
     * 返回值无意义
     */
    private fun onSwipingPagerInterrupting(slot: Int, shouldInterruptSwipe: Boolean): Boolean {
        if (currentFocus != slot) {
            return false
        }

        GLog.i(TAG) { "[onSwipingPagerInterrupting] slot=$slot, shouldInterruptSwipe=$shouldInterruptSwipe" }
        isRequestingSwipeableFromPagerMotion = shouldInterruptSwipe.not()
        updatePagerSwipeableStatus()
        return true
    }

    private fun updatePagerSwipeableStatus() {
        if (isPagerInitialized.not()) {
            GLog.w(TAG) { "[updatePagerSwipeableStatus] ignore, photoPager not initialized" }
            return
        }
        isForceUserInputEnabled?.let {
            photoPager.isUserInputEnabled = it
            return
        }
        val isUserInputEnabled = isSwipingPagerInterrupted().not()
        if (photoPager.isUserInputEnabled == isUserInputEnabled) {
            return
        }

        GLog.i(TAG) {
            "[updatePagerSwipeableStatus] " +
                    "isSwipingPagerInterrupted=$isUserInputEnabled, " +
                    "isRequestingSwipeableFromOutter=$isRequestingSwipeableFromOutter, " +
                    "isRequestingSwipeableFromPagerMotion=$isRequestingSwipeableFromPagerMotion, " +
                    "isRequestingSwipeableFromRenderingStatus=$isRequestingSwipeableFromRenderingStatus"
        }

        photoPager.isUserInputEnabled = isUserInputEnabled
    }

    /**
     * Marked by zhangjisong，一坨事件，未进行有条理的管理，需后续整理优化
     */
    @Suppress("LongMethod")
    private fun doCreateGalleryPhotoMotionCallback(): GalleryPhotoMotionCallback {
        return GalleryPhotoMotionCallback(
            pager = photoPager,
            sectionPage = sectionPage,
            isSwipeInterrupted = ::isSwipingPagerInterrupted,
            onSwipeInterrupting = ::onSwipingPagerInterrupting,
            onNotifyPageClicked = { slot ->
                if (negateNextImmersionChange.not()) {
                    sectionPage.pageViewModel.pageManagement.changeImmersionInteractive(
                        (sectionPage.pageViewModel.pageManagement.isUnderImmersionInteractive.value ?: false).not()
                    )
                } else {
                    negateNextImmersionChange = false
                }

                fireSingleClickEvent(slot)

                // 埋点, 触发图片点击操作
                viewModel.track.trackPictureClick(value = CLICK_VIEW)
            },
            onNotifyPageDoubleClicked = {
                sectionPage.pageViewModel.pageManagement.changeImmersionInteractive(true)
            },
            onNotifyPageLongPressed = { slot ->
                // 注意执行顺序，优先满足浮窗，再是互联分享（产品要求）
                if (isSupportDrag()) {
                    //相册大图 上出现全局可拖动浮窗(内容为当前大图的photoPager画面的bitmap)
                    viewModel.common.notifyStartDragAndDrop(photoPager, slot)
                    return@GalleryPhotoMotionCallback
                }
                /**
                 * 跨屏互联：被投屏设备上控制投屏设备，投屏设备上进行(长按+拖动)操作，投屏设备上识别到这个操作行为后，
                 * 进行业务处理(拷贝 投屏设备的相册大图数据[图片或视频]到被投屏设备上)
                 */
                if (isPagerScrolling.not() && sectionPage.pageViewModel.pageManagement.synergyTouchInterceptor.onLongPress()) {
                    //触发长按事件后，禁止父view拦截后续事件，避免同时出现列表滑动
                    findPhotoSlotBySlot(slot)?.requestParentDisallowInterceptTouchEvent(true)
                    return@GalleryPhotoMotionCallback
                }
                fireSlotLongPressed(slot)
            },
            onNotifyPageAlphaChanged = { alpha ->
                sectionPage.pageViewModel.pageManagement.changePageBackgroundAlpha(alpha)
            },
            onNotifyPageExit = { _, slotRect ->
                GLog.d(TAG, "[onNotifyPageExit] trigger photo page exit, slot area at $slotRect")
                containerSection?.requestFinishPage(
                    transitionType = PhotoPageTransitionManager.TransitionType.THUMBNAIL_TRANSITION, // 下拉结束时，指定退出动画执行缩图类型的过渡动画
                    transitionStartPosition = slotRect,
                    trackActionData = PictureTrackConstant.Value.VIDEO_PLAYER_FINISH_PULL_DOWN,
                    exitTransitionFromType = PhotoPageTransitionManager.ExitTransitionFromType.TYPE_PULL_DONE
                ) ?: let {
                    GLog.e(TAG) { "[onNotifyPageExit] why is containerSection null, no config? CHECK CODE!" }
                }
            },
            onNotifyPageRotated = { slot, degrees ->
                sectionPage.pageViewModel.notifySlotRotated(slot, degrees).let { isRotated ->
                    // 旋转完成后，暂时禁止手势，待数据刷新后重新开放：防止数据未刷新时直接操作手势导致异常
                    findPhotoSlotBySlot(slot)?.apply {
                        isGestureEnabled = isRotated.not()
                    }
                }
            },
            onNotifyPageSlideDown = { slideEvent ->
                fireSlideDownEvent(slideEvent)
            },
            onNotifyPageSlideUp = { slideEvent ->
                fireSlideUpEvent(slideEvent)
            },
            onNotifyScaleRotateBegin = { slot ->
                GLog.d(TAG, "[onNotifyScaleRotateBegin] changeImmersionInteractive. slot:$slot")
                sectionPage.pageLifecycle.coroutineScope.launch(Dispatchers.SINGLE_UN_BUSY) {
                    GTrace.trace({ "$TAG.onScaleRotateBegin.setAction" }) {
                        CpuFrequencyManager.setAction(CpuFrequencyManager.Action.TOUCH_BOOST, UAH_EVENT_TOUCH_BOOST_TIMEOUT)
                    }
                }
                sectionPage.pageViewModel.pageManagement.changeImmersionInteractive(true)
                recordInitialDoubleTapZoomScale(slot)
            }
        )
    }

    private fun onSlotTouchEvent(slotIndex: Int, event: MotionEvent): Boolean {
        val action = event.action
        // 松手后，恢复小布识屏服务
        if ((action == MotionEvent.ACTION_UP) || (action == MotionEvent.ACTION_CANCEL)) {
            (sectionPage.pageContext as? Activity)?.let {
                viewModel.pageManagement.updateColorDirectAbility(it, true, from = "$TAG ${MotionEvent.actionToString(action)}")
            }
        }
        fireUninterruptedSlotTouchEvent(slotIndex, event)
        return fireSlotTouchEvent(slotIndex, event)
    }

    private fun fireSlotTouchEvent(slotIndex: Int, event: MotionEvent): Boolean {
        var ans = false
        synchronized(slotTouchEventListeners) {
            slotTouchEventListeners.forEach { callback ->
                if (callback(slotIndex, event)) {
                    ans = true
                    return@forEach
                }
            }
        }
        return ans
    }

    @UiThread
    private fun fireSlotLongPressed(slotIndex: Int): Boolean {
        var ans = false
        slotLongPressListeners.forEach { callback ->
            if (callback(slotIndex)) {
                ans = true
                return@forEach
            }
        }
        return ans
    }

    @UiThread
    private fun fireUninterruptedSlotTouchEvent(slotIndex: Int, event: MotionEvent) {
        uninterruptedSlotTouchEventListeners.forEach { callback ->
            callback(slotIndex, event)
        }

        val iterator = removableUninterruptedSlotTouchEventListeners.iterator()
        while (iterator.hasNext()) {
            if (iterator.next().invoke(slotIndex, event)) {
                iterator.remove()
            }
        }
    }

    /**
     * 分发焦点图片姿态变换更新的事件
     */
    private fun fireFocusSlotTransformUpdatedEvent(slot: Int, animControl: AnimationControl) {
        if (slot != photoPager.currentSelection) {
            return
        }
        val photoSlot = photoPager.findPhotoSlotBySlot(slot)
        photoSlot ?: return
        val properties = photoSlot.animationPropertiesProvider
        val slotTransformPose = SlotTransformPose(
            animControl.currentPose.pivotX,
            animControl.currentPose.pivotY,
            animControl.currentPose.scaleX,
            animControl.currentPose.scaleY,
            animControl.currentPose.positionX,
            animControl.currentPose.positionY,
            animControl.currentPose.angle,
            animControl.onGetMinZoomScale(),
            animControl.onGetMaxZoomScale(),
            photoPager.width,
            photoPager.height,
            photoSlot.width,
            photoSlot.height,
            isAnimating = animControl.isAnimating(),
            properties.currentAabb
        )
        synchronized(focusSlotTransformUpdatedListeners) {
            focusSlotTransformUpdatedListeners.forEach {
                it.invoke(slotTransformPose)
            }
        }
    }

    private fun fireSlideDownEvent(slideDownEvent: SlideDownEvent): Boolean {
        return synchronized(slotSlideDownListeners) {
            var handled = false
            slotSlideDownListeners.forEach {
                handled = it(slideDownEvent) or handled
            }
            handled
        }
    }

    private fun fireSlideUpEvent(slideUpEvent: SlideUpEvent): Boolean {
        return synchronized(slotSlideUpListeners) {
            var handled = false
            slotSlideUpListeners.forEach {
                handled = it(slideUpEvent) or handled
            }
            handled
        }
    }

    private fun fireSingleClickEvent(slot: Int) {
        slotSingleClickListeners.toList().forEach { it(slot) }
    }

    /**
     * 分发 set 更新的事件
     */
    private fun fireSlotBatchSetChangedEvent() {
        slotBatchEventListeners.toList().forEach {
            it.slotBatchSetChanged()
        }
    }

    /**
     * 分发slot新增的事件
     */
    private fun fireSlotBatchInsertedEvent(position: Int, count: Int) {
        slotBatchEventListeners.toList().forEach {
            it.slotBatchInserted(position, count)
        }
    }

    /**
     * 分发slot移除的事件
     */
    private fun fireSlotBatchRemovedEvent(position: Int, count: Int) {
        slotBatchEventListeners.toList().forEach {
            it.slotBatchRemoved(position, count)
        }

        if (focusSlotInRange(position, count)) {
            viewModel.pageManagement.setShouldAnimateWhenRemoved(true)
            focusSlotRemovedListeners.toList().forEach {
                it.invoke()
            }
        }
    }

    /**
     * 分发slot移动的事件
     */
    private fun fireSlotBatchMovedEvent(fromPosition: Int, toPosition: Int) {
        slotBatchEventListeners.toList().forEach {
            it.slotBatchMoved(fromPosition, toPosition)
        }
    }

    /**
     * 分发slot刷新的事件
     */
    private fun fireSlotBatchInvalidateEvent(position: Int, count: Int) {
        slotBatchEventListeners.toList().forEach {
            it.slotBatchInvalidate(position, count)
        }
    }

    /**
     * 分发 slot更新事件分发完毕的事件。
     */
    private fun fireSlotBatchEventFinished() {
        slotBatchEventListeners.toList().forEach {
            it.slotBatchEventFinished()
        }
    }

    /**
     * 判断当前focus的slotId是否在范围内，数据变化过程中，需使用getSlidingWindowFocus获取当前滑动窗口的焦点位置
     */
    private fun focusSlotInRange(fromPosition: Int, count: Int): Boolean {
        val slotId = (photoPager.loadingStrategy as? WindowLoadingStrategy)?.getSlidingWindowFocus()
            ?: sectionPage.pageViewModel.dataLoading.focusSlot
        if (slotId < 0) {
            return false
        }
        return slotId in fromPosition until fromPosition + count
    }

    private fun subscribeLiveDataFromViewModel() {
        /*
         * 订阅 PhotoPager 数据集相关数据
         * Marked for zhangjisong :后续优化，考虑用flow
         */
        viewModel.dataLoading.diffedPhotoViewDataSet.observeForever(diffedPhotoViewDataSetChangeObserver)

        // 订阅 PhotoPager 显示与否的数据
        viewModel.pageManagement.photoPagerVisibility.observe(this) { it?.let(::updatePhotoPagerVisibility) }

        viewModel.pageManagement.pageOperationState.observe(this, ::updatePhotoPageOperationState)

        /*
         订阅焦点 ViewData 更新的数据
         使用observeForever原因：在基于原图保存的编辑场景（超级文本等）下，为了防止页面
         从旧图闪烁到新图需要后台刷新缩图
         */
        viewModel.viewModelScope.launch(Dispatchers.Main.immediate) {
            viewModel.dataLoading.diffedFocusViewData.collectNotNull(diffedFocusViewDataChangeCollector)
        }

        /*
         * 订阅期望焦点 focusHintDiff 更新的数据
         * Marked for zhangjisong :后续优化，考虑用flow
         */
        viewModel.dataLoading.diffedPhotoFocusHint.observeForever(diffedPhotoFocusHintChangeObserver)

        /**
         *监听PageTransitionState的状态，如果当前是退出状态，通知Renderer，即将要销毁
         */
        viewModel.pageManagement.photoPageTransitionState.observeForever(photoPageTransitionStateObserver)

        viewModel.pageManagement.exitPage.observeForever(exitPageObserver)

        viewModel.pageManagement.toastShort.observeForever(toastShortObserver)

        viewModel.pageManagement.firstFrameRenderingStatus.observe(this) {
            // 只有无缝动画的情况下才需要考虑渲染状态是否就绪，为了避免无缝动画情况下，相机和相册的内容叠加
            isRequestingSwipeableFromRenderingStatus = if (isSwipeForbiddenWhenNotRender) isFirstFrameRenderingReady else true
            GLog.i(TAG) { "[firstFrameRenderingStatus] isRequestingSwipeableFromRenderingStatus=$isRequestingSwipeableFromRenderingStatus" }
            updatePagerSwipeableStatus()
        }

        viewModel.pageManagement.resetPhotoPagerStateEvent.observe(this) {
            resetPhotoPagerState()
        }
    }

    private fun updatePhotoPagerDataSet(diffedPhotoViewDataSet: DiffedPhotoViewDataSet) {
        GLog.d(TAG) {
            "[updatePhotoPagerDataSet] focus=$currentFocus, isAllDataChanged=${diffedPhotoViewDataSet.isFullUpdate}, " +
                    "viewDataSetDiff=$diffedPhotoViewDataSet"
        }

        val dataCount = viewModel.dataLoading.totalCount
        if (dataCount <= 0) {
            if (isFromEditPage) {
                laterExitMode = EXIT_MODE_EXECUTOR
            } else {
                // 图片数量为0，退出大图
                GLog.d(TAG, "[updatePhotoPagerDataSet] invalid dataCount($dataCount), try exit")
                /**
                 * 虚拟图集开多个大图，如果底部大图数量是0，二次编辑后底部大图onStop isFromEditPage是false，所以非顶层大图
                 * 这里判断是不是多个大图即可，多个大图以 exitExecutor.exit 方式延时退出
                 */
                if ((lifecycle.currentState == Lifecycle.State.CREATED)
                    || (PhotoPagerSectionModeManager.getPhotoPagerSectionMode(this) == PhotoPagerSectionMode.MULTIPLE)
                ) {
                    GLog.d(TAG, LogFlag.DF, "[updatePhotoPagerDataSet] have multiple photoFragment,exit later")
                    laterExitMode = EXIT_MODE_EXECUTOR
                } else {
                    sectionPage.pageInstance.exitExecutor.exit()
                    return
                }
            }
        }

        if (diffedPhotoViewDataSet.isFullUpdate) {
            fireSlotBatchSetChangedEvent()
            photoPageAdapter.notifyDataSetChanged()
        } else {
            // Marked by Mountain : 后续调整此处逻辑
            val hasPendingEvents = diffedPhotoViewDataSet.isFullUpdate.not()
            diffedPhotoViewDataSet.dispatchUpdatesTo(diffCallback)
            if (hasPendingEvents) {
                /**
                 * 如果本次有事件更新（[diffedPhotoViewDataSet.newDataSet.dataDiffer]），将事件分发完毕的事件通知出去。
                 */
                fireSlotBatchEventFinished()

                /**
                 * 通知大图slot事件更新完毕
                 * mark by liyunting 当前先在此下发diff事件完毕后，通知大图校正窗口的事件，
                 * 待PhotoPager重新调整滑动窗口的校正设计后，再更正这个地方
                 */
                photoPageAdapter.notifySlotBatchEventFinished()


                /**
                 * 背景：
                 *
                 * 所有的位置更新源头来自 View 层（[notifyPhotoSlotFocusChanged]），
                 * VM 里面不知道何时更新焦点和焦点 ViewData，VM 只能被动收到请求后，才会去更新焦点 ViewData
                 *
                 * 如果 differ 里面有 events，说明有增删移改的情况，一般情况下，增删移改会自动更新焦点位置（如果会改变焦点）
                 *
                 * 有一种情况不会更新焦点位置（RecyclerView 不会通知更新），比如：
                 * 1. 有 100 个图片或者视频，大图当前在展示第 50 个
                 * 2. 删除当前图片（50），后面的图片（51）会跟随过来，并且会更新为新位置 50
                 * 3. 整体来说，焦点位置没有更新，RecyclerView 也就不会通知出去
                 *
                 * 问题：
                 *
                 * 但是，即使焦点没变，只要元素变了，VM 里面也需要及时更新焦点 ViewData。
                 * 上述的情况，就无法满足，因为删除后，焦点没变，view 没有通知，vm 不知道有变更，也就没有更新焦点 viewData。
                 * 导致监听焦点 viewData 的代码不能正常工作
                 *
                 *
                 * 方案：
                 *
                 * 1. 当发现 differ 里面有 events，说明有增删移改的情况，就尝试去更新一下焦点（[notifyPhotoSlotFocusChanged]）
                 * 2. 更新焦点需要特殊时机，因为 differ 分发后， RecyclerView 在下一帧才会执行增删移的动作，只有这些动作完成后才会更新焦点
                 * 3. 为了掌握正确的焦点位置，所以 postOnAnimation + post
                 * 4. 即使多次 [notifyPhotoSlotFocusChanged]，VM 里面会有去重，不会有重复的情况
                 */
                photoPager.postOnAnimation {
                    photoPager.post { onPagerSelectionChanged(slot = photoPager.currentSelection, isFromUser = false) }
                }
            }
        }
    }

    private fun updatePhotoPagerVisibility(isVisible: Boolean) {
        GLog.i(TAG) { "[updatePhotoPagerVisibility] isVisible=$isVisible" }
        val targetVisibility = if (isVisible) View.VISIBLE else View.INVISIBLE
        photoPager.visibility = targetVisibility
        //视频再做退出动画时，会透出surfaceview，根因暂无法定位，先做兜底规避
        findPhotoSlotBySlot(currentFocus)?.contentRenderer?.presentation?.visibility = targetVisibility
    }

    private fun updatePhotoPageOperationState(pageOperationState: PageOperationState) {
        GLog.d(TAG) { "[updatePhotoPageOperationState] pageOperationState=$pageOperationState" }

        updatePagerSwipeableStatus()
        photoPager.suppressLayout(PageOperationState.SuppressPhotoLayout in pageOperationState)

        // 当存在禁用大图手势时，需要关闭photoPager的手势交互
        photoPager.isSlotGestureEnabled = (SuppressPhotoGesture !in pageOperationState)

        // 注意：一定要先更新 photoPager suppressLayout 状态，再去调用 setSelection；否则 setSelection 不会生效
        if (photoPager.isLayoutSuppressed.not() && (photoSelectionWhileSuppressed != NO_SELECTION)) {
            // 要先保存 photoSelectionWhileSuppressed 为局部变量，再重置其，当 setSelection 时，可能会改变该变量
            val photoSelection = photoSelectionWhileSuppressed
            photoSelectionWhileSuppressed = NO_SELECTION
            setSelection(photoSelection)
        }
    }

    private fun updateFocusSlotViewData(diffedFocusViewData: DiffedPhotoItemViewData) {
        updateFirstFrameRenderingStatusIfNeeded()
        invalidateFocusNeighborSlotsIfNeeded(diffedFocusViewData)
        restoreInitialDoubleTapZoomScale(diffedFocusViewData)
        // 大图后台后，sectionDiff监听生命周期不会执行，而此处使用observerForever监听会执行，保证回到大图不会看到变更前的图片。
        if (lifecycle.currentState.isAtLeast(Lifecycle.State.STARTED)) {
            return
        }
        GLog.d(TAG) { "[updateFocusSlotViewData] updating. currentFocus: $currentFocus diff=$diffedFocusViewData" }
        diffCallback.onChanged(currentFocus, 1)
    }

    private fun updateFirstFrameRenderingStatusIfNeeded() {
        if (viewModel.pageManagement.firstFrameRenderingStatus.value == FirstFrameRenderingStatus.ALL_READY) {
            return
        }
        /**
         * 刷新第一帧状态
         * */
        currentFocus.let(photoPager::findPhotoSlotBySlot)?.currentRenderingStatus?.let {
            notifyFirstFrameRenderingStatusIfNeeded(it)
        }
    }

    /**
     * 接收到图片renderingStatus变化，如果判断是相机进相册查看的照片第一次变成高清状态，清除保存的记录信息
     *
     * @param position renderingStatus变化图片在列表对应的位置
     * @param newRenderingStatus 图片当前变化的状态
     */
    private fun updateSpecialThumbnailLoadRequesterIfNeeded(
        position: Int,
        newRenderingStatus: RenderingStatus
    ) {
        val info = viewModel.contentLoading.specialThumbnailLoadRequester ?: let {
            return
        }

        val data = viewModel.dataLoading.photoViewDataSet?.get(position) ?: let {
            GLog.e(TAG) { "[updateSpecialThumbnailLoadRequesterIfNeeded] viewData is null, can't get focus media info, return" }
            return
        }

        GLog.d(TAG) {
            "[updateSpecialThumbnailLoadRequesterIfNeeded] data.mediaId: ${data.mediaId}," +
                    "info.mediaId: ${info.mediaId}, newRenderingStatus: $newRenderingStatus"
        }
        if ((data.mediaId == info.mediaId) && ContentReady in newRenderingStatus) {
            GLog.d(TAG) { "[updateSpecialThumbnailLoadRequesterIfNeeded] update requester to null, indicates that it has been consumed" }
            viewModel.contentLoading.updateSpecialThumbnailLoadRequester(null)
        }
    }

    private fun notifyFirstFrameRenderingStatusIfNeeded(focusSlotRenderingStatus: RenderingStatus) {
        val isThumbnailReady = ThumbnailReady in focusSlotRenderingStatus
        val isContentReady = ContentReady in focusSlotRenderingStatus
        val newRenderingStatus = when {
            isThumbnailReady && isContentReady -> FirstFrameRenderingStatus.ALL_READY
            isThumbnailReady -> FirstFrameRenderingStatus.THUMBNAIL_READY
            isContentReady -> FirstFrameRenderingStatus.CONTENT_READY
            else -> FirstFrameRenderingStatus.NONE_READY
        }

        val oldRenderingStatus = viewModel.pageManagement.firstFrameRenderingStatus.value
        if (oldRenderingStatus == newRenderingStatus) {
            return
        }

        GLog.i(TAG, LogFlag.DL) {
            "[notifyFirstFrameRenderedIfNeeded] " +
                    "currentFocus=$currentFocus, " +
                    "focusSlotRenderingStatus=$focusSlotRenderingStatus, " +
                    "oldRenderingStatus=$oldRenderingStatus, " +
                    "newRenderingStatus=$newRenderingStatus, "
        }
        viewModel.pageManagement.notifyFirstFrameRenderingStatus(newRenderingStatus)
        if (newRenderingStatus == FirstFrameRenderingStatus.THUMBNAIL_READY
            || newRenderingStatus == FirstFrameRenderingStatus.CONTENT_READY
        ) {
            RealShowTimeInstrument.endRecord(PHOTO_SHOW_TAG)
        }
    }

    /**
     * 当大图焦点由外部通过 [setSelection] 驱动时，会通知 VM 更新焦点
     *
     * 所有来自外部并通过 [setSelection] 导致焦点的变更，应该在此处分发处理
     *
     * 该函数生效条件：[isFocusSlotDroveByPagerSelection] == false
     *
     * @see onPagerSelectionChanged
     */
    private fun onRequestingPagerSelection(slot: Int) {
        if (isFocusSlotDroveByPagerSelection) {
            return
        }

        notifyPhotoSlotFocusChanged(slot = slot, "onRequestingPagerSelection")
    }

    /**
     * 当大图焦点由内部通过 [photoPager] 驱动时，会通知 VM 更新焦点
     *
     * 所有来自 [photoPager] 焦点的变更，应该在此处分发处理
     *
     * 该函数生效条件：[isFocusSlotDroveByPagerSelection] == true
     *
     * @see onRequestingPagerSelection
     */
    private fun onPagerSelectionChanged(slot: Int, isFromUser: Boolean) {
        if (isFromUser) {
            viewModel.pageManagement.notifyPhotoSlide(currentFocus, slot)
        }

        if (isFocusSlotDroveByPagerSelection.not()) {
            return
        }

        notifyPhotoSlotFocusChanged(slot = slot, "onPagerSelectionChanged")
    }

    private fun updateSelectionIfDroveByPager(photoFocusHint: PhotoFocusHint) {
        val (_, pendingSlot) = photoFocusHint

        if (isFocusSlotDroveByPagerSelection.not()) {
            GLog.d(TAG) { "[updateSelectionIfDroveByPager] ignore, pendingSlot=$pendingSlot, isFocusSlotDroveByPagerSelection=false" }
            viewModel.dataLoading.focusHint?.markConsumed()
            return
        }

        if (photoPager.hasPendingAdapterUpdates) {
            /**
             * 正在执行更新或者动画，不要设置 selection，避免闪烁和错乱，
             * post 一次再检查，如果还在更新或者动画，则继续 post，直到没有更新或者动画为止
             */
            GLog.d(TAG) { "[updateSelectionIfDroveByPager] delaying, pendingSlot=$pendingSlot, hasPendingAdapterUpdates=true" }
            photoPager.post { viewModel.dataLoading.focusHint?.let(::updateSelectionIfDroveByPager) }
            return
        }

        /**
         *  用于判定是否需要滑动动画
         *
         *  - 如果是第一次进入，直接跳到目标位置即可，避免滑动
         *  - 如果已经设置过位置，正在展示，为避免闪烁，需要使用滑动
         */
        val useSmoothScroll = photoFocusHint.isConsumed
        photoFocusHint.markConsumed()
        val pagerSelection = photoPager.currentSelection
        GLog.d(TAG) { "[updateSelectionIfDroveByPager] updating, pendingSlot=$pendingSlot, pagerSelection=$pagerSelection, smooth=$useSmoothScroll" }
        if (pendingSlot == pagerSelection) {
            /**
             * PhotoPager 当得到数据后，默认选中的就是位置 0，如果调用 setSelection，会被去重忽略掉，
             * 需要手动 [notifyPhotoSlotFocusChanged] 通知 VM 焦点更新，以便刷新焦点数据
             */
            onPagerSelectionChanged(slot = pendingSlot, isFromUser = false)
        } else {
            photoPager.post { photoPager.setSelection(slot = pendingSlot, usingSmoothScroll = useSmoothScroll) }
        }
    }

    private fun notifyPhotoSlotFocusChanged(slot: Int, reason: String) {
        if (isFocusSlotDroveByPagerSelection && (viewModel.dataLoading.focusHint?.isConsumed != true)) {
            GLog.d(TAG) {
                "[notifyPhotoSlotFocusChanged] ignore, focusHint not ready," +
                        "slot=$slot, " +
                        "reason=$reason"
            }
            return
        }

        val viewDataCount = viewModel.dataLoading.totalCount
        if (slot !in (0 until viewDataCount)) {
            GLog.w(TAG) {
                "[notifyPhotoSlotFocusChanged] ignore, invalid data range," +
                        "total=$viewDataCount, " +
                        "slot=$slot, " +
                        "reason=$reason"
            }
            return
        }
        /*
         * 此处不必去重，可能是因为 Diff insert、remove 导致的，因此即使焦点相同，也需要告知 VM 焦点也变更了
         * 去重的操作交给 VM 去做，它那里最有数据更新的发言权
         */
        GLog.d(TAG) { "[notifyPhotoSlotFocusChanged] notify, slot=$slot, reason=$reason" }

        viewModel.dataLoading.notifyFocusSlotChanged(slot)
    }

    /**
     * 进入大图当viewModel.focusSlotViewData为null的时候，状态机
     * 状态没有到 Content状态，所以会导致左右两边item没有及时刷新(相关逻辑在 PhotoStrategyLookup 里面 onCalcMaxSlotState函数中)
     * 因此当focusSlotViewData从null变为有值时，尝试主动刷新一次左右两边的slot
     */
    private fun invalidateFocusNeighborSlotsIfNeeded(diffedFocusViewData: DiffedPhotoItemViewData) {
        if (isPagerInitialized.not()) {
            return
        }
        val shouldInvalidateFocusNeighborSlots = (diffedFocusViewData.oldItem == null) && (diffedFocusViewData.newItem != null)
        if (shouldInvalidateFocusNeighborSlots.not()) {
            return
        }
        val validSlotRange = 0 until viewModel.dataLoading.totalCount
        val neighborSlot = setOf(currentFocus - 1, currentFocus + 1).filter { it in validSlotRange }
        // 这里是 PhotoPager 自身刷新行为，不要通过 DiffCallback 的方式统一分发出去，避免影响其他模块
        neighborSlot.forEach { photoPageAdapter.notifySlotInvalidate(it) }
        GLog.d(TAG) {
            "[invalidateFocusNeighborSlotsIfNeeded] invalidate, currentFocus = $currentFocus, neighborSlot = $neighborSlot"
        }
    }

    /**
     * 获取 [position] 位置的渲染状态
     *
     * @return 返回 [RenderingStatus] 对象，非空，当找不到对应 [position] 时，返回 [RenderingStatus.NoReady]
     */
    fun getRenderingStatus(position: Int): RenderingStatus {
        if (isPagerInitialized.not()) return RenderingStatus.NoReady
        return photoPager.findPhotoSlotBySlot(position)?.currentRenderingStatus ?: RenderingStatus.NoReady
    }

    /**
     * 添加渲染状态监听器
     */
    fun addRenderingStatusListener(listener: PhotoSlot.RenderingStatusListener) {
        if (isPagerInitialized.not()) return
        photoPager.addRenderingStatusListener(listener)
    }

    /**
     * 添加渲染状态监听器
     */
    fun removeRenderingStatusListener(listener: PhotoSlot.RenderingStatusListener) {
        if (isPagerInitialized.not()) return
        photoPager.removeRenderingStatusListener(listener)
    }

    fun addContentRendererStatusListener(listener: PhotoSlot.ContentRendererStatusListener) {
        if (isPagerInitialized.not()) return
        photoPager.addContentRendererStatusListener(listener)
    }

    fun removeContentRendererStatusListener(listener: PhotoSlot.ContentRendererStatusListener) {
        if (isPagerInitialized.not()) return
        photoPager.removeContentRendererStatusListener(listener)
    }

    private fun setupIntegrationUITransitionIfNeeded() {
        if (hasIntegrationUITransition.not()) {
            return
        }

        registerSlotSlideDownListener(integrationUITransitionSlideDownListener)
    }

    private fun teardownIntegrationUITransitionIfNeeded() {
        if (hasIntegrationUITransition.not()) {
            return
        }

        unregisterSlotSlideDownListener(integrationUITransitionSlideDownListener)
    }

    private fun handleSlideDownEventForIntegrationUITransition(slideDownEvent: SlideDownEvent): Boolean {
        val integrationUIGestureToken = viewModel.inputArguments.features.value?.integrationUIGestureToken ?: let {
            GLog.e(TAG) {
                "[handleSlideDownEventForIntegrationUITransition] hasIntegrationUI transition, but no integrationUIGestureToken!!!"
            }
            return false
        }

        var shouldInterceptSlideEvent = isIntegrationUITransitionTransferringTouch

        when (slideDownEvent) {
            is SlideDownEvent.SlidingDown -> Unit
            is SlideDownEvent.SlideDownBegin -> {
                GLog.i(TAG) { "[handleSlideDownEventForIntegrationUITransition] SlideDownBegin" }

                notifySlotPresentationRegionChanged("SlideDownBegin")

                val displayId = sectionPage.pageInstance.activity?.displayOrNull?.displayId ?: Display.DEFAULT_DISPLAY
                val isTransferSuccess = viewModel.pageManagement.transferTouch(integrationUIGestureToken, displayId)
                GLog.d(TAG) { "[handleSlideDownEventForIntegrationUITransition] isTransferSuccess=$isTransferSuccess" }

                isIntegrationUITransitionTransferringTouch = isTransferSuccess
                shouldInterceptSlideEvent = isTransferSuccess
                if (isTransferSuccess) {
                    viewModel.pageManagement.changeImmersionInteractive(true)
                }
            }

            is SlideDownEvent.SlideDownEnd -> {
                GLog.i(TAG) { "[handleSlideDownEventForIntegrationUITransition] SlideDownEnd" }
                isIntegrationUITransitionTransferringTouch = false
            }
        }

        return shouldInterceptSlideEvent
    }

    /**
     * 通知强制刷新 position为 [slot]的PhotoSlot
     * @param slot Int
     */
    fun notifySlotInvalidate(slot: Int) {
        GLog.d(TAG) { "[notifySlotInvalidate] notify slot invalidate: $slot" }
        photoPageAdapter.notifySlotInvalidate(slot)
    }

    /**
     * 以当前focusSlot为中心，遍历获取可管控范围内PhotoSlot中的contentRender对象
     * @param action 遍历时回调对象
     */
    fun foreachSlotRenderer(action: (DrawableRenderer<View, Drawable>) -> Unit) {
        val slotCount: Int = controlSlotSize
        val focusSlot: Int = viewModel.dataLoading.focusSlot ?: return

        /**
         * Marked by zhangwenming findPhotoSlotBySlot本不应该开放出来，但是这里需要将事件通知到所有的Renderere中
         * 当前没有更好的设计，暂时遗留，看后面如果场景多了，统一设计。
         */
        for (index in (focusSlot - slotCount / 2)..(focusSlot + slotCount / 2)) {
            invokeSlotRenderer(index, action)
        }
    }

    /**
     * 获取到index位置上的PhotoSlot中的contentRender对象，并做对应回调操作
     * @param index 指定下标位置
     * @param action 回调对象
     */
    fun invokeSlotRenderer(index: Int, action: (DrawableRenderer<View, Drawable>) -> Unit) {
        findPhotoSlotBySlot(index)?.contentRenderer?.let { action(it) }
    }

    /**
     * 将PhotoPager当前的状态回正归位。如背景透明度、矩阵变换、手势操作等。
     */
    private fun resetPhotoPagerState() {
        GLog.i(TAG, LogFlag.DL) { "[resetPhotoPagerState], will reset slot gesture、background and so on." }
        findPhotoSlotBySlot(currentFocus)?.animationPropertiesProvider?.animationControl?.snapback()
        sectionPage.pageViewModel.pageManagement.changePageBackgroundAlpha(1f)
        // 恢复页面的交互方式
        sectionPage.pageViewModel.pageManagement.changeImmersionInteractive(isImmersiveEnabled = true)
        // 通知下拉取消。
        sectionPage.pageViewModel.pageManagement.notifySlideEvent(PhotoPageManagementViewModel.SlideEvent.SlideDownCancel)
    }

    /**
     * 创建大图Loading Overlay附加层
     * @param slot item的位置
     * @param callback 返回一个构建好的overlay对象
     */
    private fun createOverlay(context: Context, slot: Int, callback: (PhotoSlotOverlay) -> Unit) {
        viewModel.launch(Dispatchers.SINGLE_UN_BUSY) {
            GTrace.traceBegin("$TAG.createOverlay")
            val contentView = LayoutInflater.from(context).inflate(R.layout.photo_overlay_tips, null)
            GTrace.traceEnd()

            withContext(Dispatchers.UI) {
                PhotoTipsOverlay(contentView).let(callback)
            }
        }
    }

    /**
     * 通知Renderer，即将要销毁
     */
    private fun notifyRendererPreRelease() {
        foreachSlotRenderer {
            (it as? IRendererLifecycleObserver)?.onPreReleaseRenderer()
        }
    }

    /**
     * 在双手放大图片前，记录双击放大图片的初始化倍率
     * 初始倍率来自PhotoSlotSizeCalculator，但是每次使用都需要创建对象，不好修改，所以使用Pair缓存
     * @param slot 当前图片位置
     */
    private fun recordInitialDoubleTapZoomScale(slot: Int) {
        val id = viewModel.dataLoading.focusItemViewData?.id ?: return
        if (initialDoubleTapZoomScalePair?.first == id) return
        photoPager.findPhotoSlotBySlot(slot)?.animationPropertiesProvider?.doubleTapZoomInScale?.invoke()?.also {
            initialDoubleTapZoomScalePair = Pair(id, it)
        }
    }

    /**
     * 在滑动切换图片后，退出图片，恢复该图原有的的双击放大图片初始倍率
     * 初始倍率来自PhotoSlotSizeCalculator，但是每次使用都需要创建对象，不好修改，所以使用Pair缓存
     * @param diffedFocusViewData 焦点数据
     */
    private fun restoreInitialDoubleTapZoomScale(diffedFocusViewData: DiffedPhotoItemViewData) {
        val oldItem = diffedFocusViewData.oldItem ?: return
        if (oldItem.id == diffedFocusViewData.newItem?.id) return
        val (id, initialDoubleTapZoomScale) = initialDoubleTapZoomScalePair ?: return

        if ((oldItem.id == id).not()) return

        photoPager.findPhotoSlotBySlot(oldItem.position)?.animationPropertiesProvider?.let { provider ->
            provider.doubleTapZoomInScale = { initialDoubleTapZoomScale }
        }
        initialDoubleTapZoomScalePair = null
    }

    /**
     * Slot在变换过程中的内容姿态
     */
    internal data class SlotTransformPose(
        val pivotX: Float,
        val pivotY: Float,
        val scaleX: Float,
        val scaleY: Float,
        val positionX: Float,
        val positionY: Float,
        val angle: Float,
        val minScale: Float,
        val maxScale: Float,
        val pageWidth: Int,
        val pageHeight: Int,
        val slotWidth: Int,
        val slotHeight: Int,
        val isAnimating: Boolean = false,
        val currentAabb: RectF
    ) {
        /**
         * 当前位置数据能否转换为矩阵
         */
        fun canConvertMatrix(): Boolean {
            // 当Pose中任意一个属性为NaN,都不更新matrix
            if (scaleX.isNaN() || scaleY.isNaN()) {
                GLog.w(TAG) { "[canConvertMatrix] scales have NaN" }
                return false
            }
            if (angle.isNaN()) {
                GLog.w(TAG) { "[canConvertMatrix] angle is NaN" }
                return false
            }
            if (positionX.isNaN() || positionY.isNaN()) {
                GLog.w(TAG) { "[canConvertMatrix] positions have NaN" }
                return false
            }
            if (pivotX.isNaN() || pivotY.isNaN()) {
                GLog.w(TAG) { "[canConvertMatrix] pivot have NaN" }
                return false
            }
            return true
        }
    }

    /**
     * [PhotoPager]手势回调
     */
    @Suppress("LongParameterList")
    private class GalleryPhotoMotionCallback(
        pager: PhotoPager,
        private val sectionPage: ISectionPage<PhotoFragment, PhotoViewModel>,
        private val interactiveParams: InteractiveParams = InteractiveParams(),
        private val isSwipeInterrupted: () -> Boolean,
        private val onSwipeInterrupting: (Int, Boolean) -> Boolean,
        private val onNotifyPageClicked: (slot: Int) -> Unit,
        private val onNotifyPageDoubleClicked: (slot: Int) -> Unit,
        private val onNotifyPageLongPressed: (slot: Int) -> Unit,
        private val onNotifyPageAlphaChanged: (alpha: Float) -> Unit,
        private val onNotifyPageExit: (slot: Int, slotRect: Rect) -> Unit,
        private val onNotifyPageRotated: (slot: Int, degrees: Float) -> Unit,
        private val onNotifyPageSlideDown: (slideDownEvent: SlideDownEvent) -> Boolean,
        private val onNotifyPageSlideUp: (slideUpEvent: SlideUpEvent) -> Boolean,
        private val onNotifyScaleRotateBegin: (slot: Int) -> Unit
    ) : PhotoPagerMotionCallback(pager) {

        /**
         * 交互参数
         */
        private data class InteractiveParams(
            /**
             * 滑动交互Scale下限位置
             */
            val slideInteractiveScaleLower: Float = 0f,

            /**
             * 滑动交互Scale上限位置
             */
            val slideInteractiveScaleUpper: Float = 500f,

            /**
             * 滑动交互Alpha下限位置
             */
            val slideInteractiveAlphaLower: Float = 0f,

            /**
             * 滑动交互Alpha上限位置
             */
            val slideInteractiveAlphaUpper: Float = 300f,

            /**
             * 取消交互的反向滑动距离
             */
            val cancelSlideDistance: Float = 1f,

            /**
             * 输出缩放的值域下限
             */
            val outScaleRangeLower: Float = 1.05f, // scale: 105%

            /**
             * 输出缩放的值域上限
             */
            val outScaleRangeUpper: Float = 0.7f, // scale: 70%

            /**
             * 输出透明度的值域下限
             */
            val outAlphaRangeLower: Float = 1.0f, // alpha: 100%

            /**
             * 输出透明度的值域上限
             */
            val outAlphaRangeUpper: Float = 0.0f // alpha: 0%
        )

        private var targetPhotoSlot: PhotoSlot? = null
        private var targetSlot: Int = -1
        private var contentOriginScale: Float = 1f
        private var alphaAnimator: ValueAnimator? = null

        /**
         * 滑动状态值存储
         */
        private var slideState = SlideState(
            animEndOffset = getMaxSlideCalculateRange()
        )

        /**
         * 滑动隐藏/展示通知栏的位置
         */
        private val playAnimMinDistance: Float = HIDE_MENU_THRESHOLD

        /**
         * 是否状态栏处于隐藏
         */
        private var isStatusBarHide: Boolean = false

        override fun onSwipeInterrupting(slot: Int, shouldInterruptSwipe: Boolean): Boolean {
            return onSwipeInterrupting.invoke(slot, shouldInterruptSwipe)
        }

        override fun isSwipeInterrupted(): Boolean {
            return isSwipeInterrupted.invoke()
        }

        /**
         * 通知父页面重新显示缩略图的任务
         */
        private var notifyParentPageAppearFocusThumbnailJob: Job? = null

        /**
         * 记录在开始下拉交互时，界面是否处于沉浸式交互中，以便在取消下拉交互后恢复页面的交互方式
         */
        private var recordImmersionInteractiveState: Boolean = false

        /**
         * 大图退出跟手动画的上阻尼边界
         */
        private val exitDampingBoundary: Int by lazy {
            pager.context.resources.getDimensionPixelSize(R.dimen.photopage_exit_animation_damping_boundary)
        }

        /**
         * 是否确认可以关闭页面了。
         * 如果在下拉过程中有反向的移动，则本次操作无效
         */
        private var canFinishPage: Boolean = true

        private val onAlphaUpdatedListener = ValueAnimator.AnimatorUpdateListener {
            onNotifyPageAlphaChanged(it.animatedValue as Float)
        }

        override fun onSlotSingleTap(slot: Int): Boolean {
            GLog.d(TAG, "[onSlotSingleTap] Slot: $slot")
            GTrace.traceBegin("$TAG.onSlotSingleTap")
            val isCurrentSlotEvent = (pager.currentSelection == slot)
            if (isCurrentSlotEvent) {
                onNotifyPageClicked(slot)
            }
            GTrace.traceEnd()

            return isCurrentSlotEvent
        }

        override fun onSlotDoubleTap(slot: Int): Boolean {
            GLog.d(TAG, "[onSlotDoubleTap] Slot: $slot")
            GTrace.traceBegin("$TAG.onSlotDoubleTap")
            val isCurrentSlotEvent = (pager.currentSelection == slot)
            if (isCurrentSlotEvent) {
                onNotifyPageDoubleClicked(slot)
            }
            GTrace.traceEnd()

            // 埋点
            sectionPage.pageViewModel.track.trackGestureClick(value = GESTURE_DOUBLE_CLICK)

            return isCurrentSlotEvent
        }

        override fun onSlotLongTap(slot: Int) {
            GLog.d(TAG, "[onSlotLongTap] Slot: $slot")

            onNotifyPageLongPressed(slot)
        }

        override fun onScaleRotate(
            slot: Int,
            animControl: AnimationControl,
            scale: Float,
            scalePivotX: Float,
            scalePivotY: Float,
            angle: Float,
            rotatePivotX: Float,
            rotatePivotY: Float
        ): Boolean {
            return true
        }

        override fun onScaleRotateBegin(
            slot: Int,
            animControl: AnimationControl,
            scale: Float,
            scalePivotX: Float,
            scalePivotY: Float,
            angle: Float,
            rotatePivotX: Float,
            rotatePivotY: Float
        ): Boolean {
            GLog.d(TAG, "[onScaleRotateBegin] Slot:$slot")
            onNotifyScaleRotateBegin(slot)
            return super.onScaleRotateBegin(slot, animControl, scale, scalePivotX, scalePivotY, angle, rotatePivotX, rotatePivotY)
        }

        override fun onScaleRotateEnd(
            slot: Int,
            animControl: AnimationControl,
            scale: Float,
            scalePivotX: Float,
            scalePivotY: Float,
            angle: Float,
            rotatePivotX: Float,
            rotatePivotY: Float
        ): Boolean {
            val currentScale = animControl.finalPose.scaleX mix animControl.finalPose.scaleY
            if ((currentScale < animControl.onGetMinZoomScale())
                || (currentScale > animControl.onGetMaxZoomScale())
            ) {
                GLog.d(TAG, LogFlag.DL) {
                    "[onScaleRotateEnd] correct scale $currentScale, " +
                            "minScale = ${animControl.onGetMinZoomScale()}, " +
                            "maxScale = ${animControl.onGetMaxZoomScale()}"
                }
                sectionPage.pageContext?.let {
                    VibratorUtils.vibrateInSubThread(it, VibratorUtils.EffectType.VIBRATE_TYPE_CUSTOMIZED_WEAK)
                }
            }
            // 产品埋点 ： 双指操作
            sectionPage.pageViewModel.track.trackGestureClick(value = GESTURE_TWOFINGER)
            adjustDoubleTapZoomAfterPinch(animControl, slot, currentScale)
            return true
        }

        override fun onSlideDownBegin(slot: Int, animControl: AnimationControl): Boolean {
            // slot能够找到对应的PhotoSlot对象，再记录下来做接下来的操作，否则接下来的操作全部拒绝
            targetPhotoSlot = pager.findPhotoSlotBySlot(slot)?.also { photoSlot ->
                // 在下拉手势期间关闭滑动阻尼，使其能跟手滑动
                targetSlot = slot
                animControl.isScrollDampingEnabled = false
                animControl.isScaleDampingEnabled = false
                recordImmersionInteractiveState = sectionPage.pageViewModel.pageManagement.isUnderImmersionInteractive.value ?: false
                if (onNotifyPageSlideDown(SlideDownEvent.SlideDownBegin)) {
                    return true
                }
                isStatusBarHide = false
                canFinishPage = true
                changeAlphaAnimatorAndNotify(1f, SLIDE_ALPHA_ANIM_DURATION)

                // 通知底下的列表隐藏对应位置的缩略图
                notifyParentPageAppearFocusThumbnailJob?.cancel()
                notifyParentPageDisappearFocusThumbnail(slot)

                // 通知下拉开始。
                sectionPage.pageViewModel.pageManagement.notifySlideEvent(PhotoPageManagementViewModel.SlideEvent.SlideDownBegin)
            }

            return true
        }

        override fun onSlideDown(
            slot: Int,
            animControl: AnimationControl,
            eventStart: MotionEvent,
            eventEnd: MotionEvent,
            axisDistance: PhotoMotionCallback.AxisDistance
        ): Matrix {
            if (targetSlot != slot) return Matrix.emptyMatrix()

            val handled = onNotifyPageSlideDown.invoke(
                SlideDownEvent.SlidingDown(eventStart = eventStart, eventEnd = eventEnd)
            )
            if (handled) {
                animControl.scroll(0f, -axisDistance.distanceY)
                return Matrix.emptyMatrix()
            }

            targetPhotoSlot?.let {
                val isGreaterThanMinDistance = (eventEnd.y - eventStart.y) > playAnimMinDistance
                if (isStatusBarHide.not() && isGreaterThanMinDistance) { // 大于10才执行消失动画
                    // 如果当前未处于沉浸式，则进入沉浸式，在取消页面退出时再恢复
                    sectionPage.pageViewModel.pageManagement.changeImmersionInteractive(isImmersiveEnabled = true, needShowSystemBar = true)
                    isStatusBarHide = true
                } else if (isStatusBarHide && isGreaterThanMinDistance.not()) { // 小于10时会恢复
                    // 恢复页面的交互方式
                    sectionPage.pageViewModel.pageManagement.changeImmersionInteractive(
                        isImmersiveEnabled = recordImmersionInteractiveState
                    )
                    isStatusBarHide = false // 重置状态，再滑动一段后会再展示出来沉浸式
                }

                val offsetY = eventEnd.y - eventStart.y
                val displayHeight = pager.context.resources.displayMetrics.heightPixels
                // 实现大图下拉时的纵向阻尼
                var dy = if (offsetY < -exitDampingBoundary) {
                    calcRealOverScrollDist(axisDistance.distanceY.toInt(), offsetY.toInt() + exitDampingBoundary, displayHeight).toFloat()
                } else axisDistance.distanceY

                // 由于阻尼位移在反复运动过程中会产生误差，导致中心偏移，这里需要用图片实时中心和手指中心进行比对逐帧校正回正确位置
                if (offsetY > -exitDampingBoundary) {
                    val imageCenterY = (animControl as? GestureScrollerAnimControl)?.currentAabb?.let {
                        (it.top + it.bottom) / 2f
                    } ?: eventEnd.y
                    val correctY = clamp(abs(dy) * CORRECT_Y_RATIO_PER_FRAME, 1f, abs(eventEnd.y - imageCenterY))
                    if (eventEnd.y > imageCenterY) {
                        dy += correctY
                    } else if (eventEnd.y < imageCenterY) {
                        dy -= correctY
                    }
                }

                calcScaleAndAlphaByOffset(offsetY) { extraScale, alpha ->
                    // 1. PhotoSlot缩放效果
                    animControl.snapbackPose.let {
                        contentOriginScale = it.scaleX mix it.scaleY
                    }
                    val scale = contentOriginScale * extraScale
                    animControl.scroll(axisDistance.distanceX, dy)
                    animControl.scaleTo(scale, scale, eventEnd.x, eventEnd.y)

                    // 2. 页面背景半透明效果
                    changeAlphaAnimatorAndNotify(alpha, 0L)

                    // 3. 判断是否有取消下拉退出的操作出现（反向滑动）
                    canFinishPage = (axisDistance.distanceY > -interactiveParams.cancelSlideDistance)
                }
            }

            return Matrix.emptyMatrix()
        }

        override fun onSlideDownEnd(slot: Int, animControl: AnimationControl) {
            if (targetSlot != slot) return
            // 下拉手势结束后，复原滑动阻尼
            animControl.isScrollDampingEnabled = true
            animControl.isScaleDampingEnabled = true

            if (onNotifyPageSlideDown(SlideDownEvent.SlideDownEnd)) {
                return
            }

            clearSlideGlobal()

            if (canFinishPage) {
                pager.post {
                    // post到下一帧触发下拉退出大图，否则可能导致退出动画的起点闪烁。
                    onNotifyPageExit(slot, pager.currentSlotVisibleRect)
                }
                animControl.abortAnimation()

                // 通知下拉结束。
                sectionPage.pageViewModel.pageManagement.notifySlideEvent(PhotoPageManagementViewModel.SlideEvent.SlideDownEnd)

                // 产品埋点：
                sectionPage.pageViewModel.track.trackGestureClick(value = GESTURE_DOWN_FINISH)
            } else {
                animControl.scaleTo(contentOriginScale, contentOriginScale)
                animControl.snapback()
                changeAlphaAnimatorAndNotify(1f, SLIDE_ALPHA_ANIM_DURATION)

                // 恢复页面的交互方式
                sectionPage.pageViewModel.pageManagement.changeImmersionInteractive(
                    isImmersiveEnabled = recordImmersionInteractiveState
                )
                contentOriginScale = 1f

                // 通知下拉取消。
                sectionPage.pageViewModel.pageManagement.notifySlideEvent(PhotoPageManagementViewModel.SlideEvent.SlideDownCancel)
            }

            // 提交通知父页面重新显示缩略图的任务
            notifyParentPageAppearFocusThumbnailJob =
                sectionPage.pageLifecycle.coroutineScope.launch(Dispatchers.Main) {
                    GTrace.traceBegin("$TAG.notifyParentPageAppearFocusThumbnail")
                    delay(
                        sectionPage.pageViewModel.inputArguments.transition.value
                            ?.exitTransition?.thumbnailPositions?.duration ?: 0
                    )
                    if (isActive) {
                        notifyParentPageAppearFocusThumbnail(slot, canFinishPage)
                    }
                    GTrace.traceEnd()
                }
        }

        /**
         * 清空滑动的全局变量，防止下次开始复用
         */
        private fun clearSlideGlobal() {
            slideState = SlideState(
                animEndOffset = getMaxSlideCalculateRange(),
            )
        }

        override fun onSlideUpBegin(slot: Int, animControl: AnimationControl): Boolean {
            // slot能够找到对应的PhotoSlot对象，再记录下来做接下来的操作，否则接下来的操作全部拒绝
            targetPhotoSlot = pager.findPhotoSlotBySlot(slot)?.also { photoSlot ->
                // 在上拉手势期间关闭滑动阻尼，使其能跟手滑动
                targetSlot = slot

                if (onNotifyPageSlideUp(SlideUpEvent.SlideUpBegin)) {
                    return true
                }
            }
            return super.onSlideUpBegin(slot, animControl)
        }

        override fun onSlideUp(
            slot: Int,
            animControl: AnimationControl,
            eventStart: MotionEvent,
            eventEnd: MotionEvent,
            axisDistance: PhotoMotionCallback.AxisDistance
        ): Matrix {
            if (targetSlot != slot) return Matrix.emptyMatrix()
            val handled = onNotifyPageSlideUp.invoke(
                SlideUpEvent.SlidingUp(eventStart = eventStart, eventEnd = eventEnd)
            )
            if (handled) {
                animControl.scroll(0f, -axisDistance.distanceY)
                return Matrix.emptyMatrix()
            }
            return super.onSlideUp(slot, animControl, eventStart, eventEnd, axisDistance)
        }

        override fun onSlideUpEnd(slot: Int, animControl: AnimationControl) {
            if (targetSlot != slot) return

            if (onNotifyPageSlideUp(SlideUpEvent.SlideUpEnd)) {
                return
            }
            super.onSlideUpEnd(slot, animControl)
        }

        override fun onSlotRotated(slot: Int, degrees: Float) {
            /*
             * Marked by zhangjisong、CocosoonShu
             * 与 PhotoPager 里面的 onScaleRotateXXX 有些混淆，职责分离的并不是很清晰
             * onScaleRotateXXX 里面数据太多，都是裸数据，不太好
             * 需要改进 MotionCallback 的接口设计
             */
            onNotifyPageRotated(slot, degrees)
        }

        /**
         * 获取滑动触发alpha和scale变化的范围
         */
        private fun getMaxSlideCalculateRange(): Float {
            return ScreenUtils.displayScreenHeight / SCREEN_DIVIDE_THRESHOLD
        }

        /**
         * 分段函数，计算结果举例, 用于向下拖拽场景
         *   - 定义域: (-INF, 0]，值域: extraScale: 100%, alpha: 100%
         *   - 定义域: [0, 100]，值域: extraScale: 100% ~ 50%, alpha: 100% ~ 0%
         *   - 定义域: (100, INF)，值域: extraScale: 50%, alpha: 0%
         */
        private fun calcScaleAndAlphaByOffset(offset: Float, block: (extraScale: Float, alpha: Float) -> Unit) {
            // 1. 检测方向变化
            val isSlidingDown = offset > slideState.lastOffset
            val isDirectionChanged = isSlidingDown != slideState.isLastSlidingDown

            // 2. 方向变化时重置动画阶段
            if (isDirectionChanged || (offset * slideState.lastOffset) <= 0) {
                // 保存变化点的状态作为新起点
                slideState.animStartOffset = offset.coerceIn(0f, getMaxSlideCalculateRange())
                slideState.animEndOffset = if (isSlidingDown) getMaxSlideCalculateRange() else 0f

                slideState.animUpStartOffset = offset.coerceIn(-getMaxSlideCalculateRange(), 0f)
                slideState.animUpEndOffset = if (isSlidingDown) 0f else -getMaxSlideCalculateRange()

                slideState.animStartAlpha = slideState.currentAlpha // 使用当前值作为新起点
                slideState.animStartScale = slideState.currentScale
            }

            // 3. 计算当前动画段的进度
            val progress = if (offset >= 0) {
                val totalDistance = slideState.animEndOffset - slideState.animStartOffset
                val currentDistance = offset - slideState.animStartOffset
                val rawProgress = if (totalDistance != 0f) {
                    currentDistance / totalDistance
                } else 0f
                rawProgress.coerceIn(0f, 1f)
            } else {
                val totalDistance = slideState.animUpEndOffset - slideState.animUpStartOffset
                val currentDistance = offset - slideState.animUpStartOffset
                val rawProgress = if (totalDistance != 0f) {
                    currentDistance / totalDistance
                } else 0f
                rawProgress.coerceIn(0f, 1f)
            }

            // 4. 应用插值器
            val alphaInterpolation = EXIT_ALPHA_INTERPOLATOR.getInterpolation(progress)
            val scaleInterpolation = EXIT_SCALE_INTERPOLATOR.getInterpolation(progress)

            // 5. 确定目标值
            val targetAlpha = if (isSlidingDown) interactiveParams.outAlphaRangeUpper else interactiveParams.outAlphaRangeLower
            val targetScale = when {
                (isSlidingDown && offset >= 0) -> interactiveParams.outScaleRangeUpper
                (!isSlidingDown && offset < 0) -> interactiveParams.outScaleRangeLower
                else -> 1f
            }

            // 6. 计算当前值
            val currentAlpha = if (offset < 0) 1f else (slideState.animStartAlpha + (targetAlpha - slideState.animStartAlpha) * alphaInterpolation)
            val currentScale = slideState.animStartScale + (targetScale - slideState.animStartScale) * scaleInterpolation

            // 7. 数值安全限制
            slideState.currentAlpha = currentAlpha.coerceIn(interactiveParams.outAlphaRangeUpper, interactiveParams.outAlphaRangeLower)
            slideState.currentScale = currentScale.coerceIn(interactiveParams.outScaleRangeUpper, interactiveParams.outScaleRangeLower)

            block(slideState.currentScale, slideState.currentAlpha)

            slideState.isLastSlidingDown = isSlidingDown
            slideState.lastOffset = offset
        }

        private fun changeAlphaAnimatorAndNotify(targetAlpha: Float, duration: Long) {
            alphaAnimator?.pause()
            alphaAnimator?.removeAllUpdateListeners()

            val currentAlpha = alphaAnimator?.animatedValue as? Float ?: 1f
            alphaAnimator = ValueAnimator.ofFloat(currentAlpha, targetAlpha).also {
                it.addUpdateListener(onAlphaUpdatedListener)
                it.duration = duration
                it.start()
            }
        }

        private fun notifyParentPageDisappearFocusThumbnail(slot: Int) {
            sectionPage.pageViewModel.inputArguments.transition.value?.let { transition ->
                getMainDM().onPullDownStart(
                    transition.positionControllerKey,
                    slot
                )
            }
        }

        private fun notifyParentPageAppearFocusThumbnail(slot: Int, canFinishPage: Boolean) {
            sectionPage.pageViewModel.inputArguments.transition.value?.let { transition ->
                getMainDM().onPullDownFinish(
                    transition.positionControllerKey,
                    slot,
                    complete = canFinishPage
                )
            }
        }

        /**
         * 用于双指放大图片后，修改双击放大图片的倍率
         * 只要双指放大图片倍率后，就要把这个倍率直接设置给doubleTapZoomInScale
         * @param animControl 动效控制
         * @param slot 当前页面位置值
         * @param currentScale 当时双指放大的倍率大小
         */
        private fun adjustDoubleTapZoomAfterPinch(animControl: AnimationControl, slot: Int, currentScale: Float) {
            val minZoomScale = animControl.onGetMinZoomScale()
            if (currentScale <= minZoomScale) return

            val maxZoomScale = animControl.onGetMaxZoomScale()
            val zoomScale = minOf(currentScale, maxZoomScale)
            pager.findPhotoSlotBySlot(slot)?.animationPropertiesProvider?.doubleTapZoomInScale = { zoomScale }
        }
    }

    private inner class PhotoListUpdateCallback : ListUpdateCallback {
        override fun onInserted(start: Int, count: Int) {
            GLog.d(TAG) { "[DiffCallback.onInserted] start=$start, count=$count" }
            fireSlotBatchInsertedEvent(start, count)
            photoPageAdapter.notifySlotBatchInserted(start, count)
        }

        override fun onRemoved(start: Int, count: Int) {
            GLog.d(TAG) { "[DiffCallback.onRemoved] start=$start, count=$count" }
            fireSlotBatchRemovedEvent(start, count)
            photoPageAdapter.notifySlotBatchRemoved(start, count)
        }

        override fun onMoved(from: Int, to: Int) {
            GLog.d(TAG) { "[DiffCallback.onMoved] from=$from, to=$to" }
            fireSlotBatchMovedEvent(from, to)
            photoPageAdapter.notifySlotMoved(from, to)
        }

        override fun onChanged(start: Int, count: Int) {
            GLog.d(TAG) { "[DiffCallback.onChanged] start=$start, count=$count" }
            fireSlotBatchInvalidateEvent(start, count)
            photoPageAdapter.notifySlotInvalidate(start, count)
        }
    }

    inner class PhotoPagerPageTransitionAdapter : PageTransitionAdapter() {
        override fun onEnterTransitionStart() {
            // 进入动画开始的时候，不显示photoPager,添加photoPager.post，用以解决5286572(浮窗情况下，大图点击分享，大图闪烁)的bug
            photoPager.post { photoPager.alpha = MIN_VIEW_ALPHA }
        }

        override fun onEnterTransitionEnd() {
            // 进入动画结束的时候，显示photoPager
            photoPager.alpha = MAX_VIEW_ALPHA
        }

        override fun onEnterTransitionCancel() {
            photoPager.alpha = MAX_VIEW_ALPHA
        }

        override fun onExitTransitionStart() {
            // 退出动画开始的时候，不显示photoPager
            photoPager.alpha = MIN_VIEW_ALPHA
        }

        override fun onExitTransitionEnd() {
            // 退出动画结束的时候，显示photoPager
            photoPager.alpha = MAX_VIEW_ALPHA
        }

        override fun onExitTransitionCancel() {
            photoPager.alpha = MAX_VIEW_ALPHA
        }
    }

    /**
     * 大图slot变化的事件监听
     */
    interface ISlotBatchEventCallback {

        /**
         * slot 整体刷新时触发
         */
        fun slotBatchSetChanged()

        /**
         * slot新增时触发
         */
        fun slotBatchInserted(position: Int, count: Int)

        /**
         * slot移除时触发
         */
        fun slotBatchRemoved(position: Int, count: Int)

        /**
         * slot移动时触发
         */
        fun slotBatchMoved(fromPosition: Int, toPosition: Int)

        /**
         * slot刷新时触发
         */
        fun slotBatchInvalidate(position: Int, count: Int)

        /**
         * 数据的Diff事件，可能会分几组分发，当本次产生的所有Diff事件分发完毕后，通知该事件。
         */
        fun slotBatchEventFinished(): Unit = Unit
    }


    /**
     * PhotoSlotLoadingPoxy 当删除/创建/时, 通知到外部, 便于外部及时处理相应逻辑
     */
    interface IOverlayEventCallback {
        fun onCreated(slot: Int, photoSlot: PhotoSlot?)

        fun onDestroyed(slot: Int, photoSlot: PhotoSlot?)
    }

    /**
     * talkback模式下，无法选中photoPager，无法聚焦于photoPager
     * 初始化AccessibilityTouchHelper辅助类，为photoPager添加虚拟视图
     */
    private fun initAccessibilityTouchHelper() {
        val touchHelper = AccessibilityTouchHelper(photoPager)
        //为photoPager添加无障碍代理
        ViewCompat.setAccessibilityDelegate(photoPager, touchHelper)
        //使其OnHoverListener指向AccessibilityTouchHelper辅助类
        photoPager.setOnHoverListener { v, event ->
            return@setOnHoverListener touchHelper.dispatchHoverEvent(event)
        }
    }

    private inner class AccessibilityTouchHelper(host: View) : ExploreByTouchHelper(host) {
        override fun getVirtualViewAt(x: Float, y: Float): Int = 0

        override fun getVisibleVirtualViews(virtualViewIds: MutableList<Int>?) {
            virtualViewIds?.add(0)
        }

        /**
         * 调用此方法填充虚拟试图的无障碍nodeindo属性
         */
        override fun onPopulateNodeForVirtualView(virtualViewId: Int, node: AccessibilityNodeInfoCompat) {
            node.className = PhotoPagerSection::class.java.name
            if (viewModel.dataLoading.focusItemViewData?.isVideo == true) {
                node.contentDescription = sectionPage.pageContext?.getString(com.oplus.gallery.basebiz.R.string.model_title_mediatype_video)
            } else {
                node.contentDescription = sectionPage.pageContext?.getString(com.oplus.gallery.basebiz.R.string.base_media_type_image)
            }
            node.setBoundsInParent(photoPager.currentSlotVisibleRect)
            node.addAction(AccessibilityNodeInfoCompat.ACTION_CLICK)
        }

        override fun onPerformActionForVirtualView(virtualViewId: Int, action: Int, arguments: Bundle?): Boolean {
            //双击photoPager进入沉浸浏览时，进行语音播报
            if ((AccessibilityNodeInfoCompat.ACTION_CLICK == action)
                && (sectionPage.pageViewModel.pageManagement.isUnderImmersionInteractive.value == false)
            ) {
                photoPager.announceForAccessibility(sectionPage.pageContext?.getString(R.string.photopage_talkback_immersive_browse))
            }
            return false
        }
    }

    /**
     * 触发场景类型
     */
    internal enum class TriggerSceneType {
        /**
         * ContentRenderer状态变化场景
         */
        CONTENT_RENDERER_STATE_CHANGE,

        /**
         * 进大图动画状态变化场景
         */
        PAGE_TRANSITION_STATE_CHANGE,

        /**
         * 左右滑动状态变化场景
         */
        SCROLL_STATE_CHANGE
    }

    /**
     * 存储下滑动画变化时的参数，用于计算曲线位置（曲线会在方向变化时重置）
     */
    private data class SlideState(
        // 滑动方向相关
        var isLastSlidingDown: Boolean = false,

        // 位移相关
        var lastOffset: Float = 0f,

        var animStartOffset: Float = 1f,
        var animEndOffset: Float = 0f,

        var animUpStartOffset: Float = 1f,
        var animUpEndOffset: Float = 0f,

        // 视觉效果相关
        var currentAlpha: Float = 1f,
        var currentScale: Float = 1f,

        // 动画起始状态
        var animStartAlpha: Float = 1f,
        var animStartScale: Float = 1f
    )

    companion object {
        private const val TAG = "PhotoPagerSection"

        private const val LOCATION_ARRAY_SIZE = 2
        private const val NO_SELECTION = -1
        private const val SLIDE_ALPHA_ANIM_DURATION = 300L
        private const val MIN_VIEW_ALPHA = 0f
        private const val MAX_VIEW_ALPHA = 1f
        private val EXIT_ALPHA_INTERPOLATOR = PathInterpolator(0.3f, 0f, 1f, 1f)
        private val EXIT_SCALE_INTERPOLATOR = PathInterpolator(0f, 0f, 0.4f, 1f)
        private const val CONTROL_SLOT_SIZE = 7
        private const val CONTROL_SLOT_SIZE_IN_LOW_MEM = 5
        private const val SCREEN_DIVIDE_THRESHOLD = 2f
        private const val HIDE_MENU_THRESHOLD = 10f

        private const val EXIT_MODE_NONE = 0  // 无需延时退出
        private const val EXIT_MODE_CURRENT_FRAGMENT = 1  // 需要以 exitCurrentFragment 方式延时退出
        private const val EXIT_MODE_EXECUTOR = 2  // 需要以 exitExecutor.exit 方式延时退出

        private const val OFFSCREEN_PAGE_LIMIT = 2  // 每一边的离屏渲染数

        /**
         * 提频时长
         */
        private const val UAH_EVENT_TOUCH_BOOST_TIMEOUT = 500

        /**
         * 默认计算 focus 是否放大过的精度
         */
        private const val DEFAULT_SCALE_THRESHOLD = 0.00001

        /**
         * 每帧y方向位移校正比例
         */
        private const val CORRECT_Y_RATIO_PER_FRAME = 0.1f
    }
}