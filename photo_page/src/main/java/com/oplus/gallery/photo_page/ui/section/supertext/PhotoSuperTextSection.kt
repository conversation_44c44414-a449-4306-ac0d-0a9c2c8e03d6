/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - PhotoSuperTextSection.kt
 * Description: 大图页面 - 超级文本内容展示页面切片
 * Version: 1.0
 * Date: 2022/5/6
 * Author: <PERSON><PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                   <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * Xiewujie@Apps.Gallery3D 2022/5/6     1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 **************************************************************************************************/
package com.oplus.gallery.photo_page.ui.section.supertext

import android.graphics.Matrix
import android.view.MotionEvent
import android.view.View
import com.oplus.gallery.basebiz.widget.SuperTextSelectionLayout
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.foundation.arch.sectionpage.ISectionPage
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.photo_page.ui.AbstractPhotoSection
import com.oplus.gallery.photo_page.ui.PhotoFragment
import com.oplus.gallery.photo_page.ui.section.photopagersection.PhotoPagerSection
import com.oplus.gallery.photo_page.ui.section.photopagersection.PhotoPagerSection.SlotTransformPose
import com.oplus.gallery.photo_page.ui.section.photopagersection.SlideDownEvent
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.funcRecommend.FuncDetectInfo
import com.oplus.gallery.photo_page.viewmodel.funcRecommend.FuncItem
import com.oplus.gallery.photo_page.viewmodel.funcRecommend.Status
import com.oplus.gallery.photopager.viewpager.widget.ViewPager
import com.oplus.gallery.standard_lib.app.UI
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 超级文本的切片,负责超级文本2.0UI的显示
 * @param sectionPage 被此页面切片依附的主页面
 */
internal class PhotoSuperTextSection(
    sectionPage: ISectionPage<PhotoFragment, PhotoViewModel>,
) : AbstractPhotoSection(sectionPage) {

    private val pagerSection: PhotoPagerSection? get() = sectionPage.pageInstance.requireSection()

    private var selectionLayout: SuperTextSelectionLayout? = null

    /**
     * 超级文本的overlay是需要长按后才能添加，现有的overlay管理方式不适用这个，所以单独放在section管理
     */
    private var superTextOverlay: SuperTextOverlay? = null

    private val slotTouchEventListener = { _: Int, event: MotionEvent ->
        /**
         * 1、跨屏互联、浮窗的图片拖拽分享优先级高于选文事件，处于这三种模式下禁用选文;
         * 2、当前正在展示抠图结果页长按也不响应选文
         * 3、处于详情模式（大图显示不全）长按也不响应选文
         */
        if (ApiDmManager.getSynergyDM().isSynergyEnable()
            || isSupportDrag()
            || (viewModel.lnS.isShowingLnSView.value == true)
            || (viewModel.details.isInDetailsMode.value == true)
        ) {
            false
        } else {
            selectionLayout?.let {
                if (event.actionMasked == MotionEvent.ACTION_DOWN) {
                    // ACTION_DOWN时，如果触碰在超级文本上，则通知出去，以便其他业务判断是否需要继续操作
                    val isTouchDownOnText = it.isTouchOnText(event)
                    viewModel.superText.notifyTouchDownOnSuperText(isTouchDownOnText)
                }
                it.processSelectTouchEvent(event)
            } ?: false
        }
    }

    private val currentMatrix = Matrix()

    private val pageChangeCallback = object : ViewPager.OnPageChangeCallback() {
        override fun onPageScrollStateChanged(state: Int) {
            super.onPageScrollStateChanged(state)
            if (state == ViewPager.SCROLL_STATE_IDLE) {
                // 图片静止时重新显示把手
                selectionLayout?.showHandlerIfNeed()
            }
        }
    }

    private val slideDownChangeListener = { slideDownEvent: SlideDownEvent ->
        if (slideDownEvent == SlideDownEvent.SlideDownBegin) {
            selectionLayout?.exitSelectText()
        }
        // 超级文本只需要监听有这个事件即可，并不需要拦截，因此此处返回 false 即可
        false
    }

    /**
     * 不拦截大图触摸事件的监听
     * 只接受事件，不拦截事件
     */
    private val uninterruptedSlotTouchListener = { _: Int, event: MotionEvent ->
        if ((event.action == MotionEvent.ACTION_UP) || (event.action == MotionEvent.ACTION_CANCEL)) {
            viewModel.superText.notifyTouchDownOnSuperText(false)
        }
    }

    private val focusPoseChangeListener = { pose: SlotTransformPose ->
        updateSelectionMatrix(pose)
    }

    /**
     * 文本识别
     */
    private var textOcrComponent: TextOcrComponent? = null

    override fun onViewCreated(view: View) {
        super.onViewCreated(view)
        subscribeViewModelLiveData()
        // 注册焦点图片姿态变换监听，因为ocr结束的时候手势监听可能不回调了，ocr开始之前就要监听姿态变换更新
        pagerSection?.registerFocusSlotTransformUpdatedListener(focusPoseChangeListener) ?: let {
            GLog.e(TAG) { "[onViewCreated] why is pagerSection null, no config? CHECK CODE!" }
        }
        pagerSection?.registerUninterruptedTouchEventListener(uninterruptedSlotTouchListener)
        textOcrComponent = TextOcrComponent(sectionPage).apply {
            initComponent(view)
        }
    }

    override fun onAppUiStateChanged(config: AppUiResponder.AppUiConfig) {
        super.onAppUiStateChanged(config)
        textOcrComponent?.onAppUiStateChanged(config)
    }

    override fun onViewDestroy() {
        super.onViewDestroy()
        pagerSection?.apply {
            unregisterOnPageChangeCallback(pageChangeCallback)
            unregisterFocusSlotTransformUpdatedListener(focusPoseChangeListener)
            unregisterUninterruptedTouchEventListener(uninterruptedSlotTouchListener)
        }
        unbindListener()
        textOcrComponent?.releaseComponent()
    }

    private fun subscribeViewModelLiveData() {
        viewModel.photoFunc.funcDetectInfo.observe(this) {
            refreshSelectionState(it)
        }
    }

    private fun refreshSelectionState(funcDetectInfo: FuncDetectInfo) {
        when (funcDetectInfo.status) {
            Status.START -> {
                /**
                 *  OnPageChangeCallback的onPageSelected会去更新焦点进而走到这里，
                 *  不能在遍历的时候立刻移除其他OnPageChangeCallback元素，所以放到下一帧移除
                 */
                viewModel.launch(Dispatchers.UI) {
                    pagerSection?.unregisterOnPageChangeCallback(pageChangeCallback)
                }
                // 焦点变化时注销监听器，解绑overlay
                unbindListener()
                detachSuperTextOverlay()
            }
            Status.END -> {
                val ocrResult = funcDetectInfo.funcItems.firstNotNullOfOrNull { if (it is FuncItem.LongPressTextOcr) it.data else null } ?: let {
                    GLog.w(TAG, LogFlag.DL, "refreshSelectionState ocrResult is null")
                    return
                }
                // ocr扫描结束，如果有文字，就注册触摸事件，接受长按选文事件
                if (ocrResult.containsText()) {
                    GLog.d(TAG, LogFlag.DL, "refreshSelectionState, slot has text, so bind selectionLayout")
                    ensureSelectionLayout { selectionLayout ->
                        selectionLayout.imageMatrix = currentMatrix
                        selectionLayout.setData(ocrResult.aiUnitOCRResult?.ocrResult)
                    }
                    // 向PhotoPager注册触摸事件
                    pagerSection?.registerSlotTouchEventListener(slotTouchEventListener)
                } else {
                    GLog.d(TAG, LogFlag.DL, "refreshSelectionState, slot has no text, ")
                }
            }
        }
    }

    private fun ensureSelectionLayout(block: (SuperTextSelectionLayout) -> Unit) {
        selectionLayout?.let {
            block(it)
            return
        }
        val context = sectionPage.pageContext ?: let {
            GLog.e(TAG, "ensureSelectionLayout, can't create selectionLayout because pageContext is null")
            return
        }
        viewModel.launch(Dispatchers.Default) {
            SuperTextSelectionLayout(context).also {
                selectionLayout = it
                withContext(Dispatchers.UI) {
                    GTrace.trace("$TAG.ensureSelectionLayout") {
                        pagerSection?.sectionPage?.pageInstance?.view?.let { view ->
                            it.setMagnifierTargetView(view, true)
                        }
                        // 产品逻辑，大图不会启用双击文字选文逻辑，因为会影响双击放大
                        it.isDoubleClickEnable = false
                        bindSelectionLayoutListener()
                        block(it)
                    }
                }
            }
        }
    }

    private fun bindSelectionLayoutListener() {
        pagerSection?.apply {
            // 滑动选文时要禁止viewPager的滑动
            selectionLayout?.interceptParentMotionCallback = { intercept ->
                setUserInputEnable(intercept.not(), "super text set")
            }
            // 单击操作无法消费手势，需要外部拦截
            selectionLayout?.negateNextImmersionChangeCallback = { intercept ->
                negateNextImmersionChange(intercept)
            }
            selectionLayout?.stateChangedCallback = { isSelected ->
                viewModel.superText.notifySuperTextSelectState(isSelected)
                if (isSelected) {
                    viewModel.pageManagement.changeImmersionInteractive(isSelected)
                    attachSuperTextOverlayIfNeed()
                }
            }
            // 下拉时要隐藏选文浮层
            registerSlotSlideDownListener(slideDownChangeListener)
            registerOnPageChangeCallback(pageChangeCallback)
        }
    }

    private fun unbindListener() {
        unbindPagerSectionListener()
        unbindSelectionLayoutListener()
    }

    private fun detachSuperTextOverlay() {
        superTextOverlay?.let {
            pagerSection?.findPhotoSlotBySlot(currentFocus)?.detachSlotOverlay(it)
            superTextOverlay = null
        }
    }

    private fun unbindSelectionLayoutListener() {
        selectionLayout?.apply {
            exitSelectText()
            stateChangedCallback = null
            interceptParentMotionCallback = null
            negateNextImmersionChangeCallback = null
            selectionLayout = null
        }
    }

    private fun unbindPagerSectionListener() {
        pagerSection?.apply {
            unregisterSlotTouchEventListener(slotTouchEventListener)
            unregisterSlotSlideDownListener(slideDownChangeListener)
        }
    }

    private fun attachSuperTextOverlayIfNeed() {
        selectionLayout?.let { selectionLayout ->
            val photoSlot = pagerSection?.findPhotoSlotBySlot(currentFocus) ?: let {
                GLog.w(TAG, "attachSuperTextOverlayIfNeed, find photoSlot null")
                return
            }
            if (superTextOverlay != null) {
                GLog.d(TAG, "attachSuperTextOverlayIfNeed, superTextOverlay is created, so return")
                return
            }
            SuperTextOverlay(selectionLayout).also {
                photoSlot.attachSlotOverlay(it)
                superTextOverlay = it
            }
        } ?: let {
            GLog.w(TAG, "attachSuperTextOverlayIfNeed, can't bind superTextSelectionView because selectionLayout is null")
        }
    }

    private fun updateSelectionMatrix(pose: SlotTransformPose) {
        pose.apply {
            // 当Pose中任意一个属性为NaN,都不更新matrix
            if (scaleX.isNaN() || scaleY.isNaN()) {
                GLog.w(TAG, "updateSelectionMatrix, scales have NaN")
                return
            }
            if (angle.isNaN()) {
                GLog.w(TAG, "updateSelectionMatrix, angle is NaN")
                return
            }
            if (positionX.isNaN() || positionY.isNaN()) {
                GLog.w(TAG, "updateSelectionMatrix, positions have NaN")
                return
            }
            if (pivotX.isNaN() || pivotY.isNaN()) {
                GLog.w(TAG, "updateSelectionMatrix, pivot have NaN")
                return
            }
        }
        currentMatrix.apply {
            reset()
            postScale(pose.scaleX, pose.scaleY, pose.pivotX, pose.pivotY)
            postRotate(pose.angle, pose.pivotX, pose.pivotY)
            postTranslate(pose.positionX, pose.positionY)
            selectionLayout?.imageMatrix = this
        }
    }

    companion object {
        private const val TAG = "PhotoSuperTextSection"
    }
}