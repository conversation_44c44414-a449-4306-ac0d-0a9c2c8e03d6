/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoWidgetManagementViewModel.kt
 ** Description : 大图管理桌面卡片相关信息的ViewModel
 ** Version     : 1.0
 ** Date        : 2022/05/26
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>           2022/05/26  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.widgetmanagement

import android.content.res.Resources
import android.text.TextUtils
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import com.oplus.gallery.basebiz.constants.IntentConstant
import com.oplus.gallery.basebiz.constants.IntentConstant.WidgetConstant.KEY_TEXT_ID_OF_REMOVE_FROM_LIST_MENU
import com.oplus.gallery.basebiz.constants.IntentConstant.WidgetConstant.KEY_TOAST_TEXT_ID_AFTER_ALL_REMOVED
import com.oplus.gallery.business_lib.api.ApiDmManager.getWidgetDM
import com.oplus.gallery.business_lib.api.IWidgetDM
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.foundation.database.store.GalleryStore.WidgetSetColumns.MODE_DEFAULT
import com.oplus.gallery.foundation.util.collections.ExtraMap
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.viewmodel.PhotoSubViewModel
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.dataloading.DiffedPhotoViewDataSet
import com.oplus.gallery.photo_page.viewmodel.dataloading.totalCount
import com.oplus.gallery.photo_page.viewmodel.inputarguments.PhotoInputArgumentsViewModel
import com.oplus.gallery.standard_lib.ui.util.ToastUtil

/**
 * 大图管理桌面卡片相关信息的ViewModel。
 *
 * 桌面卡片目前必须在图集reload之后才能获取相关信息，不能在进入大图时立即获取参数，
 * 因此在这里单独抽出一个VM来管理相关操作。
 *
 * Marked : 后续桌面卡片优化后，若无需reload之后才获取信息，可移除此ViewModel，
 * 相关信息应集合到 [PhotoInputArgumentsViewModel] 中。
 */
internal class PhotoWidgetManagementViewModel(
    override val pageViewModel: PhotoViewModel
) : PhotoSubViewModel(pageViewModel) {

    //////////// 以下为对外输出状态 ///////////

    /**
     * 从桌面卡片进入大图页的卡片相关状态。
     */
    val widgetState: LiveData<PhotoFromWidgetState> get() = _widgetState
    private val _widgetState = MutableLiveData<PhotoFromWidgetState>()

    /**
     * 从桌面卡片获取的原始卡片信息。
     * - 内部信息请参照 [IWidgetDM.getWidgetInfo]
     */
    val originalWidgetInfo: LiveData<ExtraMap> get() = _originalWidgetInfo
    private val _originalWidgetInfo = MutableLiveData<ExtraMap>()

    private var hasShowToast: Boolean = false

    private val photoViewDataSetChangeObserver = Observer<DiffedPhotoViewDataSet> {
        GTrace.trace({ "$TAG.diffedViewDataSet" }) {
            checkShouldShowToast()
            notifyModelLoadingFinished()
        }
    }

    /////////// 以下为对外公开方法 /////////////

    /**
     * 通知Model加载完毕。
     * - 桌面卡片要在model加载完成后获取卡片信息。
     */
    private fun notifyModelLoadingFinished() {
        ensureWidgetInfo()
    }

    //////////// 以下为内部方法 /////////////

    override fun onCreate() {
        super.onCreate()

        pageViewModel.dataLoading.diffedPhotoViewDataSet.observeForever(photoViewDataSetChangeObserver)
    }

    override fun onStop() {
        super.onStop()
        checkShouldShowToast()
    }

    override fun onDestroy() {
        super.onDestroy()

        pageViewModel.dataLoading.diffedPhotoViewDataSet.removeObserver(photoViewDataSetChangeObserver)
    }

    /**
     * 若[widgetInfo]还未初始化，初始化[widgetInfo]
     */
    private fun ensureWidgetInfo() {
        if (_originalWidgetInfo.value != null) {
            return
        }

        pageViewModel.inputArguments.dataSource.value?.setPath?.let { setPath ->
            getWidgetDM().getWidgetInfo(pageViewModel.context, Path.fromString(setPath))
        }?.let { widgetInfo ->
            _originalWidgetInfo.postValue(widgetInfo)
            PhotoFromWidgetState(
                isFromWidget = true,
                widgetCode = widgetInfo.getString(IntentConstant.WidgetConstant.KEY_WIDGET_CODE, EMPTY_STRING),
                widgetMode = widgetInfo.getInt(IntentConstant.WidgetConstant.KEY_WIDGET_MODE, MODE_DEFAULT),
                trackModeName = widgetInfo.getString(IntentConstant.WidgetConstant.KEY_WIDGET_TRACK_MODE_NAME, EMPTY_STRING),
                widgetDisplayListId = widgetInfo.getString(IntentConstant.WidgetConstant.KEY_DISPLAY_LIST_ID, EMPTY_STRING),
                removeFromWidgetListResId = widgetInfo.getInt(KEY_TEXT_ID_OF_REMOVE_FROM_LIST_MENU, Resources.ID_NULL),
                allPhotoRemovedFromWidgetListResId = widgetInfo.getInt(KEY_TOAST_TEXT_ID_AFTER_ALL_REMOVED, Resources.ID_NULL)
            ).let { state ->
                _widgetState.postValue(state)
            }
        } ?: let {
            _originalWidgetInfo.postValue(ExtraMap())
            _widgetState.postValue(PhotoFromWidgetState.NotFromWidget)
        }
    }

    /**
     * 检查是否需要弹出桌面卡片的 Toast。
     * - 来着桌面卡片
     * - 图片数目为 0
     */
    private fun checkShouldShowToast() {
        pageViewModel.menuControl.latestMenuItemClickId.value?.let { action ->
            widgetState.value?.let { state ->
                val isTargetAction = (action == R.id.action_custom_remove_from_widget_list
                        || action == R.id.action_recommended_remove_from_widget_list)
                val isDataEmpty = pageViewModel.dataLoading.totalCount == 0
                if (state.isFromWidget && isDataEmpty && isTargetAction && hasShowToast.not()) {
                    hasShowToast = true
                    showEmptyWidgetAlbumToast(state.allPhotoRemovedFromWidgetListResId)
                }
            }
        }
    }

    /**
     * Toast提示桌面卡片图集列表中的图片全部被删除。
     * @param tipTextResId 提示文本的资源id
     */
    private fun showEmptyWidgetAlbumToast(tipTextResId: Int) {
        if (tipTextResId <= 0) return
        pageViewModel.context.getString(tipTextResId).let {
            if (!TextUtils.isEmpty(it)) {
                ToastUtil.showShortToast(it)
            }
        }
    }

    /**
     * 从桌面卡片进入大图是携带的状态
     */
    internal data class PhotoFromWidgetState(
        /**
         * 当前大图页是否来自桌面卡片
         */
        val isFromWidget: Boolean = false,

        /**
         * 桌面卡片的widgetCode。
         * 桌面可以同时存在多个桌面卡片，widgetCode作为单个卡片的标识符。
         */
        val widgetCode: String = EMPTY_STRING,

        /**
         * 桌面卡片当前列表模式。
         * - 默认 [MODE_DEFAULT]。
         * - 自选列表 [MODE_CUSTOM]。
         * - 精选列表 [MODE_RECOMMENDED]。
         */
        val widgetMode: Int = MODE_DEFAULT,

        /**
         * 桌面卡片模式相关埋点使用的字段
         */
        val trackModeName: String = EMPTY_STRING,

        /**
         * 桌面卡片正在显示的列表的ID。
         *
         * 目前使用场景：从桌面卡片移除图片时使用。
         */
        val widgetDisplayListId: String = EMPTY_STRING,

        /**
         * "移出 ## 列表" 文本的资源id。
         * - 从桌面卡片进入大图时，大图菜单中需要显示该菜单项。
         * - 不同情况下会有不同的文本，因此需要桌面卡片给出资源id。
         */
        val removeFromWidgetListResId: Int = Resources.ID_NULL,

        /**
         * "已移出所有 ## 图片” 文本资源id。
         * - 桌面卡片图集中所有图片被移除时，会 Toast 提示用户。
         * - 不同情况下会有不同的文本，因此需要桌面卡片给出资源id。
         */
        val allPhotoRemovedFromWidgetListResId: Int = Resources.ID_NULL
    ) {
        companion object {
            /**
             * 不是来自于桌面卡片
             */
            val NotFromWidget = PhotoFromWidgetState()
        }
    }

    companion object {
        private const val TAG = "PhotoWidgetManagementViewModel"
    }
}