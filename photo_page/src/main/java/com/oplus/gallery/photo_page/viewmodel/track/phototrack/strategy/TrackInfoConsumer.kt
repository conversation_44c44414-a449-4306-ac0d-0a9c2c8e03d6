/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : TrackInfoConsumer.kt
 ** Description : 埋点记录的消费者
 ** Version     : 1.0
 ** Date        : 2022/06/23
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2022/06/23  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.track.phototrack.strategy

import android.content.Context

/**
 * 埋点记录的消费者
 */
internal class TrackInfoConsumer(private val context: Context) {

    /**
     * 消费一次埋点信息。
     * @param trackInfo 埋点信息
     * @param onMergeReport 将两次报告进行合并。
     * @param onTrack 对埋点记录进行埋点，并返回埋点后希望保存的埋点信息，若返回空，不会保存并且会清除已有记录。
     */
    @Suppress("UNCHECKED_CAST")
    fun <Report : Any> consume(
        trackInfo: TrackInfo<Report>,
        onMergeReport: (old: Report, new: Report) -> Report,
        onTrack: (TrackInfo<Report>) -> TrackInfo<Report>?
    ) = trackInfo.let { newInfo -> // 将当前埋点数据当做新数据使用
        // 1. 创建缓存操作器
        createCacheOperator(
            trackInfo::class.java as Class<TrackInfo<Report>>
        ).let { trackOperator ->
            // 2. 尝试获取缓存的旧数据
            trackOperator.get(newInfo.trackKey)?.let { oldInfo ->
                // 3. 判断缓存数据是否有效
                val isOldInfoValid =
                    kotlin.runCatching {
                        /**
                         * oldInfo 是通过序列化得到的，其中的数据可能不合法（不可为空的数据为null了）
                         * 通过 copy 方法触发所有属性的非空检查，如果有非法字段，会触发异常，认为是无效缓存。
                         */
                        oldInfo.copy()
                        true
                    }.getOrDefault(false)

                if (isOldInfoValid) {
                    // 3.1. 缓存有效，合并新老数据后消费数据
                    mergeAndConsume(newInfo, oldInfo, onMergeReport, onTrack, trackOperator)
                } else {
                    // 3.2. 缓存无效，抛弃无效缓存，直接消费新的埋点数据
                    consumeTrackInfo(newInfo, onTrack, trackOperator)
                }
            } ?: let {
                // 4. 不存在缓存，直接消费新的埋点数据
                consumeTrackInfo(newInfo, onTrack, trackOperator)
            }
        }
    }

    /**
     * 合并新老数据后，消费埋点数据
     */
    private fun <Report : Any> mergeAndConsume(
        newInfo: TrackInfo<Report>,
        oldInfo: TrackInfo<Report>,
        onMergeReport: (old: Report, new: Report) -> Report,
        onTrack: (TrackInfo<Report>) -> TrackInfo<Report>?,
        trackOperator: CacheObjectOperator<TrackInfo<Report>>
    ) {

        val isShouldTrack = ((newInfo.trackTime - oldInfo.trackTime) >= newInfo.timeInterval)

        /*
         *  1. 此处进行合并
         * - trackTime: 若需要进行埋点，则保留本次埋点的时间；若不进行埋点，只缓存，保留上次埋点的时间。
         * - 其他数据： 以新数据为准
         */
        val mergedTrackInfo =
            TrackInfo(
                trackKey = newInfo.trackKey,
                report = onMergeReport(oldInfo.report, newInfo.report),
                trackTime = if (isShouldTrack) newInfo.trackTime else oldInfo.trackTime,
                isTrackFirstTime = newInfo.isTrackFirstTime,
                timeInterval = newInfo.timeInterval
            )

        if (isShouldTrack) {
            // 2. 满足埋点间隔，进行埋点
            onTrack(mergedTrackInfo)?.let {
                trackOperator.cache(it.trackKey, it)
            } ?: trackOperator.remove(newInfo.trackKey)
        } else {
            // 3. 不满足埋点间隔，缓存合并后数据
            trackOperator.cache(mergedTrackInfo.trackKey, mergedTrackInfo)
        }
    }

    /**
     * 直接消费 [trackInfo]
     */
    private fun <Report : Any> consumeTrackInfo(
        trackInfo: TrackInfo<Report>,
        onTrack: (TrackInfo<Report>) -> TrackInfo<Report>?,
        trackOperator: CacheObjectOperator<TrackInfo<Report>>
    ) {
        if (trackInfo.isTrackFirstTime || trackInfo.timeInterval <= 0) {
            onTrack(trackInfo)?.let {
                trackOperator.cache(trackInfo.trackKey, it)
            } ?: trackOperator.remove(trackInfo.trackKey)
        } else {
            trackOperator.cache(trackInfo.trackKey, trackInfo)
        }
    }

    /**
     * 创建一个缓存数据的操作器
     */
    private fun <CacheObject> createCacheOperator(clazz: Class<CacheObject>) = CacheObjectOperator(
        context = context,
        valueClass = clazz
    )
}
