/********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: PhotoClipBoundTransitionView
 ** Description: 大图定制的ClipBoundTransitionView
 ** Version: 1.0
 ** Date : 2024/12/25
 ** Author: Conghu.CAI@Apps.Gallery3D
 ** TAG: PhotoClipBoundTransitionView
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>      <version>       <desc>
 ** ------------------------------------------------------------------------------
 **  Conghu.CAI@Apps.Gallery3D      2024/12/25     1.0     first created
 ********************************************************************************/
package com.oplus.gallery.photo_page.ui.transition

import android.content.Context
import android.graphics.Canvas
import android.graphics.Rect
import android.graphics.RectF
import android.util.AttributeSet
import androidx.annotation.Keep
import androidx.annotation.UiThread
import androidx.annotation.WorkerThread
import com.oplus.animation.OplusAsyncAnimatorUtils
import com.oplus.gallery.foundation.ui.widget.BaseClipBoundTransitionView
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.times
import com.oplus.gallery.photo_page.ui.transition.TransitionAnimStrategy.NORMAL
import com.oplus.gallery.photo_page.ui.transition.TransitionAnimStrategy.RENDER
import com.oplus.gallery.standard_lib.app.AppConstants.Number.NUMBER_HALF

/**
 * 大图定制的ClipBoundTransitionView。
 * - 具有大小缩放、裁剪、平移的原功能。
 * - 既能跑MainThread动画，又能跑RenderThread动画。不同动画的区别：
 *      - 主线程和BaseClipBoundTransitionView系相同，是直接重绘内容。
 *      - Render线程动画，不会重绘，会操作View缩放、平移等属性。
 *
 * - 跑MainThread动画时，此View和[BaseClipBoundTransitionView]行为完全一致，完全是老动画的那一套东西。
 * - 当运行Render动画时，部分原始功能不可用，或者需要业务方自行做一些收尾工作。业务方使用时要注意：
 *      - 原BoundsType仅支持默认的DEFAUL策略，不支持SPECIAL策略。需要先还原其动画策略
 *      - 只有当setDestinationBound时才会开启绘制流程。因此尽量确保所有参数在此之前就下发完成。否则就要等下一帧才能上屏了。
 *      - 如果使用了setDestinationBound，那在动画/业务结束后，就要手动调用resetOutlineToNone移除轮廓裁剪。否则可能会有显示问题。
 *      - 如果使用了setImageAlpha，那在动画/业务结束后，就要手动调用resetViewAlpha确认透明度生效。否则透明度可能失效。
 *
 * @see com.oplus.gallery.foundation.ui.animation.RenderAnimator
 * @see com.oplus.gallery.photo_page.ui.transition.TransitionAnimStrategy
 */
@Keep
class PhotoClipBoundTransitionView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : BaseClipBoundTransitionView(context, attrs, defStyleAttr) {

    /**
     * 此clipView采用的动画策略。不同的策略，跑的线程、对view的操作都不同。
     * - NORMAL：View重绘，以达到裁剪、缩放等效果。View本身大小、位置不改变。
     * - RENDER：更改View属性。内容不重绘，而View本身大小、位置发生改变。
     * @see com.oplus.gallery.photo_page.ui.transition.TransitionAnimStrategy
     */
    var animStrategy: TransitionAnimStrategy = NORMAL

    /**
     * 当前设置的image内容透明度
     */
    private var currentAlpha: Int = ALPHA_MAX_VALUE

    /**
     * 备份：view的pivotXY
     * 目的：非ui线程不能直接调用getPivotXY，需要此处拷贝一份给子线程使用
     */
    private var storedPivotX: Float = 0F
    private var storedPivotY: Float = 0F

    /**
     * 将动画策略重置回Normal。会重新走View重绘的那一套。
     */
    fun resetAnimStrategy() {
        animStrategy = NORMAL
    }

    /**
     * 重置View的裁剪轮廓为None。
     * 如果使用了[setDestinationBound]，需要在动画or业务结束后调用此方法。
     */
    fun resetOutlineToNone() {
        outlineProvider = null
        this.clipToOutline = false
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)
        /**
         * 横竖屏切换，回调onConfigurationChanged的时候，view的绘制并未完成，所直接在onConfigurationChanged获取view的坐标值
         * 还是旧状态的值onLayout之后布局位置才是确定好了，所以在这里判断布局是否变化再更更新坐标
         */
        if (changed) {
            backupPivotXY()
        }
    }

    /**
     * 重置View的透明度，确保透明度生效。
     * 如果使用了[setImageAlpha]，需要在动画or业务结束后调用此方法。
     */
    fun resetViewAlpha() {
        val alphaNormalized = currentAlpha.toFloat() / ALPHA_MAX_VALUE
        alpha = alphaNormalized
    }

    /**
     * 在render动画开始前主线程调用：在主线程通过getPivotXY，拷贝一份view的pivot。
     * 目的：保存pivot拷贝，给子线程使用。
     * 原因：在子线程中调用getPivotXY，会触发RenderNode.updateMatrix，这会导致crash问题 (bug#8693706)
     * 注：涉及getPivotXY的调用，禁止在非UI线程调用！
     */
    @UiThread
    fun backupPivotXY() {
        storedPivotX = pivotX
        storedPivotY = pivotY
    }

    override fun onDraw(canvas: Canvas) {
        // 根据不同的动画策略，走不同的绘制
        when (animStrategy) {
            // 对于普通的主线程动画，需要通过图片clip scale translate后重绘
            NORMAL -> redrawByClip(canvas)

            // 对于Render线程动画，当普通imageView使用即可。
            RENDER -> redrawNotClip(canvas)
        }
    }

    override fun onSourceBoundSet() {
        when (animStrategy) {
            NORMAL -> super.onSourceBoundSet()  // 对于主线程动画，走重绘动画那一套逻辑即可
            RENDER -> Unit  // 对于Render动画，用不到这个rect
        }
    }

    /**
     * 此处RENDER动画，会在渲染线程对动画view进行缩放、位移、裁剪，来达到和原来主线程动画的重绘方案相同的效果。
     * 具体见技术方案：
     * https://odocs.myoas.com/docs/473Qy8EvV5igd83w/ 《大图入场动画 - RenderThread动画改造方案》访问密码 aktwrk
     */
    override fun onDestinationBoundSet() {
        when (animStrategy) {
            NORMAL -> super.onDestinationBoundSet()  // 对于主线程动画，走重绘动画那一套逻辑即可
            RENDER -> {
                // 守卫: 数据校验
                val drawableWidth = this.drawable?.intrinsicWidth ?: 0
                val drawableHeight = this.drawable?.intrinsicHeight ?: 0
                if (drawableWidth <= 0 || drawableHeight <= 0) {
                    // imageView上没内容，那还做什么过渡动画。直接return
                    GLog.w(TAG, LogFlag.DL) { "<onDestinationBoundSet> drawable.size is null, return!" }
                    return
                }

                val destWidth = destinationBound.width()
                val destHeight = destinationBound.height()
                val sourceWidth = sourceBound.width()
                val sourceHeight = sourceBound.height()
                val previewWidth = this.width
                val previewHeight = this.height
                if ((destWidth <= 0) || (destHeight <= 0) ||
                    (sourceWidth <= 0) || (sourceHeight <= 0) ||
                    (previewWidth <= 0) || (previewHeight <= 0)) {
                    // 这种情况是无法计算出缩放等参数的。业务方自行规避，直接return
                    return
                }

                // 0.1 计算drawable绘制到此view上的缩放比率
                val widthScale = previewWidth.toFloat() / drawableWidth
                val heightScale = previewHeight.toFloat() / drawableHeight
                val contentScale = minOf(widthScale, heightScale)

                // 0.2 计算drawable中sourceBound区域绘制到view上的对应区域
                val contentSourceRect = RectF(sourceBound).apply {
                    times(contentScale)
                    val dx = (previewWidth - contentScale * drawableWidth) * NUMBER_HALF
                    val dy = (previewHeight - contentScale * drawableHeight) * NUMBER_HALF
                    offset(dx, dy)
                }

                // 1. 异步设置scale
                val scaleRatio = setViewScaleAsync(contentScale, sourceWidth, sourceHeight, destWidth, destHeight)

                // 2. 异步设置 translate
                setViewTranslateAsync(contentSourceRect.left, contentSourceRect.top, scaleRatio)

                // 3. 异步设置 clip 以及 圆角
                setViewClipAsync(contentSourceRect, scaleRatio, destWidth, destHeight)
            }
        }
    }

    /**
     * 在RenderThread中异步对view进行平移操作。
     * @param contentSourceLeft sourceBound在view上的映射区域-左上点X
     * @param contentSourceTop sourceBound在view上的映射区域-左上点Y
     * @param scaleRatio view当前的缩放比率
     */
    @WorkerThread
    private fun setViewTranslateAsync(contentSourceLeft: Float, contentSourceTop: Float, scaleRatio: Float) {
        // 目标显示区域的左上点位置
        val destLeft = destinationBound.left
        val destTop = destinationBound.top

        // sourceBound在view上映射区域 - 经view缩放后的左上点位置
        val pivotX = storedPivotX   // 禁止在非UI线程直接getPivotXY，只允许读取pivotXY的拷贝
        val pivotY = storedPivotY
        val sourceBoundLeftAfterScale = (contentSourceLeft * scaleRatio) + ((1 - scaleRatio) * pivotX)
        val sourceBoundTopAfterScale = (contentSourceTop * scaleRatio) + ((1 - scaleRatio) * pivotY)

        // 异步移动view，使得上面两个点完全重合，显示区域和sourceBound映射区域重合。
        val translateX = destLeft - sourceBoundLeftAfterScale
        val translateY = destTop - sourceBoundTopAfterScale
        OplusAsyncAnimatorUtils.setTranslationX(this, translateX)
        OplusAsyncAnimatorUtils.setTranslationY(this, translateY)
    }

    /**
     * 在RenderThread中异步对view进行缩放操作。
     * @param contentScale drawable绘制到view上的缩放比率
     * @return scaleRatio
     */
    @WorkerThread
    private fun setViewScaleAsync(contentScale: Float, sourceWidth: Int, sourceHeight: Int, destWidth: Int, destHeight: Int): Float {
        // 通过drawable绘制到view上的缩放比率，计算sourceBound经缩放后的宽高
        val scaledSourceWidth = sourceWidth * contentScale
        val scaledSourceHeight = sourceHeight * contentScale

        // 将drawable中的sourceBound区域缩小至destinationBound的显示范围内
        val scaleXRatio = destWidth / scaledSourceWidth
        val scaleYRatio = destHeight / scaledSourceHeight
        val scaleRatio = maxOf(scaleXRatio, scaleYRatio)

        // 对view缩放，使得destinationBound能显示在sourceBound内部
        OplusAsyncAnimatorUtils.setScaleX(this, scaleRatio)
        OplusAsyncAnimatorUtils.setScaleY(this, scaleRatio)
        return scaleRatio
    }

    /**
     * 在RenderThread中异步对view进行裁剪操作，包含圆角操作。
     * @param contentSourceRect drawable中sourceBound区域内容，绘制到view上后的映射区域
     * @param scaleRatio view当前缩放比例
     */
    @WorkerThread
    private fun setViewClipAsync(contentSourceRect: RectF, scaleRatio: Float, destWidth: Int, destHeight: Int) {
        // 获取destBound在view上的sourceBound上对应的区域，作为view上的裁剪区域
        val destSourceXRatio = contentSourceRect.width() / destWidth
        val destSourceYRatio = contentSourceRect.height() / destHeight
        val destSourceRatio = minOf(destSourceXRatio, destSourceYRatio)

        val clipRect = Rect().apply {
            left = contentSourceRect.left.toInt()
            top = contentSourceRect.top.toInt()
            right = (contentSourceRect.left + destSourceRatio * destWidth).toInt()
            bottom = (contentSourceRect.top + destSourceRatio * destHeight).toInt()
        }

        // 裁剪是通过修改view裁剪轮廓实现的。确保view开启了裁剪轮廓。
        if (clipToOutline.not()) {
            GLog.w(TAG, LogFlag.DL) { "[setViewClipAsync] ClipAsync failed coz clipToOutline false. Sth wrong may occur in anim." }
            return
        }
        val cornerRadius = destinationCornerRadiusX / scaleRatio

        // 根据裁剪区域、圆角半角，对轮廓修改来实现裁剪效果
        OplusAsyncAnimatorUtils.setOutlineRoundRect(this, clipRect, cornerRadius, 0F)
    }

    override fun onImageAlphaSet(alpha: Int) {
        currentAlpha = alpha
        when (animStrategy) {
            NORMAL -> super.onImageAlphaSet(alpha)  // 对于主线程动画，走重绘的那一套逻辑即可
            RENDER -> {
                // Render动画，则异步修改alpha
                val alphaNormalized = alpha.toFloat() / ALPHA_MAX_VALUE
                OplusAsyncAnimatorUtils.setAlpha(this, alphaNormalized)
            }
        }
    }

    override fun onDestCornerRadiusYSet() {
        when (animStrategy) {
            NORMAL -> super.onDestCornerRadiusYSet()  // 对于主线程动画，走重绘的那一套逻辑即可
            RENDER -> Unit  // 无需操作，统一交给onDestinationBoundSet裁剪圆角
        }
    }

    override fun onDestCornerRadiusXSet() {
        when (animStrategy) {
            NORMAL -> super.onDestCornerRadiusXSet()  // 对于主线程动画，走重绘的那一套逻辑即可
            RENDER -> Unit  // 无需操作，统一交给onDestinationBoundSet裁剪圆角
        }
    }

    /**
     * - NORMAL动画 - 应该支持所以BoundsType的重绘操作。
     * - Render动画 - 仅支持BoundsType.DEFAULT这个默认策略。
     * @see [com.oplus.gallery.foundation.ui.widget.BoundsType]
     */
    override fun onBoundsTypeSet() {
        when (animStrategy) {
            NORMAL -> super.onBoundsTypeSet()  // 对于主线程动画，走重绘的那一套逻辑即可

            /**
             * 对于Render动画，setBoundType无需执行。
             * - 对BoundsType.DEFAULT而言，目的是将当前drawable重新按照fitCenter绘制到view上。
             *      而Render动画根本不会对view进行重绘，显示的始终是默认fitCenter的内容。因此天然就支持了，无需操作。
             * - 对BoundsType.SPECIAL而言，目的是drawable按照某个bound重绘。
             *      因为在RenderThread重绘风险太大，所以这一点Render动画无法支持。也无需操作了。
             */
            RENDER -> Unit
        }
    }

    companion object {
        private const val TAG = "PhotoClipBoundTransitionView"
        private const val ALPHA_MAX_VALUE = 255
    }
}