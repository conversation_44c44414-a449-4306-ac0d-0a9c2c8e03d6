/***********************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd.
 ** All rights reserved.
 **
 ** File: - PhotoAIScenerySection
 ** Description: PhotoAIScenerySection.
 ** Version: 1.0
 ** Date : 2025/4/3
 ** Author: 91001615
 **
 ** ---------------------Revision History: ---------------------
 **  <author>      <data>      <version >     <desc>
 ** ------------------------------------------------------------
 ** 91001615      2025/4/3     1.0         created
 ***************************************************************/
package com.oplus.gallery.photo_page.ui.section.scenery

import android.app.Activity
import android.view.View
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.foundation.arch.sectionpage.ISectionPage
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.ui.AbstractPhotoSection
import com.oplus.gallery.photo_page.ui.PhotoFragment
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel

/**
 * AI 风光功能大图入口页切片
 */
internal class PhotoAIScenerySection(
    sectionPage: ISectionPage<PhotoFragment, PhotoViewModel>
) : AbstractPhotoSection(sectionPage) {

    private val activity: Activity?
        get() = sectionPage.pageContext as? BaseActivity

    private var contentView: View? = null

    override fun onViewCreated(view: View) {
        super.onViewCreated(view)
        GLog.d(TAG, LogFlag.DL) { "[onViewCreated]" }
        contentView = view

        val parent = view.findViewById<View>(R.id.photo_tag_container)
        parent.setOnClickListener {
            GLog.d(TAG, LogFlag.DL) { "[onViewCreated] [onclick]" }
            viewModel.menuControl.notifyMenuItemClicked(R.id.action_ai_scenery, false)
        }
    }

    companion object {
        private const val TAG = "PhotoAIScenerySection"
    }
}