/********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - FuncAdapter
 ** Description:正常显示在外面的功能项列表适配器
 ** Version: 1.0
 ** Date: 2024-05-20
 ** Author: zhongxuechang@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** zhongxuechang@Apps.Gallery3D    2024-05-20     1.0
 ********************************************************************************/
package com.oplus.gallery.photo_page.ui.section.intelliRecommend

import android.content.Context
import android.content.res.ColorStateList
import android.graphics.Outline
import android.graphics.drawable.RippleDrawable
import android.text.TextUtils
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewOutlineProvider
import android.widget.ImageView
import androidx.core.content.ContextCompat
import androidx.core.view.doOnLayout
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListUpdateCallback
import androidx.recyclerview.widget.RecyclerView
import com.oplus.gallery.basebiz.widget.WindowFocusAwareLinearLayout
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.ui.section.intelliRecommend.FuncAdapter.FuncViewHolder
import com.oplus.gallery.standard_lib.ui.util.DoubleClickUtils

internal class FuncAdapter(
    private val context: Context,
    private val isUseForGuideDialog: Boolean = false
) : RecyclerView.Adapter<FuncViewHolder>() {

    /**
     * 所有数据项
     */
    private val normalItems = mutableListOf<FuncPresent>()
    private var recyclerView: RecyclerView? = null
    private var layoutManager: FuncLayoutManager? = null
    private val textScrollCountMap: MutableMap<String, Int> = mutableMapOf()
    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        this.recyclerView = recyclerView
        this.layoutManager = (recyclerView.layoutManager as? FuncLayoutManager) ?: let {
            GLog.e(TAG, LogFlag.DL, "onAttachedToRecyclerView, layoutManager must is CollapsedLayoutManager")
            null
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FuncViewHolder {
        val itemView = LayoutInflater.from(context).inflate(R.layout.item_intelli_func_normal, parent, false)
        return FuncViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: FuncViewHolder, position: Int) {
        holder.bindViewHolder(normalItems[position], textScrollCountMap, isUseForGuideDialog)
    }

    override fun getItemCount(): Int {
        return normalItems.size
    }

    /**
     * 刷新适配器数据
     * @param funcItems 新的数据
     * @param animate 是否执行item动画
     */
    internal fun refresh(funcItems: List<FuncPresent> = normalItems, animate: Boolean) {
        val oldNormalItems = mutableListOf<FuncPresent>().apply {
            addAll(normalItems)
        }
        refreshAfterMeasure(oldNormalItems, funcItems, animate)
    }

    private fun refreshAfterMeasure(oldItems: List<FuncPresent>, newItems: List<FuncPresent>, animate: Boolean) {
        val recyclerView = recyclerView ?: return
        val layoutManager = layoutManager ?: return
        recyclerView.post {
            // 预计算子view会重新通过adapter绑定新数据，需将新数据填充进去
            normalItems.clear()
            normalItems.addAll(newItems)

            val childList = layoutManager.preMeasureChild(recyclerView)
            if (childList.size < newItems.size) {
                normalItems.clear()
                normalItems.addAll(newItems.subList(0, childList.size))
            }
            /* 要是不post直接调用，可能会因为layout过一次，并且在preMeasureChild方法里已经绑定过View，
            在后续使用的是notifyDataSetChanged时，里面会认为无需更新，导致刷新失效 */
            recyclerView.post {
                onNormalItemRefresh(oldItems, normalItems, animate)
            }
        }
    }

    /**
     * 正常在外显示项的数据刷新回调，实现该方法触发adapter刷新
     * @param oldNormalItems 旧的数据
     * @param newNormalItems 新的数据
     * @param animate 是否执行动画
     */
    private fun onNormalItemRefresh(
        oldNormalItems: List<FuncPresent>,
        newNormalItems: List<FuncPresent>,
        animate: Boolean
    ) {
        val recyclerView = recyclerView ?: return
        val isAnimating = recyclerView.itemAnimator?.isRunning == true
        // mark by zxc 尝试不跳过动画
        if (animate.not() || isAnimating) {
            if (isAnimating) {
                recyclerView.itemAnimator?.endAnimations()
            }
            notifyDataSetChanged()
            return
        }
        val result = DiffUtil.calculateDiff(FuncDiffCallback(oldNormalItems, newNormalItems))
        result.dispatchUpdatesTo(
            object : ListUpdateCallback {
                override fun onInserted(position: Int, count: Int) {
                    GLog.d(TAG, LogFlag.DL) { "onInserted position:$position, count:$count" }
                    notifyItemRangeInserted(position, count)
                }

                override fun onRemoved(position: Int, count: Int) {
                    GLog.d(TAG, LogFlag.DL) { "onRemoved position:$position, count:$count" }
                    notifyItemRangeRemoved(position, count)
                }

                override fun onMoved(fromPosition: Int, toPosition: Int) {
                    GLog.d(TAG, LogFlag.DL) { "onMoved fromPosition:$fromPosition, toPosition:$toPosition" }
                    notifyItemMoved(fromPosition, toPosition)
                }

                override fun onChanged(position: Int, count: Int, payload: Any?) {
                    GLog.d(TAG, LogFlag.DL) { "onChanged position:$position, count:$count, payload:$payload" }
                    notifyItemRangeChanged(position, count, payload)
                }
            }
        )
    }

    /**
     * 智能推荐功能项的ViewHolder
     */
    internal class FuncViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {

        private val tvFunc: AutoEndMarqueeTextView = itemView.findViewById(R.id.tv_func)
        private val ivFunc: ImageView = itemView.findViewById(R.id.iv_func)
        private val wlFunc: WindowFocusAwareLinearLayout = itemView.findViewById(R.id.wl_func)
        private var pressedColorStateList = ColorStateList.valueOf(itemView.context.getColor(R.color.photopage_intelli_func_item_bg_pressed))

        internal fun bindViewHolder(funcPresent: FuncPresent, scrollCountMap: MutableMap<String, Int>, isUseForGuideDialog: Boolean) {
            ivFunc.setImageDrawable(funcPresent.icon)
            if (isUseForGuideDialog) {
                tvFunc.setTextSize(
                    TypedValue.COMPLEX_UNIT_PX,
                    tvFunc.context.resources.getDimensionPixelOffset(R.dimen.photopage_intelli_func_guide_title_size).toFloat()
                )
            }
            tvFunc.text = funcPresent.title
            itemView.setOnClickListener {
                if ((itemView.parent as? RecyclerView)?.isAnimating == true) {
                    GLog.d(TAG, LogFlag.DL, "bindViewHolder isAnimating")
                    return@setOnClickListener
                }
                if (DoubleClickUtils.isFastDoubleClick()) {
                    return@setOnClickListener
                }

                funcPresent.clickFunc.invoke()
            }
            val currentCount = scrollCountMap[funcPresent.tag] ?: 0
            if (currentCount >= MAX_MARQUEE_COUNT) {
                tvFunc.ellipsize = TextUtils.TruncateAt.END
                tvFunc.isSelected = false
            } else {
                tvFunc.setMarqueeRepeatAndStart(
                    MAX_MARQUEE_COUNT - scrollCountMap.getOrDefault(
                        funcPresent.tag,
                        0
                    ), object : MarqueeListener {
                        override fun onMarqueeLoopCompleted(loopCount: Int) {
                            scrollCountMap[funcPresent.tag] = scrollCountMap.getOrDefault(
                                funcPresent.tag,
                                0
                            ) + 1
                        }

                        override fun onMarqueeEnd() {
                            scrollCountMap[funcPresent.tag] = MAX_MARQUEE_COUNT
                        }
                    })
            }
            //设置外轮廓
            itemView.outlineProvider = object : ViewOutlineProvider() {
                override fun getOutline(view: View?, outline: Outline?) {
                    if (view == null) return
                    val radius = itemView.context.resources.getDimension(R.dimen.photopage_intelli_func_radius)
                    outline?.setRoundRect(ITEM_VIEW_START, ITEM_VIEW_START, view.width, view.height, radius)
                }
            }

            //暗亮色重新设置资源
            itemView.background = ContextCompat.getDrawable(itemView.context, R.drawable.bg_intelli_func_item_single)
            tvFunc.setTextColor(ContextCompat.getColor(itemView.context, R.color.photopage_intelli_func_item_text_color))
            pressedColorStateList =
                ColorStateList.valueOf(ContextCompat.getColor(itemView.context, R.color.photopage_intelli_func_item_bg_pressed))
            wlFunc.background = RippleDrawable(pressedColorStateList, null, null)
        }
    }

    private inner class FuncDiffCallback(val oldList: List<FuncPresent>, val newList: List<FuncPresent>) : DiffUtil.Callback() {

        override fun getOldListSize(): Int {
            return oldList.size
        }

        override fun getNewListSize(): Int {
            return newList.size
        }

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return oldItemPosition == newItemPosition
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return oldList[oldItemPosition].javaClass == newList[newItemPosition].javaClass
        }
    }

    companion object {
        private const val TAG = "FuncAdapter"
        private const val MAX_MARQUEE_COUNT = 2

        // View开始x,y坐标均为0，即左上角坐标为(0,0)
        private const val ITEM_VIEW_START = 0
    }
}