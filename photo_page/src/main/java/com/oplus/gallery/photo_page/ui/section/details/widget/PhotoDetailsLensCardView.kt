/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoDetailsTimeCard.kt
 ** Description : 大图内容详情信息镜头UI展示块
 ** Version     : 1.0
 ** Date        : 2025/4/11
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>                  2025/4/11       1.0     create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.ui.section.details.widget

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isGone
import androidx.core.view.isVisible
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_CSHOT
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_FAST_VIDEO
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_GIF
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_NONE
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_OLD_SLOW_MOTION
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_OLIVE
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_PANORAMA
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_PORTRAIT_BLUR
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_RAW
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_SLOW_MOTION
import com.oplus.gallery.business_lib.model.data.base.utils.VideoTypeUtils
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.viewmodel.details.PhotoDetails
import com.oplus.gallery.photo_page.viewmodel.details.PhotoDetailsConstants
import com.oplus.gallery.photo_page.viewmodel.details.PhotoDetailsViewData
import com.oplus.gallery.standard_lib.app.AppConstants

/**
 * 大图内容详情信息镜头UI展示块
 */
internal class PhotoDetailsLensCardView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    defStyleRes: Int = 0
) : PhotoDetailsBaseCardView(context, attrs, defStyleAttr, defStyleRes) {

    /**
     * 机型
     */
    private var tvModel: TextView? = null

    /**
     * 拍摄模式1——靠左的ImageView
     */
    private var ivModelLeft: ImageView? = null

    /**
     * 拍摄模式2——靠右的ImageView
     */
    private var ivModelRight: ImageView? = null

    /**
     * 展示镜头信息的容器layout
     */
    private var flCameraLens: FrameLayout? = null

    /**
     * 暂无镜头信息
     */
    private var tvNoCameraLens: TextView? = null

    /**
     * 镜头信息
     */
    private var tvCameraLens: TextView? = null

    /**
     * 横线
     */
    private var vLine: View? = null

    /**
     * 镜头焦距
     */
    private var tvFocalLength: TextView? = null

    /**
     * 镜头光圈
     */
    private var tvAperture: TextView? = null

    /**
     * 像素
     */
    private var tvResolution: TextView? = null

    /**
     * 宽高
     */
    private var tvWidthHeight: TextView? = null

    /**
     * 大小
     */
    private var tvSize: TextView? = null

    /**
     * 格式
     */
    private var tvMimeType: TextView? = null

    /**
     * 像素end侧的分割线
     */
    private var vLineToEndOfResolution: View? = null

    /**
     * 宽高end侧的分割线
     */
    private var vLineToEndOfWidthHeight: View? = null

    /**
     * 大小end侧的分割线
     */
    private var vLineToEndOfSize: View? = null

    /**
     * 初始化view
     */
    override fun initView() {
        super.initView()
        //机型
        tvModel = view.findViewById(R.id.tv_model)
        //拍摄模式1——靠左的ImageView
        ivModelLeft = view.findViewById(R.id.iv_model_left)
        //拍摄模式2——靠右的ImageView
        ivModelRight = view.findViewById(R.id.iv_model_right)
        //展示镜头信息的容器layout
        flCameraLens = view.findViewById(R.id.fl_camera_lens)
        //暂无镜头信息
        tvNoCameraLens = view.findViewById(R.id.tv_no_camera_lens)
        //镜头信息
        tvCameraLens = view.findViewById(R.id.tv_camera_lens)
        //横线
        vLine = view.findViewById(R.id.v_line)
        //镜头焦距
        tvFocalLength = view.findViewById(R.id.tv_focal_length)
        //镜头光圈
        tvAperture = view.findViewById(R.id.tv_aperture)
        //像素
        tvResolution = view.findViewById(R.id.tv_resolution)
        //宽高
        tvWidthHeight = view.findViewById(R.id.tv_width_height)
        //大小
        tvSize = view.findViewById(R.id.tv_size)
        //格式
        tvMimeType = view.findViewById(R.id.tv_mime_type)
        //分割线1
        vLineToEndOfResolution = view.findViewById(R.id.v_line_to_end_of_resolution)
        //分割线2
        vLineToEndOfWidthHeight = view.findViewById(R.id.v_line_to_end_of_width_height)
        //分割线3
        vLineToEndOfSize = view.findViewById(R.id.v_line_to_end_of_size)
    }

    /**
     * 设置layoutId
     */
    override fun getLayoutId(): Int {
        return R.layout.photo_details_lens_card
    }

    /**
     * 更新view
     */
    override fun updateData(photoDetails: PhotoDetails) {
        //获取展示信息
        this.photoDetails = photoDetails

        //获取详情字段
        val viewData = photoDetails.photoDetailsViewData
        //机型
        tvModel?.text = getModel(viewData)

        //镜头信息
        setCameraLens(viewData)

        //拍摄模式左侧——是否展示
        ivModelLeft?.isGone = if (photoDetails.isVideo()) {
            false
        } else {
            viewData.hasFlash == null
        }
        //拍摄模式左侧——靠左的ImageView
        val modelLeft = getModelLeftResource(viewData)
        if (modelLeft != FORMAT_NONE) {
            ivModelLeft?.setImageResource(modelLeft)
        }

        //拍摄模式右侧——是否展示
        val modelRight = getModelRightResource(viewData)
        ivModelRight?.isVisible = modelRight != FORMAT_NONE
        //拍摄模式右侧——靠右的ImageView
        if (modelRight != FORMAT_NONE) {
            ivModelRight?.setImageResource(modelRight)
        }

        //镜头焦距
        tvFocalLength?.text = viewData.focalLength
        //镜头光圈
        tvAperture?.text = viewData.aperture
        //像素
        tvResolution?.text = viewData.resolution
        //宽高
        tvWidthHeight?.text = viewData.widthHeight
        //大小
        tvSize?.text = viewData.size
        //格式
        tvMimeType?.text = viewData.mimeType
        //设置参数单位颜色
        setTextUnit()
    }

    /**
     * 明暗模式更新UI
     */
    override fun refreshUI() {
        super.refreshUI()
        //刷新资源
        photoDetails?.let {
            updateData(it)
        }
        //设置颜色
        vLine?.background = ContextCompat.getDrawable(context, R.drawable.photopage_ic_details_lens_line)
        //分割线1
        vLineToEndOfResolution?.background = ContextCompat.getDrawable(context, R.drawable.photopage_bg_photo_details_lens_line)
        //分割线2
        vLineToEndOfWidthHeight?.background = ContextCompat.getDrawable(context, R.drawable.photopage_bg_photo_details_lens_line)
        //分割线3
        vLineToEndOfSize?.background = ContextCompat.getDrawable(context, R.drawable.photopage_bg_photo_details_lens_line)
    }

    /**
     * 获取镜头信息
     * Mark By @all 是否截图、有无相机信息等未添加
     */
    private fun getModel(viewData: PhotoDetailsViewData): String {
        //视频处理录屏情况
        if (photoDetails?.isVideo() == true) {
            val videoModel = viewData.videoModel
            if (videoModel == PhotoDetailsConstants.PhotoDetailsModelKeys.FORMAT_SCREENSHOT_VIDEO) {
                //录屏
                return ContextCompat.getString(context, R.string.photopage_details_screen_recordings)
            }
        } else {
            //图片处理截屏情况
            val imageModel = viewData.imageModel
            if (imageModel == PhotoDetailsConstants.PhotoDetailsModelKeys.FORMAT_SCREENSHOT_IMAGE) {
                //截屏
                return ContextCompat.getString(context, R.string.photopage_details_screenshots)
            }
        }

        return when (val model = viewData.shootingInfo) {
            //信息未加载
            null -> EMPTY_STRING

            //无相机信息
            EMPTY_STRING -> ContextCompat.getString(context, R.string.photopage_details_no_camera_information)

            //返回对应的相机信息
            else -> model
        }
    }

    /**
     * 设置镜头信息相关逻辑
     */
    private fun setCameraLens(viewData: PhotoDetailsViewData) {
        //设置信息textView
        val lensString = getCameraLens(viewData)
        //是否展示“暂无镜头信息”
        val noCameraLensInfo = viewData.cameraLens == PhotoDetailsConstants.PhotoDetailsLensModelKeys.NONE
        //设置镜头信息
        tvNoCameraLens?.text = ContextCompat.getString(context, R.string.photopage_details_no_lens_information)
        tvCameraLens?.text = lensString
        //是否需要展示镜头信息
        flCameraLens?.isVisible = (getModel(viewData) == viewData.shootingInfo)
        //暂无镜头信息时显示且有设备信息时显示
        tvNoCameraLens?.isVisible = noCameraLensInfo
        //以下为暂无镜头信息时隐藏
        tvCameraLens?.isVisible = !noCameraLensInfo
        vLine?.isVisible = !noCameraLensInfo
        tvFocalLength?.isVisible = !noCameraLensInfo
        tvAperture?.isVisible = !noCameraLensInfo
    }

    /**
     * 获取镜头信息String
     */
    private fun getCameraLens(viewData: PhotoDetailsViewData): String {
        val cameraLens = viewData.cameraLens
        return when (cameraLens) {
            //前置摄像头
            PhotoDetailsConstants.PhotoDetailsLensModelKeys.FRONT -> ContextCompat.getString(context, R.string.photopage_details_front_camera)

            //显微摄像头
            PhotoDetailsConstants.PhotoDetailsLensModelKeys.MICRO -> ContextCompat.getString(context, R.string.photopage_details_micro_camera)

            //微距摄像头
            PhotoDetailsConstants.PhotoDetailsLensModelKeys.MACRO -> ContextCompat.getString(context, R.string.photopage_details_macro_camera)

            //主摄像头
            PhotoDetailsConstants.PhotoDetailsLensModelKeys.BACK -> ContextCompat.getString(context, R.string.photopage_details_back_camera)

            //超广角摄像头
            PhotoDetailsConstants.PhotoDetailsLensModelKeys.ULTRA_WIDE -> {
                ContextCompat.getString(
                    context,
                    R.string.photopage_details_ultra_wide_camera
                )
            }

            //广角摄像头
            PhotoDetailsConstants.PhotoDetailsLensModelKeys.WIDE -> ContextCompat.getString(context, R.string.photopage_details_wide_camera)

            //长焦摄像头
            PhotoDetailsConstants.PhotoDetailsLensModelKeys.TELEPHOTO ->
                ContextCompat.getString(context, R.string.photopage_details_telephoto_camera)

            //信息未加载
            null -> EMPTY_STRING

            else -> ContextCompat.getString(context, R.string.photopage_details_no_lens_information)
        }
    }

    /**
     * 获取左侧图标的resId
     */
    private fun getModelLeftResource(viewData: PhotoDetailsViewData): Int {
        if (photoDetails?.isVideo() == true) {
            //视频详情页
            val videoModel = viewData.videoModel
            return when (videoModel) {
                //录屏
                PhotoDetailsConstants.PhotoDetailsModelKeys.FORMAT_SCREENSHOT_VIDEO -> R.drawable.photopage_ic_details_record

                //延时摄影
                FORMAT_FAST_VIDEO -> R.drawable.photopage_ic_details_fast_video

                //慢动作
                FORMAT_SLOW_MOTION -> R.drawable.photopage_ic_details_slow_video

                //慢动作
                FORMAT_OLD_SLOW_MOTION -> R.drawable.photopage_ic_details_slow_video

                //无特殊模式
                else -> R.drawable.photopage_ic_details_video
            }
        } else {
            val flash = viewData.hasFlash
            return when (flash) {
                //开闪光
                true -> R.drawable.photopage_ic_details_flash

                //没开闪光
                false -> R.drawable.photopage_ic_details_no_flash

                //无信息
                null -> FORMAT_NONE
            }
        }
    }

    /**
     * 获取右侧图标的resId
     */
    private fun getModelRightResource(viewData: PhotoDetailsViewData): Int {
        if (photoDetails?.isVideo() == true) {
            val hdrFormat = viewData.hdrFormat
            return when (hdrFormat) {
                //HLG模式
                VideoTypeUtils.HDR_TYPE_HLG -> R.drawable.photopage_ic_details_hlg

                //杜比视界模式
                VideoTypeUtils.HDR_TYPE_DOLBY -> R.drawable.photopage_ic_details_dolby

                //HDR10模式
                VideoTypeUtils.HDR_TYPE_HDR10 -> R.drawable.photopage_ic_details_hdr10

                //HDR10+模式
                VideoTypeUtils.HDR_TYPE_HDR10_PLUS -> R.drawable.photopage_ic_details_hdr10plus

                //无特殊模式，MarkBy@All LOG模式还没判断
                else -> FORMAT_NONE
            }
        } else {
            val imageModel = viewData.imageModel
            return when (imageModel) {
                //截图
                PhotoDetailsConstants.PhotoDetailsModelKeys.FORMAT_SCREENSHOT_IMAGE -> R.drawable.photopage_ic_details_screenshot

                //人像模式
                FORMAT_PORTRAIT_BLUR -> R.drawable.photopage_ic_details_figure

                //连拍模式
                FORMAT_CSHOT -> R.drawable.photopage_ic_details_cshots

                //olive模式
                FORMAT_OLIVE -> R.drawable.photopage_ic_details_olive

                //gif模式
                FORMAT_GIF -> R.drawable.photopage_ic_details_gif

                //raw模式
                FORMAT_RAW -> R.drawable.photopage_ic_details_raw

                //全景模式
                FORMAT_PANORAMA -> R.drawable.photopage_ic_details_group_photo

                //无特殊模式
                else -> FORMAT_NONE
            }
        }
    }

    /**
     * 设置单位颜色
     */
    private fun setTextUnit() {
        //设置mm
        setTextUnitColor(
            tvFocalLength,
            tvFocalLength?.text?.length?.minus(AppConstants.Number.NUMBER_2) ?: AppConstants.Number.NUMBER_0,
            tvFocalLength?.text?.length ?: AppConstants.Number.NUMBER_0
        )
        //设置f
        setTextUnitColor(tvAperture, AppConstants.Number.NUMBER_0, AppConstants.Number.NUMBER_1)
    }
}