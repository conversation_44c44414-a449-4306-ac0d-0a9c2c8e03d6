/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoMenuRemoveFromLabelActionRule.kt
 ** Description : 大图页菜单不是此类操作规则
 ** Version     : 1.0
 ** Date        : 2022/03/18
 ** Author      : <PERSON>@Apps.Gallery
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON>@Apps.Gallery              2022/03/18  1.0         create file
 *********************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules

import androidx.annotation.IdRes
import com.oplus.gallery.business_lib.helper.BottomMenuHelper
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.label.set.LabelAlbum
import com.oplus.gallery.foundation.fileaccess.FileConstants
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant.Value.MENU_ITEM_REMOVE_FROM_LABEL
import com.oplus.gallery.foundation.ui.dialog.base.BaseAlertDialog
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.dataloading.DiffedPhotoItemViewData
import com.oplus.gallery.photo_page.viewmodel.dataloading.PhotoItemViewData

/**
 * 大图页菜单不是此类操作规则
 */
internal class PhotoMenuRemoveFromLabelActionRule(
    @IdRes ruleAction: Int,
    viewModel: PhotoViewModel,
    private val bottomMenuHelper: BottomMenuHelper
) : PhotoMenuActionRule(TAG, ruleAction, viewModel) {

    /**
     * 删除弹窗
     */
    private var dialog: BaseAlertDialog<out Any>? = null

    /**
     * 菜单执行回调的锁，避免多线程问题。
     */
    private val actionLock = Any()

    override fun onFocusViewDataChanged(diffedFocusViewData: DiffedPhotoItemViewData): Unit =
        synchronized(actionLock) {
            if (diffedFocusViewData.isContentChanged) {
                dialog?.cancel()
            }
        }

    override fun execute(viewData: PhotoItemViewData, mediaItem: MediaItem, onDone: () -> Unit) {
        // 埋点： 不是此类菜单项被点击。
        trackMenuClick(MENU_ITEM_REMOVE_FROM_LABEL)

        val labelId = viewModel.inputArguments.dataSource.value?.setPath?.let { path ->
            TextUtil.getPathSuffix(path).toInt()
        }
        val labelName = viewModel.inputArguments.title.value?.albumName

        if (labelId == null || labelId == LabelAlbum.INVALID_LABEL_ID || labelName.isNullOrEmpty()) {
            GLog.w(TAG, "[execute] labelId or labelName is invalid.")
            onDone()
            return
        }

        dialog = bottomMenuHelper.doRemoveFromLabelAction(
            itemPath = Path.fromString(viewData.id),
            isImage = (viewData.mediaType == FileConstants.MediaType.MEDIA_TYPE_IMAGE),
            labelId = labelId,
            labelName = labelName,
            trackCallerEntry = trackCaller,
            onCompleted = { _, _ ->
                synchronized(actionLock) { dialog = null }
                onDone()
            }
        )
    }

    companion object {
        private const val TAG = "PhotoMenuRemoveFromLabelActionRule"
    }
}