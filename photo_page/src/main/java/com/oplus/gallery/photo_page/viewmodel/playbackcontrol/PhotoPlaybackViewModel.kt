/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoPlaybackViewModel.kt
 ** Description : 大图页播控ViewModel
 ** Version     : 1.0
 ** Date        : 2022/04/11
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>           2022/04/11  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.playbackcontrol

import android.app.Activity
import android.media.MediaFormat.COLOR_TRANSFER_HLG
import android.media.MediaFormat.COLOR_TRANSFER_ST2084
import android.text.TextUtils
import android.view.Choreographer
import android.view.Window
import androidx.annotation.MainThread
import androidx.annotation.UiThread
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business_lib.fileprocessor.FileProcessScene
import com.oplus.gallery.business_lib.fileprocessor.FinishListener
import com.oplus.gallery.business_lib.helper.VideoAutoPlayHelper
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.isVideoReady
import com.oplus.gallery.business_lib.model.data.base.item.toLocalVideo
import com.oplus.gallery.business_lib.model.data.base.utils.Constants
import com.oplus.gallery.business_lib.model.data.base.utils.MediaItemUtils
import com.oplus.gallery.common.playbackcontrol.IPlaybackInfoChangedListener
import com.oplus.gallery.common.playbackcontrol.PlaybackRequest
import com.oplus.gallery.common.playbackcontrol.PlaybackState
import com.oplus.gallery.foundation.database.store.CloudSyncStore.LocalFileStatus.LOCAL_FILE_STATUS_DEFAULT
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant.Value.VIDEO_PLAYER_FINISH_SWITCH_TO_NEXT
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant.Value.VIDEO_PLAYER_FINISH_SWITCH_TO_PRE
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.collectNotNull
import com.oplus.gallery.foundation.util.ext.runOnUiThread
import com.oplus.gallery.foundation.util.ext.setOrPostValue
import com.oplus.gallery.foundation.util.lifecycle.SingleLiveEvent
import com.oplus.gallery.foundation.util.math.MathUtil.toScale
import com.oplus.gallery.framework.abilities.config.IConfigListener
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.ViewBehavior.VIDEO_AUTO_PLAY
import com.oplus.gallery.framework.abilities.hardware.IHardwareAbility
import com.oplus.gallery.framework.abilities.hardware.audio.IAudioVolumeController
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.viewmodel.PhotoSubViewModel
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.brighten.PhotoBrightenViewModel.Companion.isDolbyVision
import com.oplus.gallery.photo_page.viewmodel.dataloading.PhotoDataLoadingViewModel.Companion.INVALID_INDEX
import com.oplus.gallery.photo_page.viewmodel.dataloading.PhotoItemViewData
import com.oplus.gallery.photo_page.viewmodel.dataloading.diffedFocusViewDataDistinctId
import com.oplus.gallery.photo_page.viewmodel.dataloading.focusItemViewData
import com.oplus.gallery.photo_page.viewmodel.inputarguments.PhotoInputArgumentsViewModel.DataSource.DataSet.SHARED_ALBUM
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PageOperationState.SuppressPlayback
import com.oplus.gallery.photo_page.viewmodel.playbackcontrol.PhotoLocalPlaybackControl.Companion.LOOP_PLAY_MIN_TIME
import com.oplus.gallery.photo_page.viewmodel.track.producttracker.tracker.IVideoPlayProductTracker
import com.oplus.gallery.photo_page.viewmodel.track.producttracker.tracker.IVideoPlayProductTracker.Companion.ACTION_FINISH
import com.oplus.gallery.photo_page.widget.seekbar.ISliceRetriever
import com.oplus.gallery.photo_page.widget.seekbar.ISliceRetrieverFactory
import com.oplus.gallery.standard_lib.app.AppConstants
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.standard_lib.codec.player.AVController
import com.oplus.gallery.standard_lib.codec.player.AVController.LoadingState
import com.oplus.gallery.standard_lib.codec.player.AVPlayer
import com.oplus.gallery.standard_lib.scheduler.session.PrioritySession
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.videoeditor.data.VideoOptions
import com.oplus.gallery.videoeditor.data.VideoSpec
import com.oplus.gallery.videoeditor.utils.CodecSupport
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.launch
import java.math.RoundingMode
import java.util.Collections
import java.util.concurrent.atomic.AtomicReference
import kotlin.math.abs
import kotlin.system.measureTimeMillis

/**
 * Player 播放状态，给 View 层使用的 ViewData
 */
data class PlaybackInfo(
    /**
     * 视频宽度
     */
    val videoWidth: Int = 0,

    /**
     * 视频高度
     */
    val videoHeight: Int = 0,

    /**
     * 视频帧率
     */
    val videoFps: Float = 0.0f,

    /**
     * 当播放器准备就绪之后,则开始播放
     */
    var shouldPlayAfterPrepared: Boolean = false,

    /**
     * 播放器标识，用于标识播放器的 id
     */
    val playerToken: Int = 0,
    /**
     * 是否被静音
     */
    val isMuted: Boolean = false,

    /**
     * 是否正在播放
     */
    val isPlaying: Boolean = false,

    /**
     * 是否正是在执行快进、快退操作
     */
    val isSeeking: Boolean = false,

    /**
     * 播放总时长
     */
    val duration: Long = 0L,

    /**
     * 播放位置与总时长的百分比
     */
    val positionInPercent: Double = 0.0,

    /**
     * 音频是否可用
     */
    val isAudioValid: Boolean = true,

    /**
     * 是否为 HDR 视频
     *
     * Marked by zhangjisong 此处属于 DataSource 相关信息，并不属于 Playback，待大图稳定，清理 Mark 的时候进行分类封装
     */
    val isHDRVideo: Boolean = false,

    /**
     * 媒体数据源就绪状态
     */
    val mediaSourceState: MediaSourceState = MediaSourceState.UNKNOWN,

    /**
     * 本地文件状态
     *
     * @see com.oplus.gallery.foundation.database.store.CloudSyncStore.LocalFileStatus
     */
    val localFileStatus: Int = LOCAL_FILE_STATUS_DEFAULT,

    /**
     * 视频缩图小分片是否应该强制刷新
     */
    val shouldSliceForceInvalidate: Boolean = false,

    /**
     * 播放器状态
     */
    val playerState: LoadingState = LoadingState.INIT,

    /**
     * 当前播放器是否可用的,对于云视频来说token是有效的，对于本地视频来说uri是有效的
     */
    var isValidPlayer: Boolean = true,

    /**
     * 当前播放器是否正以120fps播放
     */
    val isPlaybackAt120Fps: Boolean = false,

    /**
     * 当前播放器播放状态
     *
     * 该值在“视频A->详情->B->A==未播放”中至关重要，能让PhotoPlaybackViewModel.launchUpdatePlaybackInfoTask中驱动_playbackInfo更新从而驱动视频播放至关重要
     */
    val playbackState: AVController.PlaybackState = AVController.PlaybackState.IDLE
) {
    fun isPlayerTokenEquals(other: PlaybackInfo?): Boolean {
        return playerToken == other?.playerToken
    }

    internal fun isPlayerSizeEquals(other: PlaybackInfo?): Boolean {
        return (videoWidth == other?.videoWidth) && (videoHeight == other.videoHeight)
    }

    /**
     * 播放器的信息是否发生变化：
     * 如 视频下载原图、播放器状态切换、宽高变化等
     * 注意:不要增加类似position、positionInPercent这种频繁变化的判断，否则可能会发生频繁调用
     */
    fun isPlayInfoChanged(other: PlaybackInfo?): Boolean {
        return other?.let {
            (localFileStatus != it.localFileStatus) ||
                (mediaSourceState != it.mediaSourceState) ||
                (isPlaying != it.isPlaying) ||
                (playerState != it.playerState) ||
                (isValidPlayer != it.isValidPlayer) ||
                (videoWidth != it.videoWidth) ||
                (videoHeight != it.videoHeight) ||
                (isMuted != it.isMuted) ||
                (isAudioValid != it.isAudioValid)
        } ?: true
    }
}

/**
 * 媒体数据源就绪状态
 */
enum class MediaSourceState {
    /**
     * 未知，还未加载到相关信息前是此状态
     */
    UNKNOWN,

    /**
     * 就绪
     */
    READY,

    /**
     * 未就绪，如 共享图集视频未下载
     */
    NOT_READY;

    val isReady: Boolean
        get() = this == READY

    val isNotReady: Boolean
        get() = this == NOT_READY
}

internal typealias MediaItemGetter = (PhotoItemViewData) -> MediaItem?

private typealias OnPlayerErrorCallback = (avController: AVController, what: Int, extra: Int, details: String) -> Unit

/**
 * 该类仅负责业务性质的视频播放控制，不负责创建、销毁等工作
 *
 * 公开接口：
 * [play]：UI触发，执行播放操作
 * [pause]：UI触发，执行暂停操作
 * [mute]：UI触发，执行静音操作
 * [unmute]：UI触发，执行取消静音操作
 * [seek]：UI触发，快进操作
 * [enterPreviewSeeking]：UI触发，进入快进模式，后续 seek 有即时预览
 * [exitPreviewSeeking]：UI触发，退出快进模式，后续 seek 无即时预览
 * [notifyWindowFocusChanged]：UI触发，焦点变更，当丢失、获取焦点后有不同操作
 * [clearCurrentPlayer]：清除当前播放器，移除回调等
 *
 * internal 接口：
 * [changeCurrentPlayer]：切换 AVController
 * [onCleared]：清除当前播放器，移除回调等
 */
internal class PhotoPlaybackViewModel(
    override val pageViewModel: PhotoViewModel,
    private val mediaItemGetter: MediaItemGetter,
    private val workerSession: PrioritySession,
    private val queueWorkScope: CoroutineScope
) : PhotoSubViewModel(pageViewModel) {

    /**
     * [IHardwareAbility]配置能力的对象，避免频繁创建
     */
    private val hardwareAbility: IHardwareAbility? by lazy {
        pageViewModel.context.getAppAbility<IHardwareAbility>().apply {
            if (this == null) {
                GLog.i(TAG, LogFlag.DL) { "[hardwareAbility]init hardwareAbility failed , hardwareAbility is null" }
            }
        }
    }

    /**
     * 播放器状态
     */
    val playbackInfo: LiveData<PlaybackInfo?> get() = _playbackInfo
    private val _playbackInfo: MutableLiveData<PlaybackInfo?> = MutableLiveData(null)

    /**
     * 是否支持视频缩图预览
     * 与设计沟通不使用视频缩图样式，暂时保留
     */
    val isSupportPlaybackPreview: LiveData<Boolean> get() = _isSupportPlaybackPreview
    private val _isSupportPlaybackPreview: MutableLiveData<Boolean> = MutableLiveData(false)

    /**
     * 是否自动播放视频
     */
    val shouldAutoPlay: LiveData<Boolean> get() = _shouldAutoPlay
    private val _shouldAutoPlay: MutableLiveData<Boolean> = MutableLiveData()

    /**
     * 是否视频播放卡顿弹出框
     */
    val shouldShowVideoUnsmoothlyTips: LiveData<Boolean> get() = _shouldShowVideoUnsmoothlyTips
    private val _shouldShowVideoUnsmoothlyTips: MutableLiveData<Boolean> = MutableLiveData(false)

    /**
     * 播控ui风格
     * 优先级： THUMB_LINE > THUMBNAIL_SEEKBAR > DEFAULT_SEEKBAR
     */
    val playbackUiStyle: LiveData<PlaybackUiStyle> get() = _playbackUiStyle
    private val _playbackUiStyle: MutableLiveData<PlaybackUiStyle> = MutableLiveData()

    /**
     * 是否需要将当前正在播放的视频，对应的资源给重新加载一遍。
     */
    val shouldReloadFocusVideoResource: LiveData<Boolean> get() = _shouldReloadFocusVideoResource
    private val _shouldReloadFocusVideoResource: SingleLiveEvent<Boolean> = SingleLiveEvent()

    /**
     * 当前focus的视频，因为播放器异常导致请求重新加载视频资源的请求次数。
     */
    private var focusVideoRequestReloadResourceTimes: Int = 0

    /**
     * 视频缩图小分片获取器是否已经初始化，切换播放器后重置此状态
     */
    private var isSliceRetrieverInitialized = false

    private var hasUnsmoothlyToastShown: Boolean = false

    /**
     * 视频缩图小分片获取器的工厂: 带缩图的单seekBar
     */
    val sliceRetrieverFactory: LiveData<ISliceRetrieverFactory?> get() = _sliceRetrieverFactory
    private val _sliceRetrieverFactory: MutableLiveData<ISliceRetrieverFactory?> = MutableLiveData(null)
    private val videoSliceRetrieverFactory = object : ISliceRetrieverFactory {

        @Volatile
        var isProductRetrieverPermitted: Boolean = true
            @Synchronized set

        private val sliceRetrieverSet: MutableSet<ISliceRetriever> = Collections.synchronizedSet(mutableSetOf())

        @Synchronized
        fun releaseAll() {
            measureTimeMillis {
                sliceRetrieverSet.toSet().forEach(ISliceRetriever::release)
                if (sliceRetrieverSet.isEmpty().not()) {
                    GLog.w(TAG, "[releaseAll] sliceRetrieverSet is not empty, need clear it!")
                    sliceRetrieverSet.clear()
                }
            }.let { costTime ->
                GLog.d(TAG, "[releaseAll] costTime = $costTime ms")
            }
        }

        @Synchronized
        override fun create(): ISliceRetriever {
            if (isSupportThumbLine.not()) {
                if (isValidPlaybackInfo.not()) {
                    GLog.d(TAG) { "[createSliceRetriever] emptyRetriever, playbackInfo not found" }
                    return emptyThumbnailRetriever
                }
                if (isSupportPlaybackPreview.value != true) {
                    GLog.d(TAG) { "[createSliceRetriever] emptyRetriever, preview not supported" }
                    return emptyThumbnailRetriever
                }
            }
            if (isProductRetrieverPermitted.not()) {
                GLog.d(TAG) { "[createSliceRetriever] emptyRetriever, retriever not permitted" }
                return emptyThumbnailRetriever
            }
            val photoItemViewData = pageViewModel.dataLoading.focusItemViewData ?: let {
                GLog.w(TAG) { "[createSliceRetriever] emptyRetriever, photoItemViewData not found" }
                return emptyThumbnailRetriever
            }
            val mediaItem = mediaItemGetter.invoke(photoItemViewData)?.toLocalVideo() ?: let {
                GLog.w(TAG) { "[createSliceRetriever] emptyRetriever, mediaItem not found" }
                return emptyThumbnailRetriever
            }
            if (mediaItem.contentUri == null) {
                GLog.d(TAG) { "[createSliceRetriever] emptyRetriever, contentUri not found" }
                return emptyThumbnailRetriever
            }

            GLog.d(TAG, "[createSliceRetriever] created")

            isSliceRetrieverInitialized = true
            val realRetriever = VideoSliceRetriever(pageViewModel.context, workerSession, mediaItem)
            return object : ISliceRetriever by realRetriever {

                init {
                    sliceRetrieverSet.add(this)
                }

                override fun release() {
                    launch(Dispatchers.IO) {
                        realRetriever.release()
                    }
                    sliceRetrieverSet.remove(this)
                }
            }
        }
    }

    /**
     * 自动播放配置变更的监听器，变动时应请求刷新自动播放状态
     */
    private val autoPlayConfigListener = IConfigListener {
        val shouldAutoPlay = shouldAutoPlay()
        GLog.d(TAG) { "[autoPlayConfigChanged] shouldAutoPlay=$shouldAutoPlay" }
        _shouldAutoPlay.postValue(shouldAutoPlay)
    }

    @Volatile
    private var focusViewDataIdWhileSuppressed: String? = null

    @Volatile
    private var isFocusChangedWhileSuppressed: Boolean = false

    /**
     * 是否支持缩图轴
     */
    private val isSupportThumbLine: Boolean
        get() = (pageViewModel.inputArguments.features.value?.isThumbLineEnabled == true)

    private val isPlaybackSuppressed: Boolean
        get() = pageViewModel.pageManagement.pageOperationState.value?.contains(SuppressPlayback) ?: false

    /**
     * 存储更新播放器状态的任务，避免多次启动
     */
    private val updatePlaybackInfoTaskRef: AtomicReference<Runnable> = AtomicReference()


    /**
     * 页面跳转时，期望续播的位置
     */
    private var pendingPlaybackPosition: Long = INVALID_PLAYBACK_POSITION

    /**
     * 用于判断当前播放器是否有效
     */
    private val isValidPlaybackInfo: Boolean
        get() {
            if (photoPlaybackControlManager.curPhotoPlaybackControl.isInvalidPlayer()) {
                return false
            }

            if ((pageViewModel.outputChannels.castingManager.isCasting.value == true)
                && (pageViewModel.outputChannels.castingManager.isVideoCasting.value != true)
            ) {
                return false
            }

            return true
        }


    /**
     * 帧间隔粒度的刷新
     *
     * 注意：不要懒加载，懒加载触发时机可能在子线程，用的不是主线程的 Handler
     */
    private val choreographer = Choreographer.getInstance()
    private val frameCallback = Choreographer.FrameCallback {
        if ((photoPlaybackControlManager.playbackControlType.needUpdatePlaybackInfo)) {
            launchUpdatePlaybackInfoTask()
            doScheduleRefreshPlaybackInfo()
        }
    }

    /**
     * diffedFocus流：只有focus为在线可播放视频时才下发
     */
    private val diffedFocusOlineVideoFilterFlow by lazy {
        pageViewModel.dataLoading.diffedFocusViewData.filter {
            // 只关心focus的是在线可播放视频的情况
            it?.newItem?.isOnlinePlayableVideo == true
        }
    }

    /**
     * seekBar控制音量时，通过audioController来设置是否静音，并将状态回调，IPhotoPlaybackable依据状态设置是否静音<p>
     * IPhotoPlaybackable的静音状态会在变更后通过playingInfo回调给VM，VM再回调给View层
     * 进出不大图不销毁audioVolumeController，目的是为了保持用户状态一致
     */
    private val audioVolumeController: IAudioVolumeController?
        get() = hardwareAbility?.let {
            it.volume?.let { audioVolumeManager ->
                audioVolumeManager.getAudioVolumeController(PHOTO_PAGE_AUDIO_CONTROLLER_NAME) ?: let {
                    // 不要使用 [shouldAutoPlay], 会有时机问题，初始化时 inputVM 还未初始化。
                    val forceMuteWhenInit = VideoAutoPlayHelper.isVideoAutoPlay
                    audioVolumeManager.createAudioVolumeController(
                        uniqueName = PHOTO_PAGE_AUDIO_CONTROLLER_NAME,
                        forceMuteWhenInit = forceMuteWhenInit
                    ).also {
                        GLog.d(TAG) { "[init audioVolumeManager] forceMuteWhenInit=$forceMuteWhenInit" }
                    }
                }
            }
        }

    /**
     * Audio状态变更监听回调
     */
    private val playerAudioStateChangeListener = object : IAudioVolumeController.AudioStateChangedListener {
        override fun onStateChanged(state: IAudioVolumeController.AudioState, audioMode: IAudioVolumeController.AudioMode) {
            val isMute = (state == IAudioVolumeController.AudioState.AUDIO_STATE_MUTE)
            GLog.d(TAG, LogFlag.DL) { "playerAudioStateChangeListener onOLiveStateChanged isMute=$isMute" }
            launch(Dispatchers.UI.immediate) {
                // 是否静音 统一从AudioController是否静音的状态来控制
                photoPlaybackControlManager.curPhotoPlaybackControl.setMute(isMute)
            }
            if (isMute.not()) {
                pageViewModel.track.trackVideoPlayerOperation(
                    PictureTrackConstant.Value.VIDEO_PLAYER_ACTION_MUTE_OR_NOT,
                    PictureTrackConstant.Value.VIDEO_PLAYER_ACTION_RESULT_POSITIVE,
                    PictureTrackConstant.Value.VIDEO_PLAYER_AUDIO_CONTROLLER_VOLUME
                )
            }
        }

        override fun onAudioFocusChanged(hasFocus: Boolean) {
            GLog.d(TAG) { "[onAudioFocusChanged] hasFocus=$hasFocus" }
            if (!hasFocus) {
                launch(Dispatchers.UI.immediate) {
                    pause()
                }
            }
        }

        override fun onAudioModeChanged(audioMode: IAudioVolumeController.AudioMode) = Unit
        override fun onUnmutedFromVolumeChangedAction() = Unit
        override fun onMutedFromVolumeChangedAction() = Unit
    }

    /**
     * 播放器管理类，VM可以使用此类进行播放器的切换，以提供不同的数据源
     */
    private val photoPlaybackControlManager =
        PhotoPlaybackControlManager(
            playerEventListener = InternalPlayerEventListener(pageViewModel).apply {
                // 仅支持大小屏接力的机型如xueying，需要监听。
                this.onPlayerErrorCallback = ::onPlayerError
            },
            audioControllerGetter = ::audioVolumeController,
            playbackInfoChangedListener = object : IPlaybackInfoChangedListener {
                override fun onUpdatePlaybackInfo() {
                    launchUpdatePlaybackInfoTask()
                }

                override fun onCancelScheduleRefreshPlaybackInfo() {
                    doCancelScheduleRefreshPlaybackInfo()
                }

                override fun onScheduleRefreshPlaybackInfo() {
                    doScheduleRefreshPlaybackInfo()
                }
            },
            mediaSourceStateGetter = { getMediaSourceState() },
            isAllowPlay = { isAllowPlay() },
            isInDetailsModeGetter = { pageViewModel.details.isInDetailsMode.value == true }
        )


    /**
     * 视频缩图条的当前可见性，
     * 视频被放大后会显示这个来替代缩图轴
     * 详见 AlterPlaybackThumbProgressController 类
     */
    val isAlterThumbProgressBarVisible: StateFlow<Boolean> get() = _isAlterThumbProgressBarVisible
    private val _isAlterThumbProgressBarVisible: MutableStateFlow<Boolean> = MutableStateFlow(false)


    override fun onBind() {
        updatePlayBackUiStyleIfNeed()
        updatePlaybackControlManagerStatus()
        _sliceRetrieverFactory.postValue(videoSliceRetrieverFactory)
        _shouldAutoPlay.postValue(shouldAutoPlay())
        subscribeStatesFromViewModel()
    }

    /**
     * 处于视频大图页且在播放过程时，需要设置屏幕为常亮
     *
     * @param currentWindow 设为常亮的window
     * @param isKeepScreenOn true:常亮
     */
    internal fun notifyScreenStateChanged(currentWindow: Window, isKeepScreenOn: Boolean) {
        hardwareAbility?.let { hardwareAbility ->
            hardwareAbility.screen?.setKeepScreenOn(currentWindow, isKeepScreenOn)
        } ?: GLog.w(TAG) {
            "[notifyScreenStateChanged] hardwareAbility not found!"
        }
    }

    /**
     * 判断焦点在恢复后视频是否会自动播放
     */
    internal fun shouldPlayAfterGetFocus() = photoPlaybackControlManager.curPhotoPlaybackControl.shouldPlayAfterGetFocus()

    @Suppress("LongMethod")
    private fun subscribeStatesFromViewModel() {
        pageViewModel.outputChannels.castingManager.isCasting.observeForever { isCasting ->
            val isCastingPlayback = photoPlaybackControlManager.playbackControlType == PlaybackControlType.CASTING_PLAYBACK
            GLog.d(TAG) {
                "[subscribeStatesFromViewModel] outputChannels.observeForever isCasting=$isCasting isCastingPlayback=$isCastingPlayback"
            }
            if ((isCasting && isCastingPlayback) || (isCasting.not() && isCastingPlayback.not())) {
                return@observeForever
            }

            // 记录当前播放断点，并暂停该播放器
            val position = photoPlaybackControlManager.curPhotoPlaybackControl.getCurrentPosition()
            photoPlaybackControlManager.curPhotoPlaybackControl.changePlayerState(PlaybackState.PAUSED, null)

            // 切换到目标播放器
            val playbackControlType = if (isCasting == true) PlaybackControlType.CASTING_PLAYBACK else selectPhotoPlaybackControlType()
            photoPlaybackControlManager.switchPlaybackControl(playbackControlType)

            // seek到断点：如果播放器已经准备好，则直接seek，否则先记录断点，等到Prepared后再seek到断点
            photoPlaybackControlManager.curPhotoPlaybackControl.let { photoPlaybackControl ->
                GLog.d(TAG) { "[subscribeStatesFromViewModel] position=$position playbackControlType=$playbackControlType" }
                if (photoPlaybackControl.getCurrentPlayingInfo().loadingState == LoadingState.LOADED) {
                    photoPlaybackControl.seekTo(position)
                } else {
                    photoPlaybackControl.recordPosition(position)
                    photoPlaybackControl.changePlayerState(PlaybackState.PLAYING, null)
                }
            }

            photoPlaybackControlManager.curPhotoPlaybackControl.changePlayerState(PlaybackState.PLAYING, null)
        }

        pageViewModel.outputChannels.castingManager.playerController.observeForever { castingPlayer ->
            photoPlaybackControlManager.changeCurrentPlayer(PlaybackControlType.CASTING_PLAYBACK, castingPlayer ?: AVController.EMPTY)
        }

        pageViewModel.inputArguments.dataSource.observeForever { dataSource ->
            pendingPlaybackPosition = dataSource?.playbackPosition ?: INVALID_PLAYBACK_POSITION
            photoPlaybackControlManager.switchPlaybackControl(selectPhotoPlaybackControlType())
        }
        pageViewModel.inputArguments.features.observeForever { features ->
            // 相机进入时需要保持非静音
            if (features.shouldUnmute) {
                GLog.d(TAG) { "[inputArguments.features] shouldUnmute=$features.shouldUnmute" }
                audioVolumeController?.unmute()
            }
        }

        launch(Dispatchers.Main.immediate) {
            pageViewModel.dataLoading.diffedFocusViewDataDistinctId.collectNotNull {
                GTrace.trace({ "$TAG.focusSlotViewDataDiff" }) {
                    /**
                     * 资源切换了，重置计数。
                     */
                    focusVideoRequestReloadResourceTimes = 0
                    if (isPlaybackSuppressed) {
                        isFocusChangedWhileSuppressed = true
                        // 播控被抑制 && 焦点变更，此时需要停止播放和重置位置；否则可能还在后台播放
                        pause()
                        seek(0.0)
                        launchUpdatePlaybackInfoTask()
                    } else {
                        setupPlaybackStatusWhileFocusChanged()
                    }
                    hasUnsmoothlyToastShown = false
                    photoPlaybackControlManager.curPhotoPlaybackControl.changePlayerState(PlaybackState.INITIALIZED, PlaybackRequest.INITIALIZE)
                }
            }
        }

        pageViewModel.pageManagement.pageOperationState.observeForever {
            if (isPlaybackSuppressed) {
                focusViewDataIdWhileSuppressed = pageViewModel.dataLoading.focusItemViewData?.id
                return@observeForever
            }

            val isSuppressed = isFocusChangedWhileSuppressed

            // 这个条件一定要在 clearCurrentPlayer 之前，不然会 setup 个寂寞
            if (isFocusChangedWhileSuppressed) {
                isFocusChangedWhileSuppressed = false
                setupPlaybackStatusWhileFocusChanged()
            }

            val currentFocusId = pageViewModel.dataLoading.focusItemViewData?.id

            /**
             * 若当前是视频 & 拖拽缩图轴至其他后松手
             * 视频切换了,需要 clear player
             */
            val shouldClearPlayer = (focusViewDataIdWhileSuppressed != null) && (currentFocusId != focusViewDataIdWhileSuppressed)

            /**
             * 若当前是视频 & 拖拽缩图轴至其他 Item (不松手),再回到当前 Item 后松手
             * 此时若是自动播放开关为开启状态,则应该播放视频
             */
            val shouldPlay = isSuppressed && shouldAutoPlay()

            when {
                shouldClearPlayer -> clearCurrentPlayer()
                shouldPlay -> {
                    /**
                     * 1.此处仅拖拽缩图轴后, focus 未发生变化时回调
                     * 2.参数中的 activity 仅用于显示下载弹窗
                     * 3.此情况下,下载弹窗应在之前显示过了,此处传 null 不影响
                     */
                    play(null)
                }
            }
            focusViewDataIdWhileSuppressed = null

            launchUpdatePlaybackInfoTask()
        }

        playbackInfo.observeForever {
            seekToPendingPositionIfNeeded()
        }
        // 退出动画的监听
        pageViewModel.pageManagement.exitTransition.observeForever {
            /*
             * 退出动画执行的过程中，如果还在播放视频，会导致动画卡顿问题，因此动画开始的时候将视频暂停播放，而不是等到onDestroy的时候通过release来停止播放。
             * 但是投屏播放的时候，退出不需要暂停。因为该处加上是否为投屏播放的判断。
             */
            photoPlaybackControlManager.getPlayBackControl(selectPhotoPlaybackControlType())
                .takeIf { it.isPlaying() }
                ?.changePlayerState(PlaybackState.PAUSED, PlaybackRequest.PAUSE)
        }

        launch(Dispatchers.Main.immediate) {
            diffedFocusOlineVideoFilterFlow.collectNotNull { diffedFocusViewData ->
                GTrace.trace({ "$TAG.diffedFocusViewData" }) {
                    diffedFocusViewData?.newItem?.id?.takeIf { isOnlinePlayableVideo() }?.let {
                        // 谷歌云视频的判断依赖mediaItem,所以需要在数据加载完,根据mediaItem来决定创建哪个UIController
                        updatePlayBackUiStyleIfNeed()
                    }
                }
            }
        }
    }

    private fun setupPlaybackStatusWhileFocusChanged() {
        // 当焦点视频变化，应该清除之前的播放位置记录
        recordPosition(null)
    }

    /**
     * 选择手机端当前需要操作的播放器。
     *
     * 手机端目前有一种播放器控制流程：
     * [PlaybackControlType.LOCAL_PLAYBACK]：本地图集播放控制
     */
    private fun selectPhotoPlaybackControlType(): PlaybackControlType = PlaybackControlType.LOCAL_PLAYBACK


    /**
     * 尝试根据 [pendingPlaybackPosition] 续播。
     *
     * 注意：
     *
     * - 按正常逻辑来说，应该还要判断 itemPath 是否一致，但现在 PlayerController、Playbackable 没有足够信息，这里没有判断
     * - Player 的信息是异步加载过来的，时机、MediaItem 信息高度保持一致，这里并不是特别严谨
     * - codec 应该尝试复用
     * - 页面跳转时，放大的缩图应该和视频播放到的位置图像保持一致，不然可能会突变
     * - 页面跳转时，放大的缩图是否使用正在播放的视频代替
     * - 以上这些坑，暂未处理，以后打通整体续播流程时再处理吧
     * - Marked by jeasoon
     */
    private fun seekToPendingPositionIfNeeded() {
        // 期望的 position 是无效的，不做处理
        val pendingPosition = pendingPlaybackPosition
        if (pendingPosition < 0) return

        // Player 还没有准备好~不处理，等下次更新。
        val playbackInfo = _playbackInfo.value ?: return
        if (playbackInfo.duration <= 0) return

        // Player 已经准备好了，重置 pendingPosition
        pendingPlaybackPosition = INVALID_PLAYBACK_POSITION

        if (pendingPosition !in (0..playbackInfo.duration)) {
            // pendingPosition 可能是错的，兼容错误
            GLog.e(TAG) {
                "[seekToPendingPositionIfNeeded] pendingPosition out of duration: pendingPosition=$pendingPosition, playbackInfo=$playbackInfo"
            }
            return
        }

        // seek 到目标位置
        photoPlaybackControlManager.curPhotoPlaybackControl.seekTo(pendingPosition)
    }

    private fun registerAutoPlayConfigListener() {
        ConfigAbilityWrapper.registerListener(VIDEO_AUTO_PLAY, autoPlayConfigListener)
    }

    private fun unregisterAutoPlayConfigListener() {
        ConfigAbilityWrapper.unregisterListener(VIDEO_AUTO_PLAY, autoPlayConfigListener)
    }
    /////////////////// 以下为Lifecycle接口 //////////////////////

    override fun onCreate() {
        super.onCreate()
        /**
         * 蜻蜓小大屏接力时，Section重建，但是重用了VM，导致PlayBackStyle未被更新到正确的状态，
         * 需要在Section Created前，更新其到正确状态
         */
        updatePlayBackUiStyleIfNeed()
        updatePlaybackControlManagerStatus()

        registerAutoPlayConfigListener()
    }

    override fun onStart() {
        super.onStart()
        videoSliceRetrieverFactory.apply {
            isProductRetrieverPermitted = true
        }
        audioVolumeController?.let {
            it.addAudioStateChangeListener(playerAudioStateChangeListener)
            it.enableAudioWatcher()
        }
    }

    override fun onStop() {
        super.onStop()
        videoSliceRetrieverFactory.apply {
            isProductRetrieverPermitted = false
            GTrace.trace("$TAG.onPause.releaseAll") {
                releaseAll()
            }
        }
        audioVolumeController?.let {
            it.removeAudioStateChangedListener(playerAudioStateChangeListener)
            it.disableAudioWatcher()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        clearCurrentPlayer()
        unregisterAutoPlayConfigListener()
        //关闭 ability
        closeAbility()
    }

    /**
     * 关闭Ability
     */
    private fun closeAbility() {
        hardwareAbility?.close()
    }

    /**
     * 清除当前 AVController 记录，移除回调等
     */
    override fun onCleared() {
        super.onCleared()
        clearCurrentPlayer()
    }

    /////////////////// 以下为播控接口 //////////////////////
    /**
     * UI触发，执行播放操作
     * @param activity 视频播放可能需要下载，此activity用于显示下载的弹窗
     */
    @UiThread
    fun play(activity: Activity?) {
        downloadAndPlay(activity, isDownloadAction = false)
    }

    /**
     * 下载并播放
     * @param activity 视频播放可能需要下载，此activity用于显示下载的弹窗
     * @param isDownloadAction 点击下载还是点击播放
     * @param onDone 完成回调，不管是否成功，都会执行
     */
    fun downloadAndPlay(activity: Activity?, isDownloadAction: Boolean = true, onDone: (() -> Unit)? = null) {
        if (needDownloadVideoFiles(isDownloadAction)) {
            //数据没准备好，先下载
            downloadVideoFiles(activity, onDone)
            return
        }
        onDone?.invoke()
        playInner()
    }

    private fun isOnlinePlayableVideo(): Boolean {
        return pageViewModel.onlinePlayer.isOnlinePlayableVideo()
    }

    fun isValidPlayer(): Boolean {
        return playbackInfo.value?.isValidPlayer == true
    }

    fun isPlayerLoading(playbackInfo: PlaybackInfo) =
        playbackInfo.isValidPlayer && (LoadingState.LOADING == playbackInfo.playerState)

    /**
     * 播放器是否已经准备就绪：播放器完成第一帧渲染
     */
    fun isPlayerPrepared() = playbackInfo.value?.playerState == LoadingState.LOADED

    /**
     * 真实播放
     */
    private fun playInner() {
        photoPlaybackControlManager.curPhotoPlaybackControl.changePlayerState(PlaybackState.PLAYING, PlaybackRequest.PLAY)
        // 埋点，视频开始播放
        pageViewModel.track.trackPlayBackEvent(
            avKey = photoPlaybackControlManager.curPhotoPlaybackControl.playerToken,
            action = IVideoPlayProductTracker.ACTION_PLAY
        )

        // 埋点，视频播放时静音状态
        pageViewModel.track.trackPlayBackEvent(
            avKey = photoPlaybackControlManager.curPhotoPlaybackControl.playerToken,
            action = if (photoPlaybackControlManager.curPhotoPlaybackControl.isMute()) {
                IVideoPlayProductTracker.ACTION_MUTE
            } else {
                IVideoPlayProductTracker.ACTION_UNMUTE
            }
        )
    }

    /**
     * 是否需要下载视频
     */
    private fun needDownloadVideoFiles(isDownloadAction: Boolean): Boolean {
        // 谷歌云视频时,isReady=true,但谷歌云视频不应该下载，应是播放
        return (playbackInfo.value?.mediaSourceState?.isNotReady == true) || (isDownloadAction && isOnlinePlayableVideo())
    }

    /**
     * 下载视频文件,包括处理云端下载和共享图集下载
     */
    private fun downloadVideoFiles(activity: Activity?, onDone: (() -> Unit)? = null) {
        val photoItemViewData = pageViewModel.dataLoading.focusItemViewData
        photoItemViewData ?: run {
            onDone?.invoke()
            GLog.w(TAG, "downloadVideoFiles->photoItemViewData is null ")
            return
        }
        val mediaItem = mediaItemGetter.invoke(photoItemViewData)
        mediaItem ?: run {
            onDone?.invoke()
            GLog.w(TAG, "downloadVideoFiles->mediaItem is null ")
            return
        }
        if (isSharePhotoScene()) {
            pageViewModel.sharedMedia.startDownloadSharedOriginFiles(mediaItem)
            onDone?.invoke()
        } else {
            downloadCloudVideoFiles(activity, onDone)
        }
    }

    /**
     * 下载云端视频文件
     * @param activity 弹窗用
     */
    private fun downloadCloudVideoFiles(activity: Activity?, onDone: (() -> Unit)?) {
        if (activity == null) {
            onDone?.invoke()
            GLog.w(TAG, "downloadCloudVideoFiles but activity is null")
            return
        }
        pageViewModel.cloudSync.triggerDownloadOriginal(
            activity = activity,
            scene = FileProcessScene.DOWNLOAD_ORIGIN_SCENE_PLAY_VIDEO,
            finishListener = object : FinishListener {
                override fun onFinished(success: Boolean, errCode: Int, errMsg: String?) {
                    onDone?.invoke()
                    if (success) {
                        pageViewModel.pageManagement.changeImmersionInteractive(true)
                        playInner()
                    } else {
                        GLog.w(TAG, "downloadVideoFiles but fail, errCode:$errCode errMsg:$errMsg")
                    }
                }
            }
        )
    }

    /**
     * UI触发，执行暂停操作
     */
    @UiThread
    fun pause() {
        photoPlaybackControlManager.curPhotoPlaybackControl.changePlayerState(PlaybackState.PAUSED, PlaybackRequest.PAUSE)

        // 埋点视频暂停播放
        pageViewModel.track.trackPlayBackEvent(
            avKey = photoPlaybackControlManager.curPhotoPlaybackControl.playerToken,
            action = IVideoPlayProductTracker.ACTION_PAUSE
        )
    }

    /**
     * UI触发，执行播放状态切换操作。
     * ```
     * 当前是播放状态，则执行暂停操作；
     * 当期是暂停状态，则执行播放操作。
     * ```
     * @param activity 视频播放可能需要下载，此activity用于显示下载的弹窗
     */
    @UiThread
    fun togglePlay(activity: Activity?) {
        if (isPlaying()) {
            pause()
        } else {
            play(activity)
        }
    }

    /**
     * UI触发，执行静音操作
     */
    @UiThread
    fun mute() {
        audioVolumeController?.mute()

        // 产品埋点： 视频静音状态
        pageViewModel.track.trackPlayBackEvent(
            avKey = photoPlaybackControlManager.curPhotoPlaybackControl.playerToken,
            action = IVideoPlayProductTracker.ACTION_MUTE
        )
    }

    /**
     * UI触发，执行取消静音操作
     */
    @UiThread
    fun unmute() {
        audioVolumeController?.unmute()
        audioVolumeController?.enableAudioFocusGrabIfUnmute()
        // 产品埋点： 视频静音状态
        pageViewModel.track.trackPlayBackEvent(
            avKey = photoPlaybackControlManager.curPhotoPlaybackControl.playerToken,
            action = IVideoPlayProductTracker.ACTION_UNMUTE
        )
    }

    /**
     * UI触发，执行静音状态切换操作。
     * ```
     * 当前是静音状态，则执行取消静音操作；
     * 当期是取消静音状态，则执行静音操作。
     * ```
     */
    @UiThread
    fun toggleMute() {
        if (_playbackInfo.value?.isMuted == true) {
            unmute()
        } else {
            mute()
        }
    }

    /**
     * UI触发，快进操作
     * 可配合 [enterPreviewSeeking] 和 [exitPreviewSeeking] 使用
     */
    @UiThread
    fun seek(percent: Double) {
        val duration = photoPlaybackControlManager.curPhotoPlaybackControl.getDuration()
        var targetPercent = percent.coerceIn(SEEK_PERCENT_MIN, SEEK_PERCENT_MAX)
        if ((duration > TimeUtils.TIME_30_MIN_IN_MS) && isSupportThumbLine.not()) {
            /**
             * 视频时长大于30分钟，则百分比保留3位小数点，最后一位舍弃，降低精度，修复长视频松后画面跳动问题。
             * 缩图轴因为二级展开态view比较宽，30分钟以上不需要降低精度
             */
            targetPercent = targetPercent.toScale(AppConstants.Number.NUMBER_3, RoundingMode.FLOOR)
        }
        val targetPosition = (targetPercent * duration).toLong()
        photoPlaybackControlManager.curPhotoPlaybackControl.seekTo(targetPosition)
    }

    /**
     * UI触发，快进操作
     */
    @UiThread
    fun seek(position: Long) {
        val targetPosition = position.coerceIn(0L, photoPlaybackControlManager.curPhotoPlaybackControl.getDuration())
        photoPlaybackControlManager.curPhotoPlaybackControl.seekTo(targetPosition)
    }

    /**
     * UI触发，进入预览快进模式，后续 seek 有即时预览
     */
    @UiThread
    fun enterPreviewSeeking() {
        photoPlaybackControlManager.curPhotoPlaybackControl.enterPreviewSeeking()
    }

    /**
     * UI触发，退出预览快进模式，后续 seek 无即时预览
     */
    @UiThread
    fun exitPreviewSeeking() {
        photoPlaybackControlManager.curPhotoPlaybackControl.exitPreviewSeeking()
    }

    /**
     * UI触发，焦点变更，当丢失、获取焦点后有不同操作
     *
     * 注意：这是 WindowFocus，不是 slotFocus！
     *
     * 注意：这是 WindowFocus，不是 slotFocus！
     *
     * 注意：这是 WindowFocus，不是 slotFocus！
     *
     * 1. 重新获取焦点 && 之前失去焦点时正在播放 -> 静音 + 播放
     * 2. 失去焦点 && 正在播放 -> 暂停 + 记录正在播放状态
     *
     * @param isFocused 是否获取焦点
     * @param isForceToContinuePlaying 是否强制继续播放
     */
    @UiThread
    fun notifyWindowFocusChanged(isFocused: Boolean, isForceToContinuePlaying: Boolean = false) {
        photoPlaybackControlManager.curPhotoPlaybackControl.notifyWindowFocusChanged(isFocused, isForceToContinuePlaying)
    }

    /**
     * 通知切换音量状态
     * @param isMuted 通知播放器切换是否需要静音
     */
    internal fun notifyAudioStateChanged(isMuted: Boolean) {
        audioVolumeController?.apply {
            if (isMuted) {
                mute()
            } else {
                unmute()
                enableAudioFocusGrabIfUnmute()
            }
        }
    }

    /**
     * 通知切换请求音频焦点的状态，相册中存在不需要请求音频焦点的场景，
     * @param isNeedRequest 需要请求音频焦点
     */
    internal fun changeAudioFocusRequestState(isNeedRequest: Boolean) {
        audioVolumeController?.apply {
            needRequestAudioFocus(isNeedRequest)
        }
    }

    /**
     * 通知页面滑动到另一图片
     * @param from 滑动起始图片位置
     * @param to 滑动终止图片位置
     */
    internal fun notifyPhotoSlide(from: Int, to: Int) {
        if ((from == INVALID_INDEX) ||
            (to == INVALID_INDEX) ||
            (abs(from - to) != 1)
        ) {
            /**
             * 不是滑动到另一张图片，直接退出
             * - from 无效
             * - to 无效
             * - 二者差值不是 1
             * 满足任意一个条件，即认为不是滑动到另一张图片。
             */
            return
        }
        if (isFocusSlotVideo().not()) {
            /**
             * 不是视频，直接退出
             */
            return
        }

        // 产品埋点，用户滑动事件导致的视频播放结束
        if (from > to) {
            pageViewModel.track.trackPlayBackEvent(
                action = ACTION_FINISH,
                actionData = VIDEO_PLAYER_FINISH_SWITCH_TO_PRE
            )
        } else if (to > from) {
            pageViewModel.track.trackPlayBackEvent(
                action = ACTION_FINISH,
                actionData = VIDEO_PLAYER_FINISH_SWITCH_TO_NEXT
            )
        }
    }

    /**
     * 记录 AVController 的状态
     */
    @MainThread
    internal fun recordPosition(position: Long? = null) {
        GLog.d(TAG) { "[recordPosition] position=$position" }
        photoPlaybackControlManager.recordPosition(selectPhotoPlaybackControlType(), expectPosition = position)
    }

    internal fun getCurrentPosition(): Long {
        return photoPlaybackControlManager.curPhotoPlaybackControl.getCurrentPosition()
    }

    /**
     * 清除 AVController 记录，移除回调等
     */
    @MainThread
    internal fun clearCurrentPlayer(avController: AVController? = null) {
        photoPlaybackControlManager.clearCurrentPlayer(selectPhotoPlaybackControlType(), avController)
        launchUpdatePlaybackInfoTask()

        // 产品埋点，清除当前播放器
        pageViewModel.track.changeCurrentController(null)
    }

    /**
     * 切换 AVController
     * @param avController
     */
    @MainThread
    internal fun changeCurrentPlayer(avController: AVController) {
        photoPlaybackControlManager.changeCurrentPlayer(selectPhotoPlaybackControlType(), avController)
        //切换播放器需要重置视频缩图准备状态为false才可以刷新预览进度
        isSliceRetrieverInitialized = false
        /**
         * 防止[clearCurrentPlayer]在[changeCurrentPlayer]之后调用，导致设置好的AvPlayer被清除
         */
        focusViewDataIdWhileSuppressed = null

        launchUpdatePlaybackInfoTask()

        // 大图整体视频播放逻辑改为大于等于1s循环播放
        val duration = photoPlaybackControlManager.curPhotoPlaybackControl.getDuration()
        if ((duration > 0) && (duration < LOOP_PLAY_MIN_TIME)) {
            photoPlaybackControlManager.curPhotoPlaybackControl.setLooping(false)
        } else {
            photoPlaybackControlManager.curPhotoPlaybackControl.setLooping(true)
        }

        // 产品埋点，当前焦点的播放器改变
        pageViewModel.track.changeCurrentController(avController.hashCode())
    }

    /**
     * 当前视频缩图是否已经开始预览
     */
    internal fun isPlaybackPreviewStarted() = isSliceRetrieverInitialized

    /**
     * 通知视频缩图条可见性变化
     */
    internal fun notifyAlterThumbProgressBarVisibilityChange(isVisible: Boolean) {
        _isAlterThumbProgressBarVisible.tryEmit(isVisible)
    }

    /////////////////// 以下为内部私有方法定义 //////////////////////

    private fun launchUpdatePlaybackInfoTask() {
        var task: Runnable? = null
        task = Runnable {
            if (updatePlaybackInfoTaskRef.compareAndSet(task, null).not()) {
                GLog.d(TAG) { "[launchUpdatePlaybackInfoTask] task replaced, drop" }
                return@Runnable
            }
            if (isPlaybackSuppressed && isFocusChangedWhileSuppressed) {
                _playbackInfo.postValue(null)
                return@Runnable
            }
            val playbackInfo = doLoadPlaybackInfo()

            //是否 视频播放流畅
            playbackInfo?.let {
                checkVideoPlaySmoothly(it)
            }

            // player change
            if (_playbackInfo.value?.isPlayerTokenEquals(playbackInfo) != true) {
                isSliceRetrieverInitialized = false
            }
            // playbackInfo change PlayState变更也需要通知出去（业务详情页 需要该状态做封面到视频内容的动画）
            if (_playbackInfo.value != playbackInfo) {
                _playbackInfo.postValue(playbackInfo)
            }
        }

        if (updatePlaybackInfoTaskRef.compareAndSet(null, task)) {
            if (isPlaybackSuppressed && isFocusChangedWhileSuppressed) {
                updatePlaybackInfoTaskRef.compareAndSet(task, null)
                _playbackInfo.setOrPostValue(null)
                return
            }
            // GLog.d(TAG) { "[launchUpdatePlaybackInfoTask] launching task" }
            /**
             * 前提：
             *    doLoadPlaybackInfo 内部都是实时获取参数并更新，所以 Runnable 并无差别，但要求
             *    必须是任务执行必须是单线程的，不然可能会有执行时序问题
             * 条件：
             *    设置成功 -> 说明前面没有任务待执行，直接安排执行即可，即 compareAndSet 返回 true
             *    否则    -> 说明前面已经安排过任务了，但是还没执行到，所以抛弃即可，即 compareAndSet 返回 false
             */
            queueWorkScope.launch { task.run() }
        } else {
            GLog.d(TAG) { "[launchUpdatePlaybackInfoTask] task exists, skip" }
        }
    }

    /**
     * 视频播放流畅
     * @param playbackInfo
     */
    private fun checkVideoPlaySmoothly(playbackInfo: PlaybackInfo) {
        if (_playbackInfo.value?.isPlayerTokenEquals(playbackInfo) != true ||
            _playbackInfo.value?.isPlayerSizeEquals(playbackInfo) != true
        ) {
            if ((playbackInfo.videoWidth <= 0) || (playbackInfo.videoHeight <= 0)
                || (playbackInfo.videoFps <= 0f) || (playbackInfo.videoFps.isNaN())
            ) {
                GLog.w(TAG, LogFlag.DL) {
                    "[checkVideoPlaySmoothly] playbackInfo size or videoFps is wrong or NaN, return!"
                }
                return
            }
            if (hasUnsmoothlyToastShown.not() && isVideoSpecificationSupported(playbackInfo).not()) {
                _shouldShowVideoUnsmoothlyTips.setOrPostValue(true)
                hasUnsmoothlyToastShown = true
            }
        }
    }

    /**
     * 视频的尺寸、帧率、编码格式是否支持
     * @param playbackInfo 视频的尺寸、帧率
     *
     */
    private fun isVideoSpecificationSupported(playbackInfo: PlaybackInfo): Boolean {
        val focusItemViewData = pageViewModel.dataLoading.focusItemViewData ?: return false
        val mediaItem = mediaItemGetter(focusItemViewData) ?: return false
        if (TextUtils.isEmpty(mediaItem.codecType).not()) {
            val videoSpec = VideoSpec(playbackInfo.videoWidth, playbackInfo.videoHeight, playbackInfo.videoFps)
            val options = VideoOptions(mediaItem.codecType, false, false)
            return CodecSupport.isSupport(videoSpec, options).apply {
                GLog.d(TAG, LogFlag.DL) {
                    "[isVideoSpecificationSupported]: codecType=${mediaItem.codecType},videoSpec:$videoSpec,options:$options,isSupport:$this"
                }
            }
        }
        GLog.w(TAG, LogFlag.DL) { "[isVideoSpecificationSupported] photoItemViewData is error or mediaItem data wrong or size not match" }
        return true
    }

    private fun doLoadPlaybackInfo(): PlaybackInfo? {
        if (isValidPlaybackInfo.not()) {
            return null
        }
        val curPlaybackControl = photoPlaybackControlManager.curPhotoPlaybackControl
        val playerToken = curPlaybackControl.playerToken
        val playingInfo = curPlaybackControl.getCurrentPlayingInfo()
        val isPlaying = playingInfo.isPlaying
        val duration = getVideoDuration(playingInfo)
        val position = playingInfo.currentPosition
        val isMute = playingInfo.isMute
        val isSeeking = playingInfo.isSeeking
        val isHDRVideo = playingInfo.isHDRVideo
        val videoWidth = playingInfo.videoWidth
        val videoHeight = playingInfo.videoHeight
        val videoFrameRate = playingInfo.videoFrameRate

        val positionPercent = when {
            (duration <= 0L) -> SEEK_PERCENT_MIN
            (position >= duration) -> SEEK_PERCENT_MAX
            else -> (position.toDouble() / duration).coerceIn(SEEK_PERCENT_MIN, SEEK_PERCENT_MAX)
        }

        val isAudioValid = isAudioValid(playingInfo)

        val mediaSourceState = getMediaSourceState()

        val localFileStatus = getLocalFileStatus()

        return PlaybackInfo(
            playerToken = playerToken,
            isMuted = isMute,
            isPlaying = isPlaying,
            isSeeking = isSeeking,
            duration = duration,
            positionInPercent = positionPercent,
            isAudioValid = isAudioValid,
            isHDRVideo = isHDRVideo,
            mediaSourceState = mediaSourceState,
            localFileStatus = localFileStatus,
            videoWidth = videoWidth,
            videoHeight = videoHeight,
            videoFps = videoFrameRate,
            shouldPlayAfterPrepared = playingInfo.shouldPlayAfterPrepared,
            shouldSliceForceInvalidate = (isSliceRetrieverInitialized.not()) && (_isSupportPlaybackPreview.value == true),
            playerState = playingInfo.loadingState,
            isValidPlayer = playingInfo.isValidPlayer,
            isPlaybackAt120Fps = playingInfo.isPlaybackAt120Fps,
            playbackState = playingInfo.playbackState
        )
    }

    private fun getMediaSourceState(): MediaSourceState {
        return pageViewModel.dataLoading.focusItemViewData?.let { mediaItemGetter(it) }?.let {
            if (it.isVideoReady()) MediaSourceState.READY else MediaSourceState.NOT_READY
        } ?: MediaSourceState.UNKNOWN
    }

    /**
     * 获取视频的总时长
     * 针对投屏场景时，如果HeyCast SDK返回的时长为0时，视频总时长就用本地的视频时长
     * durationInSec : 从PhotoViewModel里拿到的视频时长单位是s, 因为对播放器来说默认都是ms,
     * 所以需要将s乘以1000转换成ms
     */
    private fun getVideoDuration(playingInfo: AVController.PlayingInfo): Long =
        if (playingInfo.duration > 0L) {
            playingInfo.duration
        } else {
            (pageViewModel.dataLoading.focusItemViewData?.duration ?: 0L) * TimeUtils.MILLISECOND_IN_SECOND
        }

    private fun getLocalFileStatus(): Int {
        return pageViewModel.dataLoading.focusItemViewData?.let { mediaItemGetter(it) }?.localFileStatus ?: LOCAL_FILE_STATUS_DEFAULT
    }

    /**
     * 是否允许播放视频
     * 谷歌云端在线视频：
     * 1.移动网络时，弹窗提示，用户同意后，可继续播放，进程内不再提示弹窗；
     * 2.冷启动后，会继续弹窗提示
     */
    private fun isAllowPlay(): Boolean {
        return pageViewModel.onlinePlayer.isAllowPlay()
    }

    /**
     * 音频是否可用
     * 慢动作、延时摄影、无效视频音频不可用
     */
    private fun isAudioValid(playingInfo: AVController.PlayingInfo): Boolean {
        val loadingState = playingInfo.loadingState

        if ((loadingState == AVController.LoadingState.INIT)
            || (loadingState == AVController.LoadingState.LOADING)
        ) {
            return true
        }

        val isVideoLoadFailed = (playingInfo.duration <= 0)
            || (playingInfo.loadingState == AVController.LoadingState.FAILED)

        val mediaItem = pageViewModel.dataLoading.focusItemViewData?.let(mediaItemGetter)
        val isSlowMotion = MediaItemUtils.isItemSupportFormat(mediaItem, Constants.IMediaItemSupportFormat.FORMAT_SLOW_MOTION)
        val isFastVideo = MediaItemUtils.isItemSupportFormat(mediaItem, Constants.IMediaItemSupportFormat.FORMAT_FAST_VIDEO)

        return !isSlowMotion && !isFastVideo && !isVideoLoadFailed
    }

    private val AVController.PlayingInfo.isHDRVideo: Boolean
        get() = (colorTransfer == COLOR_TRANSFER_ST2084) || (colorTransfer == COLOR_TRANSFER_HLG)

    private fun doScheduleRefreshPlaybackInfo() {
        val photoPlaybackControl = photoPlaybackControlManager.curPhotoPlaybackControl
        if (photoPlaybackControl.isPlayerSeeking()) {
            GLog.d(TAG, "[doScheduleRefreshPlaybackInfo] not schedule when player Seeking")
            return
        }
        if (photoPlaybackControl.isInvalidPlayer()) {
            GLog.d(TAG, "[doScheduleRefreshPlaybackInfo] not schedule, not a valid player")
            return
        }
        // Marked by: 2021/10/27 暂未考虑帧率变化情况
        choreographer.removeFrameCallback(frameCallback)
        choreographer.postFrameCallback(frameCallback)
    }

    private fun doCancelScheduleRefreshPlaybackInfo() {
        choreographer.removeFrameCallback(frameCallback)
    }

    private fun updatePlayBackUiStyleIfNeed() {
        val playbackStyle = when {
            isSupportThumbLine -> PlaybackUiStyle.THUMB_LINE
            else -> PlaybackUiStyle.DEFAULT_SEEKBAR
        }
        if (playbackUiStyle.value != playbackStyle) {
            _playbackUiStyle.postValue(playbackStyle)
        }
    }

    private fun updatePlaybackControlManagerStatus() {
        photoPlaybackControlManager.shouldResetToStartWhenEnd = isSupportThumbLine.not()
    }

    /**
     * 视频是否自动播放，如果[feature]中指定了，使用[feature]，否则获取用户配置项。
     * Marked: 目前先使用 [VideoAutoPlayHelper.isVideoAutoPlay],后续应统一使用配置能力
     */
    private fun shouldAutoPlay(): Boolean =
        pageViewModel.inputArguments.features.value?.shouldAutoPlay ?: VideoAutoPlayHelper.isVideoAutoPlay

    /**
     * 当前大图显示的是否时视频
     */
    private fun isFocusSlotVideo(): Boolean {
        return pageViewModel.dataLoading.focusItemViewData?.let(mediaItemGetter)?.mediaType?.equals(
            MEDIA_TYPE_VIDEO
        ) ?: false
    }

    internal fun isPlaying(): Boolean {
        return _playbackInfo.value?.isPlaying == true
    }

    /**
     * 当前是否是共享图集场景
     */
    private fun isSharePhotoScene(): Boolean {
        return pageViewModel.inputArguments.dataSource.value?.whichDataSet == SHARED_ALBUM
    }

    /**
     * Player异常事件的回调
     * @param avController AVController
     * @param what Int
     * @param extra Int
     * @param details String
     */
    private fun onPlayerError(avController: AVController, what: Int, extra: Int, details: String) {
        dealTblErrorReason(avController, what, extra)
    }

    /**
     * 处理由TBL错误码导致的播放异常
     *
     * @param avController AVController
     * @param what Int
     * @param extra Int
     */
    private fun dealTblErrorReason(avController: AVController, what: Int, extra: Int) {
        GLog.d(TAG) { "[playerTblErrorReason], current error code is:[$what:$extra]" }
        when (extra) {
            // 1.ffmpeg视频解码失败播放器报错code 2.不支持的封装格式，demux解封装失败 3.不支持的视频格式，如杜比视频的profile 20格式
            TBL_ERROR_REASON_RD_VIDEO_FF_DECODER, TBL_ERROR_REASON_RD_VIDEO_UNSUPPORT, TBL_ERROR_REASON_RD_VIDEO_OVER_SPEC ->
                shouldShowPlayFailedToast(avController)
            // 当播放器异常，且需要重建播放器才能恢复时，播放器报错code
            TBL_ERROR_REASON_RD_VIDEO, TBL_ERROR_REASON_RD_VIDEO_MC_INIT, TBL_ERROR_REASON_RD_VIDEO_MC_QUERY -> {
                shouldReloadFocusVideoResource(avController)
            }

            else -> Unit
        }
    }

    /**
     * 校验是否需要弹出"播放失败"的toast提示：ffmpeg视频解码失败时弹出，错误码313000
     *
     * @param avController AVController
     */
    private fun shouldShowPlayFailedToast(avController: AVController) {
        if (photoPlaybackControlManager.curPhotoPlaybackControl.playerToken == avController.playerToken()) {
            runOnUiThread { ToastUtil.showShortToast(R.string.photopage_playback_failed) }
        }
    }

    /**
     * 校验是否需要重新加载视频资源
     *
     * 频繁折叠支持接力的手机（如xueying）此时可能surface已经被销毁了，但是依然在播放视频。此时视频会从硬解切换到软解
     * surface重新创建后也不会切换回来。
     * 该场景下，杜比视频色彩差异较大，该问题必须要修复。因此做了这个兼容处理。
     *
     * @param avController AVController
     */
    private fun shouldReloadFocusVideoResource(avController: AVController) {
        if (photoPlaybackControlManager.curPhotoPlaybackControl.playerToken != avController.playerToken()) {
            GLog.d(TAG) { "[shouldReloadFocusVideoResource] player error not current playing, ignore." }
            return
        }

        val focusSlotViewData = pageViewModel.dataLoading.focusItemViewData
        if (focusSlotViewData?.isDolbyVision() != true) {
            GLog.d(TAG) { "[shouldReloadFocusVideoResource] focus is not dolby video, should ignore." }
            return
        }

        focusVideoRequestReloadResourceTimes++
        if (focusVideoRequestReloadResourceTimes > FOCUS_RELOAD_PLAYER_RESOURCE_MAX_TIMES) {
            GLog.d(TAG) { "[shouldReloadFocusVideoResource] reload times exceeds $focusVideoRequestReloadResourceTimes" }
            return
        }

        GLog.d(TAG) { "[shouldReloadFocusVideoResource] should release and reload resource $focusVideoRequestReloadResourceTimes" }
        _shouldReloadFocusVideoResource.setOrPostValue(true)
    }

    /**
     * 播放事件监听器，用于接收当前播放器的相关事件。
     * - 异常。
     * - 输出信息。
     * - 状态改变。
     */
    private class InternalPlayerEventListener(
        private val viewModel: PhotoViewModel
    ) : AVController.OnEventListener {
        /**
         * Player异常的回调。
         */
        var onPlayerErrorCallback: OnPlayerErrorCallback? = null

        /**
         * 埋点VM
         */
        private val tracker: IVideoPlayProductTracker get() = viewModel.track

        override fun onError(avController: AVController, what: Int, extra: Int, details: String) {
            onPlayerErrorCallback?.invoke(avController, what, extra, details)
        }

        override fun onInfo(avController: AVController, what: Int, extra: Int, details: String) {
            when (what) {
                AVController.INFO_START_RENDER -> {
                    // 产品埋点： 视频开始渲染
                    tracker.trackPlayBackEvent(
                        avKey = avController.hashCode(),
                        action = IVideoPlayProductTracker.ACTION_RENDER
                    )
                }

                else -> Unit
            }

            // 产品埋点 ：视频播放输出信息
            tracker.trackPlayBackEvent(
                avKey = avController.hashCode(),
                action = IVideoPlayProductTracker.ACTION_INFO,
                actionData = AVPlayer.infoCodeToString(what)
            ) {
                putString(IVideoPlayProductTracker.EXTRA, AVPlayer.infoCodeToString(extra))
                putString(IVideoPlayProductTracker.DETAILS, details)
            }
        }

        override fun onPlaybackStateChanged(avController: AVController, state: AVController.PlaybackState) {
            when (state) {
                AVController.PlaybackState.COMPLETED -> {
                    /**
                     * 当播放完成时：缩图轴模式下，不需要置0，以避免缩图轴回滚现象导致的交互问题
                     */
                    if (viewModel.playback.playbackUiStyle.value != PlaybackUiStyle.THUMB_LINE) {
                        avController.seekTo(0)
                    }
                    // 产品埋点：视频正常播放完毕
                    tracker.trackPlayBackEvent(
                        avKey = avController.hashCode(),
                        action = ACTION_FINISH,
                        actionData = PictureTrackConstant.Value.VIDEO_PLAYER_FINISH_NORMAL_COMPLETE
                    )
                }

                else -> Unit
            }
        }
    }

    /**
     * 播控ui风格枚举
     */
    internal enum class PlaybackUiStyle {
        /**
         * 缩略图轴风格
         */
        THUMB_LINE,

        /**
         * 多帧预览风格，
         * 因缩图加载会有慢的问题，与设计沟通后目前不使用此样式，统一使用 DEFAULT_SEEKBAR。
         */
        THUMBNAIL_SEEKBAR,

        /**
         * 原生seekBar风格
         */
        DEFAULT_SEEKBAR;
    }

    private companion object {

        const val UNDEFINED_AUDIO_CONTROLLER_NAME = "undefined_audio_controller"
        const val PHOTO_PAGE_AUDIO_CONTROLLER_NAME = "photo_page_audio_controller"

        private const val TAG = "PhotoPlaybackViewModel"

        private const val SEEK_PERCENT_MIN = 0.0
        private const val SEEK_PERCENT_MAX = 1.0

        private const val PROP_PREVIEW_TYPE = "debug.gallery.videoplayer.seekbar"
        private const val PREVIEW_TYPE_DEFAULT = "default"
        private const val PREVIEW_TYPE_THUMBNAIL = "thumbnail"

        private const val INVALID_PLAYBACK_POSITION: Long = -1L

        /**
         * 定义播放器的错误类型。当前错误类型是TBL给的
         */
        private const val TBL_ERROR_REASON_RD_VIDEO = 300000 //视频渲染失败
        private const val TBL_ERROR_REASON_RD_VIDEO_MC_INIT = 301000 // Decoder Initialization exception
        private const val TBL_ERROR_REASON_RD_VIDEO_MC_QUERY = 302000 // Decoder Query Exception.
        private const val TBL_ERROR_REASON_RD_VIDEO_FF_DECODER = 313000 //ffmpeg视频解码失败错误码
        private const val TBL_ERROR_REASON_RD_VIDEO_OVER_SPEC = 315000 //不支持的格式，如杜比视频的profile 20格式
        private const val TBL_ERROR_REASON_RD_VIDEO_UNSUPPORT = 21000 //不支持的封装格式，demux解封装失败


        /**
         * 在视频播放异常需要重新加载资源时，同一个资源，最大重新加载次数。
         */
        private const val FOCUS_RELOAD_PLAYER_RESOURCE_MAX_TIMES = 5

        private val emptyThumbnailRetriever: ISliceRetriever = ISliceRetriever.EmptySliceRetriever
    }
}