/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoMenuDefinition.kt
 ** Description : 大图菜单信息定义
 ** Version     : 1.0
 ** Date        : 2022/02/17
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>@Apps.Gallery              2022/02/17  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.menucontrol

import android.content.res.Resources
import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.os.Bundle
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_AI_ID
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_CLOUD_DOWNLOAD
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_CONVERT_PDF
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_COPY
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_CSHOT
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_CUSTOM_REMOVE_WIDGET_LIST
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_DELETE
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_DLNA
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_EDIT
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_ENHANCE_TEXT
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_EXPORT_OLIVE
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_EXPORT_VIDEO
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_FAVORITES
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_FREE_FACE
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_GOOGLE_PASS_SCAN
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_GROUP_PHOTO
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_IMPORT
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_INFO
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_JIGSAW
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_MORE
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_MOVE
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_OLIVE_PHOTO
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_OPEN_IN_SYSTEM_PLAYER
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_PORTRAIT_BLUR
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_PREVIEW_CHECK
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_PREVIEW_SELECT
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_RECOMMENDED_REMOVE_WIDGET_LIST
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_REMOVE_LABEL
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_RENAME_FILE
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_SAFE_BOX
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_SAVE
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_SAVE_TO_GIF
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_SELF_SPLIT
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_SETAS_CONTACT
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_SETAS_WALLPAPER
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_SET_COVER
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_SHARE
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_SHARED_MEDIA_DELETE
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_SHARED_MEDIA_SAVE
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_SHARED_MEDIA_TIP
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_SLIDESHOW
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_TRANSFORM_HEIF_TO_JPEG
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_TRANSFORM_TO_SDR
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_VIDEO_GET_FRAME
import com.oplus.gallery.foundation.util.datastructure.LongFlags
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.ui.section.photomenu.MenuIcon
import com.oplus.gallery.photo_page.ui.theme.MenuDecorationTheme
import com.oplus.gallery.basebiz.R as baseR

/**
 * 大图菜单信息表
 */
internal class PhotoMenuItemInfoTable {

    /**
     * 菜单项信息表
     *
     * TODO 配置表应该从asset/photo_menu_configs.xml读取到SharedPreferences中，
     *      然后从SharedPreferences中读取到menuItemConfigs
     */
    private val menuItemConfigs: Map<Int, MenuItemInfo> = mutableMapOf<Int, MenuItemInfo>().also {
        fun register(
            action: Int,
            support: Long,
            isCheckable: Boolean,
            shouldShowNewTip: Boolean,
            isNeverUsedByUser: Boolean,
            menuIcon: MenuIcon? = null
        ) {
            it[action] = MenuItemInfo(action, support, isCheckable, shouldShowNewTip, isNeverUsedByUser, menuIcon)
        }

        register(
            R.id.action_google_pass_scan,        OPERATION_SUPPORT_GOOGLE_PASS_SCAN,
            isCheckable = false, shouldShowNewTip = false, isNeverUsedByUser = true
        )
        register(
            R.id.action_recycle,                 OPERATION_SUPPORT_DELETE,
            isCheckable = false, shouldShowNewTip = false, isNeverUsedByUser = true,
            menuIcon = MenuIcon.Static(R.drawable.photopage_ic_menu_recycle_selector, R.drawable.photopage_ic_menu_recycle_selector_light)
        )
        register(R.id.action_restore_recycled,   OPERATION_SUPPORT_DELETE,
            isCheckable = false, shouldShowNewTip = false, isNeverUsedByUser = true,
            menuIcon = MenuIcon.Static(R.drawable.photopage_ic_menu_restore_selector, R.drawable.photopage_ic_menu_restore_selector_light)
        )
        register(R.id.action_delete_recycled,    OPERATION_SUPPORT_DELETE,
            isCheckable = false, shouldShowNewTip = false, isNeverUsedByUser = true,
            menuIcon = MenuIcon.Static(R.drawable.photopage_ic_menu_recycle_selector, R.drawable.photopage_ic_menu_recycle_selector_light)
        )
        register(
            com.oplus.gallery.basebiz.R.id.action_free_face_from_group,    OPERATION_SUPPORT_DELETE,
            isCheckable = false, shouldShowNewTip = false, isNeverUsedByUser = true
        )
        register(
            R.id.action_share,                   OPERATION_SUPPORT_SHARE,
            isCheckable = false, shouldShowNewTip = false, isNeverUsedByUser = true,
            menuIcon = MenuIcon.Static(R.drawable.photopage_ic_menu_share_selector, R.drawable.photopage_ic_menu_share_selector_light)
        )
        register(
            R.id.action_setas_wallpaper,         OPERATION_SUPPORT_SETAS_WALLPAPER,
            isCheckable = false, shouldShowNewTip = false, isNeverUsedByUser = true
        )
        register(
            R.id.action_transform_heif_to_jpeg,        OPERATION_SUPPORT_TRANSFORM_HEIF_TO_JPEG,
            isCheckable = false, shouldShowNewTip = true, isNeverUsedByUser = true
        )
        register(
            R.id.action_edit,                    OPERATION_SUPPORT_EDIT,
            isCheckable = false, shouldShowNewTip = true, isNeverUsedByUser = true,
            menuIcon = MenuIcon.Static(R.drawable.photopage_ic_menu_edit_selector, R.drawable.photopage_ic_menu_edit_selector_light)
        )
        register(
            R.id.action_details,                 OPERATION_SUPPORT_INFO,
            isCheckable = true, shouldShowNewTip = false, isNeverUsedByUser = true,
            menuIcon = MenuIcon.Static(R.drawable.photopage_ic_menu_details_selector, R.drawable.photopage_ic_menu_details_selector_light)
        )
        register(
            R.id.action_import,                  OPERATION_SUPPORT_IMPORT,
            isCheckable = false, shouldShowNewTip = false, isNeverUsedByUser = true,
            menuIcon = MenuIcon.Static(R.drawable.photopage_ic_menu_import_selector, R.drawable.photopage_ic_menu_import_selector_light)
        )
        register(
            R.id.action_favorites,               OPERATION_SUPPORT_FAVORITES,
            isCheckable = true,  shouldShowNewTip = false, isNeverUsedByUser = true,
            menuIcon = MenuIcon.Static(R.drawable.photopage_ic_menu_favorites_selector, R.drawable.photopage_ic_menu_favorites_selector_light)
        )
        register(
            R.id.action_setas_contact,           OPERATION_SUPPORT_SETAS_CONTACT,
            isCheckable = false, shouldShowNewTip = false, isNeverUsedByUser = true
        )
        register(
            com.oplus.gallery.basebiz.R.id.action_encrypt,                 OPERATION_SUPPORT_SAFE_BOX,
            isCheckable = false, shouldShowNewTip = false, isNeverUsedByUser = true
        )
        register(
            R.id.action_more,                    OPERATION_SUPPORT_MORE,
            isCheckable = false, shouldShowNewTip = true,  isNeverUsedByUser = true,
            menuIcon = MenuIcon.Static(R.drawable.photopage_ic_menu_more_selector, R.drawable.photopage_ic_menu_more_selector_light)
        )
        register(
            R.id.action_slideshow,               OPERATION_SUPPORT_SLIDESHOW,
            isCheckable = false, shouldShowNewTip = false, isNeverUsedByUser = true
        )
        register(
            com.oplus.gallery.basebiz.R.id.action_free_face_from_group,    OPERATION_SUPPORT_FREE_FACE,
            isCheckable = false, shouldShowNewTip = false, isNeverUsedByUser = true
        )
        register(
            R.id.action_save,                    OPERATION_SUPPORT_SAVE,
            isCheckable = false, shouldShowNewTip = false, isNeverUsedByUser = true,
            menuIcon = MenuIcon.Static(R.drawable.photopage_ic_menu_save_selector, R.drawable.photopage_ic_menu_save_selector_light)
        )
        register(
            com.oplus.gallery.basebiz.R.id.action_photo_jigsaw,            OPERATION_SUPPORT_JIGSAW,
            isCheckable = false, shouldShowNewTip = false, isNeverUsedByUser = true
        )
        register(
            com.oplus.gallery.basebiz.R.id.action_move_to,                 OPERATION_SUPPORT_MOVE,
            isCheckable = false, shouldShowNewTip = false, isNeverUsedByUser = true
        )
        register(
            R.id.action_cshot,                   OPERATION_SUPPORT_CSHOT,
            isCheckable = false, shouldShowNewTip = false, isNeverUsedByUser = true,
            menuIcon = MenuIcon.Static(R.drawable.photopage_ic_menu_cshot_selector, R.drawable.photopage_ic_menu_cshot_selector_light)
        )
        register(
            R.id.action_enhancetext,               OPERATION_SUPPORT_ENHANCE_TEXT,
            isCheckable = false, shouldShowNewTip = false, isNeverUsedByUser = true,
            menuIcon = MenuIcon.Static(R.drawable.photopage_ic_menu_enhancetext_selector, R.drawable.photopage_ic_menu_enhancetext_selector_light)
        )
        register(
            com.oplus.gallery.basebiz.R.id.action_dlna,                    OPERATION_SUPPORT_DLNA,
            isCheckable = false, shouldShowNewTip = false, isNeverUsedByUser = true
        )
        register(
            R.id.action_getvideoframe,           OPERATION_SUPPORT_VIDEO_GET_FRAME,
            isCheckable = false, shouldShowNewTip = false, isNeverUsedByUser = true,
            menuIcon = MenuIcon.Static(
                R.drawable.photopage_ic_menu_getvideoframe_selector,
                R.drawable.photopage_ic_menu_getvideoframe_selector_light
            )
        )
        register(
            R.id.action_transform_to_sdr,    OPERATION_SUPPORT_TRANSFORM_TO_SDR,
            isCheckable = false, shouldShowNewTip = true, isNeverUsedByUser = true
        )
        register(
            com.oplus.gallery.basebiz.R.id.action_remove_from_label,       OPERATION_SUPPORT_REMOVE_LABEL,
            isCheckable = false, shouldShowNewTip = false, isNeverUsedByUser = true
        )
        register(
            R.id.action_rename_file,             OPERATION_SUPPORT_RENAME_FILE,
            isCheckable = false, shouldShowNewTip = true,  isNeverUsedByUser = true
        )
        register(
            R.id.action_convert_photo_to_pdf,    OPERATION_SUPPORT_CONVERT_PDF,
            isCheckable = false, shouldShowNewTip = true,  isNeverUsedByUser = true
        )
        register(
            R.id.action_custom_remove_from_widget_list, OPERATION_SUPPORT_CUSTOM_REMOVE_WIDGET_LIST,
            isCheckable = false, shouldShowNewTip = true,  isNeverUsedByUser = true
        )
        register(
            R.id.action_recommended_remove_from_widget_list, OPERATION_SUPPORT_RECOMMENDED_REMOVE_WIDGET_LIST,
            isCheckable = false, shouldShowNewTip = true,  isNeverUsedByUser = true
        )
        register(
            R.id.action_aiidphoto,               OPERATION_SUPPORT_AI_ID,
            isCheckable = false, shouldShowNewTip = false, isNeverUsedByUser = true,
            menuIcon = MenuIcon.Static(R.drawable.photopage_ic_menu_aiidphoto_selector, R.drawable.photopage_ic_menu_aiidphoto_selector_light)
        )
        register(
            R.id.action_portrait_blur,           OPERATION_SUPPORT_PORTRAIT_BLUR,
            isCheckable = false, shouldShowNewTip = true,  isNeverUsedByUser = true,
            menuIcon = MenuIcon.Static(
                R.drawable.photopage_ic_menu_portrait_blur_selector,
                R.drawable.photopage_ic_menu_portrait_blur_selector_light
            )
        )
        register(
            R.id.action_shared_media_tip,         OPERATION_SUPPORT_SHARED_MEDIA_TIP,
            isCheckable = false, shouldShowNewTip = false,  isNeverUsedByUser = true,
            menuIcon = MenuIcon.Static(
                R.drawable.photopage_ic_menu_shared_media_upload_failed_tip,
                R.drawable.photopage_ic_menu_shared_media_upload_failed_tip_light
            )
        )
        register(
            R.id.action_shared_media_delete,      OPERATION_SUPPORT_SHARED_MEDIA_DELETE,
            isCheckable = false, shouldShowNewTip = false,  isNeverUsedByUser = true,
            menuIcon = MenuIcon.Static(R.drawable.photopage_ic_menu_recycle_selector, R.drawable.photopage_ic_menu_recycle_selector_light)
        )
        register(
            R.id.action_shared_media_save,        OPERATION_SUPPORT_SHARED_MEDIA_SAVE,
            isCheckable = false, shouldShowNewTip = false,  isNeverUsedByUser = true,
            menuIcon = MenuIcon.Static(R.drawable.photopage_ic_menu_save_selector, R.drawable.photopage_ic_menu_save_selector_light)
        )
        register(R.id.action_self_splitting,          OPERATION_SUPPORT_SELF_SPLIT,
            isCheckable = false, shouldShowNewTip = true,   isNeverUsedByUser = true,
            menuIcon = MenuIcon.Static(R.drawable.photopage_ic_menu_selfsplit_selector, R.drawable.photopage_ic_menu_selfsplit_selector_light)
        )
        register(
            R.id.action_cloud_download,           OPERATION_SUPPORT_CLOUD_DOWNLOAD,
            isCheckable = false, shouldShowNewTip = false,  isNeverUsedByUser = true,
            menuIcon = MenuIcon.Static(
                R.drawable.photopage_ic_menu_cloud_download_selector,
                R.drawable.photopage_ic_menu_cloud_download_selector_light
            )
        )
        register(R.id.action_save_to_gif,        OPERATION_SUPPORT_SAVE_TO_GIF,
            isCheckable = false, shouldShowNewTip = true,   isNeverUsedByUser = true)
        register(R.id.action_copy_to,        OPERATION_SUPPORT_COPY,
            isCheckable = false, shouldShowNewTip = false,   isNeverUsedByUser = true)
        register(
            R.id.action_preview_check,        OPERATION_SUPPORT_PREVIEW_CHECK,
            isCheckable = true, shouldShowNewTip = false,   isNeverUsedByUser = true,
            menuIcon = MenuIcon.Static(R.drawable.base_shape_btn_check_photo_menu, R.drawable.photopage_ic_menu_btn_check_light)
        )
        register(
            R.id.action_group_photo,         OPERATION_SUPPORT_GROUP_PHOTO,
            isCheckable = false, shouldShowNewTip = true,  isNeverUsedByUser = true,
            menuIcon = MenuIcon.Static(R.drawable.photopage_ic_menu_group_photo_selector, R.drawable.photopage_ic_menu_group_photo_selector_light)
        )
        register(R.id.action_olive_photo,         OPERATION_SUPPORT_OLIVE_PHOTO,
            isCheckable = false, shouldShowNewTip = true,  isNeverUsedByUser = true,
            menuIcon = MenuIcon.Static(R.drawable.photopage_ic_menu_olive_selector, R.drawable.photopage_ic_menu_olive_selector_light)
        )
        register(R.id.action_export_video,        OPERATION_SUPPORT_EXPORT_VIDEO,
            isCheckable = false, shouldShowNewTip = true,   isNeverUsedByUser = true)
        register(
            R.id.action_preview_selected,    OPERATION_SUPPORT_PREVIEW_SELECT,
            isCheckable = true, shouldShowNewTip = false,   isNeverUsedByUser = true,
            menuIcon = MenuIcon.Static(baseR.drawable.base_editor_ic_done_normal, R.drawable.photopage_ic_menu_done_normal_light)
        )
        register(R.id.action_set_cover, OPERATION_SUPPORT_SET_COVER,
            isCheckable = false, shouldShowNewTip = false,   isNeverUsedByUser = true)
        register(
            R.id.action_open_in_system_player,        OPERATION_SUPPORT_OPEN_IN_SYSTEM_PLAYER,
            isCheckable = false, shouldShowNewTip = false, isNeverUsedByUser = true
        )
        register(
            R.id.action_export_olive,        OPERATION_SUPPORT_EXPORT_OLIVE,
            isCheckable = false, shouldShowNewTip = false, isNeverUsedByUser = true
        )
    }

    /**
     * 根据菜单能力项来查询对应的菜单项信息
     */
    fun queryMenuItemInfoWithSupportFlags(
        supportFlags: Long,
        callback: (flag: Long, menuItemInfo: MenuItemInfo) -> Unit
    ) {
        LongFlags(supportFlags).forEach { flag ->
            menuItemConfigs.values.forEach { menuInfo ->
                if (menuInfo.supportOperation == flag) {
                    callback(flag, menuInfo)
                }
            }
        }
    }

    /**
     * 根据菜单actionId来查询对应图标menuIcon
     * @param actionId 菜单actionId
     */
    fun queryMenuIconWithActionId(actionId: Int): MenuIcon? {
        return menuItemConfigs[actionId]?.menuIcon
    }
}

/**
 * 菜单项信息
 */
data class MenuItemInfo(

    /**
     * 菜单项action id
     */
    val action: Int,

    /**
     * 菜单项对应的SUPPORT_OPERATION
     */
    val supportOperation: Long,

    /**
     * 是否为可选中的菜单项
     */
    val isCheckable: Boolean,

    /**
     * 是否应该显示未曾使用的提示
     */
    val shouldShowNewTip: Boolean,

    /**
     * 是否从未被用户使用过
     */
    val isNeverUsedByUser: Boolean,

    /**
     * 图标资源（亮色主题、暗色主题）
     */
    val menuIcon: MenuIcon?,
)

/**
 * 菜单状态
 */
data class MenuState(
    /**
     * 顶部菜单是否可见
     */
    val isTopMenuVisible: Boolean = false,

    /**
     * 底部菜单是否可见
     */
    val isBottomMenuVisible: Boolean = false,

    /**
     * 顶部预设菜单
     */
    val topMenuResId: Int = Resources.ID_NULL,

    /**
     * 底部预设菜单
     */
    val bottomMenuResId: Int = Resources.ID_NULL,

    /**
     * 菜单项状态是否可用。
     * 可能菜单数据第一次刷新时，数据还未准备好，此时菜单项的状态均是不可信的。
     */
    val isMenuItemDataValid: Boolean = false,

    /**
     * 可用的菜单项信息：
     *  - View层通过对比此列表中的菜单项目来确定界面上可以显示哪些菜单项
     *  - View层通过此列表中对菜单项的状态定义来确定菜单项按钮的状态
     *
     *  Map的键值对包含：
     *  - key：为菜单项action
     *  - value：MenuItemState
     */
    val menuItemStates: Map<Int, MenuItemState> = emptyMap()
)

/**
 * 菜单项状态
 */
data class MenuItemState(
    /**
     * 菜单项ID
     */
    val id: Int,

    /**
     * 是否显示
     */
    val isVisible: Boolean,

    /**
     * 是否可用
     */
    val isEnabled: Boolean,

    /**
     * 是否为带选中状态的菜单项
     */
    val isCheckable: Boolean,

    /**
     * 是否已为选中状态
     */
    val isChecked: Boolean,

    /**
     * 图标资源（亮色主题、暗色主题）
     */
    val menuIcon: MenuIcon,

    /**
     * 菜单项提示
     * - 没有提示
     * - 提示数字（红点）
     * - 提示文本
     */
    val tips: PhotoMenuItemTips,

    /**
     * 菜单项的额外触摸热区，如果为[Rect.empty()]则不存在额外的触摸热区
     */
    val extraTouchArea: Rect,

    /**
     * 是否已触发长按
     */
    var isLongPressed: Boolean = false,

    /**
     * 是否置灰显示
     * 注意：这里与disable的区别是只是图标置灰显示，但会响应用户点击
     */
    var isGrayDisplay: Boolean = false,

    /**
     * 记录图标使用的主题，在图标被设置到 MenuItem 后会赋值。
     * 用于判断当前图标的主题是否变更。
     */
    var iconTheme: MenuDecorationTheme? = null,
)

/**
 * 大图菜单项的提示。
 */
sealed class PhotoMenuItemTips {
    /**
     * 没有任何提示
     */
    object ItemTipsNone : PhotoMenuItemTips()

    /**
     * 提示数字(或红点)
     * @param number 要显示的数字，为 0 时显示一个红点
     */
    data class ItemTipsNumber(val number: Int) : PhotoMenuItemTips()

    /**
     * 提示文本。
     * @param order 展示时的优先级，[order]越小的，越优先展示
     * @param textRes 要显示的文本资源
     * @param onShow 文字提示被显示时回调
     * @param onDismiss 文字提示隐藏时回调
     * @param onCloseIconClick Tips点×隐藏回调
     */
    data class ItemTipsText(
        val order: Int,
        val textRes: Int,
        val onShow: () -> Unit = {},
        val onDismiss: () -> Unit = {},
        val onCloseIconClick: () -> Unit = {}
    ) : PhotoMenuItemTips()

    /**
     * Icon提示
     * @param type 提示类型
     * @param tipsDrawable 要显示的Drawable资源
     */
    data class ItemIconTips(val type: IconTipType, val tipsDrawable: Drawable? = null) : PhotoMenuItemTips()
}

/**
 * icon提示类型
 */
enum class IconTipType {
    /** 大师水印的限定水印类型 */
    RESTRICT_WATERMARK
}

/**
 * 气泡弹窗状态
 */
data class BubbleWindowState(
    /**
     * 事件ID，可用于对事件去重
     */
    val eventId: Long,

    /**
     * 事件时间
     */
    val eventTime: Long,

    /**
     * 事件中包含的行为属性
     */
    val eventAction: BubbleAction
)

/**
 * 气泡行为
 */
data class BubbleAction(
    /**
     * 事件的预览图片，用于显示在气泡弹窗中
     */
    val actionPreview: Drawable,

    /**
     * 气泡行为的附加属性
     */
    val payload: Bundle? = null,

    /**
     * 当气泡行为被用户请求时，回调
     */
    val onAction: (BubbleAction) -> Unit
)

/**
 * 大图菜单标题
 */
data class PhotoMenuTitle(
    /**
     * 大图标题处导航图标资源
     */
    val navigationIcon: MenuIcon,
    /**
     * 标题对应的Media的key
     */
    val mediaKey: String,
    /**
     * 标题
     */
    val title: String,
    /**
     * 副标题
     */
    val subTitle: String
)



