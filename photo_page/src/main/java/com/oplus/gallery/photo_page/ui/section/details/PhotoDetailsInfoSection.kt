/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoDetailsInfoSectionImpl.kt
 ** Description : 大图页面 - 详细信息页面切片
 ** Version     : 1.0
 ** Date        : 2021/11/23
 ** Author      : Jisong.<PERSON>@Apps.Gallery
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>           2021/11/23  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.ui.section.details

import android.view.View
import android.view.View.OnLayoutChangeListener
import android.view.ViewGroup
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.lifecycleScope
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business.renderer.olive.OLiveState
import com.oplus.gallery.foundation.arch.sectionpage.ISectionPage
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.foundation.util.ext.collect
import com.oplus.gallery.foundation.util.ext.getOrLog
import com.oplus.gallery.foundation.util.ext.isRtl
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.ui.AbstractPhotoSection
import com.oplus.gallery.photo_page.ui.PhotoFragment
import com.oplus.gallery.photo_page.ui.section.photomenu.PhotoMenuSection
import com.oplus.gallery.photo_page.ui.section.photopagersection.PhotoPagerSection
import com.oplus.gallery.photo_page.ui.section.thumblinesection.PhotoAnimationConfig
import com.oplus.gallery.photo_page.ui.theme.MenuDecorationTheme
import com.oplus.gallery.photo_page.ui.theme.PhotoTagTheme
import com.oplus.gallery.photo_page.ui.theme.PhotoThemedDrawableRes
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.dataloading.diffedFocusViewDataDistinctContent
import com.oplus.gallery.photo_page.viewmodel.dataloading.focusSlot
import com.oplus.gallery.photo_page.viewmodel.details.PhotoDetailsConstants
import com.oplus.gallery.photo_page.viewmodel.details.PhotoDetailsConstants.FormatType.AI_SCENERY
import com.oplus.gallery.photo_page.viewmodel.details.PhotoDetailsConstants.FormatType.DOLBY
import com.oplus.gallery.photo_page.viewmodel.details.PhotoDetailsConstants.FormatType.HDR
import com.oplus.gallery.photo_page.viewmodel.details.PhotoDetailsConstants.FormatType.OLIVE
import com.oplus.gallery.photo_page.viewmodel.details.PhotoDetailsConstants.FormatType.RAW
import com.oplus.gallery.photo_page.viewmodel.details.PhotoDetailsConstants.FormatType.YUV
import com.oplus.gallery.photo_page.viewmodel.details.PhotoDetailsConstants.Key.IS_SHARED_FILE
import com.oplus.gallery.photo_page.viewmodel.details.PhotoDetailsConstants.Key.IS_VIDEO
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PhotoDecorationState
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PhotoPageManagementViewModel
import com.oplus.gallery.photo_page.viewmodel.playbackcontrol.PlaybackInfo
import com.oplus.gallery.photo_page.widget.infotag.PhotoTagContainer
import com.oplus.gallery.photo_page.widget.infotag.PhotoTagView
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentHashMap

/**
 * 大图页详情页面切片
 *
 * 目前包含内容有：
 * - 图片格式类型：HDR、RAW、XHD、10 bit、10 亿色
 */
internal class PhotoDetailsInfoSection(
    sectionPage: ISectionPage<PhotoFragment, PhotoViewModel>
) : AbstractPhotoSection(sectionPage) {

    private val pagerSection: PhotoPagerSection?
        get() = sectionPage.pageInstance.requireSection()

    @OptIn(ExperimentalUnsignedTypes::class)
    private val menuSection: PhotoMenuSection? get() = sectionPage.pageInstance.requireSection<PhotoMenuSection>()

    private val isExitState: Boolean
        get() {
            val state = viewModel.pageManagement.photoPageTransitionState.value ?: return false
            return state >= PhotoPageManagementViewModel.PageTransitionState.EXITING
        }

    private var lastPlaybackInfo: PlaybackInfo? = null
    private val playbackInfo: PlaybackInfo?
        get() = viewModel.playback.playbackInfo.value

    /**
     * 设备是否支OLIVE
     */
    private val isSupportOliveFeature: Boolean by lazy {
        ConfigAbilityWrapper.getBoolean(ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_OLIVE)
    }

    /**
     * 大图页角标，如 10Bit、RAW、OLIVE、Dolby...
     * 默认为空容器，角标的显示与否与其内是否有 PhotoTagView 相关
     */
    private var photoTag: PhotoTagContainer? = null

    /**
     * PhotoTagView 的缓存，
     *
     * key：PhotoDetailsConstants.FormatType
     */
    private val photoTagViews: MutableMap<Int, PhotoTagView> = ConcurrentHashMap()

    private var hasWindowFocus = false

    /**
     * PlaybackInfo是否已经监听订阅
     */
    private var hasSubscribedPlaybackInfo = false

    /**
     * tag名称
     */
    private var currentTagContent: PhotoTagView.TagContent? = null

    /**
     * 上次tag文字标签信息
     */
    private var lastTagContent: PhotoTagView.TagContent? = null

    /**
     * 是否有tag匹配的名字生效
     */
    private var hasTagNameFormat = false

    /**
     * 上次tag状态标记
     */
    private var lastTagStatusFlag: Boolean = true

    /**
     * olive 的photoTag图标
     */
    private val photoOliveOnTagIcon: PhotoThemedDrawableRes by lazy {
        PhotoThemedDrawableRes(
            R.drawable.photo_ic_live_on_photo_tag,
            R.drawable.photo_ic_live_on_photo_tag_light
        )
    }

    private val photoOliveOffTagIcon: PhotoThemedDrawableRes by lazy {
        PhotoThemedDrawableRes(
            R.drawable.photo_ic_live_off_photo_tag,
            R.drawable.photo_ic_live_off_photo_tag_light
        )
    }

    /**
     * realme AI 风光左侧图标
     */
    private val photoSceneryTagStartIcon: PhotoThemedDrawableRes by lazy {
        PhotoThemedDrawableRes(
            R.drawable.photopage_ic_scenery_ai,
            R.drawable.photopage_ic_scenery_ai
        )
    }

    /**
     * realme AI 风光右侧图标
     */
    private val photoSceneryTagEndIcon: PhotoThemedDrawableRes by lazy {
        PhotoThemedDrawableRes(
            R.drawable.photopage_ic_scenery_arrow,
            R.drawable.photopage_ic_scenery_arrow
        )
    }

    private val currentPhotoOliveTagIcon: PhotoThemedDrawableRes get() = if (lastTagStatusFlag) photoOliveOnTagIcon else photoOliveOffTagIcon

    /**
     * Olive tag 的文字
     */
    private val oliveTagText: String get() = sectionPage.pageInstance.resources.getString(R.string.photopage_detail_format_olive_another)

    /**
     * AI 风光 tag 的文字
     */
    private val sceneryTagText: String get() = sectionPage.pageInstance.resources.getString(R.string.photopage_detail_scenery)

    /**
     * 当前需要移除标签的 slot
     * 用于在延时消失时，校验请求是否变更。
     */
    private var slotToRemoveTag: Int? = null

    /**
     * 隐藏标签的延时 runnable，
     * 结合 [slotToRemoveTag] 使用
     */
    private val hideTagContentRunnable: Runnable = Runnable {
        hidePhotoTagContent()
    }

    /**
     * 当删除时需执行动画效果
     */
    private var shouldAnimateWhenRemoved: Boolean = false

    private val focusSlotRemovedListener = {
        if (viewModel.details.isInDetailsMode.value != true) {
            shouldAnimateWhenRemoved = true
            hidePhotoTagContent(withAnimation = true, indexCheck = false)
        }
    }

    @OptIn(ExperimentalUnsignedTypes::class)
    private val photoTagLayoutChangeListener: OnLayoutChangeListener = OnLayoutChangeListener { _, _, _, _, bottom, _, _, _, _ ->
        // 更新半沉浸模式的判断阈值到 PhotoTag 的底部
        menuSection?.updateHalfImmersiveTopThreshold(bottom)
    }


    override fun onCreate() {
        super.onCreate()
        subscribeLiveDataFromViewModel()
        pagerSection?.registerFocusSlotRemovedListener(focusSlotRemovedListener)
    }

    override fun onViewCreated(view: View) {
        photoTag = view.findViewById<PhotoTagContainer?>(R.id.photo_tag_container).apply {
            addOnLayoutChangeListener(photoTagLayoutChangeListener)
        }
        updatePhotoTagTheme()
    }

    override fun onViewDestroy() {
        super.onViewDestroy()
        photoTag?.removeOnLayoutChangeListener(photoTagLayoutChangeListener)
    }

    override fun onDestroy() {
        super.onDestroy()
        hidePhotoTagContent(withAnimation = false, indexCheck = false)
        pagerSection?.unregisterFocusSlotRemovedListener(focusSlotRemovedListener)
    }

    override fun onWindowInsetsChanged(windowInsets: WindowInsetsCompat) {
        super.onWindowInsetsChanged(windowInsets)
        recalculatePhotoTagMargin()
    }

    private fun subscribeLiveDataFromViewModel() {
        viewModel.playback.playbackInfo.observe(this) {
            updatePhotoTagByPlaybackInfoIfNeeded(it, lastPlaybackInfo)
            lastPlaybackInfo = it
        }

        viewModel.details.currentPhotoInfo.observe(this) {
            resetTag()
            updatePhotoTag(MSG_UPDATE_TAG_INFO_CURRENT)
        }

        viewModel.pageManagement.photoDecorationState.observe(this) {
            updatePhotoTag(MSG_UPDATE_TAG_INFO_DECORATION)
        }

        viewModel.scenery?.supportAIScenery?.observe(this) {
            updatePhotoTag(MSG_UPDATE_TAG_AI_SCENERY_STATE_CHANGE)
        }

        viewModel.dataLoading.diffedFocusViewDataDistinctContent.collect(this@PhotoDetailsInfoSection) {
            GTrace.trace({ "$TAG.diffedFocusViewData" }) {
                //数据更新初始化
                hasSubscribedPlaybackInfo = false
            }
        }

        viewModel.pageManagement.photoPageTransitionState.observe(this) {
            if (isExitState) {
                // 退出大图时直接隐藏
                hidePhotoTagContent(withAnimation = false, indexCheck = false)
            }
        }

        viewModel.olive.focusSlotOLiveState.observe(this) {
            oliveTagUpdate(it, viewModel.olive.manualPlayFocusOLiveFormUser.value)
        }

        // 由于存在一种情况，滑动到之后自动播放，然后此时手动点击播放，此时播放状态是从VIDEO到VIDEO监听不到播放状态的改变
        viewModel.olive.manualPlayFocusOLiveFormUser.observe(this) {
            oliveTagUpdate(viewModel.olive.focusSlotOLiveState.value, it)
        }

        viewModel.olive.oliveEnableStatus.observe(this) {
            updateOliveIcon(viewModel.olive.oliveEnable)
        }

        lifecycleScope.launch {
            viewModel.pageManagement.pageTheme.map { it.tagTheme }.collect {
                updatePhotoTagTheme()
            }
        }

        lifecycleScope.launch {
            viewModel.menuControl.menuTheme.map { it.first }.collect {
                updatePhotoTagTheme()
                updatePhotoTag(MSG_UPDATE_TAG_UPDATE_MENU_THEME)
            }
        }

        sectionPage.pageViewModel.details.isInDetailsMode.observe(this) {
            updatePhotoTag(MSG_UPDATE_TAG_IN_DETAIL_VISIBILITY)
        }
    }

    /**
     * 更新角标的主题，
     * - 顶栏半沉浸式时使用半沉浸式主题，
     * - 其余情况跟随大图主题
     */
    private fun updatePhotoTagTheme() {
        photoTag?.theme = if (viewModel.menuControl.menuTheme.value.first == MenuDecorationTheme.HalfImmersive) {
            PhotoTagTheme.HalfImmersive
        } else {
            viewModel.pageManagement.pageTheme.value.tagTheme
        }
    }

    /**
     * 更新olive图标。
     * 背景：用户点击菜单、长按播放时需要隐藏olive图标，播放结束后需要重新显示tag图标。
     */
    private fun oliveTagUpdate(oLiveState: OLiveState?, manualPlayFocusOLiveFormUser: Boolean?) {
        if (isSupportOliveFeature.not()) {
            GLog.d(TAG, LogFlag.DL) { "[oliveTagUpdate] not support for olive. no need to update tag." }
            return
        }

        if (oLiveState == null) {
            GLog.d(TAG, LogFlag.DL) { "[oliveTagUpdate] the oLiveState data is not ready " }
            return
        }

        when {
            shouldHideOliveTag(oLiveState, manualPlayFocusOLiveFormUser) -> {
                // olive被用户点击播放时，需要隐藏图标
                hidePhotoTagContent(withAnimation = false, indexCheck = false)
            }

            (photoTag?.currentContent == null) && (oLiveState == OLiveState.COVER) -> {
                // 当olive重新切到封面时，如果发现此时没有图标则尝试更新tag，重新显示olive图标
                updatePhotoTag(MSG_UPDATE_TAG_OLIVE_STATE_CHANGE)
            }
        }
    }

    /**
     * 判断是否需要隐藏olive图标：olive长按播放时触发隐藏。
     *
     * 三个条件都需要满足：
     * 1. 正在显示olive图标
     * 2. 当前olive正在播放视频
     * 3. 当前olive播放视频是由用户点击触发的，而非滑动自动播放。
     */
    private fun shouldHideOliveTag(oLiveState: OLiveState?, manualPlayFocusOLiveFormUser: Boolean?): Boolean {
        return (photoTag?.currentContent?.tag == OLIVE) &&
                (oLiveState == OLiveState.VIDEO) &&
                (manualPlayFocusOLiveFormUser == true)
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        hasWindowFocus = hasFocus
    }

    /**
     * 从缓存中获取或创建一个对应的 PhotoTagView，
     * 同时会将 formatType 绑定到 PhotoTagView.tag 中。
     *
     * @param formatType 角标类型 PhotoDetailsConstants.FormatType
     */
    private fun getPhotoTagView(formatType: Int): PhotoTagView? {
        val context = photoTag?.context ?: sectionPage.pageContext ?: return null.also {
            GLog.e(TAG, LogFlag.DL) { "[getPhotoTagView] no context!" }
        }
        return photoTagViews.getOrPut(formatType) {
            PhotoTagView(context).apply {
                tag = formatType
            }
        }
    }

    /**
     * tag标签逻辑
     * @param reason 监听订阅信息
     */
    private fun updatePhotoTag(reason: String) {
        if (isExitState) {
            hidePhotoTagContent(withAnimation = false, indexCheck = false)
            GLog.w(TAG, "[updatePhotoTag] reason = $reason, is exit state, skip")
            return
        }

        val photoInfo = viewModel.details.currentPhotoInfo.value ?: let {
            GLog.d(TAG, LogFlag.DL) { "[updatePhotoTag] <reason> photoInfo is null, skip .reason is $reason" }
            return
        }

        if (photoInfo.index != viewModel.dataLoading.focusSlot) {
            GLog.d(TAG, LogFlag.DL) { "[updatePhotoTag] <reason> photoInfo index not equals focus, skip. reason : $reason" }
            return
        }

        var formatType = photoInfo.details.getInt(PhotoDetailsConstants.Key.FORMAT_TYPE, PhotoDetailsConstants.FormatType.OTHER)
        // HDR和杜比,优先显示杜比
        if ((formatType != DOLBY) && (playbackInfo?.isHDRVideo == true) && hasSubscribedPlaybackInfo) {
            formatType = HDR
        }
        GLog.d(TAG, LogFlag.DL) {
            "[updatePhotoTag] $reason, focusSlot=${viewModel.dataLoading.focusSlot}, " +
                    "formatType =$formatType, photoInfo.index =${photoInfo.index}"
        }
        val photoTagView = getPhotoTagView(formatType)?.apply {
            if (formatType == OLIVE && isSupportOliveFeature) {
                updateOliveIcon(viewModel.olive.oliveEnable)
                setStartDrawable(currentPhotoOliveTagIcon)
                setEndDrawable(null)
            } else if (formatType == AI_SCENERY) {
                updateAISceneryTag()
            }
        }.getOrLog(TAG, "[updatePhotoTag] photoTag is null!") ?: return

        if (currentTagContent == null) {
            currentTagContent = generateFormatName(formatType)
        }

        currentTagContent?.apply {
            hasTagNameFormat = true
            setFormatNameToPhotoTag(photoTagView, this)
            setTagAnimation(photoTagView = photoTagView, reason = reason, index = photoInfo.index)
            lastTagContent = this
        } ?: run {
            hasTagNameFormat = false
            hidePhotoTagContent(withAnimation = true, indexCheck = false)
            GLog.d(TAG, LogFlag.DL) { "[updatePhotoTag] reason= $reason formatName no value" }
        }

        recalculatePhotoTagMargin()
    }

    private fun updateAISceneryTag() {
        val photoTagView = photoTag?.currentContent as? PhotoTagView
        photoTagView?.setStartDrawable(photoSceneryTagStartIcon)
        photoTagView?.setEndDrawable(photoSceneryTagEndIcon)
    }

    /**
     * 根据当前oliveEnable状态来更新对应tag图标
     */
    private fun updateOliveIcon(enable: Boolean) {
        if (lastTagStatusFlag != enable) {
            lastTagStatusFlag = enable
            val photoTagView = photoTag?.currentContent as? PhotoTagView
            if (photoTagView?.tag == OLIVE) {
                photoTagView.setStartDrawable(currentPhotoOliveTagIcon)
            }
        }
    }

    /**
     * 根据媒体格式生成标签名字
     * @param formatType  媒体格式
     */
    private fun generateFormatName(formatType: Int): PhotoTagView.TagContent? {
        val details = viewModel.details.currentPhotoInfo.value?.details
        return when {
            (details?.getBoolean(IS_SHARED_FILE, false) == true) && details.getBoolean(IS_VIDEO, false) -> null

            (formatType == DOLBY) && (viewModel.brighten.isSupportDolbyBrightenPresent) -> {
                PhotoTagView.TagContent.Image(
                    PhotoThemedDrawableRes(
                        R.drawable.photopage_detail_ic_proxdr_dolby_vision,
                        R.drawable.photopage_detail_ic_proxdr_dolby_vision_light
                    )
                )
            }

            isProXDR(formatType) -> {
                PhotoTagView.TagContent.Image(
                    PhotoThemedDrawableRes(R.drawable.photopage_detail_ic_proxdr, R.drawable.photopage_detail_ic_proxdr_light)
                )
            }

            (AI_SCENERY == formatType) -> PhotoTagView.TagContent.Text(sceneryTagText)

            (OLIVE == formatType) && isSupportOliveFeature -> PhotoTagView.TagContent.Text(oliveTagText)
            else -> {
                val strRes = when (formatType) {
                    RAW -> R.string.photopage_detail_format_raw
                    YUV -> if (ResourceUtils.isChineseSimp()) R.string.photopage_detail_format_10bit_zh else R.string.photopage_detail_format_10bit
                    else -> null
                }
                val text = strRes?.let { PhotoTagView.TagContent.Text(sectionPage.pageInstance.resources.getString(it)) }
                GLog.d(TAG, LogFlag.DL) { "generateFormatName, $text" }
                text
            }
        }
    }

    private fun isProXDR(formatType: Int) =
        ((formatType == HDR) && (viewModel.brighten.isSupportDolbyBrightenPresent || viewModel.brighten.isSupportHdrVisionBrightenPresent))

    /**
     * 标签显示文字
     *
     * @param photoTagView 当前 TAG 的具体实现类
     * @param content tag 的内容
     */
    private fun setFormatNameToPhotoTag(photoTagView: PhotoTagView, content: PhotoTagView.TagContent) {
        val tvPhotoTag = photoTag ?: return
        photoTagView.setContent(content)
        val charSequence = photoTagView.text

        /**修改bug7913565，无障碍语音播报，会默认是RAW，需要加判断如果是olive图，需要播报实况*/
        if ((charSequence == oliveTagText) || (charSequence == sceneryTagText)) {
            tvPhotoTag.contentDescription = charSequence
            return
        }
        //RAW全为大写时，播报其每个字母。RAW需要播报英文发音，需要将其变为小写。
        sectionPage.pageInstance.resources.getString(R.string.photopage_detail_format_raw).let { text ->
            if (text == charSequence) {
                tvPhotoTag.contentDescription = text.lowercase()
            } else {
                tvPhotoTag.contentDescription = text
            }
        }
    }

    /**
     * 当前是否支持显示 TAG，
     * 1. 正在退出大图时不显示
     * 2. 沉浸式时不显示
     * 3. 顶栏半沉浸式时不显示
     * 4. 详情模式时不显示
     */
    @OptIn(ExperimentalUnsignedTypes::class)
    private fun couldShowTag(): Boolean {
        val isDecorationStateShown = viewModel.pageManagement.photoDecorationState.value is PhotoDecorationState.Show
        val isInDetailsMode = viewModel.details.isInDetailsMode.value ?: false
        val isTopMenuHalfImmersive = menuSection?.shouldUseHalfImmersiveMode()?.first ?: false
        return !isExitState && isDecorationStateShown && isInDetailsMode.not() && isTopMenuHalfImmersive.not()
    }

    /**
     * 设置 TAG 动画
     * 视频的 TAG 会在视频开始播放的 [PHOTO_TAG_ANIMATION_DELAY] 毫秒后自动消失。
     *
     * @param photoTagView 当前 TAG 的具体实现类
     * @param reason 调用的来源
     * @param index tag 对应的 slot
     */
    private fun setTagAnimation(photoTagView: PhotoTagView, reason: String, index: Int) {
        if (index != currentFocus) return

        // 当前不支持显示 TAG 时直接隐藏
        if (!couldShowTag()) {
            GLog.d(TAG, LogFlag.DL) { "[setTagAnimation] hide, reason: $reason, isCanShowTag: false" }
            hidePhotoTagContent(indexCheck = false)
            return
        }

        val formatType = photoTagView.tag
        var shouldDelayInvisible = false
        if ((formatType == PhotoDetailsConstants.FormatType.DOLBY) || (formatType == PhotoDetailsConstants.FormatType.HDR)) {
            // 视频播放中需延迟将 tag 隐藏，停止时则需显示
            shouldDelayInvisible = viewModel.playback.isPlaying()
        }

        // 判断是否有需要替换 TAG，如无需变更则跳过。
        if (shouldSkipPhotoTagReplace(photoTagView, index, shouldDelayInvisible)) return

        val delayTime = if (shouldAnimateWhenRemoved) {
            shouldAnimateWhenRemoved = false
            REMOVED_DELAY_ALPHA_SHOW_TIME
        } else {
            0L
        }
        photoTag?.replace(photoTagView, delayTime)

        if (shouldDelayInvisible) {
            slotToRemoveTag = index
            photoTag?.removeCallbacks(hideTagContentRunnable)
            photoTag?.postDelayed(hideTagContentRunnable, PHOTO_TAG_ANIMATION_DELAY)
        } else {
            // 需要显示 tag，取消隐藏的 post 回调。
            slotToRemoveTag = null
            photoTag?.removeCallbacks(hideTagContentRunnable)
        }
    }

    /**
     * 是否可以跳过这次的 PhotoTag 替换，满足以下条件：
     * - 角标类型相同，且
     * - 先前的角标和现在的角标都常显，或
     * - 先前的角标和现在的角标都会延迟隐藏，且标记的 slot 和当前相同
     */
    private fun shouldSkipPhotoTagReplace(photoTagView: PhotoTagView, index: Int, shouldDelayInvisible: Boolean): Boolean {
        val isShowingSameTag = photoTag?.currentContent?.tag == photoTagView.tag
        val preShouldDelayInvisible = slotToRemoveTag != null
        val isPreAndNowSame = preShouldDelayInvisible == shouldDelayInvisible
        val canSkipIfBothVisible = isPreAndNowSame && !shouldDelayInvisible
        val canSkipIfBothInvisible = isPreAndNowSame && shouldDelayInvisible && (slotToRemoveTag == index)
        GLog.d(TAG, LogFlag.DL) {
            "[shouldSkipPhotoTagReplace] shouldDelayInvisible: $shouldDelayInvisible, " +
                    "isShowingSameTag: $isShowingSameTag, index: $index, slotToRemoveTag: $slotToRemoveTag"
        }
        return isShowingSameTag && (canSkipIfBothVisible || canSkipIfBothInvisible)
    }

    /**
     * 隐藏 TAG，将 PhotoTagView 从容器中移除
     *
     * @param withAnimation 是否需要隐藏动画
     * @param indexCheck 是否需要检查 focus 变化（延时调用时传 true，直接调用时传 false）
     */
    private fun hidePhotoTagContent(withAnimation: Boolean = true, indexCheck: Boolean = true) {
        if (!indexCheck || (slotToRemoveTag == currentFocus)) {
            photoTag?.removeContent(withAnimation)
        } else {
            GLog.d(TAG, LogFlag.DL) { "[hidePhotoTagContent] not to remove, slotToRemoveTag: $slotToRemoveTag, current: $currentFocus" }
        }
        slotToRemoveTag = null
    }

    /**
     * 重置Tag信息
     */
    private fun resetTag() {
        currentTagContent = null
    }

    /**
     * 更新[photoTag]的 `margin`。
     *
     * 当用户打开虚拟导航栏时，横屏会把导航栏放在屏幕的左侧或右侧。
     * 此时 [photoTag] 会被导航栏遮挡，需要通过改变margin的方式对其进行规避。
     *
     * Marked: 李程里， 后续应该提供一种对大图上方控件的统一管理方式。不然每个都要单独适配，很容易遗漏。
     */
    private fun recalculatePhotoTagMargin() {
        val tvPhotoTag = photoTag ?: return

        var marginStart = tvPhotoTag.context.resources.getDimensionPixelSize(R.dimen.photopage_detail_tag_margin_start_small_screen)
        val isNaviBarHasHeightValue = (tvPhotoTag.isRtl && (sectionPage.pageInstance.rightNaviBarHeight() != 0))
                || (tvPhotoTag.isRtl.not() && (sectionPage.pageInstance.leftNaviBarHeight() != 0))
        val horizontalPadding =
            if (sectionPage.pageInstance.isLandscape() && sectionPage.pageInstance.isInMultiWindow()
                    .not() && isNaviBarHasHeightValue.not()
            ) {
                marginStart = tvPhotoTag.context.resources.getDimensionPixelSize(R.dimen.photopage_detail_tag_margin_start)
                if (ScreenUtils.isLargeScreenWidth(tvPhotoTag.context)) {
                    sectionPage.pageViewModel.context.resources.getDimensionPixelSize(
                        R.dimen.photopage_top_menu_horizontal_padding_in_landscape_large_mode
                    )
                } else {
                    sectionPage.pageViewModel.context.resources.getDimensionPixelSize(
                        R.dimen.photopage_top_menu_horizontal_padding_in_landscape_mode
                    )
                }
            } else 0

        updatePhotoTagMargin(marginStart, horizontalPadding)
    }


    /**
     * 更新PhotoTag间距
     * @param marginStart 起始间距
     * @horizontalPadding 横屏时  水平间距
     *
     */
    private fun updatePhotoTagMargin(marginStart: Int, horizontalPadding: Int) {
        val tvPhotoTag = photoTag ?: return

        val layoutParams = tvPhotoTag.layoutParams as? ViewGroup.MarginLayoutParams ?: let {
            GLog.d(TAG) { "[updatePhotoTagMargin] tvPhotoTag layoutParams is not MarginLayoutParams" }
            return
        }
        if (tvPhotoTag.isRtl) {
            marginStart + sectionPage.pageInstance.rightNaviBarHeight() + horizontalPadding
        } else {
            marginStart + sectionPage.pageInstance.leftNaviBarHeight() + horizontalPadding
        }.let { expectedMarginStart ->
            if (expectedMarginStart == layoutParams.marginStart) {
                GLog.d(TAG) { "[updatePhotoTagMargin] tvPhotoTag marginStart is not changed" }
                return
            }
            layoutParams.marginStart = expectedMarginStart
            tvPhotoTag.layoutParams = layoutParams

            GLog.d(TAG) { "[updatePhotoTagMargin] tvPhotoTag marginStart has changed, new marginStart=$expectedMarginStart." }
        }
    }

    /**
     * playbackInfo 会频繁调用，为防止频繁刷新 tag UI，此处添加守卫语句
     */
    private fun updatePhotoTagByPlaybackInfoIfNeeded(newPlaybackInfo: PlaybackInfo?, oldPlaybackInfo: PlaybackInfo?) {
        if (newPlaybackInfo?.isPlayerTokenEquals(oldPlaybackInfo) != true) {
            // 播放器都变更：需要刷新一下 tag UI
            hasSubscribedPlaybackInfo = true
            updatePhotoTag(MSG_UPDATE_TAG_INFO_TOKEN)
            updatePhotoTag(MSG_UPDATE_TAG_INFO_CURRENT)
            return
        }

        if (newPlaybackInfo.isHDRVideo != oldPlaybackInfo?.isHDRVideo) {
            // 播放器没有变更 && HDR 标志位变更了：需要刷新一下 tag UI
            updatePhotoTag(MSG_UPDATE_TAG_INFO_HDR)
            return
        }

        if (newPlaybackInfo.isPlaying != oldPlaybackInfo.isPlaying) {
            //  播放状态标志位变更了：需要刷新一下 tag UI
            updatePhotoTag(
                if (newPlaybackInfo.isPlaying) MSG_UPDATE_TAG_INFO_PLAYING else MSG_UPDATE_TAG_INFO_PAUSE
            )
            return
        }
    }

    private companion object {
        private const val TAG = "PhotoInfoSection"

        private const val MSG_UPDATE_TAG_INFO_CURRENT: String = "currentPhotoInfo"
        private const val MSG_UPDATE_TAG_INFO_DECORATION: String = "photoDecorationVisibility"
        private const val MSG_UPDATE_TAG_AI_SCENERY_STATE_CHANGE: String = "AIScenery state change"
        private const val MSG_UPDATE_TAG_INFO_TOKEN: String = "playbackInfo Token change"
        private const val MSG_UPDATE_TAG_INFO_HDR: String = "playbackInfo isHDRVideo change"
        private const val MSG_UPDATE_TAG_INFO_PLAYING: String = "playbackInfo isPlaying true"
        private const val MSG_UPDATE_TAG_INFO_PAUSE: String = "playbackInfo isPlaying false"
        private const val MSG_UPDATE_TAG_OLIVE_STATE_CHANGE: String = "olive state change"
        private const val MSG_UPDATE_TAG_IN_DETAIL_VISIBILITY: String = "in detail visibility"
        private const val MSG_UPDATE_TAG_UPDATE_MENU_THEME: String = "update menu theme"
        private const val PHOTO_TAG_ANIMATION_DELAY = 3000L
        private const val REMOVED_DELAY_ALPHA_SHOW_TIME = PhotoAnimationConfig.PHOTO_REMOVED_DELAY_ALPHA_SHOW_TIME
    }
}