/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoMenuExecutor.kt
 ** Description : 大图页菜单执行器
 ** Version     : 1.0
 ** Date        : 2022/02/17
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>@Apps.Gallery              2022/02/17  1.0         create module
 *********************************************************************************/
@file:Suppress("SpacingAroundParens")
package com.oplus.gallery.photo_page.viewmodel.menucontrol

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.lifecycle.viewModelScope
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.business_lib.helper.BottomMenuHelper
import com.oplus.gallery.business_lib.menuoperation.MenuRequestCode
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.AI_ELIMINATE
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.AI_ID_PHOTO
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.AI_REPAIR_DEBLUR
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.AI_REPAIR_DEREFLECTION
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.DROP_PICTURE
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.AI_LIGHTING
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.ENHANCE_TEXT
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.GIF_SYNTHESIS
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.GROUP_PHOTO
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.IMAGE_QUALITY_ENHANCE
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.NORMAL
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.PASSERBY_ELIMINATE
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.AI_SCENERY
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.PORTRAIT_BLUR
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.PRIVACY_WATERMARK
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.SUPER_TEXT
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.AI_BESTTAKE
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.AI_COMPOSITION
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.EXPORT_OLIVE
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.ext.collectNotNull
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.ui.PhotoFragment
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.dataloading.DiffedPhotoItemViewData
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuCShotActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuCopyToActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuCouldDownloadActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuDetailActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuEditActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuEncryptActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuExportVideoActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuFavoriteActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuGetVideoFrameActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuGifActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuGooglePassScanActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuHdrToSdrActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuHeifToJpegActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuImportActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuLaunchGalleryActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuLensActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuLnSActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuMoveToActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuOlivePlayActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuOpenInSystemPlayerActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuPreviewCheckActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuPreviewSelectActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuProjectionActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuRecycleActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuRemoveFromLabelActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuRemoveFromPersonActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuRemoveFromWidgetListActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuRenameFileActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuSaveActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuSelfSplitActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuSetContactActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuSetCoverActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuSetWallpaperActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuShareActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuSharedMediaDeleteActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuSharedMediaTipActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuSlidingShowActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuToOcrScannerActionRule
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuToOcrScannerActionRule.OcrScannerPageType
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuToPdfActionRule
import com.oplus.gallery.standard_lib.scheduler.session.WorkerSession
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.FlowCollector
import kotlinx.coroutines.launch
import com.oplus.gallery.basebiz.R as BasebizR

/**
 * 菜单请求域，用于给各类菜单 Rule 提供一些 View 层的操作
 */
interface MenuScope {

    /**
     * 跳转新页面
     * @param pageContainerId page的容器id
     * @param routerName 要路由的组件名称
     * @param data 路由携带的 arguments
     */
    fun startPage(pageContainerId: Int = BasebizR.id.base_fragment_container, routerName: String, data: Bundle? = null)

    /**
     * 获取PageView，用于绑定锚点view
     */
    fun getPageView(): View?
}

/**
 * 大图页菜单执行器，大图页的菜单点击事件会分发到此处进行处理。
 * 内部会将不同的菜单事件分发到特定的PhotoMenuXXXActionRule中具体执行。
 */
class PhotoMenuExecutor(
    private val fragment: PhotoFragment,
    private val session: WorkerSession,
    private val viewModel: PhotoViewModel,
    private val menuScope: MenuScope
) : MenuScope by menuScope {

    /**
     * 菜单执行时使用的规则集合
     */
    private val menuActionRules: List<PhotoMenuActionRule> = kotlin.run {
        val activity = (fragment.activity as? BaseActivity) ?: let {
            GLog.e(TAG, "[doAction] error, activity is null or can't cast to BaseActivity")
            return@run listOf<PhotoMenuActionRule>()
        }
        val bottomMenuHelper = BottomMenuHelper(activity)
        // 函数上增加了注解以豁免空格检查，保持清晰可读风格
        listOf(
            PhotoMenuLensActionRule                 (R.id.action_lens,                    viewModel, activity),
            // Marked by: 2022/3/30 lichengli 短信进入也是FileProvider，完成后需测试该功能。
            PhotoMenuSaveActionRule                 (R.id.action_save,                    viewModel, activity, session),
            PhotoMenuSaveActionRule                 (R.id.action_shared_media_save,       viewModel, activity, session),
            PhotoMenuCShotActionRule(
                R.id.action_cshot, viewModel, fragment, intArrayOf(MenuRequestCode.RELEASE_CSHOT)
            ),
            PhotoMenuShareActionRule                (R.id.action_share,                   viewModel, fragment, activity, session),
            PhotoMenuImportActionRule               (R.id.action_import,                  viewModel, activity, bottomMenuHelper),
            PhotoMenuDetailActionRule               (R.id.action_details,                 viewModel, activity),
            PhotoMenuMoveToActionRule               (BasebizR.id.action_move_to,                 viewModel, fragment, activity),
            PhotoMenuRecycleActionRule              (R.id.action_recycle,                 viewModel, activity, fragment, bottomMenuHelper, menuScope),
            PhotoMenuRecycleActionRule              (R.id.action_delete_recycled,         viewModel, activity, fragment, bottomMenuHelper),
            PhotoMenuRecycleActionRule              (R.id.action_restore_recycled,        viewModel, activity, fragment, bottomMenuHelper),
            PhotoMenuEncryptActionRule              (BasebizR.id.action_encrypt,                 viewModel, activity, fragment, bottomMenuHelper),
            PhotoMenuFavoriteActionRule             (R.id.action_favorites,               viewModel, fragment),
            PhotoMenuHdrToSdrActionRule             (R.id.action_transform_to_sdr,    viewModel, fragment, activity, session),
            PhotoMenuSelfSplitActionRule            (R.id.action_self_splitting,          viewModel, fragment, activity),
            PhotoMenuProjectionActionRule           (
                BasebizR.id.action_dlna,                    viewModel, activity, intArrayOf(
                    BasebizR.id.action_dlna)),
            PhotoMenuProjectionActionRule           (R.id.action_projection,              viewModel, activity, intArrayOf(R.id.action_projection)),
            PhotoMenuProjectionActionRule           (R.id.action_screencast,              viewModel, activity, intArrayOf(R.id.action_screencast)),
            PhotoMenuRenameFileActionRule           (R.id.action_rename_file,             viewModel, activity),
            PhotoMenuToOcrScannerActionRule         (R.id.action_to_ocr_scanner, OcrScannerPageType.DOCUMENT_SCANNER, viewModel, activity),
            PhotoMenuToOcrScannerActionRule         (R.id.action_to_ocr_scanner_super_text, OcrScannerPageType.SUPER_TEXT, viewModel, activity),
            PhotoMenuToPdfActionRule                (R.id.action_convert_photo_to_pdf,    viewModel, activity),
            PhotoMenuSetContactActionRule           (R.id.action_setas_contact,           viewModel, activity),
            PhotoMenuHeifToJpegActionRule           (R.id.action_transform_heif_to_jpeg,        viewModel, fragment, activity, session),
            PhotoMenuSlidingShowActionRule          (R.id.action_slideshow,               viewModel, fragment),
            PhotoMenuSetWallpaperActionRule         (R.id.action_setas_wallpaper,         viewModel, activity),
            PhotoMenuLaunchGalleryActionRule        (R.id.action_gallery,                 viewModel, activity, fragment),
            PhotoMenuGetVideoFrameActionRule        (R.id.action_getvideoframe,           viewModel, fragment),
            PhotoMenuCouldDownloadActionRule        (R.id.action_cloud_download,          viewModel, activity),
            PhotoMenuSharedMediaTipActionRule       (R.id.action_shared_media_tip,        viewModel, activity),
            PhotoMenuRemoveFromLabelActionRule      (BasebizR.id.action_remove_from_label,       viewModel, bottomMenuHelper),
            PhotoMenuRemoveFromPersonActionRule     (BasebizR.id.action_free_face_from_group,    viewModel, bottomMenuHelper),
            PhotoMenuSharedMediaDeleteActionRule    (R.id.action_shared_media_delete,     viewModel),
            PhotoMenuEditActionRule                 (R.id.action_aiidphoto,               viewModel, fragment, activity, AI_ID_PHOTO),
            PhotoMenuRemoveFromWidgetListActionRule (R.id.action_custom_remove_from_widget_list, viewModel, activity, bottomMenuHelper),
            PhotoMenuRemoveFromWidgetListActionRule (R.id.action_recommended_remove_from_widget_list, viewModel, activity, bottomMenuHelper),
            PhotoMenuEditActionRule(
                R.id.action_enhancetext, viewModel, fragment, activity, ENHANCE_TEXT, menuScope, intArrayOf(MenuRequestCode.PHOTO_EDIT_ENHANCE_TEXT)
            ),
            PhotoMenuEditActionRule(
                R.id.action_supertext, viewModel, fragment, activity, SUPER_TEXT, menuScope, intArrayOf(MenuRequestCode.PHOTO_EDIT_SUPER_TEXT)
            ),
            PhotoMenuEditActionRule(
                R.id.action_ai_composition,
                viewModel,
                fragment,
                activity,
                AI_COMPOSITION,
                menuScope,
                intArrayOf(MenuRequestCode.AI_COMPOSITION_REQUEST_CODE)
            ),
            PhotoMenuEditActionRule(
                R.id.action_ai_repair_deblur,
                viewModel,
                fragment,
                activity,
                AI_REPAIR_DEBLUR,
                menuScope,
                intArrayOf(MenuRequestCode.AI_REPAIR_REQUEST_CODE)
            ),
            PhotoMenuEditActionRule(
                R.id.action_ai_scenery,
                viewModel,
                fragment,
                activity,
                AI_SCENERY,
                menuScope,
                intArrayOf(MenuRequestCode.AI_SCENERY_REQUEST_CODE)
            ),
            PhotoMenuEditActionRule(
                R.id.action_ai_repair_dereflection,
                viewModel,
                fragment,
                activity,
                AI_REPAIR_DEREFLECTION,
                menuScope,
                intArrayOf(MenuRequestCode.AI_REPAIR_REQUEST_CODE)
            ),
            PhotoMenuEditActionRule( //AI补光
                R.id.action_ai_lighting,
                viewModel,
                fragment,
                activity,
                AI_LIGHTING,
                menuScope,
                intArrayOf(MenuRequestCode.AI_LIGHTING_CODE)
            ),
            PhotoMenuEditActionRule(
                R.id.action_passerby_eliminate,
                viewModel,
                fragment,
                activity,
                PASSERBY_ELIMINATE,
                menuScope,
                intArrayOf(MenuRequestCode.AI_ELIMINATE_REQUEST_CODE)
            ),
            PhotoMenuEditActionRule(
                R.id.action_ai_eliminate,
                viewModel,
                fragment,
                activity,
                AI_ELIMINATE,
                menuScope,
                intArrayOf(MenuRequestCode.AI_ELIMINATE_REQUEST_CODE)
            ),
            PhotoMenuEditActionRule(
                R.id.action_portrait_blur,
                viewModel,
                fragment,
                activity,
                PORTRAIT_BLUR,
                menuScope,
                intArrayOf(MenuRequestCode.PHOTO_EDIT_PORTRAIT_BLUR)
            ),
            PhotoMenuEditActionRule(
                R.id.action_edit,
                viewModel,
                fragment,
                activity,
                NORMAL,
                menuScope,
                intArrayOf(MenuRequestCode.VIDEO_EDIT, MenuRequestCode.PHOTO_EDIT_DEFAULT)
            ),
            PhotoMenuGifActionRule(
                R.id.action_save_to_gif,
                viewModel,
                fragment,
                activity,
                GIF_SYNTHESIS,
                menuScope,
                intArrayOf(MenuRequestCode.GIF_SYNTHESIS_REQUEST_CODE)
            ),
            PhotoMenuEditActionRule(
                R.id.action_privacy_watermark,
                viewModel,
                fragment,
                activity,
                PRIVACY_WATERMARK,
                menuScope,
                intArrayOf(MenuRequestCode.PHOTO_EDIT_PRIVACY_WATERMARK)
            ),
            PhotoMenuEditActionRule( // 画质增强
                R.id.action_image_quality_enhance,
                viewModel,
                fragment,
                activity,
                IMAGE_QUALITY_ENHANCE,
                menuScope,
                intArrayOf(MenuRequestCode.PHOTO_EDIT_IMAGE_QUALITY_ENHANCE)
            ),
            PhotoMenuLnSActionRule(
                R.id.action_lns_result_menu,
                viewModel,
                fragment,
                activity,
                menuScope,
                intArrayOf(MenuRequestCode.LNS_VIEW_SAVED_RESULT_REQUEST_CODE)
            ),
            PhotoMenuCopyToActionRule(R.id.action_copy_to, viewModel, fragment),
            PhotoMenuPreviewCheckActionRule(R.id.action_preview_check, viewModel),
            PhotoMenuEditActionRule(
                R.id.action_group_photo,
                viewModel,
                fragment,
                activity,
                GROUP_PHOTO,
                menuScope,
                intArrayOf(MenuRequestCode.GROUP_PHOTO_REQUEST_CODE)
            ),
            PhotoMenuEditActionRule(
                R.id.action_drop_picture_to_edit,
                viewModel,
                fragment,
                activity,
                DROP_PICTURE,
                menuScope,
                intArrayOf(MenuRequestCode.DROP_PICTURE_REQUEST_CODE)
            ),
            PhotoMenuEditActionRule(
                R.id.action_ai_best_take,
                viewModel,
                fragment,
                activity,
                AI_BESTTAKE,
                menuScope,
                intArrayOf(MenuRequestCode.AI_BESTTAKE_REQUEST_CODE)
            ),
            PhotoMenuEditActionRule(
                R.id.action_export_olive,
                viewModel,
                fragment,
                activity,
                EXPORT_OLIVE,
                menuScope,
                intArrayOf(MenuRequestCode.EXPORT_OLIVE_REQUEST_CODE)
            ),
            PhotoMenuOlivePlayActionRule(R.id.action_olive_photo, viewModel, activity,),
            PhotoMenuExportVideoActionRule(R.id.action_export_video, viewModel, fragment),
            PhotoMenuPreviewSelectActionRule(R.id.action_preview_selected, viewModel, activity, fragment),
            PhotoMenuSetCoverActionRule(R.id.action_set_cover, viewModel, fragment),
            PhotoMenuGooglePassScanActionRule(R.id.action_google_pass_scan, viewModel),
            PhotoMenuOpenInSystemPlayerActionRule(R.id.action_open_in_system_player, viewModel, activity)
        )
    }

    /**
     * 焦点数据变更的监听器，会在焦点ViewData数据变化时通知菜单Rule进行响应。
     */
    private val diffedFocusViewDataCollector = FlowCollector<DiffedPhotoItemViewData?> { diff ->
        GTrace.trace({ "$TAG.focusSlotViewDataDiff" }) {
            // oldItem 为空，说明是首次加载，不是数据变化
            if (diff?.oldItem == null) {
                GLog.i(TAG) { "[focusViewDataObserver] diff.oldItem is null" }
                return@trace
            }
            menuActionRules.forEach { rule -> rule.onFocusViewDataChanged(diff) }
        }
    }

    init {
        viewModel.viewModelScope.launch(Dispatchers.Main.immediate) {
            viewModel.dataLoading.diffedFocusViewData.collectNotNull(diffedFocusViewDataCollector)
        }
    }

    /**
     * 执行菜单项对应的操作
     * @param onDone 当菜单操作执行完成后，不论成败，无论有无匹配项，都需要会通过此接口回调操作已结束
     * @param extras 点击菜单的附带参数
     * @param isLongClicked 是否长按事件
     */
    fun doAction(action: Int, isLongClicked: Boolean, extras: Bundle?, onDone: (action: Int) -> Unit) {
        GLog.i(TAG) { "[doAction] action: $action" }
        menuActionRules
            .find { rule -> rule.matchRule(action) }
            ?.doAction(isLongClicked, extras) { onDone(action) } // 匹配到规则，执行规则
            ?: onDone(action) // 没有匹配到规则，直接调用onDone
    }

    /**
     * 配置更新
     */
    fun onAppUiStateChanged(config: AppUiResponder.AppUiConfig) {
        menuActionRules.forEach { rule ->
            rule.onAppUiStateChanged(config)
        }
    }

    fun handleResult(requestCode: Int, resultCode: Int, intent: Intent?) {
        GLog.i(TAG) { "[handleResult] action: $requestCode" }
        menuActionRules.forEach { rule ->
            if (rule.matchResponseCode(requestCode)) {
                rule.handleResult(requestCode, resultCode, intent)
                return@forEach
            }
        }
    }

    fun onCreate() {
        GLog.d(TAG, "[onCreate]")
        menuActionRules.forEach(PhotoMenuActionRule::onCreate)
    }

    fun onStart() {
        GLog.d(TAG, "[onStart]")
        menuActionRules.forEach(PhotoMenuActionRule::onStart)
    }

    fun onStop() {
        GLog.d(TAG, "[onStop]")
        menuActionRules.forEach(PhotoMenuActionRule::onStop)
    }

    fun onDestroy() {
        GLog.d(TAG, "[onDestroy]")
        menuActionRules.forEach(PhotoMenuActionRule::onDestroy)
    }

    companion object {
        private const val TAG = "PhotoMenuExecutor"
    }
}