/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ISeekBar.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/04/05
 ** Author: zhangjisong
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>            <version>        <desc>
 ** ------------------------------------------------------------------------------
 ** zhangjisong                     2022/04/05        1.0
 *********************************************************************************/
package com.oplus.gallery.photo_page.widget.seekbar

import androidx.annotation.MainThread
import com.oplus.gallery.photo_page.widget.seekbar.ISeekBar.Companion.progress

/**
 * 为了统一缩图 SeekBar 和系统常规 SeekBar 的操作，故定义了 ISeekBar 接口。
 *
 * 原本想在 ISeekBar 定义接口属性，如 var min: Int，
 * 但是该属性与 Java 编写的 AppCompatSeekBar 的 getMin，setMin 等函数不兼容，这是 kotlin 语法缺陷，暂时只能规避，使用明确的函数声明进行代替。
 * 同时为了保证代码风格同 kotlin 的属性风格，故在 ISeekBar 的伴生对象声明了扩展函数，如 var ISeekBar.min: Int
 *
 * @see progress
 * @see min
 * @see max
 */
interface ISeekBar {

    /**
     * 获取进度最小值
     *
     * @see android.widget.SeekBar.getMin
     */
    fun getMin(): Int


    /**
     * 设置进度最小值
     *
     * @see android.widget.SeekBar.setMin
     */
    @MainThread
    fun setMin(value: Int)


    /**
     * 获取进度最大值
     *
     * @see android.widget.SeekBar.getMax
     */
    fun getMax(): Int


    /**
     * 设置进度最大值
     *
     * @see android.widget.SeekBar.setMax
     */
    @MainThread
    fun setMax(value: Int)


    /**
     * 获取当前进度值
     *
     * @see android.widget.SeekBar.getProgress
     */
    fun getProgress(): Int


    /**
     * 设置当前进度值
     *
     * @see android.widget.SeekBar.setProgress
     */
    @MainThread
    fun setProgress(value: Int, isFromUser: Boolean = false)


    /**
     * 设置或者获取 [ISeekBar] 动作监听器
     *
     * @see android.widget.SeekBar.OnSeekBarChangeListener
     */
    var onSeekBarChangeListener: OnSeekBarChangeListener?


    /**
     * [ISeekBar] 动作监听器，触摸 [ISeekBar] 或者调整 [progress] 时使用
     *
     * @see android.widget.SeekBar.OnSeekBarChangeListener
     */
    interface OnSeekBarChangeListener {

        /**
         * 当 [ISeekBar.progress] 变动时调用
         *
         * @see android.widget.SeekBar.OnSeekBarChangeListener.onProgressChanged
         */
        fun onProgressChanged(seekBar: ISeekBar, progress: Int, fromUser: Boolean)


        /**
         * 当开始触摸 [ISeekBar] 控件时调用
         *
         * @see android.widget.SeekBar.OnSeekBarChangeListener.onStartTrackingTouch
         */
        fun onStartTrackingTouch(seekBar: ISeekBar)


        /**
         * 当停止触摸 [ISeekBar] 控件时调用
         *
         * @see android.widget.SeekBar.OnSeekBarChangeListener.onStopTrackingTouch
         */
        fun onStopTrackingTouch(seekBar: ISeekBar)
    }

    companion object {

        /**
         * 为兼容 Java 形式的 [android.widget.SeekBar.getMin] 和 [android.widget.SeekBar.setMin]，
         * 使编码风格为 kotlin 属性操作，故添加此扩展函数。
         *
         * - 当写入 min 之时，会调用 [ISeekBar.setMin]
         * - 当读取 min 之时，会调用 [ISeekBar.getMin]
         *
         * @see ISeekBar.setMin
         * @see ISeekBar.getMin
         */
        var ISeekBar.min: Int
            get() = getMin()
            @MainThread
            set(value) {
                setMin(value)
            }


        /**
         * 为兼容 Java 形式的 [android.widget.SeekBar.getMax] 和 [android.widget.SeekBar.setMax]，
         * 使编码风格为 kotlin 属性操作，故添加此扩展函数。
         *
         * - 当写入 max 之时，会调用 [ISeekBar.setMax]
         * - 当读取 max 之时，会调用 [ISeekBar.getMax]
         *
         * @see ISeekBar.setMax
         * @see ISeekBar.getMax
         */
        var ISeekBar.max: Int
            get() = getMax()
            @MainThread
            set(value) {
                setMax(value)
            }


        /**
         * 为兼容 Java 形式的 [android.widget.SeekBar.getProgress] 和 [android.widget.SeekBar.setProgress]，
         * 使编码风格为 kotlin 属性操作，故添加此扩展函数。
         *
         * - 当写入 progress 之时，会调用 [ISeekBar.setProgress]
         * - 当读取 progress 之时，会调用 [ISeekBar.getProgress]
         *
         * @see ISeekBar.setProgress
         * @see ISeekBar.getProgress
         */
        var ISeekBar.progress: Int
            get() = getProgress()
            @MainThread
            set(value) {
                setProgress(value)
            }
    }
}