/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ThumbnailLoader.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2021/09/29
 ** Author: zhangjisong
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>            <version>        <desc>
 ** ------------------------------------------------------------------------------
 ** zhangjisong                     2021/09/29        1.0
 *********************************************************************************/
package com.oplus.gallery.business.contentloading.loader

import android.content.Context
import android.graphics.ColorSpace
import android.graphics.drawable.Drawable
import android.media.MediaCodecInfo.CodecCapabilities
import android.media.MediaMetadataRetriever
import android.util.Size
import androidx.annotation.VisibleForTesting
import androidx.annotation.WorkerThread
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business.contentloading.ContentLoader
import com.oplus.gallery.business.contentloading.OnFutureDoneListener
import com.oplus.gallery.business.drawable.AVPlayerDrawable
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.isHasselWaterMarkerPhoto
import com.oplus.gallery.business_lib.model.data.base.item.isOlivePhoto
import com.oplus.gallery.business_lib.model.data.base.item.isOnlinePlayableVideo
import com.oplus.gallery.business_lib.model.data.base.item.isVideo
import com.oplus.gallery.business_lib.model.data.base.item.toOriginalItem
import com.oplus.gallery.business_lib.model.data.base.item.toVideoMediaItem
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_VIDEO_FRAME_RATE
import com.oplus.gallery.foundation.exif.oplus.OplusExifTag.EXIF_TAG_WATER_MARK
import com.oplus.gallery.foundation.exif.utils.hasFlag
import com.oplus.gallery.foundation.fileaccess.FileAccessManager
import com.oplus.gallery.foundation.fileaccess.FileConstants
import com.oplus.gallery.foundation.fileaccess.data.OpenFileRequest
import com.oplus.gallery.foundation.taskscheduling.Cancelable
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.config.args.ConfigExtraArgs
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Codec.PLATFORM_CODEC_CAPABILITY_QUERY
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SYSTEM_SUPPORT_120_FPS_PLAYBACK
import com.oplus.gallery.framework.abilities.resourcing.IResourcingAbility
import com.oplus.gallery.framework.abilities.resourcing.key.LocalMediaResourceKey
import com.oplus.gallery.framework.abilities.resourcing.key.ResourceKey
import com.oplus.gallery.framework.abilities.resourcing.key.ResourceKeyFactory
import com.oplus.gallery.framework.abilities.resourcing.key.SharedMediaResourceKey
import com.oplus.gallery.framework.abilities.resourcing.options.AvPlayerGetOptions
import com.oplus.gallery.framework.app.withAbility
import com.oplus.gallery.standard_lib.app.AppConstants.Degree.DEGREE_180
import com.oplus.gallery.standard_lib.app.AppConstants.Degree.DEGREE_90
import com.oplus.gallery.standard_lib.codec.player.AVPlayer
import com.oplus.gallery.standard_lib.codec.player.adapter.PlayerAdapter.Companion.FPS_120_MIN_AVERAGE_FPS_THRESHOLD
import com.oplus.gallery.standard_lib.codec.player.effect.EffectWrapper
import com.oplus.gallery.standard_lib.scheduler.Job
import com.oplus.gallery.standard_lib.scheduler.JobContext
import com.oplus.gallery.standard_lib.scheduler.session.PrioritySession
import kotlinx.coroutines.CoroutineScope
import java.util.concurrent.Future

internal class AVPlayerLoader(
    private val context: Context,
    mediaItem: MediaItem,
    workSession: PrioritySession,
    workScope: CoroutineScope,
    futureListener: OnFutureDoneListener<Drawable> = { }
) : ContentLoader<Drawable>(mediaItem, workSession, workScope, futureListener) {

    private val cancelable = Cancelable()

    /**
     * 外部输入视频播放参数
     */
    private var outPlayOptions: OuterPlayOption? = null

    override fun onLoadJobCanceled(workingFuture: Future<Drawable>?) {
        super.onLoadJobCanceled(workingFuture)
        cancelable.cancel()
    }

    override fun onCreateLoadJob(): Job<Drawable>? {
        // Marked by: 2021/9/29 check isTemporaryItem

        if (isPlayableMediaItem(mediaItem).not()) {
            GLog.e(TAG) { "[onCreateLoadJob] the mediaItem used to create the AVPlayer loader job must be LocalVideo or OlivePhoto, return null" }
            return null
        }

        return object : Job<Drawable> {
            override fun call(jc: JobContext): Drawable? = runCatching {
                val resourceKey = ResourceKeyFactory.createResourceKey(mediaItem.toOriginalItem()) ?: let {
                    GLog.w(TAG) { "[onCreateLoadJob] failed to create resourceKey, path=${mediaItem.path}" }
                    return null
                }

                val playOptions = AvPlayerGetOptions().apply {
                    targetColorSpace = outPlayOptions?.targetColorSpace
                    shouldPlaybackAt120Fps = couldPlaybackAt120Fps()
                    playType = outPlayOptions?.playType
                    oliveSubVideoInfo = outPlayOptions?.oliveSubVideoInfo
                    videoPlayEffect = outPlayOptions?.videoPlayEffect
                }

                val avPlayer = context.withAbility<IResourcingAbility, AVPlayer?> {
                    it?.requestAVPlayer(key = resourceKey, options = playOptions, cancelable = cancelable)
                } ?: let {
                    GLog.w(TAG) { "[onCreateLoadJob] failed to request AVPlayer, resourceKey=$resourceKey;" }
                    return null
                }

                updatePlayerSourceIfNeed(resourceKey, avPlayer)
                val videoSize = makeMediaItemSize(mediaItem, avPlayer)
                // TBL播放olive的图片的时候，会通过回调纠正olive的图片的角度，可以判断olive图片的时候将其角度设置为0
                val rotation = if (mediaItem.isOlivePhoto()) 0 else mediaItem.rotation
                return AVPlayerDrawable(avPlayer, videoSize, rotation)
            }.onFailure {
                GLog.e(TAG, it) { "[onCreateLoadJob] error to request AVPlayer, path=${mediaItem.path} ;" }
            }.getOrNull()
        }
    }

    fun setOuterPlayOptions(playOptions: OuterPlayOption?) {
        outPlayOptions = playOptions
    }

    /**
     * 检验传入的MediaItem，是否具备可以播放的属性
     */
    private fun isPlayableMediaItem(mediaItem: MediaItem): Boolean = when {
        mediaItem.isOlivePhoto() -> true
        (mediaItem.toVideoMediaItem() != null) -> true
        else -> false
    }

    /**
     * 谷歌云在线视频时,加载在线播放器必须的token
     */
    @WorkerThread
    private fun updatePlayerSourceIfNeed(
        resourceKey: ResourceKey,
        avPlayer: AVPlayer
    ) {
        loadOnlinePlayerConfigIfNeed(resourceKey)?.let { source ->
            avPlayer.setDataSource(avPlayer.dataSource.copy(media3Source = source))
        }
    }

    /**
     * 加载云端视频播放器所必须的配置信息
     * @return 云端视频播放器所必须的配置信息：如token及视频大小和时间
     */
    @WorkerThread
    private fun loadOnlinePlayerConfigIfNeed(key: ResourceKey): AVPlayer.Media3Source? {
        when (key) {
            is LocalMediaResourceKey -> key.mediaItem
            is SharedMediaResourceKey -> key.mediaItem
            else -> {
                GLog.w(TAG) { "[loadOnlinePlayerConfigIfNeed] not local media" }
                null
            }
        }?.takeIf { it.isOnlinePlayableVideo() }?.let { mediaItem ->
            val time = System.currentTimeMillis()
            val token = mediaItem.cloudGlobalId?.let { cloudId ->
                ApiDmManager.getCloudSyncDM().loadSessionToken(cloudId) ?: kotlin.run {
                    // 由于单个视频release时，有时延:当前视频release时,下一个视频立即load时会导致无法loadToken,所以需要延时后再次尝试load
                    Thread.sleep(LOAD_TRY_AGAIN_DELAY)
                    GLog.w(TAG) { "[loadOnlinePlayerConfigIfNeed] loadSessionToken try again" }
                    ApiDmManager.getCloudSyncDM().loadSessionToken(cloudId)
                }
            }
            GLog.d(TAG) {
                "[loadOnlinePlayerConfigIfNeed] finish token=$token id=${mediaItem.cloudGlobalId} " +
                        "cost=${System.currentTimeMillis() - time}"
            }
            return AVPlayer.Media3Source(
                token = token,
                mediaWidth = mediaItem.cloudWidth,
                mediaHeight = mediaItem.cloudHeight,
                duration = mediaItem.duration.toLong(),
            )
        } ?: GLog.d(TAG) { "[loadOnlinePlayerConfigIfNeed] not Google Cloud mediaItem " }
        return null
    }

    private fun makeMediaItemSize(mediaItem: MediaItem, avPlayer: AVPlayer): Size = when {
        mediaItem.isOnlinePlayableVideo() -> Size(mediaItem.cloudWidth, mediaItem.cloudHeight)
        mediaItem.isOlivePhoto() -> calculateOliveVideoSize(mediaItem, avPlayer)
        else -> Size(mediaItem.width, mediaItem.height)
    }

    /**
     * 计算Olive资源的视频的尺寸
     *
     * 创建Drawable不能直接使用MediaItem的宽高，因为Olive比较特殊（是一个图片，内含视频），可能存在图片和视频宽高比例不一致的场景。
     * eg. 带水印的Olive资源， 图片宽高=内容宽高+水印宽高，而视频的宽高比例和图片内容宽高相等，这就导致视频与整张图片宽高比例不一致。
     * 两者宽高比例不一致就会导致视频显示时被拉伸
     *
     * - 不带水印的资源，则直接使用原MediaItem的宽高
     * - 带水印的资源，需要单独去解析出视频的宽高
     * - 后续如果有其他类型的资源导致Olive资源的图片与视频宽高比例不一致的话，请在下方needRetrieveSize补充
     *
     * @param mediaItem 用于获取图片宽高
     * @param avPlayer AVPlayer 用于获取dataSource，以便解析视频的宽高
     *
     * @return Olive资源的宽高size
     */
    private fun calculateOliveVideoSize(mediaItem: MediaItem, avPlayer: AVPlayer): Size {
        // 注意，这里的mediaItem可能是factItem，解码实际使用时，需要使用其实体MediaItem。
        val originalMediaItem = mediaItem.toOriginalItem() ?: let {
            GLog.w(TAG) { "[calculateOliveVideoSize] originalMediaItem not found, path=${mediaItem.path}" }
            return Size(mediaItem.width, mediaItem.height)
        }
        var targetSize = Size(originalMediaItem.width, originalMediaItem.height)

        /**
         * 是否需要“检索取回”尺寸
         * 默认应该是不需要的，即：图片的尺寸和视频的尺寸 宽高比例一致。
         *
         * 1. 但如果像添加了水印的Olive资源，原图片加上水印后，宽高比例和视频的不一致，
         * 而大图显示会按照图片的大小创建View，播放视频时也在上述大小的View中展示，就会因为比例不一致而导致视频被拉伸
         *
         * 目前只有带水印的Olive资源有该问题，若以后新需求也会导致图片比例变更，需要在needRetrieveSize后面加上异或的条件
         *
         * 2.如果走特效框架需要解析视频的真实宽高，不能用封面的宽高，否则会有实况图滑动闪黑的问题
         */
        val needRetrieveSize = mediaItem.isHasselWaterMarkerPhoto() || mediaItem.tagFlags.hasFlag(EXIF_TAG_WATER_MARK)
        val enableFrameFill = (outPlayOptions?.videoPlayEffect != null)
        if (needRetrieveSize.not() && enableFrameFill.not()) {
            GLog.d(TAG) { "<calculateOliveVideoSize> needRetrieveSize is false, no need to retrieve olive video size." }
            return targetSize
        }

        val dataSource = avPlayer.dataSource
        val videoSourceDescriptor = dataSource.videoSourceDescriptor
        if (videoSourceDescriptor !is AVPlayer.VideoSourceDescriptor.MIX) {
            GLog.e(TAG, "<calculateOliveVideoSize> videoSourceDescriptor is not MIX, sth is wrong, skip retrieve olive video size.")
            return targetSize
        }
        val length = videoSourceDescriptor.length
        if (length <= 0) {
            GLog.e(TAG, "<calculateOliveVideoSize> has no length:$length, skip retrieve olive video size.")
            return targetSize
        }
        val offset = videoSourceDescriptor.offset

        val startTime = System.currentTimeMillis()
        val openFileRequest = OpenFileRequest.Builder().apply {
            setImage(true)
            setModeType(FileConstants.FileModeType.MODE_READ)
            setUri(dataSource.uri)
            setFile(dataSource.path)
        }.builder()
        runCatching {
            FileAccessManager.getInstance().openFile(context, openFileRequest)?.use { pfd ->
                if ((pfd.fileDescriptor == null) || pfd.fileDescriptor.valid().not()) {
                    GLog.e(TAG) {
                        "<calculateOliveVideoSize> fileDescriptor=${pfd.fileDescriptor} isValid=${pfd.fileDescriptor?.valid()} " +
                                "source is invalid, skip retrieve olive video size."
                    }
                    return targetSize
                }
                val retriever = MediaMetadataRetriever()
                val subVideoInfo = videoSourceDescriptor.subVideoInfo
                if ((videoSourceDescriptor.playType == AVPlayer.VideoPlayType.SUB) && (subVideoInfo != null)) {
                    retriever.setDataSource(pfd.fileDescriptor, subVideoInfo.offset, subVideoInfo.length)
                } else {
                    retriever.setDataSource(pfd.fileDescriptor, offset, length)
                }
                val width = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH)?.toInt() ?: 0
                val height = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT)?.toInt() ?: 0
                val rotation = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_ROTATION)?.toInt() ?: 0
                // 依据是否旋转而对调宽高
                targetSize = if ((rotation + DEGREE_90) % DEGREE_180 == 0) {
                    Size(height, width)
                } else {
                    Size(width, height)
                }
                GLog.d(TAG) { "<calculateOliveVideoSize> rotation=$rotation targetSize=$targetSize costTimeMs=${GLog.getTime(startTime)}" }
            }
        }.onFailure {
            GLog.e(TAG, "<calculateOliveVideoSize> retrieve video size got error: ${it.message}")
        }
        return targetSize
    }

    /**
     * 判断此视频能否以120FPS预览。包含三个因素：
     * - 系统feature支持
     * - 资源本身帧率达到120fps
     *      - 如果资源帧率为null说明数据尚未解析，默认按照120fps播，由播放器兜底。
     * - 平台能力能满足120fps解码
     *      - 如果codecType为null说明尚未解析，默认按照120fps播，由播放器兜底。
     *      - 未查询到此codecMimeType解码能力，默认按照120fps播，由播放器兜底。
     */
    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun couldPlaybackAt120Fps(): Boolean {
        if (mediaItem.isVideo.not()) {
            return false
        }

        // 1 检查系统feature支持
        if (ConfigAbilityWrapper.getBoolean(FEATURE_IS_SYSTEM_SUPPORT_120_FPS_PLAYBACK).not()) {
            return false
        }

        // 2 检查资源本身帧率达到120fps
        val videoFps = mediaItem.extra?.getFloatExtra(EXTRA_KEY_VIDEO_FRAME_RATE) ?: let {
            // 帧率未知，则由播放器兜底
            GLog.w(TAG, LogFlag.DL) { "[couldPlaybackAt120Fps] unknown fps for ${mediaItem.mediaId}, default 120 fps." }
            FPS_120_MIN_AVERAGE_FPS_THRESHOLD
        }
        if (videoFps < FPS_120_MIN_AVERAGE_FPS_THRESHOLD) {
            return false
        }

        // 3 检查平台能力能满足120fps解码
        val codecType = mediaItem.codecType ?: let {
            // 未知编解码器，此时一样由播放器兜底
            GLog.w(TAG, LogFlag.DL) { "[couldPlaybackAt120Fps] unknown codecType for ${mediaItem.mediaId}, default 120 fps." }
            return true
        }

        /**
         * marked by caiconghu 此处ConfigAbility接口设计的不合理。后续接口改了之后这里的逻辑也要改下。
         * - getConfigWithArgs无法望文生义。后续需要改成queryConfig之类更容易理解的接口名。
         * - 当前传参是通过data class。但是data class 无法和ConfigID对应上，后续需要把ConfigID改成结构体，内部维护参数列表。
         *
         * 具体修改建议见：[com.oplus.gallery.framework.abilities.config.IConfigAbility.getConfigWithArgs]
         */
        val queryArgs = ConfigExtraArgs.CodecConfigExtraArgs(codecType, false)
        val codecCap = ConfigAbilityWrapper.getConfigWithArgs(PLATFORM_CODEC_CAPABILITY_QUERY, queryArgs) as? CodecCapabilities
        // 平台不支持此解码器，不支持120fps播放
        val maxSupportedFps = codecCap?.run {
            // 对于FaceItem之类的mediaItem，需要取原item的真实宽高
            val width = mediaItem.toOriginalItem()?.width ?: 0
            val height = mediaItem.toOriginalItem()?.height ?: 0

            kotlin.runCatching {
                videoCapabilities.getSupportedFrameRatesFor(width, height).upper.toFloat()
            }.onFailure {
                GLog.e(TAG, LogFlag.DL, it) { "[couldPlaybackAt120Fps] failed to get SupportedFps from codecCap. Is your mediaItem size valid?" }
            }.getOrNull()
        } ?: let {
            // 未查询到此codecMimeType解码能力，默认按照120fps播，由播放器兜底。
            GLog.w(TAG, LogFlag.DL) { "[couldPlaybackAt120Fps] not found codec for ${mediaItem.mediaId}(codec: $codecType), default 120 fps." }
            return true
        }

        // 平台支持帧率无法达到120，不支持120播放
        if (maxSupportedFps < videoFps) {
            return false
        }
        return true
    }

    /**
     * 外部输入播放参数配置
     */
    data class OuterPlayOption(
        /**
         * 视频对应的色彩空间
         */
        var targetColorSpace: ColorSpace? = null,

        /**
         * 实况照片视频参数：视频播放类型
         */
        var playType: AVPlayer.VideoPlayType? = null,

        /**
         *实况照片视频参数：小视频信息
         */
        var oliveSubVideoInfo: AVPlayer.VideoInfo? = null,

        /**
         * 实况照片视频参数：视频播放特效列表
         */
        var videoPlayEffect: EffectWrapper? = null
    )

    private companion object {
        private const val TAG = "AVPlayerLoader"
        private const val LOAD_TRY_AGAIN_DELAY = 80L
    }
}