/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : OLiveStateOperator.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/05/23
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_wXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** tan<PERSON><PERSON><PERSON>@Apps.Gallery          2024/05/23     1.0         OPLUS_ARCH_wXTENDS
 *********************************************************************************/
package com.oplus.gallery.business.renderer.olive

import androidx.annotation.UiThread
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business.contentloading.loader.effect.BrightenEffect
import com.oplus.gallery.business.drawable.AVPlayerDrawable
import com.oplus.gallery.business.renderer.olive.OLiveStateOperator.OLiveOperatorEventListener.Companion.ERROR_CODE_PLAY_ERROR
import com.oplus.gallery.business.renderer.olive.OLiveStateRequest.*
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_OLIVE
import com.oplus.gallery.standard_lib.codec.player.AVController
import com.oplus.gallery.standard_lib.codec.player.AVPlayer
import com.oplus.gallery.standard_lib.codec.player.AVPlayer.Companion.errorCodeToString
import com.oplus.gallery.standard_lib.codec.player.AVPlayer.Companion.infoCodeToString
import com.oplus.gallery.standard_lib.codec.player.adapter.PlayerAdapter

/**
 * 配合OLiveRenderer 在其不同状态下对 olive播放的控制
 * 职责：只处理olive状态切换相关业务，不做他用 相当于OLiveRenderer的小VM
 */
internal class OLiveStateOperator(oliveRenderer: OLiveRenderer) {

    /**
     * Operator一些操作事件监听器
     * 1. olive 封面状态变更回调
     * 2. 资源播放异常回调
     */
    private var oliveOperatorEventListener: OLiveOperatorEventListener? = null

    /**
     * 区间播放范围
     *  null   资源不支持区间播放
     * 非null 资源支持区间播放
     * 场景：大图滑动触发olive播放行为所需的播放区间
     * 注意：这与olive2.0编辑里视频裁减后标记的针对原视频的播放区间并不对等，详见 [OLiveInfo.clipPlayPositionRange]
     */
    val clipPositionRange: LongRange?
        get() = oliveInfo.clipPlayPositionRange

    /**
     * 当前OLive资源，视频的时长区间
     */
    val videoPlayRange: LongRange? by lazy {
        oliveInfo.videoPlayRange
    }

    /**
     * OLive 播放开关状态
     */
    val oliveEnable: Boolean
        get() = oliveInfo.oliveEnable

    /**
     * OLive 声音开关状态
     */
    val oliveSoundEnable: Boolean
        get() = oliveInfo.oliveSoundEnable


    /**
     * Olive 是否有换过封面
     */
    val oliveCoverChanged: Boolean
        get() = oliveInfo.oliveCoverChanged

    /**
     * Olive 的视频是否被平台解码器支持
     */
    val isVideoSupportByDecoder: Boolean? get() = oliveInfo.isVideoSupportByDecoder

    /**
     * 是否支持区间播放
     * 场景：olive大图滑动后触发自动播放
     * 0、机型支持滑动播放
     * 1、视频本身有播放区间
     * 2、olive播放状态开启
     * 3、当前平台是否支持此规格的 Olive 视频，详见：[isVideoSupportByDecoder]，无法获取时不自动播放
     */
    val isSupportClipPlay: Boolean
        get() {
            return isSupportOliveSlidePlay
                    && oliveInfo.isSupportClipPlay
                    && oliveInfo.oliveEnable
                    && (isVideoSupportByDecoder == true)
        }

    /**
     * 是否为单解码器设备
     */
    private val isSupportOliveSlidePlay: Boolean by lazy {
        ConfigAbilityWrapper.getBoolean(ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_OLIVE_SLIDE_PLAY)
    }

    /**
     * 当前olive播放状态
     */
    private var playbackState = AVController.PlaybackState.IDLE

    /**
     * 当前播放的视频类型
     */
    private var currentVideoPlayType = AVPlayer.VideoPlayType.MAIN

    private val isFeatureSupportOLive by lazy {
        ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_OLIVE)
    }

    private val oliveInfo: OLiveInfo by lazy {
        OLiveInfo(oliveRenderer)
    }

    /**
     * OperatorState
     */
    private val operatorState: OperatorState
        get() {
            val rendererHasFocused = oliveInfo.rendererHasFocused
            val renderState = oliveInfo.renderer.rendererState

            return when {
                (rendererHasFocused && (renderState == OLiveRenderer.RendererState.PRESENT)) -> OperatorState.OPERATION
                (renderState == OLiveRenderer.RendererState.CONTENT) -> OperatorState.RENDERER
                else -> OperatorState.NORMAL
            }
        }

    private val avControllerManger: AVControllerManger by lazy {
        AVControllerManger(eventListener = InternalPlayerEventListener(this))
    }

    /**
     * 记录因播放器处于 Idle 状态需要延迟播放的 startPlayOLive 请求。
     */
    private var deferredStartPlayRequest: StartPlayRequest? = null

    /**
     * 当render的ContentSource变化时，需要重置一些资源
     * 1.界面回复默认态
     * 2.切换播放器
     */
    fun notifyWhenRendererContentSourceChanged() {
        avControllerManger.changeAVController(oliveInfo.avController)
    }

    // ----------------------------------------焦点业务
    /**
     *  开始播放olive图片视频内容
     *
     *  @param positionRange olive图片里的视频需播放指定的区间
     *
     *  @param isMute 播放时是否设置播放器静音
     *
     *  @param autoChangeToCover  true   封面状态的切换会根据播放器状态/其他条件自动维护，默认为true  推荐
     *                            false  在stopPlayOLive时会调用切换到封面，不响应内部播放器状态的变更
     *
     *  @param autoChangeToVideo  true   视频状态的切换会根据播放器状态/其他条件自动维护，默认为true  推荐
     *                            false  只会在外部调用 startPlayOLive 函数时被执行一次，慎用！！！- 如果播放器第一帧没渲染，会存在闪黑问题
     *  @param playEffectList 播放特效列表
     */
    fun startPlayOLive(
        positionRange: LongRange? = null,
        isMute: Boolean = false,
        autoChangeToCover: Boolean = true,
        autoChangeToVideo: Boolean = true,
        playEffectList: List<String>? = null,
        playType: AVPlayer.VideoPlayType = AVPlayer.VideoPlayType.MAIN
    ) {
        if (isFeatureSupportOLive.not()) {
            GLog.d(TAG, "[startPlayOLive] feature is not support OLive, return")
            return
        }

        if (ensurePlayerCanPlay(positionRange, isMute, autoChangeToCover, autoChangeToVideo, playEffectList).not()) {
            GLog.d(TAG, LogFlag.DL) { "[startPlayOLive] called, player cannot play" }
            return
        }

        oliveInfo.autoChangeToCover = autoChangeToCover
        oliveInfo.autoChangeToVideo = autoChangeToVideo

        requestChangeOLiveStateIfNeed(COVER_TO_VIDEO)

        oliveInfo.isPlayedCompleted = false

        adjustPlayerMuteState(isMute)

        GLog.d(TAG) { "[startPlayOLive] positionRange = $positionRange, oliveInfo = $oliveInfo" }

        positionRange?.takeIf {
            (it.first >= 0) && (it.last > 0) && (it.first < it.last)
        }.let { avControllerManger.play(it, playEffectList, playType) }
    }

    /**
     * 通知当前界面切换到cover状态
     * @param force  默认值 false 根据当前Operator状态切换到封面：[OperatorState.OPERATION]下才可以被更新
     *                     true  强制切换到封面状态
     */
    fun notifyResetOLiveStateToCoverIfNeed(force: Boolean = false) {
        if (force.not() && operatorState != OperatorState.OPERATION) {
            GLog.d(TAG) { "[notifyResetOLiveStateToCoverIfNeed] force = false && operatorState ！= OPERATION. return" }
            return
        }
        requestChangeOLiveStateIfNeed(
            request = VIDEO_TO_COVER,
            transitionType = OLiveTransitionType.NORMAL, // 不可以带动画，要不然用户会有感知
            force = true
        )
    }

    /**
     * 停止播放Olive内容
     */
    fun stopPlayOLive() {
        requestChangeOLiveStateIfNeed(VIDEO_TO_COVER)
        avControllerManger.pause()
    }

    /**
     * 更新Olive状态
     * 保持封面状态下，OLive只展示封面，不支持播放，不支持展示视频帧
     * 退出此状态后，如果当前处于RENDERER状态，且有焦点，且有播放区间，且有播放准备状态，则自动切换到视频播放状态
     * @param shouldKeepCoverState 是否保持封面状态
     */
    fun updateOLiveStateForCoverMode(shouldKeepCoverState: Boolean) {
        if (shouldKeepCoverState) {
            notifyResetOLiveStateToCoverIfNeed(true)
            avControllerManger.pause()
        } else {
            val paybackStatePrepared = (playbackState != AVController.PlaybackState.PREPARING)
                    && (playbackState != AVController.PlaybackState.INITIALIZED)
                    && (playbackState != AVController.PlaybackState.IDLE)

            if ((oliveInfo.renderer.oliveState != OLiveState.VIDEO)
                && (operatorState == OperatorState.RENDERER)
                && isSupportClipPlay
                && (paybackStatePrepared)
                && (oliveInfo.rendererHasFocused.not())
            ) {
                requestChangeOLiveStateIfNeed(request = COVER_TO_VIDEO, transitionType = OLiveTransitionType.NORMAL, true)
            }
        }
    }

    /**
     * 更新屏幕HdrSdr比例
     * @param ratio 如果屏幕有被提亮，屏幕提亮后的亮度相比于基准亮度的scale值
     */
    fun updateHdrSdrRatio(ratio: Float) {
        avControllerManger.updateHdrSdrRatio(ratio)
    }

    /**
     * 调整播放器静音状态
     *
     * @param forceMute 是否强制静音
     */
    private fun adjustPlayerMuteState(forceMute: Boolean) {
        if (forceMute) avControllerManger.mutePlayer() else avControllerManger.unMutePlayer()
        GLog.d(TAG) { "[adjustPlayerMuteState] forceMute = $forceMute" }
    }

    /**
     * 设置OLiveOperator事件监听
     */
    fun setOLiveOperatorEventListener(eventListener: OLiveOperatorEventListener?, isSticky: Boolean = false) {
        oliveOperatorEventListener = eventListener
        if (isSticky.not()) {
            return
        }
        eventListener?.onOLiveStateChanged(this, oliveInfo.renderer.oliveState)
    }

    /**
     * 必要的场景更新封面状态
     * 1.焦点图
     * 2.不支持滑动区间播放（資源不支持/交互不支持）
     * 3.非[OperatorState.RENDERER]状态不支持
     *
     * 规避：bug 8530257，在不支持滑动播放olive，非滑动大图浏览图片场景（点击缩图轴切换等），因大图状态机攀升慢导致的闪（1-3帧）新焦点图视频界面问题
     *
     * @param isPageSelectedFormUserDrag 大图page的选中，是否来自用户的拖拽触发
     */
    internal fun updateToCoverStateIfNeed(isPageSelectedFormUserDrag: Boolean) {
        if (oliveInfo.rendererHasFocused.not() || isSupportClipPlay.not() || isPageSelectedFormUserDrag) {
            return
        }

        // 只接受RENDERER状态的更新。即只有划入的焦点在没有达到OPERATION前可以设置
        if (operatorState != OperatorState.RENDERER) {
            return
        }

        notifyResetOLiveStateToCoverIfNeed(true)
    }

    /**
     * 当Renderer状态发生变更时
     * @param rendererState 当前renderer处于的状态
     * 详见：[OLiveRenderer.RendererState]
     */
    internal fun onOLiveRendererStateChanged(rendererState: OLiveRenderer.RendererState) {
        val state = operatorState
        GLog.d(TAG) { "[onOLiveRendererStateChanged] rendererState = $rendererState, oliveInfo = $oliveInfo" }
        when {
            (isSupportClipPlay && (state == OperatorState.RENDERER) && oliveInfo.rendererHasFocused.not()) -> {
                setOLiveOperatorEventListener(null)
                avControllerManger.apply {
                    pause()
                    //实况有小视频时，如果失去焦点，不需要seek。播放时会主动seek到0为止
                    if (oliveInfo.subVideoInfo == null) {
                        clipPositionRange?.start?.let(avControllerManger::seek)
                    }
                }
            }

            (state == OperatorState.NORMAL) -> avControllerManger.clearCurrentPlayer()
        }
    }

    /**
     * 当前播放器是否处于播放完成状态
     */
    fun isPlayedCompleted() = oliveInfo.isPlayedCompleted

    /**
     * 播放事件监听器，用于接收当前播放器的相关事件。
     * - 异常。
     * - 输出信息。
     * - 状态改变。
     */
    private inner class InternalPlayerEventListener(val operator: OLiveStateOperator) : AVController.OnEventListener {
        override fun onError(avController: AVController, what: Int, extra: Int, details: String) {
            GLog.d(TAG) {
                "[onError] avController = ${avController.hashCode()}, what = ${errorCodeToString(what)}, extra = ${errorCodeToString(extra)} " +
                    ", details: $details"
            }
            operator.onOLivePlayerError(avController, extra)
        }

        override fun onInfo(avController: AVController, what: Int, extra: Int, details: String) {
            GLog.d(TAG) {
                "[onInfo] avController = ${avController.hashCode()}, what = ${infoCodeToString(what)} ,extra = ${infoCodeToString(extra)} , " +
                    "details = $details "
            }
            operator.onOLivePlayerInfo(avController, what)

            if (what == AVController.INFO_PLAYING_INFO_READY) {
                startDeferredPlay()
            }
        }

        override fun onPlaybackStateChanged(avController: AVController, state: AVController.PlaybackState) {
            GLog.d(TAG) { "[onPlaybackStateChanged] avController = ${avController.hashCode()}, state = $state" }
            synchronized(playbackState) {
                playbackState = state
            }
            operator.onOLivePlayerStateChanged(avController, state)
        }
    }

    /**
     * 当播放器状态异常时调用
     * 注意：此方法只能是player相关状态的回调使用
     */
    private fun onOLivePlayerError(avController: AVController, errorCode: Int) {
        runMainThread {
            //当播放器播放报错时，通知vm
            notifyFocusOlivePlayError()
        }
    }

    private fun notifyFocusOlivePlayError() {
        if (operatorState != OperatorState.OPERATION) {
            return
        }
        oliveOperatorEventListener?.onError(this, ERROR_CODE_PLAY_ERROR)
    }

    /**
     * 当播放器打印的基础信息
     * 注意：此方法只能是player相关状态的回调使用
     */
    private fun onOLivePlayerInfo(avController: AVController, what: Int) {
        /**
         * 避免从相机进入大图，olive图在大图滑动过程中PREPARING状态下surfaceView第一帧没有绘制，
         * 收到onSeekComplete回调会从cover切换到video，导致透出底下相机页面
         */
        runMainThread {
            val isNotPreparingSeekComplete = (playbackState != AVController.PlaybackState.PREPARING)
                    && (AVController.INFO_SEEK_COMPLETE == what)
            if (isNotPreparingSeekComplete
                && isSupportClipPlay
                && oliveInfo.rendererHasFocused.not()
                && (operatorState == OperatorState.RENDERER)
            ) {
                GLog.d(TAG) { "[onOLivePlayerInfo] aVController = ${avController.hashCode()}, what = INFO_SEEK_COMPLETE" }
                requestChangeOLivStateWhenPlayerStateChangedIfNeed(COVER_TO_VIDEO)
                avController.setPlayVideoType(AVPlayer.VideoPlayType.SUB)
            }
        }
    }

    /**
     * 当播放器状态改变时调用
     * 注意：此方法只能是player相关状态的回调使用
     */
    private fun onOLivePlayerStateChanged(avController: AVController, playbackState: AVController.PlaybackState) {
        runMainThread {
            val state = operatorState
            val hasFocused = oliveInfo.rendererHasFocused

            GLog.d(TAG) {
                "[onOLivePlayerStateChanged] hasFocused = $hasFocused, operatorState = $state, " +
                    "playbackState = $playbackState, aVController = ${avController.hashCode()}"
            }

            when {
                (state == OperatorState.OPERATION) -> {
                    when (playbackState) {
                        AVController.PlaybackState.STARTED -> requestChangeOLivStateWhenPlayerStateChangedIfNeed(request = COVER_TO_VIDEO)
                        else -> requestChangeOLivStateWhenPlayerStateChangedIfNeed(request = VIDEO_TO_COVER)
                    }
                }

                (hasFocused.not() && (state == OperatorState.RENDERER) && isSupportClipPlay) -> {
                    when (playbackState) {
                        AVController.PlaybackState.PREPARED -> requestChangeOLivStateWhenPlayerStateChangedIfNeed(request = COVER_TO_VIDEO)
                        else -> {}
                    }
                }

                else -> {}
            }

            oliveInfo.isPlayedCompleted = (playbackState == AVController.PlaybackState.COMPLETED)
        }
    }

    /**
     * 当播放器状态改变时：请求切换olive图片的显示状态
     *
     * 注意：此方法只能是player相关状态的回调使用
     */
    private fun requestChangeOLivStateWhenPlayerStateChangedIfNeed(
        request: OLiveStateRequest,
        transitionType: OLiveTransitionType = OLiveTransitionType.DEFAULT_ANIMATION
    ) {
        when (operatorState) {
            OperatorState.OPERATION -> {
                val notNeedAutoToCover = (oliveInfo.autoChangeToCover.not() && (request == VIDEO_TO_COVER))
                val notNeedChangeToVideo = (oliveInfo.autoChangeToVideo.not() && (request == COVER_TO_VIDEO))

                if (notNeedAutoToCover || notNeedChangeToVideo) {
                    // 当olive状态，不需要跟随播放器状态自动切换时return： 比如长按播放olive场景，回到封面状态需要根据手指是否抬起来决定和播放内容状态毫无关系
                    GLog.d(TAG) {
                        "[requestChangeOLivStateWhenPlayerStateChangedIfNeed] autoChangeToCover = ${oliveInfo.autoChangeToCover}," +
                            " autoChangeToVideo = ${oliveInfo.autoChangeToVideo}. return"
                    }
                    return
                }
            }

            else -> {}
        }
        if (oliveInfo.shouldKeepCoverState && (request == COVER_TO_VIDEO)) {
            GLog.d(TAG, LogFlag.DL) {
                "[requestChangeOLivStateWhenPlayerStateChangedIfNeed] shouldKeepCoverState = ${oliveInfo.shouldKeepCoverState}," +
                        " request = $request. return"
            }
            return
        }

        requestChangeOLiveState(request, transitionType)
    }


    /**
     * 当满足一定条件时直接切换olive的显示状态：
     * 条件：
     * 1.不需要自动切换(根据播放器状态改变)显示状态时
     * 2.需要强制更新时
     * @param request 期望切换到的OLiveState
     * @param force 强制请求更新olive状态
     */
    private fun requestChangeOLiveStateIfNeed(
        request: OLiveStateRequest,
        transitionType: OLiveTransitionType = OLiveTransitionType.DEFAULT_ANIMATION,
        force: Boolean = false
    ) {
        when {
            (force || oliveInfo.autoChangeToCover.not()) && ((request == VIDEO_TO_COVER)) -> requestChangeOLiveState(request, transitionType)

            (force || oliveInfo.autoChangeToVideo.not()) && ((request == COVER_TO_VIDEO)) -> requestChangeOLiveState(request, transitionType)
        }
    }

    /**
     * 请求改变olive的显示状态：是否成功/结果以 [notifyWhenOLiveStateChanged] 为准
     *
     * @param request 期望达到的状态
     * @param transitionType 改变状态时的过渡类型，默认为OLiveTransitionType.DEFAULT_ANIMATION动画
     * 详见[OLiveTransitionType]
     */
    @UiThread
    private fun requestChangeOLiveState(
        request: OLiveStateRequest,
        transitionType: OLiveTransitionType = OLiveTransitionType.DEFAULT_ANIMATION
    ) {
        oliveInfo.renderer.requestMigrateState(request, transitionType)
    }

    /**
     * 切换到主线程
     * @param todoCallBack 主线程回调
     * 注意：这里的切换依赖view持有的attachInfo里的mHandler：即这个只有在view被加到view树上后才会被执行
     */
    private fun runMainThread(todoCallBack: () -> Unit) {
        /*
         * 对于大图的业务来说，presentation 在shouldPrepareContentRenderer之后才被加到view树上
         * 即：post的Runnable被执行在shouldPrepareContentRenderer时机之后
         */
        oliveInfo.renderer.presentation.post {
            todoCallBack.invoke()
        }
    }

    /**
     * 当Renderer oliveState 切换成功后，通知operator
     *
     * @param state 当前olive状态
     */
    internal fun notifyWhenOLiveStateChanged(state: OLiveState) {
        GLog.d(TAG) { "[notifyWhenOLiveStateChanged] state = $state, oliveInfo = $oliveInfo" }
        notifyOliveStateChangedIfNeed(state)
    }

    private fun notifyOliveStateChangedIfNeed(state: OLiveState) {
        if (operatorState != OperatorState.OPERATION) {
            // 只支持焦点可见状态的通知
            return
        }

        //在切换到封面后，视频seek到实况照片视频起始位置，防止再次长按出现封面和视频停止帧重叠显示的问题
        if (state == OLiveState.COVER) {
            val videoStartMs = ((oliveInfo.renderer.contentSource?.videoStartUs) ?: 0) / ONE_MS_IN_US
            avControllerManger.seek(videoStartMs)
        }

        oliveOperatorEventListener?.onOLiveStateChanged(this, state)
    }

    /**
     * 检查播放器的状态是否可以进行播放，
     * 如果不能播放则记录此次播放请求，待播放器准备完毕后再进行播放。
     * 入参对齐 [startPlayOLive]
     * @see startDeferredPlay
     *
     * @param positionRange olive图片里的视频需播放指定的区间
     * @param isMute 播放时是否设置播放器静音
     * @param autoChangeToCover true   封面状态的切换会根据播放器状态/其他条件自动维护，默认为true  推荐
     *                          false  在stopPlayOLive时会调用切换到封面，不响应内部播放器状态的变更
     * @param autoChangeToVideo true   视频状态的切换会根据播放器状态/其他条件自动维护，默认为true  推荐
     *                          false  只会在外部调用 startPlayOLive 函数时被执行一次，慎用！！！- 如果播放器第一帧没渲染，会存在闪黑问题
     * @param playEffectList 播放特效列表
     *
     * @return true 如果播放器可以进行播放，false 则播放器当前不支持播放
     */
    private fun ensurePlayerCanPlay(
        positionRange: LongRange? = null,
        isMute: Boolean = false,
        autoChangeToCover: Boolean = true,
        autoChangeToVideo: Boolean = true,
        playEffectList: List<String>? = null
    ): Boolean {
        return if (playbackState == AVController.PlaybackState.IDLE) {
            // 播放器还在初始状态，尚未开始加载，延后播放的调用
            deferredStartPlayRequest = StartPlayRequest(
                positionRange = positionRange,
                isMute = isMute,
                autoChangeToCover = autoChangeToCover,
                autoChangeToVideo = autoChangeToVideo,
                playEffectList = playEffectList,
            )
            false
        } else {
            deferredStartPlayRequest = null
            true
        }
    }

    /**
     * 在视频准备完成后，对先前延迟的播放请求做播放。
     * 播放条件：
     *   - 需已设置 deferredStartPlayRequest
     *   - 当前为可交互状态
     *   - 播放器仅处于 prepared 状态
     * 调用处：[InternalPlayerEventListener.onInfo] 且 what 为 AVController.INFO_PLAYING_INFO_READY
     * @see ensurePlayerCanPlay
     */
    private fun startDeferredPlay() {
        deferredStartPlayRequest?.let { request ->
            val canPlayOlive = operatorState == OperatorState.OPERATION
            val shouldPlayByPlayer = playbackState == AVController.PlaybackState.PREPARED
            if (canPlayOlive && shouldPlayByPlayer) {
                startPlayOLive(
                    positionRange = request.positionRange,
                    isMute = request.isMute,
                    autoChangeToCover = request.autoChangeToCover,
                    autoChangeToVideo = request.autoChangeToVideo,
                    playEffectList = request.playEffectList,
                    playType = currentVideoPlayType
                )
            }
            deferredStartPlayRequest = null
        }
    }

    /**
     * olive播控部分
     */
    private inner class AVControllerManger(
        private var avController: AVController = AVController.EMPTY,
        private val eventListener: InternalPlayerEventListener
    ) {

        /**
         * 切换播放器
         */
        fun changeAVController(controller: AVController) {
            if (controller == avController) {
                return
            }
            clearCurrentPlayer()
            avController = controller.apply {
                addOnEventListener(eventListener)
            }
        }

        /**
         * 执行播放操作
         *
         * @param positionRange 指定的播放的区间
         * @param playEffectList 播放特效列表
         */
        fun play(
            positionRange: LongRange? = null,
            playEffectList: List<String>? = null,
            playType: AVPlayer.VideoPlayType = AVPlayer.VideoPlayType.MAIN
        ) {
            //实况照片播放小视频时，需要将range参数丢弃
            val playRange = if ((playType == AVPlayer.VideoPlayType.SUB) && (oliveInfo.subVideoInfo != null)) null else positionRange

            if (getCurrentPosition() > 0L) {
                // 对于olive的播放业务来说：当positionRange为null时，就意味着必须从头播放，不存在接着播放，故需要先seek到0
                GLog.d(TAG) { "[play] player is playing, seek to zero" }
                seek(playRange?.start ?: 0L)
            }

            //如果有特效的话，设置特效列表
            playEffectList?.let { if (isPlaying().not()) { avController.setVideoEffects(it) } }
            currentVideoPlayType = playType
            avController.setPlayVideoType(playType)
            avController.play(playRange)
        }

        /**
         * 是否正在播放
         */
        fun isPlaying() = avController.isPlaying()

        /**
         * 执行暂停操作
         */
        fun pause() {
            avController.pause()
        }

        /**
         * 更新屏幕HdrSdr比例
         * @param ratio 如果屏幕有被提亮，屏幕提亮后的亮度相比于基准亮度的scale值
         * ---
         * marked by caiconghu 此处getPlayEffect as OliveXXEffect从基类拆箱成具体子类，然后调用子类getXX方法，这不符合多态性的设计。
         * 应该改造下：这个getXXX方法放到基类去。基类默认实现返回null，然后子类再定义具体的返回值，这样就可以避免破坏多态。
         */
        fun updateHdrSdrRatio(ratio: Float) {
            val effectList = ((avController as? AVPlayer)?.getVideoPlayEffect())?.coordinators
            val effect = effectList?.firstNotNullOfOrNull { it as? BrightenEffect }
            effect?.ratio = ratio
        }

        /**
         * UI触发，快进操作
         */
        fun seek(position: Long) {
            if ((position > 0) && (getCurrentPosition() == position)) {
                GLog.w(TAG) { "[OLiveStateOperator] seek callback onInfo = INFO_SEEK_COMPLETE avController = ${avController.hashCode()}" }
                eventListener.onInfo(avController, AVController.INFO_SEEK_COMPLETE, AVController.INFO_UNKNOWN, PlayerAdapter.NULL_INFO)
                return
            }
            avController.seekTo(position = position, seekType = AVController.SeekType.NORMAL)
        }

        /**
         * 获取当前播放器的position
         */
        fun getCurrentPosition(): Long = avController.getCurrentPosition()

        /**
         * 获取当前视频的duration
         */
        fun getDuration(): Long = avController.getDuration()

        /**
         * 设置播放器静音
         * 注意：这里只是播放器的音频输出，不是系统级别的静音
         */
        fun mutePlayer() {
            avController.setMute(true)
        }

        /**
         * 设置播放器非静音状态
         * 注意：这里只是播放器的音频输出，不是系统级别的接解除静音
         */
        fun unMutePlayer() {
            avController.setMute(false)
        }

        /**
         * 清除 AVController 记录，移除回调等
         */
        fun clearCurrentPlayer() {
            playbackState = AVController.PlaybackState.IDLE
            avController.removeOnEventListener(eventListener)
            avController.pause()
        }
    }

    /**
     * olive 图片的基础信息
     * 渲染器，播放器，资源类型等
     */
    private data class OLiveInfo(
        /**
         * olive图片的渲染器
         */
        val renderer: OLiveRenderer,

        /**
         * 标记单次呼器olive播放：是否根据播放器状态自动控制切换到封面状态
         */
        var autoChangeToCover: Boolean = true,

        /**
         * 标记单次呼器olive播放：是否根据播放器状态自动控制切换到视频状态
         */
        var autoChangeToVideo: Boolean = true,

        /**
         * 对应的播放器，是否处于播放完成状态
         */
        var isPlayedCompleted: Boolean = false
    ) {
        /**
         * olive播放器Controller
         */
        val avController: AVController
            get() {
                return (renderer.contentSource?.videoDrawable as? AVPlayerDrawable)?.avPlayer ?: AVController.EMPTY
            }

        val rendererHasFocused: Boolean
            get() = renderer.hasFocused

        /**
         * olive2.0
         * 当durationOfClipPlayMs时间于180ms或者资源异常时,返回null -- 资源不支持滑动自动区间播放
         * 当durationOfClipPlayMs在180ms(包含)-[OLIVE_CLIP_PLAY_POSITION_MAX_LIMIT] (包含)时, 返回 [OLiveImageContent.videoStartUs] - 封面时间
         * 当durationOfClipPlayMs大于[[OLIVE_CLIP_PLAY_POSITION_MAX_LIMIT]时，返回 （封面时间-[OLIVE_CLIP_PLAY_POSITION_MAX_LIMIT]）- 封面时间
         *
         * 注释: durationOfClipPlayMs为[封面时间]与[olive指定资源指定的起始位置]的差值
         */
        val clipPlayPositionRange: LongRange?
            get() {
                val imageContent = renderer.contentSource ?: let {
                    GLog.d(TAG) { "[getClipPositionRange] contentSource is null" }
                    return null
                }

                // olive 2.0后，编辑可以指定视频播放区间，这里取指定区间开始的位置与封面位置的差值做比较，即：滑动自动播放可播放的时间区间
                val durationOfClipPlayMs = (imageContent.coverTimeInUs - imageContent.videoStartUs) / ONE_MS_IN_US
                if (durationOfClipPlayMs < OLIVE_CLIP_PLAY_POSITION_MIN_LIMIT_IN_MS) {
                    GLog.d(TAG) { "[getClipPositionRange] coverTimeMs < ${OLIVE_CLIP_PLAY_POSITION_MIN_LIMIT_IN_MS}ms return null" }
                    return null
                }

                val coverTimeMs = (imageContent.coverTimeInUs / ONE_MS_IN_US).coerceAtLeast(0)
                val startPosition: Long = (coverTimeMs - OLIVE_CLIP_PLAY_POSITION_MAX_LIMIT)
                    .coerceAtLeast(imageContent.videoStartUs / ONE_MS_IN_US)
                    .coerceAtLeast(0)
                val endPosition: Long = coverTimeMs

                return LongRange(startPosition, endPosition)
            }

        /**
         * 当前OLive资源，视频的时长区间
         */
        val videoPlayRange: LongRange?
            get() {
                val oliveContent = renderer.contentSource
                if (oliveContent == null) {
                    GLog.e(TAG, LogFlag.DL) { "[getVideoPlayRange] contentSource is not oliveDrawable" }
                    return null
                }

                if ((oliveContent.videoStartUs < 0)
                    || (oliveContent.videoEndUs <= 0)
                    || (oliveContent.videoStartUs >= oliveContent.videoEndUs)
                ) {
                    GLog.e(TAG, LogFlag.DL) {
                        "[getVideoPlayRange] time is error, videoStartUs=${oliveContent.videoStartUs} videoEndUs=${oliveContent.videoEndUs}"
                    }
                    return null
                }

                return LongRange(oliveContent.videoStartUs / ONE_MS_IN_US, oliveContent.videoEndUs / ONE_MS_IN_US)
            }

        /**
         * OLive 播放开关状态
         */
        val oliveEnable: Boolean
            get() {
                val oliveContent = renderer.contentSource
                if (oliveContent == null) {
                    GLog.e(TAG, LogFlag.DL) { "[getOliveEnable] contentSource is not oliveDrawable" }
                    return true
                }
                return oliveContent.oliveEnable
            }

        /**
         * OLive 声音开关状态
         */
        val oliveSoundEnable: Boolean
            get() {
                val oliveContent = renderer.contentSource
                if (oliveContent == null) {
                    GLog.e(TAG, LogFlag.DL) { "[getOliveSoundEnable] contentSource is not oliveDrawable" }
                    return true
                }
                return oliveContent.oliveSoundEnable
            }


        /**
         * OLive 封面是否已切换
         */
        val oliveCoverChanged: Boolean
            get() {
                val oliveContent = renderer.contentSource
                if (oliveContent == null) {
                    GLog.e(TAG, LogFlag.DL) { "[oliveCoverChanged] contentSource is not oliveDrawable" }
                    return false
                }
                return oliveContent.isCoverChanged
            }

        /**
         * 是否支持区间播放
         */
        val isSupportClipPlay: Boolean
            get() {
                return (clipPlayPositionRange != null)
            }

        /**
         * Olive 的视频是否被平台解码器支持
         */
        val isVideoSupportByDecoder: Boolean?
            get() = renderer.contentSource?.isVideoSupportByDecoder

        /**
         * 是否保持在Cover封面状态
         * 详情模式下，封面状态会一直保持
         */
        val shouldKeepCoverState: Boolean
            get() = renderer.shouldKeepCoverState

        /**
         * 双视频实况，小视频信息
         */
        val subVideoInfo: AVPlayer.VideoInfo?
            get() = renderer.contentSource?.subVideoInfo

        private val slot: Int
            get() = renderer.logIndex

        override fun toString(): String {
            return " OLiveInfo[ slot = $slot," +
                    " rendererHasFocused = $rendererHasFocused" +
                    " avController = ${avController.hashCode()}," +
                    " renderer = ${renderer.hashCode()}," +
                    " clipPlayPositionRange = $clipPlayPositionRange" +
                    " isPlayedCompleted = $isPlayedCompleted," +
                    " autoChangeToCover = $autoChangeToCover," +
                    " autoChangeToVideo = $autoChangeToVideo," +
                    " oliveEnable = $oliveEnable," +
                    " oliveSoundEnable = $oliveSoundEnable]"
        }
    }

    /**
     * 操作状态：用来标记当前Operator的状态，可以响应的对应业务
     *
     * Operator 状态响应的对应业务如下：
     * NORMAL    >  初始态
     * RENDERER  >  OLiveRenderer
     * OPERATION >  OLiveRenderer + PhotoOliveViewModel（焦点可见业务逻辑）
     */
    private enum class OperatorState {
        /**
         * 初始状态
         */
        NORMAL,

        /**
         * 渲染器状态：控制权在render
         * 1. renderer处于焦点状态 但是其内容状态未达到[OLiveRenderer.RendererState.PRESENT]
         * 2. renderer处于非焦点状态
         */
        RENDERER,

        /**
         * vm可以有具备一定的控制状态 即：可以和用户交互
         * 1.renderer处于焦点状态且其内容状态达到[OLiveRenderer.RendererState.PRESENT]
         */
        OPERATION
    }

    /**
     * 播放 Olive 视频的请求，内部参数详见：[startPlayOLive]
     */
    private data class StartPlayRequest(
        val positionRange: LongRange?,
        val isMute: Boolean,
        val autoChangeToCover: Boolean,
        val autoChangeToVideo: Boolean,
        val playEffectList: List<String>?
    )

    /**
     * OLiveOperator 事件监听
     */
    internal interface OLiveOperatorEventListener {
        /**
         * olive 状态变化
         */
        fun onOLiveStateChanged(operator: OLiveStateOperator, state: OLiveState)

        /**
         * olive操作异常时：目前只有播放失败
         */
        fun onError(operator: OLiveStateOperator, errCode: Int)

        companion object {
            /**
             * 视频播放失败
             */
            const val ERROR_CODE_PLAY_ERROR = 0x1
        }
    }

    private companion object {
        private const val TAG = "OLiveStateOperator"
        private const val OLIVE_CLIP_PLAY_POSITION_MIN_LIMIT_IN_MS = 180L
        private const val ONE_MS_IN_US = 1000L

        private val OLIVE_CLIP_PLAY_POSITION_MAX_LIMIT by lazy {
            ConfigAbilityWrapper.getLong(ConfigID.Business.Pages.PhotoPage.Olive.OLIVE_CLIP_PLAY_POSITION_MAX_LIMIT_IN_MS)
        }
    }
}