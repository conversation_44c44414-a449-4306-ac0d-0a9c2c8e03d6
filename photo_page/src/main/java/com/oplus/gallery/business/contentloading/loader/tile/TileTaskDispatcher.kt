/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - TileTaskDispatcher.kt
 ** Description:
 *  职责如下：
 *        1.负责ITileTaskManager任务调度：优先级
 **
 ** Version: 1.0
 ** Date: 2021/09/28
 ** Author: yaoweihe
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>            <version>        <desc>
 ** ------------------------------------------------------------------------------
 ** yaoweihe                     2021/09/28        1.0
 *********************************************************************************/
package com.oplus.gallery.business.contentloading.loader.tile

import android.os.Handler
import android.os.HandlerThread
import androidx.annotation.VisibleForTesting
import com.oplus.gallery.standard_lib.scheduler.session.WorkerSession
import com.oplus.gallery.foundation.util.debug.GLog
import java.util.Queue
import java.util.concurrent.LinkedBlockingQueue

/**
 * 负责ITileTaskManager间任务调度：根据优先级
 */
class TileTaskDispatcher(
    private val session: WorkerSession,
    /**
     * 并行任务数量限制，默认为4
     */
    private val jobLimitSize: Int = TILE_JOB_LIMIT
) {
    private val foregroundTaskManagerList: Queue<ITaskManager<TileTask>> = LinkedBlockingQueue()
    private val backgroundTaskManagerList: Queue<ITaskManager<TileTask>> = LinkedBlockingQueue()
    private val launchNextRunnable = Runnable {
        launchNextTask()
    }

    private var isStop = false
    private var handler: Handler? = null

    @Volatile
    private var curTileTaskManager: ITaskManager<TileTask>? = null

    /**
     * 当队列中存在任务变化时，触发重新排队
     */
    val onTaskChanged: (() -> Unit) = {
        active()
    }

    init {
        val thread = HandlerThread("TileTaskDispatcher")
        thread.start()
        handler = Handler(thread.looper)

        start()
    }

    fun start() {
        GLog.d(TAG, "start.")
        isStop = false
        active()
    }

    /**
     * 激活任务池，开始执行任务
     */
    fun active() {
        requestNext()
        curTileTaskManager = null
    }

    fun stop() {
        GLog.d(TAG, "stop.")
        isStop = true
    }

    fun release() {
        GLog.d(TAG, "release.")
        clearTaskManager()
        handler?.removeCallbacksAndMessages(null)
        handler?.looper?.quitSafely()
        handler = null
    }

    /**
     * **增加前台任务管理器**
     *
     * @param tileTaskManager 任务管理器
     */
    fun addForegroundTaskManager(tileTaskManager: ITaskManager<TileTask>) {
        if (foregroundTaskManagerList.contains(tileTaskManager).not()) {
            foregroundTaskManagerList.add(tileTaskManager)
        }
        backgroundTaskManagerList.remove(tileTaskManager)
        active()
    }

    /**
     * **增加后台任务管理器**
     *
     * @param tileTaskManager 任务管理器
     */
    fun addBackgroundTaskManager(tileTaskManager: ITaskManager<TileTask>) {
        GLog.d(TAG, "addBackgroundTaskManager $tileTaskManager")
        if (backgroundTaskManagerList.contains(tileTaskManager).not()) {
            backgroundTaskManagerList.add(tileTaskManager)
        }
        foregroundTaskManagerList.remove(tileTaskManager)
        active()
    }

    /**
     * **移除任务管理器**
     *
     * @param tileTaskManager 任务管理器
     */
    fun removeTaskManager(tileTaskManager: ITaskManager<TileTask>) {
        GLog.d(TAG, "removeTaskManager $tileTaskManager")

        foregroundTaskManagerList.remove(tileTaskManager)
        backgroundTaskManagerList.remove(tileTaskManager)

        if (curTileTaskManager == tileTaskManager) {
            active()
        }
    }

    fun clearTaskManager() {
        foregroundTaskManagerList.clear()
        backgroundTaskManagerList.clear()
    }

    private fun requestNext() {
        handler?.removeCallbacks(launchNextRunnable)
        handler?.post(launchNextRunnable)
    }

    /**
     * 1.前提
     *  前台队列[foregroundTaskManagerList]：队列按照FIFO策略
     *  后台队列[backgroundTaskManagerList]：队列按照FIFO策略
     *
     * 2.执行（并行任务数<=[jobLimitSize]）
     *  按顺序先从[foregroundTaskManagerList]取出不为空的任务ITaskManager执行任务，
     *  若无，则继续从[backgroundTaskManagerList]取出不为空的任务ITaskManager执行任务
     *
     * 3.结束
     *  当两个队列均无不为空任务ITaskManager时，[TileTaskDispatcher]停止
     */
    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    fun launchNextTask() {
        if (isStop) return

        if (session.size() > jobLimitSize) {
            // 当前正在运行的解图job的个数超过了阈值，return，不能有太多job执行
            return
        }

        if (curTileTaskManager == null) {
            curTileTaskManager = findHighPriorityTaskManager()
        }
        curTileTaskManager?.findNextTask()?.apply {
            GLog.d(TAG, "launchNextTask $curTileTaskManager, $this")
            startLoad {
                curTileTaskManager?.takeIf { it.isEmpty() }?.let {
                    curTileTaskManager = null
                }
                requestNext()
            }

            launchNextTask()
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    fun findHighPriorityTaskManager(): ITaskManager<TileTask>? {
        foregroundTaskManagerList.forEach { tileTaskManager ->
            if (!tileTaskManager.isEmpty()) {
                return tileTaskManager
            }
        }

        backgroundTaskManagerList.forEach { tileTaskManager ->
            if (!tileTaskManager.isEmpty()) {
                return tileTaskManager
            }
        }

        return null
    }

    companion object {
        private const val TAG = "TileTaskDispatcher"
        private const val TILE_JOB_LIMIT = 4
    }
}

/**
 *  定义任务管理器基本接口
 */
interface ITaskManager<T> {
    val onTaskChanged: (() -> Unit)
    fun isEmpty(): Boolean
    fun findNextTask(): T?
}