/*********************************************************************************
 ** Copyright (C), 2022-2032, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : ViewDataTaskRateLimiter.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/03/26
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>               2023/03/26  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.business.ratelimiter

import com.oplus.gallery.business.ratelimiter.DynamicDistinctPriorityRateLimiter.Companion.DEFAULT_LIMIT_COUNT
import com.oplus.gallery.business.ratelimiter.DynamicDistinctPriorityRateLimiter.TaskData
import com.oplus.gallery.business.ratelimiter.DynamicPriorityRateLimiter.IComparatorLocker
import com.oplus.gallery.business.ratelimiter.DynamicPriorityRateLimiter.ITaskWorker
import com.oplus.gallery.business.ratelimiter.DynamicPriorityRateLimiter.RateLimiterEnvironments
import com.oplus.gallery.business.ratelimiter.DynamicPriorityRateLimiter.Task
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag

/**
 * 以 position 为优先级的限流器
 *
 * 其他：因时间问题，暂未将优先级任务分发和限流分开，因此，暂时不适合放到 foundation
 */
open class DynamicDistinctPriorityRateLimiter<TD : TaskData<TD>>(
    /**
     * 并发数量，默认为 [DEFAULT_LIMIT_COUNT]
     */
    limitCount: Int = DEFAULT_LIMIT_COUNT,

    /**
     * 任务调度器，默认在调用线程执行
     */
    workScheduler: IWorkScheduler,

    /**
     * 优先级对比锁
     */
    comparatorLocker: IComparatorLocker = object : IComparatorLocker {},

    /**
     * 感知队列中任务是否允许执行
     */
    canExecute: (TD, RateLimiterEnvironments<TD>) -> Boolean
) {

    private val positionIdMapping: MutableMap<String, Long?> = HashMap()
    private val rateLimiter: DynamicPriorityRateLimiter<TD> = DynamicPriorityRateLimiter(
        limitCount = limitCount,
        taskWorker = RateLimiterTaskWorker(workScheduler),
        comparatorLocker = comparatorLocker,
        canExecute = canExecute,
        taskSwitch = true
    )

    /**
     * @param startTriggerTask 是否立即触发任务队列执行，因为send 方法会触发任务队列执行，因此默认不触发队列执行
     * 打开RateLimiter开关，允许执行任务,根据条件决定是否触发任务队列执行
     */
    fun taskSwitchOnWithTriggerTask(startTriggerTask: Boolean = false) {
        rateLimiter.taskSwitchOnWithTriggerTask(startTriggerTask)
    }

    /**
     * [position] 位置，是否有任务在运行
     */
    private fun isRunning(taskData: TD): Boolean {
        return positionIdMapping.containsKey(taskData.key)
    }

    /**
     * 取消 [position] 的任务，如果取消成功，则返回 true。
     *
     * 注意：取消成功，不代表执行成功，因为取消时，任务已经在运行，即使取消，任务本身也可能忽略取消而继续执行
     */
    protected fun cancel(taskData: TD): Boolean {
        return positionIdMapping.remove(taskData.key)?.let(rateLimiter::cancel) ?: false
    }

    /**
     * 发送一个 [PositionTask] 任务，如果发送成功，则返回 true
     *
     * 如果返回 false，说明有相同 [PositionTask.position] 的任务在执行
     */
    protected fun send(taskData: TD, task: PositionTask<TD>): Boolean {
        taskData
            .let(::Task).onExecute { reply ->
                task.onExecuteCallback(taskData, reply.isCanceled, reply::reply)
            }.onCancel { reply ->
                GLog.d(TAG, LogFlag.DL) { "[send] exec canceled, task.taskData = $taskData, id=$reply.taskId, isExecuted=${reply.isReplied}" }
                task.onCanceledCallback?.invoke(taskData)
            }.onFailed { reply, throwable ->
                GLog.e(TAG, LogFlag.DL, "[send] exec failed, task.taskData = $taskData, id=${reply.taskId}, error=$throwable", throwable)
                task.onFailedCallback?.invoke(taskData, reply.isCanceled, throwable)
            }.onFinish { reply ->
                GLog.d(TAG, LogFlag.DL) { "[send] exec finished, task.taskData = $taskData, id=${reply.taskId}, isExecuted=${reply.isReplied}" }
                positionIdMapping.remove(taskData.key, reply.taskId)
                task.onFinishedCallback?.invoke(taskData, reply.isCanceled)
            }.also {
                positionIdMapping[taskData.key] = it.id
                rateLimiter.send(it)
            }
        return true
    }

    /**
     * 任务回执，当执行时，如果任务执行完，需要调用 [ITaskReply.reply]，通知此次异步任务已完结
     */
    fun interface ITaskReply {
        fun reply()
    }

    /**
     * 任务
     *
     * @param position 位置信息
     * @param onExecuteCallback 同 [onExecute]
     */
    open class PositionTask<TD : TaskData<TD>>(
        internal var onExecuteCallback: (taskData: TD, isCanceled: Boolean, taskReply: ITaskReply) -> Unit = { _, _, taskReply -> taskReply.reply() }
    ) {

        protected var taskData: TD? = null

        internal var onCanceledCallback: ((taskData: TD) -> Unit)? = null
            private set

        internal var onFinishedCallback: ((taskData: TD, Boolean) -> Unit)? = null
            private set

        internal var onFailedCallback: ((taskData: TD, Boolean, Throwable) -> Unit)? = null
            private set

        /**
         * 当任务执行时的回调，如果任务还没来得及调度执行就取消了，则不会走该回调
         */
        fun onExecute(replyTask: (taskData: TD, isCanceled: Boolean, taskReply: ITaskReply) -> Unit): PositionTask<TD> {
            this.onExecuteCallback = replyTask
            return this
        }

        /**
         * 当任务取消时的回调
         */
        fun onCancel(callback: (taskData: TD) -> Unit): PositionTask<TD> {
            onCanceledCallback = callback
            return this
        }

        /**
         * 当任务完成时的回调，即使调用了 [MyRateLimiter1.cancel] 或者出现异常，该回调也会执行
         */
        fun onFinish(callback: (taskData: TD, isCanceled: Boolean) -> Unit): PositionTask<TD> {
            onFinishedCallback = callback
            return this
        }

        /**
         * 当执行 [onExecute] 抛出异常时的回调
         */
        fun onFailed(callback: (taskData: TD, isCanceled: Boolean, Throwable) -> Unit): PositionTask<TD> {
            onFailedCallback = callback
            return this
        }

        override fun toString(): String {
            return "PositionTask(taskData = $taskData)"
        }
    }

    /**
     * 任务调度器
     */
    interface IWorkScheduler {
        /**
         * 执行 [runnable] 任务
         */
        fun schedule(runnable: Runnable): Unit = runnable.run()

        /**
         * 取消 [runnable] 任务
         */
        fun unschedule(runnable: Runnable): Unit = Unit
    }

    abstract class TaskData<Data>(val key: String) : Comparable<Data>

    private class RateLimiterTaskWorker(private val scheduler: IWorkScheduler) : ITaskWorker {
        override fun execute(runnable: Runnable): Unit = scheduler.schedule(runnable)
        override fun cancel(runnable: Runnable): Unit = scheduler.unschedule(runnable)
    }

    private companion object {
        private const val TAG = "DynamicDistinctPriorityRateLimiter"

        private const val DEFAULT_LIMIT_COUNT = 2
    }
}