/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : Contract.kt
 ** Description : 协调中的某部分,与Coordinator搭配使用,定义自己需要监听哪些Contract及如何响应状态变化
 ** Version     : 1.0
 ** Date        : 2023/03/02
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/03/02  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.business.coordinate

/**
 * 协调中的某部分,与 [Coordinator] 搭配使用,定义自己需要监听哪些 [Contract] 及如何响应状态变化
 * @see Coordinator
 */
internal abstract class Contract<CS : Coordinator.CoordinateState>(
    /**
     *  ID
     */
    val id: String,
) {
    /**
     * 对应的协调者
     */
    protected var coordinator: Coordinator<CS>? = null

    /**
     * 绑定 [Coordinator] 与 [Contract]
     */
    internal fun bindCoordinator(coordinator: Coordinator<CS>) {
        this.coordinator = coordinator
    }

    /**
     * 解绑定 [Coordinator] 与 [Contract]
     */
    internal fun unbindCoordinator() {
        this.coordinator = null
    }

    /**
     * 发送事件,所有该 [Contract] 的监听者都会收到该事件
     */
    protected fun sendEvent(event: Coordinator.CoordinateEvent) {
        coordinator?.coordinateEvent(id, event)
    }

    /**
     * 获取当前状态
     */
    protected fun <T> currState(): T? {
        return coordinator?.getContractStateById(id) as? T
    }

    /**
     * 设置需要监听哪些 [Contract] 变化
     * * 当对应 [Contract] 发生状态更新时,会回调 [onCoordinateState]
     * * 当对应 [Contract] 发送事件时,会回调 [onCoordinateEvent]
     */
    abstract fun dependency(): List<String>

    /**
     * 初始化 [Contract] 的状态
     */
    internal open fun onInitState(): Unit = Unit

    /**
     * [Contract] 与 [Coordinator] 开始监听自身状态时回调,可以设置自己的 Listener ,在对应的回调中去 [updateState]
     */
    internal abstract fun onContract()

    /**
     * [Contract] 与 [Coordinator] 停止监听自身状态时回调,可以反注册 Listener 等
     */
    internal abstract fun onDecontract()

    /**
     * 当 [dependency] 对应的 [Contract] 发生状态更新时回调
     *
     * 在此处做联动操作,所需要的状态参数可以通过以下方式获取:
     * * [Coordinator.coordinateState] 协调者的状态
     * * [currState] 当前 [Contract] 的状态
     * * [Coordinator.getContractStateById] 获取对应 [Contract] 的状态
     */
    internal abstract fun onCoordinateState()

    /**
     * 当 [dependency] 对应的 [Contract] 发送事件时回调
     */
    internal abstract fun onCoordinateEvent(event: Coordinator.CoordinateEvent)

    /**
     * 更新 [Contract] 的状态
     *
     * @param state : 触发者 [Contract] 的状态
     * @param shouldRetriggerBySelf true:由该 [Contract] 触发的联动,false:不是由该 [Contract] 触发的联动
     */
    protected open fun updateState(state: ContractState, shouldRetriggerBySelf: Boolean = false) {
        coordinator?.coordinateState(id, state, shouldRetriggerBySelf)
    }

    /**
     * [Contract] 的状态
     */
    open class ContractState
}