/********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - GroupPhotoUserAgreementDialogInterceptor
 ** Description: 外销合影优化用户须知拦截
 ** Version: 1.0
 ** Date : 2024/6/4
 ** Author: 80302808@Apps.Gallery3D
 ** TAG: GroupPhotoUserAgreementDialogInterceptor
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  80302808@Apps.Gallery3D         2024/06/04    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.business.aiunitdownload.groupphoto

import android.content.ActivityNotFoundException
import android.content.Context
import android.os.Bundle
import android.view.KeyEvent
import com.oplus.gallery.basebiz.R as BaseR
import com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.SETTING_PRIVACY_POLICY
import com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.SETTING_USER_AGREEMENT
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business_lib.R
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.business_lib.ui.dialog.authoring.DialogListener
import com.oplus.gallery.business_lib.ui.dialog.chain.AlertChainDataDeliverer
import com.oplus.gallery.business_lib.ui.dialog.chain.BaseAlertInterceptor
import com.oplus.gallery.business_lib.ui.dialog.chain.IAlert
import com.oplus.gallery.business_lib.ui.dialog.chain.IAlertNegativeListener
import com.oplus.gallery.business_lib.ui.dialog.chain.IAlertNextListener
import com.oplus.gallery.business_lib.ui.dialog.chain.IKeyListener
import com.oplus.gallery.business_lib.ui.dialog.chain.NormalStatementDialogChainAlert
import com.oplus.gallery.foundation.authorizing.ui.text.style.SpanParams
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.config.ISettingsAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.Permission.IS_AGREE_USER_AGREEMENT
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.Permission.IS_AUTHORIZE_ACCESS_AI_GROUP_PHOTO
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REGION_CN
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.router_lib.Starter
import com.oplus.gallery.router_lib.meta.PostCard

/**
 * 外销合影优化用户须知拦截
 */
class GroupPhotoUserAgreementDialogInterceptor(
    private val context: Context,
    private val listener: DialogListener? = null
) : BaseAlertInterceptor<Bundle>() {
    /**
     * 拦截条件：
     * （1）外销
     * （2）用户未同意权限
     * */
    override fun shouldIntercept(alertChainDataDeliverer: AlertChainDataDeliverer<Bundle>?): Boolean {
        return ConfigAbilityWrapper.getBoolean(IS_REGION_CN).not()
                && ConfigAbilityWrapper.getBoolean(IS_AUTHORIZE_ACCESS_AI_GROUP_PHOTO, false).not()
    }

    override fun createAlert(): IAlert {
        val contentSpan = SpanParams(com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_ai_group_photo_statement)
        val linkSpan = if (ConfigAbilityWrapper.getBoolean(IS_AGREE_USER_AGREEMENT, false)) {
            SpanParams(
                com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_check_details_msg_1, intArrayOf(
                    BaseR.string.base_ai_personal_privacy_policy
                ), arrayOf(
                    SpanParams.OnSpanClickListener {
                        gotoPrivacyPolicyPage()
                    }
                ), true)
        } else {
            SpanParams(
                com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_check_details_msg_2, intArrayOf(
                    BaseR.string.base_user_agreement,
                    BaseR.string.base_ai_personal_privacy_policy
                ), arrayOf(
                    SpanParams.OnSpanClickListener {
                        Starter.ActivityStarter(context, Bundle(), PostCard(SETTING_USER_AGREEMENT)).start()
                    },
                    SpanParams.OnSpanClickListener {
                        gotoPrivacyPolicyPage()
                    }
                ), true)
        }
        val builder = NormalStatementDialogChainAlert.Builder(context)
            .buildTitle(com.oplus.gallery.basebiz.R.string.base_permission_statement_dialog_title)
            .buildPositive(com.oplus.gallery.foundation.authorizing.R.string.authorizing_option_agree_and_use)
            .buildNegative(com.oplus.gallery.foundation.authorizing.R.string.authorizing_option_disagree)
            .buildContentSpan(contentSpan)
            .buildLinkSpan(linkSpan)
            .buildNextListener(object : IAlertNextListener<NormalStatementDialogChainAlert> {
                override fun onNext(alert: NormalStatementDialogChainAlert) {
                    context.getAppAbility<ISettingsAbility>()?.use {
                        it.authorizeAccessAIGroupPhoto(true)
                        listener?.onAgree()
                        nextNode?.proceed(callback, alertChainDataDeliverer)
                    }
                }
            })
            .buildNegativeListener(object : IAlertNegativeListener<NormalStatementDialogChainAlert> {
                override fun onNegative(alert: NormalStatementDialogChainAlert) {
                    callback?.finish(alert.type())
                    listener?.onRefuse()
                }
            }).buildKeyListener(object : IKeyListener<NormalStatementDialogChainAlert> {
                override fun onKey(alert: NormalStatementDialogChainAlert, keyCode: Int, event: KeyEvent?): Boolean {
                    GLog.d(TAG) { "[onKey]: 4==keyCode->$keyCode,1==${event?.action} alertType->${alert.type()}" }
                    if ((keyCode == KeyEvent.KEYCODE_BACK) && (event?.action == KeyEvent.ACTION_UP)) {
                        callback?.finish(alert.type())
                        listener?.onRefuse()
                    }
                    return false
                }
            })
            .buildAlertType(AI_GROUP_PHOTO_USER_AGREEMENT_TYPE)
        return builder.create()
    }

    /**
     * 打开隐私协议页面
     */
    private fun gotoPrivacyPolicyPage() {
        val regionPrivacySetting = ApiDmManager.getSettingDM().getRegionPrivacySetting()
        regionPrivacySetting?.let {
            if (it.isPrivacyAgreementRequired()) {
                Starter.ActivityStarter(this.context, Bundle(), PostCard(SETTING_PRIVACY_POLICY)).start()
            } else {
                ApiDmManager.getSettingDM().getPrivacyNoticeIntent()?.let { intent ->
                    try {
                        context.startActivity(intent)
                    } catch (ex: ActivityNotFoundException) {
                        GLog.e(TAG, "privacy policy start activity exception = $ex")
                    }
                }
            }
        }
    }

    companion object {
        private const val TAG = "GroupPhotoUserAgreementDialogInterceptor"
        private const val AI_GROUP_PHOTO_USER_AGREEMENT_TYPE = 16
    }
}