/*********************************************************************************
 ** Copyright (C), 2022-2032, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : EffectFactory.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2025/07/30
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>               2025/07/30  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.business.contentloading.loader.effect

import android.graphics.Rect
import android.net.Uri
import android.util.Pair
import android.util.Size
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.standard_lib.app.AppConstants
import com.oplus.gallery.standard_lib.codec.player.effect.EffectFactory
import com.oplus.gallery.standard_lib.codec.player.effect.EffectKey
import com.oplus.gallery.standard_lib.codec.player.effect.EisInfo
import com.oplus.tbl.exoplayer2.Effect
import com.oplus.tbl.exoplayer2.util.UnstableApi
import com.oplus.tblplayer.processor.CropEffect
import com.oplus.tblplayer.processor.EdgeEffect
import com.oplus.tblplayer.processor.FrameInterpolationEffect
import com.oplus.tblplayer.processor.HomoMatrixTransformationEffect
import com.oplus.tblplayer.processor.MultiTextureMatrixProvider
import com.oplus.tblplayer.processor.ScaleTransformation
import com.oplus.tblplayer.processor.util.AlphaBlendEnum

/**
 * TBL 视频播放特效工厂: 根据 [EisInfo] 裁剪
 * - 特效: [CropEffect]
 * - [EffectKey]: [EffectKey.TBL_CROP_EFFECT]
 * - 如需自定义裁剪,请使用 [CropEffectFactory]
 */
@UnstableApi
class EisCropEffectFactory(
    eisInfo: EisInfo?
) : EffectFactory {
    private val videoSizeList: List<Int>? = eisInfo?.videoSize
    private val cropList: List<Int>? = eisInfo?.cropRect

    override fun createCoordinator(): Effect? {
        val videoSize = videoSizeList?.let {
            if (it.size != AppConstants.Number.NUMBER_2) {
                GLog.e(TAG) { "[createCoordinator] skip create , video size : ${it.size}" }
                return null
            }
            Size(it[AppConstants.Number.NUMBER_0], it[AppConstants.Number.NUMBER_1])
        }

        val cropRect = cropList?.let {
            if (it.size != AppConstants.Number.NUMBER_4) {
                GLog.e(TAG) { "[createCoordinator] skip create , crop size : ${it.size}" }
                return null
            }
            Rect().apply {
                left = it[AppConstants.Number.NUMBER_0]
                top = it[AppConstants.Number.NUMBER_1]
                right = it[AppConstants.Number.NUMBER_2]
                bottom = it[AppConstants.Number.NUMBER_3]
            }
        }

        return generateCropEffect(videoSize, cropRect)
    }

    private fun generateCropEffect(videoSize: Size?, cropRect: Rect?): Effect? {
        //获取视频大小
        videoSize ?: run {
            GLog.e(TAG) { "[generateCropEffect] can not create effect cause size is null" }
            return null
        }

        //获取裁剪Rect
        cropRect ?: run {
            GLog.e(TAG) { "[generateCropEffect] can not create effect cause cropRect is null" }
            return null
        }

        val effect = CropEffect.createCropEffectFromRect(videoSize, 0, cropRect)
        GLog.d(TAG) { "[generateCropEffect] create effect : $effect , videoSize : $videoSize , cropRect : $cropRect" }
        return effect
    }

    private companion object {
        private const val TAG = "EisCropEffectFactory"
    }
}

/**
 * TBL 视频播放特效工厂: 根据自定义的 [videoSize] 和 [cropRect] 裁剪
 * - 特效: [CropEffect]
 * - [EffectKey]: [EffectKey.TBL_CUSTOM_CROP_EFFECT]
 * - 如需根据 [EisInfo] 裁剪,请使用 [EisCropEffectFactory]
 */
@UnstableApi
class CropEffectFactory(
    private val videoSize: Size?,
    private val cropRect: Rect?
) : EffectFactory {
    override fun createCoordinator(): Effect? {
        //获取视频大小
        videoSize ?: run {
            GLog.e(TAG) { "[createCoordinator] can not create effect cause size is null" }
            return null
        }

        //获取裁剪Rect
        cropRect ?: run {
            GLog.e(TAG) { "[createCoordinator] can not create effect cause cropRect is null" }
            return null
        }

        val effect = CropEffect.createCropEffectFromRect(videoSize, 0, cropRect)
        GLog.d(TAG) { "[createCoordinator] create effect : $effect , videoSize : $videoSize , cropRect : $cropRect" }
        return effect
    }

    private companion object {
        private const val TAG = "CropEffectFactory"
    }
}

/**
 * TBL 视频播放特效工厂: 插帧
 * - 特效: [FrameInterpolationEffect]
 * - [EffectKey]: [EffectKey.TBL_FRAME_FILL_EFFECT]
 */
@UnstableApi
class FrameFillEffectFactory : EffectFactory {
    override fun createCoordinator(): Effect? {
        val effect = FrameInterpolationEffect.createBlendFrameInterpolationEffect(AlphaBlendEnum.BLEND_MODE_DEFAULT)
        GLog.d(TAG) { "[createCoordinator] create effect : $effect" }
        return effect
    }

    private companion object {
        private const val TAG = "FrameFillEffectFactory"
    }
}

/**
 * TBL 视频播放特效工厂: 缩放
 * - 特效: [ScaleTransformation]
 * - [EffectKey]: [EffectKey.TBL_SCALE_EFFECT]
 */
@UnstableApi
class ScaleEffectFactory(
    private val eisInfo: EisInfo? = null
) : EffectFactory {
    override fun createCoordinator(): Effect? {
        val scale = eisInfo?.eisCropFactor?.let {
            if (it.size != AppConstants.Number.NUMBER_2) {
                Pair(DEFAULT_VIDEO_SCALE, DEFAULT_VIDEO_SCALE)
            } else {
                Pair(it[AppConstants.Number.NUMBER_0], it[AppConstants.Number.NUMBER_1])
            }
        } ?: let {
            GLog.d(TAG) { "[createCoordinator] video crop factor is null , use default scale" }
            Pair(DEFAULT_VIDEO_SCALE, DEFAULT_VIDEO_SCALE)
        }

        val effect = ScaleTransformation.Builder().setScale(scale.first, scale.second).build()
        GLog.d(TAG) { "[createCoordinator] create effect : $effect , scale : $scale , eisInfo : $eisInfo" }
        return effect
    }

    private companion object {
        private const val TAG = "ScaleEffectFactory"

        /**默认缩放比例*/
        private const val DEFAULT_VIDEO_SCALE = 1.05F
    }
}

/**
 * TBL 视频播放特效工厂: 视野范围
 * - 特效: [HomoMatrixTransformationEffect]
 * - [EffectKey]: [EffectKey.TBL_FOV_EFFECT]
 */
@UnstableApi
class FovEffectFactory(
    private val eisInfo: EisInfo? = null
) : EffectFactory {
    override fun createCoordinator(): Effect? {
        val matrices = eisInfo?.matrices ?: let {
            GLog.e(TAG) { "[createCoordinator] can not create effect cause matrices is null" }
            return null
        }

        val matrixList = mutableListOf<Pair<Long, FloatArray>>().apply {
            matrices.forEach { (key, value) ->
                add(Pair<Long, FloatArray>(key.toLong(), value.toFloatArray()))
            }
        }

        val orientation = eisInfo.videoOrientation ?: let {
            GLog.d(TAG) { "generateFovEffect: video orientation is null" }
            return null
        }

        val matrixProvider = MultiTextureMatrixProvider(matrixList)
        val effect = HomoMatrixTransformationEffect.createHomoMatrixEffect(
            matrixProvider, false, orientation
        )

        GLog.d(TAG) { "[createCoordinator] create effect : $effect , eisInfo : $eisInfo" }
        return effect
    }

    private companion object {
        private const val TAG = "FovEffectFactory"
    }
}

/**
 * TBL 视频播放特效工厂: 边界(不显示的内容位置填充黑色)
 * - 特效: [EdgeEffect]
 * - [EffectKey]: [EffectKey.TBL_FOV_EFFECT]
 */
@UnstableApi
class EdgeEffectFactory(
    private val videoSize: Size?,
    private val cropRect: Rect?
) : EffectFactory {
    override fun createCoordinator(): Effect? {
        //获取视频大小
        videoSize ?: run {
            GLog.e(TAG) { "[createCoordinator] can not create effect cause videoSize is null" }
            return null
        }

        cropRect ?: run {
            GLog.e(TAG) { "[createCoordinator] can not create effect cause cropRect is null" }
            return null
        }

        //获取裁剪Rect
        val rect = Rect().apply {
            left = (cropRect.left - AppConstants.Number.NUMBER_4).coerceAtLeast(0)
            top = (cropRect.top - AppConstants.Number.NUMBER_4).coerceAtLeast(0)
            right = cropRect.right + AppConstants.Number.NUMBER_4
            bottom = cropRect.bottom + AppConstants.Number.NUMBER_4
        }

        val effect = EdgeEffect.createForPresentationRect(videoSize, 0, rect)
        GLog.d(TAG) { "[createCoordinator] create effect : $effect , videoSize : $videoSize , cropRect : $cropRect" }
        return effect
    }

    private companion object {
        private const val TAG = "EdgeEffectFactory"
    }
}

/**
 * TBL 视频播放特效工厂: 提亮
 * - 特效: [BrightenEffect]
 * - [EffectKey]: [EffectKey.TBL_CUSTOM_EFFECT_TRANSFORM]
 */
@UnstableApi
class HdrEffectFactory(
    private val ratio: Float,
    private val isHdrVideo: Boolean,
    private val mediaUri: Uri,
) : EffectFactory {
    override fun createCoordinator(): Effect {
        val effect = BrightenEffect(isHdrVideo = isHdrVideo, mediaUri = mediaUri, ratio = ratio)
        GLog.d(TAG) { "[createCoordinator] create effect : $effect , ratio : $ratio , isHdrVideo : $isHdrVideo , mediaUri : $mediaUri" }
        return effect
    }

    private companion object {
        private const val TAG = "HdrEffectFactory"
    }
}