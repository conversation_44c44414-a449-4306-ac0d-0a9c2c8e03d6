/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - IHdrImageContentHolder.kt
 * Description: HDR资源的容器，包括HDR相关参数，HDR所对应资源的ID等
 *
 * Version: 1.0
 * Date: 2025/03/01
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>                     <date>          <version>    <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>       2025/03/01        1.0        OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.business.drawable.hdrimage

import android.graphics.Bitmap
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.photo_page.viewmodel.dataloading.PhotoItemViewData
import com.oplus.gallery.standard_lib.codec.drawable.IHdrImageContent
import com.oplus.gallery.standard_lib.codec.drawable.IHdrMetadataPack

/**
 * HDR资源的容器，包括HDR相关参数，HDR所对应资源的ID等
 */
internal interface IPhotoHdrImageContent : IHdrImageContent {
    /**
     * HDR资源所对应的资源id，大图中使用，需要是Path.toString得到的值。
     */
    val contentId: String

    companion object {
        /**
         * [IPhotoHdrImageContent.contentId]在业务端的默认值。如不需要关心该参数的场景，可以使用该默认值。
         */
        internal const val CONTENT_ID_DEFAULT = "default_id"

        /**
         * 根据给定的[Path]生成[contentId]
         * 大图中的HDR Content ID，统一都是由path生成的，所以这里的ContentID也要通过Path生成。
         *
         * @param path
         * @return
         */
        fun createContentId(path: Path): String = path.toString()

        /**
         * 创建[IPhotoHdrImageContent.contentId]的方法。
         *
         * @param viewData
         * @return
         */
        fun createContentId(viewData: PhotoItemViewData): String = viewData.id
    }
}

/**
 * 大图使用的[IPhotoHdrImageContent]的实现类
 */
internal data class PhotoHdrImageContent(
    override val contentId: String,
    override val grayImage: Bitmap,
    override val metadata: IHdrMetadataPack
) : IPhotoHdrImageContent

