/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : OliveDrawableLoader.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/03/03
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** tan<PERSON><PERSON><PERSON>@Apps.Gallery          2024/03/03      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.business.contentloading.loader

import android.graphics.Bitmap
import android.graphics.ColorSpace
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.drawable.Drawable
import android.net.Uri
import android.util.Size
import androidx.annotation.OptIn
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business.contentloading.ContentLoader
import com.oplus.gallery.business.contentloading.LoaderOptions
import com.oplus.gallery.business.contentloading.OnFutureDoneListener
import com.oplus.gallery.business.contentloading.loader.effect.CropEffectFactory
import com.oplus.gallery.business.contentloading.loader.effect.EdgeEffectFactory
import com.oplus.gallery.business.contentloading.loader.effect.EisCropEffectFactory
import com.oplus.gallery.business.contentloading.loader.effect.FovEffectFactory
import com.oplus.gallery.business.contentloading.loader.effect.FrameFillEffectFactory
import com.oplus.gallery.business.contentloading.loader.effect.HdrEffectFactory
import com.oplus.gallery.business.contentloading.loader.effect.ScaleEffectFactory
import com.oplus.gallery.business.contentloading.loader.util.OliveFrameFillerChecker
import com.oplus.gallery.business.drawable.AVPlayerDrawable
import com.oplus.gallery.business.drawable.hdrimage.HdrOliveDrawable
import com.oplus.gallery.business.drawable.olive.IOLiveImageContent
import com.oplus.gallery.business.drawable.olive.OLiveDrawable
import com.oplus.gallery.business.drawable.olive.OLiveImageContent
import com.oplus.gallery.business.drawable.recycle
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.getMediaUri
import com.oplus.gallery.business_lib.model.data.base.item.isDolbyVideoOlive
import com.oplus.gallery.business_lib.model.data.base.item.isHdrVideoOlive
import com.oplus.gallery.business_lib.model.data.base.item.toOriginalItem
import com.oplus.gallery.business_lib.videoedit.IMetadataGetter
import com.oplus.gallery.business_lib.videoedit.MetadataGetterFactory
import com.oplus.gallery.business_lib.videoedit.MetadataKey
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.codec.extend.EXTEND_KEY_OLIVE_SUB_VIDEO
import com.oplus.gallery.foundation.codec.extend.EXTEND_KEY_OLIVE_SUB_VIDEO_SIZE
import com.oplus.gallery.foundation.database.store.CloudSyncStore.LocalFileStatus
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_OLIVE_VIDEO_CODEC_TYPE
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_OLIVE_VIDEO_FPS
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_OLIVE_VIDEO_HEIGHT
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_OLIVE_VIDEO_WIDTH
import com.oplus.gallery.foundation.fileaccess.extension.FileExtendedContainer
import com.oplus.gallery.foundation.fileaccess.extension.OpenFileMode
import com.oplus.gallery.foundation.taskscheduling.Cancelable
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.getFileDescriptorSafely
import com.oplus.gallery.foundation.util.ext.getOrLog
import com.oplus.gallery.framework.abilities.caching.CacheOperation
import com.oplus.gallery.framework.abilities.caching.StorageQuality
import com.oplus.gallery.framework.abilities.colormanagement.IColorManagementAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_PHOTO_PAGE_HDR_VIDEO_OLIVE_TRANSFORM
import com.oplus.gallery.framework.abilities.resourcing.IResourcingAbility
import com.oplus.gallery.framework.abilities.resourcing.crop.CropParams
import com.oplus.gallery.framework.abilities.resourcing.key.ResourceKeyFactory
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.framework.abilities.watermark.IWatermarkMasterAbility
import com.oplus.gallery.framework.abilities.watermark.masterextend.toValidString
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.framework.app.withAbility
import com.oplus.gallery.olive_decoder.OLiveDecode
import com.oplus.gallery.standard_lib.app.AppConstants.Number
import com.oplus.gallery.standard_lib.codec.drawable.IHdrImageContent
import com.oplus.gallery.standard_lib.codec.player.AVPlayer
import com.oplus.gallery.standard_lib.codec.player.effect.EffectKey.TBL_CROP_EFFECT
import com.oplus.gallery.standard_lib.codec.player.effect.EffectKey.TBL_CUSTOM_CROP_EFFECT
import com.oplus.gallery.standard_lib.codec.player.effect.EffectKey.TBL_CUSTOM_EFFECT_TRANSFORM
import com.oplus.gallery.standard_lib.codec.player.effect.EffectKey.TBL_EDGE_EFFECT
import com.oplus.gallery.standard_lib.codec.player.effect.EffectKey.TBL_FOV_EFFECT
import com.oplus.gallery.standard_lib.codec.player.effect.EffectKey.TBL_FRAME_FILL_EFFECT
import com.oplus.gallery.standard_lib.codec.player.effect.EffectKey.TBL_SCALE_EFFECT
import com.oplus.gallery.standard_lib.codec.player.effect.EffectUtil
import com.oplus.gallery.standard_lib.codec.player.effect.EffectWrapper
import com.oplus.gallery.standard_lib.codec.retriever.MediaMetadataRetrieverUtils
import com.oplus.gallery.standard_lib.scheduler.Job
import com.oplus.gallery.standard_lib.scheduler.JobContext
import com.oplus.gallery.standard_lib.scheduler.session.PrioritySession
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.gallery.videoeditor.data.VideoSpec
import com.oplus.gallery.videoeditor.utils.CodecSupportParser
import com.oplus.tbl.exoplayer2.util.UnstableApi
import kotlinx.coroutines.CoroutineScope
import java.util.concurrent.Future
import kotlin.math.abs
import kotlin.math.min
import kotlin.system.measureTimeMillis

internal class OliveDrawableLoader(
    mediaItem: MediaItem,
    workSession: PrioritySession,
    workScope: CoroutineScope,
    val coverLoader: ContentLoader<Drawable>,
    val videoLoader: ContentLoader<Drawable>,
    private val loaderOptions: LoaderOptions?,
    futureListener: OnFutureDoneListener<Drawable> = { },
) : ContentLoader<Drawable>(mediaItem, workSession, workScope, futureListener) {

    /**
     * 大图是否支持HDR视频的Olive的下变换
     */
    private val isSupportHdrVideoOliveTransform: Boolean by lazy {
        ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_PHOTO_PAGE_HDR_VIDEO_OLIVE_TRANSFORM, false)
    }

    /**
     * 两件事
     * 1.生成封面的drawable
     * 2.生成视频的drawable
     */
    private val cancelable = Cancelable()

    override fun onLoadJobCanceled(workingFuture: Future<Drawable>?) {
        super.onLoadJobCanceled(workingFuture)
        cancelable.cancel()
    }

    @OptIn(UnstableApi::class)
    override fun onCreateLoadJob(): Job<Drawable>? {
        return object : Job<Drawable> {

            val coverDrawableLoadJob = coverLoader.onCreateLoadJob()
            val videoDrawableLoadJob = videoLoader.onCreateLoadJob()

            override fun call(jc: JobContext): Drawable? = runCatching {
                if (cancelable.isCancelled()) {
                    GLog.e(TAG) { "[onCreateLoadJob] job canceled for ${mediaItem.id}, will not load." }
                    return null
                }

                var coverDrawable: Drawable
                val coverJobCost = measureTimeMillis {
                    coverDrawable = coverDrawableLoadJob?.call(jc) ?: let {
                        GLog.e(TAG) { "[onCreateLoadJob] coverDrawable is null" }
                        return@runCatching null
                    }
                }

                if (cancelable.isCancelled()) {
                    coverDrawable.recycle()
                    GLog.e(TAG) { "[onCreateLoadJob] job canceled for ${mediaItem.id} after load coverDrawable, will not load." }
                    return null
                }

                val oLiveImageContent = loadOlivePhotoContent(mediaItem)
                val offset = oLiveImageContent.videoOffset.takeIf { it != 0L }
                val filePath = mediaItem.filePath
                val eisData = offset?.let { EffectUtil.getEisData(filePath = filePath, offset = it) }
                val effectWrapper = EffectWrapper()
                effectWrapper.forceEnable = shouldLoadEffect()

                //设置实况视频外部播放参数
                val outerOptions = AVPlayerLoader.OuterPlayOption().apply {
                    //色彩空间
                    targetColorSpace = ContextGetter.context.getAppAbility<IColorManagementAbility>()?.use {
                        if (it.isDisplaySupportWCG.not() && (it.isWideColorGamutEnabled?.not() == true)) {
                            // 色彩管理1.0 生动模式下，olive视频需要强制关闭P3色域以保证与图片显示一致
                            ColorSpace.get(ColorSpace.Named.SRGB)
                        } else {
                            null
                        }
                    }

                    videoPlayEffect = effectWrapper
                    //双视频实况，子视频信息
                    oliveSubVideoInfo = oLiveImageContent.subVideoInfo
                    //双视频实况，默认设置播放子视频
                    playType = getOlivePlayType(oLiveImageContent)
                }.also {
                    (videoLoader as? AVPlayerLoader)?.setOuterPlayOptions(it)
                }

                /**
                 * 加载实况视频播放的前景和背景图片, 用于覆盖在视频上方解决视频内容播放的显示问题
                 */
                loadOLiveVideoMaskBitmapIfNeed(oLiveImageContent, eisData)

                var videoDrawable: Drawable
                val videoJobCost = measureTimeMillis {
                    videoDrawable = videoDrawableLoadJob?.call(jc) ?: let {
                        GLog.e(TAG) { "[onCreateLoadJob] videoDrawable is null" }
                        return@runCatching null
                    }
                    offset?.let { effectWrapper.updateVideoPlayEffect(oLiveImageContent, eisData, videoDrawable) }
                }

                if (cancelable.isCancelled()) {
                    GLog.e(TAG) { "[onCreateLoadJob] job canceled for ${mediaItem.id} after load videoDrawable, will not load." }
                    coverDrawable.recycle()
                    videoDrawable.recycle()
                    return null
                }

                // Marked by tlyao  考虑并行执行, 节省时间, 先打个时间看看
                GLog.d(TAG) { "[onCreateLoadJob.call] coverCost = ${coverJobCost}ms, videoCost = ${videoJobCost}ms, id:${mediaItem.id}" }

                val hdrImageContent = coverDrawable as? IHdrImageContent
                if (hdrImageContent != null) {
                    return HdrOliveDrawable(coverDrawable, videoDrawable, hdrImageContent, oLiveImageContent)
                }

                return OLiveDrawable(coverDrawable, videoDrawable, oLiveImageContent)
            }.onFailure {
                GLog.e(TAG, it) { "[onCreateLoadJob] error to request OliveDrawableLoader;" }
            }.getOrNull()
        }
    }

    /**
     * 设置视频播放时候使用的前景和背景图片，解决实况视频本身内容有模糊的问题。在播放过程中不能让用户看到视频四周模糊
     *
     * @param imageContent
     * @param loader
     */
    private fun loadOLiveVideoMaskBitmapIfNeed(imageContent: OLiveImageContent, eisData: String?) {
        //前景Mask, 加载实况封面缩图并裁掉内容区域，只留下水印部分
        val videoForeground =
            if (shouldLoadOLiveVideoForeground(
                    oliveImageDisplayRectF = imageContent.imageDisplayRectF,
                    oliveVideoDisplayRectF = imageContent.videoDisplayRectF,
                    eisData = eisData
                )
            ) {
                var thumbnailType = ThumbnailSizeUtils.getFullThumbnailKey()
                val videoDisplayRectF = imageContent.videoDisplayRectF ?: RectF()
                if (min(videoDisplayRectF.width(), videoDisplayRectF.height()) > ThumbnailSizeUtils.THUMBNAIL_SIZE_PHOTO) {
                    thumbnailType = ThumbnailSizeUtils.TYPE_SUPER_LARGE_THUMBNAIL
                }
                loadOLiveVideoBackground(mediaItem, thumbnailType)
            } else null

        //背景Mask, 如果有前景的话就不需要背景
        val videoBackground =
            if ((videoForeground == null) && shouldLoadOLiveVideoBackground(
                    oliveImageDisplayRectF = imageContent.imageDisplayRectF,
                    oliveVideoDisplayRectF = imageContent.videoDisplayRectF,
                    isOnlyFrameWaterMark = imageContent.isOnlyFrameWaterMark
                )
            ) {
                loadOLiveVideoBackground(mediaItem, ThumbnailSizeUtils.getFullThumbnailKey())
            } else null

        imageContent.apply {
            oliveVideoForeground = videoForeground
            oliveVideoBackground = videoBackground
        }
    }

    /**
     * 获取实况照片中小视频的位置和大小信息
     * @param path 实况照片路径
     *
     */
    private fun getOliveSubVideo(coverChanged: Boolean, oliveEditorFlag: Int?): AVPlayer.VideoInfo? {
        //文件合法
        val uri = mediaItem.getMediaUri() ?: run {
            GLog.w(TAG, LogFlag.DL) { "[getSubVideoInfo] image uri is null" }
            return null
        }

        //封面有切换，或是主视频有被修改过，双视频功能失效。仅播放大视频
        if (coverChanged || (oliveEditorFlag != null)) {
            GLog.w(TAG, LogFlag.DL) { "[getSubVideoInfo] image is changed, no need use sub video" }
            return null
        }

        kotlin.runCatching {
            FileExtendedContainer().use {
                it.setDataSource(context = ContextGetter.context, uri)
                val videoLength = it.getExtensionData(EXTEND_KEY_OLIVE_SUB_VIDEO_SIZE)?.toValidString()?.toLong()
                val videoOffset = it.getExtensionDataOffset(EXTEND_KEY_OLIVE_SUB_VIDEO).toLong()
                if ((videoLength == null) || (videoOffset <= 0)) {
                    GLog.w(TAG, LogFlag.DL) { "[getSubVideoInfo] failed to get subVideoInfo, len=$videoLength, offset=$videoOffset" }
                    return null
                }

                //检查小视频和封面宽高比是否接近，差异过大情况下不使用小视频
                if (isSubVideoValid(uri, videoOffset, videoLength).not()) {
                    GLog.w(TAG, LogFlag.DL) { "[getSubVideoInfo] video and cover ratio is no equal" }
                    return null
                }

                return AVPlayer.VideoInfo(videoOffset, videoLength)
            }
        }

        GLog.w(TAG, LogFlag.DL) { "[getSubVideoInfo] do not have sub video" }
        return null
    }

    /**
     * 判断小视频是否有效，判断小视频的宽高比是否和封面接近。避免宽高比差距太大的图片和视频
     * @param uri 实况地址
     * @param offset 视频在文件偏移量
     * @param length 视频长度
     * @return true表示视频和封面宽高比接近
     */
    private fun isSubVideoValid(uri: Uri, offset: Long?, length: Long?): Boolean {
        val videoOffset = offset ?: return false
        val videoLength = length ?: return false

        MediaMetadataRetrieverUtils.getRetrieverInfoData(uri, videoOffset, videoLength)?.let {
            if ((it.height == 0) || (it.width == 0) || (mediaItem.width == 0) || (mediaItem.height == 0)) {
                GLog.e(TAG, LogFlag.DL) { "[isSubVideoValid] height or width error" }
                return false
            }

            val subVideoRatio = it.width / it.height.toFloat()
            val coverImageRatio = mediaItem.width / mediaItem.height.toFloat()
            return abs(subVideoRatio - coverImageRatio) < RATIO_THRESHOLD
        } ?: return false
    }

    /**
     * 获取默认加载的实况照片视频类型（大视频还是小视频）
     */
    private fun getOlivePlayType(imageContent: IOLiveImageContent): AVPlayer.VideoPlayType  {
        //双视频没有小视频时，初始化播放大视频
        if (imageContent.subVideoInfo == null) {
            return AVPlayer.VideoPlayType.MAIN
        }

        //不支持滑动播放的实况，初始化播放大视频
        if (ConfigAbilityWrapper.getBoolean(ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_OLIVE_SLIDE_PLAY, false).not()) {
            return AVPlayer.VideoPlayType.MAIN
        }

        return AVPlayer.VideoPlayType.SUB
    }

    private fun shouldLoadEffect(): Boolean {
        // 不支持补帧时，关闭视频播放特效，返回空的播放特效
        if (ConfigAbilityWrapper.getBoolean(ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_OLIVE_FRAME_FILLTER).not()) {
            GLog.e(TAG, LogFlag.DL) { "[shouldLoadEffect] feature not support , should not load effect" }
            return false
        }

        // 杜比视频生成的olive图不需要特效处理
        if (mediaItem.isDolbyVideoOlive()) {
            GLog.e(TAG, LogFlag.DL) { "[shouldLoadEffect] olive video is dolby , should not load effect" }
            return false
        }

        return true
    }

    /**
     * 获取olive文件的视频特效解码器
     */
    @OptIn(UnstableApi::class)
    private fun EffectWrapper.updateVideoPlayEffect(oliveImageContent: OLiveImageContent, eisData: String?, videoDrawable: Drawable) {
        val mediaUri = mediaItem.getMediaUri() ?: Uri.EMPTY
        val isHdrVideoOlive = mediaItem.isHdrVideoOlive()
        val eisInfo = eisData?.let { EffectUtil.getEisInfo(it) }
        val rectF: RectF? = oliveImageContent.videoDisplayRectF?.takeIf { oliveImageContent.isOnlyFrameWaterMark && it.isEmpty.not() }
        val contentRect = if (rectF == null) null else {
            Rect().apply { rectF.round(this) }
        }
        val videoSize = if (rectF == null) null else {
            (videoDrawable as? AVPlayerDrawable)?.let { Size(it.onGetVideoWidth(), it.onGetVideoHeight()) }
        }

        // 定义方法:注册裁剪特效
        val coordinateCustomCrop = fun() {
            val factory = CropEffectFactory(videoSize, contentRect)
            coordinate(TBL_CUSTOM_CROP_EFFECT, factory)
        }

        // 定义方法:注册裁剪特效
        val coordinateEisCrop = fun() {
            val factory = EisCropEffectFactory(eisInfo)
            coordinate(TBL_CROP_EFFECT, factory)
        }

        // 定义方法:注册插帧特效
        val coordinateFrameFill = fun() {
            val factory = FrameFillEffectFactory()
            coordinate(TBL_FRAME_FILL_EFFECT, factory)
        }

        // 定义方法:注册缩放特效
        val coordinateScaleEffect = fun() {
            val factory = ScaleEffectFactory(eisInfo)
            coordinate(TBL_SCALE_EFFECT, factory)
        }

        // 定义方法:注册视野特效
        val coordinateFovEffect = fun() {
            val factory = FovEffectFactory(eisInfo)
            coordinate(TBL_FOV_EFFECT, factory)
        }

        // 定义方法:注册边界特效
        val coordinateEdgeEffect = fun() {
            val factory = EdgeEffectFactory(videoSize, contentRect)
            coordinate(TBL_EDGE_EFFECT, factory)
        }

        // 定义方法:注册提亮特效
        val coordinateHdrEffect = fun() {
            val ratio = loaderOptions?.hdrSdrRatio ?: return
            val factory = HdrEffectFactory(ratio, isHdrVideoOlive, mediaUri)
            coordinate(TBL_CUSTOM_EFFECT_TRANSFORM, factory)
        }

        // 定义方法:屏蔽插帧特效(如需)
        val disableFrameFillIfNeed = fun() {
            if (OliveFrameFillerChecker.isSupportFrameFiller(mediaItem).not()) {
                GLog.d(TAG) { "[updateVideoPlayEffect] frame filler not supported , mute frame filler effects , ${mediaItem.id}" }
                frameFillRelatedEffects.forEach(::mute)
            }
        }

        coordinateCustomCrop()
        coordinateEisCrop()
        coordinateFrameFill()
        coordinateScaleEffect()
        coordinateFovEffect()
        coordinateEdgeEffect()
        coordinateHdrEffect()

        if (shouldLoadEffect().not()) {
            return
        }

        disableFrameFillIfNeed()

        add(TBL_CUSTOM_EFFECT_TRANSFORM)
        add(TBL_FRAME_FILL_EFFECT)
        add(TBL_CROP_EFFECT)

        GLog.d(TAG) { "[getVideoPlayEffect] effect list : $this" }
    }

    /**
     * 解出Olive图片的基础信息
     */
    private fun loadOlivePhotoContent(mediaItem: MediaItem): OLiveImageContent {
        var oLiveImageContent: OLiveImageContent
        var videoDisplayRectF: RectF? = null
        var imageDisplayRectF: RectF? = null
        var isOnlyFrameWaterMark: Boolean = false
        ContextGetter.context.getAppAbility<IWatermarkMasterAbility>()?.newWatermarkFileOperator(mediaItem.contentUri)?.use {
            val watermarkExtInfo = it.getAiMasterWatermarkExtInfo()
            videoDisplayRectF = watermarkExtInfo?.videoDisplayRect
            imageDisplayRectF = watermarkExtInfo?.imageDisplayRect
            isOnlyFrameWaterMark = watermarkExtInfo?.isOnlyFrameWatermark() ?: false
        }

        val cost = measureTimeMillis {
            val decoder: OLiveDecode = OLiveDecode.create(mediaItem.filePath)
            val oLivePhoto = decoder.decode()
            val coverTime: Long = oLivePhoto?.coverTimeInUs ?: 0L
            val offset: Long = oLivePhoto?.microVideo?.offset ?: 0L
            val length: Long = oLivePhoto?.microVideo?.length ?: 0L
            val videoStartUs: Long = oLivePhoto?.microVideo?.videoStartUs ?: 0L
            val videoEndUs: Long = oLivePhoto?.microVideo?.videoEndUs ?: 0L
            val isCoverChanged: Boolean = decoder.isCoverChanged()
            val oliveEditFlag = oLivePhoto?.oliveEditorFlag
            val oliveEnable: Boolean = oLivePhoto?.oliveEnable ?: true
            val oliveSoundEnable: Boolean = oLivePhoto?.oliveSoundEnable ?: true
            val oliveSubVideoInfo:  AVPlayer.VideoInfo? = getOliveSubVideo(isCoverChanged, oliveEditFlag)

            oLiveImageContent = OLiveImageContent(
                coverTimeInUs = coverTime,
                videoOffset = offset,
                videoLength = length,
                videoStartUs = videoStartUs,
                videoEndUs = videoEndUs,
                videoDisplayRectF = videoDisplayRectF,
                imageDisplayRectF = imageDisplayRectF,
                isOnlyFrameWaterMark = isOnlyFrameWaterMark,
                shouldReleaseVideoBackground = true,
                isCoverChanged = isCoverChanged,
                oliveEditorFlag = oliveEditFlag,
                oliveEnable = oliveEnable,
                oliveSoundEnable = oliveSoundEnable,
                isVideoSupportByDecoder = isVideoSupportByDecoder(mediaItem, offset, length),
                subVideoInfo = oliveSubVideoInfo
            )
        }
        GLog.d(TAG) { "[loadOlivePhotoVideoOffset] id = ${this.mediaItem.id},  cost = ${cost}ms, oliveImageContent = $oLiveImageContent" }
        return oLiveImageContent
    }

    /**
     * 判断视频是否支持被平台能力解码：
     * 1. 如果数据库中有已解析的信息，则直接使用。
     * 2. 如果没有则直接解析文件进行获取。
     *
     * 判断项：视频分辨率、帧率、视频编码类型
     *
     * @param mediaItem MediaItem
     * @return 是否被平台支持，失败则 null
     */
    private fun isVideoSupportByDecoder(mediaItem: MediaItem, videoOffset: Long, videoLength: Long): Boolean? {
        val isOriginalPhoto = mediaItem.toOriginalItem()?.localFileStatus == LocalFileStatus.LOCAL_FILE_STATUS_ORIGINAL
        if (isOriginalPhoto.not()) {
            // 如果本地文件非原图（此时文件内无视频），则直接返回 null，兜底规避 #9296756 的 bug。
            return null
        }

        var source = ""
        val dependInfo = mediaItem.getOliveVideoDependInfo()?.also {
            source = "mediaItem"
        } ?: MetadataGetterFactory.create(MetadataGetterFactory.DEFAULT_GETTER).getOliveVideoDependInfo(mediaItem, videoOffset, videoLength).also {
            source = "MetadataGetterFactory"
        }

        dependInfo ?: let {
            GLog.e(TAG, LogFlag.DL) { "[isVideoSupportByDecoder] dependInfo is null" }
            return null
        }

        val width = dependInfo.width
        val height = dependInfo.height
        val fps = dependInfo.fps
        val codecMimeType = dependInfo.codecType

        val specification = VideoSpec(width = width, height = height, fps = fps)
        val option = CodecSupportParser.Options(codecMimeType, isEncoder = false, isDesireHardwareAccelerated = false)

        // 需 Olive 内视频的尺寸和帧率被解码器支持
        val isSizeSupport = codecSupportParser.isSupportSize(specification = specification, options = option) ?: false
        val isFpsSupport = codecSupportParser.getSpecificationMaxFps(specification = specification, options = option) >= fps
        GLog.d(TAG, LogFlag.DL) {
            "[isVideoSupportByDecoder] mediaItem: ${mediaItem.mediaId}, source: $source, " +
                    "isSizeSupport: $isSizeSupport, isFpsSupport: $isFpsSupport, dependInfo: $dependInfo"
        }
        return isSizeSupport && isFpsSupport
    }

    /**
     * 通过 mediaItem 的 extra 进行获取 olive 视频相关信息
     */
    private fun MediaItem.getOliveVideoDependInfo(): OliveVideoDependInfo? {
        return extra?.let { extra ->
            val width = extra.getIntegerExtra(EXTRA_KEY_OLIVE_VIDEO_WIDTH).getOrLog(TAG, "extra.width") ?: return@let null
            val height = extra.getIntegerExtra(EXTRA_KEY_OLIVE_VIDEO_HEIGHT).getOrLog(TAG, "extra.height") ?: return@let null
            if ((width <= 0) || (height <= 0)) {
                GLog.d(TAG, "[MediaItem.getOliveVideoDependInfo] width or height is invalid, width: $width, height: $height")
                return@let null
            }

            val codecMimeType = extra.getStringExtra(EXTRA_KEY_OLIVE_VIDEO_CODEC_TYPE).getOrLog(TAG, "extra.codecType")
            if (codecMimeType.isNullOrEmpty()) return@let null

            val fps = extra.getFloatExtra(EXTRA_KEY_OLIVE_VIDEO_FPS).getOrLog(TAG, "extra.fps") ?: Number.NUMBER_0f

            OliveVideoDependInfo(width, height, fps, codecMimeType)
        }
    }

    /**
     * 解析文件获取 olive 视频相关信息
     */
    private fun IMetadataGetter.getOliveVideoDependInfo(mediaItem: MediaItem, offset: Long, length: Long): OliveVideoDependInfo? {
        return mediaItem.contentUri?.getFileDescriptorSafely(
            ContextGetter.context,
            OpenFileMode.MODE_READ.mode,
            TAG
        )?.use { pfd ->
            val metadataKeys = listOf(MetadataKey.WIDTH, MetadataKey.HEIGHT, MetadataKey.FPS, MetadataKey.CODEC_TYPE, MetadataKey.PROFILE)
            val metadata = parse(metadataKeys, pfd.fileDescriptor, Pair(offset, length))

            val width = (metadata[MetadataKey.WIDTH].getOrLog(TAG, "failed to get width") as? Int) ?: return null
            val height = (metadata[MetadataKey.HEIGHT].getOrLog(TAG, "failed to get height") as? Int) ?: return null
            val fps = (metadata[MetadataKey.FPS].getOrLog(TAG, "failed to get fps") as? Float) ?: return null
            val codecType = (metadata[MetadataKey.CODEC_TYPE].getOrLog(TAG, "failed to get codecType") as? String) ?: return null

            OliveVideoDependInfo(width, height, fps, codecType)
        } ?: let {
            GLog.e(TAG, LogFlag.DL) { "[getOliveVideoDependInfo] failed to open uri fd" }
            null
        }
    }

    private fun loadOLiveVideoBackground(mediaItem: MediaItem, thumbnailType: Int): Bitmap? =
        GTrace.trace({ "$TAG.loadOLiveVideoBackground.${mediaItem.mediaId}" }) {
            val resourceKey = ResourceKeyFactory.createResourceKey(mediaItem) ?: return@trace null
            val options = ResourceGetOptions(
                inThumbnailType = thumbnailType,
                inCacheOperation = CacheOperation.ReadWriteAllCache - CacheOperation.WriteMemCache,
                inCropParams = CropParams.noCrop(),
                inStorageQuality = StorageQuality.NOT_CARE
            )
            return@trace ContextGetter.context.withAbility<IResourcingAbility, Bitmap?> {
                it?.requestBitmap(resourceKey, options, cancelable)?.result
            }
        }

    /**
     * 是否需要给OLive的视频设置背景
     *
     * 部分大师水印OLive资源，图片左右两边有水印，但视频没有水印，会导致在播放的时候视频左右两边是黑色的。针对这个场景，我们直接将图片作为视频播放的背景。
     *
     * @param oliveImageDisplayRectF
     * @param oliveVideoDisplayRectF
     * @return
     */
    private fun shouldLoadOLiveVideoBackground(
        oliveImageDisplayRectF: RectF?,
        oliveVideoDisplayRectF: RectF?,
        isOnlyFrameWaterMark: Boolean
    ): Boolean {
        return (oliveImageDisplayRectF?.isEmpty == false) && (oliveVideoDisplayRectF?.isEmpty == true) && isOnlyFrameWaterMark
    }


    /**
     * 是否需要给OLive的视频设置背景
     * 1.对实况图片播放视频有开启特效
     * 2.实况照片有eis数据
     * 3.实况照片封面和视频都有水印
     *
     * @param oliveImageDisplayRectF  封面内容区域
     * @param oliveVideoDisplayRectF  视频内容区域
     * @param playEffect  视频播放特效
     * @return
     */
    @OptIn(UnstableApi::class)
    private fun shouldLoadOLiveVideoForeground(
        oliveImageDisplayRectF: RectF?,
        oliveVideoDisplayRectF: RectF?,
        eisData: String?
    ): Boolean {
        if (shouldLoadEffect().not()) {
            GLog.e(TAG, LogFlag.DL) { "[shouldLoadOLiveVideoForeground]should not load effect, no need to set foreground" }
            return false
        }

        //没有eis数据时，不需要设置前景
        if (eisData == null) {
            GLog.e(TAG, LogFlag.DL) { "[shouldLoadOLiveVideoForeground], olive do not have eis, no need to set foreground" }
            return false
        }

        return (oliveImageDisplayRectF?.isEmpty == false) && (oliveVideoDisplayRectF?.isEmpty == false)
    }

    /**
     * Olive 视频相关信息，用于判断是否被解码器支持
     *
     * @param width 视频宽
     * @param height 视频高
     * @param fps 帧率
     * @param codecType 视频编码类型
     */
    private data class OliveVideoDependInfo(
        val width: Int,
        val height: Int,
        val fps: Float,
        val codecType: String
    )

    private companion object {
        private const val TAG = "OliveDrawableLoader"

        /**
         * live photo 裁剪操作标记
         * Marked by zhangweichao, 待优化，等sdk更新后从sdk中获取
         */
        private const val FLAG_OLIVE_EDITOR_TRANSFORM = 1

        /**
         * 视频的 MimeType 前缀
         */
        private const val MIMETYPE_PREFIX_VIDEO = "video/"

        /**
         * 与插帧相关的特效名，
         * 禁用插帧时则以下特效不会被加入到最后的特效列表中。
         */
        private val frameFillRelatedEffects = setOf(TBL_FRAME_FILL_EFFECT, TBL_FOV_EFFECT)

        /**
         * 平台解码器查询器
         */
        private val codecSupportParser: CodecSupportParser by lazy { CodecSupportParser() }

        /**
         * 实况照片封面和视频宽高比差异容忍值，超过这个值则认为视频异常
         */
        private const val RATIO_THRESHOLD = 0.1f
    }
}