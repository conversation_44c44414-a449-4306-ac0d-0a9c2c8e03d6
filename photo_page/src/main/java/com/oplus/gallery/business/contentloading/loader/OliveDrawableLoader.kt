/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : OliveDrawableLoader.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/03/03
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** tan<PERSON><PERSON><PERSON>@Apps.Gallery          2024/03/03      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.business.contentloading.loader

import android.graphics.Bitmap
import android.graphics.ColorSpace
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.drawable.Drawable
import android.util.Size
import androidx.annotation.OptIn
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business.contentloading.ContentLoader
import com.oplus.gallery.business.contentloading.LoaderOptions
import com.oplus.gallery.business.contentloading.OnFutureDoneListener
import com.oplus.gallery.business.contentloading.loader.util.OliveFrameFillerChecker
import com.oplus.gallery.business.contentloading.loader.effect.OliveTBLVideoPlayEffect
import com.oplus.gallery.business.contentloading.loader.effect.OliveTBLVideoPlayEffect.Companion.TBL_CUSTOM_EFFECT_TRANSFORM
import com.oplus.gallery.business.drawable.AVPlayerDrawable
import com.oplus.gallery.business.drawable.hdrimage.HdrOliveDrawable
import com.oplus.gallery.business.drawable.olive.IOLiveImageContent
import com.oplus.gallery.business.drawable.olive.OLiveDrawable
import com.oplus.gallery.business.drawable.olive.OLiveImageContent
import com.oplus.gallery.business.drawable.recycle
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.getMediaUri
import com.oplus.gallery.business_lib.model.data.base.item.isDolbyVideoOlive
import com.oplus.gallery.business_lib.model.data.base.item.isHdrVideoOlive
import com.oplus.gallery.business_lib.model.data.base.item.isOliveHlgVideo
import com.oplus.gallery.business_lib.videoedit.IMetadataGetter
import com.oplus.gallery.business_lib.videoedit.MetadataGetterFactory
import com.oplus.gallery.business_lib.videoedit.MetadataKey
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_OLIVE_VIDEO_CODEC_TYPE
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_OLIVE_VIDEO_FPS
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_OLIVE_VIDEO_HEIGHT
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_OLIVE_VIDEO_WIDTH
import com.oplus.gallery.foundation.fileaccess.extension.OpenFileMode
import com.oplus.gallery.foundation.taskscheduling.Cancelable
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.getFileDescriptorSafely
import com.oplus.gallery.foundation.util.ext.getOrLog
import com.oplus.gallery.framework.abilities.caching.CacheOperation
import com.oplus.gallery.framework.abilities.caching.StorageQuality
import com.oplus.gallery.framework.abilities.colormanagement.IColorManagementAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_PHOTO_PAGE_HDR_VIDEO_OLIVE_TRANSFORM
import com.oplus.gallery.framework.abilities.resourcing.IResourcingAbility
import com.oplus.gallery.framework.abilities.resourcing.crop.CropParams
import com.oplus.gallery.framework.abilities.resourcing.key.ResourceKeyFactory
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.framework.abilities.watermark.IWatermarkMasterAbility
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.framework.app.withAbility
import com.oplus.gallery.olive_decoder.OLiveDecode
import com.oplus.gallery.standard_lib.app.AppConstants.Number
import com.oplus.gallery.standard_lib.codec.drawable.IHdrImageContent
import com.oplus.gallery.standard_lib.codec.player.effect.IVideoPlayEffect
import com.oplus.gallery.standard_lib.codec.player.effect.TBLVideoPlayEffect
import com.oplus.gallery.standard_lib.codec.player.effect.TBLVideoPlayEffect.Companion.TBL_FOV_EFFECT
import com.oplus.gallery.standard_lib.codec.player.effect.TBLVideoPlayEffect.Companion.TBL_FRAME_FILL_EFFECT
import com.oplus.gallery.standard_lib.scheduler.Job
import com.oplus.gallery.standard_lib.scheduler.JobContext
import com.oplus.gallery.standard_lib.scheduler.session.PrioritySession
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.gallery.videoeditor.data.VideoSpecification
import com.oplus.gallery.videoeditor.utils.CodecSupportParser
import com.oplus.tbl.exoplayer2.util.UnstableApi
import kotlinx.coroutines.CoroutineScope
import java.util.concurrent.Future
import kotlin.math.min
import kotlin.system.measureTimeMillis
import kotlin.time.measureTime

internal class OliveDrawableLoader(
    mediaItem: MediaItem,
    workSession: PrioritySession,
    workScope: CoroutineScope,
    val coverLoader: ContentLoader<Drawable>,
    val videoLoader: ContentLoader<Drawable>,
    private val loaderOptions: LoaderOptions?,
    futureListener: OnFutureDoneListener<Drawable> = { },
) : ContentLoader<Drawable>(mediaItem, workSession, workScope, futureListener) {

    /**
     * 大图是否支持HDR视频的Olive的下变换
     */
    private val isSupportHdrVideoOliveTransform: Boolean by lazy {
        ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_PHOTO_PAGE_HDR_VIDEO_OLIVE_TRANSFORM, false)
    }

    /**
     * 两件事
     * 1.生成封面的drawable
     * 2.生成视频的drawable
     */
    private val cancelable = Cancelable()

    override fun onLoadJobCanceled(workingFuture: Future<Drawable>?) {
        super.onLoadJobCanceled(workingFuture)
        cancelable.cancel()
    }

    @OptIn(UnstableApi::class)
    override fun onCreateLoadJob(): Job<Drawable>? {
        return object : Job<Drawable> {

            val coverDrawableLoadJob = coverLoader.onCreateLoadJob()
            val videoDrawableLoadJob = videoLoader.onCreateLoadJob()

            override fun call(jc: JobContext): Drawable? = runCatching {
                if (cancelable.isCancelled()) {
                    GLog.e(TAG) { "[onCreateLoadJob] job canceled for ${mediaItem.id}, will not load." }
                    return null
                }

                var coverDrawable: Drawable
                val coverJobCost = measureTimeMillis {
                    coverDrawable = coverDrawableLoadJob?.call(jc) ?: let {
                        GLog.e(TAG) { "[onCreateLoadJob] coverDrawable is null" }
                        return@runCatching null
                    }
                }

                if (cancelable.isCancelled()) {
                    coverDrawable.recycle()
                    GLog.e(TAG) { "[onCreateLoadJob] job canceled for ${mediaItem.id} after load coverDrawable, will not load." }
                    return null
                }

                val oLiveImageContent = loadOlivePhotoContent(mediaItem)

                (videoLoader as? AVPlayerLoader)?.let { loader ->
                    loader.videoTargetColorSpace = ContextGetter.context.getAppAbility<IColorManagementAbility>()?.use {
                        if (it.isDisplaySupportWCG.not() && (it.isWideColorGamutEnabled?.not() == true)) {
                            // 色彩管理1.0 生动模式下，olive视频需要强制关闭P3色域以保证与图片显示一致
                            ColorSpace.get(ColorSpace.Named.SRGB)
                        } else {
                            null
                        }
                    }

                    loader.videoPlayEffects = getVideoPlayEffect(oLiveImageContent)
                }

                /**
                 * 加载实况视频播放的前景和背景图片, 用于覆盖在视频上方解决视频内容播放的显示问题
                 */
                loadOLiveVideoMaskBitmapIfNeed(oLiveImageContent, videoLoader as? AVPlayerLoader)

                var videoDrawable: Drawable
                val videoJobCost = measureTimeMillis {
                    videoDrawable = videoDrawableLoadJob?.call(jc) ?: let {
                        GLog.e(TAG) { "[onCreateLoadJob] videoDrawable is null" }
                        return@runCatching null
                    }

                    /**
                     *大师水印的实况照片设置特效播放参数
                     */
                    oLiveImageContent.videoDisplayRectF?.takeIf { oLiveImageContent.isOnlyFrameWaterMark && it.isEmpty.not() }?.let {
                        val contentRect = Rect().apply { it.round(this) }
                        val videoSize = (videoDrawable as? AVPlayerDrawable)?.let { Size(it.onGetVideoWidth(), it.onGetVideoHeight()) }
                        val effectParams = TBLVideoPlayEffect.PlayEffectParams(cropRect = contentRect, videoSize = videoSize)
                        (videoLoader as? AVPlayerLoader)?.videoPlayEffects?.setPlayEffectParams(effectParams)
                    }
                }

                if (cancelable.isCancelled()) {
                    GLog.e(TAG) { "[onCreateLoadJob] job canceled for ${mediaItem.id} after load videoDrawable, will not load." }
                    coverDrawable.recycle()
                    videoDrawable.recycle()
                    return null
                }

                // Marked by tlyao  考虑并行执行, 节省时间, 先打个时间看看
                GLog.d(TAG) { "[onCreateLoadJob.call] coverCost = ${coverJobCost}ms, videoCost = ${videoJobCost}ms, id:${mediaItem.id}" }

                val hdrImageContent = coverDrawable as? IHdrImageContent
                if (hdrImageContent != null) {
                    return HdrOliveDrawable(coverDrawable, videoDrawable, hdrImageContent, oLiveImageContent)
                }

                return OLiveDrawable(coverDrawable, videoDrawable, oLiveImageContent)
            }.onFailure {
                GLog.e(TAG, it) { "[onCreateLoadJob] error to request OliveDrawableLoader;" }
            }.getOrNull()
        }
    }

    /**
     * 设置视频播放时候使用的前景和背景图片，解决实况视频本身内容有模糊的问题。在播放过程中不能让用户看到视频四周模糊
     *
     * @param imageContent
     * @param loader
     */
    private fun loadOLiveVideoMaskBitmapIfNeed(imageContent: OLiveImageContent, loader: AVPlayerLoader?) {
        //前景Mask, 加载实况封面缩图并裁掉内容区域，只留下水印部分
        val videoForeground =
            if (shouldLoadOLiveVideoForeground(
                    oliveImageDisplayRectF = imageContent.imageDisplayRectF,
                    oliveVideoDisplayRectF = imageContent.videoDisplayRectF,
                    playEffect = loader?.videoPlayEffects
                )
            ) {
                var thumbnailType = ThumbnailSizeUtils.getFullThumbnailKey()
                val videoDisplayRectF = imageContent.videoDisplayRectF ?: RectF()
                if (min(videoDisplayRectF.width(), videoDisplayRectF.height()) > ThumbnailSizeUtils.THUMBNAIL_SIZE_PHOTO) {
                    thumbnailType = ThumbnailSizeUtils.TYPE_SUPER_LARGE_THUMBNAIL
                }
                loadOLiveVideoBackground(mediaItem, thumbnailType)
            } else null

        //背景Mask, 如果有前景的话就不需要背景
        val videoBackground =
            if ((videoForeground == null) && shouldLoadOLiveVideoBackground(
                    oliveImageDisplayRectF = imageContent.imageDisplayRectF,
                    oliveVideoDisplayRectF = imageContent.videoDisplayRectF,
                    isOnlyFrameWaterMark = imageContent.isOnlyFrameWaterMark
                )
            ) {
                loadOLiveVideoBackground(mediaItem, ThumbnailSizeUtils.getFullThumbnailKey())
            } else null

        imageContent.apply {
            oliveVideoForeground = videoForeground
            oliveVideoBackground = videoBackground
        }
    }

    /**
     * 获取olive文件的视频特效解码器
     */
    @OptIn(UnstableApi::class)
    private fun getVideoPlayEffect(imageContent: IOLiveImageContent): IVideoPlayEffect? {
        /**
         * 不支持补帧时，关闭视频播放特效，返回空的播放特效
         */
        if (ConfigAbilityWrapper.getBoolean(ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_OLIVE_FRAME_FILLTER).not()) {
            GLog.e(TAG, LogFlag.DL) { "[getVideoPlayEffect] feature not support" }
            return null
        }

        /**
         * 杜比视频生成的olive图不需要特效处理
         */
        if (mediaItem.isDolbyVideoOlive()) {
            GLog.e(TAG, LogFlag.DL) { "[getVideoPlayEffect] olive video is dolby" }
            return null
        }

        val mediaUri = mediaItem.getMediaUri() ?: return null

        // 是否是HDR视频的Olive图
        val isHdrVideoOlive = mediaItem.isHdrVideoOlive()
        var oliveVideoPlayEffect: TBLVideoPlayEffect? = null
        imageContent.videoOffset.takeIf { it != 0L }?.also { offset ->
            oliveVideoPlayEffect = OliveTBLVideoPlayEffect(
                filePath = mediaItem.filePath,
                offset = offset,
                isHdrVideo = isHdrVideoOlive,
                mediaUri = mediaUri
            ).also { effect ->
                // 判断是否支持插帧
                if (OliveFrameFillerChecker.isSupportFrameFiller(mediaItem).not()) {
                    GLog.d(TAG, LogFlag.DL) { "[getVideoPlayEffect] is not support frame filler, ${mediaItem.id}" }
                    frameFillRelatedEffects.forEach(effect::disableEffectByKey)
                }

                if (effect.init()) {
                    effect.decode()
                }

                // 如果为支持下变换的hdr视频的Olive图，则添加下变换Effect，否则播放的首帧没有下变换（会导致未提亮而色彩暗淡）
                if (isSupportHdrVideoOliveTransform && mediaItem.isOliveHlgVideo()) {
                    // 设置“屏幕提亮后的亮度相比于基准亮度的scale值”
                    loaderOptions?.hdrSdrRatio?.let { effect.hdrSdrRatio = it }

                    val fovEffects = effect.getEffectListKeys()
                    val finalPlayEffectList: List<String> = ArrayList<String>().apply {
                        // 下变换效果要放在第0个
                        add(TBL_CUSTOM_EFFECT_TRANSFORM)
                        addAll(fovEffects)
                    }
                    effect.setVideoEffects(finalPlayEffectList)
                }
            }
        }

        GLog.d(TAG, LogFlag.DL) { "extractEffectMap: " + oliveVideoPlayEffect?.getEffectList()?.size }
        return oliveVideoPlayEffect
    }

    /**
     * 解出Olive图片的基础信息
     */
    private fun loadOlivePhotoContent(mediaItem: MediaItem): OLiveImageContent {
        var oLiveImageContent: OLiveImageContent
        var videoDisplayRectF: RectF? = null
        var imageDisplayRectF: RectF? = null
        var isOnlyFrameWaterMark: Boolean = false
        ContextGetter.context.getAppAbility<IWatermarkMasterAbility>()?.newWatermarkFileOperator(mediaItem.contentUri)?.use {
            val watermarkExtInfo = it.getAiMasterWatermarkExtInfo()
            videoDisplayRectF = watermarkExtInfo?.videoDisplayRect
            imageDisplayRectF = watermarkExtInfo?.imageDisplayRect
            isOnlyFrameWaterMark = watermarkExtInfo?.isOnlyFrameWatermark() ?: false
        }

        val cost = measureTimeMillis {
            val decoder: OLiveDecode = OLiveDecode.create(mediaItem.filePath)
            val oLivePhoto = decoder.decode()
            val coverTime: Long = oLivePhoto?.coverTimeInUs ?: 0L
            val offset: Long = oLivePhoto?.microVideo?.offset ?: 0L
            val length: Long = oLivePhoto?.microVideo?.length ?: 0L
            val videoStartUs: Long = oLivePhoto?.microVideo?.videoStartUs ?: 0L
            val videoEndUs: Long = oLivePhoto?.microVideo?.videoEndUs ?: 0L
            val isCoverChanged: Boolean = decoder.isCoverChanged()
            val oliveEditFlag = oLivePhoto?.oliveEditorFlag
            val oliveEnable: Boolean = oLivePhoto?.oliveEnable ?: true
            val oliveSoundEnable: Boolean = oLivePhoto?.oliveSoundEnable ?: true

            oLiveImageContent = OLiveImageContent(
                coverTimeInUs = coverTime,
                videoOffset = offset,
                videoLength = length,
                videoStartUs = videoStartUs,
                videoEndUs = videoEndUs,
                videoDisplayRectF = videoDisplayRectF,
                imageDisplayRectF = imageDisplayRectF,
                isOnlyFrameWaterMark = isOnlyFrameWaterMark,
                shouldReleaseVideoBackground = true,
                isCoverChanged = isCoverChanged,
                oliveEditorFlag = oliveEditFlag,
                oliveEnable = oliveEnable,
                oliveSoundEnable = oliveSoundEnable,
                isVideoSupportByDecoder = isVideoSupportByDecoder(mediaItem, offset, length),
            )
        }
        GLog.d(TAG) { "[loadOlivePhotoVideoOffset] id = ${this.mediaItem.id},  cost = ${cost}ms, oliveImageContent = $oLiveImageContent" }
        return oLiveImageContent
    }

    /**
     * 判断视频是否支持被平台能力解码：
     * 1. 如果数据库中有已解析的信息，则直接使用。
     * 2. 如果没有则直接解析文件进行获取。
     *
     * 判断项：视频分辨率、帧率、视频编码类型
     *
     * @param mediaItem MediaItem
     * @return 是否被平台支持，失败则 null
     */
    private fun isVideoSupportByDecoder(mediaItem: MediaItem, videoOffset: Long, videoLength: Long): Boolean? {
        var source = ""
        val dependInfo = mediaItem.getOliveVideoDependInfo()?.also {
            source = "mediaItem"
        } ?: MetadataGetterFactory.create(MetadataGetterFactory.DEFAULT_GETTER).getOliveVideoDependInfo(mediaItem, videoOffset, videoLength).also {
            source = "MetadataGetterFactory"
        }

        dependInfo ?: let {
            GLog.e(TAG, LogFlag.DL) { "[isVideoSupportByDecoder] dependInfo is null" }
            return null
        }

        val width = dependInfo.width
        val height = dependInfo.height
        val fps = dependInfo.fps
        val codecMimeType = dependInfo.codecType

        val specification = VideoSpecification(width = width, height = height, fps = fps)
        val option = CodecSupportParser.Options(codecMimeType, isEncoder = false, isDesireHardwareAccelerated = false)

        // 需 Olive 内视频的尺寸和帧率被解码器支持
        val isSizeSupport = codecSupportParser.isSupportSize(specification = specification, options = option) ?: false
        val isFpsSupport = codecSupportParser.getSpecificationMaxFps(specification = specification, options = option) >= fps
        GLog.d(TAG, LogFlag.DL) {
            "[isVideoSupportByDecoder] mediaItem: ${mediaItem.mediaId}, source: $source, " +
                    "isSizeSupport: $isSizeSupport, isFpsSupport: $isFpsSupport, dependInfo: $dependInfo"
        }
        return isSizeSupport && isFpsSupport
    }

    /**
     * 通过 mediaItem 的 extra 进行获取 olive 视频相关信息
     */
    private fun MediaItem.getOliveVideoDependInfo(): OliveVideoDependInfo? {
        return extra?.let { extra ->
            val width = extra.getIntegerExtra(EXTRA_KEY_OLIVE_VIDEO_WIDTH).getOrLog(TAG, "extra.width") ?: return@let null
            val height = extra.getIntegerExtra(EXTRA_KEY_OLIVE_VIDEO_HEIGHT).getOrLog(TAG, "extra.height") ?: return@let null
            if ((width <= 0) || (height <= 0)) {
                GLog.d(TAG, "[MediaItem.getOliveVideoDependInfo] width or height is invalid, width: $width, height: $height")
                return@let null
            }

            val codecMimeType = extra.getStringExtra(EXTRA_KEY_OLIVE_VIDEO_CODEC_TYPE).getOrLog(TAG, "extra.codecType")
            if (codecMimeType.isNullOrEmpty()) return@let null

            val fps = extra.getFloatExtra(EXTRA_KEY_OLIVE_VIDEO_FPS).getOrLog(TAG, "extra.fps") ?: Number.NUMBER_0f

            OliveVideoDependInfo(width, height, fps, codecMimeType)
        }
    }

    /**
     * 解析文件获取 olive 视频相关信息
     */
    private fun IMetadataGetter.getOliveVideoDependInfo(mediaItem: MediaItem, offset: Long, length: Long): OliveVideoDependInfo? {
        return mediaItem.contentUri?.getFileDescriptorSafely(
            ContextGetter.context,
            OpenFileMode.MODE_READ.mode,
            TAG
        )?.use { pfd ->
            val metadataKeys = listOf(MetadataKey.WIDTH, MetadataKey.HEIGHT, MetadataKey.FPS, MetadataKey.CODEC_TYPE, MetadataKey.PROFILE)
            val metadata = parse(metadataKeys, pfd.fileDescriptor, Pair(offset, length))

            val width = (metadata[MetadataKey.WIDTH].getOrLog(TAG, "failed to get width") as? Int) ?: return null
            val height = (metadata[MetadataKey.HEIGHT].getOrLog(TAG, "failed to get height") as? Int) ?: return null
            val fps = (metadata[MetadataKey.FPS].getOrLog(TAG, "failed to get fps") as? Float) ?: return null
            val codecType = (metadata[MetadataKey.CODEC_TYPE].getOrLog(TAG, "failed to get codecType") as? String) ?: return null

            OliveVideoDependInfo(width, height, fps, codecType)
        } ?: let {
            GLog.e(TAG, LogFlag.DL) { "[getOliveVideoDependInfo] failed to open uri fd" }
            null
        }
    }

    private fun loadOLiveVideoBackground(mediaItem: MediaItem, thumbnailType: Int): Bitmap? =
        GTrace.trace({ "$TAG.loadOLiveVideoBackground.${mediaItem.mediaId}" }) {
            val resourceKey = ResourceKeyFactory.createResourceKey(mediaItem) ?: return@trace null
            val options = ResourceGetOptions(
                inThumbnailType = thumbnailType,
                inCacheOperation = CacheOperation.ReadWriteAllCache - CacheOperation.WriteMemCache,
                inCropParams = CropParams.noCrop(),
                inStorageQuality = StorageQuality.NOT_CARE
            )
            return@trace ContextGetter.context.withAbility<IResourcingAbility, Bitmap?> {
                it?.requestBitmap(resourceKey, options, cancelable)?.result
            }
        }

    /**
     * 是否需要给OLive的视频设置背景
     *
     * 部分大师水印OLive资源，图片左右两边有水印，但视频没有水印，会导致在播放的时候视频左右两边是黑色的。针对这个场景，我们直接将图片作为视频播放的背景。
     *
     * @param oliveImageDisplayRectF
     * @param oliveVideoDisplayRectF
     * @return
     */
    private fun shouldLoadOLiveVideoBackground(
        oliveImageDisplayRectF: RectF?,
        oliveVideoDisplayRectF: RectF?,
        isOnlyFrameWaterMark: Boolean
    ): Boolean {
        return (oliveImageDisplayRectF?.isEmpty == false) && (oliveVideoDisplayRectF?.isEmpty == true) && isOnlyFrameWaterMark
    }


    /**
     * 是否需要给OLive的视频设置背景
     * 1.对实况图片播放视频有开启特效
     * 2.实况照片有eis数据
     * 3.实况照片封面和视频都有水印
     *
     * @param oliveImageDisplayRectF  封面内容区域
     * @param oliveVideoDisplayRectF  视频内容区域
     * @param playEffect  视频播放特效
     * @return
     */
    @OptIn(UnstableApi::class)
    private fun shouldLoadOLiveVideoForeground(
        oliveImageDisplayRectF: RectF?,
        oliveVideoDisplayRectF: RectF?,
        playEffect: IVideoPlayEffect?
    ): Boolean {
        //特效列表为空时，不需要设置前景
        val olivePlayEffect = (playEffect as? TBLVideoPlayEffect) ?: run {
            GLog.e(TAG, LogFlag.DL) { "[shouldLoadOLiveVideoForeground], olive do not have play effect, no need to set foreground" }
            return false
        }

        //没有eis数据时，不需要设置前景
        if (olivePlayEffect.getVideoEisData() == null) {
            GLog.e(TAG, LogFlag.DL) { "[shouldLoadOLiveVideoForeground], olive do not have eis, no need to set foreground" }
            return false
        }

        return (oliveImageDisplayRectF?.isEmpty == false) && (oliveVideoDisplayRectF?.isEmpty == false)
    }

    /**
     * Olive 视频相关信息，用于判断是否被解码器支持
     *
     * @param width 视频宽
     * @param height 视频高
     * @param fps 帧率
     * @param codecType 视频编码类型
     */
    private data class OliveVideoDependInfo(
        val width: Int,
        val height: Int,
        val fps: Float,
        val codecType: String
    )

    private companion object {
        private const val TAG = "OliveDrawableLoader"

        /**
         * live photo 裁剪操作标记
         * Marked by zhangweichao, 待优化，等sdk更新后从sdk中获取
         */
        private const val FLAG_OLIVE_EDITOR_TRANSFORM = 1

        /**
         * 视频的 MimeType 前缀
         */
        private const val MIMETYPE_PREFIX_VIDEO = "video/"

        /**
         * 与插帧相关的特效名，
         * 禁用插帧时则以下特效不会被加入到最后的特效列表中。
         */
        private val frameFillRelatedEffects = setOf(TBL_FRAME_FILL_EFFECT, TBL_FOV_EFFECT)

        /**
         * 平台解码器查询器
         */
        private val codecSupportParser: CodecSupportParser by lazy { CodecSupportParser() }
    }
}