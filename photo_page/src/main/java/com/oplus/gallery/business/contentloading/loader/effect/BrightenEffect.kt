/*********************************************************************************
 ** Copyright (C), 2022-2032, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : RatioGlProcessorEffect.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2025/07/30
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>               2025/07/30  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.business.contentloading.loader.effect

import android.net.Uri
import com.oplus.gallery.business.contentloading.loader.effect.HdrVideoOliveTransformTBLGLProcessor.TransformConfig
import com.oplus.gallery.standard_lib.codec.player.effect.EffectKey
import com.oplus.tblplayer.processor.GlProcessorEffect

/**
 * TBL 视频播放特效: 提亮
 * - [EffectKey]: [EffectKey.TBL_CUSTOM_EFFECT_TRANSFORM]
 * - 可以通过 [ratio] 实时更新提亮倍数
 */
class BrightenEffect(isHdrVideo: Boolean, mediaUri: Uri, var ratio: Float) : GlProcessorEffect(
    HdrVideoOliveTransformTBLGLProcessor(
        isHdrVideo = isHdrVideo,
        mediaUri = mediaUri,
        transformConfig = TransformConfig { ratio }
    )
)