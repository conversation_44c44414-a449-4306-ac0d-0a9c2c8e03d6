<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.oplus.gallery.photo_page.ui.section.details.widget.PanelDetailsTextView
        android:id="@+id/tv_fps"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/photopage_detail_content_exif_iso_horizontal_padding"
        android:ellipsize="end"
        android:fontFamily="Avenir Next"
        android:gravity="center"
        android:includeFontPadding="false"
        android:letterSpacing="0.04"
        android:lineHeight="@dimen/photopage_detail_view_dimen_16dp"
        android:maxLines="3"
        android:textColor="@color/photopage_details_card_big_text"
        android:textFontWeight="600"
        android:textSize="@dimen/photopage_detail_text_view_small_size"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/tv_duration"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="30 FPS" />

    <com.oplus.gallery.photo_page.ui.section.details.widget.PanelDetailsView
        android:id="@+id/v_line"
        android:layout_width="@dimen/photopage_detail_view_line"
        android:layout_height="@dimen/photopage_detail_view_dimen_12dp"
        android:background="@color/photopage_details_parameter_card_line_black"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/tv_fps"
        app:layout_constraintRight_toLeftOf="@+id/tv_duration"
        app:layout_constraintTop_toTopOf="parent" />

    <com.oplus.gallery.photo_page.ui.section.details.widget.PanelDetailsTextView
        android:id="@+id/tv_duration"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/photopage_detail_content_exif_iso_horizontal_padding"
        android:ellipsize="end"
        android:fontFamily="Avenir Next"
        android:gravity="center"
        android:includeFontPadding="false"
        android:letterSpacing="0.04"
        android:lineHeight="@dimen/photopage_detail_view_dimen_16dp"
        android:maxLines="3"
        android:textColor="@color/photopage_details_card_big_text"
        android:textFontWeight="600"
        android:textSize="@dimen/photopage_detail_text_view_small_size"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tv_fps"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="00:05" />

</androidx.constraintlayout.widget.ConstraintLayout>