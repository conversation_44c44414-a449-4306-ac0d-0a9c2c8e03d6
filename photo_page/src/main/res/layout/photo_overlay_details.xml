<?xml version="1.0" encoding="utf-8"?>
<com.oplus.gallery.photo_page.ui.section.details.widget.OverlayConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="ResourceName">

    <com.oplus.gallery.photo_page.ui.section.intelliRecommend.FuncRecyclerView
        android:id="@+id/vs_intelli_func"
        android:layout_width="@dimen/photopage_intelli_func_item_max_width"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/photopage_intelli_func_margin_start"
        android:layout_marginEnd="@dimen/photopage_intelli_func_margin_end"
        android:layout_marginBottom="@dimen/photopage_intelli_func_margin_bottom"
        android:visibility="visible"
        app:layout_constrainedHeight="true"
        app:layout_constraintBottom_toTopOf="@id/sv_detail"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="1" />

    <com.oplus.gallery.photo_page.ui.section.details.widget.PanelDetailsScrollView
        android:id="@+id/sv_detail"
        android:layout_width="match_parent"
        android:layout_height="@dimen/photopage_details_small_screen_portrait_min_height"
        android:layout_gravity="bottom"
        android:background="@color/photo_page_details_bg_color"
        app:layout_constraintBottom_toBottomOf="parent">

        <include layout="@layout/photo_page_layout_details_content" />
    </com.oplus.gallery.photo_page.ui.section.details.widget.PanelDetailsScrollView>

</com.oplus.gallery.photo_page.ui.section.details.widget.OverlayConstraintLayout>
