<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:state_checked="true" android:state_pressed="true">
        <bitmap
            android:tint="?attr/couiColorPressBackground"
            android:src="@drawable/photopage_ic_menu_favorites_checked"/>
    </item>

    <item android:state_checked="true">
        <bitmap
            android:tint="?attr/gColorPrimary"
            android:src="@drawable/photopage_ic_menu_favorites_checked"/>
    </item>

    <item android:state_enabled="false">
        <bitmap
            android:alpha="0.2"
            android:src="@drawable/photopage_ic_menu_favorites"/>
    </item>

    <item android:state_pressed="true">
        <bitmap
            android:alpha="0.55"
            android:src="@drawable/photopage_ic_menu_favorites"/>
    </item>

    <item>
        <bitmap
            android:alpha="0.9"
            android:src="@drawable/photopage_ic_menu_favorites"/>
    </item>

</selector>