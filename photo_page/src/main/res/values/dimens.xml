<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!--TODO 下沉到 foundation - SeekBar-start -->
    <dimen name="foundation_thumbnail_seekbar_slice_width">40dp</dimen>
    <dimen name="foundation_thumbnail_seekbar_slice_height">40dp</dimen>
    <!--TODO 下沉到 foundation - SeekBar-end -->

    <!-- start：简版大图 -->
    <!-- start：简版大图拖拽把手，用于小屏场景（比如蜻蜓外屏）-->
    <dimen name="concisephoto_container_handle_root_min_height">26dp</dimen>
    <dimen name="concisephoto_container_handle_margin_end">2dp</dimen>
    <!-- end：  简版大图拖拽把手，用于小屏场景（比如蜻蜓外屏）-->

    <!-- start：简版大图菜单：删除，用于小屏场景（比如蜻蜓外屏）-->
    <dimen name="concisephoto_menu_btn_recycle_size">38dp</dimen>
    <dimen name="concisephoto_menu_btn_recycle_margin_bottom">17dp</dimen>
    <dimen name="concisephoto_menu_btn_recycle_margin_horizontal">14dp</dimen>
    <!-- end：  简版大图菜单：删除，用于小屏场景（比如蜻蜓外屏）-->

    <!-- start：简版大图播控，用于小屏场景（比如蜻蜓外屏）-->
    <dimen name="concisephoto_playback_btn_play_size">48dp</dimen>
    <dimen name="concisephoto_playback_btn_play_margin_bottom">12dp</dimen>
    <dimen name="concisephoto_playback_seekbar_margin_bottom">70.5dp</dimen>
    <dimen name="concisephoto_playback_seekbar_margin_horizontal">10dp</dimen>
    <dimen name="concisephoto_playback_floatmsg_margin_bottom">102.5dp</dimen>
    <dimen name="concisephoto_playback_floatmsg_text_size">12sp</dimen>
    <!-- end：  简版大图播控，用于小屏场景（比如蜻蜓外屏）-->

    <!-- start：切换大图显示设备蒙版提示的样式，用于小屏场景（比如蜻蜓外屏）-->
    <dimen name="photopage_masktips_img_arrow_width">28dp</dimen>
    <dimen name="photopage_masktips_img_arrow_height">12dp</dimen>
    <dimen name="photopage_masktips_img_arrow_margin_bottom">18dp</dimen>
    <dimen name="photopage_masktips_img_arrow_anim_delta_y">27dp</dimen>
    <dimen name="photopage_masktips_txt_text_size">16sp</dimen>
    <dimen name="photopage_masktips_txt_margin_horizontal">16dp</dimen>
    <dimen name="photopage_masktips_txt_margin_bottom">64dp</dimen>
    <dimen name="photopage_masktips_icon_size">108dp</dimen>
    <dimen name="photopage_masktips_icon_margin_top">156dp</dimen>
    <dimen name="photopage_masktips_anim_breathing_bg_height">800dp</dimen>
    <!-- end：  切换大图显示设备蒙版提示的样式，用于小屏场景（比如蜻蜓外屏）-->
    <!-- start：简版大图菜单 -->
    <dimen name="concisephoto_menu_toolbar_padding_top">4dp</dimen>
    <!-- end：简版大图菜单 -->
    <!-- end：  简版大图 -->


    <dimen name="photopage_playback_thumbnail_seekbar_padding_vertical">8dp</dimen>
    <dimen name="photopage_playback_thumbnail_seekbar_height">60dp</dimen>
    <dimen name="photopage_playback_thumbnail_seekbar_margin_horizontal">10dp</dimen>
    <dimen name="photopage_playback_thumbnail_seekbar_slice_width">40dp</dimen>
    <dimen name="photopage_playback_thumbnail_seekbar_slice_height">40dp</dimen>

    <integer name="photopage_playback_time_text_size_shadow_radio">1</integer>
    <dimen name="photopage_playback_thumbnail_time_background_radius">5.33dp</dimen>
    <dimen name="photopage_playback_thumbnail_time_padding_top">4dp</dimen>
    <dimen name="photopage_playback_thumbnail_time_padding_bottom">3.67dp</dimen>
    <dimen name="photopage_playback_thumbnail_time_margin_bottom">4dp</dimen>
    <dimen name="photopage_playback_thumbnail_time_text_size">24sp</dimen>

    <dimen name="photopage_playback_normal_seekbar_height">60dp</dimen>
    <dimen name="photopage_playback_normal_time_text_size">12sp</dimen>
    <dimen name="photopage_playback_normal_time_margin_btn">10dp</dimen>

    <dimen name="photopage_playback_btn_width">40dp</dimen>
    <dimen name="photopage_playback_btn_height">40dp</dimen>

    <dimen name="photopage_playback_overlay_loading_text_margin_top">16dp</dimen>
    <dimen name="photopage_playback_overlay_loading_text_size">16dp</dimen>
    <dimen name="photopage_playback_overlay_loading_size">26dp</dimen>

    <dimen name="photopage_thumbline_playback_layout_padding_bottom">12dp</dimen>
    <dimen name="photopage_thumbline_playback_info_hints_height">28dp</dimen>
    <dimen name="photopage_thumbline_playback_info_hints_min_width">113dp</dimen>
    <dimen name="photopage_thumbline_playback_info_hints_min_padding_vertical">4.38dp</dimen>
    <dimen name="photopage_thumbline_playback_info_hints_min_padding_start">10dp</dimen>
    <dimen name="photopage_thumbline_playback_info_hints_min_padding_end">10dp</dimen>
    <dimen name="photopage_thumbline_playback_button_width">19.25dp</dimen>
    <dimen name="photopage_thumbline_playback_button_height">19.25dp</dimen>
    <dimen name="photopage_thumbline_playback_button_margin_end">2dp</dimen>
    <dimen name="photopage_thumbline_playback_button_sound_width">28dp</dimen>
    <dimen name="photopage_thumbline_playback_button_sound_height">28dp</dimen>
    <dimen name="photopage_thumbline_playback_button_sound_margin_start">12dp</dimen>
    <dimen name="photopage_thumbline_playback_button_sound_padding">6dp</dimen>
    <dimen name="photopage_thumbline_playback_progress_hints_text_size">10dp</dimen>

    <dimen name="photopage_cshot_indicator_item_spacing">4dp</dimen>
    <dimen name="photopage_cshot_indicator_height">40dp</dimen>
    <dimen name="photopage_cshot_indicator_arrow_height">24dp</dimen>
    <dimen name="photopage_cshot_indicator_item_size_default">40dp</dimen>
    <dimen name="photopage_cshot_indicator_item_size_small">20dp</dimen>
    <dimen name="photopage_cshot_indicator_animation_view_width">26dp</dimen>
    <dimen name="photopage_cshot_indicator_animation_view_height">26dp</dimen>

    <dimen name="photopage_cshot_page_pager_item_margin">8dp</dimen>
    <dimen name="photopage_cshot_page_margin_top">12dp</dimen>
    <dimen name="photopage_cshot_page_margin_bottom">12dp</dimen>
    <dimen name="photopage_cshot_indicator_margin_bottom">16dp</dimen>
    <!-- 横屏和竖屏的边距不一样，但是当分屏横屏场景需要替换为最小边距 -->
    <dimen name="photopage_cshot_page_padding_horizontal_min">80dp</dimen>
    <dimen name="photopage_cshot_page_padding_horizontal">48dp</dimen>
    <dimen name="photopage_cshot_page_checkbox_size">24dp</dimen>
    <dimen name="photopage_cshot_page_checkbox_margin">4dp</dimen>

    <dimen name="photopage_cshot_top_menu_horizontal_padding_in_landscape_mode">28dp</dimen>
    <dimen name="photopage_cshot_top_menu_top_padding_default">32dp</dimen>
    <dimen name="photopage_cshot_top_menu_top_padding_no_status_bar">12dp</dimen>

    <dimen name="photopage_bubble_min_size">80dp</dimen>
    <dimen name="photopage_bubble_max_size">142dp</dimen>
    <dimen name="photopage_bubble_margin_top">10dp</dimen>
    <dimen name="photopage_bubble_horizontal_margin">16dp</dimen>
    <dimen name="photopage_bubble_padding">4dp</dimen>
    <dimen name="photopage_bubble_inside_corner_radius">16dp</dimen>
    <dimen name="photopage_bubble_outside_corner_radius">20dp</dimen>
    <dimen name="photopage_bubble_elevation">20dp</dimen>

    <dimen name="photopage_detail_tag_margin_start">18dp</dimen>
    <dimen name="photopage_detail_tag_margin_start_small_screen">20dp</dimen>
    <dimen name="photopage_detail_tag_margin_top">12dp</dimen>
    <dimen name="photopage_detail_tag_height">20dp</dimen>

    <dimen name="photopage_menu_texttips_min_anchor_center_x_end_margin">33dp</dimen>
    <dimen name="photopage_top_menu_horizontal_padding_in_landscape_mode">28dp</dimen>
    <dimen name="photopage_top_menu_horizontal_padding_in_landscape_large_mode">44dp</dimen>

    <dimen name="photopage_sub_menu_horizontal_margin_for_top_menu">24dp</dimen>
    <dimen name="photopage_toolbar_min_height">52dp</dimen>

    <!-- 投屏 start -->
    <dimen name="cast_list_dialog_height_default">418dp</dimen>
    <dimen name="cast_list_dialog_content_card_corner_radius">10dp</dimen>
    <dimen name="cast_list_dialog_content_card_margin">16dp</dimen>
    <dimen name="cast_list_dialog_content_card_padding_bottom">8dp</dimen>
    <dimen name="cast_list_dialog_content_card_padding_top">8dp</dimen>
    <dimen name="cast_list_dialog_searching_img_container_wh">120dp</dimen>
    <dimen name="cast_list_item_img_tv_margin_start">16dp</dimen>
    <dimen name="cast_list_dialog_button_height">44dp</dimen>
    <dimen name="cast_list_dialog_button_width_large">220dp</dimen>
    <dimen name="cast_list_dialog_search_wlan_hint_margin_horizontal">24dp</dimen>
    <dimen name="cast_list_dialog_search_device_margin_top">50dp</dimen>
    <dimen name="cast_list_dialog_no_device_h">200dp</dimen>
    <dimen name="cast_list_dialog_content_card_margin_top">16dp</dimen>
    <dimen name="cast_list_dialog_button_layout_margin_top">24dp</dimen>
    <dimen name="cast_list_dialog_button_layout_margin_bottom">20dp</dimen>
    <dimen name="cast_list_dialog_left_button_margin_start">24dp</dimen>
    <dimen name="cast_list_dialog_left_button_margin_end">8dp</dimen>
    <dimen name="cast_list_dialog_center_button_margin_start">16dp</dimen>
    <dimen name="cast_list_dialog_center_button_margin_end">8dp</dimen>
    <dimen name="cast_list_dialog_right_button_margin_end">24dp</dimen>
    <dimen name="cast_list_dialog_button_text_size">16sp</dimen>

    <dimen name="cast_list_item_img_tv_wh">24dp</dimen>
    <dimen name="cast_list_item_content_layout_pending_start">16dp</dimen>
    <dimen name="cast_list_item_content_layout_pending_end">16dp</dimen>
    <dimen name="cast_list_item_device_name_min_height">21dp</dimen>
    <dimen name="cast_list_item_recommend_min_height">14dp</dimen>
    <dimen name="cast_list_item_recommend_padding_horizontal">3dp</dimen>
    <dimen name="cast_list_item_name_padding_end">8dp</dimen>
    <dimen name="cast_list_item_state_description_min_height">16dp</dimen>
    <dimen name="cast_list_item_info_padding_vertical">12dp</dimen>
    <dimen name="cast_list_item_info_layout_width">208dp</dimen>
    <dimen name="cast_list_item_info_state_margin_top">3dp</dimen>
    <!-- 投屏 end -->

    <dimen name="photopage_tips_overlay_anim_view_width">50dp</dimen>
    <dimen name="photopage_tips_overlay_anim_view_padding">12dp</dimen>

    <!-- ↓↓↓缩略图轴小尺寸↓↓↓ -->
    <dimen name="photopage_thumbline_list_height">38dp</dimen>
    <dimen name="photopage_thumbline_list_height_landscape">52dp</dimen>
    <dimen name="photopage_thumbline_item_width_normal">21.33dp</dimen>
    <dimen name="photopage_thumbline_item_height_normal">32dp</dimen>
    <dimen name="photopage_thumbline_item_width_center">32dp</dimen>
    <dimen name="photopage_thumbline_item_height_center">32dp</dimen>
    <dimen name="photopage_thumbline_gap_center_horizontal_image">16dp</dimen>
    <dimen name="photopage_thumbline_gap_center_horizontal_video">16dp</dimen>
    <dimen name="photopage_thumbline_gap_center_top">4dp</dimen>
    <dimen name="photopage_thumbline_gap_center_bottom">2dp</dimen>
    <dimen name="photopage_thumbline_gap_center_bottom_landscape">16dp</dimen>
    <dimen name="photopage_thumbline_gap_normal_horizontal">4dp</dimen>
    <dimen name="photopage_thumbline_gap_normal_top">4dp</dimen>
    <dimen name="photopage_thumbline_gap_normal_bottom">2dp</dimen>
    <dimen name="photopage_thumbline_gap_normal_bottom_landscape">16dp</dimen>
    <dimen name="photopage_thumbline_item_playback_thumb_vertical_offset">-3.34dp</dimen>
    <dimen name="photopage_thumbline_item_playback_thumb_slice_width">21.33dp</dimen>
    <dimen name="photopage_thumbline_item_playback_thumb_slice_height">32dp</dimen>
    <dimen name="photopage_thumbline_item_playback_preview_slice_width">32dp</dimen>
    <dimen name="photopage_thumbline_item_playback_preview_slice_height">32dp</dimen>
    <dimen name="photopage_thumbline_item_playback_detail_slice_width">48dp</dimen>
    <dimen name="photopage_thumbline_item_playback_detail_height">36dp</dimen>
    <dimen name="photopage_thumbline_item_playback_detail_gap_center_top">2dp</dimen>
    <dimen name="photopage_thumbline_item_playback_detail_gap_center_bottom">0dp</dimen>
    <dimen name="photopage_thumbline_item_playback_detail_gap_center_bottom_landscape">14dp</dimen>
    <dimen name="photopage_thumbline_item_drawable_radius">3dp</dimen>
    <!-- 缩略图轴大尺寸 -->
    <dimen name="photopage_thumbline_ml_screen_list_height">54dp</dimen>
    <dimen name="photopage_thumbline_ml_screen_item_width_normal">24dp</dimen> <!-- 24dp -->
    <dimen name="photopage_thumbline_ml_screen_item_height_normal">36dp</dimen>
    <dimen name="photopage_thumbline_ml_screen_item_width_center">36dp</dimen> <!-- 36dp -->
    <dimen name="photopage_thumbline_ml_screen_item_height_center">36dp</dimen>
    <dimen name="photopage_thumbline_ml_screen_gap_center_horizontal_image">20dp</dimen>
    <dimen name="photopage_thumbline_ml_screen_gap_center_horizontal_video">20dp</dimen>
    <dimen name="photopage_thumbline_ml_screen_gap_normal_vertical">6dp</dimen>
    <dimen name="photopage_thumbline_ml_screen_gap_center_vertical">6dp</dimen>
    <dimen name="photopage_thumbline_ml_screen_item_playback_thumb_slice_width">24dp</dimen>
    <dimen name="photopage_thumbline_ml_screen_item_playback_thumb_slice_height">36dp</dimen>
    <dimen name="photopage_thumbline_ml_screen_item_playback_preview_slice_width">36dp</dimen>
    <dimen name="photopage_thumbline_ml_screen_item_playback_preview_slice_height">36dp</dimen>
    <dimen name="photopage_thumbline_ml_screen_item_playback_detail_slice_width">53.33dp</dimen>
    <dimen name="photopage_thumbline_ml_screen_item_playback_detail_height">40dp</dimen>
    <dimen name="photopage_thumbline_ml_screen_item_playback_detail_gap_center_vertical">4dp</dimen>
    <!-- ↑↑↑缩略图轴↑↑↑ -->

    <!-- 半沉浸播放缩图轴 -->
    <dimen name="photopage_halfimmersive_playback_seekbar_section_height">40dp</dimen>
    <dimen name="photopage_halfimmersive_playback_seekbar_slice_width">45.33dp</dimen>
    <dimen name="photopage_halfimmersive_playback_seekbar_slice_height">34dp</dimen>
    <dimen name="photopage_halfimmersive_playback_seekbar_indicator_offset">-3.29dp</dimen>
    <dimen name="photopage_halfimmersive_playback_seekbar_indicator_width">1.84dp</dimen>
    <dimen name="photopage_halfimmersive_playback_seekbar_indicator_height">38dp</dimen>
    <dimen name="photopage_halfimmersive_playback_seekbar_indicator_border">1.29dp</dimen>
    <!-- 半沉浸播放缩图轴 end -->

    <!-- 功能区定义  -->
    <dimen name="photopage_start_functional_area_size">36dp</dimen>
    <dimen name="photopage_end_functional_area_size">30dp</dimen>
    <dimen name="photopage_functional_area_margin_horizontal">16dp</dimen>
    <dimen name="photopage_functional_area_margin_bottom">16dp</dimen>
    <!-- 功能区定义 end -->

    <!-- proXDR按钮  -->
    <dimen name="photopage_pro_xdr_margin_start">16dp</dimen>
    <dimen name="photopage_pro_xdr_margin_bottom">12dp</dimen>
    <dimen name="photopage_pro_xdr_width">61dp</dimen>
    <dimen name="photopage_pro_xdr_height">28dp</dimen>
    <dimen name="photopage_pro_xdr_padding_vertical">8.5dp</dimen>
    <dimen name="photopage_pro_xdr_padding_horizontal">12dp</dimen>
    <!-- proXDR按钮 end -->

    <!-- 文本识别按钮  -->
    <dimen name="photopage_text_ocr_margin_end">16dp</dimen>
    <dimen name="photopage_text_ocr_margin_bottom">11dp</dimen>
    <dimen name="photopage_text_ocr_width">30dp</dimen>
    <dimen name="photopage_text_ocr_height">30dp</dimen>
    <dimen name="photopage_text_ocr_padding_vertical">6dp</dimen>
    <dimen name="photopage_text_ocr_padding_horizontal">6dp</dimen>
    <!-- 文本识别按钮 end -->

    <!-- google lens  -->
    <dimen name="photopage_lens_margin_end">16dp</dimen>
    <dimen name="photopage_lens_margin_bottom">11dp</dimen>
    <dimen name="photopage_lens_height">28dp</dimen>
    <dimen name="photopage_lens_padding_vertical">6dp</dimen>
    <dimen name="photopage_lens_padding_start">10dp</dimen>
    <dimen name="photopage_lens_padding_end">12dp</dimen>
    <dimen name="photopage_lens_title_size">11dp</dimen>
    <dimen name="photopage_lens_icon_height">18dp</dimen>
    <dimen name="photopage_lens_icon_width">18dp</dimen>
    <dimen name="photopage_lens_space_width">5dp</dimen>
    <dimen name="photopage_lens_line_width">0.66dp</dimen>
    <!-- google lens end -->

    <dimen name="photopage_bottom_fab_center_to_bottom">26dp</dimen>
    <dimen name="photopage_bottom_fab_ml_screen_center_to_bottom">30dp</dimen>

    <!-- 智能功能推荐区  -->
    <dimen name="photopage_intelli_func_margin_start">24dp</dimen>
    <dimen name="photopage_intelli_func_small_horizontal_screen_margin_start">18dp</dimen>
    <dimen name="photopage_intelli_func_normal_margin_start">0dp</dimen>
    <dimen name="photopage_intelli_func_margin_end">16dp</dimen>
    <dimen name="photopage_intelli_func_height">32dp</dimen>
    <dimen name="photopage_intelli_func_radius">100dp</dimen>
    <dimen name="photopage_intelli_func_stroke_width">0.66dp</dimen>
    <dimen name="photopage_intelli_func_icon_height">20dp</dimen>
    <dimen name="photopage_intelli_func_icon_width">20dp</dimen>
    <dimen name="photopage_intelli_func_title_size">12dp</dimen>
    <dimen name="photopage_intelli_func_item_max_width">160dp</dimen>
    <dimen name="photopage_intelli_func_item_padding_start">10dp</dimen>
    <dimen name="photopage_intelli_func_item_padding_end">10dp</dimen>
    <dimen name="photopage_intelli_func_interval">12dp</dimen>
    <!-- recycleView算上marginStart和marginEnd最宽是288dp -->
    <dimen name="photopage_intelli_func_recycle_view_width">288dp</dimen>
    <!-- 智能功能推荐区 end -->

    <!-- 智能功能功能引导区  -->
    <dimen name="photopage_intelli_func_guide_scroll_padding_bottom">24dp</dimen>
    <dimen name="photopage_intelli_func_guide_padding_bottom">16dp</dimen>
    <dimen name="photopage_intelli_func_guide_title_margin_top">24dp</dimen>
    <dimen name="photopage_intelli_func_guide_btn_margin_top">8dp</dimen>
    <dimen name="photopage_intelli_func_guide_func_height">100dp</dimen>
    <dimen name="photopage_intelli_func_guide_func_width">160dp</dimen>
    <dimen name="photopage_intelli_func_guide_title_size">10dp</dimen>
    <dimen name="photopage_intelli_func_guide_func_capsule_margin_end">12dp</dimen>
    <dimen name="photopage_intelli_func_guide_func_capsule_margin_bottom">54dp</dimen>
    <!-- 智能功能功能引导区 end -->

    <!--couiviewpager相关属性-->
    <dimen name="photopage_coui_fast_sliding_next_pager_distance_threshold">40dp</dimen>
    <dimen name="photopage_coui_recycle_touch_slop_threshold">16dp</dimen>
    <!--couiviewpager相关属性 end-->

    <!-- 抠图 start -->
    <dimen name="photopage_lns_save_snackbar_margin_bottom">88dp</dimen>
    <dimen name="photopage_lns_subject_top_secure_space">112dp</dimen>
    <dimen name="photopage_lns_subject_bottom_secure_space">80dp</dimen>
    <dimen name="photopage_lns_select_subject_max_length">200dp</dimen>
    <!-- 抠图 end   -->

    <!-- 新春水印 start -->
    <dimen name="photopage_spring_watermark_icon_height_margin_top">4dp</dimen>
    <dimen name="photopage_spring_watermark_icon_height_margin_end">6dp</dimen>
    <dimen name="spring_festival_watermark_info_list_item_image_width">24dp</dimen>
    <dimen name="spring_festival_watermark_info_list_item_image_height">24dp</dimen>
    <dimen name="type_festival_watermark_info_list_item_image_width">5dp</dimen>
    <dimen name="type_festival_watermark_info_list_item_image_height">5dp</dimen>
    <!-- 新春水印 end   -->

    <!-- 云同步 start -->
    <dimen name="photopage_cloudsync_uploading_size">24dp</dimen>
    <!-- 云同步 end   -->
    <!-- olive状态编辑 start-->
    <dimen name="photopage_olive_tips_snackbar_margin_bottom">104dp</dimen>
    <dimen name="photopage_olive_tips_snackbar_margin_bottom_default">88dp</dimen>
    <!-- olive状态编辑 end  -->

    <!-- 大图详情页 start -->
    <dimen name="photopage_detail_view_dimen_20dp">20dp</dimen>
    <dimen name="photopage_detail_view_dimen_19dp">19dp</dimen>
    <dimen name="photopage_detail_view_dimen_18dp">18dp</dimen>
    <dimen name="photopage_detail_view_dimen_16dp">16dp</dimen>
    <dimen name="photopage_detail_view_dimen_14dp">14dp</dimen>
    <dimen name="photopage_detail_view_dimen_12dp">12dp</dimen>
    <dimen name="photopage_detail_view_dimen_10dp">10dp</dimen>
    <dimen name="photopage_detail_view_dimen_8dp">8dp</dimen>
    <dimen name="photopage_detail_view_dimen_6dp">6dp</dimen>
    <dimen name="photopage_detail_view_dimen_5dp">5dp</dimen>
    <dimen name="photopage_detail_view_dimen_4dp">4dp</dimen>
    <dimen name="photopage_detail_view_dimen_3dp">3dp</dimen>
    <dimen name="photopage_detail_view_dimen_2dp">2dp</dimen>
    <dimen name="photopage_detail_view_dimen_0dp">0dp</dimen>
    <dimen name="photopage_detail_text_view_adjust_background_radius">100dp</dimen>
    <dimen name="photopage_detail_text_view_adjust_max_width">100dp</dimen>
    <dimen name="photopage_detail_text_view_name_margin_right">16dp</dimen>
    <dimen name="photopage_detail_text_view_big_size">16dp</dimen>
    <dimen name="photopage_detail_text_view_middle_size">14dp</dimen>
    <dimen name="photopage_detail_text_view_small_size">12dp</dimen>
    <dimen name="photopage_detail_info_image_high_size">20dp</dimen>
    <dimen name="photopage_detail_info_arrow_size">24dp</dimen>
    <dimen name="photopage_detail_view_line">1dp</dimen>
    <dimen name="photopage_detail_lens_line_width">0.66dp</dimen>
    <dimen name="photopage_detail_lens_line_height">10dp</dimen>
    <dimen name="photopage_details_small_screen_portrait_min_height">440dp</dimen>
    <dimen name="photopage_details_tablet_portrait_height">422dp</dimen>
    <dimen name="photopage_detail_content_top_padding">20dp</dimen>
    <dimen name="photopage_detail_content_horizontal_padding">10dp</dimen>
    <dimen name="photopage_detail_content_divider_height">0.66dp</dimen>
    <dimen name="photopage_detail_content_divider_margin_horizontal">4dp</dimen>
    <dimen name="photopage_detail_content_divider_interval">16dp</dimen>
    <dimen name="photopage_detail_content_divider_lens_top">16dp</dimen>
    <dimen name="photopage_detail_outline_height">0.33dp</dimen>
    <dimen name="photopage_detail_outline_height_negative">-0.33dp</dimen>
    <dimen name="photopage_detail_content_exif_iso_horizontal_padding">12dp</dimen>
    <dimen name="photopage_detail_content_exif_iso_vertical_padding">16dp</dimen>
    <dimen name="photopage_detail_content_histogram_height">88dp</dimen>
    <dimen name="photopage_detail_content_mask_height">78dp</dimen>
    <dimen name="photopage_detail_scrollbar_height">100dp</dimen>
    <dimen name="photopage_detail_scrollbar_with">3dp</dimen>
    <!-- 大图详情页 end -->

    <!-- 顶栏菜单 -->
    <dimen name="photopage_top_menu_extra_padding_start">4dp</dimen>
    <dimen name="photopage_top_menu_extra_padding_end">4dp</dimen>
    <dimen name="photopage_top_menu_title_margin">0dp</dimen>
    <!-- 顶栏菜单 - 半沉浸模式 -->
    <dimen name="photopage_top_menu_extra_padding_start_half_immersive">16dp</dimen>
    <dimen name="photopage_top_menu_extra_padding_end_half_immersive">10dp</dimen>
    <dimen name="photopage_top_menu_title_margin_half_immersive">8dp</dimen>
    <dimen name="photopage_top_menu_icon_tips_width">31dp</dimen>
    <dimen name="photopage_top_menu_icon_tips_height">18dp</dimen>
    <dimen name="photopage_top_menu_icon_tips_margin_top">3dp</dimen>
    <!-- 顶栏菜单 end -->

    <!-- 底栏菜单 -->
    <dimen name="photopage_bottom_menu_icon_tips_width">31dp</dimen>
    <dimen name="photopage_bottom_menu_icon_tips_height">18dp</dimen>
    <dimen name="photopage_bottom_menu_icon_tips_margin_right">3dp</dimen>
    <dimen name="photopage_bottom_menu_item_height">54dp</dimen>
    <dimen name="photopage_bottom_menu_icon_top_space">17dp</dimen>
    <!-- 底栏菜单 end -->

    <!-- 大图退出跟手动画阻尼边界-->
    <dimen name="photopage_exit_animation_damping_boundary">100dp</dimen>
    <!-- 地图在详情页显示高度 -->
    <dimen name="detail_map_container_height">96dp</dimen>
    <dimen name="detail_map_container_margin_top">12dp</dimen>
    <dimen name="detail_map_container_margin_bottom">20dp</dimen>
    <!-- 地图在详情页显示的圆角radis -->
    <dimen name="detail_map_container_radis">12dp</dimen>
    <dimen name="detail_map_container_stroke_width">0.6dp</dimen>

</resources>