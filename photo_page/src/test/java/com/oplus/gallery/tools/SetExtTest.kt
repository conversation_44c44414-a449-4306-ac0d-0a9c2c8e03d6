/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : SetExtTest.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2025/03/20
 ** Author      : <EMAIL>
 ** TAG         :
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                         <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** linkail<PERSON>@oppo.com              2025/03/20       1.0      create module
 *********************************************************************************/
package com.oplus.gallery.tools;

import org.junit.Assert.assertEquals
import org.junit.Test

class SetExtTest {
    @Test
    fun test_differ_empty() {
        val a = sortedSetOf<Int>()
        val b = sortedSetOf<Int>()
        val c = sortedSetOf(1, 2, 3)

        val result1 = a.difference(b)
        assertEquals(setOf<Int>(), result1.first)
        assertEquals(setOf<Int>(), result1.second)

        val result2 = a.difference(c)
        assertEquals(setOf<Int>(), result2.first)
        assertEquals(c.toSet(), result2.second)

        val result3 = c.difference(b)
        assertEquals(c.toSet(), result3.first)
        assertEquals(b, result3.second)
    }

    @Test
    fun test_differ_intersect() {
        val a = sortedSetOf<Int>(1, 2, 3, 4, 5)
        val b = sortedSetOf<Int>(2, 3)

        val result1 = a.difference(b)
        assertEquals(setOf<Int>(1, 4, 5), result1.first)
        assertEquals(setOf<Int>(), result1.second)

        val result2 = b.difference(a)
        assertEquals(setOf<Int>(), result2.first)
        assertEquals(setOf<Int>(1, 4, 5), result2.second)
    }

    @Test
    fun test_differ_complement() {
        val a = sortedSetOf<Int>(1, 2, 3, 4, 5)
        val b = sortedSetOf<Int>(1, 2, 3)
        val c = sortedSetOf<Int>(4, 5)

        val result1 = a.difference(b)
        assertEquals(setOf<Int>(4, 5), result1.first)
        assertEquals(setOf<Int>(), result1.second)

        val result2 = b.difference(a)
        assertEquals(setOf<Int>(), result2.first)
        assertEquals(setOf<Int>(4, 5), result2.second)

        val result3 = a.difference(c)
        assertEquals(setOf<Int>(1, 2, 3), result3.first)
        assertEquals(setOf<Int>(), result3.second)

        val result4 = c.difference(a)
        assertEquals(setOf<Int>(), result4.first)
        assertEquals(setOf<Int>(1, 2, 3), result4.second)

        val result5 = b.difference(c)
        assertEquals(b.toSet(), result5.first)
        assertEquals(c.toSet(), result5.second)

        val result6 = c.difference(b)
        assertEquals(c.toSet(), result6.first)
        assertEquals(b.toSet(), result6.second)

        val d = sortedSetOf<Int>(1, 2, 3, 4, 5, 7)
        val e = sortedSetOf<Int>(1, 6)

        val result7 = d.difference(e)
        assertEquals(setOf<Int>(2, 3, 4, 5, 7), result7.first)
        assertEquals(setOf<Int>(6), result7.second)

        val result8 = e.difference(d)
        assertEquals(setOf<Int>(6), result8.first)
        assertEquals(setOf<Int>(2, 3, 4, 5, 7), result8.second)
    }
}