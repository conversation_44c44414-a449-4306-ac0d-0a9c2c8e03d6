/********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AVPlayerLoaderTest
 ** Description: AVPlayerLoader单测类
 ** Version: 1.0
 ** Date : 2025/3/10
 ** Author: Conghu.CAI@Apps.Gallery3D
 ** TAG: AVPlayerLoaderTest
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>      <version>       <desc>
 ** ------------------------------------------------------------------------------
 **  Conghu.CAI@Apps.Gallery3D      2025/03/10     1.0     first created
 ********************************************************************************/
package com.oplus.gallery.business.contentloading.loader.player

import android.media.MediaCodecInfo
import android.media.MediaFormat.MIMETYPE_VIDEO_HEVC
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business.contentloading.loader.AVPlayerLoader
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO
import com.oplus.gallery.framework.abilities.config.IConfigAbility
import com.oplus.gallery.framework.abilities.config.args.ConfigExtraArgs
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Codec.PLATFORM_CODEC_CAPABILITY_QUERY
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SYSTEM_SUPPORT_120_FPS_PLAYBACK
import com.oplus.gallery.framework.app.GalleryApplication
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.spyk
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class AVPlayerLoaderTest {
    private lateinit var mockContext: GalleryApplication
    private lateinit var avPlayerLoader: AVPlayerLoader
    private lateinit var mediaItem: MediaItem
    private lateinit var extras: MediaItem.IExtra
    private lateinit var configAbility: IConfigAbility
    private lateinit var codecCap: MediaCodecInfo.CodecCapabilities
    private lateinit var videoCapabilities: MediaCodecInfo.VideoCapabilities
    private lateinit var args: ConfigExtraArgs

    @Before
    fun setUp() {
        mockkObject(ContextGetter)
        mockkObject(ConfigAbilityWrapper)
        args = mockk()
        extras = mockk()
        mockContext = mockk()
        configAbility = mockk()
        mediaItem = mockk()
        codecCap = mockk()
        videoCapabilities = mockk()
        avPlayerLoader = spyk(
            AVPlayerLoader(mockContext, mediaItem, mockk(), mockk())
        )

        every { ContextGetter.context } returns mockContext
        every { mockContext.applicationContext } returns mockContext

        every { mediaItem.mediaType } returns MEDIA_TYPE_VIDEO
        every { mediaItem.extra } returns extras
        every { mediaItem.mediaId } returns 0
        every { mediaItem.width } returns 1080
        every { mediaItem.height } returns 1920
        every { mediaItem.codecType } returns MIMETYPE_VIDEO_HEVC

        every { mockContext.getAppAbility<IConfigAbility>() } returns configAbility
        every { configAbility.getBooleanConfig(FEATURE_IS_SYSTEM_SUPPORT_120_FPS_PLAYBACK) } returns true
        every { configAbility.getConfigWithArgs(PLATFORM_CODEC_CAPABILITY_QUERY, any(), null) } returns null
        every { configAbility.close() } returns Unit

        every { codecCap.videoCapabilities } returns videoCapabilities
    }

    @After
    fun tearDown() {
        clearAllMocks()
    }

    @Test
    fun `could playback at 120 fps when unknown fps`() {
        every { mediaItem.extra?.getFloatExtra(any()) } returns null
        Assert.assertTrue(avPlayerLoader.couldPlaybackAt120Fps())
    }

    @Test
    fun `could not playback at 120 fps when video fps is less than threshold`() {
        every { mediaItem.extra?.getFloatExtra(any()) } returns 0F
        Assert.assertFalse(avPlayerLoader.couldPlaybackAt120Fps())

        every { mediaItem.extra?.getFloatExtra(any()) } returns 60F
        Assert.assertFalse(avPlayerLoader.couldPlaybackAt120Fps())
    }

    @Test
    fun `could playback at 120 fps when unknown codec type`() {
        every { mediaItem.extra?.getFloatExtra(any()) } returns 120F
        every { mediaItem.codecType } returns null
        Assert.assertTrue(avPlayerLoader.couldPlaybackAt120Fps())
    }

    @Test
    fun `could playback at 120 fps when codec not found`() {
        every { mediaItem.extra?.getFloatExtra(any()) } returns 120F
        every { mediaItem.codecType } returns MIMETYPE_VIDEO_HEVC
        every { configAbility.getConfigWithArgs(PLATFORM_CODEC_CAPABILITY_QUERY, any(), null) } returns null
        Assert.assertTrue(avPlayerLoader.couldPlaybackAt120Fps())
    }

    @Test
    fun `could not playback at 120 fps when capability is not enough`() {
        every { mediaItem.extra?.getFloatExtra(any()) } returns 120F
        every { mediaItem.codecType } returns MIMETYPE_VIDEO_HEVC
        every { configAbility.getConfigWithArgs(PLATFORM_CODEC_CAPABILITY_QUERY, any(), null) } returns codecCap
        every { videoCapabilities.getSupportedFrameRatesFor(any(), any()).upper } returns 60.0
        Assert.assertFalse(avPlayerLoader.couldPlaybackAt120Fps())
    }

    @Test
    fun `could playback at 120 fps when every conditions meet`() {
        every { mediaItem.extra?.getFloatExtra(any()) } returns 120F
        every { mediaItem.codecType } returns MIMETYPE_VIDEO_HEVC
        every { configAbility.getConfigWithArgs(PLATFORM_CODEC_CAPABILITY_QUERY, any(), null) } returns codecCap
        every { videoCapabilities.getSupportedFrameRatesFor(any(), any()).upper } returns 120.0
        Assert.assertTrue(avPlayerLoader.couldPlaybackAt120Fps())
    }
}
