/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : IndicatorHandleBindDataRequestTest.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/05/30
 ** Author      : <EMAIL>
 ** TAG         :
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                         <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>           2022/05/30       1.0      create module
 *********************************************************************************/
package com.oplus.gallery.cshot_page.ui.section.cshotindicatorsection

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.ui.view.ItemViewData
import com.oplus.gallery.business_lib.viewmodel.base.ListViewModel
import com.oplus.gallery.cshot_page.viewmodel.CShotViewModel
import com.oplus.gallery.test.tools.DimensionTraverserAssembler
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.Parameterized
import org.junit.runners.Parameterized.Parameters

@RunWith(Parameterized::class)
class IndicatorHandleBindDataRequestTest(private val testData: TestData) {

    private lateinit var cshotLoadingProxy: CShotIndicatorLoadingProxy

    @Before
    fun setup() {
        val lifecycleOwner = mockk<LifecycleOwner>()
        val cShotVm = mockk<CShotViewModel>().apply {
            val activeInfoLiveDataLiveData = mockk<MutableLiveData<ListViewModel<MediaItem, ItemViewData>.ActiveDataInfo>>()
            every { activeInfoLiveDataLiveData.observe(any(), any()) } returns Unit
            every { activeInfoLiveData } returns activeInfoLiveDataLiveData

            val focusSlotDataLiveData = mockk<MutableLiveData<Int>>()
            every { focusSlotDataLiveData.observe(any(), any()) } returns Unit
            every { focusSlot } returns focusSlotDataLiveData

            val indicatorVisibleItemCountLiveData = mockk<MutableLiveData<Int>>()
            every { indicatorVisibleItemCountLiveData.observe(any(), any()) } returns Unit
            every { indicatorVisibleItemCount } returns indicatorVisibleItemCountLiveData
        }

        cshotLoadingProxy = spyk(CShotIndicatorLoadingProxy(lifecycleOwner, cShotVm))
    }

    @Test
    @Ignore("FIXME zhangjisong:单测失败，待负责人修改")
    fun `should return expected result when calling handleBindDataRequest with specified conditions`() {
        // given
        val position = testData.position
        val itemData = testData.itemViewData
        val expectedReturns = testData.expectedReturns
        val expectedItemChanged = testData.expectedItemChangedPosition

        // when
        var actualItemChanged: Int? = null
        cshotLoadingProxy.onItemChanged = { actualItemChanged = it }
        val actualReturns = cshotLoadingProxy.handleBindDataRequest(position, itemData)

        // then
        Assert.assertEquals("itemChanged: testData=$testData", expectedItemChanged, actualItemChanged)
        Assert.assertEquals("returns: testData=$testData", expectedReturns, actualReturns)
    }

    data class TestData(
        val position: Int,
        val itemViewData: ItemViewData?,
        val expectedItemChangedPosition: Int?,
        val expectedReturns: Boolean
    )

    companion object {

        @JvmStatic
        @Parameters
        fun data(): List<Array<TestData>> {
            return DimensionTraverserAssembler()
                .dimensionOf("position", 10010, 10086)
                .dimensionOf("viewDataPosition", 10086, 1001010086, null)
                .assemble {
                    arrayOf(
                        buildTestData(
                            position = it["position"] as Int,
                            viewDataPosition = it["viewDataPosition"] as? Int
                        )
                    )
                }
        }

        private fun buildTestData(
            position: Int,
            viewDataPosition: Int?
        ): TestData {
            val expectedItemChangedPosition: Int?
            val expectedReturns: Boolean
            when {
                (viewDataPosition == null) -> {
                    expectedItemChangedPosition = null
                    expectedReturns = false
                }
                (viewDataPosition != position) -> {
                    expectedItemChangedPosition = null
                    expectedReturns = true
                }
                else -> {
                    expectedItemChangedPosition = position
                    expectedReturns = true
                }
            }
            return TestData(
                position = position,
                itemViewData = viewDataPosition?.let { ItemViewData("", it, "", 0L, 0) },
                expectedItemChangedPosition = expectedItemChangedPosition,
                expectedReturns = expectedReturns,
            )
        }
    }
}