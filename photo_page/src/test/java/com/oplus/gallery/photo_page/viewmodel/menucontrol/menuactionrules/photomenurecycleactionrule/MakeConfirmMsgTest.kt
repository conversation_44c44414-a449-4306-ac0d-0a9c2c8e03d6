/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MakeConfirmMsgTest.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/05/31
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>                   <date>       <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     <EMAIL>     2022/05/31     1.0              create
 ******************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.photomenurecycleactionrule

import androidx.lifecycle.MutableLiveData
import com.oplus.gallery.business_lib.R
import com.oplus.gallery.business_lib.cshot.ReleaseCShotHelper
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.cshot.CShotDataHelper
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.basebiz.uikit.fragment.BaseFragment
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.inputarguments.PhotoInputArgumentsViewModel
import com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules.PhotoMenuRecycleActionRule
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO
import com.oplus.gallery.foundation.database.util.DatabaseUtils
import com.oplus.gallery.photo_page.viewmodel.menucontrol.PhotoMenuPresent
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.spyk
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class MakeConfirmMsgTest {

    @MockK
    private lateinit var mediaItem: MediaItem

    @MockK
    private lateinit var activity: BaseActivity

    @MockK
    private lateinit var fragment: BaseFragment

    @MockK
    private lateinit var viewModel: PhotoViewModel

    private val deleteRecycled = "delete_recycled"

    private val restore = "restore"

    private val deleteCShot = "delete_cshot"
    private val restoreCShot = "restore_cshot"

    private val delete = "delete"
    private lateinit var recycleActionRule: PhotoMenuRecycleActionRule

    @Before
    fun setup() {
        MockKAnnotations.init(this)
        mockkStatic(R::class)
        mockkStatic(DatabaseUtils::class)
        mockkStatic(CShotDataHelper::class)
        mockkStatic(ReleaseCShotHelper::class)
        val inputArguments = mockk<PhotoInputArgumentsViewModel>()
        val menu = mockk<PhotoInputArgumentsViewModel.Menu>()
        every { viewModel.inputArguments } returns inputArguments
        every { inputArguments.menu } returns MutableLiveData(menu)
        every { menu.present } returns PhotoMenuPresent.ViewContent
        every { mediaItem.path } returns Path.fromString("/path/path")
        every { mediaItem.isRecycledItem } returns false
        every { activity.getString(com.oplus.gallery.basebiz.R.string.base_comfirm_delete) } returns delete
        every { activity.getString(com.oplus.gallery.basebiz.R.string.base_restore) } returns restore
        every { activity.getString(com.oplus.gallery.basebiz.R.string.base_delete_single_permanently) } returns deleteRecycled
    }

    @Test
    fun `should return deleteRecycled when ruleAction is action_delete_recycled`() {
        // given
        recycleActionRule = mockkPhotoMenuRecycleActionRule(com.oplus.gallery.basebiz.R.id.action_delete_recycled)
        val expected = deleteRecycled

        // when
        val actual = recycleActionRule.makeConfirmMsg(mediaItem)

        // then
        Assert.assertEquals(expected, actual)
    }

    @Test
    fun `should return delete when ruleAction is action_recycle and mediaItem is Video`() {
        // given
        recycleActionRule = mockkPhotoMenuRecycleActionRule(com.oplus.gallery.basebiz.R.id.action_recycle)
        every { mediaItem.mediaType } returns MEDIA_TYPE_VIDEO
        val expected = delete

        // when
        val actual = recycleActionRule.makeConfirmMsg(mediaItem)

        // then
        Assert.assertEquals(expected, actual)
    }

    @Test
    fun `should return restoreVideo when ruleAction is action_restore_recycled and mediaItem is Video`() {
        // given
        recycleActionRule = mockkPhotoMenuRecycleActionRule(com.oplus.gallery.basebiz.R.id.action_restore_recycled)
        every { mediaItem.mediaType } returns MEDIA_TYPE_VIDEO
        val expected = restore

        // when
        val actual = recycleActionRule.makeConfirmMsg(mediaItem)

        // then
        Assert.assertEquals(expected, actual)
    }

    @Test
    fun `should return delete when ruleAction is action_recycle and mediaItem is Image but not cshot`() {
        // given
        recycleActionRule = mockkPhotoMenuRecycleActionRule(com.oplus.gallery.basebiz.R.id.action_recycle)
        every { mediaItem.mediaType } returns MEDIA_TYPE_IMAGE
        val expected = delete

        // when
        val actual = recycleActionRule.makeConfirmMsg(mediaItem)

        // then
        Assert.assertEquals(expected, actual)
    }

    @Test
    fun `should return restoreImage when ruleAction is action_restore_recycled and mediaItem is Image but not cshot`() {
        // given
        recycleActionRule = mockkPhotoMenuRecycleActionRule(com.oplus.gallery.basebiz.R.id.action_restore_recycled)
        every { mediaItem.mediaType } returns MEDIA_TYPE_IMAGE
        val expected = restore

        // when
        val actual = recycleActionRule.makeConfirmMsg(mediaItem)

        // then
        Assert.assertEquals(expected, actual)
    }

    @Test
    fun `should return restoreCShot when ruleAction is action_restore_recycled and mediaItem is cshot`() {
        // given
        val cshotCount = 10
        recycleActionRule = mockkPhotoMenuRecycleActionRule(com.oplus.gallery.basebiz.R.id.action_restore_recycled, cshotCount)
        mockkCShotConfirm(cshotCount)
        every { mediaItem.mediaType } returns MEDIA_TYPE_IMAGE
        val expected = restoreCShot + cshotCount

        // when
        val actual = recycleActionRule.makeConfirmMsg(mediaItem)

        // then
        Assert.assertEquals(expected, actual)
    }

    @Test
    fun `should return deleteCShot when ruleAction is action_restore_recycled and mediaItem is cshot`() {
        // given
        val cshotCount = 10
        recycleActionRule = mockkPhotoMenuRecycleActionRule(com.oplus.gallery.basebiz.R.id.action_restore_recycled, cshotCount)
        mockkCShotConfirm(cshotCount)
        every { mediaItem.mediaType } returns MEDIA_TYPE_IMAGE
        val expected = restoreCShot + cshotCount

        // when
        val actual = recycleActionRule.makeConfirmMsg(mediaItem)

        // then
        Assert.assertEquals(expected, actual)
    }

    @Test
    fun `should return delete when ruleAction is action_recycle and mediaItem is cshot but cshotId is nearestDeleteCshotID`() {
        // given
        val cshotCount = 10
        recycleActionRule = mockkPhotoMenuRecycleActionRule(com.oplus.gallery.basebiz.R.id.action_recycle, cshotCount)
        mockkCShotConfirm(cshotCount)
        every { mediaItem.mediaType } returns MEDIA_TYPE_IMAGE

        val cshotId = 1000L
        every { mediaItem.cShotID } returns cshotId
        every { ReleaseCShotHelper.nearestDeleteCshotID } returns cshotId

        val expected = delete

        // when
        val actual = recycleActionRule.makeConfirmMsg(mediaItem)

        // then
        Assert.assertEquals(expected, actual)
    }

    @Test
    fun `should return restoreImage when ruleAction is action_restore_recycled and mediaItem is cshot but cshotId is nearestDeleteCshotID`() {
        // given
        val cshotCount = 20
        recycleActionRule = mockkPhotoMenuRecycleActionRule(com.oplus.gallery.basebiz.R.id.action_restore_recycled, cshotCount)
        mockkCShotConfirm(cshotCount)
        every { mediaItem.mediaType } returns MEDIA_TYPE_IMAGE

        val cshotId = 1000L
        every { mediaItem.cShotID } returns cshotId
        every { ReleaseCShotHelper.nearestDeleteCshotID } returns cshotId

        val expected = restore

        // when
        val actual = recycleActionRule.makeConfirmMsg(mediaItem)

        // then
        Assert.assertEquals(expected, actual)
    }

    /**
     * mock连拍的提示词
     */
    private fun mockkCShotConfirm(cshotCount: Int) {
        every { activity.getString(com.oplus.gallery.basebiz.R.string.base_delete_single_permanently, cshotCount) } returns deleteCShot
        every { activity.getString(com.oplus.gallery.basebiz.R.string.base_restore, cshotCount) } returns restoreCShot + cshotCount
    }

    /**
     * mockk 一个 PhotoMenuRecycleActionRule
     * @param ruleAction 测试用的action
     * @param cshotCount 连拍数量，-1表示无效连拍
     */
    private fun mockkPhotoMenuRecycleActionRule(ruleAction: Int, cshotCount: Int = -1) =
        spyk(PhotoMenuRecycleActionRule(ruleAction, viewModel, activity, fragment, mockk()), recordPrivateCalls = true) {
            every { DatabaseUtils.isCShotIdValid(any()) } returns (cshotCount != -1)
            every { mediaItem.cShotID } returns 1001L
            every { ReleaseCShotHelper.nearestDeleteCshotID } returns 1000L
            every { CShotDataHelper.getPicCntOfCshot(any(), any()) } returns 10
        }
}