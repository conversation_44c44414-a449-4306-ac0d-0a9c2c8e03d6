/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : LoadFormatTypeTest.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/06/13
 ** Author      : <EMAIL>
 ** TAG         :
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                         <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>           2022/06/13       1.0      create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.details

import com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_DOLBY_VISION
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_JPEG
import com.oplus.gallery.business_lib.model.data.base.utils.ImageTypeUtils
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO
import com.oplus.gallery.foundation.util.thread.ThreadUtils
import com.oplus.gallery.test.tools.DimensionTraverserAssembler
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.Parameterized
import org.junit.runners.Parameterized.Parameters

@RunWith(Parameterized::class)
class LoadFormatTypeTest(private val testData: TestData) {

    private lateinit var photoInfoLoader: PhotoInfoLoader
    private lateinit var mediaItem: LocalMediaItem

    @Before
    fun setup() {
        photoInfoLoader = spyk(PhotoInfoLoader(mockk(), mockk(), mockk(), 0))
        mockkObject(PhotoInfoLoader.Companion)
        every { photoInfoLoader.isYuv(any()) } returns testData.isYuv
        every { photoInfoLoader.isOlivePhoto(any()) } returns testData.isOlive
        every { photoInfoLoader.isAISceneryPhoto(any()) } returns testData.isAIScenery

        mediaItem = mockk()
        every { mediaItem.mediaType } returns if (testData.isVideo) MEDIA_TYPE_VIDEO else MEDIA_TYPE_IMAGE
        every { mediaItem.path } returns mockk()
        every { mediaItem.codecType } returns ""
        every { mediaItem.getSupportFormat(FORMAT_DOLBY_VISION) } returns FORMAT_JPEG
        every { mediaItem.syncStatus } returns 0
        every { mediaItem.name } returns ""
        every { mediaItem.extra } returns null
        every { mediaItem.tagFlags } returns 0L

        mockkStatic(ImageTypeUtils::class)
        every { ImageTypeUtils.isRawFilePath(any()) } returns testData.isRawFilePath

        mockkStatic(ThreadUtils::class)
        every { ThreadUtils.isMainThread() } returns false
    }

    @After
    fun tearDown() {
        unmockkStatic(ImageTypeUtils::class)
        unmockkObject(PhotoInfoLoader.Companion)
    }

    @Test
    fun `should return expected formatType when calling loadFormatType with specified conditions`() {
        // Given
        val expectedFormatType = testData.expected

        // When
        val actualFormatType = photoInfoLoader.loadFormatType(mediaItem)

        // Then
        Assert.assertEquals("testData=$testData", expectedFormatType, expectedFormatType)
    }

    data class TestData(
        val isYuv: Boolean,
        val isVideo: Boolean,
        val isRawFilePath: Boolean,
        val expected: Int,
        val isOlive: Boolean,
        val isAIScenery: Boolean
    )

    companion object {

        @JvmStatic
        @Parameters
        fun data(): List<Array<TestData>> {
            return DimensionTraverserAssembler()
                .dimensionOfBoolean("isYuv")
                .dimensionOfBoolean("isVideo")
                .dimensionOfBoolean("isRawFilePath")
                .dimensionOfBoolean("isOlive")
                .dimensionOfBoolean("isAIScenery")
                .assemble {
                    arrayOf(
                        buildTestData(
                            isYuv = it["isYuv"] as Boolean,
                            isVideo = it["isVideo"] as Boolean,
                            isRawFilePath = it["isRawFilePath"] as Boolean,
                            isOlive = it["isOlive"] as Boolean,
                            isAIScenery = it["isAIScenery"] as Boolean,
                        )
                    )
                }
        }

        private fun buildTestData(
            isYuv: Boolean,
            isVideo: Boolean,
            isRawFilePath: Boolean,
            isOlive: Boolean,
            isAIScenery: Boolean,
        ): TestData {
            val expected = when {
                isVideo -> PhotoDetailsConstants.FormatType.OTHER
                isRawFilePath -> PhotoDetailsConstants.FormatType.RAW
                isYuv -> PhotoDetailsConstants.FormatType.YUV
                isOlive -> PhotoDetailsConstants.FormatType.OLIVE
                isAIScenery -> PhotoDetailsConstants.FormatType.AI_SCENERY
                else -> PhotoDetailsConstants.FormatType.OTHER
            }
            return TestData(
                isYuv = isYuv,
                isVideo = isVideo,
                isRawFilePath = isRawFilePath,
                isOlive = isOlive,
                isAIScenery = isAIScenery,
                expected = expected
            )
        }
    }
}