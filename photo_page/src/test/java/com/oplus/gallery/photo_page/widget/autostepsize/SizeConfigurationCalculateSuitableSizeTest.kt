/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : SizeConfigurationCalculateSuitableSizeTest.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/06/01
 ** Author      : <EMAIL>
 ** TAG         :
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                         <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>           2022/06/01       1.0      create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.widget.autostepsize

import com.oplus.gallery.test.tools.DimensionTraverserAssembler
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.Parameterized
import org.junit.runners.Parameterized.Parameters

@RunWith(Parameterized::class)
class SizeConfigurationCalculateSuitableSizeTest(private val testData: TestData) {

    @Test
    fun `should return expected result when calling SizeConfiguration#calculateSuitableSize with specified conditions`() {
        // given
        val originSize = testData.originSize
        val totalCount = testData.totalCount
        val contentCount = testData.contentCount
        val padding = testData.padding
        val gap = testData.gap
        val expectedReturns = testData.expectedReturns

        // when
        val actualReturns = AutoStepSizeFrameLayout.SizeConfiguration().calculateSuitableSize(
            originSize, totalCount, contentCount, padding, gap
        )

        // then
        Assert.assertEquals("testData=$testData", expectedReturns, actualReturns)
    }

    data class TestData(
        val originSize: Int,
        val totalCount: Int,
        val contentCount: Int,
        val padding: Int,
        val gap: Int,
        val expectedReturns: Int
    )

    companion object {

        @JvmStatic
        @Parameters
        fun data(): List<Array<TestData>> {
            return DimensionTraverserAssembler()
                .dimensionOf("originSize", 0, 100100)
                .dimensionOf("totalCount", -100, 0, 100)
                .dimensionOf("contentCount", 0, 100)
                .dimensionOf("padding", 0, 100)
                .dimensionOf("gap", 0, 100)
                .assemble {
                    arrayOf(
                        buildTestData(
                            originSize = it["originSize"] as Int,
                            totalCount = it["totalCount"] as Int,
                            contentCount = it["contentCount"] as Int,
                            padding = it["padding"] as Int,
                            gap = it["gap"] as Int
                        )
                    )
                }
        }

        private fun buildTestData(
            originSize: Int,
            totalCount: Int,
            contentCount: Int,
            padding: Int,
            gap: Int
        ): TestData {
            val expectedReturns = when {
                (totalCount <= 0) -> originSize
                else -> {
                    val gridSizeWithGap = (originSize - padding * 2 + gap).toDouble() / totalCount
                    val suitableSize = gridSizeWithGap * contentCount - gap
                    suitableSize.toInt().coerceIn(0, originSize)
                }
            }
            return TestData(
                originSize = originSize,
                totalCount = totalCount,
                contentCount = contentCount,
                padding = padding,
                gap = gap,
                expectedReturns = expectedReturns
            )
        }
    }
}